/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Estilos para o modal */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Configurações gerais do modal */
.modern-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-modal .nsm-content {
  border-radius: $border-radius !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.health-modal .nsm-dialog {
  max-width: 600px !important;
  width: 95% !important;
}

.modal-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background-color: $bg-color;
}

/* Cabeçalho */
.modal-header {
  background-color: $primary-color;
  padding: 16px 20px;
  text-align: center;
  border-bottom: 1px solid $primary-dark;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.modal-title-container {
  flex: 1;
  text-align: center;
  max-width: 80%;
}

.modal-title {
  color: white;
  margin: 0;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  font-size: 18px;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color $transition ease;
  z-index: 11;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo */
.modal-content {
  padding: 20px;
  flex: 1;
  background-color: $secondary-light;
  overflow-y: auto;
  max-height: 65vh;
}

/* Grid de formulário */
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  padding: 20px;
}

.form-column {
  width: 100%;
}

.form-field {
  width: 100%;
  position: relative;
  margin-bottom: 8px;
}

/* Campos de formulário */
mat-form-field {
  width: 100%;
}

.custom-select {
  width: 100%;
  font-size: 14px !important;
}

.ng-select .ng-select-container {
  border: 1px solid $border-color !important;
  border-radius: $border-radius !important;
  background-color: $secondary-light !important;
  min-height: 48px !important;
}

.ng-select.ng-select-focused .ng-select-container {
  border-color: $primary-color !important;
}

/* Mensagens de erro */
.error-message {
  color: $error-color;
  font-size: 12px;
  margin-top: 4px;
}

/* Rodapé */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 16px 20px;
  background-color: $secondary-color;
  gap: 12px;
}

/* Botões */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: $border-radius;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition ease;
  min-width: 110px;
}

.cancel-button {
  background-color: $secondary-dark;
  color: $text-primary;
}

.cancel-button:hover {
  background-color: darken($secondary-dark, 5%);
}

.confirm-button {
  background-color: $primary-color;
  color: $secondary-light;
}

.confirm-button:hover {
  background-color: $primary-dark;
}

.reset-button {
  background-color: transparent;
  color: #000;
  border:  1px solid #000;

}

.reset-button:hover {
    color: $primary-color;
    border:  1px solid $primary-color;
}

/* Responsividade */
@media (max-width: 767px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-title {
    font-size: 16px;
  }
  
  .action-button {
    min-width: 0;
    padding: 8px;
  }
  
  .button-text {
    display: none;
  }
  
  .action-button mat-icon {
    margin: 0;
  }
  
  .modal-footer {
    padding: 12px;
  }
}