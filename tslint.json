{"extends": "tslint:recommended", "rulesDirectory": ["codelyzer"], "rules": {"array-type": false, "arrow-parens": false, "deprecation": {"severity": "warn"}, "import-blacklist": [true, "rxjs/Rx"], "interface-name": false, "max-classes-per-file": false, "max-line-length": [true, 280], "member-access": false, "member-ordering": [true, {"order": ["static-field", "instance-field", "static-method", "instance-method"]}], "no-consecutive-blank-lines": false, "no-console": [true, "debug", "info", "time", "timeEnd", "trace"], "no-empty": false, "no-inferrable-types": [true, "ignore-params"], "no-non-null-assertion": true, "no-redundant-jsdoc": true, "no-switch-case-fall-through": true, "no-use-before-declare": true, "no-var-requires": false, "object-literal-key-quotes": [true, "as-needed"], "object-literal-sort-keys": false, "ordered-imports": false, "quotemark": [true, "single"], "trailing-comma": false, "no-output-on-prefix": true, "use-input-property-decorator": true, "use-output-property-decorator": true, "use-host-property-decorator": true, "no-input-rename": true, "no-output-rename": true, "use-life-cycle-interface": true, "use-pipe-transform-interface": true, "component-class-suffix": true, "directive-class-suffix": true}}