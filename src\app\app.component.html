<router-outlet></router-outlet>
  <div *ngIf="flgExibeSpinner">
    <app-spinner></app-spinner>
  </div>
<!-- <button class="button button-primary" (click)="subscribeToNotifications()">
    Subscribe
  </button> -->


<!-- <app-login ></app-login> -->
<!-- <app-login *ngIf="!mostrarMenu && !recuperarSenha"></app-login>

 <app-menu *ngIf="mostrarMenu"></app-menu>

<app-recuperar-senha *ngIf="!mostrarMenu && recuperarSenha"></app-recuperar-senha>

<app-termo-privacidade *ngIf="privaci "></app-termo-privacidade>


<app-streaming1 *ngIf="stream1 && !stream && !conect"></app-streaming1>
<app-teste-conexao *ngIf="conect && !stream && !stream1 "></app-teste-conexao> -->

<!-- <app-streaming *ngIf="stream && !conect && !stream1"></app-streaming>

<app-streaming1 *ngIf="stream1 && !stream && !conect"></app-streaming1>


<app-teste-conexao *ngIf="conect && !stream && !stream1 "></app-teste-conexao>



<div role="main" class="row" *ngIf="mostrarMenu && !stream && !conect && !stream1">
    
    <div class="col-lg-2 col-md-2 no-mobile" style="padding: 0;"><app-colunaesquerda></app-colunaesquerda></div>
    <div class="col-lg-8 col-md-12 col-sm-12 col-xs-12" style="padding: 0;"><router-outlet></router-outlet></div>
    <div class="col-lg-2 col-md-2 no-mobile" style="padding: 0;"><app-colunadireita></app-colunadireita> </div>
       
</div> -->