.div-login {
    margin-top: 12%;
    // width: 55%;
    /* float: right; */
    margin-left: 22%;
    position: relative;
    bottom: 0;
    margin-right: 10px;
  }
  header {
    min-height: 45px;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    border-bottom: 1px solid #ccc;
    background: #ececec;
    z-index: 2;
  }
  .div-login1{
    margin-top: 8%;
    // width: 55%;
    /* float: right; */
    // margin-left: 22%;
    position: relative;
    bottom: 0;
    // margin-right: 10px;
  }
  
  h3 {
    font-family: "Arvo", serif;
    font-weight: bold;
  }
  
  .footer-faso {
    position: absolute;
    width: 100%;
    bottom: 0px;
    padding: 10px;
    background: white;
  }
  
  .bandeira{
    width: 21px;
    margin-left: 5px;
    margin-right: 5px;
  }
  
  .lang-br{
    margin-left: 0px;
    font-weight: bold;
    align-items:center;
    color:#777;
    font-size: 12px;
  }
  
  .lang-es{
    margin-right: 15px;
    font-weight: bold;
    align-items:center;
    color:#777;
    font-size: 12px;
  }
  
  .lang-eng{
    font-weight: bold;
    align-items:center;
    color:#777;
    font-size: 12px;
  }
  
  
  
  .title-country{
    margin-bottom: 0.4rem!important;
    color: #666;
      font-size: small;
      font-weight: bold;
  }
  
  .cel{
    margin-left: 22px;
  }
  
  .email{
    margin-right: 7px;
  }
  
  .space-icon {
    margin-right: 4px;
  }

  .logo-footer {
    width: 8%;
}
  
  .input-login {
    width: 100%;
  }
  
  .esqueceu-senha {
    margin-top: -30px;
    padding-top: 5px;
  }
  
  .logo-controler {
    width: 100%;
  }
  .a-esquecisenha {
    color: rgb(50, 58, 165);
    text-decoration: none;
  }
  .padding-10 {
    padding: 10px;
    color:rgba(0,0,0,.6);
  }
  
  .padding-5{
    padding: 5px;
  }
  
  .danger-baloon {
    height: 55px;
    border-radius: 5px;
    background: #ffbfbf;
    font-weight: 600;
    text-align: center;
    padding-top: 2vh;
    margin-bottom: 10px;
  }
  
  .background-login {
    background-image: url(/assets/build/img/bg-5.jpg);
    background-attachment: fixed;
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;
    width: 100vw;
    position: absolute;
    height: 99vh;
    overflow-y: hidden;
    overflow-x: hidden;
    // margin-top: 65px;
  }

  .footer-faso-novo {
    position: absolute;
    width: 100%;
    bottom: 0px;
    padding: 10px;
    height: auto;
    display: flex;
    justify-content: space-around;
    background: #ececec;
  }
  
  .logoRodapefooter {
    max-height: 40px;
  }
  
  @media(max-width:1450px){
   .background-login{
    height: 99vh;
   }
  }

  @media (max-width: 1050px) {
    .telaGrande {
      display: none;
    }
  }
  
  @media (max-width: 1024px) {
    .div-login {
      margin-top: 8%;
      // width: 60%;
      left: 0%;
    }
  
    .logo-controler {
      width: 90%;
    }
  
    .padding-10 {
      padding: 0px;
    }
  
    .background-login{
      height: 99vh;
    }
  }
  
  @media (max-width: 768px) {
    .div-login {
      margin-top: 8%;
      // width: 80%;
      // left: -7%;
       margin-right: auto; 
       margin-left: auto; 
      
    }
    .input-login {
      padding: 10px;
    }
    .row-button{
      padding: 10px;;
    }
    .cel{
      margin-left: 0px;
    }
  
    .mob-b{width: 55px;}
  
    .font-0{
      color: white;
    }
  
    .f-12{
      font-size: 12px;
    }
  
    .logo-footer{
      width: 7%;
  }
  
    .background-login {
      background-image: url(/assets/build/img/bg-4.png);
      background-attachment: fixed;
      background-position: top center;
      background-repeat: no-repeat;
      background-size: cover;
      width: 100vw;
      position: absolute;
      height: 100vh;
      overflow-y: hidden;
    }
  
    .footer-faso{
      display:none;
    }
  }
  @media (max-width: 600px) {
    .div-login {
      margin-top: 25%;
      // width: 90%;
      // left: -2%;
      margin-left: 0px;
      margin-right: 0px;
    }
    .div-login1 {
      margin-top: 10%;
      position: relative;
      bottom: 0;
    }  
  }
  @media (max-width: 425px) {
    .div-login {
      margin-top: 25%;
      // width: 90%;
      // left: -2%;
      margin-left: 0px;
      margin-right: 0px;
    }
    .div-login1 {
      margin-top: 3%;
      position: relative;
      bottom: 0;
    } 
    .background-login {
      height: 100vh;
      padding-top: 30px;
      background-image:unset;
    }
    .logo-footer {
      width: 25%;
    }
  
    .padding-10 {
      padding: 10px;
    }
  
    .footer-faso{
      position: fixed;
      width: 100%;
      bottom: 0px;
      padding: 10px;
      background: white;
      border-top: 1px solid #ddd;
    }
  
    .a-esquecisenha{
      cursor: pointer;
      font-weight: 500;
      font-size: 12px;
      text-align: center;
      text-align: left;
      padding-right: 25px;
    }
  }
  
  @media (max-width: 375px) {
    .div-login {
      margin-top: 20%;
      // width: 94%;
      left: 0%;
      margin-left: 0px;
      margin-right: 0px;
    }
    .example-form {
      padding: 10px;
    }
    .div-login1 {
      margin-top: 6%;
      position: relative;
      bottom: 0;
    } 
  }
  
  @media (max-width: 320px) {
    .padding-10 {
      padding: 15px;
    }
  
    .div-login {
      margin-top: 35%;
      // width: 95%;
      left: 0%;
      margin-left: 0px;
      margin-right: 0px;
    }
  
    .background-login {
      height: 100vh;
      margin-top: 0!important;
    }
  }
  
  ////WXGA VARIANTE
  @media  (height:635px) {
    .background-login {
      height: 95vh;
      margin-top: 0!important;
    }
  }
  ////WXGA VARIANTE v2
  @media  (height:667px) {
    .background-login {
      height: 100vh;
      margin-top: 0!important;
    }
  }
  //WXGA
  @media (height: 768px) {
    .background-login {
      height: 90vh;
    }
  }
  //FHD variante
  @media  (height: 979px) {
    .background-login {
      height: 90vh;
    }
  }
  @media (height: 1080px) {
    .background-login {
      height: 90vh;
    }
  }
  @media (height: 860px) {
    .background-login {
      height: 90vh;
    }
  }
  @media (height: 892px) {
    .background-login {
      height: 90vh;
    }
  }
  @media (height: 993px) {
    .background-login {
      height: 90vh;
    }
  }


  
.Title-config {
    color: #666;
    font-size: 15px;
    text-transform: capitalize;
}

.clinic {
    font-size: 12px!important;
}

.menu-icon {
    height: 25px;
    font-size: 25px;
    margin-left: 22px;
}

.menu-icon-drop {
    height: 25px;
    font-size: 25px;
    margin-left: 10px;
}

.body-config {
    white-space: pre-wrap;
    padding: 5px;
    line-height: 20px;
}

.dropdown-config {
    min-width: 250px;
    left: -90px !important;
    padding-top: 10px !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    padding-bottom: 10px !important;
    border: none;
}

.painel-config {
    border-top: 1px dashed #1265b9;
    padding: 10px;
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.19);
}

.lateral-menu {
    padding-top: 10px;
}

.menu-expanded {
    display: flex;
    padding: 15px;
    transition: all 2s;
    cursor: pointer;
}

.menu-expanded:hover {
    background: #0b2638a3;
}

i.fa {
    font-size: 25px;
    padding: 9px;
    width: 30px;
    text-align: center;
    text-shadow: 1px 1px 1px #000;
}

.leftMenu {
    height: 100%;
    margin-top: 60px;
    background-color: #1265b9;
    position: fixed;
    left: 0;
    top: 0;
    width: 50px;
    transition: all ease 1s;
    overflow: hidden;
    transition: 0.5s cubic-bezier(0.8, 0.5, 0.2, 1.4);
    // box-shadow: 1px 4px 8px 4px rgba(0, 0, 0, 0.3);
}

  
  