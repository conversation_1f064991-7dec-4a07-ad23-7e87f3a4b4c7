import { Usuario } from './usuario';
import { Contato } from './contato';
import { Endereco } from './endereco';
import { Pessoa } from './pessoa';

export class Medico {

    idMedico?: number;
    crm?: string;
    idUsuarioAcesso?: number;
    flgInativo?: boolean;
    dtaCadastro?: Date;
    idUsuarioGerador?: number;
    valorConsulta?: string;
    codigoConvenio?: string;
    retidoClinica?: string; 
    pessoa?:Pessoa;
    idTipoOrientacao?: number;


    especialidade?: EspecialidadeMedico[];
    contato?: Contato;
    endereco?:Endereco;
    usuario?: Usuario;
    horarios?: DiaHora[];
}


export class EspecialidadeMedico {

    IdEspecialidadeMedico?: number;
    DesEspecialidade?: string;
    IdMedico?: number;
    FlgInativo?: boolean;
    DtaCadastro?: Date;
    IdUsuarioGerador?: number;

}

export class RecadoDia {
    IdRecadoDia?: number;
    IdMedico?: number;
    DesRecado?: string;
    DtaDiaRecado?: Date | string;
    FlgVisualizado?: boolean;
    IdUsuarioGerador?: number;
    DtaCadastro?: Date | string;
    FlgInativo?: boolean;
    idClinica?: number;
}

export class DiaHora {

    IdDataTrabalhos?: number;
    DiaSemana?: string;
    HoraEntrada?: string;
    HoraInicioIntervalo?: string;
    HoraFimIntervalo?: string;
    HoraSaida?: string;
    TempoConsulta?: string;
    IdMedico?: number;
    DtaCadastro?: Date;
    IdUsuarioGerador?: number;
}

export class ParametrosGetRecadoDia {
    DtaInicio?: Date;
    DtaFim?: Date;
    inicio?: number;
    fim?: number;
    idMedico?: number;
    idclinica?: number;
    idUsuario?: number;
}

export class ParametrosAtividadesUsuario {
    DtaInicio?: Date;
    DtaFim?: Date;
    inicio?: number;
    fim?: number;
    idclinica?: number;
    TipoUsuario?: string;
    idUser?:number;
    idUsuario?: number;
}