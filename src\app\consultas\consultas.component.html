<!-- •••••••••••••••••••••••••••••• MODAIS •••••••••••••••••••••••••••••• -->
<ngx-smart-modal #ModalAgenda identifier="ModalAgenda" customClass="modern-modal health-modal">
    <div class="modal-container">
        <header class="modal-header">
            <div class="modal-title-container">
                <h3 class="modal-title">{{ 'TELACONSULTAS.FACASEUAGENDAMENTO' | translate }}</h3>
            </div>
            <button class="close-button" (click)="ModalAgenda.close()">
                <mat-icon>close</mat-icon>
            </button>
        </header>

        <main class="modal-content split-content">
            <!-- Coluna da imagem ilustrativa -->
            <div class="image-column">
                <div class="appointment-image"></div>
            </div>

            <!-- Coluna do formulário -->
            <div class="form-column">
                <div class="form-container">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.NOME' | translate }}</mat-label>
                            <mat-icon matPrefix>account_circle</mat-icon>
                            <input matInput [(ngModel)]="nomecliente" disabled>
                        </mat-form-field>
                    </div>

                    <div class="form-field">
                        <label class="select-label">{{ 'TELACONSULTAS.ESPECIALIDADE' | translate }}</label>
                        <ng-select [items]="DadosEspecialidade"
                            placeholder="{{ 'TELACONSULTAS.ESPECIALIDADE' | translate }}" bindLabel="desEspecialidade"
                            bindValue="idEspecialidade" [(ngModel)]="idEspecialidade" class="custom-select" required>
                        </ng-select>
                    </div>

                    <div class="form-field">
                        <label class="select-label">{{ 'TELACONSULTAS.MEDICOS' | translate }}</label>
                        <ng-select [items]="ListaMedicos" placeholder="{{ 'TELACONSULTAS.MEDICOS' | translate }}"
                            bindLabel="nomeMedico" bindValue="idMedico" [(ngModel)]="idMedico" class="custom-select">
                        </ng-select>
                    </div>
                </div>
            </div>
        </main>

        <footer class="modal-footer">
            <button class="action-button cancel-button" (click)="ModalAgenda.close()">
                <mat-icon>cancel</mat-icon>
                <span>{{ 'TELACONSULTAS.CANCELAR' | translate }}</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<ngx-smart-modal #cancelarHorario identifier="cancelarHorario" customClass="modern-modal health-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <div class="modal-title-container">
                <h3 class="modal-title">{{ 'TELACONSULTAS.DESEJACANCELARESSEHORARIO' | translate }}</h3>
            </div>
        </header>

        <main class="modal-content">
            <div class="form-container">


                <div class="form-field">
                    <mat-form-field appearance="outline" class="reason-input">
                        <mat-label>{{ 'TELACONSULTAS.MOTIVODOCANCELAMENTO' | translate }}</mat-label>
                        <textarea matInput [(ngModel)]="Motivocancelameto" (change)="MotivoCampo()" rows="4"
                            maxlength="200"></textarea>
                        <mat-hint>{{Motivocancelameto.length + 0}}/200</mat-hint>
                    </mat-form-field>

                    <div class="error-message" *ngIf="cancelamento">
                        {{ 'TELACONSULTAS.MOTIVODESERPREENCHIDO' | translate }}
                    </div>
                </div>
            </div>
        </main>

        <footer class="modal-footer">
            <div class="actions-container">
                <button class="action-button cancel-button" (click)="cancelarHorario.close()">
                    <mat-icon>cancel</mat-icon>
                    <span>{{ 'TELACONSULTAS.NAO' | translate }}</span>
                </button>

                <button class="action-button confirm-button" (click)="CancelarConsulta()">
                    <mat-icon>check_circle</mat-icon>
                    <span>{{ 'TELACONSULTAS.SIM' | translate }}</span>
                </button>
            </div>
        </footer>
    </div>
</ngx-smart-modal>

<ngx-smart-modal #editarHorario identifier="editarHorario" customClass="modern-modal health-modal emailmodal"
    [dismissable]="false">
    <div class="modal-container">
        <header class="modal-header">
            <div class="modal-title-container">
                <h3 class="modal-title">{{ 'TELACONSULTAS.DESEJAEDITARESSEHORARIO' | translate }} {{dtahora}}</h3>
            </div>
            <button class="close-button" (click)="editarHorario.close()">
                <mat-icon>close</mat-icon>
            </button>
        </header>

        <main class="modal-content">
            <div class="form-container">
                <!-- Paciente e busca por CPF -->
                <div class="form-row">
                    <div class="form-field with-icon">
                        <label class="select-label">{{ 'TELACONSULTAS.PACIENTE' | translate }}</label>
                        <ng-select [items]="DadosPacientes" placeholder="{{ 'TELACONSULTAS.PACIENTE' | translate }}"
                            bindLabel="nome" bindValue="idCliente" [(ngModel)]="IdPaciente" class="custom-select"
                            required>
                        </ng-select>
                        <div class="error-message" *ngIf="paci.invalid">
                            {{getErrorMessagepaci() | translate }}
                        </div>

                        <button class="info-button" [disabled]="IdPaciente == 0 || IdPaciente == undefined"
                            (click)="informacao('Paciente', IdPaciente)">
                            <mat-icon>info</mat-icon>
                        </button>
                    </div>

                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.BUSQUEPELOCPF' | translate }}</mat-label>
                            <input matInput [(ngModel)]="CPF" mask="000.000.000-00" maxlength="14"
                                (change)="CarregaPacientes()">
                        </mat-form-field>
                    </div>
                </div>

                <!-- Médico e especialidade -->
                <div class="form-row">
                    <div class="form-field" *ngIf="!loginComMedico">
                        <label class="select-label">{{ 'TELACONSULTAS.MEDICO' | translate }}</label>
                        <ng-select [items]="ListaMedicos" placeholder="{{ 'TELACONSULTAS.MEDICO' | translate }}"
                            bindLabel="nomeMedico" bindValue="idMedico" [(ngModel)]="IdMedico" [formControl]='medi'
                            (change)="CarregaespecialidadeMedico()" class="custom-select" required>
                        </ng-select>
                        <div class="error-message" *ngIf="medi.invalid">
                            {{getErrorMessagemedi() | translate }}
                        </div>
                    </div>

                    <div class="form-field">
                        <label class="select-label">{{ 'TELACONSULTAS.ESPECIALIDADE' | translate }}</label>
                        <ng-select [items]="DadosEspecialidadeMedico"
                            placeholder="{{ 'TELACONSULTAS.ESPECIALIDADE' | translate }}" bindLabel="desEspecialidade"
                            bindValue="idEspecialidade" [(ngModel)]="Especialidade" (change)="CarregaMedicos()"
                            class="custom-select" required>
                        </ng-select>
                    </div>
                </div>

                <!-- Data e hora -->
                <div class="form-row">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELAAGENDA.DATAEHORA' | translate }}</mat-label>
                            <input matInput [(ngModel)]="dtahora" disabled>
                        </mat-form-field>
                    </div>
                </div>

                <!-- Nova data e hora -->
                <div class="form-row">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.NOVADATA' | translate }}</mat-label>
                            <input matInput [(ngModel)]="dtanova" (keypress)="mascaraData($event)"
                                (blur)="ValidaDtaEd($any($event.target).value)" maxlength="10" required>
                        </mat-form-field>
                        <div class="error-message" *ngIf="Dtanasc">
                            {{ 'TELACADASTROMEDICO.ERRODATA' | translate }}
                        </div>
                        <div class="error-message" *ngIf="DtanascVasil && !Dtanasc">
                            {{ 'TELACADASTROMEDICO.ERROCAMPO' | translate }}
                        </div>
                    </div>

                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.NOVOHORARIO' | translate }}</mat-label>
                            <input matInput type="time" [(ngModel)]="selectedTime"
                                (blur)="VerificaHora($any($event.target).value)" required>
                        </mat-form-field>
                        <div class="error-message" *ngIf="hraEdit">
                            {{ 'TELACADASTROMEDICO.ERRODATA' | translate }}
                        </div>
                        <div class="error-message" *ngIf="hraEditVasil && !hraEdit">
                            {{ 'TELACADASTROMEDICO.ERROCAMPO' | translate }}
                        </div>
                    </div>
                </div>

                <!-- Tipo de agendamento e convênio -->
                <div class="form-row">
                    <div class="form-field">
                        <label class="select-label">{{ 'TELAAGENDA.TIPOAGENDAMENTO' | translate }}</label>
                        <ng-select [items]="dadosTipoAgendamento"
                            placeholder="{{ 'TELAAGENDA.TIPOAGENDAMENTO' | translate }}" bindLabel="desTipoAgendamento"
                            bindValue="idTipoAgendamento" [(ngModel)]="idtipoagendamento" class="custom-select">
                        </ng-select>
                    </div>

                    <div class="form-field">
                        <label class="select-label">Convênio</label>
                        <ng-select [items]="dadosConvenio" placeholder="Convênio" bindLabel="desConvenio"
                            bindValue="idConvenio" [(ngModel)]="idConvenio" (change)="PreencheValorPagamento()"
                            class="custom-select">
                        </ng-select>
                    </div>
                </div>

                <!-- Valor e código de convênio -->
                <div class="form-row">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>Valor</mat-label>
                            <input matInput [(ngModel)]="valorConsulta" [required]='flgExigePagamento'
                                (keypress)="mascaraValor($event)" (change)="mascaraValor($any($event))"
                                (keyup)="mascaraValor($event)">
                            <mat-error *ngIf="valor.invalid">
                                {{getErrorMessagevalorConsulta() | translate }}
                            </mat-error>
                        </mat-form-field>
                    </div>

                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>Cód. Convênio</mat-label>
                            <input matInput [(ngModel)]="codConvenio">
                        </mat-form-field>
                    </div>
                </div>

                <!-- Retorno e observação -->
                <div class="form-row">
                    <div class="form-field checkbox-field">
                        <mat-checkbox [(ngModel)]="FlgRetorno">Retorno</mat-checkbox>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-field full-width">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.OBSERVACAO' | translate }}</mat-label>
                            <textarea matInput [(ngModel)]="DesAgendamento" rows="3" maxlength="150"></textarea>
                            <mat-hint align="end">{{DesAgendamento.length + 0}}/150</mat-hint>
                        </mat-form-field>
                    </div>
                </div>

                <!-- Opção de pagamento com cartão -->
                <div class="form-row payment-toggle" *ngIf="!FlgProntuario && flgHabilitaPagamento">
                    <div class="toggle-container">
                        <span>Exige Pagamento Cartão</span>
                        <mat-slide-toggle [(ngModel)]='flgExigePagamento' color="primary"></mat-slide-toggle>
                    </div>
                </div>

                <!-- Tipo de consulta -->
                <div class="form-row" *ngIf="!flgSomenteProntuario">
                    <div class="radio-group-container">
                        <label class="radio-group-label">Tipo de Consulta</label>
                        <mat-radio-group [(ngModel)]="FlgProntuario" class="radio-group">
                            <mat-radio-button [value]="false" color="primary">Telemedicina</mat-radio-button>
                            <mat-radio-button [value]="true" color="primary">Presencial</mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
            </div>
        </main>

        <footer class="modal-footer">
            <button class="action-button cancel-button" (click)="editarHorario.close()">
                <mat-icon>cancel</mat-icon>
                <span>{{ 'TELACONSULTAS.NAO' | translate }}</span>
            </button>

            <button class="action-button confirm-button" (click)="SalvarConsulta()">
                <mat-icon>check_circle</mat-icon>
                <span>{{ 'TELACONSULTAS.SIM' | translate }}</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<ngx-smart-modal #InforCancelamento identifier="InforCancelamento" customClass="modern-modal health-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <div class="modal-title-container">
                <h3 class="modal-title">{{ 'TELACONSULTAS.DADOSCANCELAMENTO' | translate }}</h3>
            </div>
        </header>

        <main class="modal-content">
            <div class="form-container">
                <div class="form-row">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.CANCELAMENTOFEITO' | translate }}</mat-label>
                            <input matInput disabled [(ngModel)]="DadosInformCancelament.nome">
                        </mat-form-field>
                    </div>

                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.DATACANCELAMENTO' | translate }}</mat-label>
                            <input matInput disabled [(ngModel)]="DadosInformCancelament.Dta">
                        </mat-form-field>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-field full-width">
                        <mat-form-field appearance="outline">
                            <mat-label>{{ 'TELACONSULTAS.MOTIVOCANCELAMENTO' | translate }}</mat-label>
                            <textarea matInput disabled rows="4" [(ngModel)]="DadosInformCancelament.Motivo"></textarea>
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </main>

        <footer class="modal-footer">
            <button class="action-button close-button-footer" (click)="InforCancelamento.close()">
                <mat-icon>check_circle</mat-icon>
                <span>{{ 'TELACONSULTAS.SAIR' | translate }}</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<ngx-smart-modal #InforAvalicao identifier="InforAvalicao" customClass="nsm-centered medium-modal emailmodal">

    <div class="col-md-12" style="max-width: 300px; padding: 15px; margin: 0 auto; justify-content: center;">
        <div class="modal-info title-modal-aval">
            <b> {{ 'TELACONSULTAS.AVALIACAODOPACIENTE' | translate }} </b><br>
        </div>

        <hr class="sep-1" />

        <div class="modal-info stars-modal">
            <div class="row  p-20">
                <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
                <div class="col-12 " style="text-align: left !important; padding: 0;">

                    <form id="avaliacao1" class="p-t-20">
                        <div class="estrelas" *ngFor="let item of quesitosAvaliacao; let i = index">
                            <input type="radio" id="cm_star-empty{{i}}" name="fb{{i}}" value checked />
                            <label for="cm_star-1{{i}}" style="margin-left: 3px;"><i class="fa"></i></label>
                            <input type="radio" [checked]='item.valor >= 1' disabled id="cm_star-1{{i}}" name="fb{{i}}"
                                value="{{item.idQuesito}}" />
                            <label for="cm_star-2{{i}}" style="margin-left: 3px;"><i class="fa"></i></label>
                            <input type="radio" [checked]='item.valor >= 2' disabled id="cm_star-2{{i}}" name="fb{{i}}"
                                value="{{item.idQuesito}}" />
                            <label for="cm_star-3{{i}}" style="margin-left: 3px;"><i class="fa"></i></label>
                            <input type="radio" [checked]='item.valor >= 3' disabled id="cm_star-3{{i}}" name="fb{{i}}"
                                value="{{item.idQuesito}}" />
                            <label for="cm_star-4{{i}}" style="margin-left: 3px;"><i class="fa"></i></label>
                            <input type="radio" [checked]='item.valor >= 4' disabled id="cm_star-4{{i}}" name="fb{{i}}"
                                value="{{item.idQuesito}}" />
                            <label for="cm_star-5{{i}}" style="margin-left: 3px;"><i class="fa"></i></label>
                            <input type="radio" [checked]='item.valor >= 5' disabled id="cm_star-5{{i}}" name="fb{{i}}"
                                value="{{item.idQuesito}}" />
                            <label class="usuario-principal" id="{{item.idQuesito}}"
                                style="margin-left: 15px;">{{item.quesito}}</label>
                        </div>
                    </form>
                    <mat-form-field class="w-100" style="padding-top: 20px;">

                        <textarea matInput placeholder="{{ 'TELACONSULTAS.OBSERVACAO' | translate }}" name="Observações"
                            id="obsAmbiente" disabled style="max-height: 77px; color: black">{{ObsAvaliacao}}</textarea>
                    </mat-form-field>
                </div>
            </div>
        </div>

        <div class="logo-medicina-modal">
            <img class="logo-modal-aval" src="assets/build/img/logo medicina para voce.png">
        </div>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #AvaliacaoNaoFeita identifier="AvaliacaoNaoFeita" customClass="nsm-centered medium-modal emailmodal">
    <div class style=" width: 300px; height: 200px;">

        <div class="modal-info aval-naoenviada">
            <b> {{ 'TELACONSULTAS.AVALIACAONAOENVIADA' | translate }}</b><br>
            <b>{{ 'TELACONSULTAS.PELOPACIENTE' | translate }}</b>
        </div>

        <hr class="sep-1" />

        <div class="modal-info col-md-12">
            <button class="btn-primary " mat-raised-button style="color:white;" (click)="AvaliacaoNaoFeita.close()"
                style="margin-right: 2%;">
                <mat-icon style="outline: 0px;">clear</mat-icon> {{
                'TELACONSULTAS.SAIR' | translate }}
            </button>
        </div>

        <div class="logo-medicina-modal">
            <img class="logo-modal-footer" src="assets/build/img/logo medicina para voce.png">
        </div>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #testeConexao identifier="testeConexao" customClass="nsm-centered medium-modal emailmodal">
    <div class="background-delete" style=" width: 530px;">
        <div style="justify-content: center;  display: flex;">
            <div class="modal-info" style=" margin-top: 33%; margin-right: 62%;">
                <b> {{ 'TELACONSULTAS.FACAOTESTEDECONEXAO' | translate
                    }}</b><br>
                <b>{{ 'TELACONSULTAS.CAMERAEAUDIO' | translate }}</b>
            </div>
            <mat-icon style="position: absolute; font-size: 83px; margin-left: 14%; top: 30px; color: white;">
                cell_wifi
            </mat-icon>
            <mat-icon style=" position: absolute; font-size: 100px; margin-left: 31%; top: 30px; color: white;">
                videocam
            </mat-icon>
            <mat-icon style=" position: absolute; font-size: 88px; top: 30px; color: white;">
                volume_up
            </mat-icon>
        </div>
    </div>

    <div class="modal-info">
        <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4">
            <button class="btn-primary " mat-raised-button style="color:white;" (click)="irTesteConexao()"
                style="margin-right: 2%;">
                <mat-icon>cell_wifi</mat-icon> {{ 'TELACONSULTAS.IRPARAOTESTE' |
                translate }}
            </button>

            <button class="btn-primary " mat-raised-button style="color:white;" (click)="testeConexao.close()"
                style="margin-right: 2%;">
                <mat-icon style="outline: 0px;">clear</mat-icon> {{
                'TELACONSULTAS.SAIR' | translate }}
            </button>
        </div>
    </div>
</ngx-smart-modal>

<ngx-smart-modal #InforUsuario identifier="InforUsuario"
    customClass="nsm-centered medium-modal modal form-modal-Cliente emailmodal" style="padding: 0 !important;">
    <!-- <div class="col-md-12 background-Iniciar" style="display: flex; height: 100px; width: 100% !important">
                <div class="modal-info" style="padding-left: 30px; align-self: center;">
                    <b> {{ 'TELAAGENDA.DADOS' | translate }} {{DadosInformUsuario.tipoUsuario}}</b><br>
                </div>
                <mat-icon style="color: white;font-size: 88px;margin-left: 30%;">
                     perm_identity</mat-icon>
            </div> -->
    <div class="modal-info-pac">
        <div class="col-md-12 header-infopac">

            <div class="col-12" style=" align-self: flex-end;">

                <b class="title-infopac-modal"> Informações do
                    {{DadosInformUsuario.tipoUsuario}}</b>

            </div>
            <!--<div class="col-6" style="align-self: center;">
                    <mat-icon style="color: white; font-size: 88px; margin-left: 30%;">
                        perm_identity</mat-icon>
                </div>-->
        </div>

        <hr class="sep-1" />

        <div class="modal-info">

            <div class="col-md-12 col-sm-12 row mt-3" style=" margin-left: 0; margin-right: 0;">
                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled name="Nome"
                        [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" disabled name="CPF"
                        mask="000.000.000-00" [(ngModel)]="DadosInformUsuario.cpf" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email" disabled
                        name="Email" [(ngModel)]="DadosInformUsuario.email" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" disabled name="Celular"
                        mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.celular" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.TELEFONE' | translate }}" disabled name="Telefone"
                        mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.tel" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.TELEFONECOM' | translate }}" disabled
                        name="TelComerciar" mask="(00) 00000-0000" style="
      color: black;" [(ngModel)]="DadosInformUsuario.telComer">
                </mat-form-field>

                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">

                    <button class="btn-primary " mat-raised-button style="color:white;" (click)="InforUsuario.close()"
                        style="margin-right: 2%;">
                        <mat-icon style="outline: 0px;">clear</mat-icon> {{
                        'TELAAGENDA.SAIR' | translate }}
                    </button>

                </div>

            </div>
        </div>
    </div>
    <div class="logo-medicina-modal">
        <img class="logo-modal-footer" src="assets/build/img/logo medicina para voce.png">
    </div>
</ngx-smart-modal>

<ngx-smart-modal #Avaliacao identifier="Avaliacao" customClass="nsm-centered medium-modal emailmodal">
    <div class="modal-aval-pendente">
        <div class="col-md-6 div-avaliacao">

            <div class="col-md-6 col-sm-12 modal-avaliacao">
                <div class="row form-content p-20" style="padding-bottom:0px">
                    <h4 class="p-b-20 title-avaliacao"
                        style="text-transform: uppercase; padding-bottom: 20px; margin: 0 auto;">
                        {{ 'TELACONSULTAS.FACASUAAVALIACAOPENDENTE' | translate
                        }} teste
                    </h4>

                    <hr class="sep-1" />

                    <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
                    <div class="col-12 div-estrelas-modal">
                        <form id="avaliacao">
                            <div class="estrelas" *ngFor="let item of quesitosAvaliacao; let i = index;"
                                style="margin-top: 1%;">
                                <input type="radio" id="cm_star-empty{{i}}" name="fb{{i}}" value checked />
                                <label for="cm_star-1{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                                <input type="radio" id="cm_star-1{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                                <label for="cm_star-2{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                                <input type="radio" id="cm_star-2{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                                <label for="cm_star-3{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                                <input type="radio" id="cm_star-3{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                                <label for="cm_star-4{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                                <input type="radio" id="cm_star-4{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                                <label for="cm_star-5{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                                <input type="radio" id="cm_star-5{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                                <label class="usuario-principal" id="{{item.idQuesito}}"
                                    style="margin-left: 5%;">{{item.quesito}}</label>
                            </div>
                        </form>

                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12" hintLabel="Máx. 100" appearance="outline"
                            floatLabel="always">
                            <mat-label>{{ 'TELACONSULTAS.ALGUMAOBSERVACAO' |
                                translate }}</mat-label>
                            <textarea matInput [(ngModel)]="AlgumaObservacao" name="Observações" id="obs"
                                style="max-height: 77px; color: black;" maxlength="100"></textarea>
                            <mat-hint align="end">{{ AlgumaObservacao.length +
                                0}}/100</mat-hint>
                        </mat-form-field>
                    </div>

                </div>
                <div class="col-12 row-button text-center p-t-20">
                    <!-- <button mat-flat-button (click)="ModalAgenda.close()" class="input-align btn btn-danger"> NÃO </button> -->
                    <button mat-flat-button (click)="EnviarAvaliacao()" class="input-align btn btn-primary big-btn">
                        {{ 'TELACONSULTAS.ENVIAR' | translate }}
                    </button>

                </div>
            </div>
        </div>

        <div class="logo-modal-avaliacao">
            <img class="logo-aval-footer" src="assets/build/img/logo medicina para voce.png">
        </div>

    </div>

</ngx-smart-modal>

<ngx-smart-modal #ConfirmarChegada identifier="ConfirmarChegada" customClass="modern-modal health-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <div class="modal-title-container">
                <h3 class="modal-title">Confirmar Chegada</h3>
            </div>
        </header>

        <footer class="modal-footer">
            <button class="action-button cancel-button" (click)="ConfirmarChegada.close()">
                <mat-icon>cancel</mat-icon>
                <span>Cancelar</span>
            </button>

            <button class="action-button confirm-button" (click)="Checkin()">
                <mat-icon>check_circle</mat-icon>
                <span>Confirmar Chegada</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<ngx-smart-modal #infoConsulta identifier="infoConsulta" customClass="nsm-centered medium-modal emailmodal">
    <div class>
        <div class="modal-info" style="margin: 0 auto; text-align: center; justify-content: center; margin-top: 5%;">
            <b class="title-rapido" style="margin: 0 auto;">Acesso
                Rápido</b><br>
        </div>

        <hr class="sep-1" />

        <div class="modal-info">
            <div class="col-md-12 col-sm-12 row mt-3 "
                style=" margin-left: 0; margin-right: 0; padding: 0; justify-content: center;">

                <div class="link-acesso">

                    <b> Link Acesso:</b><br>

                    <b class="col-10"
                        style="text-align: center; justify-content: center; margin: 0 auto; max-width: 280px; word-wrap: break-word">{{modalInfo.guid}}</b><br><br>

                    <b> Cod.Acesso: {{modalInfo.CodAcesso}}</b>

                </div>

                <div class="col-md-12 div-botoes-rapido">

                    <button class=" button-interactive btn btn-primary "
                        style="margin-right: 2%; margin-top: 2px; padding: 9px;" title="Copiar Link e Código"
                        (click)="copyMessage('Link Acesso: ' +modalInfo.guid  +'\n' +  'Cod.Acesso: ' + modalInfo.CodAcesso)">
                        <i class="fa fa-files-o" style="
                                font-size: 25px; padding: 3px;"> </i>
                        <!-- <mat-icon> file_copy</mat-icon> -->
                    </button>

                    <button class="button-interactive btn btn-primary "
                        style="margin-right: 2%; margin-top: 2px; padding: 9px;" title="Enviar por Email"
                        (click)="EnviarEmailAcesso(modalInfo.idConsulta)">
                        <i class="fa fa-envelope fa-fw fa-1x" style="font-size: 25px; padding: 3px;"> </i>
                        <!-- <mat-icon> email</mat-icon> -->
                        <!-- Enviar por Email  -->
                    </button>

                    <button class="button-interactive btn btn-primary " style="margin-top: 2px; padding: 9px;"
                        title="Enviar por Whatsapp " (click)="EnviarWhatsAcesso(modalInfo.idConsulta)">
                        <i class="fa fa-whatsapp fa-fw fa-1x" style="
                             font-size: 25px; padding: 3px;"> </i>
                    </button>

                </div>

            </div>
        </div>

        <div class="logo-medicina-modal">
            <img class="logo-modal-footer" src="assets/build/img/logo medicina para voce.png">
        </div>

    </div>
</ngx-smart-modal>

<ng-template #ModalConfirmaçãoLista class="ModalConfirmaçãoConsultas">
    <div class="modal-wrapper">
        <button class="close-button" mat-icon-button (click)="fecharModal()">
            &#10005;
        </button>
        <div class="modal-container">
            <h2 class="modal-title">Selecão de Lote</h2>

            <mat-form-field appearance="fill" class="select-field">
                <mat-label>Escolha uma opção</mat-label>
                <mat-select [(ngModel)]="tipoSelecaoFiltroModalConfirmacao"
                    (ngModelChange)="filtrarListaConfirmacaoPaciente()">
                    <mat-option [value]="1">Todas as consultas</mat-option>
                    <mat-option [value]="2">Confirmação de consultas ainda não
                        solicitadas</mat-option>
                    <mat-option [value]="3">Consultas confirmadas pelo
                        paciente</mat-option>
                    <mat-option [value]="4">Consultas canceladas pelo
                        paciente</mat-option>
                    <mat-option [value]="5">Confirmação pendente</mat-option>
                    <mat-option [value]="6">Consultas finalizadas</mat-option>
                </mat-select>
            </mat-form-field>

            <table class="consultas-table">
                <tr>
                    <th colspan="2">Consultas</th>
                    <th>Status</th>
                </tr>
                <tr *ngFor="let consulta of listaConfirmcaoPaciente">
                    <td>
                        <mat-checkbox [(ngModel)]="consulta.flgSelected"
                            *ngIf="consulta.exibeOpcaoValidacao"></mat-checkbox>
                    </td>
                    <td>
                        <span>Paciente: {{consulta.nome | truncate: 25:
                            "…"}}</span>
                        <span>Data: {{consulta.dtaConsulta | date:
                            'dd/MM/yyyy HH:mm'}}</span>
                    </td>
                    <td>
                        {{ consulta.status }}
                    </td>
                </tr>
            </table>

            <div class="savemodal">
                <button mat-flat-button color="primary">Gerar</button>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #ModalSelecaoLoteGuias class="ModalConfirmaçãoConsultas">
    <div class="modal-wrapper">
        <button class="close-button" mat-icon-button (click)="fecharModal()">
            &#10005;
        </button>
        <div class="modal-container">
            <h2 class="modal-title">Confirmação de Consultas</h2>

            <mat-form-field appearance="fill" class="select-field">
                <mat-label>Escolha uma opção</mat-label>
                <mat-select [(ngModel)]="tipoSelecaoFiltroModalConfirmacao"
                    (ngModelChange)="filtrarListaConfirmacaoPaciente()">
                    <mat-option [value]="1">Todas as consultas</mat-option>
                    <mat-option [value]="2">Confirmação de consultas ainda não
                        solicitadas</mat-option>
                    <mat-option [value]="3">Consultas confirmadas pelo
                        paciente</mat-option>
                    <mat-option [value]="4">Consultas canceladas pelo
                        paciente</mat-option>
                    <mat-option [value]="5">Confirmação pendente</mat-option>
                    <mat-option [value]="6">Consultas finalizadas</mat-option>
                </mat-select>
            </mat-form-field>

            <table class="consultas-table">
                <tr>
                    <th colspan="2">Consultas</th>
                    <th>Status</th>
                </tr>
                <tr *ngFor="let consulta of listaConfirmcaoPaciente">
                    <td>
                        <mat-checkbox [(ngModel)]="consulta.flgSelected"
                            *ngIf="consulta.exibeOpcaoValidacao"></mat-checkbox>
                    </td>
                    <td>
                        <span>Paciente: {{consulta.nome | truncate: 25:
                            "…"}}</span>
                        <span>Data: {{consulta.dtaConsulta | date:
                            'dd/MM/yyyy HH:mm'}}</span>
                    </td>
                    <td>
                        {{ consulta.status }}
                    </td>
                </tr>
            </table>

            <div class="savemodal">
                <button mat-flat-button color="primary" (click)="salvarConfirmacaoConsultasLista()">Salvar</button>
            </div>
        </div>
    </div>
</ng-template>

<!-- •••••••••••••••••••••••••••••• FIM MODAIS •••••••••••••••••••••••••••••• -->

<mat-card class="dashboard-card">
    <mat-card-content>
        <div class="card-header">
            <div class="search-filters">
                <div class="search-bar" *ngIf="this.usuarioLogadoService.getIdTipoUsuario() != 4">
                    <!-- Busca de médico -->
                    <mat-form-field appearance="outline" class="w-100">
                        <mat-label>{{ 'TELACONSULTAS.MEDICO' | translate }}</mat-label>
                        <input matInput (keyup.enter)="CarregaConsultas()" name="pesquisa" [(ngModel)]="pesquisaMedico">
                        <button mat-icon-button matSuffix class="search" (click)="CarregaConsultas()">
                            <mat-icon>search</mat-icon>
                        </button>
                    </mat-form-field>

                    <!-- Busca de paciente -->
                    <mat-form-field appearance="outline" class="w-100" *ngIf="tipoUsuario != 'Paciente'">
                        <mat-label>{{ 'TELACONSULTAS.PACIENTE' | translate }}</mat-label>
                        <input matInput (keyup.enter)="CarregaConsultas()" name="pesquisa"
                            [(ngModel)]="pesquisaPaciente">
                        <button mat-icon-button matSuffix class="search" (click)="CarregaConsultas()">
                            <mat-icon>search</mat-icon>
                        </button>
                    </mat-form-field>

                    <div class="filter-row">
                        <!-- Status da consulta -->
                        <div class="filter-item">
                            <ng-select [items]="DadosStatus" (click)="carregaDadosStatus()"
                                (change)="CarregaConsultas()"
                                placeholder="{{ 'TELACONSULTAS.STATUSDACONSULTA' | translate }}" [(ngModel)]="status">
                            </ng-select>
                        </div>

                        <!-- Consultas de hoje -->
                        <div class="filter-item">
                            <mat-slide-toggle [(ngModel)]='Hoje' (change)="DataHj()" color="primary">
                                {{ 'TELACONSULTAS.CONSULTASDEHOJE' | translate }}
                            </mat-slide-toggle>
                        </div>

                        <!-- Data da consulta -->
                        <div class="filter-item">
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-label>{{ 'TELACONSULTAS.DATADACONSULTA' | translate }}</mat-label>
                                <input matInput name="DataConsulta" (keyup)="ValidaDta($any($event.target).value)"
                                    (keypress)="mascaraData($event)" [(ngModel)]="DtaConsulta" maxlength="10">
                            </mat-form-field>
                            <div class="validation-error" *ngIf="DtaErrado == true">
                                {{ 'TELACADASTROMEDICO.ERRODATA' | translate }}
                            </div>
                        </div>
                    </div>
                    <!-- Botões de ação -->
                    <div class="action-buttons">
                        <div *ngIf="tipoUsuario != 'Paciente'" class="button-container">
                            <button mat-flat-button class="btn-primary" (click)="irPAgenda()">
                                <mat-icon>add</mat-icon>
                                <span>{{ 'TELACONSULTAS.CRIARAGENDAMENTO' | translate }}</span>
                            </button>

                            <button mat-mini-fab class="btn-accent" matTooltip="Faturamento"
                                (click)="AbrirModalFaturamentoGeral()">
                                <mat-icon>request_quote</mat-icon>
                                <span>Faturamento</span>
                            </button>
                        </div>

                        <div *ngIf="!naotemanalises" class="button-container">
                            <button mat-icon-button matTooltip="Modal confirmação geral" class="modalConfirmacao"
                                (click)="abrirModal()">
                                <img src="../../assets/icons/whatsIconConfirm.svg" alt="Confirmar">
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Calendário duplo -->
                <!-- <div class="calendar-container" *ngIf="this.usuarioLogadoService.getIdTipoUsuario() != 4 ">
                    <div id="calendar-dash" class="calendar-wrapper" fxLayout="row" fxLayout.xs="column" fxLayoutWrap
                        fxLayoutGap="0.5%" fxLayoutAlign="center">
                        <mat-calendar [selected]="DtaConsulta" (selectedChange)="onSelect($event)">
                        </mat-calendar>
                    </div>
                    <div id="calendar-dash" class="calendar-wrapper" fxLayout="row" fxLayout.xs="column" fxLayoutWrap
                        fxLayoutGap="0.5%" fxLayoutAlign="center">
                        <mat-calendar #calendar2 [startAt]="DtaConsultaFim" [selected]="DtaConsultaFim">
                        </mat-calendar>
                    </div>
                </div> -->
            </div>
        </div>
        <!-- Lista de consultas para mobile -->
        <div class="consultation-mobile-list">
            <!-- Mensagem quando não há consultas -->
            <div class="empty-list-indicator" *ngIf="naotemanalises">
                <mat-icon>event_busy</mat-icon>
                <p>Sem consultas cadastradas</p>
            </div>
            <div class="consultation-card" *ngFor="let item of consultas; let i = index">
                <div class="card-header">
                    <!-- Status indicators -->
                    <div class="status-indicators">
                        <mat-icon *ngIf="item?.flgProntuario === false">videocam</mat-icon>
                        <div class="indicator"
                            *ngIf="item?.flgPacienteEntrouConsulta && tipoUsuario != 'Paciente' && !item?.flgRealizada && !item?.flgInativo">
                        </div>
                    </div>

                    <!-- Título (nome do médico ou paciente) -->
                    <div class="title">
                        <h3 *ngIf="tipoUsuario == 'Paciente'">{{item?.medico?.pessoa?.nomePessoa}}</h3>
                        <h3 *ngIf="tipoUsuario == 'Médico'">{{item?.paciente?.pessoa?.nomePessoa}}</h3>
                        <h3 *ngIf="tipoUsuario != 'Médico' && tipoUsuario != 'Paciente'">
                            <div>Paciente: {{item?.paciente?.pessoa?.nomePessoa}}</div>
                            <div>Médico: {{item?.medico?.pessoa?.nomePessoa}}</div>
                        </h3>
                    </div>

                    <!-- Status stamps (Finalizada/Cancelada) -->
                    <div class="status-stamp">
                        <img *ngIf="item?.flgRealizada" src="{{ 'TELACONSULTAS.CARIMBOFINALIZADA' | translate }}"
                            alt="Finalizada">
                        <img *ngIf="item?.flgInativo" src="{{ 'TELACONSULTAS.CARIMBOCANCELADA' | translate }}"
                            alt="Cancelada">
                    </div>
                </div>

                <!-- Informações da consulta -->
                <div class="card-body">
                    <div class="info-item" *ngIf="tipoUsuario != 'Paciente'">
                        <span class="label">{{ 'TELACONSULTAS.TIPODEAGENDAMENTO' | translate }}</span>
                        <span class="value">{{item?.flgProntuario ? 'Presencial' : 'Telemedicina'}}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">{{ 'TELACONSULTAS.DATADACONSULTA' | translate }}</span>
                        <span class="value">{{item?.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}</span>
                    </div>
                    <div class="info-item" *ngIf="tipoUsuario != 'Paciente'">
                        <span class="label">{{ 'TELACONSULTAS.TEMPODACONSULTA' | translate }}</span>
                        <span class="value">{{item?.tempoConsulta}}</span>
                    </div>
                </div>

                <!-- Botões de ação -->
                <div class="mobile-action-buttons">
                    <!-- <button matTooltip="Exibe objeto" (click)="consolePrintVariavel(item)">
                        <mat-icon>cookie</mat-icon> i
                    </button> -->

                    <button class="fab-toggle" mat-fab (click)="openToggle(i)">
                        <mat-icon>{{toggle[i] ? 'keyboard_arrow_down' : 'keyboard_arrow_up'}}</mat-icon>
                    </button>

                    <div class="fab-menu" [class.open]="toggle[i]">
                        <!-- Botões específicos para médicos/administradores -->
                        <ng-container *ngIf="item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Paciente'">
                            <!-- Perfil do paciente -->
                            <button mat-mini-fab matTooltip="{{'TELACONSULTAS.PERFILDOPACIENTE' | translate}}"
                                *ngIf="item?.flgInativo" (click)="informacao('Paciente', item?.paciente?.idCliente)">
                                <mat-icon>person</mat-icon>
                            </button>

                            <!-- Editar consulta -->
                            <button mat-mini-fab matTooltip="{{'TELACONSULTAS.EDITAR' | translate}}"
                                *ngIf="!item?.flgRealizada && !item?.flgInativo"
                                (click)="editarConsulta(item?.idConsulta)">
                                <mat-icon>edit</mat-icon>
                            </button>

                            <!-- Cancelar consulta -->
                            <button mat-mini-fab matTooltip="{{'TELACONSULTAS.CANCELAR' | translate}}"
                                *ngIf="!item?.flgRealizada && !item?.flgInativo"
                                (click)="CancelaValue(item?.idConsulta)">
                                <mat-icon>delete</mat-icon>
                            </button>
                        </ng-container>

                        <!-- Botões comuns -->
                        <!-- Avaliação da consulta -->
                        <button mat-mini-fab matTooltip="{{'TELACONSULTAS.AVALIACAODACONSULTA' | translate}}"
                            *ngIf="item?.flgRealizada && !item?.flgInativo"
                            (click)="infoAvaliacao(item?.idConsulta, item?.paciente?.idCliente)">
                            <mat-icon>star</mat-icon>
                        </button>

                        <!-- Motivo do cancelamento -->
                        <button mat-mini-fab matTooltip="{{'TELACONSULTAS.MOTIVODOCANCELAMENTO' | translate}}"
                            *ngIf="item?.flgInativo && !item?.flgRealizada"
                            (click)="infoCancelamento(item?.idConsulta)">
                            <mat-icon>cancel</mat-icon>
                        </button>

                        <!-- Iniciar consulta (em andamento) -->
                        <button mat-mini-fab matTooltip="{{'TELACONSULTAS.INICIARCONSULTA' | translate}}"
                            *ngIf="item?.flgAndamento && !!item?.flgRealizada && FlgSomenteProntuario !== true"
                            class="highlight-button" (click)="Consulta(item?.idConsulta, item?.flgProntuario)">
                            <mat-icon>play_arrow</mat-icon>
                        </button>

                        <!-- Iniciar consulta (pronta) -->
                        <button mat-mini-fab matTooltip="{{'TELACONSULTAS.INICIARCONSULTA' | translate}}"
                            *ngIf="!item?.flgRealizada && !item?.flgInativo && FlgSomenteProntuario !== true"
                            class="pulse-button highlight-button"
                            (click)="Consulta(item?.idConsulta, item?.flgProntuario)">
                            <mat-icon>play_arrow</mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de consultas para desktop -->
        <div class="consultation-desktop-list">
            <div class="consultation-list-container">
                <!-- Mensagem quando não há consultas -->
                <div class="empty-list-indicator" *ngIf="naotemanalises">
                    <mat-icon>event_busy</mat-icon>
                    <p>Sem consultas cadastradas</p>
                </div>

                <mat-accordion *ngIf="!naotemanalises">
                    <mat-expansion-panel *ngFor="let item of consultas; let i = index" hideToggle
                        [expanded]="step === item?.idConsulta" (opened)="setStep(item?.idConsulta || 0)">

                        <mat-expansion-panel-header>
                            <div class="panel-header-container">
                                <!-- Paciente/Médico -->
                                <div class="panel-column patient-doctor">
                                    <div class="status-icon">
                                        <mat-icon *ngIf="item?.flgInativo || item?.flgRealizada">face</mat-icon>
                                        <mat-icon *ngIf="item?.flgAndamento && !item?.flgRealizada && !item?.flgInativo"
                                            class="active">directions_run</mat-icon>
                                        <mat-icon
                                            *ngIf="!item?.flgAndamento && !item?.flgRealizada && !item?.flgInativo && item?.flgCheckInPaciente"
                                            class="ready">emoji_people</mat-icon>
                                        <mat-icon
                                            *ngIf="!item?.flgAndamento && !item?.flgRealizada && !item?.flgInativo && !item?.flgCheckInPaciente">face</mat-icon>
                                    </div>

                                    <!-- Nome do paciente ou médico -->
                                    <span class="name" *ngIf="tipoUsuario === 'Médico'">
                                        Paciente: {{item?.paciente?.pessoa?.nomePessoa || '' | truncate: 25: "…"}}
                                    </span>
                                    <span class="name" *ngIf="tipoUsuario === 'Paciente'">
                                        Médico: {{item?.medico?.pessoa?.nomePessoa || '' | truncate: 25: "…"}}
                                    </span>
                                    <span class="name" *ngIf="tipoUsuario !== 'Médico' && tipoUsuario !== 'Paciente'">
                                        <div>Paciente: {{item?.paciente?.pessoa?.nomePessoa || '' | truncate: 15: "…"}}
                                        </div>
                                        <div>Dr: {{item?.medico?.pessoa?.nomePessoa || '' | truncate: 15: "…"}}</div>
                                    </span>
                                </div>

                                <!-- Data da consulta -->
                                <div class="panel-column date">
                                    Data: {{item?.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}
                                </div>

                                <!-- Tipo de pagamento -->
                                <div class="panel-column payment-type">
                                    <span>{{item?.pagamento === 'Dinheiro' ? 'Particular' : 'Convênio'}}</span>
                                    <img *ngIf="item?.flgRetorno" src="assets/build/img/R.png" class="retorno-icon"
                                        alt="Retorno">

                                    <!-- Ícone de vídeo para telemedicina -->
                                    <div class="video-call-indicator" *ngIf="item?.flgProntuario === false">
                                        <mat-icon>videocam</mat-icon>
                                        <div class="indicator"
                                            *ngIf="item?.flgPacienteEntrouConsulta && tipoUsuario !== 'Paciente' && !item?.flgRealizada && !item?.flgInativo">
                                        </div>
                                    </div>

                                    <!-- Botão de confirmação whatsapp -->
                                    <button mat-icon-button matTooltip="Confirmar consulta" (click)="modalEnvioMsg()">
                                        <img src="../../assets/icons/whatsIcon.svg" class="whatsapp-icon"
                                            alt="WhatsApp">
                                    </button>
                                </div>

                                <!-- Status do paciente e ações -->
                                <div class="panel-column actions">

                                    <!-- <button mat-icon-button matTooltip="Exibe objeto" (click)="consolePrintVariavel(item)">
                                        <mat-icon>cookie</mat-icon>
                                    </button> -->

                                    <!-- Emoji de status do paciente -->
                                    <button mat-icon-button *ngIf="validaExibieStatusConsulta(item?.dtaConsulta)">
                                        <span class="status-emoji"
                                            *ngIf="validaStatusClienteConsulta(item?.dtaConsulta) === 1"
                                            matTooltip="Paciente pronto">😃</span>
                                        <span class="status-emoji"
                                            *ngIf="validaStatusClienteConsulta(item?.dtaConsulta) === 2"
                                            matTooltip="Paciente esperando">😒</span>
                                        <span class="status-emoji"
                                            *ngIf="validaStatusClienteConsulta(item?.dtaConsulta) === 3"
                                            matTooltip="Paciente esperando a mais de 15 min">😠</span>
                                        <span class="status-emoji"
                                            *ngIf="validaStatusClienteConsulta(item?.dtaConsulta) === 4"
                                            matTooltip="Paciente esperando a mais de 30 min">😡</span>
                                    </button>

                                    <!-- Faturamento -->
                                    <button mat-icon-button matTooltip="Faturamento"
                                        (click)="AbrirModalFaturamento(item)">
                                        <mat-icon>request_quote</mat-icon>
                                    </button>

                                    <!-- Check-in paciente -->
                                    <button mat-icon-button matTooltip="Confirmar chegada do paciente"
                                        *ngIf="!item?.flgCheckInPaciente && (!item?.flgRealizada && !item?.flgInativo)"
                                        (click)="confirmarChegadaPaciente(item?.idConsulta || 0)">
                                        <mat-icon>airline_seat_recline_normal</mat-icon>
                                    </button>

                                    <!-- Observação chegada -->
                                    <button mat-icon-button matTooltip="Observação de chegada"
                                        *ngIf="item?.flgPacienteChegouClinica && !item?.flgCheckInPaciente && (!item?.flgRealizada && !item?.flgInativo)"
                                        (click)="AdicionaObservacaoConsultaComponent(item?.idConsulta || 0, item?.observacaoChegada || '')">
                                        <mat-icon>edit_note</mat-icon>
                                    </button>

                                    <!-- Consulta confirmada -->
                                    <button mat-icon-button matTooltip="Consulta confirmada pelo paciente"
                                        *ngIf="item?.dtaConfirmacao && !item?.flgRealizada && !item?.flgInativo">
                                        <mat-icon class="success-icon">check_circle</mat-icon>
                                    </button>

                                    <!-- Aguardando confirmação -->
                                    <button mat-icon-button matTooltip="Aguardando confirmação do paciente"
                                        *ngIf="!item?.dtaConfirmacao && item?.dtaEnvioPedidoConfirmacao && !item?.flgRealizada && !item?.flgInativo">
                                        <mat-icon>schedule</mat-icon>
                                    </button>

                                    <!-- Avaliação da consulta -->
                                    <button mat-icon-button
                                        matTooltip="{{'TELACONSULTAS.AVALIACAODACONSULTA' | translate}}"
                                        *ngIf="item?.flgRealizada && !item?.flgInativo"
                                        (click)="infoAvaliacao(item?.idConsulta, item?.paciente?.idCliente || 0)">
                                        <mat-icon>star</mat-icon>
                                    </button>

                                    <!-- Check-in -->
                                    <button mat-icon-button matTooltip="Check-in"
                                        *ngIf="item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Paciente' && !item?.flgRealizada && !item?.flgAndamento && !item?.flgInativo && !item?.flgCheckInPaciente"
                                        (click)="ModalChekin(item?.idConsulta)">
                                        <mat-icon>check</mat-icon>
                                    </button>

                                    <!-- Editar consulta -->
                                    <button mat-icon-button matTooltip="{{'TELACONSULTAS.EDITAR' | translate}}"
                                        *ngIf="item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Paciente' && !item?.flgRealizada && !item?.flgInativo"
                                        (click)="editarConsulta(item?.idConsulta)">
                                        <mat-icon>edit</mat-icon>
                                    </button>

                                    <!-- Motivo cancelamento -->
                                    <button mat-icon-button
                                        matTooltip="{{'TELACONSULTAS.MOTIVODOCANCELAMENTO' | translate}}"
                                        *ngIf="item?.flgInativo && !item?.flgRealizada"
                                        (click)="infoCancelamento(item?.idConsulta)">
                                        <mat-icon>cancel</mat-icon>
                                    </button>

                                    <!-- Cancelar consulta -->
                                    <button mat-icon-button matTooltip="{{'TELACONSULTAS.CANCELAR' | translate}}"
                                        *ngIf="item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Paciente' && !item?.flgRealizada && !item?.flgInativo"
                                        (click)="CancelaValue(item?.idConsulta)">
                                        <mat-icon>delete</mat-icon>
                                    </button>

                                    <!-- Iniciar consulta -->
                                    <button mat-icon-button matTooltip="{{'TELACONSULTAS.INICIARCONSULTA' | translate}}"
                                        *ngIf="item?.paciente?.pessoa?.usuario?.desTipoUsuario !== '' && !item?.flgRealizada && !item?.flgInativo && FlgSomenteProntuario !== true"
                                        class="start-button" (click)="Consulta(item?.idConsulta, item?.flgProntuario)">
                                        <mat-icon>play_arrow</mat-icon>
                                    </button>

                                    <!-- Status stamps -->
                                    <div class="status-stamp"
                                        *ngIf="item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Administrador' && item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Atendente'">
                                        <img *ngIf="item?.flgRealizada"
                                            src="{{ 'TELACONSULTAS.CARIMBOFINALIZADA' | translate }}" alt="Finalizada"
                                            class="stamp-icon">
                                        <img *ngIf="item?.flgInativo"
                                            src="{{ 'TELACONSULTAS.CARIMBOCANCELADA' | translate }}" alt="Cancelada"
                                            class="stamp-icon">
                                    </div>
                                </div>
                            </div>
                        </mat-expansion-panel-header>

                        <!-- Conteúdo expandido do painel -->
                        <div class="panel-content">
                            <div class="expanded-info">
                                <!-- Avaliação se finalizada -->
                                <div class="rating-container" *ngIf="item?.flgRealizada">
                                    <div class="star-rating">
                                        <div class="stars">
                                            <input type="radio" id="cm_star-empty{{i}}" name="fb{{i}}" value=""
                                                checked />
                                            <label for="cm_star-1{{i}}"><i class="fa"></i></label>
                                            <input type="radio" [checked]='item?.valorAvaliacao! >= 1' disabled
                                                id="cm_star-1{{i}}" name="fb{{i}}" value="{{item?.idQuesito}}" />
                                            <label for="cm_star-2{{i}}"><i class="fa"></i></label>
                                            <input type="radio" [checked]='item?.valorAvaliacao! >= 2' disabled
                                                id="cm_star-2{{i}}" name="fb{{i}}" value="{{item?.idQuesito}}" />
                                            <label for="cm_star-3{{i}}"><i class="fa"></i></label>
                                            <input type="radio" [checked]='item?.valorAvaliacao! >= 3' disabled
                                                id="cm_star-3{{i}}" name="fb{{i}}" value="{{item?.idQuesito}}" />
                                            <label for="cm_star-4{{i}}"><i class="fa"></i></label>
                                            <input type="radio" [checked]='item?.valorAvaliacao! >= 4' disabled
                                                id="cm_star-4{{i}}" name="fb{{i}}" value="{{item?.idQuesito}}" />
                                            <label for="cm_star-5{{i}}"><i class="fa"></i></label>
                                            <input type="radio" [checked]='item?.valorAvaliacao! >= 5' disabled
                                                id="cm_star-5{{i}}" name="fb{{i}}" value="{{item?.idQuesito}}" />
                                        </div>
                                    </div>
                                </div>

                                <!-- Informações do usuário e consulta -->
                                <div class="info-columns">
                                    <!-- Dados do usuário -->
                                    <div class="info-column" *ngIf="tipoUsuario !== 'Paciente'">
                                        <h4 class="info-title">Dados do Usuário</h4>

                                        <div class="info-item">
                                            <mat-icon>calendar_today</mat-icon>
                                            <span>{{item?.paciente?.pessoa?.dtaNascimento | date: 'dd/MM/yyyy'}}</span>
                                        </div>

                                        <div class="info-item">
                                            <mat-icon>local_phone</mat-icon>
                                            <span>{{item?.paciente?.pessoa?.contato?.telefoneMovel}}</span>
                                        </div>

                                        <div class="info-item"
                                            *ngIf="!item?.flgProntuario && item?.paciente?.pessoa?.usuario?.desTipoUsuario !== 'Paciente' && !item?.flgRealizada && !item?.flgInativo">
                                            <span class="quick-access"
                                                (click)="modalInformativo(item?.idConsulta)">Acesso rápido</span>
                                        </div>

                                        <div class="observation" *ngIf="item?.observacaoChegada">
                                            {{item?.observacaoChegada}}
                                        </div>
                                    </div>

                                    <!-- Dados da consulta -->
                                    <div class="info-column">
                                        <h4 class="info-title">Dados da Consulta</h4>

                                        <div class="info-item">
                                            <span class="label">Consulta:</span>
                                            <span>{{item?.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}</span>
                                        </div>

                                        <div class="info-item">
                                            <span class="label">Tempo da Consulta:</span>
                                            <span>{{item?.tempoConsulta}}</span>
                                        </div>

                                        <div class="info-item" *ngIf="item?.pagamento === 'Dinheiro'">
                                            <span>Consulta Particular</span>
                                        </div>

                                        <div class="info-item" *ngIf="item?.pagamento !== 'Dinheiro'">
                                            <span class="label">Convênio:</span>
                                            <span>{{item?.convenio?.desConvenio}}</span>
                                        </div>

                                        <div class="info-item" *ngIf="item?.flgRetorno">
                                            <span class="highlight">Retorno</span>
                                        </div>
                                    </div>

                                    <!-- Logo do convênio/particular -->
                                    <div class="info-column logo-column">
                                        <div class="logo-container">
                                            <img *ngIf="item?.pagamento === 'Convênio'"
                                                [src]="item?.convenio?.logo == null ? 'assets/build/img/logo-clinica.png' : item?.convenio?.logo"
                                                alt="Logo convênio" class="convenio-logo">

                                            <img *ngIf="item?.pagamento === 'Dinheiro'"
                                                src="assets/build/img/cifrao.png" alt="Particular"
                                                class="convenio-logo">

                                            <img *ngIf="item?.pagamento === 'Convênio' && item?.flgRetorno"
                                                src="assets/build/img/R.png" alt="Retorno" class="retorno-badge">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </mat-expansion-panel>
                </mat-accordion>

                <!-- Botão de carregar mais -->
                <div class="load-more-container"
                    *ngIf="(consultas != undefined && consultas.length > 0) && bOcultaCarregaMais == false">
                    <button mat-flat-button class="btn-secondary" (click)="CarregaMaisConsultas()">
                        {{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}
                    </button>
                </div>
            </div>
        </div>
    </mat-card-content>
</mat-card>