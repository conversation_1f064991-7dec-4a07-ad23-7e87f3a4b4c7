import { trigger, state, transition, animate, style } from '@angular/animations';
  
  export const FadeIn =  
    trigger('openClose',[
              state('open', style({
                opacity:'1',
                display: 'block'
              })),
          
              state('closed', style({
                opacity:'0',
                display: 'none'
              })),
              transition('open => closed', [
                 animate('0.2s')
                ]),
              transition('closed => open',[
                animate('0.2s')
              ]),
            ]
            );

        
 

 