/* VARIÁVEIS DE CORES */
  $primary-color: #2E8B57;
  $primary-light: #f3fff3;
  $primary-dark: #175834;
  $secondary-color: #f4f4f9;
  $secondary-light: #f4f2fc;
  $secondary-dark: #175834;
  $accent-color: #175834;
  $error-color: #dc2626;
  $text-primary: #1e293b;
  $text-secondary: #64748b;
  $border-color: #e2e8f0;
  $bg-color: #f8fafc;
  $card-bg: #ffffff;
  $border-radius: 12px;
  $box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  $transition: 0.2s;
/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', Arial, sans-serif;
  background-color:$bg-color;
  color:$text-primary;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Correções Angular Material */
::ng-deep .mat-button-base {
  font-family: 'Roboto', Arial, sans-serif !important;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color:$card-bg;
  border-radius:$border-radius;
  box-shadow:$box-shadow;
  padding: 24px;
  overflow: hidden;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-icon {
  background-color:$primary-light;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-icon .material-icons {
  color: $primary-color;
  font-size: 24px;
}

.header-title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
}

/* FILTROS */
.filtros {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.busca-container {
  flex: 1;
  max-width: 100%;
}

.busca-field {
  width: 100%;
}

.busca-field ::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

.busca-field ::ng-deep .mat-form-field-flex {
  background-color:$bg-color;
}

.busca-field ::ng-deep .mat-form-field-outline {
  color:$border-color;
}

.busca-field ::ng-deep .mat-form-field-infix {
  padding: 0.5em 0;
}

.busca-field ::ng-deep .mat-icon {
  color: $primary-color;
}

.btn-busca {
  background-color: transparent !important;
  transition: all$transition;
}

.btn-busca:hover {
  transform: scale(1.1);
}

.btn-adicionar mat-icon {
  margin-right: 8px;
  font-size: 18px;
}



.btn-adicionar:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* TOGGLES DE FILTRO */
.filtro-toggles {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 24px;
  gap: 16px;
  background-color: $bg-color;
  padding: 16px;
  border-radius:$border-radius;
}

.toggle-item {
  margin-right: 24px;
  background-color: white;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all$transition;
}

.toggle-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}


/* LISTA DE ANÁLISES */
.lista-container {
  background-color:$bg-color;
  border-radius:$border-radius;
  padding: 8px;
}

.lista-scroll {
  max-height: 56vh;
  overflow-y: scroll;
  padding-right: 8px;
}

.lista-scroll::-webkit-scrollbar {
  width: 6px;
}

.lista-scroll::-webkit-scrollbar-track {
  background:$secondary-light;
  border-radius: 10px;
}

.lista-scroll::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

/* ITEM DE ANÁLISE */
.analise-card {
  background-color: white;
  border-radius:$border-radius;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all$transition;
  border-left: 4px solid transparent;
}

.analise-card:hover {
  transform: translateY(-2px);
  box-shadow:$box-shadow;
  border-left: 4px solid $primary-color;
}

.analise-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.analise-avatar {
  margin-right: 16px;
}

.analise-avatar img {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid$primary-light;
}

.analise-detalhes {
  flex: 1;
}

.analise-nome {
  color:$text-primary;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.analise-data {
  color:$text-secondary;
  font-size: 14px;
  margin: 0;
}

.analise-status {
  display: flex;
  gap: 20px;
  margin: 0 16px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 60px;
}

.status-icon {
  margin-bottom: 8px;
}

.status-icon img {
  width: 32px;
  height: 32px;
}

.status-indicator {
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status-indicator mat-icon {
  font-size: 20px;
}

.status-ok mat-icon {
  color: #22c55e;
}

.status-pendente mat-icon {
  color:$error-color;
}

.analise-acoes {
  display: flex;
  width: 100px;
  justify-content: end;
}

.analise-acoes button {
  margin-left: 8px;
  width: 36px;
  height: 36px;
  background-color: rgba($primary-color, 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all$transition;
}

.analise-acoes button:hover {
  background-color: $primary-color;
  transform: scale(1.1);
}

.analise-acoes button:hover mat-icon {
  color: white;
}

.analise-acoes mat-icon {
  color: $primary-color;
  font-size: 20px;
  transition: color$transition;
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color:$text-secondary;
}

.lista-vazia mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* MODAL DO CHAT */
.chat-modal ::ng-deep .nsm-content {
  border-radius: $border-radius !important;
  padding: 0;
  overflow: hidden;
  width: 90%;
  max-width: 600px;
}

.chat-modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 540px;
}

.chat-header {
  background-color: $primary-color;
  color: white;
  padding: 16px;
  text-align: center;
  border-top-left-radius:$border-radius;
  border-top-right-radius:$border-radius;
}

.chat-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.chat-body {
  padding: 16px;
  overflow-y: auto;
  max-height: 400px;
  background-color: #f8fafc;
}

/* MENSAGENS */
.msg-esquerda, .msg-direita {
  display: flex;
  margin-bottom: 16px;
}

.msg-esquerda {
  justify-content: flex-start;
}

.msg-direita {
  justify-content: flex-end;
}

.mensagem {
  border-radius: 12px;
  padding: 8px 12px;
  max-width: 80%;
  min-width: 120px;
}

.msg-normal {
  background-color: #e6f7ff;
  color:$text-primary;
}

.msg-importante {
  background-color: $primary-color;
  color: white;
}

.mensagem-sistema .msg-wrapper {
  background-color:$secondary-light;
  color:$text-secondary;
  padding: 12px;
  border-radius: 12px;
  text-align: center;
  margin: 0 auto;
  max-width: 80%;
}

.msg-autor {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.msg-texto {
  font-size: 14px;
  margin-bottom: 4px;
  word-break: break-word;
}

.msg-hora {
  font-size: 10px;
  text-align: right;
  opacity: 0.7;
}

/* RESPONSIVIDADE */
@media (max-width: 768px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn-adicionar {
    margin-left: 0;
    margin-top: 16px;
    width: 100%;
    justify-content: center;
  }
  
  .analise-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .analise-info {
    margin-bottom: 16px;
    width: 100%;
  }
  
  .analise-status {
    margin: 16px 0;
    width: 100%;
    justify-content: space-around;
  }
  
  .analise-acoes {
    width: 100%;
    justify-content: flex-end;
  }
}
