import { Component, OnInit } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { Router, RouterModule } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ConvenioService } from 'src/app/service/convenio.service';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
  selector: 'app-pesquisa-convenio',
  templateUrl: './pesquisa-convenio.component.html',
  styleUrls: ['./pesquisa-convenio.component.scss'],
  animations: [
    trigger('openClose', [
      state('open', style({
        opacity: '1',
        display: 'block'
      })),
      state('closed', style({
        opacity: '0',
        display: 'none'
      })),
      transition('open => closed', [
        animate('0.2s')
      ]),
      transition('closed => open', [
        animate('0.2s')
      ]),
    ])
  ],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIcon,
    MatFormFieldModule,
    TranslateModule,
    RouterModule,
    NgxSmartModalModule,
    MatSlideToggleModule,
    MatTooltipModule
  ]
})
export class PesquisaConvenioComponent implements OnInit {
  constructor(
    private spinner: SpinnerService,
    private convenioService: ConvenioService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private router: Router,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    private snackBarAlert: AlertComponent

  ) { }

  // @Output() FadeIn: string;
  isOpen = false;

  toggle: boolean[] = []

  inativos = false;
  DadosTab: any = [];
  bOcultaCarregaMais = false;
  pesquisa = ""
  qtdRegistros = 10;
  idUsuarioDelet: number = 0;
  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔';
  // emailenviado: string = 'Email Enviado. ✔';
  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  legenda: boolean = false;


  ngOnInit() {

    this.CarregaTable();
  }

  CarregaTable() {
    try {
      this.bOcultaCarregaMais = false

      if (this.inativos == false) {
        this.convenioService.getGridConvenio(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {


          this.DadosTab = retorno

          this.DadosTab.forEach((element: any) => {
            if (element.cnpj != null && element.cnpj != '' && element.cnpj != undefined)
              element.cnpj = this.Editado(element.cnpj);
          });
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {

          this.snackBarAlert.falhaSnackbar("Falha ao carregar tabela!")
          this.spinner.hide();
        })
      }
      else {
        this.convenioService.getGridConvenioInativos(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

          this.DadosTab = retorno
          this.DadosTab.forEach((element: any) => {
            if (element.cnpj != null && element.cnpj != '' && element.cnpj != undefined)
              element.cnpj = this.Editado(element.cnpj);
          });
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {

          this.snackBarAlert.falhaSnackbar("Falha ao carregar tabela!")
          this.spinner.hide();
        })
      }
    } catch (error) {

      this.snackBarAlert.falhaSnackbar("Falha na conexão!")
      this.spinner.hide();

    }
  }


  CarregarMais() {
    try {


      if (this.inativos == false) {
        this.convenioService.getGridConvenio(this.DadosTab.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          var dados = retorno;

          for (let index = 0; index < dados.length; index++) {
            this.DadosTab.push(dados[index]);
          }
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Falha ao carregar mais!")
          this.spinner.hide();
        })
      }
      else {
        this.convenioService.getGridConvenioInativos(this.DadosTab.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          var dados = retorno;

          for (let index = 0; index < dados.length; index++) {
            this.DadosTab.push(dados[index]);
          }
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Falha ao carregar mais!")
          this.spinner.hide();
        })
      }
    } catch (error) {

      this.snackBarAlert.falhaSnackbar("Falha na conexão!")
      this.spinner.hide();

    }
  }





  Editado(ao_cnpj: any) {

    ao_cnpj = ao_cnpj.replace(/\D/g, "");
    ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
    ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
    ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");

    return ao_cnpj;
  }


  public editClinica(id: any) {
    if (id != "" && id != 0) {
      this.localStorageService.idConvenio = id;
      this.router.navigate(['/convenio']);
    }
  }

  // ErroCarregar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELAPESQUISACLINICA.ERROAOCARREGAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }



  AtivarUsuario() {
    try {
      this.convenioService.AtivarConvenio(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {

        if (retorno == true) {

          this.tradutor.get("Convênio ativo com sucesso.").subscribe((res: string) => {
            ;
            this.ngxSmartModalService.getModal('ativarItem').close();
            this.snackBarAlert.sucessoSnackbar(res);
            this.CarregaTable();
          });
        }
        else {

          this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
            ;
            ;
            this.snackBarAlert.falhaSnackbar(res);

          });
        }
        this.spinner.hide();
      }, () => {
        this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
          this.snackBarAlert.falhaSnackbar(res);
        });
        this.spinner.hide();
      })

    } catch (error) {

      this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
        ;
        ;
        this.snackBarAlert.falhaSnackbar(res);
      });
    }
  }

  InativarUsuario() {
    try {
      this.convenioService.inativarConvenio(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {

        if (retorno == true) {
          this.ngxSmartModalService.getModal('excluirItem').close();

          this.tradutor.get('TELAPESQUISACLINICA.CLINICACOMSUCESSO').subscribe(() => {
            this.snackBarAlert.sucessoSnackbar("convenio inativado com sucesso.");
            this.CarregaTable();
          });
        }
        else {

          this.tradutor.get('TELAPESQUISACLINICA.ERROAOINATIVAR').subscribe((res: string) => {
            ;
            ;
            this.snackBarAlert.falhaSnackbar(res);
          });
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    } catch (error) {

      this.tradutor.get('TELAPESQUISACLINICA.ERROAOINATIVAR').subscribe((res: string) => {
        ;
        this.snackBarAlert.falhaSnackbar(res);
        this.spinner.hide();
      });
    }

  }
  public ValorUsuarioAtivar(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('ativarItem').open();
  }

  public ValorUsuario(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }


  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }


  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTab[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.DadosTab[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index: any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.DadosTab[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTab[this.indexGlobal]['toggle'] = !this.DadosTab[this.indexGlobal]['toggle'];
  }

}
