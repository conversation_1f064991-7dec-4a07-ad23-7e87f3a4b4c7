.div-login {
  margin-top: 12%;
  // width: 55%;
  /* float: right; */
  position: relative;
  bottom: 0;
  margin-right: 10px;
}
header {
  min-height: 60px;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  border-bottom: 1px solid #ccc;
  background: #ececec;
  z-index: 2;
}
.div-login1{
  margin-top: 8%;
  // width: 55%;
  /* float: right; */
  // margin-left: 22%;
  position: relative;
  bottom: 0;
  // margin-right: 10px;
}

h3 {
  font-family: "Arvo", serif;
  font-weight: bold;
}

.footer-faso {
  position: absolute;
  width: 100%;
  bottom: 0px;
  padding: 10px;
  background: white;
}

.bandeira{
  width: 21px;
  margin-left: 5px;
  margin-right: 5px;
}

.lang-br{
  margin-left: 0px;
  font-weight: bold;
  align-items:center;
  color:#777;
  font-size: 12px;
}

.lang-es{
  margin-right: 15px;
  font-weight: bold;
  align-items:center;
  color:#777;
  font-size: 12px;
}

.lang-eng{
  font-weight: bold;
  align-items:center;
  color:#777;
  font-size: 12px;
}



.title-country{
  margin-bottom: 0.4rem!important;
  color: #666;
    font-size: small;
    font-weight: bold;
}

.cel{
  margin-left: 22px;
}

.email{
  margin-right: 7px;
}

.space-icon {
  margin-right: 4px;
}

.logo-footer {
  width: 10%;
  margin-top: 4px;
}

.input-login {
  width: 100%;
}

.esqueceu-senha {
  margin-top: -30px;
  padding-top: 5px;
}

.logo-controler {
  width: 100%;
}
.a-esquecisenha {
  color: rgb(50, 58, 165);
  text-decoration: none;
}
.padding-10 {
  padding: 10px;
  color:rgba(0,0,0,.6);
}

.padding-5{
  padding: 5px;
}

.danger-baloon {
  height: 55px;
  border-radius: 5px;
  background: #ffbfbf;
  font-weight: 600;
  text-align: center;
  padding-top: 2vh;
  margin-bottom: 10px;
}

.background-login {
  // background-image: url(/assets/build/img/bg-5.jpg);
  background-image: url(/assets/build/img/novo-banner.jpeg);
  background-attachment: fixed;
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100vw;
  position: absolute;
  height: 99vh;
  overflow-y: hidden;
  overflow-x: hidden;
  // margin-top: 65px;
}

.footer-faso-novo {
  position: absolute;
  width: 100%;
  bottom: 0px;
  padding: 10px;
  height: auto;
  display: flex;
  justify-content: space-around;
  background: #ececec;
}

.logoRodapefooter {
  max-height: 40px;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-flex {
  height: 50px;
    margin: 0 auto;
    margin-top: 10px;
    align-items: center;
}
.img-login-logo {
  width: 40%;
  margin-bottom: 25px;
  justify-content: center;
  text-align: center;
  align-items: center;
  margin-top: -10px;
}

.cpf-invalido {
  font-size: 65%;
  color: #f44336;
  font-weight: 500;
  position: absolute;
  left: 29px;
  top: 60px;
}
.prec-preenc {
  font-size: 65%;
  color: #f44336;
  font-weight: 500;
  position: absolute;
  left: 30px;
  top: 60px;
}
.erro-nome {
  margin-left: -7px;
  margin-top: -1px;
  font-weight: 500;
}
.email-inval {
  font-size: 66%;
  color: #f44336;
  font-weight: 500;
  margin-left: 10% !important;
}
.tel-preenc {
  font-size: 71%;
  color: #f44336;
  font-weight: 500;
  margin-left: 8% !important;
  font-family: arial;
}
.div-input-resp {
  padding: unset;
  margin-bottom: 30px;

}
.campo-cpf-login label {
  margin-top: -10px;
}

@media (max-width: 1309px) {
  .tel-preenc {
    font-size: 68%;
  }
  .erro-nome {
    font-size: 90%;
    font-weight: 500;
  }
}

@media (max-width: 1260px) {
  .title-input {
    font-size: 14px;
    align-items: center;
  }
}

@media (max-width: 1255px) {
  .tel-preenc {
    font-size: 68%;
  }
  .tel-preenc {
    font-size: 64%;
  }
}

@media (max-width: 1183px) {
  .tel-preenc {
    font-size: 54%;
  }
  .email-inval {
    font-size: 54%;
  }
}

@media (max-width: 1130px) {
  .title-input {
    font-size: 12px;
    align-items: center;
  }
}

@media (max-width: 1050px) {
  .telaGrande {
    display: none;
  }
}

@media (max-width: 1041px) {
  .email-inval {
    font-size: 51%;
  }
}

@media (max-width: 1030px) {
  .title-input {
    font-size: 11px;
    align-items: center;
  }
}

@media (max-width: 1024px) {
  .div-login {
    margin-top: 8%;
    // width: 60%;
    left: 0%;
  }

  .logo-controler {
    width: 90%;
  }

  .padding-10 {
    padding: 0px;
  }


  .div-resp-login {
    margin-top: 92px;
  }
  .img-login-logo {
    width: 60%;
  }
}

@media (max-width: 1003px) {
  .tel-preenc {
    font-size: 47%;
    font-weight: 600;
  }
}

@media (max-width: 1000px) {
  .div-resp-login {
    display: block;
    margin: 0 auto;
    padding: 15px;
  }
  .title-input {
    font-size: 12px;
    max-width: 100%;
  }
  .div-input-resp {
    max-width: 100%;
    margin-bottom: 10px;
  }
  .email-resp {
    max-width: 100%;
    margin-top: 6px;
  }
  .celular-resp {
    max-width: 100%;
    margin-top: 18px;
  }
  .email-inval {
    margin-left: 4% !important;
  }
  .tel-preenc {
    font-size: 46.8%;
    margin-left: 4% !important;
  }
  .div-nome-covid {
    margin-top: 8px;
    margin-bottom: 10px;
  }
  .sus-input {
    margin-top: 18px;
  }
}

@media (max-width: 820px) {
  .div-resp-login {
    margin-top: 3%;
  }
  .title-input {
    font-size: 14px;
  }
  .celular-resp {
    margin-bottom: 7px;
  }
  .title-input {
    margin-bottom: 7px;
  }
  .email-inval {
    margin-left: 5% !important;
    font-size: 59%;
  }
  .tel-preenc {
    font-size: 55%;
    margin-left: 5% !important;
  }
  .erro-nome {
    font-size: 89%;
  }
}

@media (max-width: 768px) {
  .div-login {
    margin-top: 8%;
    // width: 80%;
    // left: -7%;
     margin-right: auto;
     margin-left: auto;

  }
  .input-login {
    padding: 10px;
  }
  .row-button{
    padding: 10px;;
  }
  .cel{
    margin-left: 0px;
  }

  .mob-b{width: 55px;}

  .font-0{
    color: white;
  }

  .f-12{
    font-size: 12px;
  }

  .logo-footer{
    width: 17%;
    margin-top: 7px;
}

  .footer-faso{
    display:none;
  }
}

@media (max-width: 767px) {
  .div-resp-login {
    max-width: 400px;
    margin-top: 1px;
  }
  .title-input {
    margin-bottom: 11px;
  }
  .celular-resp {
    margin-top: 26px;
  }
  .sus-input {
    margin-top: 22px;
  }
}

@media (max-width: 700px) {
  .div-resp-login {
    margin-top: 7px;
  }
}

@media (max-width: 600px) {
  .div-login {
    margin-top: 25%;
    // width: 90%;
    // left: -2%;
    margin-left: 0px;
    margin-right: 0px;
  }
  .div-login1 {
    margin-top: 10%;
    position: relative;
    bottom: 0;
  }
  .div-resp-login {
    margin-top: -10%;
  }
  .img-login-logo {
    margin-top: -3px;
    }
  .logo-respons {
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 20px;
  }
  .logo-footer{
    width: 20%;
}
}

@media (max-width: 580px) {
  .img-login-logo {
    width: 70%;
  }
}





@media (max-width: 500px) {
  .img-login-logo {
    width: 70%;
  }
  .div-resp-login {
    margin-top: -10%;
  }
}


@media (max-width: 435px) {
  .celular-resp {
    top: 5px;
  }
  .sus-input {
    top: 10px;
  }
}

@media (max-width: 425px) {

  .div-login1 {
    margin-top: 3%;
    position: relative;
    bottom: 0;
  }
  .background-login {
    height: 100vh;
    padding-top: 30px;
    background-image:none;
  }
  .logo-footer {
    width: 33%;
  }

  .padding-10 {
    padding: 10px;
  }

  .footer-faso{
    position: fixed;
    width: 100%;
    bottom: 0px;
    padding: 10px;
    background: white;
    border-top: 1px solid #ddd;
  }

  .a-esquecisenha{
    cursor: pointer;
    font-weight: 500;
    font-size: 12px;
    text-align: center;
    text-align: left;
    padding-right: 25px;
  }

}

@media (max-width: 400px) {
  .img-login-logo {
    width: 75%;
  }
}

@media (max-width: 380px) {

  .div-input-resp {
    margin-top: 9px;
  }
  .card-total {
    box-shadow: 1px 1px 1px 2px #888;
    border-radius: 10px;
    padding: 10px;
  }

}

@media (max-width: 375px) {
  .div-login {
    margin-top: 20%;
    // width: 94%;
    left: 0%;
    margin-left: 0px;
    margin-right: 0px;
  }
  .example-form {
    padding: 10px;
  }
  .div-login1 {
    margin-top: 6%;
    position: relative;
    bottom: 0;
  }


}

@media (max-width: 335px) {
  .tel-preenc {
    margin-left: 6% !important;
  }
  .email-inval {
    margin-left: 6% !important;
  }
}

@media (max-width: 320px) {
  .padding-10 {
    padding: 15px;
  }

  .div-login {
    margin-top: 35%;
    // width: 95%;
    left: 0%;
    margin-left: 0px;
    margin-right: 0px;
  }

  .div-resp-login {
    /* margin-top: -0vh; */
    /* overscroll-behavior-x: auto; */
  }

  .div-input-resp {
    margin-top: 3px;
}
}



.Title-config {
  color: #666;
  font-size: 15px;
  text-transform: capitalize;
}

.clinic {
  font-size: 12px!important;
}

.menu-icon {
  height: 25px;
  font-size: 25px;
  margin-left: 22px;
}

.menu-icon-drop {
  height: 25px;
  font-size: 25px;
  margin-left: 10px;
}

.body-config {
  white-space: pre-wrap;
  padding: 5px;
  line-height: 20px;
}

.dropdown-config {
  min-width: 250px;
  left: -90px !important;
  padding-top: 10px !important;
  padding-left: 15px !important;
  padding-right: 15px !important;
  padding-bottom: 10px !important;
  border: none;
}

.painel-config {
  border-top: 1px dashed #1265b9;
  padding: 10px;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.19);
}

.lateral-menu {
  padding-top: 10px;
}

.menu-expanded {
  display: flex;
  padding: 15px;
  transition: all 2s;
  cursor: pointer;
}

.menu-expanded:hover {
  background: #0b2638a3;
}

i.fa {
  font-size: 25px;
  padding: 9px;
  width: 30px;
  text-align: center;
  text-shadow: 1px 1px 1px #000;
}

.leftMenu {
  height: 100%;
  margin-top: 60px;
  background-color: #1265b9;
  position: fixed;
  left: 0;
  top: 0;
  width: 50px;
  transition: all ease 1s;
  overflow: hidden;
  transition: 0.5s cubic-bezier(0.8, 0.5, 0.2, 1.4);
  // box-shadow: 1px 4px 8px 4px rgba(0, 0, 0, 0.3);
}


