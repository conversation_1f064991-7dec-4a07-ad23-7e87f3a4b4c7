<div class="nsm-centered medium-modal modal form-modal-Cliente" style="padding: 0 !important;"
    *ngIf="!FlgExibeModalUsuarioExistente">
    <div class="col-md-12" style="width: 100% !important;">


        <div class="modal-info" md-dialog-content>
            <!-- Paciente:  -->

            <div class="div-title">
                <b class="title-novopac"> Novo Paciente</b><br>
            </div>

            <hr class="sep-1" />


            <div class="col-md-12 col-sm-12 row mt-3" style=" margin-left: 0; margin-right: 0;">
                <!-- <mat-form-field class="col-md-6 col-sm-12 input-spacing campo-NovoCliente" appearance="legacy">
                    <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" name="CPF"
                        (change)="ValidarCpf($any($event.target).value)" mask="000.000.000-00" maxlength="14" required
                        [(ngModel)]="dadosNovoPaciente.cpf">

                        <span class="ml-1 text-center" *ngIf="campoCPFInvalido == true" style="font-size: 65%;color: #f44336;font-weight: 600;position: absolute;
                        left: 1px;top: 37px;">CPF inválido</span>
                        <span class="ml-1 text-center" *ngIf="campoCPFVazil == true && campoCPFInvalido != true"
                            style="font-size: 65%;color: #f44336;font-weight: 600;position: absolute;
                        left: 1px;top: 37px;">Esse campo precisa ser preenchido</span>
                        </mat-form-field> -->


                <div class="col-md-6 col-sm-12" style="padding: unset">
                    <mat-form-field class="col-md-12 col-sm-12 input-spacing" style="height: 31px;">
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.CPF' | translate }}" name="CPF"
                            (blur)='validarCpf($any($event.target).value)'
                            mask="000.000.000-00" maxlength="14" required [(ngModel)]="dadosNovoPaciente.cpf">
                    </mat-form-field>


                    <span class="ml-1 text-center" *ngIf="campoCPFInvalido == true" style="
                        font-size: 65%;
                        color: #f44336;
                        font-weight: 500;
                        position: absolute;
                        left: 13px;
                        top: 32px;">CPF inválido</span>
                    <span class="ml-1 text-center" *ngIf="campoCPFVazil == true && campoCPFInvalido != true" style="
                        font-size: 65%;
                        color: #f44336;
                        font-weight: 500;
                        position: absolute;
                        left: 13px;
                        top: 32px;">Esse campo precisa ser preenchido</span>
                </div>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing campo-NovoCliente Nome-Pac">
                    <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" name="Nome" required
                       
                        [(ngModel)]="dadosNovoPaciente.nome" >
                </mat-form-field>

                <div class="col-md-6 col-sm-12 campo-NovoCliente " style="padding: unset" required>
                    <mat-form-field class="col-12  input-spacing" style="height: 31px;">
                        <input matInput placeholder="{{ 'TELAAGENDA.DATADENASCIMENTO' | translate }}"
                            name="Data de Nascimento" id="DtaNasc" [(ngModel)]="dadosNovoPaciente.dtaNascimento"
                            maxlength="10"
                            >
                    </mat-form-field>
                </div>

                <!-- <mat-form-field appearance="legacy" class="col-md-6 col-sm-12 input-spacing campo-NovoCliente">
                    <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email" name="Email" required
                        [(ngModel)]="dadosNovoPaciente.email" [formControl]='email'>
                    <mat-error *ngIf="email.invalid">{{getErrorMessageEmail() | translate }}</mat-error>
                    </mat-form-field> -->
                <div class="col-md-6 col-sm-12" style="padding: unset; height: 50px;">
                    <mat-form-field class="col-12  input-spacing campo-NovoCliente Email-Pac"
                        style="height: 31px;">
                        <input matInput placeholder="{{ 'TELACADASTROCLINICA.EMAIL' | translate }}" type="email"
                            name="Email" required
                            [(ngModel)]="dadosNovoPaciente.email">
                    </mat-form-field>
                </div>


                <mat-form-field class="col-md-6 col-sm-12 input-spacing campo-NovoCliente Cel-Pac">
                    <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" name="Telefone Movel"
                         required
                        maxlength="15" [(ngModel)]="dadosNovoPaciente.telefoneMovel">
                </mat-form-field>


                <mat-form-field class="col-md-6 col-sm-12 col-xs-12 input-spacing campo-NovoCliente">
                    <input matInput placeholder="{{ 'TELAAGENDA.PROCEDENCIA' | translate }}" name="Procedência"
                        [(ngModel)]="dadosNovoPaciente.procedencia">
                </mat-form-field>

                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: unset; height: 50px;">
                    <mat-form-field class="col-12 input-spacing" style="height: 31px;">
                        <mat-select placeholder="{{ 'TELAAGENDA.CLINICASCORRESPONDENTE' | translate }}" multiple
                            name="clinicas" [formControl]="clinicas">
                            <mat-option *ngFor="let item of DadosClinicas;let i = index" [value]="item">
                                {{item.desClinica | truncate : 40 : "…"}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <div class=" col-md-6 col-sm-12 col-xs-12 col-lg-6 button_custom mt-4" align="left">

                    <mat-checkbox style="margin-right: 10%;" [(ngModel)]="dadosNovoPaciente.flgBoasVindas">
                        {{ 'TELAAGENDA.ENVIARBOASVINDAS' | translate }}
                    </mat-checkbox>

                </div>
                <div class=" col-md-12 button_custom mt-4 botao"
                    style="margin: 0 auto; justify-content: center; text-align: center;">
                    <button class="btn-primary " mat-raised-button style="color:white;"
                        style="margin-right: 10px;">
                        <mat-icon>clear</mat-icon><span class="custom-span title-btns">{{ 'TELAAGENDA.LIMPAR' |
                            translate }}</span>
                    </button>

                    <button class="btn-primary " mat-raised-button style="color:white; margin-right: 10px"
                        >
                        <mat-icon>save</mat-icon> <span class="custom-span title-btns">{{ 'TELAAGENDA.SALVAR' |
                            translate }}</span>
                    </button>
                </div>

            </div>
        </div>

        <div class="logo-medicina-modal">
         </div>

    </div>
</div>





<div class="nsm-centered medium-modal" *ngIf="FlgExibeModalUsuarioExistente">
    <div class="modal-header p-t-20 p-b-20">
        <div class="row">
            <div class=" col-12">
                <h1 class="little-title fw-700" style=" padding-left: 3vh; text-align: center">{{mensagemPaciente}}</h1>
            </div>


        </div>
    </div>



    <mat-divider></mat-divider>
    <div class="row-button text-center p-t-20">
        <button mat-flat-button (click)="AceitarUsuarioExistente()" class="input-align btn btn-success">Aceitar</button>
        <button mat-flat-button
            class="input-align btn btn-danger">Cancelar</button>

    </div>
</div>