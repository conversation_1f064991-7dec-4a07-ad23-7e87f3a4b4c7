// Variáveis de estilo
$primary-color: #2E8B57; // Verde
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

// Loader animado
.loader {
  margin: 2em auto;
  width: 2.5em;
  height: 2.5em;
  border-radius: 50%;
  position: relative;
  animation: rotate 1s linear infinite;
  
  &::before,
  &::after {
    content: "";
    box-sizing: border-box;
    position: absolute;
    inset: 0px;
    border-radius: 50%;
    border: 3px solid $primary-light;
    animation: prixClipFix 2s linear infinite;
  }
  
  &::after {
    transform: rotate3d(90, 90, 0, 180deg);
    border-color: $primary-color;
  }
}

@keyframes rotate {
  0% { transform: rotate(0deg) }
  100% { transform: rotate(360deg) }
}

@keyframes prixClipFix {
  0% { clip-path: polygon(50% 50%, 0 0, 0 0, 0 0, 0 0, 0 0) }
  50% { clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 0, 100% 0, 100% 0) }
  75%, 100% { clip-path: polygon(50% 50%, 0 0, 100% 0, 100% 100%, 100% 100%, 100% 100%) }
}

// Estilos globais para o componente de diálogo
::ng-deep {
  .mat-mdc-dialog-container {
    padding: 0;
    margin: 0;
    border-radius: $border-radius;
    overflow: hidden;
  }
  
  .mat-menu-panel {
    border-radius: $border-radius;
    
    .mat-menu-content {
      padding: 0;
    }
  }
  
  .mat-mdc-menu-item {
    transition: background-color $transition;
    
    &:hover {
      background-color: $primary-light;
    }
  }
}

// Container principal
#modal {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  flex: 0;
  background-color: $bg-color;
  display: flex;
  flex-direction: column;
  font-family: 'Roboto', sans-serif;
  color: $text-primary;
}

// Cabeçalho
header {
  padding: 0 1.5rem;
  width: 100%;
  height: 70px;
  background-color: $card-bg;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid $border-color;
  
  #hdChatText {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.2rem;
    font-weight: 500;
    color: $primary-color;
    
    img {
      width: 1.75rem;
    }
  }
  
  #hdTextoCentro {
    display: flex;
    flex-direction: column;
    text-align: center;
    
    #nomeUser {
      font-size: 1rem;
      font-weight: 600;
      padding: 0;
      margin: 0;
      color: $text-primary;
    }
    
    #descUser {
      font-size: 0.8rem;
      padding: 0;
      margin: 0;
      color: $text-secondary;
    }
  }
  
  #hdBotoes {
    display: flex;
    gap: 0.5rem;
    
    button {
      background-color: transparent;
      padding: 0;
      min-width: 0;
      width: 40px;
      height: 40px;
      flex-shrink: 0;
      line-height: 40px;
      border-radius: 50%;
      font-size: 14px;
      text-align: center;
      color: $text-secondary;
      transition: all $transition;
      
      &:hover {
        background-color: $primary-light;
        color: $primary-color;
      }
      
      &:active {
        background-color: rgba($primary-color, 0.2);
      }
    }
  }
}

// Itens do menu
.spanMenu {
  color: $text-secondary !important;
  font-family: 'Roboto', sans-serif !important;
  font-size: 0.875rem;
  font-weight: 400;
}

.config {
  mat-icon {
    color: $text-secondary !important;
    transition: color $transition;
  }
  
  &:hover mat-icon {
    color: $primary-color !important;
  }
}

// Conteúdo principal
content {
  width: 100%;
  height: calc(100% - 70px);
  padding: 0;
  margin: 0;
  display: flex;
  flex: 1;
  
  // Barra lateral de contatos
  nav {
    overflow-y: auto;
    width: 35%;
    background-color: $card-bg;
    border-right: 1px solid $border-color;
    
    &::-webkit-scrollbar {
      width: 6px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: rgba($text-secondary, 0.3);
      border-radius: 3px;
    }
    
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
    
    .input-group {
      padding: 1rem;
      
      .input-group-prepend {
        .input-group-text {
          background-color: #ffff;
          border: none;
          border-radius: $border-radius 0 0 $border-radius;
          color: $primary-color;
          padding: 0.5rem;
        }
      }
      
      input {
        border: none;
        border-radius: 0 $border-radius $border-radius 0;
        background-color: #f7f7f7;
        color: $text-primary;
        padding: 0.75rem 1rem;
        transition: all $transition;
        
        &::placeholder {
          color: rgba($text-secondary, 0.7);
        }
        
        &:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.3);
        }
      }
    }
    
    #cardContato {
      cursor: pointer;
      width: 100%;
      padding: 0.75rem 1rem;
      position: relative;
      display: flex;
      align-items: center;
      border-bottom: 1px solid $border-color;
      transition: background-color $transition;
      
      &:hover {
        background-color: rgba($primary-light, 0.5);
      }
      
      &.usuarioSelecionado {
        background-color: $primary-light;
        border-left: 3px solid $primary-color;
      }
      
      img {
        height: 40px;
        width: 40px;
        border-radius: 50%;
        margin-right: 1rem;
        object-fit: cover;
      }
      
      #txtContatoNome {
        font-size: 0.85rem;
        display: flex;
        flex-direction: column;
        color: $text-secondary;
        
        b {
          color: $text-primary;
          font-size: 0.95rem;
          margin-bottom: 0.2rem;
        }
      }
      
      #qtdMsgContato {
        position: absolute;
        right: 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: $card-bg;
        background-color: $primary-color;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        width: 1.5rem;
        height: 1.5rem;
        box-shadow: $box-shadow;
      }
    }
  }
  
  // Placeholder quando nenhum chat está selecionado
  #OcultarChat {
    height: 100%;
    width: 65%;
    background-color: $bg-color;
    display: flex;
    flex-direction: column;
    color: $text-secondary;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    mat-icon {
      font-size: 4rem;
      height: 4rem;
      width: 4rem;
      margin-bottom: 1rem;
      color: rgba($primary-color, 0.3);
    }
  }
  
  // Área principal de chat
  main {
    position: relative;
    height: 100%;
    width: 65%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: $bg-color;
    
    .Mensagens {
      height: calc(100% - 70px);
      width: 100%;
      overflow: auto;
      display: flex;
      flex-direction: column-reverse;
      padding: 1rem;
      
      &::-webkit-scrollbar {
        width: 6px;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba($text-secondary, 0.3);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
      
      #LinhaMsg {
        width: 100%;
        display: flex;
        margin-bottom: 0.8rem;
        
        &.remetente {
          justify-content: flex-end;
          
          .msgDiv {
            .contentMsg {
              border-bottom-right-radius: 0;
              background-color: $primary-light;
              color: $text-primary;
              
              .infoMensagem {
                justify-content: flex-end;
                text-align: right;
                color: rgba($text-secondary, 0.8);
                
                #MsgLidaVerificado {
                  color: $primary-color;
                  font-size: 0.9rem;
                  margin-left: 0.25rem;
                }
              }
            }
          }
        }
        
        &.destinatario {
          justify-content: flex-start;
          
          .msgDiv {
            .contentMsg {
              background-color: $card-bg;
              border-bottom-left-radius: 0;
              
              .infoMensagem {
                text-align: left;
                color: rgba($text-secondary, 0.8);
              }
            }
          }
        }
        
        .msgDiv {
          max-width: 65%;
          display: flex;
          justify-content: flex-start;
          
          .contentMsg {
            border-radius: $border-radius;
            display: flex;
            flex-direction: column;
            gap: 5px;
            padding: 0.75rem 1rem 0.5rem 1rem;
            box-shadow: $box-shadow;
            
            .infoMensagem {
              font-size: 0.7rem;
              display: flex;
              align-items: center;
            }
            
            .txtMensagem {
              font-size: 0.95rem;
              text-align: left;
              line-height: 1.4;
              word-break: break-word;
            }
          }
          
          #iconeUsario {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: flex-end;
            
            img {
              height: 30px;
              width: 30px;
              border-radius: 50%;
              margin: 0 0.5rem;
              margin-bottom: 0;
              object-fit: cover;
            }
          }
        }
      }
    }
    
    // Área de digitação da mensagem
    footer {
      height: 70px;
      width: 100%;
      background-color: #f7f7f7;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1rem;
      border-top: 1px solid $border-color;
      
      .input-group {
        width: 100%;
        display: flex;
        align-items: center;
        
        textarea {
          border: none;
          border-radius: $border-radius;
          background-color: #ffff;
          color: $text-primary;
          padding: 0.75rem 1rem;
          resize: none;
          height: 45px;
          max-height: 100px;
          transition: all $transition;
          font-family: 'Roboto', sans-serif;
          font-size: 0.95rem;
          
          &:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba($primary-color, 0.3);
          }
          
          &::placeholder {
            color: rgba($text-secondary, 0.7);
          }
        }
        
        .input-group-prepend {
          margin-left: 0.5rem;
          background-color: $primary-color;
          border-radius: 50%;
          width: 45px;
          height: 45px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all $transition;
          box-shadow: $box-shadow;
          
          &:hover {
            background-color: $primary-dark;
          }
          
          .input-group-text {
            background-color: transparent;
            border: none;
            color: white;
            padding: 0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            
            mat-icon {
              font-size: 1.25rem;
            }
          }
        }
      }
    }
  }
}

#btFechar {
  color: $text-secondary;
  
  &:hover {
    color: $error-color;
  }
}

::ng-deep{
  .mat-mdc-dialog-surface.mdc-dialog__surface{
    max-height: 790px;
  }
}
