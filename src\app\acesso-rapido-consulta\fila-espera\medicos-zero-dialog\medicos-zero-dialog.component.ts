// medicos-zero-dialog.component.ts
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-medicos-zero-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatDividerModule
  ],
  template: `
    <div class="modal-header p-t-20 p-b-20">
      <div class="row">
        <div class="col-12">
          <h1 class="little-title fw-700" style="padding-left: 3vh; text-align: center">
            Não há médicos on-line no momento
            <br>tente novamente mais tarde.
          </h1>
        </div>
      </div>
    </div>

    <mat-divider></mat-divider>
    
    <div class="row-button text-center p-t-20" mat-dialog-actions>
      <button mat-flat-button (click)="sair()" class="btn-primary">
        Sair
      </button>
    </div>
  `,
  styleUrls: ['./medicos-zero-dialog.component.scss']
})
export class MedicosZeroDialogComponent {

  constructor(
    private dialogRef: MatDialogRef<MedicosZeroDialogComponent>
  ) { }

  sair(): void {
    this.dialogRef.close('sair');
  }
}