import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatTabsModule } from '@angular/material/tabs';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ConsultaService } from '../service/consulta.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CriptografarUtil } from '../Util/Criptografar.util';
import { FilaEsperaMedicoService } from '../service/fila-espera-medico.service';
import { SignalHubService } from '../service/signalHub.service';

@Component({
  selector: 'app-streaming-guest',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTooltipModule,
    MatTabsModule
  ],
  templateUrl: './streaming-guest.component.html',
  styleUrls: ['./streaming-guest.component.scss']
})
export class StreamingGuestComponent implements OnInit, OnDestroy {

  // Variáveis principais
  FlgVideo: boolean = false;
  LinkVideo?: string;
  urlSafe?: SafeResourceUrl;
  sala?: string;
  
  // Dados do médico
  nomeMedico: string = '';
  idMedico: number = 0;
  
  // Dados do paciente (carregados via token)
  tokenPaciente: string = '';
  dadosQuestionario: any = null;
  dadosColeta: any = null;
  nomePaciente: string = '';
  
  // Estados da interface
  videoConnected: boolean = false;
  micMuted: boolean = true;
  cameraOff: boolean = false;
  consultaFinalizada: boolean = false;
  
  // Timer da consulta
  tempoConsulta: string = '00:00';
  private timerInterval?: any;
  private segundosTotal: number = 0;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private usuarioLogadoService: UsuarioLogadoService,
    private consultaService: ConsultaService,
    private spinner: SpinnerService,
    private alertComponent: AlertComponent,
    private filaEsperaMedicoService: FilaEsperaMedicoService,
    private signalHubService: SignalHubService
  ) {}

  ngOnInit() {
    this.consultaService;
    this.spinner;
    this.filaEsperaMedicoService;

    this.carregarDadosMedico();
    this.carregarTokenPaciente();
    this.carregarDadosPaciente();
    this.configurarSignalR();
    this.iniciarTimer();
  }

  ngOnDestroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }

  private configurarSignalR() {
    // Configurar listener para receber dados do paciente
    this.signalHubService.OnDadosPacienteRecebidos.subscribe((dadosPaciente: any) => {
      console.log('📋 Dados do paciente recebidos pelo médico:', dadosPaciente);

      // Atualizar dados locais com os dados recebidos do paciente
      if (dadosPaciente.questionario) {
        this.dadosQuestionario = dadosPaciente.questionario;
        this.nomePaciente = dadosPaciente.questionario.nome || this.nomePaciente;
      }

      if (dadosPaciente.coleta) {
        this.dadosColeta = dadosPaciente.coleta;
      }

      console.log('✅ Dados do paciente atualizados no médico:', {
        nome: this.nomePaciente,
        temQuestionario: !!this.dadosQuestionario,
        temColeta: !!this.dadosColeta
      });
    });
  }

  private carregarDadosMedico() {
    // Carregar dados do médico logado
    if (this.usuarioLogadoService.isLogged()) {
      this.nomeMedico = this.usuarioLogadoService.getNomeUsuario() || 'Médico';
      this.idMedico = this.usuarioLogadoService.getIdUsuarioAcesso() || 0;
    } else {
      this.alertComponent.falhaSnackbar('Erro: Médico não está logado');
      this.router.navigate(['/login']);
      return;
    }
  }

  private carregarTokenPaciente() {
    // Carregar token do paciente da URL ou localStorage
    this.route.queryParams.subscribe(params => {
      this.tokenPaciente = params['token'];
      
      if (!this.tokenPaciente) {
        this.router.navigate(['/fila-espera-medico']);
        return;
      }
      
      console.log('Token do paciente carregado:', this.tokenPaciente);
      this.iniciarVideoconferencia();
      this.solicitarDadosPaciente();
    });
  }

  private async solicitarDadosPaciente() {
    if (!this.tokenPaciente) {
      console.warn('⚠️ Token do paciente não disponível para solicitar dados');
      return;
    }

    try {
      console.log('📋 Solicitando dados do paciente automaticamente...');
      await this.signalHubService.solicitarDadosPaciente(this.tokenPaciente);
      console.log('✅ Solicitação de dados enviada com sucesso');
    } catch (error) {
      console.error('❌ Erro ao solicitar dados do paciente:', error);
      // Não mostrar erro para o usuário, pois é uma funcionalidade automática
    }
  }

  private carregarDadosPaciente() {
    try {
      // Tentar carregar dados do questionário (pode não estar disponível no médico)
      const questionarioData = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');
      if (questionarioData) {
        try {
          // Se já é um objeto, usar diretamente; se é string, fazer parse
          this.dadosQuestionario = typeof questionarioData === 'string' ? JSON.parse(questionarioData) : questionarioData;
          this.nomePaciente = this.dadosQuestionario.nome || 'Paciente';
          console.log('✅ Dados do questionário carregados no médico:', this.dadosQuestionario);
        } catch (parseError) {
          console.warn('⚠️ Erro ao fazer parse do questionário no médico:', parseError);
          this.dadosQuestionario = questionarioData;
          this.nomePaciente = (questionarioData as any)?.nome || 'Paciente';
        }
      }

      // Tentar carregar dados da coleta
      const coletaData = CriptografarUtil.obterLocalStorageCriptografado('VittalTecDados');
      if (coletaData) {
        try {
          // Se já é um objeto, usar diretamente; se é string, fazer parse
          this.dadosColeta = typeof coletaData === 'string' ? JSON.parse(coletaData) : coletaData;
          console.log('✅ Dados da coleta carregados no médico:', this.dadosColeta);
        } catch (parseError) {
          console.warn('⚠️ Erro ao fazer parse da coleta no médico:', parseError);
          this.dadosColeta = coletaData;
        }
      }

      // Se não conseguiu carregar o nome do paciente, usar padrão
      if (!this.nomePaciente) {
        this.nomePaciente = `Paciente (${this.tokenPaciente.substring(0, 8)}...)`;
      }

      console.log('📊 Dados do paciente carregados no médico:', {
        nome: this.nomePaciente,
        temQuestionario: !!this.dadosQuestionario,
        temColeta: !!this.dadosColeta
      });

    } catch (error) {
      console.error('❌ Erro ao carregar dados do paciente no médico:', error);
      this.nomePaciente = `Paciente (${this.tokenPaciente.substring(0, 8)}...)`;
    }
  }

  private iniciarVideoconferencia() {
    if (!this.tokenPaciente) {
      this.alertComponent.falhaSnackbar('Token do paciente é obrigatório');
      return;
    }

    try {
      // Usar o token do paciente para gerar a sala (mesma lógica do paciente)
      this.sala = "medicinaparavoce" + this.tokenPaciente;
      this.FlgVideo = true;

      console.log('Médico iniciando videoconferência para sala:', this.sala);

      // Determinar endpoint baseado no hostname (mesma lógica do paciente)
      if (window.location.hostname === "camposverdes.medicinaparavoce.com.br") {
        this.LinkVideo = `${environment.endPointTokboxCamposVerdes}&room=${this.sala}&iframe=true&userType=doctor`;
      } else if (window.location.hostname === "atendimento.medicinaparavoce.com.br") {
        this.LinkVideo = `${environment.endPointTokboxCovid}&room=${this.sala}&iframe=true&userType=doctor`;
      } else {
        this.LinkVideo = `${environment.endPointTokbox}&room=${this.sala}&iframe=true&userType=doctor`;
      }

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.LinkVideo);
      this.videoConnected = true;
      
      console.log('Videoconferência do médico iniciada com sucesso. URL:', this.LinkVideo);
      
    } catch (error) {
      console.error('Erro ao iniciar videoconferência do médico:', error);
      this.alertComponent.falhaSnackbar('Erro ao conectar com o paciente');
    }
  }

  private iniciarTimer() {
    this.timerInterval = setInterval(() => {
      this.segundosTotal++;
      const minutos = Math.floor(this.segundosTotal / 60);
      const segundos = this.segundosTotal % 60;
      this.tempoConsulta = `${minutos.toString().padStart(2, '0')}:${segundos.toString().padStart(2, '0')}`;
    }, 1000);
  }

  // Ações do médico
  toggleMicrophone() {
    this.micMuted = !this.micMuted;
    this.enviarComandoParaVideo('toggleMic');
  }

  toggleCamera() {
    this.cameraOff = !this.cameraOff;
    this.enviarComandoParaVideo('toggleCamera');
  }

  private enviarComandoParaVideo(comando: string) {
    console.log(`Comando enviado para vídeo: ${comando}`);
  }

  finalizarConsulta() {
    if (confirm('Tem certeza que deseja finalizar a consulta?')) {
      this.consultaFinalizada = true;
      this.FlgVideo = false;
      
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }

      // Redirecionar para fila de espera do médico
      this.router.navigate(['/fila-espera-medico']);
    }
  }

  voltarParaFilaEspera() {
    this.router.navigate(['/fila-espera-medico']);
  }

  // Getters para status
  get statusConexao(): string {
    if (this.consultaFinalizada) return 'Consulta Finalizada';
    if (this.videoConnected) return 'Conectado';
    return 'Conectando...';
  }

  get corStatus(): string {
    if (this.consultaFinalizada) return '#6c757d';
    if (this.videoConnected) return '#28a745';
    return '#ffc107';
  }

  // Métodos para acessar dados do paciente
  getDadosQuestionario() {
    return this.dadosQuestionario;
  }

  getDadosColeta() {
    return this.dadosColeta;
  }

  getTokenPaciente() {
    return this.tokenPaciente;
  }

  // Método para obter dados formatados do paciente
  getDadosFormatadosPaciente() {
    const dados: any = {};

    if (this.dadosQuestionario) {
      dados.questionario = {
        nome: this.dadosQuestionario.nome || 'Não informado',
        cpf: this.dadosQuestionario.cpf || 'Não informado',
        sintomas: this.dadosQuestionario.sintomas || 'Não informado',
        intensidadeDor: this.dadosQuestionario.intensidadeDor || 'Não informado',
        alergias: this.dadosQuestionario.alergias || 'Nenhuma'
      };
    }

    if (this.dadosColeta) {
      dados.coleta = {
        pressaoArterial: this.dadosColeta.pressaoArterial || 'Não coletado',
        frequenciaCardiaca: this.dadosColeta.frequenciaCardiaca || 'Não coletado',
        temperatura: this.dadosColeta.temperatura || 'Não coletado'
      };
    }

    return dados;
  }
}
