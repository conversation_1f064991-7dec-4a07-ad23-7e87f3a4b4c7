<div class="container mother-div">
    <mat-card class="card-principal">
      <!-- CABEÇALHO -->
      <div class="header">
        <div class="header-main">
          <div class="header-icon">
            <span class="material-icons">domain</span>
          </div>
          <h2 class="header-title">Lista de Faturas</h2>
        </div>
      </div>
  
      <!-- AÇÕES -->
      <div class="actions-container">
        <div class="spacer"></div>
        <div class="actions-buttons">
          <button class="btn-acao" (click)="AdicionarNovaFatura()">
            <mat-icon>add</mat-icon>
            <span>Adicionar</span>
          </button>
          <button class="btn-acao" (click)="GerarRelatorio()">
            <mat-icon>picture_as_pdf</mat-icon>
            <span>Gerar PDF</span>
          </button>
        </div>
      </div>
  
      <!-- TABELA DE FATURAS -->
      <div class="tabela-container">
        <table class="tabela-faturas">
          <thead>
            <tr>
              <th class="coluna-check">
                <mat-checkbox [(ngModel)]="flgSelecionaTodos" (change)="AdicionarTodos()"></mat-checkbox>
              </th>
              <th class="coluna-descricao">
                <span>Descrição</span>
              </th>
              <th class="coluna-convenio">
                <span>Convênio</span>
              </th>
              <th class="coluna-total">
                <span>Total</span>
              </th>
              <th class="coluna-acoes">
                <span>Ações</span>
              </th>
            </tr>
          </thead>
          <tbody *ngIf="listaFatura && listaFatura.length > 0">
            <tr *ngFor="let item of listaFatura">
              <td class="coluna-check">
                <mat-checkbox [checked]="ValidaFoiSelecionado(item.idFatura!)" (change)="AdicionarFatura($event, item.idFatura!)"></mat-checkbox>
              </td>
              <td class="coluna-descricao">
                <span class="cell-content">{{item.descricao}}</span>
              </td>
              <td class="coluna-convenio">
                <span class="cell-content">{{item.desConvenio}}</span>
              </td>
              <td class="coluna-total">
                <span class="cell-content">{{ item.valor ? ("R$ " + item.valor.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })) : 'Não definido' }}</span>
              </td>
              <td class="coluna-acoes">
                <button class="btn-detalhar" mat-icon-button (click)="AbrirModalDetalhesFatura(item)" matTooltip="Detalhar">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
          <tbody *ngIf="!listaFatura || listaFatura.length === 0">
            <tr class="linha-vazia">
              <td colspan="5">
                <div class="mensagem-vazia">
                  <mat-icon>sentiment_very_dissatisfied</mat-icon>
                  <p>Nenhuma fatura encontrada</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </mat-card>
  </div>