export class GuiaExames {
    IdGuiaServico?: string;
    RegistroANS1?: string;
    NGuia3?: string;
    DataAutorizacao4?: string;
    Senha5?: string;
    DataValidadeSenha6?: string;
    DataEmissaoGuia7?: string;
    NumeroCarteira8?: string;
    Plano9?: string;
    Validade10?: string;
    Nome11?: string;
    NuCartaoNacionalSaude12?: string;
    CodigoOperadora13?: string;
    NomeContratado14?: string;
    CodigoCnes15?: string;
    // Obrigatório
    NomeProfissionalSolicitante16?: string;
    // Obrigatório
    ConselhoProfissional17?: string;
    NumeroConselho18?: string;
    UF19?: string;
    CodigoCboS20?: string;
    DataSolicitacao21?: string;
    HoraSolicitacao21?: string;
    CaraterSolicitacao22?: string;
    Cid10_23?: string;
    // Obrigatório
    IndicacaoClinica24?: string;
    Tabela25_1?: string;
    Tabela25_2?: string;
    Tabela25_3?: string;
    Tabela25_4?: string;
    Tabela25_5?: string;
    // Obrigatório
    CodigoProcedimento26_1?: string;
    CodigoProcedimento26_2?: string;
    CodigoProcedimento26_3?: string;
    CodigoProcedimento26_4?: string;
    CodigoProcedimento26_5?: string;
    Descricao27_1?: string;
    Descricao27_2?: string;
    Descricao27_3?: string;
    Descricao27_4?: string;
    Descricao27_5?: string;
    QtSolic28_1?: string;
    QtSolic28_2?: string;
    QtSolic28_3?: string;
    QtSolic28_4?: string;
    QtSolic28_5?: string;
    GtAutoriz29_1?: string;
    GtAutoriz29_2?: string;
    GtAutoriz29_3?: string;
    GtAutoriz29_4?: string;
    GtAutoriz29_5?: string;
    CodigoOperadora30?: string;
    NomeContrato31?: string;
    TL32?: string;
    LogradNumComplem33?: string;
    Municipio36?: string;
    UF37?: string;
    CodIBGE38?: string;
    CEP39?: string;
    CodigoCNES40?: string;
    CodigoOperadora40a?: string;
    NomeContrato41?: string;
    ConselhoProfissional42?: string;
    NumeroConselho43?: string;
    UF44?: string;
    CodigoCBOs45?: string;
    AGrauParticipacao45?: string;
    TipoAtendimento46?: string;
    IndicacaoAcidente47?: string;
    TipoSaida48?: string;
    TipoDoenca49?: string;
    TempoDoenca50?: string;
    Data51_1?: string;
    Data51_2?: string;
    Data51_3?: string;
    Data51_4?: string;
    Data51_5?: string;
    HoraInicial52_1?: string;
    HoraInicial52_2?: string;
    HoraInicial52_3?: string;
    HoraInicial52_4?: string;
    HoraInicial52_5?: string;
    HoraFinal53_1?: string;
    HoraFinal53_2?: string;
    HoraFinal53_3?: string;
    HoraFinal53_4?: string;
    HoraFinal53_5?: string;
    Tabela54_1?: string;
    Tabela54_2?: string;
    Tabela54_3?: string;
    Tabela54_4?: string;
    Tabela54_5?: string;
    CodigoProcedimento55_1?: string;
    CodigoProcedimento55_2?: string;
    CodigoProcedimento55_3?: string;
    CodigoProcedimento55_4?: string;
    CodigoProcedimento55_5?: string;
    Descricao56_1?: string;
    Descricao56_2?: string;
    Descricao56_3?: string;
    Descricao56_4?: string;
    Descricao56_5?: string;
    Qtde57_1?: string;
    Qtde57_2?: string;
    Qtde57_3?: string;
    Qtde57_4?: string;
    Qtde57_5?: string;
    Via58_1?: string;
    Via58_2?: string;
    Via58_3?: string;
    Via58_4?: string;
    Via58_5?: string;
    Tec59_1?: string;
    Tec59_2?: string;
    Tec59_3?: string;
    Tec59_4?: string;
    Tec59_5?: string;
    RedAcresc60_1?: string;
    RedAcresc60_2?: string;
    RedAcresc60_3?: string;
    RedAcresc60_4?: string;
    RedAcresc60_5?: string;
    ValorUnitario61_1?: string;
    ValorUnitario61_2?: string;
    ValorUnitario61_3?: string;
    ValorUnitario61_4?: string;
    ValorUnitario61_5?: string;
    ValorTotal62_1?: string;
    ValorTotal62_2?: string;
    ValorTotal62_3?: string;
    ValorTotal62_4?: string;
    ValorTotal62_5?: string;
    DataAssinProcSerie63_1?: string;
    Observacao64?: string;
    TotalProcedimentosR65?: string;
    TotalTaxasAlugueisR66?: string;
    TotalMateriaisR67?: string;
    TotalProcedimentosR68?: string;
    TotalDiariasR69?: string;
    TotalGasesMedicinaisR70?: string;
    TotalGeralGuiaR71?: string;
    // Obrigatório
    DataAssinProcSerie86?: string;
    DataAssinBenefResponsAutoriz87?: string;
    DataAssinBenefRespons88?: string;
    DataAssinPrestExecutante89?: string;

}
