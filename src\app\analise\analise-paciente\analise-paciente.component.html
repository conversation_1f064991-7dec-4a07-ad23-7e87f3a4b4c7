<!--DIV LISTA CADASTROS-->

<div style="padding: 0 5px;">
  <mat-card appearance="outlined" class="principal">
    <div class="div-tudo">

      <div class="linha-header">
        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; padding: 10px 0;">

          <div class="icon-page-title">
            <span class="material-icons" style="color: #1f5f3d; font-size: 30px;">post_add</span>
          </div>
          <div style="align-self: center;">
            <h3 class="title-page">Pendência</h3>
          </div>

        </div>
      </div>

      <!--DIV TABELA-->

      <div class="div-scroll-tabela">
        <div class="title-dados">
          <h5> Exames Solicitados </h5>
        </div>
        <div class="col-md-12 col-sm-12 col-xs-12 linha-tudo" *ngFor="let exames of listaAnalise">
          <div class="col-md-6 col-sm-12 col-xs-6 dados-tabela">
            <div class="col-md-3 col-sm-12 col-xs-3 foto-locais">
              <label for="imageperfilusuario" style="text-align: center; margin: 0;">
                <img src="{{ exames.lnkFotoPessoa }}" class="img-local profile_img-upload"
                  style="display: inline; border-radius: 50px;" alt="Foto de Perfil"
                  title="{{'TELACADASTROUSUARIO.FOTODEPERFIL' | translate}}">
              </label>
              <!-- <input type="file" id="imageperfilusuario" (change)="AlterarImagemClinica($event)"
                  (click)="LimpaCampoFile()" />
                  <input type="text" style="display: none;" id="Foto" name="Foto" [(ngModel)]="Dados.foto"> -->
            </div>

            <div class="col-md-9 col-sm-12 col-xs-9 infos-itens">
              <p>Exame: {{exames.nmeExame }}</p>
            </div>

          </div>

          <div class="col-md-3 col-sm-12 col-xs-3 data-tabela">
            <p>Data de Cadastro: {{exames.dtaCadastro | date: 'dd/MM/yyyy'}}</p>
            <p *ngIf="!exames.chaveArquivo">Arquivo Enviado: Não</p>
            <p *ngIf="exames.chaveArquivo">Arquivo Enviado: Sim</p>
          </div>


          <!-- <div class="col-md-3 col-sm-12 col-xs-3 btns-tabela" style="padding-top: 20px;">    
              <label for="file" class="btn btn-primary" [style.backgroundColor]="analise.chaveArquivo ? 'green !important' : '#5260ff !important' "  style="font-size:12px!important;margin-bottom: 20px; cursor: pointer;" >                
              <span *ngIf="!analise.chaveArquivo">{{ 'TELASTREAMING.CARREGARARQUIVO' | translate }}</span>
              <span *ngIf="analise.chaveArquivo">Arquivo Carregado</span>
              </label>
              <input type="file" style="width:100%;" id="file" (change)="SubirArquivoConsulta($event,analise)" [disabled]="analise.chaveArquivo"
                accept=".pdf" />
            </div> -->

          <div class="col-md-3 col-sm-12 col-xs-3 btns-tabela" style="padding-top: 20px;" *ngIf="!exames.chaveArquivo">
            <label for="file" class="btn btn-primary"
              [style.backgroundColor]="exames.chaveArquivo ? 'green !important' : '#5260ff !important' "
              style="font-size:12px!important;margin-bottom: 20px; cursor: pointer;">
              <span *ngIf="!exames.chaveArquivo">{{ 'TELASTREAMING.CARREGARARQUIVO' | translate }}</span>
            </label>
            <input type="file" style="width:100%;" id="file" (change)="SubirArquivoConsulta($event,exames)"
              [disabled]="exames.chaveArquivo" accept=".pdf" />
          </div>

          <div class="col-md-3 col-sm-12 col-xs-3 btns-tabela" style="padding-top: 20px; cursor: none;"
            *ngIf="exames.chaveArquivo">
            <label class="btn btn-primary"
              [style.backgroundColor]="exames.chaveArquivo ? 'green !important' : '#b6edd2 !important' "
              style="font-size:12px!important;margin-bottom: 20px; cursor: pointer; border: none !important;">
              <span *ngIf="exames.chaveArquivo">Arquivo Carregado</span>
            </label>
          </div>
        </div>

        <div style="align-self: center;" *ngIf="naotemanalises">
          <h3>Sem análises Cadastradas</h3>
        </div>
      </div>
      <!-- <div class="div-scroll-tabela">
        <div class="title-dados">
          <h5> Formulários Solicitados </h5>
        </div>
        <div class="col-md-12 col-sm-12 col-xs-12 linha-tudo" *ngFor="let forms of formsList">
          <div class="col-md-6 col-sm-12 col-xs-6 dados-tabela">
            <div class="col-md-9 col-sm-12 col-xs-9 infos-itens">
              <p>Formulario: {{forms.nomeFormulario }}</p>
            </div>

          </div>

          <div class="col-md-3 col-sm-12 col-xs-3 data-tabela">
            <p>Data de Cadastro: {{forms.dtaCadastro | date: 'dd/MM/yyyy'}}</p>
            <p>Formulario {{forms.flgRespondido? "": "não"}} respondido</p>
          </div>

          <div class="col-md-3 col-sm-12 col-xs-3 btns-tabela" style="padding-top: 20px;" *ngIf="!forms.flgRespondido">
            <button for="file" class="btn btn-primary" (click)="preparaFormulario(forms)"
              [style.backgroundColor]="forms.flgRespondido ? 'green !important' : '#5260ff !important' "
              style="font-size:12px!important;margin-bottom: 20px; cursor: pointer;">
              <span *ngIf="!forms.flgRespondido">Responder</span>
            </button>
          </div>

          <div class="col-md-3 col-sm-12 col-xs-3 btns-tabela" style="padding-top: 20px; cursor: pointer;"
            *ngIf="forms.flgRespondido">
            <button class="btn btn-primary"  (click)="preparaFormulario(forms)"
              [style.backgroundColor]="forms.flgRespondido ? 'green !important' : '#5260ff !important' "
              style="font-size:12px!important;margin-bottom: 20px; cursor: pointer;">
              <span *ngIf="forms.flgRespondido">Ver respostas</span>
            </button>
          </div>
        </div>

        <div style="align-self: center;" *ngIf="formsList.length < 1">
          <h3>Sem formulários cadastrados</h3>
        </div>
      </div> -->
    </div>
  </mat-card>
</div>
<ngx-smart-modal #formularioViewer identifier="formularioRespondido" customClass="nsm-centered medium-modal emailmodal">
  <div id="formularioViewer">
    <div class="formulario-content">
      <h1 id="tituloModal">
        {{formularioSelecionadoModal.nomeFormulario}}
      </h1>
      <div class="chat-container">
        <div *ngFor="let dados of listaPerguntas">
          <div class="chat-message left">
            <p>Pergunta: {{dados.pergunta}} <br>
              Tipo de Resposta: {{dados.tipoResposta}}</p>
          </div>
          <div class="chat-message right" *ngIf="dados.flgRespondido">
            <p>• Resposta: {{dados.resposta}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</ngx-smart-modal>

<ngx-smart-modal #formularioViewer identifier="formularioNaoRespondido" customClass="nsm-centered medium-modal emailmodal">
  <div id="formularioViewer">
    <div class="formulario-content">
      <h1 id="tituloModal">
        {{formularioSelecionadoModal.nomeFormulario}}
      </h1>
      <div class="chat-container">
        <div *ngFor="let dados of listaPerguntaResposta">
          <div class="chat-message left">
            <p>Pergunta: {{dados.pergunta}} <br>
              Tipo de Resposta: {{dados.tipoResposta}}</p>
          </div>
          <div class="chat-message right" >
            <mat-form-field style="width: 90%" *ngIf="dados.idTipoResposta == 1">
              <mat-label>Resposta: </mat-label>
              <select matNativeControl required [(ngModel)]="dados.resposta">
                <option value="sim">Sim</option>
                <option value="não">Não</option>
              </select>
            </mat-form-field>

            <mat-form-field class="example-form-field" style="width: 90%"  *ngIf="dados.idTipoResposta == 2">
              <mat-label>Resposta:</mat-label>
              <input matInput type="text" [(ngModel)]="dados.resposta">
              <button matSuffix mat-icon-button aria-label="Clear">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
            
            <mat-form-field class="example-form-field" style="width: 90%"  *ngIf="dados.idTipoResposta == 3">
              <mat-label>Resposta:</mat-label>
              <input matInput type="number" [(ngModel)]="dados.resposta">
              <button matSuffix mat-icon-button aria-label="Clear">
                <mat-icon>close</mat-icon>
              </button>
            </mat-form-field>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="row-button">
    <button mat-flat-button (click)="Salvar()" class="btn btn-add btn-email" style="background-color: rgb(176, 209, 176) !important;">Salvar</button>
  </div>
</ngx-smart-modal>