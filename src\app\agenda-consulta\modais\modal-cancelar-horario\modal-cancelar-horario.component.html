<div class>

    <div class="modal-info text-center">
        <b class>
            {{ 'TELAAGENDA.DESEJACANCELARESSEHORARIO' | translate }}
        </b>
    </div>

    <hr class="sep-1" />

    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 " style="margin-top: 20px;" hintLabel="Máx. 200"
        floatLabel="always">
        <mat-label>{{ 'TELAAGENDA.MOTIVODOCANCELAMENTO' | translate
            }}</mat-label>
        <textarea matInput #input maxlength="200" required name="Motivo Cancelamento" [(ngModel)]="Motivocancelameto"
            (change)="MotivoCampo()"
            style="max-height: 200px; min-height: 48px;padding: 5px; border: solid 1px #8e8e8e;"></textarea>
        <mat-hint align="end">{{Motivocancelameto.length +
            0}}/200</mat-hint>
    </mat-form-field>
    <div class="danger-baloon" *ngIf="cancelamento == true">
        <label style="color: red;" class="text-right">{{
            'TELAAGENDA.MOTIVODESERPREENCHIDO' | translate }}</label>
    </div>

    <div class="row-button text-center p-t-20 p-b-20">
        <button mat-flat-button (click)="fecharModal(false)" class="input-align btn btn-danger">
            {{ 'TELAAGENDA.NAO' | translate }} </button>
        <button mat-flat-button class="input-align btn btn-success" (click)=CancelarConsulta()>
            {{ 'TELAAGENDA.SIM' | translate }} </button>
    </div>

</div>