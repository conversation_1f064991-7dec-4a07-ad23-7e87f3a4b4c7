<mat-card appearance="outlined" style="padding:0px;" class="mother-div">
  <mat-card-title class="spacer-card">
    <div class="row">
      <div class="col-md-3 top-title">
        <mat-icon class="icon-title">group</mat-icon> <a class="title-content medic-title">Reuniões</a>
      </div>
      <div class="col-md-6"></div>
      <div class="col-md-3 col-sm-3 no-mobile ">
        <!-- <button mat-raised-button class="input-align btn-primary f-r" (click)="ConsultaSolicitar();">
          <mat-icon style="color:white;">add</mat-icon> <a class="ad-pp "><PERSON><PERSON><PERSON></a>
        </button> -->
      </div>
    </div>
  </mat-card-title>
  <mat-card-content>
    <div class="col-md-12 col-sm-12 col-xs-12  no-mobile-card" style="margin-top: 20px;">
      <table *ngFor="let item of DadosSolicitacao" class="table" id="DatatableCliente"
        style="margin-top: 10px; margin-bottom: 10px;">
        <thead style="display: none;">
          <tr>
            <th class="">{{ 'TELAPESQUISAMEDICO.NOME' | translate }}</th>
            <th class="">{{ 'TELAPESQUISAMEDICO.DADOS' | translate }}</th>
            <th class="text-center">{{ 'TELAPESQUISAMEDICO.ACOES' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr class="card_table">
            <td id="paciente">


              <b style="font-size: 12px;vertical-align: inherit!important;">
                Nome da Sala: {{item.tituloReuniao}}</b><br>


            </td>
            <td class="" style="width:25%;">

              <label class="Title-b">Data da Solicitação: {{item.dtaReuniao  | date: 'dd/MM/yyyy HH:mm'}}</label>


            </td>


            <td class="text-center" style="width: 5%;">


              <!-- <button class="btn-primary buttons-mobilet" id="opcao2" mat-mini-fab style="    width: 30px;
                  touch-action: none;
                  user-select: none;
                  -webkit-user-drag: none;
                  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                  height: 30px;
                  margin-right: 4%;" matTooltip="{{'TELACONSULTAS.CANCELAR' | translate}}"
                (click)="CancelaValue(item.idReuniao)" *ngIf="item.flgInativo != true  &&  item.flgrealizada != true">
                <mat-icon style="
                      width: 23px !important;
                      margin-top: -7px;
                      font-size: 19px;" aria-label="Cancelar Consulta" class="svg-icon">delete</mat-icon>
              </button> -->

              <button class="btn-primary buttons-mobilet" mat-mini-fab style="    width: 30px;
              touch-action: none;
              user-select: none;
              -webkit-user-drag: none;
              -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
              height: 30px;
              margin-right: 4%;" matTooltip="informação de acesso."
                (click)="modalInformativo(item.idReuniao)">
                <mat-icon style="
                  font-size: 20px;
                  width: 22px !important;
                  margin-top: -8px;" aria-label="Motivo do cancelamento">notification_important</mat-icon>
              </button>

              <button class="btn-primary buttons-mobilet" id="opcao2" mat-mini-fab style="    width: 30px;
                  background-color: green !important;
                  touch-action: none;
                  user-select: none;
                  -webkit-user-drag: none;
                  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                  height: 30px;
                  margin-right: 4%;" matTooltip="ir para a Reunião"
                (click)="CarregaReuniao(item.idReuniao,item.codAcesso)">
                <mat-icon style=" width: 22px !important;
                      margin-top: -10px;" aria-label="Iniciar consulta  choicer" class="">
                  play_arrow</mat-icon>
              </button>
              <div class="text-center">
                <div *ngIf="item.flgrealizada == true"
                  style="position: absolute; margin-top: -40px; margin-left: 50px;">
                  <img src="{{ 'TELACONSULTAS.CARIMBOFINALIZADA' | translate }}" style="width: 40px;">
                </div>
                <div *ngIf="item.flgInativo == true" style="position: absolute; margin-top: -60px; margin-left: 50px;">
                  <img src="{{ 'TELACONSULTAS.CARIMBOCANCELADA' | translate }}" alt="" style="width: 40px;">

                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="row">
      <div class="col-md-12 col-sm-12 col-12 text-center desktop-none">
        <button mat-raised-button class="input-align btn-primary  desktop-none " (click)="ConsultaSolicitar();"
          style="margin-bottom: 10px;">
          <mat-icon style="color:white;">add</mat-icon> <a class="ad-pp ">Criar Reunião</a>
        </button>
      </div>


    </div>

    <mat-card appearance="outlined" *ngFor="let item of DadosSolicitacao; let i = index" class="header-card no-desktop">
      <mat-card-header style="margin-left: -16px;">
        <div class="col-12 text-center titulo-card">
          <b class="Title-b titulo-medicos">{{item.nomeSolicitante}}</b><br *ngIf="item.nomeMedico != null">
          <b class="Title-b titulo-medicos" *ngIf="item.nomeMedico != null">Médico: {{item.nomeMedico}}</b>
        </div>
      </mat-card-header>

      <div class="div_paciente date text-center">
        <h4 class="Title-b">{{ 'TELAPESQUISAMEDICO.DATADECADASTRO' | translate }}</h4>
        <label class="label_paciente"> {{item.dtaCadastro | date: 'dd/MM/yyyy HH:mm'}} </label><br>
      </div>
      <div class="grid-buttons" style="bottom: -10px !important;">
        <div class="botoes-subir" [@openClose]="toggle[i] ? 'open': 'closed'">
          <button class="btn-primary  buttons-mobilet" id="opcao2" mat-mini-fab
            style="background-color: green !important;"
            *ngIf=" item.flgInativo != true &&  item.flgrealizada != true  && ( item.flgAndamento == true || item.flgFilaEspera == true ) ">

            <mat-icon aria-label="Iniciar consulta  choicer">
              play_arrow</mat-icon>
          </button>


          <button class="btn-primary buttons-mobilet bottom-sobre" id="opcao8" mat-mini-fab          
            title="{{'TELAPESQUISAMEDICO.EXCLUIR' | translate}}">
            <mat-icon class="" aria-label="Deletar Linha selecionada">delete</mat-icon>
          </button>
        </div>
        <button class="btn-primary circle-subir" mat-mini-fab (click)="toggle[i] = !toggle[i]">
          <mat-icon>keyboard_arrow_up</mat-icon>
        </button>

      </div>
    </mat-card>

    <div class="col-sm-12 text-center">



      <button mat-flat-button class="btn-primary"
        *ngIf="(DadosSolicitacao != undefined &&  DadosSolicitacao.length > 0) && bOcultaCarregaMais == false"
        (click)="CarregarMais()">{{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}</button>
    </div>



  </mat-card-content>

</mat-card>




<!-- <ngx-smart-modal #cancelarHorario identifier="cancelarHorario" customClass="nsm-centered medium-modal emailmodal">
  <div class="background-delete" style="width:100%;">
    <div style="justify-content: center;  display: flex;">
      <img src="assets/build/img/time.png">
    </div>

  </div>
  <div class="modal-info text-center">
    <b class="">
      {{ 'TELACONSULTAS.DESEJACANCELARESSEHORARIO' | translate }}
    </b>


    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 " style="margin-top: 20px;" appearance="outline"
      floatLabel="always" hintLabel="Máx. 200">
      <mat-label>{{ 'TELACONSULTAS.MOTIVODOCANCELAMENTO' | translate }}</mat-label>
      <textarea matInput name="Motivo Cancelamento" [(ngModel)]="Motivocancelameto" (change)="MotivoCampo()"
        style="max-height: 200px; min-height: 48px;" maxlength="200"></textarea>
      <mat-hint align="end">{{Motivocancelameto.length + 0}}/200</mat-hint>
    </mat-form-field>
    <div class="danger-baloon" *ngIf="cancelamento == true">
      <label style="color: red;" class="text-right">{{ 'TELACONSULTAS.MOTIVODESERPREENCHIDO' | translate }}</label>
    </div>

  </div>

  <div class="row-button text-center p-t-20 p-b-20">
    <button mat-flat-button (click)="cancelarHorario.close()" class="input-align btn btn-danger">
      {{ 'TELACONSULTAS.NAO' | translate }}
    </button>
    <button mat-flat-button class="input-align btn btn-success" (click)=CancelarConsulta()>
      {{ 'TELACONSULTAS.SIM' | translate }} </button>
  </div>

</ngx-smart-modal> -->



<ngx-smart-modal #SolicitarConsulta identifier="SolicitarConsulta" customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-consultas">
    <div class="col-md-12 mb-4 no-mobile"
      style="display: flex; height: 60px; width: 515px !important; background-size: 547px !important;">
      <div class="modal-info" style="margin-top: 25px; font-size: 27px;">
        <b class="titulo-orientacao"> Orientação Médica </b><br>
      </div>
      <!--<mat-icon style="color: white; font-size: 88px;  margin-left: 25%;"> calendar_today</mat-icon>-->
    </div>

    <div class="col-md-12 mb-4 no-desktop">
      <div class="modal-info" style="font-size: 27px; padding: 20px !important;">
        <b class="titulo-orientacao2"> Orientação Médica </b>
        <!-- <br>
      <p style="margin-top: 4%; margin-bottom: -38px;"> Voçê está numa sala de espera virtual.</p> -->
      </div>
    </div>



    <div class="modal-info no-mobile">

      <hr class="sep-1">

      <!--<hr class="sep-2" />-->

      <div style="margin-top: 1rem !important;">
        <div class="row col-sm-12 info-card-posicao" style="margin-bottom: 1rem !important;">
          <!-- <div class="col-sm-12" style="margin-top: auto !important; margin-bottom: auto !important;">
            <div class="col-sm-12" *ngIf="PosicaoFila == 0">
              <p class="modal-online" style="margin-bottom: 0 !important">Médicos On-line: {{MedicosOnline}}</p><br>
            </div>
            <div class="col-12 panel-button-dropdown btn-solicitar" *ngIf="PosicaoFila == 0">
              <button class=" button-interactive btn btn-primary" (click)="solicitacaoConsulta()">
              Solicitar Orientação </button>
            </div>
        </div> -->


          <!-- <div class="col-12 row panel-button-dropdown div-posicao" *ngIf="PosicaoFila != 0">
           
            <div class="col-md-6" style="text-align: center; margin: 10px auto; max-width: 200px;">
              <p class="online-medic" style="float: left;">Médicos On-line: {{MedicosOnline}}</p><br>
              
            </div>
            <div class=" col-md-6" style="text-align: center; margin: 0 auto;">
              <div class="fila-position" style="text-align: center; margin: 20px auto; border: 4px solid #ddd;
              border-radius: 50%; height: 20vh; width: 20vh;">
              <p class="posicao-medic" style="float: left;"> Sua Posição : </p>
              <button mat-fab class="avisos button-contact basic-pulses-red btn-posicao" *ngIf="!ConsultaAgoraTeste"
                style="background: transparent; color: darkblue;">
                <h1 class="num-posicao"> {{PosicaoFila}}</h1>
              </button>
            </div>


              <button mat-fab class="avisos button-contact basic-pulses-red" *ngIf="ConsultaAgoraTeste"
                style="background: green; color: white;" (click)="IniciarConsulta()">
                <h1 style="
                             font-size: 17px;"> Entrar</h1>
              </button>

              <button mat-flat-button class="btn-danger btn-desistir" style=" float: right;  margin-top: 8%;"
                (click)="ngxSmartModalService.getModal('sairconsulta').open()">Desistir</button>

            </div>
            <div class="col-12  ">

              <span class=""> </span>
            </div>

         
          </div> -->


        </div>
      </div>
      </div>

    <div class="modal-info2 no-desktop">


    </div>

    <img class="logo-final-modal2"   >
  </div>
</ngx-smart-modal>



<ngx-smart-modal #infoConsulta identifier="infoConsulta" customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-consultas">
    <div class="col-md-12 mb-4"
      style="display: flex; height: 50px; width: 515px !important; background-size: 547px !important;">

      <div class="modal-titulo-consulta">
        <h3 class="titulo-consulta">Acesso a sala Vitual.</h3>
      </div>
    </div>

    <hr class="sep-1">
    <div class="row p-2 card-consulta">
      <div class="col-lg-9 text-left info-consulta">
        <small>
          <p style="margin-bottom: 0 !important">
            Link Acesso: {{modalInfo.guid}}</p>
        </small>
        <small>
          <p style="margin-bottom: 0 !important">
            Cod.Acesso: {{modalInfo.CodAcesso}}</p>
        </small>
      </div>
      <div class="col-lg-3" style="margin-top: auto !important; margin-bottom: auto !important;">
        <button class=" button-interactive btn btn-primary " style="float: right;
                      margin-left: 2%;" title="Copiar Link e Código"
          (click)="copyMessage('Link Acesso: ' +modalInfo.guid  +'\n' +  'Cod.Acesso: ' + modalInfo.CodAcesso)">
          <i class="fa fa-files-o" style="
                          font-size: 25px;"> </i>
        </button>
      </div>
    </div>
    <div class="text-center">
  
    </div>
  </div>
</ngx-smart-modal>