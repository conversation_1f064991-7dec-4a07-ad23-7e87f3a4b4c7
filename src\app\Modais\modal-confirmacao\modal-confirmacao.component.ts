import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef as MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-modal-confirmacao',
    templateUrl: './modal-confirmacao.component.html',
    styleUrls: ['./modal-confirmacao.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      MatDialogModule
    ]
})
export class ModalConfirmacaoComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ModalConfirmacaoComponent>,
    @Inject(MAT_DIALOG_DATA) public fraseModal: string
  ) { }

  ngOnInit(): void {
  }

  fecharModal(resultado: boolean): void {
    this.dialogRef.close(resultado);
  }
}



export interface ModalData {
  FraseConfirmacao: string;
}
