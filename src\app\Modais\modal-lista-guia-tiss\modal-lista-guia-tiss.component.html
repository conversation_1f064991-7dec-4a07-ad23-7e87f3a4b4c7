<div class="container">
    <mat-card class="card-principal">
      <!-- CABEÇALHO -->
      <div class="header">
        <div class="header-main">
          <div class="header-icon">
            <span class="material-icons">description</span>
          </div>
          <h2 class="header-title">Lista de Guias</h2>
        </div>
      </div>
  
      <!-- AÇÕES -->
      <div class="actions-container">
        <div class="spacer"></div>
        <div class="actions-buttons">
          <button class="btn-acao" (click)="BaixarRelatorioListaConsulta()">
            <mat-icon>picture_as_pdf</mat-icon>
            <span>Baixar Relatório</span>
          </button>
        </div>
      </div>
  
      <!-- TABELA DE GUIAS -->
      <div class="tabela-container">
        <table class="tabela-faturas">
          <thead>
            <tr>
              <th class="coluna-descricao" style="width: 30%">
                <span>Número Guia Operadora</span>
              </th>
              <th class="coluna-convenio" style="width: 60%">
                <span>Fatura</span>
              </th>
              <th class="coluna-acoes" style="width: 10%">
                <span>Selecionar</span>
              </th>
            </tr>
          </thead>
          <tbody *ngIf="listaGuiaTiss && listaGuiaTiss.length > 0">
            <tr *ngFor="let item of listaGuiaTiss">
              <td class="coluna-descricao">
                <span class="cell-content">{{item.numGuiaOperadora}}</span>
              </td>
              <td class="coluna-convenio">
                <span class="cell-content">{{item.desFatura}}</span>
              </td>
              <td class="coluna-acoes">
                <mat-checkbox (change)="AdicionarGuiaTiss($event, item.idGuiaTiss!)"></mat-checkbox>
              </td>
            </tr>
          </tbody>
          <tbody *ngIf="!listaGuiaTiss || listaGuiaTiss.length === 0">
            <tr class="linha-vazia">
              <td colspan="3">
                <div class="mensagem-vazia">
                  <mat-icon>sentiment_very_dissatisfied</mat-icon>
                  <p>Nenhuma guia encontrada</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </mat-card>
  </div>