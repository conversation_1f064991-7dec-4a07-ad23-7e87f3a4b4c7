// Variáveis - Esquema de cores azul e roxo
$primary-color: #2E8B57 ; // Índigo
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9 ; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; // Tom médio entre azul e roxo
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s ;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: #fff;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
  margin: 0;
  border: none;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-icon {
  background-color: transparent;
  border: 2px solid $primary-color;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-icon .material-icons {
  color: $primary-color;
  font-size: 24px;
}

.header-title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0;
  text-transform: uppercase;
}

/* FILTROS */
.filtros {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.toggles-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 24px;
  gap: 16px;
  background-color: $bg-color;
  padding: 16px;
  border-radius: $border-radius;
  width: 100%;
}

.toggle-item {
  margin-right: 24px;
  background-color: white;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  color: $primary-color;
  font-weight: 400;
  font-size: 14px;
  margin: 0;
}

.toggle-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.toggle-item ::ng-deep .mat-slide-toggle-bar {
  background-color: rgba(0, 0, 0, 0.1);
}

.toggle-item ::ng-deep .mat-slide-toggle-thumb {
  background-color: white;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-bar {
  background-color: rgba(82, 96, 255, 0.5) !important;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-thumb {
  background-color: $primary-color !important;
}

.busca-container {
  flex-grow: 1;
  max-width: 100%;
}

.busca-field {
  width: 100%;
}

.busca-field ::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

.busca-field ::ng-deep .mat-form-field-flex {
  background-color: $bg-color;
}

.busca-field ::ng-deep .mat-form-field-outline {
  color: $border-color;
}

.btn-busca {
  color: $primary-color;
  background-color: transparent;
}

.adicionar-container {
  display: flex;
  justify-content: flex-end;
}

.btn-adicionar {
  background-color: $primary-color;
  color: white;
  height: 45px;
  padding: 0 16px;
  border-radius: 8px;
  font-size: 14px;
  transition: all $transition;
}

.btn-adicionar:hover {
  background-color: $primary-dark;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-adicionar:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* LISTA DE PACIENTES - DESKTOP */
.lista-container {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 8px;
  margin-bottom: 24px;
}

.lista-scroll {
  max-height: 57vh;
  overflow-y: auto;
  padding: 10px;
}

.lista-scroll::-webkit-scrollbar {
  width: 6px;
}

.lista-scroll::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.lista-scroll::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.paciente-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid transparent;
}

.paciente-card:hover {
  transform: translateY(-2px);
  box-shadow: $box-shadow;
  border-left: 4px solid $primary-color;
}

.paciente-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 280px;
}

.paciente-avatar {
  margin-right: 16px;
}

.img-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid $primary-light;
}

.paciente-detalhes {
  flex: 1;
}

.paciente-detalhes.sem-foto {
  margin-left: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item mat-icon {
  font-size: 18px;
  color: $primary-color;
  margin-right: 8px;
}

.info-item .nome {
  font-weight: 600;
  color: $text-primary;
}

.paciente-dados {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 240px;
  margin: 0 16px;
}

.paciente-dados.com-foto {
  padding-left: 16px;
}

.dados-item {
  margin-bottom: 8px;
}

.dados-item:last-child {
  margin-bottom: 0;
}

.dados-label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: $primary-color;
  margin-bottom: 2px;
}

.dados-valor {
  color: $text-secondary;
}

.paciente-acoes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-width: 200px;
  justify-content: flex-end;
  
  .remove {
    color: $error-color !important;
    background-color: rgba(255, 107, 107, 0.05) !important;
    mat-icon {
      color: $error-color;
      &:hover {
        color: lighten($error-color, 15%);
      }
    }
  }
}

.paciente-acoes button {
  width: 36px;
  height: 36px;
  background-color: $primary-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition;
}

.paciente-acoes button:hover {
  background-color: $primary-color;
  transform: scale(1.1);
}

.paciente-acoes button:hover mat-icon {
  color: white;
}

.paciente-acoes mat-icon {
  color: $primary-color;
  font-size: 20px;
  transition: color $transition;
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: $text-secondary;
}

.lista-vazia mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* MOBILE VIEW */
.mobile-view {
  display: none;
}

/* LISTA DE PACIENTES - MOBILE */
.lista-mobile {
  margin-bottom: 24px;
}

.paciente-card-mobile {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.paciente-header-mobile {
  display: flex;
  align-items: center;
  padding: 16px;
}

.paciente-avatar-mobile {
  margin-right: 16px;
}

.paciente-info-mobile {
  flex: 1;
}

.paciente-nome-mobile {
  font-size: 18px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
}

.paciente-titulo-mobile {
  color: $text-secondary;
  margin: 0;
  font-size: 14px;
}

.paciente-dados-mobile {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
}

.dados-mobile-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
}

.dados-mobile-item p {
  color: $text-secondary;
  margin: 0;
  font-size: 14px;
}

.acoes-mobile {
  position: relative;
  padding: 16px;
  text-align: right;
}

.acoes-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  position: absolute;
  right: 80px;
  bottom: 16px;
  max-width: calc(100% - 80px);
}

.acoes-buttons button {
  background-color: $primary-color;
  color: white;
}

.toggle-button {
  background-color: $primary-color;
  color: white;
}

/* BOTÃO CARREGAR MAIS */
.carregar-mais {
  text-align: center;
  margin-top: 20px;
}

.btn-carregar {
  background-color: $primary-color;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all $transition;
}

.btn-carregar:hover {
  background-color: $primary-dark;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* MODAIS */
.modal-container ::ng-deep .nsm-content {
  border-radius: $border-radius;
  padding: 0;
  overflow: hidden;
  width: 90%;
  max-width: 500px;
}

.modal-content {
  display: flex;
  flex-direction: column;
}

.modal-header {
  background-color: $primary-color;
  color: white;
  padding: 16px 24px;
  text-align: center;
}

.modal-header h3 {
  margin: 0;
  font-weight: 500;
  font-size: 18px;
}

.modal-body {
  padding: 24px;
  text-align: center;
}

.modal-text {
  font-size: 16px;
  color: $text-primary;
  margin-bottom: 8px;
}

.modal-subtexto {
  font-size: 14px;
  color: $text-secondary;
  margin-bottom: 16px;
}

.alerta-inativo {
  background-color: #FEF2F2;
  border-left: 4px solid $error-color;
  padding: 12px;
  margin-top: 16px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alerta-inativo mat-icon {
  color: $error-color;
  margin-bottom: 8px;
}

.alerta-inativo p {
  color: $error-color;
  font-weight: 500;
}

.alerta-subtexto {
  font-size: 12px;
  margin-top: 4px;
}

.motivo-field {
  width: 100%;
  margin-bottom: 16px;
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
}

.select-field {
  width: 100%;
  font-size: 14px;
}

.modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
  background-color: #f9fafb;
}

.btn-cancelar {
  background-color: #f3f4f6;
  color: $text-secondary;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-cancelar:hover {
  background-color: #e5e7eb;
}

.btn-confirmar {
  background-color: $primary-color;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-confirmar:hover {
  background-color: $primary-dark;
}

.btn-excluir {
  background-color: #d85959;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-excluir:hover {
  background-color: #dc2626;
}

/* ANIMAÇÕES PARA BOTÕES MOBILE */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.acoes-buttons button {
  animation: slideIn 0.2s ease-out forwards;
}

/* RESPONSIVIDADE */
@media (max-width: 1024px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toggles-container, .busca-container, .adicionar-container {
    width: 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }
  
  .adicionar-container {
    margin-bottom: 0;
  }
  
  .btn-adicionar {
    width: 100%;
    justify-content: center;
  }
  
  .paciente-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .paciente-info, .paciente-dados, .paciente-acoes {
    width: 100%;
    margin: 0;
    margin-bottom: 16px;
  }
  
  .paciente-acoes {
    margin-bottom: 0;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .desktop-view {
    display: none;
  }
  
  .mobile-view {
    display: block;
  }
}