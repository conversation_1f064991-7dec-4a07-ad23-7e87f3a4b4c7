<form [formGroup]="form" (ngSubmit)="SalvarChamado()">
    <div style="display: flex; justify-content: space-between;">
        <h2 mat-dialog-title style="font-weight: 700; color: #5260ff; margin-bottom: -2px;"><PERSON><PERSON> paciente</h2>
        <button mat-icon-button (click)="fecharModal()"> <mat-icon>close</mat-icon> </button>
    </div>

    <hr>
    <br>

    <span>Escolha a sala:</span>
    <div class="campo">
        <mat-form-field appearance="outline" *ngIf="listaSala.length > 0">
            <mat-label>Salas</mat-label>
            <mat-select formControlName="idSala">
                <mat-option *ngFor="let salas of listaSala" [value]="salas.idSala">
                    {{salas.numero +" - "+ salas.descSala}}
                </mat-option>
            </mat-select>
        </mat-form-field>

        <span class="msgErro" *ngIf="listaSala.length < 1">
            Não há salas. Primeiro cadastre uma sala antes de chamar o paciente.
        </span>
    </div>

    <span>Escolha o tipo de atendimento:</span>
    <div class="campo">
        <mat-form-field appearance="outline">
            <mat-label>Tipos de atendimentos</mat-label>
            <mat-select formControlName="idTipoAtendimento">
                <mat-option *ngFor="let tipo of listaTipoAtendimento" [value]="tipo.idTipoAtendimento">
                    {{tipo.descAtendimento}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>

    <br>

    <div style="display: flex; justify-content: flex-end; gap: 2em; width: 100%;">
        <button mat-raised-button style="background-color: rgb(163, 163, 163); color: #000000;" (click)="fecharModal()">
            cancelar
        </button>
        
        <button mat-raised-button color="primary" [disabled]="!form.valid">
            <mat-icon>check_circle</mat-icon>
            salvar
        </button>
    </div>
</form>