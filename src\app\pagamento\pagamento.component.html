<!-- <ngx-smart-modal #ModalPagamento identifier="ModalPagamento" customClass="nsm-dialog-animation-ltr nsm-centered pagamento-modal medium-modal" style="padding: 0 !important;">

    <div class="col-md-12 row ajuste-row">
        <div class="col-md-6 text-center div-content d-flex align-items-center">





            <div class="checkout">
                <div class="credit-card-box">
                  <div class="flip">
                    <div class="front">
                      <div class="chip"></div>
                      <div class="logo">
                        <svg version="1.1" id="visa" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                             width="47.834px" height="47.834px" viewBox="0 0 47.834 47.834" style="enable-background:new 0 0 47.834 47.834;">
                          <g>
                            <g>
                              <path d="M44.688,16.814h-3.004c-0.933,0-1.627,0.254-2.037,1.184l-5.773,13.074h4.083c0,0,0.666-1.758,0.817-2.143
                                       c0.447,0,4.414,0.006,4.979,0.006c0.116,0.498,0.474,2.137,0.474,2.137h3.607L44.688,16.814z M39.893,26.01
                                       c0.32-0.819,1.549-3.987,1.549-3.987c-0.021,0.039,0.317-0.825,0.518-1.362l0.262,1.23c0,0,0.745,3.406,0.901,4.119H39.893z
                                       M34.146,26.404c-0.028,2.963-2.684,4.875-6.771,4.875c-1.743-0.018-3.422-0.361-4.332-0.76l0.547-3.193l0.501,0.228
                                       c1.277,0.532,2.104,0.747,3.661,0.747c1.117,0,2.313-0.438,2.325-1.393c0.007-0.625-0.501-1.07-2.016-1.77
                                       c-1.476-0.683-3.43-1.827-3.405-3.876c0.021-2.773,2.729-4.708,6.571-4.708c1.506,0,2.713,0.31,3.483,0.599l-0.526,3.092
                                       l-0.351-0.165c-0.716-0.288-1.638-0.566-2.91-0.546c-1.522,0-2.228,0.634-2.228,1.227c-0.008,0.668,0.824,1.108,2.184,1.77
                                       C33.126,23.546,34.163,24.783,34.146,26.404z M0,16.962l0.05-0.286h6.028c0.813,0.031,1.468,0.29,1.694,1.159l1.311,6.304
                                       C7.795,20.842,4.691,18.099,0,16.962z M17.581,16.812l-6.123,14.239l-4.114,0.007L3.862,19.161
                                       c2.503,1.602,4.635,4.144,5.386,5.914l0.406,1.469l3.808-9.729L17.581,16.812L17.581,16.812z M19.153,16.8h3.89L20.61,31.066
                                       h-3.888L19.153,16.8z"/>
                            </g>
                          </g>
                        </svg>
                      </div>
                      <div class="number"></div>
                      <div class="card-holder">
                        <div></div>
                      </div>
                      <div class="card-expiration-date">
                        <div></div>
                      </div>
                    </div>
                    <div class="back">
                      <div class="strip"></div>
                      <div class="logo">
                        <svg version="1.1" id="visa" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                             width="47.834px" height="47.834px" viewBox="0 0 47.834 47.834" style="enable-background:new 0 0 47.834 47.834;">
                          <g>
                            <g>
                              <path d="M44.688,16.814h-3.004c-0.933,0-1.627,0.254-2.037,1.184l-5.773,13.074h4.083c0,0,0.666-1.758,0.817-2.143
                                       c0.447,0,4.414,0.006,4.979,0.006c0.116,0.498,0.474,2.137,0.474,2.137h3.607L44.688,16.814z M39.893,26.01
                                       c0.32-0.819,1.549-3.987,1.549-3.987c-0.021,0.039,0.317-0.825,0.518-1.362l0.262,1.23c0,0,0.745,3.406,0.901,4.119H39.893z
                                       M34.146,26.404c-0.028,2.963-2.684,4.875-6.771,4.875c-1.743-0.018-3.422-0.361-4.332-0.76l0.547-3.193l0.501,0.228
                                       c1.277,0.532,2.104,0.747,3.661,0.747c1.117,0,2.313-0.438,2.325-1.393c0.007-0.625-0.501-1.07-2.016-1.77
                                       c-1.476-0.683-3.43-1.827-3.405-3.876c0.021-2.773,2.729-4.708,6.571-4.708c1.506,0,2.713,0.31,3.483,0.599l-0.526,3.092
                                       l-0.351-0.165c-0.716-0.288-1.638-0.566-2.91-0.546c-1.522,0-2.228,0.634-2.228,1.227c-0.008,0.668,0.824,1.108,2.184,1.77
                                       C33.126,23.546,34.163,24.783,34.146,26.404z M0,16.962l0.05-0.286h6.028c0.813,0.031,1.468,0.29,1.694,1.159l1.311,6.304
                                       C7.795,20.842,4.691,18.099,0,16.962z M17.581,16.812l-6.123,14.239l-4.114,0.007L3.862,19.161
                                       c2.503,1.602,4.635,4.144,5.386,5.914l0.406,1.469l3.808-9.729L17.581,16.812L17.581,16.812z M19.153,16.8h3.89L20.61,31.066
                                       h-3.888L19.153,16.8z"/>
                            </g>
                          </g>
                        </svg>
              
                      </div>
                      <div class="ccv">
                        <label>CCV</label>
                        <div></div>
                      </div>
                    </div>
                  </div>
                </div>        
              </div>
        
        </div>

        <div class="col-md-6">
            
            <form class="form-group pt-3 pt-3" [formGroup]="pagamentoForm" (submit)="submit()">

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Nome do titular do cartão</mat-label>

                    <input matInput type="text" formControlName="CartaoNome" placeholder="Ex. João dos Santos" maxlength="50">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.required && pagamentoForm.get('CartaoNome').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.minlength && pagamentoForm.get('CartaoNome').touched ">
                        Mínimo 5 caracteres
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.maxlength && pagamentoForm.get('CartaoNome').touched ">
                        Máximo 50 caracteres
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.validaNome && pagamentoForm.get('CartaoNome').value.length > 4 && pagamentoForm.get('CartaoNome').touched ">
                        Campo inválido
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.pattern && pagamentoForm.get('CartaoNome').value.length > 4 && pagamentoForm.get('CartaoNome').touched ">
                        Somente letras
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Número do cartão</mat-label>

                    <input matInput formControlName="CartaoNumero" mask="0000-0000-0000-0000" placeholder="Ex. 0000-0000-0000-0000">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNumero').errors?.required && pagamentoForm.get('CartaoNumero').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNumero').errors?.maxlength || pagamentoForm.get('CartaoNumero').errors?.minlength  && pagamentoForm.get('CartaoNumero').touched ">
                        Número inválido
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Validade do cartão</mat-label>

                    <input matInput formControlName="CartaoMesAno" placeholder="Ex. 05/25" mask="00/00">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoMesAno').errors?.required && pagamentoForm.get('CartaoMesAno').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoMesAno').errors?.maxlength || pagamentoForm.get('CartaoMesAno').errors?.minlength  || pagamentoForm.get('CartaoMesAno').errors?.dataInorreta && pagamentoForm.get('CartaoMesAno').touched ">
                        Vencimento inválido
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Código de segurança do cartão</mat-label>

                    <input matInput formControlName="CartaoSeg" placeholder="Ex. 000" mask="000">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoSeg').errors?.required && pagamentoForm.get('CartaoSeg').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoSeg').errors?.maxlength || pagamentoForm.get('CartaoSeg').errors?.minlength && pagamentoForm.get('CartaoSeg').touched">
                        Código inválido
                </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Descrição do pagamento</mat-label>

                    <input matInput formControlName="FraseFatura" placeholder="Ex. Consulta com o..." maxlength="9">

                    <mat-error
                        *ngIf="pagamentoForm.get('FraseFatura').errors?.required && pagamentoForm.get('FraseFatura').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('FraseFatura').errors?.minlength && pagamentoForm.get('FraseFatura').touched  ">
                        Mínimo 3 caracteres
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('FraseFatura').errors?.maxlength && pagamentoForm.get('FraseFatura').touched ">
                        Máximo 9 caracteres
                    </mat-error>
                </mat-form-field>

                <div class="pb-3">
                    <span><b>Valor:</b></span>
                    <span class="float-right"><b>{{ valor | currency:'BRL':true }}</b></span>
                </div>

                <button [disabled]="progress" ejs-progressbutton [duration]='7000' cssClass='e-hide-spinner' [enableProgress]='progress' tmDarkenOnHover style="border-radius: .3rem !important; background-color: #2573c2 !important;" type="submit" class="btn btn-primary btn-block">PAGAR</button>
            </form>
        </div>
    </div>





</ngx-smart-modal> -->

<ngx-smart-modal #ModalPagamento identifier="ModalPagamento" class="custom"
    customClass="nsm-centered medium-modal form-modal emailmodal" [dismissable]="false">
    <div class="row">
        <div class="col-6  no-mobile-card" style="
        background: #104161;">

            <div class="checkout">
                <div id="card-credit" class="credit-card-box">
                    <div class="flip">
                        <div class="front">
                            <div class="chip"></div>
                            <div class="logo">
                               <!--<svg version="1.1" id="visa" xmlns="http://www.w3.org/2000/svg" 
                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="47.834px"
                                    height="47.834px" viewBox="0 0 47.834 47.834"
                                    style="enable-background:new 0 0 47.834 47.834;">
                                    <g>
                                        <g>
                                            <path d="M44.688,16.814h-3.004c-0.933,0-1.627,0.254-2.037,1.184l-5.773,13.074h4.083c0,0,0.666-1.758,0.817-2.143
                                       c0.447,0,4.414,0.006,4.979,0.006c0.116,0.498,0.474,2.137,0.474,2.137h3.607L44.688,16.814z M39.893,26.01
                                       c0.32-0.819,1.549-3.987,1.549-3.987c-0.021,0.039,0.317-0.825,0.518-1.362l0.262,1.23c0,0,0.745,3.406,0.901,4.119H39.893z
                                       M34.146,26.404c-0.028,2.963-2.684,4.875-6.771,4.875c-1.743-0.018-3.422-0.361-4.332-0.76l0.547-3.193l0.501,0.228
                                       c1.277,0.532,2.104,0.747,3.661,0.747c1.117,0,2.313-0.438,2.325-1.393c0.007-0.625-0.501-1.07-2.016-1.77
                                       c-1.476-0.683-3.43-1.827-3.405-3.876c0.021-2.773,2.729-4.708,6.571-4.708c1.506,0,2.713,0.31,3.483,0.599l-0.526,3.092
                                       l-0.351-0.165c-0.716-0.288-1.638-0.566-2.91-0.546c-1.522,0-2.228,0.634-2.228,1.227c-0.008,0.668,0.824,1.108,2.184,1.77
                                       C33.126,23.546,34.163,24.783,34.146,26.404z M0,16.962l0.05-0.286h6.028c0.813,0.031,1.468,0.29,1.694,1.159l1.311,6.304
                                       C7.795,20.842,4.691,18.099,0,16.962z M17.581,16.812l-6.123,14.239l-4.114,0.007L3.862,19.161
                                       c2.503,1.602,4.635,4.144,5.386,5.914l0.406,1.469l3.808-9.729L17.581,16.812L17.581,16.812z M19.153,16.8h3.89L20.61,31.066
                                       h-3.888L19.153,16.8z" />
                                        </g>
                                    </g>
                                </svg>-->
<!-- 
                                <img src="assets/build/img/cartao-visa.png"  x="0px" y="0px" width="80px" style="margin-left: -20px;" *ngIf='visa'>

                                <img src="assets/build/img/mastercard.png"  x="0px" y="0px" width="80px" style="margin-left: -20px;" *ngIf='master'>

                                <img src="assets/build/img/hipercard2.png"  x="0px" y="0px" width="90px" style="margin-left: -30px; margin-top: 10px;" *ngIf='hiper'>

                                <img src="assets/build/img/elo2.png"  x="0px" y="0px" width="80px" style="margin-left: -20px; margin-top: 10px;" *ngIf='elo'> 

                                <img src="assets/build/img/diners2.png"  x="0px" y="0px" width="80px" style="margin-left: -20px;" *ngIf='diners'>

                                <img src="assets/build/img/discover2.png"  x="0px" y="0px" width="100px" style="margin-left: -30px;" *ngIf='discover'>

                                <img src="assets/build/img/amex.png"  x="0px" y="0px" width="80px" style="margin-left: -30px; margin-top: -10px;" *ngIf='amex'>

                                <img src="assets/build/img/jcb.png"  x="0px" y="0px" width="60px" style="margin-left: -5px; margin-top: 10px;"  *ngIf='jcb'>

                                <img src="assets/build/img/cartao-aura.png"  x="0px" y="0px" width="70px" style="margin-left: -5px; margin-top: 10px;" *ngIf='aura'> -->



                                <!--<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 146.8 120.41" *ngIf='master'>
                                    <title>mc_vrt_rgb_pos</title>
                                    <g id="Layer_2" data-name="Layer 2">
                                        <g id="Layer_1-2" data-name="Layer 1">
                                            <rect class="cls-1" width="146.8" height="120.41" />
                                            <path class="cls-2"
                                                d="M36.35,105.26v-6a3.56,3.56,0,0,0-3.76-3.8,3.7,3.7,0,0,0-3.36,1.7,3.51,3.51,0,0,0-3.16-1.7,3.16,3.16,0,0,0-2.8,1.42V95.7H21.19v9.56h2.1V100a2.24,2.24,0,0,1,2.34-2.54c1.38,0,2.08.9,2.08,2.52v5.32h2.1V100a2.25,2.25,0,0,1,2.34-2.54c1.42,0,2.1.9,2.1,2.52v5.32ZM67.42,95.7H64V92.8h-2.1v2.9H60v1.9h1.94V102c0,2.22.86,3.54,3.32,3.54a4.88,4.88,0,0,0,2.6-.74l-.6-1.78a3.84,3.84,0,0,1-1.84.54c-1,0-1.38-.64-1.38-1.6V97.6h3.4Zm17.74-.24a2.82,2.82,0,0,0-2.52,1.4V95.7H80.58v9.56h2.08V99.9c0-1.58.68-2.46,2-2.46a3.39,3.39,0,0,1,1.3.24l.64-2a4.45,4.45,0,0,0-1.48-.26Zm-26.82,1a7.15,7.15,0,0,0-3.9-1c-2.42,0-4,1.16-4,3.06,0,1.56,1.16,2.52,3.3,2.82l1,.14c1.14.16,1.68.46,1.68,1,0,.74-.76,1.16-2.18,1.16a5.09,5.09,0,0,1-3.18-1l-1,1.62a6.9,6.9,0,0,0,4.14,1.24c2.76,0,4.36-1.3,4.36-3.12s-1.26-2.56-3.34-2.86l-1-.14c-.9-.12-1.62-.3-1.62-.94s.68-1.12,1.82-1.12a6.16,6.16,0,0,1,3,.82Zm55.71-1a2.82,2.82,0,0,0-2.52,1.4V95.7h-2.06v9.56h2.08V99.9c0-1.58.68-2.46,2-2.46a3.39,3.39,0,0,1,1.3.24l.64-2a4.45,4.45,0,0,0-1.48-.26Zm-26.8,5a4.83,4.83,0,0,0,5.1,5,5,5,0,0,0,3.44-1.14l-1-1.68a4.2,4.2,0,0,1-2.5.86,3.07,3.07,0,0,1,0-6.12,4.2,4.2,0,0,1,2.5.86l1-1.68a5,5,0,0,0-3.44-1.14,4.83,4.83,0,0,0-5.1,5Zm19.48,0V95.7h-2.08v1.16a3.63,3.63,0,0,0-3-1.4,5,5,0,0,0,0,10,3.63,3.63,0,0,0,3-1.4v1.16h2.08Zm-7.74,0a2.89,2.89,0,1,1,2.9,3.06,2.87,2.87,0,0,1-2.9-3.06Zm-25.1-5a5,5,0,0,0,.14,10A5.81,5.81,0,0,0,78,104.16l-1-1.54a4.55,4.55,0,0,1-2.78,1,2.65,2.65,0,0,1-2.86-2.34h7.1c0-.26,0-.52,0-.8,0-3-1.86-5-4.54-5Zm0,1.86a2.37,2.37,0,0,1,2.42,2.32h-5a2.46,2.46,0,0,1,2.54-2.32ZM126,100.48V91.86H124v5a3.63,3.63,0,0,0-3-1.4,5,5,0,0,0,0,10,3.63,3.63,0,0,0,3-1.4v1.16H126Zm3.47,3.39a1,1,0,0,1,.38.07,1,1,0,0,1,.31.2,1,1,0,0,1,.21.3.93.93,0,0,1,0,.74,1,1,0,0,1-.21.3,1,1,0,0,1-.31.2.94.94,0,0,1-.38.08,1,1,0,0,1-.9-.58.94.94,0,0,1,0-.74,1,1,0,0,1,.21-.3,1,1,0,0,1,.31-.2A1,1,0,0,1,129.5,103.87Zm0,1.69a.71.71,0,0,0,.29-.06.75.75,0,0,0,.23-.16.74.74,0,0,0,0-1,.74.74,0,0,0-.23-.16.72.72,0,0,0-.29-.06.75.75,0,0,0-.29.06.73.73,0,0,0-.24.16.74.74,0,0,0,0,1,.74.74,0,0,0,.24.16A.74.74,0,0,0,129.5,105.56Zm.06-1.19a.4.4,0,0,1,.26.08.25.25,0,0,1,.09.21.24.24,0,0,1-.07.18.35.35,0,0,1-.21.09l.29.33h-.23l-.27-.33h-.09v.33h-.19v-.88Zm-.22.17v.24h.22a.21.21,0,0,0,.12,0,.1.1,0,0,0,0-.09.1.1,0,0,0,0-.09.21.21,0,0,0-.12,0Zm-11-4.06a2.89,2.89,0,1,1,2.9,3.06,2.87,2.87,0,0,1-2.9-3.06Zm-70.23,0V95.7H46v1.16a3.63,3.63,0,0,0-3-1.4,5,5,0,0,0,0,10,3.63,3.63,0,0,0,3-1.4v1.16h2.08Zm-7.74,0a2.89,2.89,0,1,1,2.9,3.06A2.87,2.87,0,0,1,40.32,100.48Z" />
                                            <g id="_Group_" data-name="&lt;Group&gt;">
                                                <rect class="cls-3" x="57.65" y="22.85" width="31.5" height="56.61" />
                                                <path id="_Path_" data-name="&lt;Path&gt;" class="cls-4"
                                                    d="M59.65,51.16A35.94,35.94,0,0,1,73.4,22.85a36,36,0,1,0,0,56.61A35.94,35.94,0,0,1,59.65,51.16Z" />
                                                <path class="cls-5"
                                                    d="M131.65,51.16A36,36,0,0,1,73.4,79.46a36,36,0,0,0,0-56.61,36,36,0,0,1,58.25,28.3Z" />
                                                <path class="cls-5"
                                                    d="M128.21,73.46V72.3h.47v-.24h-1.19v.24H128v1.16Zm2.31,0v-1.4h-.36l-.42,1-.42-1H129v1.4h.26V72.41l.39.91h.27l.39-.91v1.06Z" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>

                                <svg width="512px" height="223px" viewBox="0 0 512 223" version="1.1"
                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                    preserveAspectRatio="xMidYMid" *ngIf=hiper>
                                    <g>
                                        <path
                                            d="M140.554538,0 L87.4900387,0 C64.043846,1.10858841 44.8782319,10.5521938 39.3443354,30.0263634 C36.4587893,40.1825609 34.8707842,51.3367896 32.6144097,61.8648617 C21.1435851,115.397722 10.9723116,170.404009 0,222.867478 L413.120547,222.867478 C445.058547,222.867478 466.987089,216.118457 472.913967,190.770678 C475.666844,178.995318 478.304139,165.65708 480.938418,152.719863 C491.206178,102.277583 501.482984,51.8383178 512,0 L140.554538,0"
                                            fill="#B82126"></path>
                                        <path
                                            d="M353.699639,133.153129 C347.970259,138.762039 331.887435,140.355263 333.532863,126.94789 C334.8992,115.812392 347.022557,113.446149 360.162886,115.054431 C359.186071,121.145224 358.065694,128.879435 353.699639,133.153129 Z M335.600941,89.7164549 C335.053804,92.7884549 334.213522,95.5683137 333.532863,98.5067922 C340.09349,96.8623686 360.52229,91.8186667 362.49098,100.574871 C363.144533,103.484235 362.017129,106.57531 361.197929,108.849192 C342.723765,107.099357 327.666949,110.168345 323.707482,123.327749 C321.055122,132.14218 324.004643,140.816063 329.654714,143.237522 C340.53622,147.898729 353.771922,142.558871 358.35382,135.222212 C357.877961,137.762133 357.4272,140.327153 357.577788,143.495529 L367.144157,143.495529 C367.245553,134.333741 368.578761,126.917773 369.988267,118.673569 C371.189961,111.653145 373.449788,104.702996 373.090384,98.5067922 C372.269176,84.3163608 348.760345,89.3359686 335.600941,89.7164549 Z M431.523639,137.548298 C424.039404,137.708925 420.317867,133.076831 420.1472,125.396831 C419.849035,111.94629 425.749082,97.0029176 437.728878,95.6626824 C443.306667,95.0392471 447.345443,96.3363137 451.432408,97.7317647 C447.680753,112.83978 449.035043,137.172831 431.523639,137.548298 Z M456.344596,66.4465569 C455.391875,74.8011922 454.117898,82.8355765 452.467451,90.4914824 C425.195922,81.8597647 408.470588,101.924141 408.770761,126.688878 C408.829992,131.478588 409.654212,136.228141 412.649914,139.616376 C417.812078,145.455184 432.59382,146.851639 440.055969,141.944471 C441.501616,140.994761 442.97738,139.266008 443.935122,138.066322 C444.653929,137.162792 445.796392,134.799561 446.002196,135.479216 C445.610667,138.104471 445.024376,140.534965 444.969161,143.495529 L455.052549,143.495529 C456.995137,115.618635 463.004612,91.8076235 467.463027,66.4465569 L456.344596,66.4465569 Z M171.67862,133.412141 C165.730384,139.711749 151.107263,139.615373 149.960784,129.015969 C149.460831,124.403953 151.179545,119.568063 152.028863,114.796424 C152.88822,109.966557 153.506635,105.332455 154.354949,101.09189 C160.213835,93.9379451 177.430086,93.0745725 179.17691,104.971043 C180.692831,115.299388 176.604863,128.194761 171.67862,133.412141 Z M180.469961,89.7164549 C171.060204,86.1816471 159.591404,90.4011294 154.619984,94.4148078 C154.637051,94.591498 154.501522,94.6155922 154.354949,94.6286431 C154.440282,94.5573647 154.531639,94.4860863 154.619984,94.4148078 C154.61898,94.399749 154.617976,94.386698 154.613961,94.3706353 C154.69829,92.6438902 155.305663,91.4421961 155.389992,89.7164549 L145.823624,89.7164549 C141.837051,116.239059 137.109584,142.019765 132.120094,167.540455 L143.237522,167.540455 C144.846808,157.600627 145.909961,147.115671 148.150714,137.80731 C150.689631,147.597553 167.268392,145.726243 174.263718,141.944471 C188.699106,134.13898 199.832596,96.9898667 180.469961,89.7164549 Z M233.213992,108.332173 L207.100988,108.332173 C207.927216,102.328722 213.328314,95.7299451 221.837553,95.4046745 C229.846839,95.0984784 235.581239,98.3451608 233.213992,108.332173 Z M222.613584,88.6824157 C214.580204,89.3008314 207.789678,91.6128627 202.963827,96.6967216 C197.044706,102.933082 192.25098,116.719937 193.655467,129.27498 C195.659294,147.186949 217.961412,146.544439 235.800094,142.202478 C236.103278,139.0592 236.864251,136.371702 237.351153,133.412141 C230.002447,136.161882 217.243608,139.999875 209.686086,135.222212 C203.979796,131.614118 203.946667,122.477427 205.807937,114.537412 C217.796769,114.155922 230.257443,114.229208 242.263341,114.537412 C243.025318,108.909427 245.204831,102.773459 243.29738,97.2147451 C240.781553,89.8760784 231.77738,87.9766588 222.613584,88.6824157 Z M124.880816,89.7164549 C124.579639,89.7596235 124.599718,90.125051 124.621804,90.4914824 C122.172235,108.812047 118.857286,126.268235 115.314447,143.495529 L126.431875,143.495529 C129.10331,125.051482 132.163263,106.996957 135.998243,89.7164549 L124.880816,89.7164549 Z M414.977004,89.9744627 C405.15062,85.0622745 396.970667,93.3054745 393.776188,98.2477804 C394.685741,95.7118745 394.737945,92.3166118 395.585255,89.7164549 L385.759875,89.7164549 C383.111529,108.182588 379.917051,126.103592 376.193506,143.495529 L387.569945,143.495529 C387.642227,136.367686 389.041694,131.09509 390.155043,124.10378 C392.533333,109.178478 396.022965,92.8095373 413.425945,97.7317647 C414.0032,95.206902 414.243137,92.3437176 414.977004,89.9744627 Z M290.095184,130.826039 C289.071184,128.178698 288.805145,123.80662 289.061145,120.483639 C289.636392,113.012455 292.358024,103.916925 296.559435,99.7998431 C302.357082,94.1166431 313.802792,95.0573176 322.931451,98.2477804 C323.214557,95.1707608 323.832973,92.4270431 324.224502,89.4584471 C309.252016,87.0128941 295.043514,88.5338353 287.510086,96.4387137 C280.136282,104.176941 275.301396,121.962416 278.718745,133.153129 C282.719373,146.248282 300.653427,146.960063 315.176157,141.944471 C315.817663,139.312188 316.157992,136.377725 316.727216,133.670149 C308.79222,137.793255 293.621961,139.935624 290.095184,130.826039 Z M283.890949,89.7164549 C274.032439,85.734902 266.283169,92.4631843 262.690133,98.7658039 C263.504314,95.961851 263.840627,92.6780235 264.4992,89.7164549 L254.67382,89.7164549 C252.272439,108.34422 248.731608,125.832533 245.366463,143.495529 L256.484894,143.495529 C258.04298,133.010573 258.715608,118.878369 262.17211,108.849192 C264.934902,100.834886 272.165145,94.0072157 282.597898,97.7317647 C282.744471,94.7752157 283.590776,92.5184 283.890949,89.7164549 Z M104.714039,67.9976157 C103.125835,78.3028706 101.336847,88.4073412 99.5428392,98.5067922 C88.0248471,98.6282667 76.2649098,99.0760157 65.1555137,98.2477804 C67.2537098,88.3681882 68.7585882,77.8932706 70.8427294,67.9976157 L58.4332549,67.9976157 C53.989898,93.2030745 49.9491137,118.809098 44.9887373,143.495529 L57.6572235,143.495529 C59.6449882,130.831059 61.5032471,118.038086 64.1204706,106.005082 C74.9226667,105.739043 87.8692392,105.275231 98.2497882,106.264094 C96.1094275,118.774965 93.5233255,130.83909 91.5275294,143.495529 L104.19702,143.495529 C108.266918,117.91862 112.556675,92.5605647 117.641537,67.9976157 L104.714039,67.9976157 Z M136.257255,79.3740549 C138.473914,77.8480941 141.328063,70.8929255 138.067325,67.9976157 C137.03429,67.0810353 135.305537,66.8149961 132.896125,67.2225882 C130.660392,67.6000627 129.374369,68.3590275 128.500957,69.5486745 C127.095467,71.462149 125.808439,77.2306824 127.982933,79.3740549 C130.101208,81.4612078 134.846745,80.3448471 136.257255,79.3740549 L136.257255,79.3740549 Z"
                                            fill="#FFFFFF"></path>
                                    </g>
                                </svg>-->

                            </div>
                            <div class="number">{{NumeroCartao}}</div>
                            <div class="card-holder">
                                <div>{{NomeCartao}}</div>
                            </div>
                            <div class="card-expiration-date">
                                <div>{{dtaCartaoExpira}}</div>
                            </div>
                        </div>


                        <div class="back">
                            <div class="strip"></div>
                            <div class="logo">

                                <!-- <img src="assets/build/img/cartao-visa.png"  x="0px" y="0px" width="70px" style="margin-left: -5px;" *ngIf='visa'>

                                <img src="assets/build/img/mastercard.png"  x="0px" y="0px" width="70px" style="margin-left: -10px;" *ngIf='master'>

                                <img src="assets/build/img/hipercard2.png"  x="0px" y="0px" width="70px" style="margin-left: -10px; margin-top: 10px;" *ngIf='hiper'>

                                <img src="assets/build/img/elo2.png"  x="0px" y="0px" width="70px" style="margin-left: -10px; margin-top: 10px;" *ngIf='elo'> 

                                <img src="assets/build/img/diners2.png"  x="0px" y="0px" width="70px" style="margin-left: -10px;" *ngIf='diners'>

                                <img src="assets/build/img/discover2.png"  x="0px" y="0px" width="90px" style="margin-left: -25px; margin-top: 10px;" *ngIf='discover'>

                                <img src="assets/build/img/amex.png"  x="0px" y="0px" width="70px" style="margin-left: -10px; margin-top: -10px;" *ngIf='amex'>

                                <img src="assets/build/img/jcb.png"  x="0px" y="0px" width="50px" style="margin-left: 10px; margin-top: 10px;" *ngIf='jcb'>

                                <img src="assets/build/img/cartao-aura.png"  x="0px" y="0px" width="60px" style="margin-left: -1px; margin-top: 10px;" *ngIf='bandeira == aura'> -->



                                <!--<svg version="1.1" id="visa" xmlns="http://www.w3.org/2000/svg" *ngIf='visa'
                                    xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="47.834px"
                                    height="47.834px" viewBox="0 0 47.834 47.834"
                                    style="enable-background:new 0 0 47.834 47.834;">
                                    <g>
                                        <g>
                                            <path d="M44.688,16.814h-3.004c-0.933,0-1.627,0.254-2.037,1.184l-5.773,13.074h4.083c0,0,0.666-1.758,0.817-2.143
                                       c0.447,0,4.414,0.006,4.979,0.006c0.116,0.498,0.474,2.137,0.474,2.137h3.607L44.688,16.814z M39.893,26.01
                                       c0.32-0.819,1.549-3.987,1.549-3.987c-0.021,0.039,0.317-0.825,0.518-1.362l0.262,1.23c0,0,0.745,3.406,0.901,4.119H39.893z
                                       M34.146,26.404c-0.028,2.963-2.684,4.875-6.771,4.875c-1.743-0.018-3.422-0.361-4.332-0.76l0.547-3.193l0.501,0.228
                                       c1.277,0.532,2.104,0.747,3.661,0.747c1.117,0,2.313-0.438,2.325-1.393c0.007-0.625-0.501-1.07-2.016-1.77
                                       c-1.476-0.683-3.43-1.827-3.405-3.876c0.021-2.773,2.729-4.708,6.571-4.708c1.506,0,2.713,0.31,3.483,0.599l-0.526,3.092
                                       l-0.351-0.165c-0.716-0.288-1.638-0.566-2.91-0.546c-1.522,0-2.228,0.634-2.228,1.227c-0.008,0.668,0.824,1.108,2.184,1.77
                                       C33.126,23.546,34.163,24.783,34.146,26.404z M0,16.962l0.05-0.286h6.028c0.813,0.031,1.468,0.29,1.694,1.159l1.311,6.304
                                       C7.795,20.842,4.691,18.099,0,16.962z M17.581,16.812l-6.123,14.239l-4.114,0.007L3.862,19.161
                                       c2.503,1.602,4.635,4.144,5.386,5.914l0.406,1.469l3.808-9.729L17.581,16.812L17.581,16.812z M19.153,16.8h3.89L20.61,31.066
                                       h-3.888L19.153,16.8z" />
                                        </g>
                                    </g>
                                </svg>


                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 146.8 120.41" *ngIf='master'>
                                    <title>mc_vrt_rgb_pos</title>
                                    <g id="Layer_2" data-name="Layer 2">
                                        <g id="Layer_1-2" data-name="Layer 1">
                                            <rect class="cls-1" width="146.8" height="120.41" />
                                            <path class="cls-2"
                                                d="M36.35,105.26v-6a3.56,3.56,0,0,0-3.76-3.8,3.7,3.7,0,0,0-3.36,1.7,3.51,3.51,0,0,0-3.16-1.7,3.16,3.16,0,0,0-2.8,1.42V95.7H21.19v9.56h2.1V100a2.24,2.24,0,0,1,2.34-2.54c1.38,0,2.08.9,2.08,2.52v5.32h2.1V100a2.25,2.25,0,0,1,2.34-2.54c1.42,0,2.1.9,2.1,2.52v5.32ZM67.42,95.7H64V92.8h-2.1v2.9H60v1.9h1.94V102c0,2.22.86,3.54,3.32,3.54a4.88,4.88,0,0,0,2.6-.74l-.6-1.78a3.84,3.84,0,0,1-1.84.54c-1,0-1.38-.64-1.38-1.6V97.6h3.4Zm17.74-.24a2.82,2.82,0,0,0-2.52,1.4V95.7H80.58v9.56h2.08V99.9c0-1.58.68-2.46,2-2.46a3.39,3.39,0,0,1,1.3.24l.64-2a4.45,4.45,0,0,0-1.48-.26Zm-26.82,1a7.15,7.15,0,0,0-3.9-1c-2.42,0-4,1.16-4,3.06,0,1.56,1.16,2.52,3.3,2.82l1,.14c1.14.16,1.68.46,1.68,1,0,.74-.76,1.16-2.18,1.16a5.09,5.09,0,0,1-3.18-1l-1,1.62a6.9,6.9,0,0,0,4.14,1.24c2.76,0,4.36-1.3,4.36-3.12s-1.26-2.56-3.34-2.86l-1-.14c-.9-.12-1.62-.3-1.62-.94s.68-1.12,1.82-1.12a6.16,6.16,0,0,1,3,.82Zm55.71-1a2.82,2.82,0,0,0-2.52,1.4V95.7h-2.06v9.56h2.08V99.9c0-1.58.68-2.46,2-2.46a3.39,3.39,0,0,1,1.3.24l.64-2a4.45,4.45,0,0,0-1.48-.26Zm-26.8,5a4.83,4.83,0,0,0,5.1,5,5,5,0,0,0,3.44-1.14l-1-1.68a4.2,4.2,0,0,1-2.5.86,3.07,3.07,0,0,1,0-6.12,4.2,4.2,0,0,1,2.5.86l1-1.68a5,5,0,0,0-3.44-1.14,4.83,4.83,0,0,0-5.1,5Zm19.48,0V95.7h-2.08v1.16a3.63,3.63,0,0,0-3-1.4,5,5,0,0,0,0,10,3.63,3.63,0,0,0,3-1.4v1.16h2.08Zm-7.74,0a2.89,2.89,0,1,1,2.9,3.06,2.87,2.87,0,0,1-2.9-3.06Zm-25.1-5a5,5,0,0,0,.14,10A5.81,5.81,0,0,0,78,104.16l-1-1.54a4.55,4.55,0,0,1-2.78,1,2.65,2.65,0,0,1-2.86-2.34h7.1c0-.26,0-.52,0-.8,0-3-1.86-5-4.54-5Zm0,1.86a2.37,2.37,0,0,1,2.42,2.32h-5a2.46,2.46,0,0,1,2.54-2.32ZM126,100.48V91.86H124v5a3.63,3.63,0,0,0-3-1.4,5,5,0,0,0,0,10,3.63,3.63,0,0,0,3-1.4v1.16H126Zm3.47,3.39a1,1,0,0,1,.38.07,1,1,0,0,1,.31.2,1,1,0,0,1,.21.3.93.93,0,0,1,0,.74,1,1,0,0,1-.21.3,1,1,0,0,1-.31.2.94.94,0,0,1-.38.08,1,1,0,0,1-.9-.58.94.94,0,0,1,0-.74,1,1,0,0,1,.21-.3,1,1,0,0,1,.31-.2A1,1,0,0,1,129.5,103.87Zm0,1.69a.71.71,0,0,0,.29-.06.75.75,0,0,0,.23-.16.74.74,0,0,0,0-1,.74.74,0,0,0-.23-.16.72.72,0,0,0-.29-.06.75.75,0,0,0-.29.06.73.73,0,0,0-.24.16.74.74,0,0,0,0,1,.74.74,0,0,0,.24.16A.74.74,0,0,0,129.5,105.56Zm.06-1.19a.4.4,0,0,1,.26.08.25.25,0,0,1,.09.21.24.24,0,0,1-.07.18.35.35,0,0,1-.21.09l.29.33h-.23l-.27-.33h-.09v.33h-.19v-.88Zm-.22.17v.24h.22a.21.21,0,0,0,.12,0,.1.1,0,0,0,0-.09.1.1,0,0,0,0-.09.21.21,0,0,0-.12,0Zm-11-4.06a2.89,2.89,0,1,1,2.9,3.06,2.87,2.87,0,0,1-2.9-3.06Zm-70.23,0V95.7H46v1.16a3.63,3.63,0,0,0-3-1.4,5,5,0,0,0,0,10,3.63,3.63,0,0,0,3-1.4v1.16h2.08Zm-7.74,0a2.89,2.89,0,1,1,2.9,3.06A2.87,2.87,0,0,1,40.32,100.48Z" />
                                            <g id="_Group_" data-name="&lt;Group&gt;">
                                                <rect class="cls-3" x="57.65" y="22.85" width="31.5" height="56.61" />
                                                <path id="_Path_" data-name="&lt;Path&gt;" class="cls-4"
                                                    d="M59.65,51.16A35.94,35.94,0,0,1,73.4,22.85a36,36,0,1,0,0,56.61A35.94,35.94,0,0,1,59.65,51.16Z" />
                                                <path class="cls-5"
                                                    d="M131.65,51.16A36,36,0,0,1,73.4,79.46a36,36,0,0,0,0-56.61,36,36,0,0,1,58.25,28.3Z" />
                                                <path class="cls-5"
                                                    d="M128.21,73.46V72.3h.47v-.24h-1.19v.24H128v1.16Zm2.31,0v-1.4h-.36l-.42,1-.42-1H129v1.4h.26V72.41l.39.91h.27l.39-.91v1.06Z" />
                                            </g>
                                        </g>
                                    </g>
                                </svg>

                                <svg width="512px" height="223px" viewBox="0 0 512 223" version="1.1"
                                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                    preserveAspectRatio="xMidYMid" *ngIf=hiper>
                                    <g>
                                        <path
                                            d="M140.554538,0 L87.4900387,0 C64.043846,1.10858841 44.8782319,10.5521938 39.3443354,30.0263634 C36.4587893,40.1825609 34.8707842,51.3367896 32.6144097,61.8648617 C21.1435851,115.397722 10.9723116,170.404009 0,222.867478 L413.120547,222.867478 C445.058547,222.867478 466.987089,216.118457 472.913967,190.770678 C475.666844,178.995318 478.304139,165.65708 480.938418,152.719863 C491.206178,102.277583 501.482984,51.8383178 512,0 L140.554538,0"
                                            fill="#B82126"></path>
                                        <path
                                            d="M353.699639,133.153129 C347.970259,138.762039 331.887435,140.355263 333.532863,126.94789 C334.8992,115.812392 347.022557,113.446149 360.162886,115.054431 C359.186071,121.145224 358.065694,128.879435 353.699639,133.153129 Z M335.600941,89.7164549 C335.053804,92.7884549 334.213522,95.5683137 333.532863,98.5067922 C340.09349,96.8623686 360.52229,91.8186667 362.49098,100.574871 C363.144533,103.484235 362.017129,106.57531 361.197929,108.849192 C342.723765,107.099357 327.666949,110.168345 323.707482,123.327749 C321.055122,132.14218 324.004643,140.816063 329.654714,143.237522 C340.53622,147.898729 353.771922,142.558871 358.35382,135.222212 C357.877961,137.762133 357.4272,140.327153 357.577788,143.495529 L367.144157,143.495529 C367.245553,134.333741 368.578761,126.917773 369.988267,118.673569 C371.189961,111.653145 373.449788,104.702996 373.090384,98.5067922 C372.269176,84.3163608 348.760345,89.3359686 335.600941,89.7164549 Z M431.523639,137.548298 C424.039404,137.708925 420.317867,133.076831 420.1472,125.396831 C419.849035,111.94629 425.749082,97.0029176 437.728878,95.6626824 C443.306667,95.0392471 447.345443,96.3363137 451.432408,97.7317647 C447.680753,112.83978 449.035043,137.172831 431.523639,137.548298 Z M456.344596,66.4465569 C455.391875,74.8011922 454.117898,82.8355765 452.467451,90.4914824 C425.195922,81.8597647 408.470588,101.924141 408.770761,126.688878 C408.829992,131.478588 409.654212,136.228141 412.649914,139.616376 C417.812078,145.455184 432.59382,146.851639 440.055969,141.944471 C441.501616,140.994761 442.97738,139.266008 443.935122,138.066322 C444.653929,137.162792 445.796392,134.799561 446.002196,135.479216 C445.610667,138.104471 445.024376,140.534965 444.969161,143.495529 L455.052549,143.495529 C456.995137,115.618635 463.004612,91.8076235 467.463027,66.4465569 L456.344596,66.4465569 Z M171.67862,133.412141 C165.730384,139.711749 151.107263,139.615373 149.960784,129.015969 C149.460831,124.403953 151.179545,119.568063 152.028863,114.796424 C152.88822,109.966557 153.506635,105.332455 154.354949,101.09189 C160.213835,93.9379451 177.430086,93.0745725 179.17691,104.971043 C180.692831,115.299388 176.604863,128.194761 171.67862,133.412141 Z M180.469961,89.7164549 C171.060204,86.1816471 159.591404,90.4011294 154.619984,94.4148078 C154.637051,94.591498 154.501522,94.6155922 154.354949,94.6286431 C154.440282,94.5573647 154.531639,94.4860863 154.619984,94.4148078 C154.61898,94.399749 154.617976,94.386698 154.613961,94.3706353 C154.69829,92.6438902 155.305663,91.4421961 155.389992,89.7164549 L145.823624,89.7164549 C141.837051,116.239059 137.109584,142.019765 132.120094,167.540455 L143.237522,167.540455 C144.846808,157.600627 145.909961,147.115671 148.150714,137.80731 C150.689631,147.597553 167.268392,145.726243 174.263718,141.944471 C188.699106,134.13898 199.832596,96.9898667 180.469961,89.7164549 Z M233.213992,108.332173 L207.100988,108.332173 C207.927216,102.328722 213.328314,95.7299451 221.837553,95.4046745 C229.846839,95.0984784 235.581239,98.3451608 233.213992,108.332173 Z M222.613584,88.6824157 C214.580204,89.3008314 207.789678,91.6128627 202.963827,96.6967216 C197.044706,102.933082 192.25098,116.719937 193.655467,129.27498 C195.659294,147.186949 217.961412,146.544439 235.800094,142.202478 C236.103278,139.0592 236.864251,136.371702 237.351153,133.412141 C230.002447,136.161882 217.243608,139.999875 209.686086,135.222212 C203.979796,131.614118 203.946667,122.477427 205.807937,114.537412 C217.796769,114.155922 230.257443,114.229208 242.263341,114.537412 C243.025318,108.909427 245.204831,102.773459 243.29738,97.2147451 C240.781553,89.8760784 231.77738,87.9766588 222.613584,88.6824157 Z M124.880816,89.7164549 C124.579639,89.7596235 124.599718,90.125051 124.621804,90.4914824 C122.172235,108.812047 118.857286,126.268235 115.314447,143.495529 L126.431875,143.495529 C129.10331,125.051482 132.163263,106.996957 135.998243,89.7164549 L124.880816,89.7164549 Z M414.977004,89.9744627 C405.15062,85.0622745 396.970667,93.3054745 393.776188,98.2477804 C394.685741,95.7118745 394.737945,92.3166118 395.585255,89.7164549 L385.759875,89.7164549 C383.111529,108.182588 379.917051,126.103592 376.193506,143.495529 L387.569945,143.495529 C387.642227,136.367686 389.041694,131.09509 390.155043,124.10378 C392.533333,109.178478 396.022965,92.8095373 413.425945,97.7317647 C414.0032,95.206902 414.243137,92.3437176 414.977004,89.9744627 Z M290.095184,130.826039 C289.071184,128.178698 288.805145,123.80662 289.061145,120.483639 C289.636392,113.012455 292.358024,103.916925 296.559435,99.7998431 C302.357082,94.1166431 313.802792,95.0573176 322.931451,98.2477804 C323.214557,95.1707608 323.832973,92.4270431 324.224502,89.4584471 C309.252016,87.0128941 295.043514,88.5338353 287.510086,96.4387137 C280.136282,104.176941 275.301396,121.962416 278.718745,133.153129 C282.719373,146.248282 300.653427,146.960063 315.176157,141.944471 C315.817663,139.312188 316.157992,136.377725 316.727216,133.670149 C308.79222,137.793255 293.621961,139.935624 290.095184,130.826039 Z M283.890949,89.7164549 C274.032439,85.734902 266.283169,92.4631843 262.690133,98.7658039 C263.504314,95.961851 263.840627,92.6780235 264.4992,89.7164549 L254.67382,89.7164549 C252.272439,108.34422 248.731608,125.832533 245.366463,143.495529 L256.484894,143.495529 C258.04298,133.010573 258.715608,118.878369 262.17211,108.849192 C264.934902,100.834886 272.165145,94.0072157 282.597898,97.7317647 C282.744471,94.7752157 283.590776,92.5184 283.890949,89.7164549 Z M104.714039,67.9976157 C103.125835,78.3028706 101.336847,88.4073412 99.5428392,98.5067922 C88.0248471,98.6282667 76.2649098,99.0760157 65.1555137,98.2477804 C67.2537098,88.3681882 68.7585882,77.8932706 70.8427294,67.9976157 L58.4332549,67.9976157 C53.989898,93.2030745 49.9491137,118.809098 44.9887373,143.495529 L57.6572235,143.495529 C59.6449882,130.831059 61.5032471,118.038086 64.1204706,106.005082 C74.9226667,105.739043 87.8692392,105.275231 98.2497882,106.264094 C96.1094275,118.774965 93.5233255,130.83909 91.5275294,143.495529 L104.19702,143.495529 C108.266918,117.91862 112.556675,92.5605647 117.641537,67.9976157 L104.714039,67.9976157 Z M136.257255,79.3740549 C138.473914,77.8480941 141.328063,70.8929255 138.067325,67.9976157 C137.03429,67.0810353 135.305537,66.8149961 132.896125,67.2225882 C130.660392,67.6000627 129.374369,68.3590275 128.500957,69.5486745 C127.095467,71.462149 125.808439,77.2306824 127.982933,79.3740549 C130.101208,81.4612078 134.846745,80.3448471 136.257255,79.3740549 L136.257255,79.3740549 Z"
                                            fill="#FFFFFF"></path>
                                    </g>
                                </svg>-->

                            </div>
                            <div class="ccv">
                                <label>CCV</label>
                                <div>{{CCV}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6">
            <form class="form-group pt-3 pt-3" (submit)="submit()" style="width: 97%;">
                <mat-form-field class="pagamento-full-width">
                    <mat-label>Nome do titular do cartão</mat-label>

                    <input matInput type="text"  placeholder="Ex. João dos Santos"
                        maxlength="50" name="CartaoNome" [(ngModel)]="NomeCartao">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.required && pagamentoForm.get('CartaoNome').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.minlength && pagamentoForm.get('CartaoNome').touched ">
                        Mínimo 5 caracteres
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.maxlength && pagamentoForm.get('CartaoNome').touched ">
                        Máximo 50 caracteres
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.validaNome && pagamentoForm.get('CartaoNome').value.length > 4 && pagamentoForm.get('CartaoNome').touched ">
                        Campo inválido
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNome').errors?.pattern && pagamentoForm.get('CartaoNome').value.length > 4 && pagamentoForm.get('CartaoNome').touched ">
                        Somente letras
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Número do cartão</mat-label>

                    <input matInput formControlName="CartaoNumero" mask="0000-0000-0000-0000"
                        (keypress)="mascaraNumerocartao($any($event.target).value)"
                        (keyup)="mascaraNumerocartao($any($event.target).value)" placeholder="Ex. 0000-0000-0000-0000"
                        name="NumeroCartao">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNumero').errors?.required && pagamentoForm.get('CartaoNumero').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoNumero').errors?.maxlength || pagamentoForm.get('CartaoNumero').errors?.minlength  && pagamentoForm.get('CartaoNumero').touched ">
                        Número inválido
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Validade do cartão</mat-label>

                    <input matInput formControlName="CartaoMesAno" placeholder="Ex. 05/25" mask="00/00"
                        name="NumeroCartao" (keypress)="mascaraDatacartao(CartaoMesAnoInputAno.value!)" #CartaoMesAnoInputAno
                        (keyup)="mascaraDatacartao(CartaoMesAnoInputAno.value)">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoMesAno').errors?.required && pagamentoForm.get('CartaoMesAno').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoMesAno').errors?.maxlength || pagamentoForm.get('CartaoMesAno').errors?.minlength  || pagamentoForm.get('CartaoMesAno').errors?.dataInorreta && pagamentoForm.get('CartaoMesAno').touched ">
                        Vencimento inválido
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Código de segurança do cartão</mat-label>

                    <input id="card-ccv" matInput placeholder="Ex. 000" mask="000" name="CCV"
                        (focus)="focusFunction()" (focusout)="focusOutFunction()" [(ngModel)]="CCV">

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoSeg').errors?.required && pagamentoForm.get('CartaoSeg').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('CartaoSeg').errors?.maxlength || pagamentoForm.get('CartaoSeg').errors?.minlength && pagamentoForm.get('CartaoSeg').touched">
                        Código inválido
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="pagamento-full-width">
                    <mat-label>Descrição do pagamento</mat-label>

                    <input matInput formControlName="FraseFatura" placeholder="Ex. Consulta com o..." maxlength="9">

                    <mat-error
                        *ngIf="pagamentoForm.get('FraseFatura').errors?.required && pagamentoForm.get('FraseFatura').touched ">
                        Campo obrigatório
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('FraseFatura').errors?.minlength && pagamentoForm.get('FraseFatura').touched  ">
                        Mínimo 3 caracteres
                    </mat-error>

                    <mat-error
                        *ngIf="pagamentoForm.get('FraseFatura').errors?.maxlength && pagamentoForm.get('FraseFatura').touched ">
                        Máximo 9 caracteres
                    </mat-error>
                </mat-form-field>

                <div class="pb-3">
                    <span><b>Valor:</b></span>
                    <span class="float-right"><b>{{ valor | currency:'BRL':true }}</b></span>
                </div>

                <!-- <mat-form-field class="pagamento-full-width pb-3">
                    <mat-label>Valor</mat-label>

                    <input matInput formControlName="Valor" currencyMask placeholder="R$ 0,00"
                    [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }">

                    <mat-error
                        *ngIf="pagamentoForm.get('Valor').errors?.required && pagamentoForm.get('Valor').touched ">
                        Campo obrigatório
                    </mat-error>
                </mat-form-field>    -->
                <!-- <div class="pb-3">
                    <span><b>Salvar cartão </b></span>
                    <mat-slide-toggle class="botao" style="font-size: 12px" [(ngModel)]='flgSalvaCartao'>                    
                </mat-slide-toggle>
                </div> -->
                <!-- <button [disabled]="progress" ejs-progressbutton [duration]='7000' cssClass='e-hide-spinner'
                    [enableProgress]='progress' tmDarkenOnHover
                    style="border-radius: .3rem !important; background-color: #2573c2 !important;" type="submit"
                    class="btn btn-primary btn-block">PAGAR</button> -->
            </form>
        </div>

    </div>

</ngx-smart-modal>