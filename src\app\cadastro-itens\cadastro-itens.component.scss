/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-icon {
  background-color: $primary-light;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-icon .material-icons {
  color: $primary-color;
  font-size: 24px;
}

.header-title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
}

/* FILTROS */
.filtros {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.busca-container {
  flex-grow: 1;
  max-width: 100%;
}

.busca-field {
  width: 100%;
}

.busca-field ::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

.busca-field ::ng-deep .mat-form-field-flex {
  background-color: $bg-color;
}

.busca-field ::ng-deep .mat-form-field-outline {
  color: $border-color;
}

.btn-busca {
  color: $primary-color;
  background-color: transparent;
}

.adicionar-container {
  display: flex;
  justify-content: flex-end;
}



.btn-adicionar:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* LISTA DE ITENS */
.lista-container {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 8px;
  margin-bottom: 24px;
}

.lista-scroll {
  max-height: 57vh !important;
  overflow-y: auto;
  padding-right: 8px;
}

.lista-scroll::-webkit-scrollbar {
  width: 6px;
}

.lista-scroll::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.lista-scroll::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.item-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid transparent;
}

.item-card:hover {
  transform: translateY(-2px);
  box-shadow: $box-shadow;
  border-left: 4px solid $primary-color;
}

.item-info {
  display: flex;
  align-items: center;
  flex: 2;
  min-width: 280px;
}

.item-avatar {
  margin-right: 16px;
}

.img-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid $primary-light;
}

.item-detalhes {
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item mat-icon {
  font-size: 18px;
  color: $primary-color;
  margin-right: 8px;
}

.info-item .tipo {
  color: $text-secondary;
}

.info-item .nome {
  font-weight: 600;
  color: $text-primary;
}

.item-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 180px;
  margin: 0 16px;
}

.data-item {
  margin-bottom: 8px;
}

.data-item:last-child {
  margin-bottom: 0;
}

.data-label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: $primary-color;
  margin-bottom: 2px;
}

.data-valor {
  color: $text-secondary;
}

.item-acoes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-width: 100px;
  justify-content: flex-end;
}

.item-acoes button {
  width: 36px;
  height: 36px;
  background-color: $primary-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition;
  &:last-child {
    color: #FF6B6B !important;
    background-color: rgba(255, 107, 107, 0.05) !important;
    mat-icon{
      color: #ff6b6b;
      &:hover{
        color: #ff6b6b;
      }
    }
  }
}

.item-acoes button:hover {
  background-color: $primary-color;
  transform: scale(1.1);
}

.item-acoes button:hover mat-icon {
  color: white;
}

.item-acoes mat-icon {
  color: $primary-color;
  font-size: 20px;
  transition: color $transition;
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: $text-secondary;
}

.lista-vazia mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* MODAL DE CONFIRMAÇÃO */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: $border-radius;
  width: 90%;
  max-width: 400px;
  overflow: hidden;
  box-shadow: $box-shadow;
}

.modal-header {
  background-color: $primary-color;
  color: white;
  padding: 16px;
  text-align: center;
}

.modal-header h3 {
  margin: 0;
  font-weight: 500;
  font-size: 18px;
}

.modal-body {
  padding: 24px;
  text-align: center;
}

.modal-text {
  font-size: 16px;
  color: $text-primary;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px;
  background-color: #f9fafb;
}

.btn-cancelar {
  background-color: #f3f4f6;
  color: $text-secondary;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition;
}

.btn-cancelar:hover {
  background-color: #e5e7eb;
}

.btn-confirmar {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition;
}

.btn-confirmar:hover {
  background-color: $primary-dark;
}

.btn-excluir {
  background-color: $error-color;
}

.btn-excluir:hover {
  background-color: darken($error-color, 10%);
}

/* RESPONSIVIDADE */
@media (max-width: 1024px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .busca-container, .adicionar-container {
    width: 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }
  
  .adicionar-container {
    margin-bottom: 0;
  }
  
  .btn-adicionar {
    width: 100%;
    justify-content: center;
  }
  
  .item-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .item-info, .item-data, .item-acoes {
    width: 100%;
    margin: 0;
    margin-bottom: 16px;
  }
  
  .item-acoes {
    margin-bottom: 0;
    justify-content: flex-start;
  }
}