<div class="modal-container">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">Chat <PERSON>no</h3>
            <span *ngIf="flgApenasVisualizacao" class="view-only-badge">(Apenas mensagens importantes)</span>
        </div>
    </header>

    <main class="modal-container" #chatContent>
        <div class="messages-container">
            @for (message of sortedMessages; track message.IdAnaliseMensagem) {
                <div class="message-wrapper" [ngClass]="{
                    'my-message': message.idPessoa === idPessoaLogado,
                    'other-message': message.idPessoa !== idPessoaLogado
                }">
                    <div class="message" [ngClass]="{ 'important': message.flgMensagemImportante }">
                        <div class="message-user">{{ message.nomeMedico || 'Usuário' }}</div>
                        <div class="message-text">{{ message.mensagem }}</div>
                        <div class="message-timestamp">
                            {{ message.dtHoraEnvio | date:'dd/MM/yyyy HH:mm' }}
                        </div>
                    </div>
                </div>
            }

            @if (sortedMessages.length === 0) {
                <div class="empty-state">
                    <mat-icon class="empty-icon">chat</mat-icon>
                    <p>Nenhuma mensagem encontrada.</p>
                </div>
            }
        </div>
    </main>

    <footer *ngIf="!flgApenasVisualizacao" class="modal-footer">
        <div class="message-input-container">
            <mat-form-field appearance="outline" class="message-input">
                <mat-label>Digite sua mensagem</mat-label>
                <textarea matInput [(ngModel)]="objetoMensagem.mensagem" 
                    placeholder="Digite sua mensagem" maxlength="300" rows="2"></textarea>
                <mat-hint align="end">{{ objetoMensagem.mensagem?.length || 0 }}/300</mat-hint>
            </mat-form-field>
        </div>
        
        <div class="message-actions">
            <mat-checkbox [(ngModel)]="objetoMensagem.flgMensagemImportante" 
                class="important-checkbox" color="primary">
                Marcar como importante
            </mat-checkbox>
            
            <button class="action-button send-button" 
                [disabled]="!objetoMensagem.mensagem?.trim()" 
                (click)="sendMessage()">
                <mat-icon>send</mat-icon>
                <span>Enviar</span>
            </button>
        </div>
    </footer>

    <footer *ngIf="flgApenasVisualizacao" class="modal-footer view-only-footer">
        <button class="action-button close-chat-button" (click)="close()">
            <mat-icon>cancel</mat-icon>
            <span>Fechar</span>
        </button>
    </footer>
</div>