// sair-consulta-dialog.component.scss
.modal-header {
    padding: 20px;
}

.little-title {
    font-weight: 700;
    margin: 0;
    padding: 0;
}

.fw-700 {
    font-weight: 700;
}

.row-button {
    padding: 20px;
    text-align: center;
}

.btn-primary {
    // Adicione seus estilos do botão primário aqui
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;

    &:hover {
        background-color: #0056b3;
    }
}

.p-t-20 {
    padding-top: 20px;
}

.p-b-20 {
    padding-bottom: 20px;
}

// medicos-zero-dialog.component.scss
// (mesmo conteúdo que o arquivo acima)

// consulta-agora-dialog.component.scss
.background-email {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.img-responsive {
    width: 100%;
    height: auto;
    max-width: 100%;
}

.modal-info {
    padding: 20px;
    text-align: center;
}

.p-20 {
    padding: 20px;
}

// Reutilizar os estilos comuns dos outros componentes
.modal-header {
    padding: 20px;
}

.little-title {
    font-weight: 700;
    margin: 0;
    padding: 0;
}

.fw-700 {
    font-weight: 700;
}

.row-button {
    padding: 20px;
    text-align: center;
}

.btn-primary {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;

    &:hover {
        background-color: #0056b3;
    }
}
