<div class="main-container report-card">
    <!-- Header Section -->
    <div class="white-container">
      <div class="report-header">
        <div class="header-content">
          <mat-icon class="header-icon">assessment</mat-icon>
          <span class="header-title">Gerar Relatorios Clinica</span>
          <span class="clinic-name">{{nomeClinica | truncate :25}}</span>
        </div>
      </div>
    </div>

  <mat-divider class="p-t-20"></mat-divider>
  <mat-card-content style="margin-bottom: 10px;">
    <div class="row  justify-content-center" style=" margin-top: 20px; font-size: 20px;">
      <div class="col-md-12 row">
        <div class="col-md-3"></div>
        <div class="col-md-3 col-sm-12" style="padding: 5px" id="buscar-locais">
          <div style="display: flex; justify-content: space-evenly; align-items: center; gap: 1em; width: 100%;">
            <div class="form-group">
              <label for="exampleInput" style="font-size: small; padding-left: 1em; color: gray;">Data Inicial</label>
              <input type="date" class="form-control" id="exampleInput" placeholder="Digite algo"
                [(ngModel)]="strDtInicio" (change)="dtChangeInicio()">
            </div>

            <div class="form-group">
              <label for="exampleInput" style="font-size: small; padding-left: 1em; color: gray;">Data Final</label>
              <input type="date" class="form-control" id="exampleInput" placeholder="Digite algo" [(ngModel)]="strDtFim"
                (change)="dtChangeFim()">
            </div>
            <div class="col-md-12 col-sm-12 text-content col-xs-6" style="text-align: center;">
              <button class="btn-primary button_custom btn-gerar" mat-raised-button (click)="GerarRelatorio()">
                <mat-icon style=" margin-bottom: -6px;">note_add</mat-icon> <i class="no-mobile">Gerar Relatorio</i>
              </button>
            </div>
          </div>
          <p style="font-size: smaller; color: red; text-align: center;" *ngIf="flgDiferencaDatas">
            A diferença entre a data de inicio e a data de fim não pode ser inferior a 6 meses.
          </p>
          <p style="font-size: smaller; color: red; text-align: center;" *ngIf="flgDtInicioNaoPreenchida">
            A data de inicio é obrigatória.
          </p>
          <p style="font-size: smaller; color: red; text-align: center;" *ngIf="flgDtFimNaoPreenchida">
            A data final é obrigatória.
          </p>
          <p style="font-size: smaller; color: red; text-align: center;" *ngIf="flgDtFimMaiorQueInicio">
            A data final não pode ser anterior a data incial.
          </p>
        </div>



      </div>
      <div class="col-md-4 justify-content-center div-consultas">
        <mat-checkbox [(ngModel)]="Consultas" (change)="checkConsultasChange()" class="Title-relatorio-clinica">
          Consultas</mat-checkbox>
      </div>

      <div class="col-md-4 justify-content-center div-medicos">
        <mat-checkbox [(ngModel)]="CadastrosMedicos " (change)="checkCadastrosMedicos()"
          class="Title-relatorio-clinica">
          Cadastro Medicos
        </mat-checkbox>
      </div>

      <div class="col-md-4 justify-content-center div-pacientes">
        <mat-checkbox [(ngModel)]="CadastrosPacientes" (change)="checkCadastrosPacientes()"
          class="Title-relatorio-clinica">
          Cadastro Pacientes
        </mat-checkbox>
      </div>
    </div>
  </mat-card-content>

  <mat-divider class="p-t-20"></mat-divider>


  <mat-card-content id="matCardCheckbox" class="col-md-12 itens-selecao" style="max-height: 250px; overflow: auto; display: flex;">
    <div class="col-md-4 col-sm-12 col-xs-12  justify-content-center " style="padding:10px ;">
      <!-- <h5 class="text-left"><b></b></h5> -->
      <div class="col-md-12" style="margin-bottom: 1%;"><b class="Title-config">Consultas</b></div>
      <div class=" row itens-consultas">
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgDtaConsultas">
          Data da consultas</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgPaciente">
          Paciente</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgMedico">Médico
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgTipoAgendamento">Tipo agendamento
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgTempo">Tempo
          consulta</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgTipoPagamento">
          Pagamento</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgConvenio">
          Convênio</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgCodConvenio">
          Código do Convenio</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgValorConsulta">Valor da Consulta
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgRetorno">
          Retorno Convênio</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgCid">
          CID da Consulta</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgStatus">
          Status</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgObservacaoCancelamento">
          Observação de Cancelamento</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemConsultasChange()"
          [(ngModel)]="relatorioConsulta.flgObservacaoAvaliacao">
          Observação de Avaliação</mat-checkbox>
      </div>
    </div>

    <mat-divider></mat-divider>

    <div class="col-md-4 col-sm-12 col-xs-12 justify-content-center " style="padding:10px ;">
      <div class="col-md-12" style="margin-bottom: 1%;"><b class="Title-config">Cadastro Medicos</b>
      </div>

      <div class="row itens-medicos">
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgNome">
          Nome</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgEmail">Email
        </mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgDtanascimento">Data de
          nascimento</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgDtaCadastro">Data de
          cadastro</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgSexo">
          Sexo</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgCpf">
          Cpf</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgTel">
          Telefone</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgTelmovel">Telefone movel
        </mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgTelComercial">Telefone
          comercial</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgCRM">
          CRM</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgRua">
          Rua</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgNumero">N°</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgComplement">Complemento
        </mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgUf">UF
        </mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgCidade">Cidade
        </mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgBairro">Bairro
        </mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgCep">
          CEP</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgValorConsulta">Valor da
          Consulta</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgCodConvenio">Cod.
          Convenio</mat-checkbox>
        <mat-checkbox class=" col-md-12 itens-mobile" (change)="checkItemCadastrosMedicos()"
          [(ngModel)]="relatorioCadastrosMedico.flgvlRetidoClinica">Valor
          Retido</mat-checkbox>
      </div>
    </div>

    <mat-divider></mat-divider>

    <div class="col-md-4 col-sm-12 col-xs-12 justify-content-center " style="padding:10px ;">
      <div class="col-md-12" style="margin-bottom: 1%;"> <b class="Title-config">Cadastro Pacientes</b></div>

      <div class="row itens-pacientes">
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgNome">Nome
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgEmail">Email
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgDtanascimento">Data de
          nascimento</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgDtaCadastro">Data de
          cadastro</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgSexo">Sexo
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgCpf">
          Cpf</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgTel">
          Telefone</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgTelmovel">Telefone movel
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgTelComercial">Telefone
          comercial</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgRua">
          Rua</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgNumero">N°
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgComplement">Complemento
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgUf">
          UF</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgCidade">Cidade
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgBairro">Bairro
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgCep">
          CEP</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgContaAtiva">Cadastro
          Ativo</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgObsInativacao">Obs.
          Inativação</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgNaturalidade">
          Naturalidade</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgNascionalidade">
          Nascionalidade</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgProcedencia">Procedência
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgProfissao">Profissão
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgPlanoSaude">Plano de
          Saúde</mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgConvenio">Convênio
        </mat-checkbox>
        <mat-checkbox class="col-md-12 itens-mobile" (change)="checkItemCadastroPaciente()"
          [(ngModel)]="relatorioCadastrosPaciente.flgMatricula">Matrícula
        </mat-checkbox>
      </div>
    </div>

    <mat-divider></mat-divider>

  </mat-card-content>


  <mat-divider class="p-t-20"></mat-divider>
  <!--  -->

  <mat-card-content style="margin-bottom: 10px;">
    <div class="col-12" style="margin-top: 15px; display: flex; justify-content: space-between;">

      <div class="col-md-6 col-sm-12 div-nomepadrao" style="padding: 5px;" id="buscar-locais2">
        <mat-form-field appearance="outline" style="width: 100%;">
          <input matInput placeholder="Nome do Padão" name="nomePadrao" id="nomePadrao" [(ngModel)]="nomePadrao">
        </mat-form-field>
      </div>

      <div class="col-md-3 col-sm-12 div-btn-padrao" style="align-self: center; text-align: end; padding: 5px;">
        <button class="btn-primary button_custom" mat-raised-button style="color:white;" (click)="NovoPadrao()">
          <mat-icon style=" margin-bottom: -6px;">add</mat-icon> <i class="no-mobile">
            Novo Padrao</i>
        </button>
      </div>

      <div class="col-md-3 col-sm-12 div-btn-salvar" style="align-self: center; padding: 5px; text-align: end;">
        <button class="btn-primary button_custom" mat-raised-button style="float:right;color:white;"
          (click)="BotaoSalvarPadrao()">
          <mat-icon style=" margin-bottom: -6px;">save</mat-icon> <i class="no-mobile">
            Salvar Padrao</i>
        </button>
      </div>
    </div>
    <div class="component-container"  >
      <mat-card style="box-shadow: none;">
        <mat-card-content class="white-container">
            <div class="table-container">
              <div class="patterns-grid">
                <div class="pattern-card"  (click)="EditarPadrao(item.idPadraoRelatorioUsuario)" *ngFor="let item of listaTodosPadroes">
                  <div class="pattern-card-content">
                    <div class="pattern-info">
                      <h4 class="pattern-name">{{item.nomePadraoRelatorio}}</h4>
                    </div>
                    <div class="pattern-actions">
                      <button class="pattern-button delete-button" title="Excluir" (click)="$event.stopPropagation(); AbrirModalExcluir(item.idPadraoRelatorioUsuario)">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </mat-card-content>
      </mat-card>
    
      <!-- Estado vazio (mostrar quando não houver padrões) -->
      <div class="empty-patterns" *ngIf="listaTodosPadroes.length === 0">
        <mat-icon class="empty-patterns-icon">layers</mat-icon>
        <p class="empty-patterns-text">Nenhum padrão salvo. Crie um novo padrão para facilitar seus relatórios.</p>
      </div>
    </div>



<div class="col-md-12 col-sm-12 col-xs-12 ">
  <!-- *ngFor="let item of DadosClinicasUser" TESTAR TABLE SCROLLER RESPONSIVO-->
  <table class="table no-mobile" id="DatatableCliente" *ngFor="let item of DadosClinicasUser">
    <thead style="display: none;">
      <tr>
        <th class="">Nome</th>
        <th class="">Tipo Usuario</th>
        <th class="text-center">Ações</th>
      </tr>
    </thead>
      <tr class="card_table">
        <td id="paciente" style="width: 45%">
          <div class="row">

            <div class="col-12 text-left">
              <mat-icon class="material-icons icon-clinica">domain
              </mat-icon>
              <b class="content-clinica">{{item.nomeClinica}}</b><br>
              <mat-icon class="material-icons icon-clinica">phone
              </mat-icon>
              <b class="content-clinica" style="font-size: 12px;vertical-align: text-bottom;">{{item.tel}}</b><br>
              <mat-icon class="material-icons icon-clinica">mail
              </mat-icon>
              <b class=" content-clinica">
                {{item.email}}</b>
            </div>
          </div>


        </td>

        <td class="" id="data" style="width: 45%">
          <div class=" row" style="margin-left: auto">
            <div class="col-12 text-left" style="padding: unset">
              <b class="Title-b content-clinica">Data Cadastro</b>
              <small class="label_paciente content-clinica">{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</small>
            </div>
          </div>

          <!-- Cadastro Medicos Options -->
          <div class="option-column" *ngIf="CadastrosMedicos">
            <div class="option-header">
              <span class="option-title">Cadastro Medicos</span>
            </div>
            <div class="option-list">
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgNome">Nome</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgEmail">Email</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgDtanascimento">Data de
                nascimento</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgDtaCadastro">Data de
                cadastro</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgSexo">Sexo</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgCpf">Cpf</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgTel">Telefone</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgTelmovel">Telefone
                movel</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgTelComercial">Telefone
                comercial</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgCRM">CRM</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgRua">Rua</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgNumero">N°</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosMedico.flgComplement">Complemento</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgUf">UF</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgCidade">Cidade</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgBairro">Bairro</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgCep">CEP</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgValorConsulta">Valor da
                Consulta</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgCodConvenio">Cod.
                Convenio</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosMedico.flgvlRetidoClinica">Valor
                Retido</mat-checkbox>
            </div>
          </div>

          <!-- Cadastro Pacientes Options -->
          <div class="option-column" *ngIf="CadastrosPacientes">
            <div class="option-header">
              <span class="option-title">Cadastro Pacientes</span>
            </div>
            <div class="option-list">
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgNome">Nome</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgEmail">Email</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgDtanascimento">Data de
                nascimento</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgDtaCadastro">Data de
                cadastro</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgSexo">Sexo</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgCpf">Cpf</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgTel">Telefone</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgTelmovel">Telefone
                movel</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgTelComercial">Telefone
                comercial</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgRua">Rua</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgNumero">N°</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgComplement">Complemento</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgUf">UF</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgCidade">Cidade</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgBairro">Bairro</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgCep">CEP</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgContaAtiva">Cadastro
                Ativo</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgObsInativacao">Obs.
                Inativação</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgNaturalidade">Naturalidade</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgNascionalidade">Nascionalidade</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgProcedencia">Procedência</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgProfissao">Profissão</mat-checkbox>
              <mat-checkbox class="option-item" [(ngModel)]="relatorioCadastrosPaciente.flgPlanoSaude">Plano de
                Saúde</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgConvenio">Convênio</mat-checkbox>
              <mat-checkbox class="option-item"
                [(ngModel)]="relatorioCadastrosPaciente.flgMatricula">Matrícula</mat-checkbox>
            </div>
          </div>
        </td>
      </tr>
  </table>
</div>

<div class="modal-actions">
  <button class="ok-button modal-button" mat-raised-button (click)="modalOpcoes.close()">
    <mat-icon>check</mat-icon> Confirmar
  </button>
</div>

<!-- Outros modais existentes -->
<ngx-smart-modal #trocaClinica identifier="trocaClinica"
  customClass="nsm-centered medium-modal emailmodal clinic_modal nsm-dialog-animation-btt Button-sheet">
  <!-- Modal content (kept from original) -->
</ngx-smart-modal>

<ngx-smart-modal #ExcluirPadraoRelatorio identifier="ExcluirPadraoRelatorio"
  customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3 class="modal-title">Deseja excluir esse Padrão de Relatório?</h3>
    </div>
    <div class="modal-actions">
      <button class="delete-button modal-button" mat-raised-button (click)="ExcluirPadrao()">Excluir</button>
      <button class="cancel-button modal-button" mat-raised-button
        (click)="ngxSmartModalService.getModal('ExcluirPadraoRelatorio').close()">Fechar</button>
    </div>
  </div>
</ngx-smart-modal>

<ngx-smart-modal #EdicaoRelatorio identifier="EdicaoRelatorio" customClass="nsm-centered medium-modal emailmodal"
  [dismissable]=false>
  <div class="modal-container">
    <div class="modal-header">
      <h3 class="modal-title">Deseja editar este padrão?</h3>
    </div>
    <div class="modal-actions">
      <button class="edit-button modal-button" mat-raised-button (click)="SalvarPadrao(); EdicaoRelatorio.close()">
        Sim, editar
      </button>
      <button class="new-button modal-button" mat-raised-button (click)="ModalSalvarPadrao()">
        Padrão novo
      </button>
    </div>
  </div>
</ngx-smart-modal>

<ngx-smart-modal #GeracaoEnviada identifier="GeracaoEnviada" customClass="nsm-centered medium-modal emailmodal"
  [dismissable]=false>
  <div class="modal-container success-modal">
    <div class="modal-header success-header">
      <h3 class="modal-title">Seu relatório foi solicitado, logo ele será gerado!</h3>
    </div>
    <div class="modal-actions">
      <button class="ok-button modal-button" mat-raised-button (click)="GeracaoEnviada.close()">OK</button>
    </div>
  </div>
</ngx-smart-modal>

