.modal-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    font-family: 'Roboto', sans-serif;
}

// Modal Header
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1vmax 2vmax;
    border-bottom: 1px solid #e0e0e0;

    .modal-title {
        font-weight: 700;
        color: #279EFF;
        margin: 0;
        font-size: 1.5rem;
    }

    .close-btn {
        color: #666;
        transition: color 0.2s ease;

        &:hover {
            color: #ff4081;
        }
    }
}

// Modal Body
.modal-corpo {
    padding: 2vmax;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1.5vmax;
}

// Form Styling
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5vmax;
    align-items: center;

    .form-group {
        padding: 0 0.5vmax;
        margin-bottom: 1vmax;
    }
}

.form-select {
    width: 100%;
    font-size: 0.9rem;
}

.info-button-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    .info-btn {
        color: #1265b9;
    }
}

.form-actions {
    display: flex;
    justify-content: center;
    margin: 2vmax 0;

    .action-btn {
        padding: 0.6vmax 2vmax;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 0.5vmax;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        mat-icon {
            font-size: 1.2rem;
            height: 1.2rem;
            width: 1.2rem;
            line-height: 1.2rem;
        }
    }
}

// Waiting List Section
.waiting-list-container {
    margin-top: 1vmax;

    .section-title {
        font-size: 1.1rem;
        font-weight: 500;
        color: #333;
        margin-bottom: 1vmax;
    }

    .waiting-list-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 0.5vmax;

        .patient-row {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;

            &:hover {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }

            td {
                padding: 1vmax;
                border: none;

                &.patient-info {
                    width: 90%;
                }

                &.action-buttons {
                    width: 10%;
                    white-space: nowrap;
                    text-align: right;
                }
            }
        }
    }
}

// Info Grid in Patient Row
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.5vmax 1vmax;

    .info-cell {
        display: flex;
        align-items: baseline;
        gap: 0.5vmax;

        .info-label {
            font-weight: 600;
            color: #555;
            white-space: nowrap;
        }

        .info-value {
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
}

// Responsive Adjustments
@media screen and (max-width: 768px) {

    .form-group.col-md-5,
    .form-group.col-md-6 {
        width: 100%;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-button-wrapper {
        position: absolute;
        right: 1vmax;
        top: 8.5vmax;
    }
}

