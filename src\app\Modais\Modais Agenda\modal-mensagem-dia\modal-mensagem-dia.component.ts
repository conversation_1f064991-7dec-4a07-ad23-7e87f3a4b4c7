import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { RecadoDia } from 'src/app/model/medico';
import { MedicoService } from 'src/app/service/medico.service';
import { SpinnerService } from 'src/app/service/spinner.service';

@Component({
  selector: 'app-modal-mensagem-dia',
  standalone: true,
  imports: [
    MatDialogModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatButtonModule,
    TranslateModule,
    CommonModule
  ],
  templateUrl: './modal-mensagem-dia.component.html',
  styleUrl: './modal-mensagem-dia.component.scss'
})
export class ModalMensagemDiaComponent {
  DesRecadoDia: string = '';
  DiaRecado: string = '';
  NomeMedico: string = '';
  DadosRecados: any[] = [];
  campoDesRecadoVazio: boolean = false;
  viewDate: Date = new Date();
  IdMedico?: number | null;

  constructor(
    public dialogRef: MatDialogRef<ModalMensagemDiaComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    // private translate: TranslateService,
    private snackBarAlert: AlertComponent,
    private usuarioLogadoService : UsuarioLogadoService,
    private medicoService: MedicoService,
    private spinner: SpinnerService
  ) {
    this.DiaRecado = data.diaRecado;
    this.NomeMedico = data.nomeMedico;
    this.DadosRecados = data.dadosRecados;
  }

  SalvarMensagem() {
    try {
      if (this.DesRecadoDia == null || !this.DesRecadoDia.trim()) {
        this.snackBarAlert.falhaSnackbar('Preencha o campo da mensagem')
        this.campoDesRecadoVazio = true;
        this.DesRecadoDia = "";
        return
      }

      var mens = new RecadoDia
      mens.DesRecado = this.DesRecadoDia;
      mens.IdMedico = this.IdMedico!;
      mens.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
      mens.DtaDiaRecado = this.viewDate;
      mens.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
      mens.FlgVisualizado = false;
      this.medicoService.salvarRecadoDia(mens).subscribe((retorno) => {

        if (retorno) {
          this.snackBarAlert.sucessoSnackbar('Recado criado com sucesso.');
          this.DesRecadoDia = "";
          this.campoDesRecadoVazio = false;
          this.carregarMensagemDia();
        }
        this.spinner.hide();

      }, err => {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar")
        console.error(err)
        this.spinner.hide();
      })


    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão")
      console.error(error)
      this.spinner.hide();
    }
  }
  
  carregarMensagemDia() {
    // var idMedicoAcesso = this.ListaMedicos.filter((c:any) => c.idMedico == this.IdMedico)
    // var idAcesso = idMedicoAcesso[0].idUsuarioacesso;
    // // var dtaHj = new Date(this.viewDate).toLocaleDateString();
    // this.medicoService.getRecadoDia(idAcesso, this.usuarioLogadoService.getIdUltimaClinica(), 'Todos', new Date(this.viewDate).toDateString()).subscribe((retorno) => {
    //   if (retorno) {


    //     this.DadosRecados = retorno;
    //     this.ngxSmartModalService.getModal('mensagemDiaAgenda').open();
    //   }
    //   this.spinner.hide();
    // }, err => {

    //   this.snackBarAlert.falhaSnackbar('Erro ao Carregar Agenda de Espera')
    //   console.error(err)
    //   this.spinner.hide();
    // })

  }

  onNoClick(): void {
    this.dialogRef.close();
  }
}
