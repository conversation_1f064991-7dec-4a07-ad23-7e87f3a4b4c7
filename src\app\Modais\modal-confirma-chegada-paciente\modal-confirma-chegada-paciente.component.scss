/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$arrived-color: #3949AB; /* Azul para "chegou" */
$waiting-color: #3D8B40; /* Verde para "aguardando" */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.3s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo do modal */
.modal-content {
    flex: 1;
    padding: 24px;
    background-color: $secondary-light;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Container das opções */
.options-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    width: 100%;
    height: 100%;
    padding: 16px;
}

/* Cards de opção */
.option-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 24px;
    width: 45%;
    height: 80%;
    min-height: 300px;
    padding: 24px;
    border-radius: $border-radius;
    background-color: $secondary-dark;
    cursor: pointer;
    transition: all $transition ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity $transition ease;
    z-index: 1;
}

.option-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.option-card:hover::before {
    opacity: 1;
}

/* Opção chegou */
.arrived-option {
    background-color: $arrived-color;
}

/* Opção aguardando */
.waiting-option {
    background-color: $waiting-color;
}

/* Container de ícone */
.icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    background-color: $secondary-light;
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transition: all $transition ease;
    z-index: 2;
}

.option-card:hover .icon-container {
    transform: scale(1.1);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Ícone */
.option-icon {
    font-size: 60px;
    height: 60px;
    width: 60px;
    transition: all $transition ease;
}

.arrived-option .option-icon {
    color: $arrived-color;
}

.waiting-option .option-icon {
    color: $waiting-color;
}

/* Rótulo da opção */
.option-label {
    font-size: 18px;
    font-weight: 500;
    color: white;
    text-align: center;
    max-width: 80%;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Responsividade */
@media (max-width: 768px) {
    .options-container {
        flex-direction: column;
        gap: 16px;
    }
    
    .option-card {
        width: 90%;
        height: auto;
        min-height: 200px;
        padding: 16px;
    }
    
    .icon-container {
        width: 80px;
        height: 80px;
    }
    
    .option-icon {
        font-size: 40px;
        height: 40px;
        width: 40px;
    }
    
    .option-label {
        font-size: 16px;
    }
}