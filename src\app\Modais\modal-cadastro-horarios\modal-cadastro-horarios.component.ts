import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { ModalCancelarHorarioComponent } from 'src/app/agenda-consulta/modais/modal-cancelar-horario/modal-cancelar-horario.component';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { Consulta } from 'src/app/model/consulta';
import { AgendaService } from 'src/app/service/agenda.service';
import { MedicoService } from 'src/app/service/medico.service';
import { PacienteService } from 'src/app/service/pacientes.service';
import { SpinnerService } from 'src/app/service/spinner.service';

@Component({
  selector: 'app-modal-cadastro-horarios',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatButtonModule,
    TranslateModule,
    MatRadioModule,
    MatSelectModule,
    MatCheckboxModule
  ],
  templateUrl: './modal-cadastro-horarios.component.html',
  styleUrl: './modal-cadastro-horarios.component.scss'
})

export class ModalCadastroHorariosComponent implements OnInit {
  DataAtual = new Date();
  idPaciente: number | null = null;
  idMedico: number | null = null;
  idEspecialidade: number | null = null;
  idTipoAgendamento: number | null = null;
  idAgendamento: number | null = null;
  dataInicio: Date | string | null = null;
  dataFim: Date | string | null = null;
  valorConsulta: number | null = null;
  flgRetorno: boolean = false;
  flgTimeOff: boolean = false;
  observacao: string = '';
  tipoConsulta: boolean = true;
  lsPacientes: any[] = [];
  lsMedicos: any[] = [];
  lsEspecialidades: any[] = [];
  lsTipoAgendamento: any[] = [];
  flgEdicao: boolean = false;
  tiposConsulta = [
    { valor: false, label: 'Telemedicina' },
    { valor: true, label: 'Presencial' }
  ];
  idTipoUsuarioLogado: number = 1;

  constructor(
    private matDialogRef: MatDialogRef<ModalCadastroHorariosComponent>,
    @Inject(MAT_DIALOG_DATA) public dados: ObjetoModalCadastroHorario,
    private usuarioLogadoService: UsuarioLogadoService,
    private pacientesService: PacienteService,
    private spinner: SpinnerService,
    private medicoService: MedicoService,
    private agendaService: AgendaService,
    private snackBar: AlertComponent,
    private matDialog: MatDialog
  ) {
    

    this.idTipoUsuarioLogado = this.usuarioLogadoService.getIdTipoUsuario()!;
    if (this.idTipoUsuarioLogado == 2)
      this.idMedico = this.usuarioLogadoService.getIdMedico()!;
  }

  ngOnInit(): void {

    this.carregarDados();
    this.configurarValoresIniciais();
  }

  carregarDados() {
    this.carregarPacientes();
    this.carregarMedicos();
    this.carregarTiposAgendamento();
  }

  configurarValoresIniciais() {
    if (this.dados.IdMedico) {
      this.idMedico = Number(this.dados.IdMedico);
      this.carregarEspecialidadesMedico(this.dados.IdMedico);
    }

    if (this.dados.IdAgendamento) {
      this.carregarAgendamentoPorId();
      this.idAgendamento = this.dados.IdAgendamento!
      this.flgEdicao = true;
    }

    if (this.dados.IdPaciente)
      this.idPaciente = this.dados.IdPaciente

    if (this.dados.dtaAgendamento)
      this.dataInicio = this.formatarDataParaInput(this.dados.dtaAgendamento);
  }

  carregarAgendamentoPorId() {
    this.agendaService.getAgendaEdit(this.dados.IdAgendamento!).subscribe((retorno) => {
      if (retorno != null) {
        let agenda = retorno.agenda.agenda;

        if (agenda.flgInativo == true) {
          this.spinner.hide();
          this.snackBar.falhaSnackbar("Não é possivel editar um agendamento que está Cancelado")
          this.matDialogRef.close(false);
        }
        if (agenda.dtaFinalizacaoAtendimento != null && agenda.flgPeriodoOff != true) {
          this.spinner.hide();
          this.snackBar.falhaSnackbar("Não é possivel editar um agendamento finalizado")
          this.matDialogRef.close(false);
        }
        if (agenda.flgAndamento == true) {
          this.spinner.hide();
          this.snackBar.falhaSnackbar("Não é possivel editar um agendamento que está em andamento")
          this.matDialogRef.close(false);
        }

        this.idMedico = agenda.idMedico;
        this.idEspecialidade = agenda.idEspecialidade;
        this.idPaciente = agenda.idPaciente;
        this.idTipoAgendamento = agenda.idTipoAgendamento;
        this.dataFim = agenda.dtaFinalizacaoAtendimento;
        this.valorConsulta = agenda.valorConsulta;
        this.flgRetorno = agenda.flgRetorno;
        this.flgTimeOff = agenda.flgPeriodoOff
        this.dataInicio = this.formatarDataParaInput(agenda.dtaConsulta);
        if (agenda.dtaFinalizacaoAtendimento)
          this.dataFim = this.formatarDataParaInput(agenda.dtaFinalizacaoAtendimento);
        this.observacao = agenda.desAnotacao;
        this.tipoConsulta = agenda.flgProntuario;
        this.idAgendamento = agenda.idConsulta;

      }
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  carregarPacientes() {
    this.spinner.show();
    this.pacientesService.GetPacienteAgenda('', this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe(
        (retorno: any) => {
          this.lsPacientes = retorno.filter((c: any) => !c.flgInativo);
          this.spinner.hide();
        },
        erro => {
          console.error(erro);
          this.spinner.hide();
        }
      );
  }


  carregarMedicos() {
    this.spinner.show();
    this.medicoService.getMedicos(null, this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe(
        retorno => {
          this.lsMedicos = retorno;
          this.spinner.hide();
        },
        erro => {
          console.error(erro);
          this.spinner.hide();
        }
      );
  }

  carregarTiposAgendamento() {
    this.agendaService.getTipoAgendamento(this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe(
        retorno => {
          this.lsTipoAgendamento = retorno;
          this.spinner.hide();
        },
        erro => {
          console.error(erro);
          this.spinner.hide();
        }
      );
  }

  carregarEspecialidadesMedico(idMedico: number) {
    if (!idMedico) {
      this.lsEspecialidades = [];
      return;
    }

    this.spinner.show();
    this.medicoService.getEspecialidadeMedicos(idMedico)
      .subscribe(
        retorno => {
          this.lsEspecialidades = retorno;
          this.spinner.hide();
        },
        erro => {
          console.error(erro);
          this.spinner.hide();
        }
      );
  }

  validarDatas(): boolean {
    if (!this.dataInicio) {
      return false;
    }

    if (this.dataInicio < this.DataAtual)
      return false;

    if (this.flgTimeOff && !this.dataFim) {
      return false;
    }

    if (this.flgTimeOff) {
      const dataInicioObj = new Date(this.dataInicio);
      const dataFimObj = new Date(this.dataFim!);
      const dataAtual = new Date();

      return (
        dataInicioObj >= dataAtual &&
        dataFimObj >= dataInicioObj
      );
    }

    return true;
  }

  validarCamposObrigatorios(): boolean {
    // Médico sempre é obrigatório
    if (!this.idMedico)
      return false;

    if (this.flgTimeOff)
      return !!(this.dataInicio && this.dataFim);

    return !!(
      this.idPaciente &&
      this.dataInicio &&
      this.tipoConsulta !== null
    );
  }


  salvarAgendamento() {
    try {
      this.spinner.show();

      if (!this.validarCamposObrigatorios()) {
        this.snackBar.falhaSnackbar('Por favor, preencha todos os campos obrigatórios.');
        this.spinner.hide();
        return;
      }

      if (!this.validarDatas()) {
        this.snackBar.falhaSnackbar('Verifique as datas informadas.');
        this.spinner.hide();
        return;
      }

      let dt = new Date();

      if (new Date(this.dataInicio!) < dt) {
        this.spinner.hide();
        this.snackBar.falhaSnackbar('Data de início não pode ser menor que a data atual.');
        return;
      }

      const agenda = new Consulta();

      if (this.dados?.IdAgendamento)
        agenda.IdConsulta = this.dados.IdAgendamento;

      agenda.idClinica = this.usuarioLogadoService.getIdUltimaClinica();

      agenda.IdMedico = this.idMedico!;

      if (!this.flgTimeOff) {
        if (this.idPaciente)
          agenda.IdPaciente = this.idPaciente;

        if (this.idEspecialidade)
          agenda.IdEspecialidade = this.idEspecialidade;

        if (this.idTipoAgendamento)
          agenda.idTipoAgendamento = this.idTipoAgendamento;

        agenda.FlgRetorno = this.flgRetorno;

        agenda.ValorConsulta = this.valorConsulta?.toString();

        agenda.flgProntuario = this.tipoConsulta;
      }

      agenda.DtaConsulta = new Date(this.dataInicio!);
      agenda.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
      agenda.DtaCadastro = new Date();
      agenda.FlgInativo = false;
      agenda.DesAnotacao = this.observacao;

      if (this.flgTimeOff) {
        agenda.DtaFinalizacaoAtendimento = new Date(this.dataFim!);
        agenda.FlgPeriodoOff = true;
      }

      this.agendaService.salvarAgendamento(agenda).subscribe(
        (retorno) => {
          if (!retorno)
            this.snackBar.falhaSnackbar("Falha ao salvar consulta!");

          else {
            this.snackBar.sucessoSnackbar("Consulta salva com sucesso!");
            this.fecharModal(true);
            this.agendaService.atualizaDadosMes$.emit()
          }
          this.spinner.hide();
        },
        (erro) => {
          console.error(erro);
          this.snackBar.falhaSnackbar("Falha ao salvar a consulta");
          this.spinner.hide();
        }
      );
    } catch (error) {
      console.error(error);
      this.snackBar.falhaSnackbar("Falha na conexão");
      this.spinner.hide();
    }
  }


  fecharModal(flgSalvo: boolean = false) {
    this.matDialogRef.close(flgSalvo);
  }

  converterInputParaData(input: string): Date {
    return new Date(input);
  }

  formatarDataParaInput(data: Date | string | null): string {
    if (!data) return '';

    const dataObj = data instanceof Date ? data : new Date(data);

    // Ajustando para o fuso horário local
    const ano = dataObj.getFullYear();
    const mes = String(dataObj.getMonth() + 1).padStart(2, '0');
    const dia = String(dataObj.getDate()).padStart(2, '0');
    const hora = String(dataObj.getHours()).padStart(2, '0');
    const minuto = String(dataObj.getMinutes()).padStart(2, '0');

    return `${ano}-${mes}-${dia}T${hora}:${minuto}`;
  }

  excluirAgendamento() {
    let dialogRef = this.matDialog.open(ModalCancelarHorarioComponent, {
      width: '40vmax',
      data: { idCancelamento: this.idAgendamento }
    });

    dialogRef.afterClosed().subscribe(ret => {
      if (ret)
        this.matDialog.closeAll();
    })
  }

}

export class ObjetoModalCadastroHorario {
  IdAgendamento: number | null = null;
  IdMedico: number | null = null;
  IdPaciente: number | null = null;
  dtaAgendamento: Date | null = null;
}
