<div class="modal-container">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">Exportar dados {{NomePaciente}}</h3>
        </div>
    </header>

    <main class="modal-content">
        <!-- Instruções baseadas na tela atual -->
        <div class="instructions">
            <mat-icon *ngIf="TelaExibida == EnumTelas.PreencherCampos">date_range</mat-icon>
            <mat-icon *ngIf="TelaExibida == EnumTelas.ConfirmarConsultasListadas">list</mat-icon>
            <mat-icon *ngIf="TelaExibida == EnumTelas.DownloadDocumento">download</mat-icon>
            
            <p *ngIf="TelaExibida == EnumTelas.PreencherCampos">
                Preencha o intervalo das datas para localizar as consultas.
            </p>
            <p *ngIf="TelaExibida == EnumTelas.ConfirmarConsultasListadas">
                Selecione as consultas que estarão no PDF.
            </p>
            <p *ngIf="TelaExibida == EnumTelas.DownloadDocumento">
                Seu documento está pronto para download.
            </p>
        </div>

        <!-- Tela 1: Seleção de datas -->
        <div class="date-selection-panel" *ngIf="TelaExibida == EnumTelas.PreencherCampos">
            <form [formGroup]="form" class="date-form">
                <div class="form-group" [ngClass]="{'has-error': form.controls.startDate.invalid && form.controls.startDate.touched}">
                    <label for="startDate">Data Inicial</label>
                    <input type="date" id="startDate" class="date-input" formControlName="startDate" required>
                    <div class="error-message" *ngIf="form.controls.startDate.invalid && form.controls.startDate.touched">
                        A data inicial é obrigatória.
                    </div>
                </div>

                <div class="form-group" [ngClass]="{'has-error': form.errors?.dateRangeInvalid}">
                    <label for="endDate">Data Final</label>
                    <input type="date" id="endDate" class="date-input" formControlName="endDate">
                    <div class="error-message" *ngIf="form.errors?.dateRangeInvalid">
                        A data final não pode ser menor que a data inicial.
                    </div>
                </div>
            </form>
        </div>

        <!-- Tela 2: Lista de consultas -->
        <div class="appointment-list-panel" *ngIf="TelaExibida == EnumTelas.ConfirmarConsultasListadas">
            <div class="empty-state" *ngIf="listaConsultas.length < 1">
                <mat-icon>event_busy</mat-icon>
                <h5>Não há consultas nesse período</h5>
            </div>

            <div class="appointment-list" *ngIf="listaConsultas.length > 0">
                <div class="appointment-item" *ngFor="let item of listaConsultas" (click)="item.flgChecked = !item.flgChecked"
                    [ngClass]="{'selected': item.flgChecked}">
                    <div class="checkbox-container">
                        <input type="checkbox" [checked]="item.flgChecked">
                    </div>
                    <div class="appointment-details">
                        <div class="doctor">
                            <span class="label">Doutor:</span>
                            <span class="value">{{item.descConsulta}}</span>
                        </div>
                        <div class="date">
                            <span class="label">Data:</span>
                            <span class="value">{{formatDateString(item.dtConsulta!)}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tela 3: Download do documento -->
        <div class="download-panel" *ngIf="TelaExibida == EnumTelas.DownloadDocumento">
            <button class="download-button" (click)="downloadPdf()">
                <mat-icon>download</mat-icon>
                <span>Baixar documento</span>
            </button>
        </div>
    </main>

    <footer class="modal-footer" *ngIf="TelaExibida != EnumTelas.DownloadDocumento">
        <!-- Botão voltar (apenas na tela de confirmar consultas quando não há consultas) -->
        <button class="action-button back-button" 
            *ngIf="TelaExibida == EnumTelas.ConfirmarConsultasListadas && listaConsultas.length < 1" 
            (click)="voltaDatas()">
            <mat-icon>arrow_back</mat-icon>
            <span>Voltar</span>
        </button>

        <!-- Botão cancelar (em todas as telas) -->
        <button class="action-button cancel-button" (click)="fecharModal()">
            <mat-icon>cancel</mat-icon>
            <span>Cancelar</span>
        </button>

        <!-- Botão avançar (na tela de datas) -->
        <button class="action-button next-button" 
            *ngIf="TelaExibida == EnumTelas.PreencherCampos" 
            (click)="ListarConsultas()">
            <span>Avançar</span>
            <mat-icon>arrow_forward</mat-icon>
        </button>

        <!-- Botão confirmar (na tela de consultas quando há consultas) -->
        <button class="action-button confirm-button" 
            *ngIf="TelaExibida == EnumTelas.ConfirmarConsultasListadas && listaConsultas.length >= 1" 
            (click)="GerarPdf()">
            <span>Confirmar</span>
            <mat-icon>check</mat-icon>
        </button>
    </footer>
</div>