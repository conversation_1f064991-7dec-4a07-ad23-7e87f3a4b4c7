<div class="modal-mensagem-dia">
    <div class="modal-header">
        <h5 class="modal-title">Mensagem do dia</h5>

        <button type="button" class="btn-close" aria-label="Close" (click)="fecharModalMensagemDoDia()">x</button>
    </div>
    <div class="form-group">
        <label for="selectMedico" class="form-label fw-bold mb-2">Selecione o médico</label><br>
        <select id="selectMedico" class="form-select form-select-lg shadow-sm rounded border-secondary"
            style="border: solid thin black" [(ngModel)]="IdMedico" (ngModelChange)="onMedicoChange()"
            [disabled]="IdTipoUsuario == 2">
            <option *ngFor="let medico of lsMedicos" [value]="medico.idMedico">
                {{ medico.nome }}
            </option>
        </select>
    </div>
    <div class="modal-body">
        <form>
            <div class="form-group mb-3">
                <label class="form-label">Digite a Mensagem</label>
                <textarea class="form-control" rows="4" [(ngModel)]="DesRecadoDia" name="mensagem" required></textarea>
                <small class="text-muted d-block text-start mt-1"> {{DesRecadoDia.length}}/500</small>
            </div>

            <div class="form-group mb-3">
                <label class="form-label">Data de envio</label>
                <input type="datetime-local" class="form-control" [(ngModel)]="dataEnvio" name="dataEnvio" required
                    placeholder="dd/mm/aaaa --:--">
            </div>

            <div class="text-center mt-4">
                <button type="button" class="btn btn-primary btn-enviar" (click)="SalvarMensagem()">
                    Enviar mensagem
                </button>
            </div>
        </form>
    </div>
</div>