import { ControlValueAccessor } from "@angular/forms";

export abstract class ModelChangeBase implements ControlValueAccessor {

    /**
     * Holds the current value of the slider
     */
    private _val = "";
    set value(val: any) {
        if (this._val !== val) {
            this._val = val;
            this.updateChanges();
        }
    }
    get value(): any {
        return this._val;
    }

    constructor() { }

    /**
     * Invoked when the model has been changed
     */
    onChange: (_: any) => void = (_: any) => { };

    /**
     * Invoked when the model has been touched
     */
    onTouched: () => void = () => { };

    /**
     * Method that is invoked on an update of a model.
     */
    updateChanges() {
        this.onChange(this.value);
    }

    ///////////////
    // OVERRIDES //
    ///////////////

    /**
     * Writes a new item to the element.
     * @param value the value
     */
    writeValue(value: any): void {
        if (this.value !== value) {
            this.value = value;
        }
    }

    /**
     * Registers a callback function that should be called when the control's value changes in the UI.
     * @param fn
     */
    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    /**
     * Registers a callback function that should be called when the control receives a blur event.
     * @param fn
     */
    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

}