export const <PERSON><PERSON>raPraNumeroMapper = {
    'zero': '0',
    'um': '1',
    'dois': '2',
    'três': '3',
    'quatro': '4',
    'cinco': '5',
    'seis': '6',
    'meia': '6',
    'sete': '7',
    'oito': '8',
    'nove': '9'
};

export const MesesParaNumerosMapper = {
    'janeiro': '01',
    'fevereiro': '02',
    'março': '03',
    'abril': '04',
    'maio': '05',
    'junho': '06',
    'julho': '07',
    'agosto': '08',
    'setembro': '09',
    'outubro': '10',
    'novembro': '11',
    'dezembro': '12'
};

export const OpcoesDeTempoMapper = {
    'menos de um dia': 'Menos de 1 dia',
    'menos de 1 dia': 'Menos de 1 dia',
    'hoje': 'Menos de 1 dia',
    'um dia': '1-3 dias',
    'dois dias': '1-3 dias',
    'três dias': '1-3 dias',
    'uma semana': '4-7 dias',
    'sete dias': '4-7 dias',
    'duas semanas': '1-2 semanas',
    'quinze dias': '1-2 semanas',
    'mais de duas semanas': 'Mais de 2 semanas',
    'mais de quinze dias': 'Mais de 2 semanas',
    'um mês': 'Mais de 2 semanas',
    'muito tempo': 'Mais de 2 semanas'
};


export interface StepConfig {
    field: string;
    required: boolean;
    label: string;
}

export const StepItensLista = [
    { field: 'nome', required: true, label: 'Nome' },
    { field: 'cpf', required: true, label: 'CPF' },
    { field: 'dataNascimento', required: true, label: 'Nascimento' },
    { field: 'email', required: true, label: 'Email' },
    { field: 'telefone', required: true, label: 'Telefone' },
    { field: 'sintomas', required: true, label: 'Sintomas' },
    { field: 'intensidadeDor', required: true, label: 'Intensidade' },
    { field: 'tempoSintomas', required: true, label: 'Tempo' },
    { field: 'alergias', required: false, label: 'Alergias' },
    { field: 'observacoes', required: false, label: 'Observações' }
];

export const IntensidadeDorLista = [
    { valor: 1, descricao: '1 - Muito leve' },
    { valor: 2, descricao: '2 - Leve' },
    { valor: 3, descricao: '3 - Moderada' },
    { valor: 4, descricao: '4 - Intensa' },
    { valor: 5, descricao: '5 - Muito intensa' }
];

export const DuracaoSintomasOpcoesLista = [
    { valor: 'Menos de 1 dia', icone: 'today', descricao: 'Sintomas iniciaram hoje' },
    { valor: '1-3 dias', icone: 'calendar_view_day', descricao: 'Últimos dias' },
    { valor: '4-7 dias', icone: 'date_range', descricao: 'Uma semana aproximadamente' },
    { valor: '1-2 semanas', icone: 'calendar_view_week', descricao: 'Algumas semanas' },
    { valor: 'Mais de 2 semanas', icone: 'calendar_month', descricao: 'Mais tempo' }
];

export const OpcoesSintomasLista = [
    { nome: 'Dor de cabeça', icone: 'psychology' },
    { nome: 'Febre', icone: 'device_thermostat' },
    { nome: 'Tosse', icone: 'sick' },
    { nome: 'Dor de garganta', icone: 'record_voice_over' },
    { nome: 'Náusea', icone: 'sentiment_very_dissatisfied' },
    { nome: 'Dor abdominal', icone: 'emergency' },
    { nome: 'Fadiga', icone: 'battery_2_bar' },
    { nome: 'Dificuldade para respirar', icone: 'air' },
    { nome: 'Dor muscular', icone: 'fitness_center' },
    { nome: 'Tontura', icone: 'refresh' }
];

// #region Interfaces
export interface QuestionarioPreConsulta {
    nome: string;
    cpf: string;
    email: string;
    telefone: string;
    dataNascimento: string;
    alergias?: string;
    sintomas: string[];
    sintomasOutros?: string;
    intensidadeDor: number;
    tempoSintomas: string;
    observacoes?: string;
}
// #endregion



export const CamposSteps = [
    {
        id: 1,
        title: 'Qual é o seu nome completo?',
        icon: 'person',
        subTitulo: 'Precisamos saber como te chamar durante o atendimento',
        field: 'nome',
        type: 'text',
        placeholder: 'Digite seu nome completo',
        required: true,
        voiceHelp: 'Você pode falar seu nome clicando no botão do microfone ou digitá-lo no campo acima',
        label: 'Nome'
    },
    {
        id: 2,
        title: 'Qual é o seu CPF?',
        icon: 'badge',
        subTitulo: 'Precisamos do seu CPF para identificação no sistema',
        field: 'cpf',
        type: 'text',
        placeholder: '000.000.000-00',
        required: true,
        voiceHelp: 'Fale os números do seu CPF pausadamente ou digite no campo acima',
        label: 'CPF'
    },
    {
        id: 3,
        title: 'Qual é a sua data de nascimento?',
        icon: 'cake',
        subTitulo: 'Precisamos da sua idade para personalizar o atendimento',
        field: 'dataNascimento',
        type: 'date',
        placeholder: '',
        required: true,
        voiceHelp: 'Você pode falar a data (ex: "15 de março de 1990") ou selecioná-la no campo acima',
        label: 'Data de Nascimento'
    },
    {
        id: 4,
        title: 'Qual é o seu email?',
        icon: 'email',
        subTitulo: 'Usaremos para enviar informações importantes sobre sua consulta',
        field: 'email',
        type: 'email',
        placeholder: '<EMAIL>',
        required: true,
        voiceHelp: 'Fale seu email pausadamente (ex: "joão ponto silva arroba gmail ponto com")',
        label: 'Email'
    },
    {
        id: 5,
        title: 'Qual é o seu telefone?',
        icon: 'phone',
        subTitulo: 'Para contato direto em caso de necessidade',
        field: 'telefone',
        type: 'text',
        placeholder: '(00) 00000-0000',
        required: true,
        voiceHelp: 'Fale os números do seu telefone pausadamente, incluindo o DDD',
        label: 'Telefone'
    },
    {
        id: 6,
        title: 'Quais sintomas você está sentindo?',
        icon: 'medical_services',
        subTitulo: 'Descreva detalhadamente os sintomas que você está sentindo',
        field: 'sintomas',
        type: 'voice-text',
        placeholder: 'Descreva seus sintomas detalhadamente',
        required: true,
        voiceHelp: 'Use o microfone para descrever todos os sintomas que você está sentindo',
        label: 'Sintomas'
    },
    {
        id: 7,
        title: 'Qual a intensidade da sua dor?',
        icon: 'personal_injury',
        subTitulo: 'Classifique sua dor ou desconforto de 1 a 5',
        field: 'intensidadeDor',
        type: 'intensity',
        placeholder: '',
        required: true,
        voiceHelp: 'Fale um número de 1 a 5 para classificar a intensidade da sua dor',
        label: 'Intensidade'
    },
    {
        id: 8,
        title: 'Há quanto tempo você tem estes sintomas?',
        icon: 'schedule',
        subTitulo: 'Descreva há quanto tempo você sente estes sintomas',
        field: 'tempoSintomas',
        type: 'voice-text',
        placeholder: 'Ex: há 3 dias, desde ontem, há uma semana...',
        required: true,
        voiceHelp: 'Use o microfone para dizer há quanto tempo você tem estes sintomas',
        label: 'Duração Sintomas'
    },
    {
        id: 9,
        title: 'Você tem alguma alergia?',
        icon: 'medical_information',
        subTitulo: 'Informe sobre alergias medicamentosas, alimentares ou outras (opcional)',
        field: 'alergias',
        type: 'text',
        placeholder: 'Descreva alergias medicamentosas, alimentares ou outras',
        required: false,
        voiceHelp: 'Se você tem alguma alergia, descreva-a usando o microfone ou digitando. Caso não tenha, pode pular esta etapa',
        label: 'Alergias'
    },
    {
        id: 10,
        title: 'Informações adicionais',
        icon: 'description',
        subTitulo: 'Alguma informação complementar que considera importante? (opcional)',
        field: 'observacoes',
        type: 'text',
        placeholder: 'Medicamentos em uso, histórico médico relevante, etc.',
        required: false,
        voiceHelp: 'Compartilhe qualquer informação adicional que possa ser relevante para sua consulta',
        label: 'Observações'
    }
];

export interface WebhookAiQuestionarioPayload {
    formaDeResposta: string;
    historicoConversa: string[];
    ultimaMensagem: string;
    campoAtual: string;
    token: string;
}

export interface WebhookAiQuestionarioResponse {
    flgFinalizar: boolean;
    flgValidacaoCampoAtual: boolean;
    textoResposta: string;
    campoAtual: string;
    dados: QuestionarioPreConsultaDados;
}

export interface QuestionarioPreConsultaDados {
    nome: string;
    cpf: string;
    email: string;
    telefone: string;
    dataNascimento: string;
    alergias: string;
    sintomas: string;
    intensidadeDor: string;
    tempoSintomas: string;
    doencasPrevias: string;
    observacoes: string;
}