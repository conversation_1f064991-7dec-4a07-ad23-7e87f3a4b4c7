import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { CidService } from '../service/cid.service';
import { Cid } from './cid';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { PesquisaCidService } from './pesquisa-cid.service';
import { SpinnerService } from '../service/spinner.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-pesquisa-cid',
    templateUrl: './pesquisa-cid.component.html',
    styleUrls: ['./pesquisa-cid.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      FormsModule,
      ReactiveFormsModule,
      CommonModule,
      NgxSmartModalModule,
      MatIcon,
      MatCardModule,
      MatFormFieldModule,
      TranslateModule
    ]
})
export class PesquisaCidComponent implements OnInit {


  constructor(
    private spinner: SpinnerService,
    private cidService: CidService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private pesquisaCidService: PesquisaCidService,
  ) {
  }

  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;


  inicio: number = 0;
  qntPesquisa: number = 20;
  bOcultaCarregaMais = false;


  pesquisaPorCod: string = '';
  pesquisaPorDes: string = '';
  listaCid: any = [];

  desCidEdit?: string;
  codCidEdit?: string;
  idCidEdit?: number | null;

  idCidExclusao?: number | null;

  ngOnInit() {

    this.pesquisaCidService
      .getModalPesquisaCid()
      .subscribe(() => {
        this.CarregaTabela();
        this.ngxSmartModalService.getModal('PesquisaCid').open();
      });

  }

  CarregaTabela() {
    this.cidService.GetListaCid(this.inicio, this.qntPesquisa).subscribe((retorno) => {
      
      this.listaCid = retorno;
      

      if (retorno.length < this.qntPesquisa)
        this.bOcultaCarregaMais = true;
      else
        this.bOcultaCarregaMais = false;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    });
  }

  CarregarMais() {
    if (this.pesquisaPorCod.trim().length > 0 && this.pesquisaPorDes.trim().length > 0) {
      this.snackBarAlert.falhaSnackbar("erro ao buscar CID, ambos os campos de busca estão preenchidos");
      return;
    }
    if (this.pesquisaPorCod != undefined && this.pesquisaPorCod.trim().length > 0) {
      this.cidService.GetListaFiltaPorCod(this.listaCid.length, this.qntPesquisa, this.pesquisaPorCod).subscribe((retorno) => {
        
        var listaRet = retorno;
        
        for (let index = 0; index < listaRet.length; index++) {
          this.listaCid.push(listaRet[index]);
        }
        if (retorno.length < this.qntPesquisa)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    } else if (this.pesquisaPorDes != undefined && this.pesquisaPorDes.trim().length > 0) {
      this.cidService.GetListaFiltaPorDes(this.listaCid.length, this.qntPesquisa, this.pesquisaPorDes).subscribe((retorno) => {
        
        var listaRet = retorno;
        
        for (let index = 0; index < listaRet.length; index++) {
          this.listaCid.push(listaRet[index]);
        }
        if (retorno.length < this.qntPesquisa)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    } else {

      this.cidService.GetListaCid(this.listaCid.length, this.qntPesquisa).subscribe((retorno) => {
        
        var listaRet = retorno;
        
        for (let index = 0; index < listaRet.length; index++) {
          this.listaCid.push(listaRet[index]);
        }
        if (retorno.length < this.qntPesquisa)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      });
    }
  }

  FiltrarPorCod() {
    if (this.pesquisaPorCod == undefined || !this.pesquisaPorCod.trim()) {
      this.snackBarAlert.falhaSnackbar("Campo de busca vazio");
      return
    }
    else {
      this.pesquisaPorDes = ""

      this.cidService.GetListaFiltaPorCod(this.inicio, this.qntPesquisa, this.pesquisaPorCod).subscribe((retorno) => {
        
        this.listaCid = retorno;
        
        this.pesquisaPorDes = "";

        if (retorno.length < this.qntPesquisa)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      });
    }
  }



  FiltrarPorDes() {
    if (this.pesquisaPorDes == undefined || !this.pesquisaPorDes.trim()) {
      this.snackBarAlert.falhaSnackbar("Campo de busca vazio");
      return
    }
    else {
      this.pesquisaPorCod = ""
      this.cidService.GetListaFiltaPorDes(this.inicio, this.qntPesquisa, this.pesquisaPorDes).subscribe((retorno) => {
        
        this.listaCid = retorno;
        
        this.pesquisaPorCod = "";

        if (retorno.length < this.qntPesquisa)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      });
    }
  }

  ValidaPesquisa() {
    if (!this.pesquisaPorCod.trim() && !this.pesquisaPorDes.trim()) {
      this.LimparBusca()
      document.documentElement.scrollTop = 0;

    }
  }


  LimparBusca() {

    this.pesquisaPorDes = ""
    this.pesquisaPorCod = ""
    this.CarregaTabela();

  }



  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }





  CadastrarCid() {
    this.idCidEdit = null
    this.desCidEdit = ""
    this.codCidEdit = ""

    this.ngxSmartModalService.getModal('CadastraCid').open();
  }

  ModalEditar(idCid:any) {
    
    this.idCidEdit = idCid;
    var cidEdit = this.listaCid.filter((c:any) => c.idCid == idCid);
    this.desCidEdit = cidEdit[0].desCid
    this.codCidEdit = cidEdit[0].codCid

    this.ngxSmartModalService.getModal('CadastraCid').open();
  }

  SalvarCid() {
    
    var cid = new Cid();
    cid.desCid = this.desCidEdit;
    cid.codCid = this.codCidEdit;
    cid.idCid = this.idCidEdit;
    this.cidService.SalvarCid(cid).subscribe(() => {
      this.desCidEdit = "";
      this.codCidEdit = "";
      this.idCidEdit = null;
      this.pesquisaPorDes = ""
      this.pesquisaPorCod = ""
      this.CarregaTabela()
      this.ngxSmartModalService.getModal('CadastraCid').close();
      this.spinner.hide();
    });

  }

  SelecionarCIV(codCid:any) {
    this.cidService.EnviarCid(codCid);
    this.spinner.hide();
    this.ngxSmartModalService.getModal('PesquisaCid').close();
  }
  ExclusaoCid() {
    this.cidService.ExcluirCid(this.idCidExclusao!).subscribe(() => {
      this.idCidExclusao = null;
      this.CarregaTabela()
      this.ngxSmartModalService.getModal('ExcluirCid').close();
      this.spinner.hide();
    });
  }

  ModalExcluir(idCid:any) {
    
    this.idCidExclusao = idCid;
    this.ngxSmartModalService.getModal('ExcluirCid').open()


  }






}
