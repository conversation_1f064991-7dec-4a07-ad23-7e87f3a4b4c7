.gridPerfil {
    mat-cell,
    mat-footer-cell,
    mat-header-cell {
        flex: none;
        display: inline-flex;
        align-items: center;
        overflow: hidden;
        word-wrap: break-word;
        min-height: inherit;
    }
}

.perfilCelulaFlg {
    width: 12%;
    justify-content: center;
}

@media (max-width: 780px) {
    .mother-card {
        padding: 15px !important;
        margin-right: 15px;
    }
}