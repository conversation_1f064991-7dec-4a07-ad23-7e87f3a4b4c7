/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, $primary-light 0%, rgba(46, 139, 87, 0.05) 100%);
  border-radius: $border-radius;
  border-left: 4px solid $primary-color;
}

.header-icon {
  background-color: $primary-color;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  box-shadow: 0 4px 12px rgba(46, 139, 87, 0.3);
}

.header-icon-material {
  color: white;
  font-size: 28px;
}

.header-content {
  flex: 1;
}

.header-title {
  color: $primary-color;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-subtitle {
  color: $text-secondary;
  font-size: 0.95rem;
  margin: 0;
  font-weight: 400;
}

/* FILTROS E AÇÕES */
.filtros-section {
  margin-bottom: 24px;
}

.filtros-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.busca-container {
  flex: 1;
  min-width: 300px;
}

.acoes-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn-adicionar {
  background-color: $primary-color;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all $transition;
  box-shadow: 0 2px 4px rgba(46, 139, 87, 0.2);
}

.btn-adicionar:hover {
  background-color: $primary-dark;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(46, 139, 87, 0.3);
}

.btn-filtros {
  border-color: $primary-color;
  color: $primary-color;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-filtros:hover {
  background-color: $primary-light;
}

.filtros-avancados {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.filtros-avancados.expanded {
  max-height: 200px;
}

.toggles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  background-color: $bg-color;
  padding: 20px;
  border-radius: $border-radius;
  border: 1px solid $border-color;
}

.toggle-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border: 1px solid transparent;
}

.toggle-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  border-color: $primary-color;
}

.toggle-item mat-icon {
  color: $primary-color;
  font-size: 18px;
}




.toggle-item ::ng-deep .mat-slide-toggle-bar {
  background-color: rgba(0, 0, 0, 0.1);
}

.toggle-item ::ng-deep .mat-slide-toggle-thumb {
  background-color: white;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-bar {
  background-color: rgba(46, 139, 87, 0.5) !important;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-thumb {
  background-color: $primary-color !important;
}

.busca-field {
  width: 100%;
}

.busca-field ::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

.busca-field ::ng-deep .mat-form-field-flex {
  background-color: white;
  border-radius: 8px;
}

.busca-field ::ng-deep .mat-form-field-outline {
  color: $border-color;
}

.btn-busca {
  color: $primary-color;
  background-color: transparent;
}

/* ESTATÍSTICAS */
.estatisticas-container {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.estatistica-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 4px solid $primary-color;
  min-width: 160px;
}

.estatistica-item mat-icon {
  color: $primary-color;
  font-size: 24px;
}

.estatistica-info {
  display: flex;
  flex-direction: column;
}

.estatistica-numero {
  font-size: 1.5rem;
  font-weight: 700;
  color: $primary-color;
  line-height: 1;
}

.estatistica-label {
  font-size: 0.85rem;
  color: $text-secondary;
  font-weight: 500;
}

/* SEÇÃO DE PACIENTES NA FILA */
.pacientes-fila-container {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  overflow: hidden;
}

.fila-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.fila-titulo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.fila-titulo mat-icon {
  font-size: 24px;
}

.btn-atualizar {
  border-color: rgba(255, 255, 255, 0.5);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all $transition;
}

.btn-atualizar:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
}

.fila-scroll {
  max-height: 300px;
  overflow-y: auto;
  padding: 16px;
  background-color: $bg-color;
}

.paciente-fila-card {
  background-color: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid #3b82f6;
}

.paciente-fila-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.paciente-posicao {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.1rem;
}

.paciente-info-fila {
  flex: 1;
}

.paciente-nome {
  font-size: 1.1rem;
  font-weight: 600;
  color: $text-primary;
  margin-bottom: 4px;
}

.paciente-detalhes-fila {
  display: flex;
  gap: 16px;
  font-size: 0.9rem;
  color: $text-secondary;
}

.paciente-idade {
  display: flex;
  align-items: center;
  gap: 4px;
}

.paciente-tempo {
  display: flex;
  align-items: center;
  gap: 4px;
}

.paciente-acoes-fila {
  display: flex;
  gap: 8px;
}

.btn-chamar {
  background-color: #10b981;
  color: white;
  width: 40px;
  height: 40px;
  transition: all $transition;
}

.btn-chamar:hover {
  background-color: #059669;
  transform: scale(1.05);
}

/* LISTA DE MÉDICOS - DESKTOP */
.lista-container {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
  overflow: hidden;
}

.lista-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  color: white;
}

.lista-titulo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.lista-titulo mat-icon {
  font-size: 24px;
}

.lista-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.total-registros {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.lista-scroll {
  max-height: 55vh;
  overflow-y: auto;
  padding: 16px;
  background-color: $bg-color;
}

.lista-scroll::-webkit-scrollbar {
  width: 8px;
}

.lista-scroll::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.lista-scroll::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.medico-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid transparent;
  position: relative;
  overflow: hidden;
}

.medico-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-left: 4px solid $primary-color;
}

.medico-card.card-inativo {
  opacity: 0.7;
  background-color: #f9f9f9;
}

.status-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 100%;
  transition: all $transition;
}

.status-indicator.ativo {
  background-color: #10b981;
}

.status-indicator.inativo {
  background-color: #ef4444;
}

.medico-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 320px;
  gap: 16px;
}

.medico-avatar {
  position: relative;
}

.img-circle {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid $primary-light;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background-color: $primary-color;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.avatar-badge mat-icon {
  color: white;
  font-size: 14px;
}

.medico-icon-avatar {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary-light 0%, rgba(46, 139, 87, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid $primary-light;
}

.avatar-icon {
  font-size: 36px;
  color: $primary-color;
}

.medico-detalhes {
  flex: 1;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  gap: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item.nome-principal {
  margin-bottom: 8px;
}

.info-item mat-icon {
  font-size: 18px;
  color: $primary-color;
  min-width: 18px;
}

.info-item .nome {
  font-weight: 700;
  color: $text-primary;
  font-size: 1.1rem;
}

.status-badge {
  margin-left: auto;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.ativo {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.inativo {
  background-color: #fee2e2;
  color: #991b1b;
}

.medico-dados {
  flex: 1;
  min-width: 280px;
  margin: 0 20px;
}

.dados-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dados-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: $bg-color;
  border-radius: 8px;
  transition: all $transition;
}

.dados-item:hover {
  background-color: $primary-light;
  transform: translateX(4px);
}

.dados-icon {
  color: $primary-color;
  font-size: 20px;
  min-width: 20px;
}

.dados-content {
  flex: 1;
}

.dados-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 600;
  color: $primary-color;
  margin-bottom: 2px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dados-valor {
  color: $text-primary;
  font-weight: 500;
  font-size: 0.9rem;
}

.medico-acoes {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 200px;
  align-items: flex-end;
}

.acoes-principais {
  display: flex;
  gap: 8px;
}

.acoes-secundarias {
  display: flex;
  gap: 6px;
}

.btn-acao {
  width: 44px;
  height: 44px;
  transition: all $transition;
}

.btn-acao.perfil {
  background-color: #3b82f6;
}

.btn-acao.agenda {
  background-color: #10b981;
}

.btn-acao.editar {
  background-color: #f59e0b;
}

.btn-acao:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-email {
  width: 36px;
  height: 36px;
  background-color: $primary-light;
  border-radius: 50%;
  transition: all $transition;
}

.btn-email:hover {
  background-color: $primary-color;
  transform: scale(1.05);
}

.btn-email:hover mat-icon {
  color: white;
}

.btn-email.enviado {
  background-color: #dcfce7;
}

.btn-email.enviado mat-icon {
  color: #166534;
}

.btn-inativar {
  width: 36px;
  height: 36px;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 50%;
  transition: all $transition;
}

.btn-inativar:hover {
  background-color: #ef4444;
  transform: scale(1.05);
}

.btn-inativar:hover mat-icon {
  color: white;
}

.btn-inativar mat-icon {
  color: #ef4444;
}

.btn-ativar {
  width: 36px;
  height: 36px;
  background-color: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  transition: all $transition;
}

.btn-ativar:hover {
  background-color: #10b981;
  transform: scale(1.05);
}

.btn-ativar:hover mat-icon {
  color: white;
}

.btn-ativar mat-icon {
  color: #10b981;
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 32px;
  text-align: center;
  background-color: white;
  border-radius: $border-radius;
  margin: 16px;
}

.vazia-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: $bg-color;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.vazia-icon mat-icon {
  font-size: 40px;
  color: $text-secondary;
  opacity: 0.6;
}

.lista-vazia h3 {
  color: $text-primary;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.lista-vazia p {
  color: $text-secondary;
  font-size: 1rem;
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.5;
}

.btn-adicionar-vazio {
  background-color: $primary-color;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all $transition;
}

.btn-adicionar-vazio:hover {
  background-color: $primary-dark;
  transform: translateY(-1px);
}

/* MOBILE VIEW */
.mobile-view {
  display: none;
}

/* LISTA DE MÉDICOS - MOBILE */
.lista-mobile {
  margin-bottom: 24px;
}

.medico-card-mobile {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.medico-header-mobile {
  display: flex;
  align-items: center;
  padding: 16px;
}

.medico-avatar-mobile {
  margin-right: 16px;
}

.medico-info-mobile {
  flex: 1;
}

.medico-nome-mobile {
  font-size: 18px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
}

.medico-titulo-mobile {
  color: $text-secondary;
  margin: 0;
  font-size: 14px;
}

.medico-dados-mobile {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
}

.dados-mobile-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
}

.dados-mobile-item p {
  color: $text-secondary;
  margin: 0;
  font-size: 14px;
}

.acoes-mobile {
  position: relative;
  padding: 16px;
  text-align: right;
}

.acoes-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  position: absolute;
  right: 80px;
  bottom: 16px;
  max-width: calc(100% - 80px);
}

.acoes-buttons button {
  background-color: $primary-color;
  color: white;
}

.toggle-button {
  background-color: $primary-color;
  color: white;
}

/* BOTÃO CARREGAR MAIS */
.carregar-mais {
  text-align: center;
  margin: 24px 16px 16px 16px;
}

.btn-carregar {
  border-color: $primary-color;
  color: $primary-color;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 500;
  transition: all $transition;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-carregar:hover {
  background-color: $primary-color;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 139, 87, 0.3);
}

/* MODAIS */
.modal-container ::ng-deep .nsm-content {
  border-radius: $border-radius;
  padding: 0;
  overflow: hidden;
  width: 90%;
  max-width: 500px;
}

.modal-content {
  display: flex;
  flex-direction: column;
}

.modal-header {
  background-color: $primary-color;
  color: white;
  padding: 16px 24px;
  text-align: center;
}

.modal-header h3 {
  margin: 0;
  font-weight: 500;
  font-size: 18px;
}

.modal-body {
  padding: 24px;
  text-align: center;
  background-color: #fff;
}

.modal-text {
  font-size: 16px;
  color: $text-primary;
  margin-bottom: 8px;
}

.modal-subtexto {
  font-size: 14px;
  color: $text-secondary;
  margin-bottom: 16px;
}

.alerta-inativo {
  background-color: #FEF2F2;
  border-left: 4px solid $error-color;
  padding: 12px;
  margin-top: 16px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alerta-inativo mat-icon {
  color: $error-color;
  margin-bottom: 8px;
}

.alerta-inativo p {
  color: $error-color;
  font-weight: 500;
}

.alerta-subtexto {
  font-size: 12px;
  margin-top: 4px;
}

.medico-nome-modal {
  font-weight: 600;
  color: $primary-color;
  font-size: 18px;
  margin-top: 8px;
}

.modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
  background-color: #f9fafb;
}

.btn-cancelar {
  background-color: #f3f4f6;
  color: $text-secondary;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-cancelar:hover {
  background-color: #e5e7eb;
}

.btn-confirmar {
  background-color: $primary-color;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-confirmar:hover {
  background-color: $primary-dark;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.btn-excluir {
  background-color: #d85959;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-excluir:hover {
  background-color: #dc2626;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alerta-inativo mat-icon {
  color: $error-color;
  margin-bottom: 8px;
}

.alerta-inativo p {
  color: $error-color;
  font-weight: 500;
}

.alerta-subtexto {
  font-size: 12px;
  margin-top: 4px;
}

.medico-nome-modal {
  font-weight: 600;
  color: $primary-color;
  font-size: 18px;
  margin-top: 8px;
}

.modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
  background-color: #f9fafb;
}

.btn-cancelar {
  background-color: #f3f4f6;
  color: $text-secondary;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-cancelar:hover {
  background-color: #e5e7eb;
}

.btn-confirmar {
  background-color: $primary-color;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-confirmar:hover {
  background-color: $primary-dark;
}

/* ANIMAÇÕES PARA BOTÕES MOBILE */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.acoes-buttons button {
  animation: slideIn 0.2s ease-out forwards;
}

/* RESPONSIVIDADE */
@media (max-width: 1024px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toggles-container, .busca-container, .adicionar-container {
    width: 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }
  
  .adicionar-container {
    margin-bottom: 0;
  }
  
  .btn-adicionar {
    width: 100%;
    justify-content: center;
  }
  
  .medico-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .medico-info, .medico-dados, .medico-acoes {
    width: 100%;
    margin: 0;
    margin-bottom: 16px;
  }
  
  .medico-acoes {
    margin-bottom: 0;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .desktop-view {
    display: none;
  }
  
  .mobile-view {
    display: block;
  }
}