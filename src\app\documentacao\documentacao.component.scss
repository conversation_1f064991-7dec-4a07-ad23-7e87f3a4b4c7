/* Novo container específico para o título */
.modal-title-container {
  flex: 1;
  text-align: center;
  max-width: 80%; /* Evita que o título chegue muito próximo ao botão de fechar */
}/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* <PERSON><PERSON><PERSON> suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Estilos para todos os modais */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

.margin{
  margin-top: 10px;
}

.modern-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-modal .nsm-content {
  border-radius: $border-radius !important;
  overflow: hidden !important;
  padding: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.health-modal .nsm-dialog {
  max-width: 650px !important;
  width: 95% !important;
}

.modal-container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  border-radius: .3rem;
  background-color: $bg-color;
}

.modal-header {
  background-color: $primary-color;
  padding: 16px 20px;
  text-align: center;
  border-bottom: 1px solid $primary-dark;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-title {
  color: white; /* Definindo explicitamente como branco ao invés de usar variável */
  margin: 0;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  font-size: 20px;
  z-index: 10; /* Garantindo que o título esteja acima de outros elementos */
  pointer-events: none; /* Evitando problemas com cliques */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Adicionando sombra para melhorar legibilidade */
}
.modal-container > .modal-container{
  padding: 20px;
  min-width: 100%;
}
.max-witdth{
  max-width: 250px;
}
.close-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: $secondary-light;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color $transition ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.modal-content {
  padding: 20px;
  flex: 1;
  background-color: $secondary-light;
}

.modal-footer {
  display: flex;
  justify-content: center;
  padding: 16px 20px;
  background-color: $secondary-color;
  gap: 12px;
}

.modal-divider {
  height: 4px;
  background-image: linear-gradient(to right, $secondary-light, $primary-color, $primary-color, $secondary-light);
  border-radius: 10px;
  margin: 0;
}

.form-divider {
  height: 1px;
  background-color: $border-color;
  width: 100%;
  margin: 12px 0;
}

/* Estilos dos elementos do formulário */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  width: 100%;
}

.form-column {
  flex: 1;
  min-width: 250px;
  padding: 0 8px;
}

.info-column {
  flex: 0 0 50px;
  min-width: 50px;
}

.form-field {
  width: 100%;
  position: relative;
}

.full-width {
  width: 100%;
}

.form-field.with-icon {
  display: flex;
  align-items: center;
}

.form-label {
  display: block;
  font-size: 14px;
  color: $text-secondary;
  margin-bottom: 8px;
}

.inline-field {
  display: flex;
  align-items: center;
  gap: 8px;
}

.small-input {
  width: 50px;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 6px;
  text-align: center;
}

/* Customização dos elementos Angular Material */
mat-form-field.mat-mdc-form-field {
  width: 100%;
}

mat-form-field.mat-mdc-form-field-appearance-outline .mat-mdc-form-field-flex {
  background-color: $secondary-light;
}

mat-form-field.mat-mdc-form-field-appearance-outline .mat-mdc-form-field-outline {
  color: $border-color;
}

mat-form-field.mat-mdc-form-field-appearance-outline.mat-focused .mat-mdc-form-field-outline {
  color: $primary-color;
}

/* NG-Select Customização */
.custom-select {
  font-size: 14px !important;
}

.ng-select {
  border: none;
}

.ng-select .ng-select-container {
  border: 1px solid $border-color !important;
  border-radius: $border-radius !important;
  background-color: $secondary-light !important;
  min-height: 48px !important;
}

.ng-select.ng-select-focused .ng-select-container {
  border-color: $primary-color !important;
}

/* Botões e Ações */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: $border-radius;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition ease;
  min-width: 120px;
}

.cancel-button {
  background-color: $secondary-dark;
  color: $text-primary;
}

.cancel-button:hover {
  background-color: darken($secondary-dark, 5%);
}

.confirm-button {
  background-color: $primary-color;
  color: $secondary-light;
}

.confirm-button:hover {
  background-color: $primary-dark;
}

.info-button {
  color: $primary-color !important;
  background-color: transparent;
  border: 1px solid $primary-color;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition ease;
}

.info-button:hover:not([disabled]) {
  background-color: $primary-light;
}

.info-button[disabled] {
  color: $secondary-dark !important;
  border-color: $secondary-dark;
  cursor: not-allowed;
}

.info-button.attached-info {
  margin-left: 8px;
}

.search-button {
  color: $primary-color !important;
  background-color: transparent;
}

/* Mensagens de erro */
.error-message {
  color: $error-color;
  font-size: 12px;
  margin-top: 4px;
}

/* Lista de medicamentos */
.medicine-list-container {
  width: 100%;
  max-height: 250px;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  overflow: hidden;
}

.medicine-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 250px;
  overflow-y: auto;
}

.medicine-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
  transition: background-color $transition ease;
  cursor: pointer;
}

.medicine-item:hover {
  background-color: $secondary-color;
}

.medicine-name {
  font-weight: 500;
  color: $text-primary;
  margin-bottom: 4px;
}

.medicine-description {
  color: $text-secondary;
  font-size: 14px;
}

.load-more-item {
  display: flex;
  justify-content: center;
  padding: 12px;
}

.load-more-button {
  background-color: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;
  border-radius: $border-radius;
  padding: 8px 16px;
  cursor: pointer;
  transition: all $transition ease;
}

.load-more-button:hover {
  background-color: $primary-light;
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: $text-secondary;
}

/* Área de texto */
.textarea-field {
  width: 100%;
  min-height: 100px;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  padding: 12px;
  font-family: inherit;
  resize: none;
  color: $text-primary;
}
.textareaDiv{
  height: 90px;

}

.textarea-field:focus {
  outline: none;
  border-color: $primary-color;
}

.textarea-field.consultation {
  min-height: 255px;
}

.textarea-field.general {
  min-height: 100px;
}

/* Checkbox e Toggle */
.checkbox-field {
  margin: 8px 0;
}

mat-checkbox .mat-checkbox-frame,
mat-slide-toggle .mat-slide-toggle-bar {
  border-color: $border-color;
}

mat-checkbox.mat-checkbox-checked .mat-checkbox-background, 
mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: $primary-color !important;
}

/* Contêiner de busca */
.search-container {
  width: 100%;
  position: relative;
}

/* Modal Info */
.receituario{
  width: 900px;
  @media (max-width: 1000px) {
    width: 300px;
    overflow: auto;
  }
}
.info-modal .modal-content {
  background-color: $secondary-light;
}

.modal-logo {
  display: flex;
  justify-content: center;
  padding: 16px;
  background-color: $secondary-light;
}

.logo-image {
  max-width: 300px;
  height: 35px;
}

/* Responsividade */
@media (max-width: 767px) {
  .form-column {
    flex: 0 0 100%;
    padding: 0 4px;
  }
  
  .modal-container {
    min-height: unset;
  }
  
  .action-button {
    min-width: 100px;
    padding: 8px 12px;
  }
  
  .medicine-list-container {
    max-height: 200px;
  }
  
  .info-button.attached-info {
    margin-left: 4px;
  }
}

@media (max-width: 576px) {
  .modal-header {
    padding: 12px;
  }
  
  .modal-content {
    padding: 12px;
  }
  
  .modal-footer {
    padding: 12px;
  }
  
  .action-button {
    min-width: 90px;
    font-size: 13px;
  }
}