import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Agendamento, DiaDoMes, ProcessedAgendamento } from './calendario.models';
import { CalendarService } from './calendar.service';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-calendario',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
  ],
  templateUrl: './calendario.component.html',
  styleUrl: './calendario.component.scss'
})
export class CalendarioComponent implements OnChanges {
  @Input() agendamentos: Agendamento[] = [];

  @Output() horarioClicado = new EventEmitter<{ data: Date, hora: string }>();
  @Output() mesTrocado = new EventEmitter<Date>();
  @Output() diaTrocado = new EventEmitter<Date>();
  @Output() agendamentoClicado = new EventEmitter<Agendamento>();
  TituloCalendario: string = "";
  flgExibirCalendario: boolean = true;

  TIPO_AGENDAMENTO_MAP: { [key: number]: string } = {
    1: 'Consulta',
    2: 'Atendimento',
    3: 'Exame',
    4: 'Vacinação',
    5: 'Cirurgia',
  };

  // ===== CALENDAR PROPRIEDADES =====
  mesAtual: Date = new Date();
  diasDoMes: DiaDoMes[] = [];
  diasSemana: string[] = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  // ===== SCHEDULER PROPRIEDADES =====
  diaSelecionado: Date | null = null;
  horasDoDia: string[] = [];
  eventosProcessados: { [horaKey: string]: ProcessedAgendamento[] } = {};

  intervaloAgenda: number = 60;
  intervalosDisponiveis: { valor: number, label: string }[] = [
    { valor: 60, label: '1 hora' },
    { valor: 30, label: '30 minutos' },
    { valor: 15, label: '15 minutos' },
    { valor: 13, label: '13 minutos' },
  ];

  constructor(private calendarService: CalendarService) {
    this.gerarCalendario();
    this.gerarHorasDoDia();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['agendamentos']) {
      this.gerarCalendario();
      if (this.diaSelecionado) {
        this.processarEventosDoDia();
      }
    }
  }

  // ===== SHARED METHODS =====
  bt_DataAtual() {
    if (this.flgExibirCalendario) {
      this.mesAtual = new Date();
      this.gerarCalendario();
      this.mesTrocado.emit(this.mesAtual);
    } else {
      this.mesAtual = this.diaSelecionado = new Date();
      this.atualizarTituloCalendario();
      this.processarEventosDoDia();
      this.diaTrocado.emit(this.diaSelecionado);
    }
  }

  atualizarTituloCalendario() {
    if (this.flgExibirCalendario) {
      this.TituloCalendario = this.mesAtual.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    } else {
      this.TituloCalendario = this.diaSelecionado ?
        this.diaSelecionado.toLocaleDateString('pt-BR', { day: '2-digit', month: 'long', year: 'numeric' }) : "";
    }
  }

  formatarHora(data: Date): string {
    return this.calendarService.formatarHora(data);
  }

  // ===== CALENDAR METHODS =====
  bt_AlterarMes(flgAvancar: boolean) {
    this.mesAtual.setMonth(this.mesAtual.getMonth() + (flgAvancar ? 1 : -1));
    this.gerarCalendario();
    this.mesTrocado.emit(this.mesAtual);
  }

  gerarCalendario() {
    this.diasDoMes = this.calendarService.gerarCalendario(
      this.mesAtual,
      this.agendamentos
    );
    this.atualizarTituloCalendario();
  }

  getTiposAgendamentoLimitado(agendamentos: { [tipo: number]: { count: number, descricao: string } }): number[] {
    return Object.keys(agendamentos)
      .map(Number)
      .slice(0, 3);
  }

  getAgendamentosExtras(agendamentos: { [tipo: number]: { count: number, descricao: string } }): number {
    return Math.max(0, Object.keys(agendamentos).length - 3);
  }

  getTooltipExtras(agendamentos: { [tipo: number]: { count: number, descricao: string } }): string {
    const extras = Object.keys(agendamentos)
      .map(Number)
      .slice(3)
      .map(tipo => `${agendamentos[tipo].descricao} (${agendamentos[tipo].count})`);
    return extras.join(', ');
  }

  getCorAgendamento(tipo: number): string {
    const agendamentoComCor = this.agendamentos.find(
      a => a.TipoAgendamento === tipo && a.CorEvento
    );

    if (agendamentoComCor && agendamentoComCor.CorEvento) {
      return agendamentoComCor.CorEvento;
    }

    return this.calendarService.gerarCorAleatoria(tipo);
  }

  selecionarDia(dia: DiaDoMes) {
    this.diaSelecionado = dia.data;
    this.flgExibirCalendario = false;
    this.atualizarTituloCalendario();
    this.processarEventosDoDia();
    this.diaTrocado.emit(this.diaSelecionado);
  }

  // ===== SCHEDULER METHODS =====
  bt_AlterarDia(flgAvancar: boolean) {
    if (this.diaSelecionado) {
      this.diaSelecionado.setDate(this.diaSelecionado.getDate() + (flgAvancar ? 1 : -1));
      this.atualizarTituloCalendario();
      this.processarEventosDoDia();
      this.diaTrocado.emit(this.diaSelecionado);
      this.mesAtual = this.diaSelecionado;
    }
  }

  gerarHorasDoDia() {
    this.horasDoDia = this.calendarService.gerarHorasDoDia(this.intervaloAgenda);
  }

  alterarIntervaloAgenda(intervalo: number) {
    this.intervaloAgenda = intervalo;
    this.gerarHorasDoDia();

    const root = document.documentElement;
    root.style.setProperty('--intervalo-minutos', intervalo.toString());

    if (this.diaSelecionado) {
      this.processarEventosDoDia();
    }
  }

  processarEventosDoDia() {
    if (!this.diaSelecionado) return;

    this.eventosProcessados = this.calendarService.processarEventosDoDia(
      this.diaSelecionado,
      this.agendamentos,
      this.horasDoDia,
      this.intervaloAgenda
    );
  }

  getEventosProcessadosPorHora(hora: string): ProcessedAgendamento[] {
    return this.eventosProcessados[hora] || [];
  }

  getEstiloEvento(evento: ProcessedAgendamento): any {
    return this.calendarService.getEstiloEvento(evento);
  }

  onHorarioClicado(hora: string) {
    if (!this.diaSelecionado) return;

    const [horaNumero, minutoNumero] = hora.split(':').map(Number);
    const dataHoraSelecionada = new Date(this.diaSelecionado);
    dataHoraSelecionada.setHours(horaNumero, minutoNumero, 0, 0);

    this.horarioClicado.emit({
      data: dataHoraSelecionada,
      hora: hora
    });
  }

  onEventoClicado(evento: ProcessedAgendamento, event: MouseEvent) {
    event.stopPropagation();

    const agendamentoOriginal = this.agendamentos.find(
      a => a.IdAgendamento === evento.IdAgendamento
    );

    if (agendamentoOriginal) {
      this.agendamentoClicado.emit(agendamentoOriginal);
    }
  }
}