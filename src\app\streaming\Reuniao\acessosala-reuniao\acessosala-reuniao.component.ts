import { AlertComponent } from './../../../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { ValidacaoService } from 'src/app/service/validacao.service';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { ReuniaoService } from 'src/app/service/reuniaoService.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { PagamentoComponent } from 'src/app/pagamento/pagamento.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';

@Component({
    selector: 'app-acessosala-reuniao',
    templateUrl: './acessosala-reuniao.component.html',
    styleUrls: ['./acessosala-reuniao.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      PagamentoComponent,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      RouterModule,
      TranslateModule,
      MatFormFieldModule,
      MatCardModule,
    ]
})
export class AcessosalaReuniaoComponent implements OnInit {

  constructor(    
    private spinner: SpinnerService,

    private reuniaoService: ReuniaoService,
    private router: Router,
    private route: ActivatedRoute,
    public ngxSmartModalService: NgxSmartModalService,    
    public validador: ValidacaoService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private localStorageService: LocalStorageService,

  ) { }


  codAcesso?: string;
  logininvalido?: boolean;
  DivInicial = true;
  idGuid = '';
  mensagemModal = '';
  DivEspera = false;
  flgIniciarConsulta?: boolean;
  ImagemPessoaConsulta = 'assets/build/img/avatar-medico.png';
  flgAntes?: boolean;
  seg?: number;
  min?: number;
  hr?: number;
  segundos: any = '00';
  minutos: any = '00';
  hora: any = '00';
  usuarioConsulta: any = [];
  timeInterval: any;
  ModoLogin = 'Cpf';


  ngOnInit() {


    this.localStorageService.clear();

    this.idGuid = this.route.snapshot.params.idGuid;

    if (!this.idGuid) {
      this.navigateLogin();
      return;
    }

    this.getCodigoAcesso();

  }


  getCodigoAcesso() {

    // this.acessoRapido
    //   .getCodigoAcesso(this.idGuid)
    //   .subscribe(ret => {
    //     if (!ret) {
    //       this.AlgumErro(true, 'Acesso negado')
    //       this.navigateLogin();

    //     }
    //     if (ret.flgRealizada == true) {
    //       this.AlgumErro(true, 'Consulta ja finalizada')
    //       this.navigateLogin();

    //     }

    //   }, err => {
    //     ;
    //     this.navigateLogin();
    //   });
  }

  navigateLogin() {
    this.router.navigate(['login']);
  }


  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }
  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';



  CarregaReuniao() {
    if (this.codAcesso) {
      this.reuniaoService.GetReuniaoGuid(this.idGuid, this.codAcesso).subscribe((retorno) => {
        if (retorno == null) {
          this.snackBarAlert.falhaSnackbar('Cod.Acesso incorreto!')
          this.spinner.hide();
          return;
        }
        else {
          this.spinner.hide();
          this.router.navigate(['/reuniao']);
        }

      });
    }
  }


}
// const mensagemErro = {
//   guiInvalido: "Ops... Parece que seu link não é válido. Solicite outro.",
//   consultaRealizada: "Ops... Parece que você já realizou a consulta.",
//   codigoAcessoInvalido: "Ops... Seu código de acesso não consta em nosso sistema. Solicite outro código ou tente novamente.",
//   erroRequisicao: "Ops... Parece que algo deu errado. Tente novamente.",
//   usuarioNaoEncontrado: "Ops... Parece que você não está em nosso sistema. Solicite outro link ou tente novamente."
// }