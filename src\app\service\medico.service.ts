
import { Observable } from 'rxjs';
import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Medico, RecadoDia, ParametrosGetRecadoDia } from '../model/medico';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class MedicoService {

    public atualizaMensagemDia$: EventEmitter<any>;

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
        this.atualizaMensagemDia$ = new EventEmitter()

    }
    public headers = new Headers({ 'Content-Type': 'application/json' });


    public getEspecialidade() {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Especialidade')
            .toPromise();
    }
    public getEspecialidadeMedicos(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Medico/GetEspecialidadeMedico/' + id);

    }
    public getMedicosLogados(idClinica:any) {
        this.spinner.show();
        return this.http.get<number>(environment.apiEndpoint + '/Medico/MedicosLogados/' + idClinica)
            .toPromise();
    }

    public getMedicos(idespecialidade:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idEspecialidade', String(idespecialidade));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Medico/getMedicos', { params });

    }


    public getGridMedicoInativos(inicio:any, fim:any, pesquisa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Medico/getGridMedicoInativos', { params });
    }

    public getGridMedico(inicio:any, fim:any, pesquisa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Medico/getGridMedico', { params });
    }


    public getMedicoEdit(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Medico/GetCarregaUsuario/' + id);
    }
    public inativarMedico(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Medico/InativarMedico/' + id + '/' + idUsuario);
    }

    public AtivarMedico(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Medico/AtivarMedico/' + id + '/' + idUsuario);
    }

    public getOrientacaoDoMedico(id: number): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Medico/OrientacaoDoMedico', id)
    }

    public salvarMedico(cliente: Medico): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Medico/', cliente);
    }

    public salvarRecadoDia(recado: RecadoDia): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Medico/salvarMensagem', recado);
    }

    public VisualizacaoRecadoDia(idRecado:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idRecado', String(idRecado));
        params = params.append('idUsuario', String(idUsuario));
        return this.http.get(environment.apiEndpoint + '/Medico/VisualizouRecado', { params });
    }

    public getRecadoDia(idMedico:any, idClinica:any, QualRecados:any, pesquisa:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idMedico', String(idMedico));
        params = params.append('idClinica', String(idClinica));
        params = params.append('QualRecados', String(QualRecados));
        params = params.append('pesquisa', String(pesquisa));
        return this.http.get(environment.apiEndpoint + '/Medico/RecadoDia', { params });
    }
    public getTodosRecadoDia(parametrosGetRecadoDia: ParametrosGetRecadoDia): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Medico/getTodosRecadoDia', parametrosGetRecadoDia);
    }

    public DeletarRecadoDia(idrecadoDia:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('id', String(idrecadoDia));
        params = params.append('idUsuario', String(idUsuario));
        return this.http.get(environment.apiEndpoint + '/Medico/DeleteRecadoDia', { params });
    }


    public UploadCertificado(chave: any): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Medico/UploadCertificado', chave);
    }


}