// Variables - Green color scheme
$primary-color: #2E8B57; // Main green
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

// Fonts
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

// Main container
.right-column-container {
  font-family: 'Poppins', sans-serif;
  width: 100%;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 15px;
  height: auto;
}

// Stats Card
.stats-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: none;
  overflow: hidden;
  transition: all $transition ease;

}

.stats-header {
  display: flex;
  width: 100%;
  border-bottom: 1px solid rgba($primary-color, 0.1);

  .stats-header-left, .stats-header-right {
    width: 50%;
    padding: 12px 15px;
    background-color: #fff;
    text-align: center;
    
    p {
      margin: 0;
      color: $accent-color;
      font-size: 14px;
      font-weight: 500;
      text-transform: uppercase;
    }
  }
  
  .stats-header-left {
    border-right: 1px solid rgba($primary-color, 0.1);
  }
}

.stats-section-title {
  width: 100%;
  text-align: center;
  padding: 10px 0 5px;
  
  span {
    font-size: 14px;
    font-weight: 600;
    color: $primary-dark;
    text-transform: uppercase;
    position: relative;
    
    &:after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 2px;
      background-color: $primary-color;
    }
  }
}

.stats-row {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  
  .stats-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    &.previous {
      align-items: flex-end;
      padding-right: 10px;
    }
    
    &.current {
      align-items: flex-start;
      padding-left: 10px;
    }
    
    .stats-label {
      font-size: 10px;
      font-weight: 500;
      color: $text-secondary;
      text-transform: uppercase;
      margin-bottom: 2px;
    }
    
    .stats-value {
      font-size: 16px;
      font-weight: 600;
      color: $primary-color;
    }
  }
  
  .stats-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: $primary-light;
    margin: 0 5px;
    
    mat-icon {
      color: $primary-color;
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

.stats-totals {
  display: flex;
  border-top: 1px solid $border-color;
  margin-top: 5px;
  
  .total-item {
    flex: 1;
    padding: 12px 15px;
    text-align: center;
    
    &:first-child {
      border-right: 1px solid $border-color;
    }
    
    .total-label {
      font-size: 11px;
      font-weight: 600;
      color: $text-secondary;
      text-transform: uppercase;
      margin-bottom: 5px;
    }
    
    .total-value {
      font-size: 18px;
      font-weight: 700;
      color: $primary-color;
    }
  }
}

// Profile Card
.profile-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;
  transition: all $transition ease;
  border: none !important;
  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: $primary-light;
  
  .profile-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid $primary-color;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .profile-info {
    flex: 1;
    padding: 0 15px;
    
    .profile-name {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: $primary-dark;
    }
    
    .profile-role {
      margin: 0;
      font-size: 12px;
      color: $text-secondary;
    }
  }
  
  .profile-actions {
    .settings-button {
      color: $primary-color;
    }
  }
}

.appointments-list {
  max-height: 350px;
  overflow-y: auto;
  padding: 10px;
  
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 3px;
  }
}

.appointment-card {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: $secondary-light;
  border-radius: 10px;
  margin-bottom: 10px;
  transition: all $transition ease;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }
  
  .appointment-info {
    flex: 1;
    
    .doctor-name {
      margin: 0 0 5px;
      font-size: 15px;
      font-weight: 600;
      color: $primary-dark;
    }
    
    .appointment-type {
      margin: 0 0 5px;
      font-size: 12px;
      font-style: italic;
      color: $text-secondary;
    }
    
    .appointment-date {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: $primary-color;
    }
  }
  
  .appointment-actions {
    .navigate-button {
      border: 1px solid $primary-color;
      color: $primary-color;
      background-color: white;
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all $transition ease;
      
      &:hover {
        background-color: $primary-color;
        color: white;
      }
    }
  }
}

// Notification Button
.notification-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  list-style: none;
  z-index: 10;
}

.pulse-button {
  background-color: $primary-color;
  box-shadow: 0 4px 10px rgba($primary-color, 0.5);
  animation: pulse 1.5s infinite cubic-bezier(0.66, 0, 0, 1);
  
  .pulse-icon {
    width: 60%;
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba($primary-color, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba($primary-color, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba($primary-color, 0);
  }
}

// Modal Styles
.appointment-modal {
  background-color: $card-bg;
  border-radius: $border-radius;
  width: 100%;
  max-width: 400px;
  overflow: hidden;
  
  .modal-header {
    background-color: $primary-light;
    padding: 15px;
    text-align: center;
    
    .modal-title {
      margin: 0;
      color: $primary-dark;
      font-weight: 600;
      font-size: 18px;
    }
  }
  
  .divider {
    height: 3px;
    background: linear-gradient(to right, $card-bg, $primary-color, $primary-color, $card-bg);
    border: none;
    margin: 0;
  }
  
  .modal-body {
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
      width: 5px;
    }
    
    &::-webkit-scrollbar-thumb {
      background-color: $primary-color;
      border-radius: 3px;
    }
  }
}

.appointment-list {
  .appointment-item {
    display: flex;
    align-items: flex-start;
    background-color: $secondary-light;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 10px;
    
    .appointment-details {
      flex: 1;
      
      .detail-item {
        margin: 0 0 5px;
        font-size: 13px;
        line-height: 1.4;
        
        .detail-label {
          font-weight: 600;
          color: $text-secondary;
          margin-right: 5px;
        }
        
        .detail-value {
          color: $text-primary;
        }
      }
    }
    
    .appointment-actions {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .action-button {
        width: 30px;
        height: 30px;
        
        mat-icon {
          font-size: 16px;
          line-height: 16px;
          width: 16px;
          height: 16px;
        }
        
        &.cancel {
          background-color: $error-color;
          color: white;
        }
        
        &.start {
          background-color: $primary-color;
          color: white;
        }
      }
    }
  }
}

// Cancel Modal
.cancel-modal {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 20px;
  text-align: center;
  
  .cancel-icon {
    margin: 0 auto 15px;
    width: 70px;
    
    img {
      width: 100%;
    }
  }
  
  .cancel-message {
    margin-bottom: 20px;
    
    h3 {
      color: $primary-dark;
      font-weight: 600;
      font-size: 16px;
    }
  }
  
  .cancel-form {
    margin-bottom: 20px;
    
    mat-form-field {
      width: 100%;
    }
    
    .error-message {
      color: $error-color;
      font-size: 12px;
      margin-top: 5px;
    }
  }
  
  .cancel-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    
    button {
      min-width: 100px;
      
      &.cancel-button {
        background-color: #f3f4f6;
        color: $text-secondary;
      }
      
      &.confirm-button {
        background-color: $primary-color;
        color: white;
      }
    }
  }
}

// Side Drawer
.sidenav {
  width: 250px;
  
  .drawer-header {
    background-color: $primary-color;
    color: white;
    padding: 16px;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
  }
  
  .drawer-content {
    padding: 16px;
  }
}

// Media Queries
@media (max-width: 991px) {
  .right-column-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .stats-card {
    display: none;
  }
}

@media (max-width: 576px) {
  .appointment-card,
  .appointment-item {
    flex-direction: column;
    
    .appointment-actions {
      margin-top: 10px;
      flex-direction: row;
      width: 100%;
      justify-content: flex-end;
    }
  }
}
// Upcoming Appointments Card
.upcoming-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: none;
  overflow: hidden;
  transition: all $transition ease;
  margin-bottom: 20px;

}

.upcoming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid rgba($primary-color, 0.1);
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: $accent-color;
  }
  
  .view-all-button {
    color: $accent-color;
    background-color: transparent;
    padding: 5px;
    border-radius: 5px;
    &:hover {
      background-color: rgba($accent-color, 0.1);
    }
  }
}

.upcoming-list {
  padding: 10px 15px;
  max-height: 315px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 3px;
  }
  @media(max-height: 860px){
    max-height: 200px;
  }
  @media(max-height: 800px ){
    max-height: 115px;
  }
}

.upcoming-day {
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .day-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 5px;
    
    .day-name {
      font-size: 14px;
      font-weight: 600;
      color: $primary-dark;
    }
    
    .day-date {
      font-size: 12px;
      color: $text-secondary;
    }
  }
}

.upcoming-item {
  display: flex;
  align-items: center;
  padding: 10px 5px;
  position: relative;
  
  .time-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 10px;
    
    .time-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: $secondary-light;
      border: 2px solid $primary-color;
      margin-bottom: 5px;
      
      &.active {
        background-color: $primary-color;
        box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
      }
    }
    
    .time-line {
      width: 2px;
      height: 40px;
      background-color: $primary-color;
      opacity: 0.3;
    }
  }
  
  &:last-child {
    .time-indicator .time-line {
      display: none;
    }
  }
  
  .appointment-time {
    width: 50px;
    font-size: 14px;
    font-weight: 600;
    color: $primary-color;
    margin-right: 10px;
  }
  
  .appointment-details {
    flex: 1;
    
    h4 {
      margin: 0 0 3px;
      font-size: 14px;
      font-weight: 500;
      color: $text-primary;
    }
    
    p {
      margin: 0;
      font-size: 12px;
      color: $text-secondary;
    }
  }
  
  .appointment-status {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-left: 10px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
    
    &.in-person {
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
    
    &.virtual {
      background-color: rgba(#4F46E5, 0.1);
      color: #4F46E5;
    }
  }
  
  &:hover {
    background-color: $secondary-light;
    border-radius: 8px;
    cursor: pointer;
    
    .appointment-time {
      font-weight: 700;
    }
  }
}

.upcoming-footer {
  padding: 10px 15px;
  border-top: 1px solid $border-color;
  
  .schedule-button {
    width: 100%;
    background-color: #fff;
    color: $primary-color;
    border: 1px solid $primary-color;
    font-weight: 500;
    transition: all $transition ease;
    padding: 5px;
    border-radius: 5px;
    
    &:hover {
      background-color: $primary-color;
      color: white;
    }
  }
}
// Variáveis de cores
$primary-green: #27ae60;
$light-green: #e8f5e9;
$white: #ffffff;
$light-gray: #f9f9f9;
$border-color: #eaeaea;
$text-color: #4a4a4a;
$text-light: #7f8c8d;

/* Forçando estilos com !important onde necessário */
// Variáveis de cores
$primary-green: #27ae60;
$light-green: #e8f5e9;
$white: #ffffff;
$light-gray: #f9f9f9;
$border-color: #eaeaea;
$text-color: #4a4a4a;
$text-light: #7f8c8d;

// Cores para diferentes tipos de consulta
$first-visit-color: #3498db; // Azul para primeiras consultas
$return-visit-color: #27ae60; // Verde para retornos
$emergency-color: #e74c3c; // Vermelho para emergências
$exam-color: #f39c12; // Amarelo para exames

// Cores para status
$confirmed-color: #27ae60;
$pending-color: #f39c12;

// Estilos do container 
.calendar-container {
  width: 100%;
  padding: 0;
}

.calendar-card {
  background-color: $white;
  border-radius: 12px;
  overflow: hidden;

}

.calendar-content {
  padding: 0;
  
  // Ajustes para o calendário Material
  ::ng-deep .mat-calendar {
    width: 100%;
    .mat-calendar-table-header th{
      padding: 8px 0 !important;
    }
    // Remover fundo azul e deixar cabeçalho mais compacto
    .mat-calendar-header {
      padding: 8px 0 !important;
      background: #fff !important;
    }
    
    // Reduzir o tamanho geral
    .mat-calendar-table {
      font-size: 12px;
      overflow: hidden;
      border: 1px solid #d5d5d5 !important;
      border-radius: 8px;
    }
    
    // Reduzir o tamanho das células
    .mat-calendar-body-cell {
      height: 32px;
      width: 32px;
    }
    
    // Ajustar o conteúdo das células
    .mat-calendar-body-cell-content {
      height: 28px;
      width: 28px;
      line-height: 28px;
    }
    
    // Cabeçalho da tabela
    .mat-calendar-table-header {
      color: $primary-green;
      font-weight: 500;
      font-size: 11px;
      background: #fff !important;
    }
    
    // Dia selecionado
    .mat-calendar-body-selected {
      background-color: $primary-green;
      color: $white;
    }
    
    // Dia de hoje
    .mat-calendar-body-today:not(.mat-calendar-body-selected) {
      border-color: $primary-green;
    }
    
    // Seta do calendário
    .mat-calendar-arrow {
      border-top-color: $primary-green;
    }
    
    // Hover nos dias
    .mat-calendar-body-cell:hover:not(.mat-calendar-body-disabled):not(.mat-calendar-body-selected) 
    .mat-calendar-body-cell-content {
      background-color: rgba($primary-green, 0.1);
    }
    
    // Botões
    .mat-calendar-previous-button,
    .mat-calendar-next-button,
    .mat-calendar-period-button {
      color: $primary-green;
    }
    
    // Marcadores para datas com consultas
    .appointment-date .mat-calendar-body-cell-content {
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 50%;
        transform: translateX(-50%);
        width: 4px;
        height: 4px;
        border-radius: 50%;
      }
    }
    
    // Diferentes tipos de consulta
    .first-appointment .mat-calendar-body-cell-content::after {
      background-color: $first-visit-color;
    }
    
    .regular-appointment .mat-calendar-body-cell-content::after {
      background-color: $return-visit-color;
    }
    
    .emergency-appointment .mat-calendar-body-cell-content::after {
      background-color: $emergency-color;
    }
  }
}

// Footer com consultas
.calendar-footer {
  padding: 12px 15px;
  border-top: 1px solid $border-color;
  background-color: $white;
}

// Display da data
.date-display {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  
  .day-num {
    font-size: 24px;
    font-weight: 500;
    color: $primary-green;
    margin-right: 12px;
    line-height: 1;
  }
  
  .month-year {
    display: flex;
    flex-direction: column;
    
    .month {
      font-size: 14px;
      font-weight: 500;
      color: $text-color;
      text-transform: capitalize;
    }
    
    .year {
      font-size: 13px;
      color: $text-light;
    }
  }
}

// Título da seção de consultas
.appointments-title {
  display: flex;
  align-items: center;
  margin: 12px 0 8px;
  font-size: 14px;
  font-weight: 500;
  color: $text-color;
  
  .appointment-count {
    margin-left: 5px;
    color: $text-light;
    font-weight: normal;
  }
}

// Lista de consultas
.appointments-list {
  max-height: 200px;
  overflow-y: auto;
  
  .appointment-item {
    display: flex;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 6px;
    background-color: $light-gray;
    border-left: 3px solid;
    
    &.first-visit {
      border-left-color: $first-visit-color;
    }
    
    &.return-visit {
      border-left-color: $return-visit-color;
    }
    
    &.emergency-visit {
      border-left-color: $emergency-color;
    }
    
    &.exam-visit {
      border-left-color: $exam-color;
    }
    
    .appointment-time {
      display: flex;
      flex-direction: column;
      min-width: 60px;
      margin-right: 10px;
      
      .time {
        font-size: 14px;
        font-weight: 500;
        color: $text-color;
      }
      
      .duration {
        font-size: 11px;
        color: $text-light;
      }
    }
    
    .appointment-info {
      flex: 1;
      
      .appointment-title {
        display: block;
        font-size: 13px;
        font-weight: 500;
        color: $text-color;
        margin-bottom: 3px;
      }
      
      .appointment-details {
        display: flex;
        justify-content: space-between;
        font-size: 11px;
        
        .appointment-type {
          color: $text-light;
        }
        
        .appointment-status {
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 10px;
          
          &.status-confirmed {
            background-color: rgba($confirmed-color, 0.1);
            color: $confirmed-color;
          }
          
          &.status-pending {
            background-color: rgba($pending-color, 0.1);
            color: $pending-color;
          }
        }
      }
    }
  }
  
  .no-appointments {
    color: $text-light;
    font-size: 13px;
    font-style: italic;
    padding: 10px 0;
    text-align: center;
  }
}

// Responsividade
@media (max-width: 768px) {
  .calendar-card {
    border-radius: 8px;
  }
  
  .calendar-footer {
    padding: 10px 12px;
  }
  
  .date-display .day-num {
    font-size: 20px;
  }
  
  .appointments-list {
    max-height: 180px;
    
    .appointment-item {
      padding: 8px;
      
      .appointment-time .time {
        font-size: 13px;
      }
      
      .appointment-info .appointment-title {
        font-size: 12px;
      }
    }
  }
}

// Telas pequenas
@media (max-width: 320px) {
  ::ng-deep .mat-calendar-body-cell {
    height: 28px;
    width: 28px;
  }
  
  ::ng-deep .mat-calendar-body-cell-content {
    height: 24px;
    width: 24px;
    line-height: 24px;
  }
  
  .appointments-list .appointment-item {
    padding: 6px;
  }
}