/* Variáveis da paleta de cores */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

main {
    background-color: $card-bg;
    border-radius: $border-radius;
    box-shadow: none;
    border: 1px solid $border-color;
    margin-bottom: 24px;
    overflow: hidden;
    border-top:4px solid #2E8B57 !important ;
    padding: 20px;
    @media (max-width: 1000px) {
        height: 80vh;
        overflow: scroll;
    }

    h2 {
        color: $primary-color;
    }
    .divAgenda {
        width: 100%;
        padding-left: 1.5rem;
        padding-right: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;

        .escolherMedico {
            width: fit-content;
        }
    }

    .divBotoes {
        display: flex;
        gap: 1rem;
    }

    .btn {
        border: none;
        border-radius: $border-radius;
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all $transition;
    }

    .btn-primary {
        background-color: $primary-color;
        color: white;

        &:hover {
            background-color: $primary-dark;
            box-shadow: $box-shadow;
        }
    }

    .mb-3 {
        margin-bottom: 1rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: $text-primary;
        font-size: 0.875rem;
    }

    .form-select {
        display: block;
        width: 100%;
        padding: 0.75rem 2.5rem 0.75rem 1rem;
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 1.5;
        color: $text-primary;
        background-color: $card-bg;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%232E8B57' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 16px 12px;
        border: 1px solid $border-color;
        border-radius: $border-radius;
        transition: border-color $transition, box-shadow $transition;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }

    .form-select:focus {
        border-color: $primary-color;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(46, 139, 87, 0.25);
    }

    .select-medico-personalizado {
        .form-label {
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: $text-primary;
        }

        .form-select {
            height: 2.75rem;
            padding: 0.5rem 2.5rem 0.5rem 1rem;
            font-size: 0.875rem;
            border: 1px solid $border-color;
            border-radius: $border-radius;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
            min-width: 250px;

            &:hover {
                border-color: $primary-color;
            }

            &:focus {
                border-color: $primary-color;
                box-shadow: 0 0 0 3px rgba(46, 139, 87, 0.15);
            }
        }
    }

    @media (max-width: 768px) {
        .divAgenda {
            flex-direction: column;
            align-items: stretch;
            gap: 1.5rem;
        }

        .divBotoes {
            justify-content: space-between;
        }

        .escolherMedico {
            width: 100% !important;
        }

        .select-medico-personalizado {
            .form-select {
                width: 100%;
                min-width: unset;
            }
        }
    }
}
::ng-deep{
    .mat-mdc-dialog-container .mdc-dialog__surface {
        height: auto !important;
    }
}