/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
  font-family: 'Inter', 'Cairo', sans-serif;
}

.container {
  padding: 16px;
  max-width: 1400px;
  margin: 0 auto;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
  margin-bottom: 30px;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.header-icon {
  background-color: $primary-light;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  border: 2px solid $primary-color;
  
  .material-icons {
    color: $primary-color;
    font-size: 24px;
  }
}

.header-title {
  color: $primary-color;
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
}

/* FILTROS */
.filtros {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  border-radius: $border-radius;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.search-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 250px;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}

.search-label {
  font-weight: 500;
  color: $text-primary;
}

.search-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

mat-radio-button {
  --mdc-radio-state-layer-size: 30px;
  --mdc-radio-selected-icon-color: #{$primary-color} !important;
  --mdc-radio-selected-focus-icon-color: #{$primary-color} !important;
  --mdc-radio-selected-hover-icon-color: #{$primary-color} !important;
  --mdc-radio-selected-pressed-icon-color: #{$primary-color} !important;
  --mat-radio-ripple-color: black;
  --mat-radio-checked-ripple-color: #{$primary-color} !important;
}

.busca-container {
  flex: 1;
  min-width: 75%;
  
}

.busca-field {
  width: 100%;
  
  ::ng-deep .mat-form-field-wrapper {
    margin-bottom: 0;
    padding-bottom: 0;
  }
  
  ::ng-deep .mat-form-field-flex {
    background-color: white;
  }
  
  ::ng-deep .mat-form-field-outline {
    color: $border-color;
  }
  
  ::ng-deep .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .btn-busca {
    color: $primary-color;
    background-color: transparent;
  }
}

.adicionar-container {
  display: flex;
  justify-content: flex-end;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}

.btn-adicionar, .btn-adicionar-mobile {
  background-color: $primary-color;
  color: white;
  border-radius: $border-radius;
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all $transition;
  height: 40px;
  border: none;
  box-shadow: $box-shadow;
  
  &:hover {
    background-color: $primary-dark;
    transform: translateY(-2px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  mat-icon {
    font-size: 20px;
  }
}

.btn-adicionar-mobile {
  width: 100%;
  justify-content: center;
}

/* LISTA DE CONTATOS */
.lista-container {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 16px;
  margin-bottom: 30px;
}

.lista-scroll {
  max-height: 57vh;
  overflow-y: auto;
  padding: 10px;
}

.contato-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid transparent;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow;
    border-left: 4px solid $primary-color;
  }
  
  @media (max-width: 1024px) {
    flex-direction: column;
  }
}

.contato-info {
  flex: 1;
  min-width: 220px;
}

.contato-principal {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.info-item {
  display: flex;
  align-items: center;
  
  mat-icon {
    color: $primary-color;
    font-size: 20px;
    margin-right: 10px;
  }
  
  span {
    color: $text-secondary;
  }
  
  &.nome-item span {
    color: $text-primary;
    font-weight: 600;
    font-size: 16px;
  }
}

.contato-dados {
  flex: 1;
  min-width: 180px;
  
  &.secundarios {
    flex: 1;
    min-width: 180px;
  }
}

.dados-item {
  margin-bottom: 12px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.dados-label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: $primary-color;
  margin-bottom: 4px;
}

.dados-valor {
  color: $text-secondary;
}

.contato-acoes {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition;
  border: none;
  min-width: auto !important;
  
  &.edit-button {
    background-color: $primary-light;
    color: $primary-color;
    
    &:hover {
      background-color: $primary-color;
      color: white;
      transform: translateY(-2px);
    }
  }
  
  &.delete-button {
    background-color: #fff7f7;
    color: #ff6b6b;
    
    &:hover {
      background-color: $error-color;
      color: white;
      transform: translateY(-2px);
    }
  }
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: $text-secondary;
  
  mat-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
}

/* MOBILE VIEW */
.mobile-only {
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
}

.desktop-only {
  @media (max-width: 768px) {
    display: none;
  }
}

/* LISTA DE CONTATOS - MOBILE */
.lista-mobile {
  margin-bottom: 30px;
}

.contato-card-mobile {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all $transition;
  border-left: 4px solid $primary-color;
}

.contato-header-mobile {
  padding: 16px;
}

.contato-info-mobile {
  margin-bottom: 10px;
}

.contato-nome-mobile {
  font-size: 18px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
}

.contato-tipo-mobile {
  color: $text-secondary;
  margin: 0 0 4px 0;
  font-size: 14px;
}

.contato-email-mobile {
  color: $text-secondary;
  margin: 0;
  font-size: 14px;
}

.contato-dados-mobile {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  padding: 16px;
}

.dados-mobile-item {
  h4 {
    font-size: 13px;
    font-weight: 600;
    color: $primary-color;
    margin: 0 0 4px 0;
  }
  
  p {
    color: $text-secondary;
    margin: 0;
    font-size: 14px;
  }
}

.acoes-mobile {
  position: relative;
  padding: 16px;
  text-align: right;
  height: 70px;
}

.acoes-buttons {
  display: flex;
  gap: 8px;
  position: absolute;
  right: 80px;
  bottom: 16px;
  
  button {
    &.edit-button {
      background-color: $primary-color;
    }
    
    &.delete-button {
      background-color: $error-color;
    }
  }
}

.toggle-button {
  background-color: $primary-color;
  color: white;
  position: absolute;
  right: 16px;
  bottom: 16px;
}

/* BOTÃO CARREGAR MAIS */
.carregar-mais {
  text-align: center;
  margin-top: 30px;
}

.btn-carregar {
  background-color: $primary-color;
  color: white;
  padding: 8px 24px;
  border-radius: $border-radius;
  font-weight: 500;
  transition: all $transition;
  border: none;
  
  &:hover {
    background-color: $primary-dark;
    box-shadow: $box-shadow;
    transform: translateY(-2px);
  }
}

/* MODAIS */
.modal-container ::ng-deep .nsm-content {
  border-radius: $border-radius;
  padding: 0;
  overflow: hidden;
  width: 90%;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modal-content {
  display: flex;
  flex-direction: column;
}

.modal-header {
  background-color: $primary-light;
  padding: 20px 24px;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 16px;
  
  mat-icon {
    color: $primary-color;
    font-size: 24px;
  }
  
  h3 {
    margin: 0;
    color: $primary-dark;
    font-weight: 600;
    font-size: 18px;
  }
  
  .warning-icon {
    color: $error-color;
  }
}

.modal-body {
  padding: 24px;
}

.modal-text {
  font-size: 16px;
  color: $text-primary;
  margin-bottom: 8px;
  text-align: center;
}

.form-row {
  margin-bottom: 20px;
}

.form-field {
  width: 100%;
  
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
  }
  
  ::ng-deep .mat-form-field-flex {
    background-color: white;
  }
  
  ::ng-deep .mat-form-field-outline {
    color: $border-color;
  }
  
  ::ng-deep .mat-form-field-outline-thick {
    color: $primary-color;
  }
}

.field-container {
  position: relative;
  margin-bottom: 8px;
}

.field-error {
  color: $error-color;
  font-size: 12px;
  margin-top: -8px;
  margin-bottom: 8px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  background-color: $secondary-light;
  
  @media (max-width: 480px) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.btn-primary, .btn-neutral, .btn-danger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: $border-radius;
  font-weight: 500;
  transition: all $transition;
  border: none;
  
  mat-icon {
    font-size: 18px;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: $primary-dark;
  }
}

.btn-neutral {
  background-color: $secondary-color;
  color: $text-secondary;
  
  &:hover {
    background-color: darken($secondary-color, 5%);
  }
}

.btn-danger {
  background-color: rgba($error-color, 0.1);
  color: $error-color;
  
  &:hover {
    background-color: $error-color;
    color: white;
  }
}

/* ANIMAÇÕES PARA BOTÕES MOBILE */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(20px);
  }
}

.acoes-buttons.ng-trigger-openClose {
  &.ng-animating {
    animation-duration: 0.2s;
    animation-fill-mode: forwards;
  }
  
  &.open {
    animation-name: slideIn;
  }
  
  &.closed {
    animation-name: slideOut;
    display: none;
  }
}
.modal-container ::ng-deep .nsm-content {
  border-radius: $border-radius;
  padding: 0;
  overflow: hidden;
  width: 90%;
  max-width: 500px; /* Reduced from 600px */
  height: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  max-height: 80vh; /* Added max-height */
}

/* RESPONSIVIDADE */
@media (max-width: 1024px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-options, .busca-container, .adicionar-container {
    width: 100%;
    max-width: 100%;
  }
  
  .btn-adicionar {/* MODAIS - UPDATED */
    
    
    .modal-content {
      display: flex;
      flex-direction: column;
      max-height: 80vh; /* Keep content within viewport */
    }
    
    .modal-header {
      background-color: $primary-light;
      padding: 16px 24px; /* Reduced from 20px 24px */
    }
    
    .modal-title {
      display: flex;
      align-items: center;
      gap: 12px; /* Reduced from 16px */
      
      mat-icon {
        color: $primary-color;
        font-size: 22px; /* Reduced from 24px */
      }
      
      h3 {
        margin: 0;
        color: $primary-dark;
        font-weight: 600;
        font-size: 17px; /* Reduced from 18px */
      }
      
      .warning-icon {
        color: $error-color;
      }
    }
    
    .modal-body {
      padding: 20px 24px; /* Reduced from 24px */
      overflow-y: auto; /* Make the body scrollable if needed */
      max-height: calc(80vh - 120px); /* Allow scrolling for content */
    }
    
    .modal-text {
      font-size: 15px; /* Reduced from 16px */
      color: $text-primary;
      margin-bottom: 8px;
      text-align: center;
    }
    
    .form-row {
      margin-bottom: 16px; /* Reduced from 20px */
    }
    
    .form-field {
      width: 100%;
      
      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0;
      }
      
      ::ng-deep .mat-form-field-infix {
        padding: 0.5em 0; /* Reduce input field height */
      }
      
      ::ng-deep .mat-form-field-flex {
        background-color: white;
      }
      
      ::ng-deep .mat-form-field-outline {
        color: $border-color;
      }
      
      ::ng-deep .mat-form-field-outline-thick {
        color: $primary-color;
      }
    }
    
    .field-container {
      position: relative;
      margin-bottom: 6px; /* Reduced from 8px */
    }
    
    .field-error {
      color: $error-color;
      font-size: 11px; /* Reduced from 12px */
      margin-top: -6px; /* Reduced from -8px */
      margin-bottom: 6px; /* Reduced from 8px */
    }
    
    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px; /* Reduced from 12px */
      padding: 12px 24px; /* Reduced from 16px 24px */
      border-top: 1px solid $border-color;
      background-color: $secondary-light;
      
      @media (max-width: 480px) {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
    
    .btn-primary, .btn-neutral, .btn-danger {
      display: flex;
      align-items: center;
      gap: 6px; /* Reduced from 8px */
      padding: 6px 14px; /* Reduced from 8px 16px */
      border-radius: $border-radius;
      font-weight: 500;
      transition: all $transition;
      border: none;
      
      mat-icon {
        font-size: 17px; /* Reduced from 18px */
      }
    }
    
    /* For the delete modal specifically */
    #excluirItem .modal-container ::ng-deep .nsm-content {
      max-width: 400px; /* Even smaller for confirmation dialogs */
      max-height: 250px; /* Fixed height for confirmation dialogs */
    }
    
    #excluirItem .modal-body {
      padding: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    width: 100%;
    justify-content: center;
  }
  
  .contato-info, .contato-dados, .contato-acoes {
    width: 100%;
  }
  
  .contato-acoes {
    justify-content: flex-end;
  }
}