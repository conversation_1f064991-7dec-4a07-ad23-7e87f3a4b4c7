import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Atestado, Declaracao, Receita, MedicamentosProgramados } from '../model/documentos';
import { catchError, Observable, throwError } from 'rxjs';
import { flgConsultasRelatorio } from '../model/flgRelatorio';
import { ParametrosAtividadesUsuario } from '../model/medico';
import { GuiaExames } from '../model/guiaExames';
import { SpinnerService } from './spinner.service';
import { MedicamentosModeView } from '../model/medicamento';

@Injectable({
    providedIn: 'root'
})
export class DocumentosService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public GetListaMedicamentos(pesquisa: string = "", inicio: number = 0, fim: number = 10): Observable<any> {
        let params = new HttpParams();
        params = params.append('pesquisa', pesquisa);
        params = params.append('inicio', inicio);
        params = params.append('fim', fim)
        return this.http.get<MedicamentosModeView[]>(environment.apiEndpoint + '/Documentos/GetListaMedicamentos', { params })            
            .pipe(
                catchError((error) => {
                    console.error("Erro GetListaMedicamentos", error);
                    return throwError(() => error);
                })
            );
    }

    public GetMedicamentosClinica(pesquisa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Documentos/GetMedicamentosClinica', { params });
    }

    public GerarGuia(Guias: GuiaExames): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Documentos/GerarGuiaServico', Guias, { responseType: 'arraybuffer' });
    }
    public CarregaMedicamentosGrid(inicio:any, fim:any, pesquisa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));


        return this.http.get(environment.apiEndpoint + '/Documentos/CarregaMedicamentosGrid/', { params });
    }


    public RelatorioClinica(Relatorio: flgConsultasRelatorio): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Documentos/CarregaRelatorioPorClinica', Relatorio);
    }

    public GetPadraoRelatorio(idPadrao:any, idClinica:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Documentos/GetPadraoRelatorio/' + idPadrao + '/' + idClinica);
    }



    public baixarExcelRelatorioUsuario(parametrosRelatorio:any): Observable<any> {
        return this.http.post(environment.apiEndpoint + '/Documentos/CarregaRelatorioUsuario', parametrosRelatorio);
    }

    // public pesquisarRelatorioUsuario(parametrosAtividadesUsuario :ParametrosAtividadesUsuario): Observable<any> {

    //     return this.http.get(environment.apiEndpoint + '/Documentos/PesquisarRelatorioUsuario', parametrosAtividadesUsuario);

    // }

    public pesquisarRelatorioUsuario(parametrosAtividadesUsuario: ParametrosAtividadesUsuario): Observable<any> {

        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Documentos/PesquisarRelatorioUsuario', parametrosAtividadesUsuario);
    }

    public Carregagraficos(idclinica:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Documentos/CarregaGrafico/' + idclinica);

    }
    public getCID(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Documentos/GetCID');
    }
    public GerarAtestado(atestado: Atestado): Observable<any> {

        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Documentos/GerarAtestado', atestado, { responseType: 'arraybuffer' });
    }

    public inativarMedicamento(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Documentos/InativarMedicamento/' + id + '/' + idUsuario);
    }


    public SalvarMedicamentos(medicamento: MedicamentosProgramados): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Documentos/SalvaMedicamentos', medicamento);
    }

    public GerarReceita(receita: Receita): Observable<any> {

        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Documentos/GerarReceita', receita, { responseType: 'arraybuffer' });
    }

    public GerarDeclaracao(declaracao: Declaracao): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Documentos/GerarDeclaracao', declaracao, { responseType: 'arraybuffer' });
    }

    public GetTodosPadroesRelatorios(idClinica:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Documentos/GetTodosPadroesRelatorios/' + idClinica);
    }

    public SalvarPadraoRelatorio(padraoRelatorio: flgConsultasRelatorio): Observable<any> {

        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Documentos/SalvarPadraoRelatorio', padraoRelatorio);
    }

    public InativarPadraoRelatorio(idPadrao:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Documentos/InativarPadraoRelatorio/' + idPadrao);
    }

    public GerarXml(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Documentos/GerarXml');
    }

}
