// Variáveis de cores
$primary-green: #00a86b;
$light-green: #e8f5e8;
$dark-green: #006b47;
$accent-green: #4caf50;
$success-green: #00c851;
$bg-gradient: linear-gradient(135deg, #f0fff0 0%, #e8f5e8 100%);
$card-shadow: 0 10px 30px rgba(0, 168, 107, 0.1);
$hover-shadow: 0 15px 40px rgba(0, 168, 107, 0.2);

// Reset e configurações base
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

.main-container {
    min-height: 100vh;
    background: $bg-gradient;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;

    // Adicionar padrão sutil de fundo
    &::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: radial-gradient(circle at 25% 25%, rgba(0, 168, 107, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(76, 175, 80, 0.03) 0%, transparent 50%);
        pointer-events: none;
        z-index: -1;
    }
}

.queue-card {
    background: white;
    border-radius: 24px;
    box-shadow: $card-shadow;
    padding: 40px;
    width: 100%;
    max-width: 70vmax;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
        box-shadow: $hover-shadow;
        transform: translateY(-2px);
    }

    // Gradiente sutil no topo do card
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, $primary-green 0%, $accent-green 50%, $success-green 100%);
    }
}

// Animação de entrada
.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Status indicator
.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(0, 168, 107, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%);
    border-radius: 50px;
    border: 1px solid rgba(0, 168, 107, 0.2);

    .status-dot {
        width: 12px;
        height: 12px;
        background: $success-green;
        border-radius: 50%;
        margin-right: 10px;
        animation: pulse 2s infinite;
        box-shadow: 0 0 0 0 rgba(0, 200, 81, 0.7);
    }

    .status-text {
        color: $dark-green;
        font-weight: 600;
        font-size: 14px;
        letter-spacing: 0.5px;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 200, 81, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 200, 81, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 200, 81, 0);
    }
}

// Títulos
.welcome-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: $dark-green;
    text-align: center;
    margin-bottom: 10px;
    background: linear-gradient(135deg, $primary-green 0%, $accent-green 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 1.1rem;
    color: #666;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 400;
    line-height: 1.5;
}

// Container da posição
.position-container {
    text-align: center;
    margin-bottom: 40px;
}

#queuePosition {
    .position-label {
        font-size: 1rem;
        color: $dark-green;
        font-weight: 600;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .position-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, $primary-green 0%, $accent-green 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        box-shadow: 0 8px 25px rgba(0, 168, 107, 0.3);
        position: relative;

        &::before {
            content: "";
            position: absolute;
            inset: 3px;
            border-radius: 50%;
            background: white;
        }

        .position-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: $primary-green;
            z-index: 1;
            position: relative;
        }
    }
}

// Botão entrar
.enter-button {
    background: linear-gradient(135deg, $success-green 0%, $primary-green 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 6px 20px rgba(0, 200, 81, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 200, 81, 0.4);
        background: linear-gradient(135deg, $primary-green 0%, $success-green 100%);
    }

    &:active {
        transform: translateY(-1px);
    }

    i {
        font-size: 1.3rem;
    }
}

// Botão desistir
.quit-button {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a5a 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);

    &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        background: linear-gradient(135deg, #ee5a5a 0%, #ff6b6b 100%);
    }

    &:active {
        transform: translateY(0);
    }
}

// Card de instruções
.instructions-card {
    background: linear-gradient(135deg, rgba(0, 168, 107, 0.05) 0%, rgba(76, 175, 80, 0.05) 100%);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(0, 168, 107, 0.1);
    position: relative;

    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, $primary-green 0%, $accent-green 100%);
        border-radius: 16px 16px 0 0;
    }
}

.instructions-header {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 700;
    color: $dark-green;
    margin-bottom: 20px;

    i {
        color: $primary-green;
        font-size: 1.3rem;
    }
}

.instructions-content {
    .instruction-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }

        .instruction-number {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, $primary-green 0%, $accent-green 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
            margin-right: 15px;
            flex-shrink: 0;
            box-shadow: 0 3px 10px rgba(0, 168, 107, 0.2);
        }

        .instruction-text {
            color: #555;
            line-height: 1.6;
            font-size: 0.95rem;
            padding-top: 4px;
        }
    }
}

// Responsividade
@media (max-width: 768px) {
    .main-container {
        padding: 15px;
    }

    .queue-card {
        padding: 30px 20px;
        border-radius: 16px;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .position-circle {
        width: 100px !important;
        height: 100px !important;

        .position-number {
            font-size: 2rem !important;
        }
    }

    .enter-button {
        padding: 15px 30px;
        font-size: 1.1rem;
    }

    .instructions-card {
        padding: 20px;
    }

    .instruction-item {
        .instruction-number {
            width: 28px !important;
            height: 28px !important;
            font-size: 0.8rem !important;
            margin-right: 12px !important;
        }

        .instruction-text {
            font-size: 0.9rem !important;
        }
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: 1.8rem;
    }

    .queue-card {
        padding: 25px 15px;
    }

    .enter-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
}

/* Estilos para o resumo do questionário */
.questionnaire-summary {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    border-radius: 16px;
    padding: 20px;
    margin: 25px 0;
    border: 1px solid rgba(0, 168, 107, 0.1);
    box-shadow: 0 4px 15px rgba(0, 168, 107, 0.05);

    .summary-header {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 1.1rem;
        color: $dark-green;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(0, 168, 107, 0.1);

        i {
            color: $primary-green;
            font-size: 1.2rem;
        }
    }

    .summary-content {
        display: grid;
        gap: 12px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 8px;
        border-left: 3px solid $primary-green;

        .summary-label {
            font-weight: 500;
            color: #555;
            font-size: 0.9rem;
        }

        .summary-value {
            font-weight: 600;
            color: $dark-green;
            font-size: 0.95rem;
            text-align: right;
            max-width: 60%;
            word-wrap: break-word;
        }
    }
}

/* Indicador de tipo de usuário */
.user-type-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 168, 107, 0.1);
    border-radius: 20px;
    padding: 8px 16px;
    margin: 15px 0;
    font-size: 0.9rem;
    font-weight: 500;
    color: $dark-green;

    i {
        color: $primary-green;
    }
}

/* Responsividade para as novas seções */
@media (max-width: 768px) {
    .questionnaire-summary {
        margin: 20px 0;
        padding: 15px;

        .summary-item {
            flex-direction: column;
            text-align: center;
            gap: 5px;

            .summary-label {
                font-size: 0.8rem;
            }

            .summary-value {
                font-size: 0.9rem;
                max-width: 100%;
            }
        }
    }

    .user-type-indicator {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}
