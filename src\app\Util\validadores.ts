import { Injectable } from "@angular/core";

@Injectable({
  providedIn: 'root' 
})

export class ValidadoreseMascaras {

  public mascaraCpf(mascara:any, evento: KeyboardEvent) {
    mascara;
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    v = v.replace(/(\d{3})(\d)/, "$1.$2");
    v = v.replace(/(\d{3})(\d)/, "$1.$2");
    v = v.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
    (<HTMLInputElement>evento.target).value = v;
  }

  public soNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraCnpj(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");                           //Remove tudo o que não é dígito
    v = v.replace(/^(\d{2})(\d)/, "$1.$2");             //Coloca ponto entre o segundo e o terceiro dígitos
    v = v.replace(/^(\d{2})\.(\d{3})(\d)/, "$1.$2.$3"); //Coloca ponto entre o quinto e o sexto dígitos
    v = v.replace(/\.(\d{3})(\d)/, ".$1/$2");           //Coloca uma barra entre o oitavo e o nono dígitos
    v = v.replace(/(\d{4})(\d)/, "$1-$2");              //Coloca um hífen depois do bloco de quatro dígitos

    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraCep(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{5})(\d)/, "$1-$2");
    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraSomenteLetra(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\d/g, "");
    (<HTMLInputElement>evento.target).value = v;
  }

  public validateDate(id:any): Boolean {
    var RegExPattern = /^((((0?[1-9]|[12]\d|3[01])[\.\-\/](0?[13578]|1[02])[\.\-\/]((1[6-9]|[2-9]\d)?\d{2}))|((0?[1-9]|[12]\d|30)[\.\-\/](0?[13456789]|1[012])[\.\-\/]((1[6-9]|[2-9]\d)?\d{2}))|((0?[1-9]|1\d|2[0-8])[\.\-\/]0?2[\.\-\/]((1[6-9]|[2-9]\d)?\d{2}))|(29[\.\-\/]0?2[\.\-\/]((1[6-9]|[2-9]\d)?(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00)|00)))|(((0[1-9]|[12]\d|3[01])(0[13578]|1[02])((1[6-9]|[2-9]\d)?\d{2}))|((0[1-9]|[12]\d|30)(0[13456789]|1[012])((1[6-9]|[2-9]\d)?\d{2}))|((0[1-9]|1\d|2[0-8])02((1[6-9]|[2-9]\d)?\d{2}))|(2902((1[6-9]|[2-9]\d)?(0[48]|[2468][048]|[13579][26])|((16|[2468][048]|[3579][26])00)|00))))$/;

    if (!((id.match(RegExPattern)) && (id != ''))) {
      return true;
    }
    else {
      return false;
    }
  }
  public validarEmail(email:any): Boolean {
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(email);
  }

  public isMobile(device:any) {
    return [
      "ANDROID",
      "I_PAD",
      "IPHONE",
      "I_POD",
      "BLACKBERRY",
      "FIREFOX_OS",
      "WINDOWS_PHONE",
      "VITA"
    ].some(function (item) {
      return device.toUpperCase() == item;
    });
  };


  SetHorasData(data: Date, hora: string): Date | undefined{
    try {
      var horasplit = hora.substring(0, 8);

      let HoraString = horasplit.split(":");
      let preparadata = new Date(data);
      //O MES NO JAVA SCRIPT VAI DE 0 - 11, POR ISSO DESCONTAMOS UM MES
      let NovaData = new Date(preparadata.getFullYear(), preparadata.getMonth() - 1, preparadata.getDate()
        , parseInt(HoraString[0]), parseInt(HoraString[1]));
      return NovaData;
    } catch (error) {
      return undefined;
    }
  }

  MontaData(data: string, hora: string) {

    try {
      let DataString = data.split("/");
      let HoraString = hora.split(":");
      //O MES NO JAVA SCRIPT VAI DE 0 - 11, POR ISSO DESCONTAMOS UM MES
      let NovaData = new Date(parseInt(DataString[2]), parseInt(DataString[1]) - 1, parseInt(DataString[0])
        , parseInt(HoraString[0]), parseInt(HoraString[1]));
      return NovaData;

    } catch (error) {

    }
  }


  Convertdata(value: any): Date | null {
    if ((typeof value === 'string') && (value.indexOf('/') > -1)) {
      const str = value.split('/');

      const year = Number(str[2]);
      const month = Number(str[1]) - 1;
      const date = Number(str[0]);

      return new Date(year, month, date);
    } else if ((typeof value === 'string') && value === '') {
      return new Date();
    }
    const timestamp = typeof value === 'number' ? value : Date.parse(value);
    return isNaN(timestamp) ? null : new Date(timestamp);
  }


  ConvertdataRelatorio(value: any): Date | null {
    if ((typeof value === 'string') && (value.indexOf('/') > -1)) {
      const str = value.split('/');

      const year = Number(str[2]);
      const month = Number(str[1]) - 1;
      const date = Number(str[0]);

      return new Date(year, month, date);
    } else if ((typeof value === 'string') && value === '') {
      return new Date();
    }
    const timestamp = typeof value === 'number' ? value : Date.parse(value);
    return isNaN(timestamp) ? null : new Date(timestamp);
  }


  public removeMascara(str:any) {
    return str.normalize('NFD').replace(/([\u0300-\u036f]|[^0-9a-zA-Z])/g, '');
  }


  public cpf(cpf: string): boolean {


    cpf = cpf.replace(/\D/g, "");


    if (cpf == null) {
      return false;
    }
    if (cpf.length != 11) {
      return false;
    }
    if ((cpf == '00000000000') || (cpf == '11111111111') || (cpf == '22222222222') || (cpf == '33333333333') || (cpf == '44444444444') || (cpf == '55555555555') || (cpf == '66666666666') || (cpf == '77777777777') || (cpf == '88888888888') || (cpf == '99999999999')) {
      return false;
    }
    let numero: number = 0;
    let caracter: string = '';
    let numeros: string = '0123456789';
    let j: number = 10;
    let somatorio: number = 0;
    let resto: number = 0;
    let digito1: number = 0;
    let digito2: number = 0;
    let cpfAux: string = '';
    cpfAux = cpf.substring(0, 9);
    for (let i: number = 0; i < 9; i++) {
      caracter = cpfAux.charAt(i);
      if (numeros.search(caracter) == -1) {
        return false;
      }
      numero = Number(caracter);
      somatorio = somatorio + (numero * j);
      j--;
    }
    resto = somatorio % 11;
    digito1 = 11 - resto;
    if (digito1 > 9) {
      digito1 = 0;
    }
    j = 11;
    somatorio = 0;
    cpfAux = cpfAux + digito1;
    for (let i: number = 0; i < 10; i++) {
      caracter = cpfAux.charAt(i);
      numero = Number(caracter);
      somatorio = somatorio + (numero * j);
      j--;
    }
    resto = somatorio % 11;
    digito2 = 11 - resto;
    if (digito2 > 9) {
      digito2 = 0;
    }
    cpfAux = cpfAux + digito2;
    if (cpf != cpfAux) {
      return false;
    }
    else {
      return true;
    }
  }

  cnpj( cnpj:string):boolean {
    var cnpj = cnpj.replace(/[^\d]+/g, '');

    if (cnpj == '')
     return false;

    if (cnpj.length != 14)
      return false;

    // Elimina CNPJs invalidos conhecidos
    if (cnpj == "00000000000000" ||
      cnpj == "11111111111111" ||
      cnpj == "22222222222222" ||
      cnpj == "33333333333333" ||
      cnpj == "44444444444444" ||
      cnpj == "55555555555555" ||
      cnpj == "66666666666666" ||
      cnpj == "77777777777777" ||
      cnpj == "88888888888888" ||
      cnpj == "99999999999999")
      return false;

    // Valida DVs
    var tamanho:number = cnpj.length - 2
    var numeros:string = cnpj.substring(0, tamanho);
    var digitos:string = cnpj.substring(tamanho);
    var soma:number = 0;
    var pos:number = tamanho - 7;
    for (var i:number = tamanho; i >= 1; i--) {
      soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
      if (pos < 2)
        pos = 9;
    }
    var resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado != parseInt(digitos.charAt(0)))
      return false;

    tamanho = tamanho + 1;
    numeros = cnpj.substring(0, tamanho);
    soma = 0;
    pos = tamanho - 7;
    for (i = tamanho; i >= 1; i--) {
      soma += parseInt(numeros.charAt(tamanho - i)) * pos--;
      if (pos < 2)
        pos = 9;
    }
    resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado != parseInt(digitos.charAt(1)))
      return false;

    return true;

  }


  validaCNS(vlrCNS:any) {
    // Formulário que contem o campo CNS
    var soma = 0;
    var resto = 0;
    var dv = 0;
    var pis = '';
    var resultado = '';
    var tamCNS = vlrCNS.length;
    if ((tamCNS) != 15) {
      return false;
    }
    pis = vlrCNS.substring(0, 11);
    soma = (((Number(pis.substring(0, 1))) * 15) +
      ((Number(pis.substring(1, 2))) * 14) +
      ((Number(pis.substring(2, 3))) * 13) +
      ((Number(pis.substring(3, 4))) * 12) +
      ((Number(pis.substring(4, 5))) * 11) +
      ((Number(pis.substring(5, 6))) * 10) +
      ((Number(pis.substring(6, 7))) * 9) +
      ((Number(pis.substring(7, 8))) * 8) +
      ((Number(pis.substring(8, 9))) * 7) +
      ((Number(pis.substring(9, 10))) * 6) +
      ((Number(pis.substring(10, 11))) * 5));
    resto = soma % 11;
    dv = 11 - resto;
    if (dv == 11) {
      dv = 0;
    }
    if (dv == 10) {
      soma = (((Number(pis.substring(0, 1))) * 15) +
        ((Number(pis.substring(1, 2))) * 14) +
        ((Number(pis.substring(2, 3))) * 13) +
        ((Number(pis.substring(3, 4))) * 12) +
        ((Number(pis.substring(4, 5))) * 11) +
        ((Number(pis.substring(5, 6))) * 10) +
        ((Number(pis.substring(6, 7))) * 9) +
        ((Number(pis.substring(7, 8))) * 8) +
        ((Number(pis.substring(8, 9))) * 7) +
        ((Number(pis.substring(9, 10))) * 6) +
        ((Number(pis.substring(10, 11))) * 5) + 2);
      resto = soma % 11;
      dv = 11 - resto;
      resultado = pis + "001" + String(dv);
    } else {
      resultado = pis + "000" + String(dv);
    }
    if (vlrCNS != resultado) {
      return false;
    } else {
      return true;
    }
  }

  public FormatarTelefone(telefone: string) {
    
    telefone = telefone.replace(/\D/g, "");
    telefone = telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
    telefone = telefone.replace(/(\d)(\d{4})$/, "$1-$2");
    
    return telefone;
  }

  public LimparMascara(filtro: string) : string {
    var ret = '';

    if (filtro.includes('@')) {
      ret = filtro.replace(/[^a-zA-Z0-9@._\-\s]/g, ''); // Caso Email
    } else {  
      if (/[a-zA-Z]/.test(filtro)) {   
        ret = filtro.replace(/[^a-zA-Z0-9\s]/g, ''); // Caso Nome
      } else {  
        ret = filtro.replace(/[^a-zA-Z0-9]/g, '');
      }
    }

    return ret.trim();
  }

}

