<div class="container">
    <div class="formulario-container">
      <form>
        <!-- INFORMAÇÕES BÁSICAS -->
        <div class="card-secao">
          <div class="secao-header">
            <mat-icon>description</mat-icon>
            <h3>Informações Básicas</h3>
          </div>
          <div class="secao-content">
            <div class="campo-grupo">
              <!-- Número da guia no prestador -->
              <mat-form-field appearance="outline">
                <mat-label>Nº da Guia no Prestador</mat-label>
                <input formControlName="numGuiaPrestador" matInput placeholder="Ex.: 123456">
              </mat-form-field>
  
              <!-- Número da guia principal -->
              <mat-form-field appearance="outline">
                <mat-label>Nº da Guia Principal</mat-label>
                <input formControlName="numGuiaPrincipal" matInput placeholder="Ex.: 654321">
              </mat-form-field>
            </div>
            
            <div class="campo-grupo">
              <!-- Número da guia na operadora -->
              <mat-form-field appearance="outline">
                <mat-label>Nº da Guia na Operadora</mat-label>
                <input formControlName="numGuiaOperadora" matInput placeholder="Ex.: 987654">
              </mat-form-field>
  
              <!-- Senha -->
              <mat-form-field appearance="outline">
                <mat-label>Senha</mat-label>
                <input formControlName="numSenha" matInput placeholder="Senha">
              </mat-form-field>
            </div>
          </div>
        </div>
  
        <!-- DADOS DA CLÍNICA -->
        <div class="card-secao">
          <div class="secao-header">
            <mat-icon>local_hospital</mat-icon>
            <h3>Dados da Clínica</h3>
          </div>
          <div class="secao-content">
            <div class="campo-grupo">
              <!-- Nome da Clínica -->
              <mat-form-field appearance="outline" class="campo-full">
                <mat-label>Nome da Clínica</mat-label>
                <input formControlName="desClinica" matInput placeholder="Ex.: Clínica XYZ">
              </mat-form-field>
            </div>
            
            <div class="campo-grupo">
              <!-- Nome do Médico -->
              <mat-form-field appearance="outline" class="campo-maior">
                <mat-label>Nome do Médico</mat-label>
                <input formControlName="nomeMedico" matInput placeholder="Dr. João Silva">
              </mat-form-field>
  
              <!-- CBO -->
              <mat-form-field appearance="outline" class="campo-menor">
                <mat-label>CBO</mat-label>
                <input matInput placeholder="Ex.: 225125">
              </mat-form-field>
            </div>
          </div>
        </div>
  
        <!-- DADOS DO ATENDIMENTO -->
        <div class="card-secao">
          <div class="secao-header">
            <mat-icon>assignment</mat-icon>
            <h3>Dados do Atendimento</h3>
          </div>
          <div class="secao-content">
            <div class="campo-grupo">
              <!-- Tipo de Atendimento -->
              <mat-form-field appearance="outline">
                <mat-label>Tipo de Atendimento</mat-label>
                <mat-select formControlName="idTipoAtendimento">
                  <mat-option *ngFor="let tipo of listaTipoAtendimento" [value]="tipo.id">
                    {{ tipo.des }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
  
              <!-- Indicação de Acidente -->
              <mat-form-field appearance="outline">
                <mat-label>Indicação de Acidente</mat-label>
                <mat-select formControlName="idIndicacaoAcidente">
                  <mat-option *ngFor="let acidente of listaIndicacaoAcidente" [value]="acidente.id">
                    {{ acidente.des }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            
            <div class="campo-grupo">
              <!-- Regime de Atendimento -->
              <mat-form-field appearance="outline">
                <mat-label>Regime de Atendimento</mat-label>
                <mat-select formControlName="idRegimeAtendimento">
                  <mat-option *ngFor="let regime of listaRegimeAtendimento" [value]="regime.id">
                    {{ regime.des }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
  
              <!-- Caráter de Atendimento -->
              <mat-form-field appearance="outline">
                <mat-label>Caráter de Atendimento</mat-label>
                <mat-select formControlName="idCaraterAtendimento">
                  <mat-option *ngFor="let option of enumCaraterAtendimentoDescricao" [value]="option.id">
                    {{ option.des }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            
            <div class="campo-grupo">
              <!-- Saúde Ocupacional -->
              <mat-form-field appearance="outline" class="campo-full">
                <mat-label>Saúde Ocupacional</mat-label>
                <mat-select formControlName="idSaudeOcupacional">
                  <mat-option *ngFor="let saude of listaSaudeOcupacional" [value]="saude.id">
                    {{ saude.des }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </div>
  
        <!-- DETALHES DA SOLICITAÇÃO -->
        <div class="card-secao">
          <div class="secao-header">
            <mat-icon>event_note</mat-icon>
            <h3>Detalhes da Solicitação</h3>
          </div>
          <div class="secao-content">
            <div class="campo-grupo">
              <!-- Data de Autorização -->
              <mat-form-field appearance="outline">
                <mat-label>Data de Autorização</mat-label>
                <input type="date" formControlName="dtaAutorizacao" matInput>
              </mat-form-field>
  
              <!-- Validade da Senha -->
              <mat-form-field appearance="outline">
                <mat-label>Validade da Senha</mat-label>
                <input type="date" formControlName="dtaValidadeSenha" matInput>
              </mat-form-field>
  
              <!-- Atendimento ao RN -->
              <mat-form-field appearance="outline">
                <mat-label>Atendimento ao RN</mat-label>
                <mat-select formControlName="flgRn">
                  <mat-option value="true">Sim</mat-option>
                  <mat-option value="false">Não</mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
        </div>
  
        <!-- DADOS DA SOLICITAÇÃO -->
        <div class="card-secao">
          <div class="secao-header">
            <mat-icon>assignment_turned_in</mat-icon>
            <h3>Dados da Solicitação</h3>
          </div>
          <div class="secao-content">
            <div class="campo-grupo">
              <!-- Caráter de Atendimento -->
              <mat-form-field appearance="outline">
                <mat-label>Caráter de Atendimento</mat-label>
                <input formControlName="caraterAtendimento" matInput>
              </mat-form-field>
  
              <!-- Data/Hora da Solicitação -->
              <mat-form-field appearance="outline">
                <mat-label>Data/Hora da Solicitação</mat-label>
                <input type="datetime-local" formControlName="dataHoraSolicitacao" matInput>
              </mat-form-field>
            </div>
            
            <div class="campo-grupo">
              <!-- Indicação Clínica -->
              <mat-form-field appearance="outline" class="campo-full">
                <mat-label>Indicação Clínica</mat-label>
                <textarea formControlName="indicacaoClinica" matInput rows="3"></textarea>
              </mat-form-field>
            </div>
          </div>
        </div>
      </form>
    </div>
    
    <!-- BOTÕES DE AÇÃO -->
    <div class="acoes-container">
      <button class="btn-salvar" (click)="SalvarGuiaTiss()">
        <mat-icon>save</mat-icon>
        <span>Adicionar</span>
      </button>
    </div>
  </div>