import { Pipe, PipeTransform } from "@angular/core";
import { DatePipe } from "@angular/common";

export const cDateFormatTypes = {
    dateInput: "yyyy-MM-dd",
    dateLabel: "dd/MM/yyyy",
    dateTime: "dd/MM/yyyy HH:mm:ss",
    dateTimeOutput: "yyyy-MM-dd HH:mm:ss"
};

@Pipe({
    name: "dateFormat"
})
export class DateFormatPipe implements PipeTransform {
    constructor(private datePipe: DatePipe) { }

    transform(value: any, format: string, timezone?: string) {
        
        
        if (value) {
            // && value.toString().endsWith("Z")) {

            if (format === cDateFormatTypes.dateInput) {
                if (value.substring(0, 4) > 1000) {
                    return value.substring(0, 4) + "-" + value.substring(5, 7) + "-" + value.substring(8, 10);
                } else {
                    return value;
                }
            }

            if (timezone) {
                return this.datePipe.transform(value, format, timezone);
            } else {
                return this.datePipe.transform(value, format);
            }
        } else {
            return value;
        }
    }
}

@Pipe({
    name: "dateInputFormat",
    standalone: true
})
export class DateInputFormatPipe extends DateFormatPipe implements PipeTransform {
    constructor(datePipe: DatePipe) {
        super(datePipe);
    }

    transform(value: any): any {
        return super.transform(value, cDateFormatTypes.dateInput, "Z");
    }
}

@Pipe({
    name: "dateTimeFormat",
})
export class DateTimeFormatPipe extends DateFormatPipe implements PipeTransform {
    constructor(datePipe: DatePipe) {
        super(datePipe);
    }

    transform(value: any): any {
        return super.transform(value, cDateFormatTypes.dateTime);
    }
}
