<div class="container mother-div">
  <!-- MODAL DE CONFIRMAÇÃO -->
  <div *ngIf="flgmodal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Confirmação</h3>
      </div>
      <div class="modal-body">
        <p class="modal-text">Deseja realmente excluir esse item?</p>
      </div>
      <div class="modal-footer">
        <button class="btn-cancelar" (click)="Excluir(false)">Cancelar</button>
        <button class="btn-confirmar btn-excluir" (click)="Excluir(true)">Excluir</button>
      </div>
    </div>
  </div>

  <mat-card class="card-principal">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">post_add</span>
      </div>
      <h2 class="header-title">Cadastro de Itens</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">
      <div class="busca-container">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>Buscar</mat-label>
          <input matInput type="search" [(ngModel)]="filtroBusca" (keyup)="filtrarItem()">
          <button mat-icon-button matSuffix class="btn-busca">
            <mat-icon>search</mat-icon>
          </button>
        </mat-form-field>
      </div>
      
      <div class="adicionar-container">
        <button class="btn-adicionar" (click)="adicionaritens()">
          <mat-icon>add</mat-icon>
          <span>Adicionar Itens</span>
        </button>
      </div>
    </div>

    <!-- LISTA DE ITENS -->
    <div class="lista-container">
      <div class="lista-scroll">
        <div class="item-card" *ngFor="let Item of listaItens">
          <!-- INFO DO ITEM -->
          <div class="item-info">
            <div class="item-avatar">
              <img src="{{ ImagemPessoa }}" class="img-circle" alt="Foto do item">
            </div>
            <div class="item-detalhes">
              <div class="info-item">
                <mat-icon>category</mat-icon>
                <span class="tipo">Tipo: {{Item.tipoOrientacao}}</span>
              </div>
              <div class="info-item">
                <mat-icon>inventory_2</mat-icon>
                <span class="nome">Item: {{Item.nomeItem}}</span>
              </div>
            </div>
          </div>

          <!-- DATA DE CADASTRO -->
          <div class="item-data">
            <div class="data-item">
              <label class="data-label">Data de Cadastro</label>
              <span class="data-valor">{{Item.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
            </div>
          </div>

          <!-- AÇÕES -->
          <div class="item-acoes">
            <button mat-icon-button matTooltip="Editar" (click)="Editar(Item.idItem!)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Excluir" (click)="excluirItem(Item.idItem!)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="listaItens?.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhum item encontrado</p>
        </div>
      </div>
    </div>
  </mat-card>
</div>