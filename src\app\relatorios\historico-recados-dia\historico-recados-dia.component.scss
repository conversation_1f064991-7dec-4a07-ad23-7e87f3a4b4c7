@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

// Variáveis - Esquema de cores verde
$primary-color: #2E8B57; // Verde principal
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; 
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Base Styles */
body {
  font-family: 'Cairo', sans-serif;
  background-color: transparent;
  color: $text-primary;
  height: 100%;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
}

/* Main Card */
.mother-div {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: $border-radius !important;
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  overflow: hidden !important;
}

/* Containers */
.white-container {
  background-color: $card-bg;
  margin: 15px;
  border-radius: $border-radius;
  padding: 20px;
}

/* Header Styles */
.spacer-card {
  padding: 0 !important;
  margin-bottom: 0 !important;
}

.header-container {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: $card-bg;
  border-radius: $border-radius;
  margin: 15px;
}

.icon-title {
  margin: 0 !important;
  background: transparent !important;
  color: $primary-color !important;
  align-self: center;
  vertical-align: middle;
  border-radius: 100% !important;
  font-size: 30px !important;
  border: 2px solid $primary-color !important;
  height: 38px !important;
  width: 38px !important;
  text-align: center;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-content {
  font-size: 24px;
  align-self: center;
  vertical-align: middle;
  margin-left: 10px;
  font-weight: 500;
  color: $primary-color;
  font-family: 'Cairo', sans-serif;
}

/* Dividers */
mat-divider {
  margin: 0 !important;
  border-color: $border-color !important;
}

/* Content Section */
.content-section {
  margin-top: 20px;
}

.filters-row {
  display: flex;
  flex-wrap: wrap;
}

/* Form Fields */
.select-field {
  padding: 5px;
  margin-bottom: 15px;
}

.date-field {
  padding: 5px;
  position: relative;
  margin-bottom: 15px;
}

mat-form-field {
  width: 100%;
}

.error-text {
  font-size: 11px;
  color: $error-color;
  position: absolute;
  left: 20px;
  top: 33px;
}

/* Button Styles */
.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 10px;
}

.search-button {
  background-color: $primary-color !important;
  color: white !important;
  box-shadow: 0 3px 1px -2px rgba(0,0,0,0.2), 0 2px 2px rgba(0,0,0,0.14), 0 1px 5px rgba(0,0,0,0.12);
  padding: 5px 16px;
  font-size: 14px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  transition: $transition;
}

.search-button:hover {
  background-color: $primary-dark !important;
  transform: translateY(-2px);
}

.search-button mat-icon {
  margin-right: 8px;
  margin-bottom: 0 !important;
}

/* Table Styles */
.table-container {
  background-color: $card-bg;
  border-radius: $border-radius;
  overflow: hidden;
  margin-top: 20px;
}

.results-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 10px;
}

.result-row {
  background-color: $secondary-light;
  border-radius: $border-radius;
  transition: $transition;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
  border: none;
}

.result-row:hover {
  box-shadow: $box-shadow;
  transform: translateY(-2px);
  background-color: $secondary-light;
}

.medico-cell, .recado-cell, .criador-cell, .data-cell, .visualizar-cell {
  padding: 12px 8px;
  font-weight: normal;
  border: none;
}

.medico-cell {
  width: 20%;
}

.recado-cell {
  width: 25%;
}

.criador-cell {
  width: 15%;
}

.data-cell {
  width: 15%;
}

.visualizar-cell {
  width: 25%;
}

.cell-label {
  font-weight: 600;
  color: $primary-color;
  margin-right: 5px;
}

.recado-do-dia {
  font-size: 12px;
  font-weight: 600;
  text-decoration-line: underline;
  color: $primary-dark;
}

.visualizar-icon {
  vertical-align: middle;
  color: $primary-color;
  cursor: pointer;
  margin-right: 5px;
}

.visualizar-btn {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.visualizar-btn span {
  margin-left: 5px;
}

/* Load More Button */
.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-bottom: 10px;
}

.load-more-button {
  background-color: $primary-color !important;
  color: white !important;
  padding: 0 20px;
  border-radius: $border-radius;
  transition: $transition;
}

.load-more-button:hover {
  background-color: $primary-dark !important;
  transform: translateY(-2px);
}

/* Modal Styles */
.modal-container {
  border-radius: $border-radius;
  overflow: hidden;
}

.modal-header {
  background-color: $primary-light;
  padding: 20px;
  text-align: center;
}

.success-header {
  background-color: $primary-color;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-title {
  color: $primary-color;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.success-header .modal-title {
  color: white;
}

.modal-content {
  background-color: $card-bg;
  padding: 20px;
}

.message-field {
  width: 100%;
}

.message-textarea {
  min-height: 200px !important;
  max-height: 250px !important;
  color: $text-primary !important;
}

.modal-actions {
  background-color: $card-bg;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.modal-button {
  padding: 0 20px !important;
  height: 40px !important;
  border-radius: $border-radius !important;
  margin-bottom: 10px;
}

.ok-button, .close-button {
  background-color: $primary-color !important;
  color: white !important;
}

/* Custom styles for ng-select */
::ng-deep .ng-select .ng-select-container {
  border-color: $border-color !important;
  border-radius: 4px !important;
  min-height: 45px !important;
}

::ng-deep .ng-select.ng-select-focused .ng-select-container {
  border-color: $primary-color !important;
  box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25) !important;
}

::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
  background-color: $primary-light !important;
  color: $primary-color !important;
}

::ng-deep .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
  background-color: rgba(46, 139, 87, 0.1) !important;
  color: $primary-color !important;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: $secondary-color;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: $primary-color;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: $primary-dark;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .result-row {
    padding: 10px;
  }
  
  .medico-cell, .recado-cell, .criador-cell, .data-cell, .visualizar-cell {
    width: 50%;
    padding: 5px;
  }
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    text-align: center;
  }
  
  .icon-title {
    margin-bottom: 10px !important;
  }
  
  .title-content {
    margin-left: 0;
  }
  
  .no-mobile {
    display: none;
  }
  
  .result-row {
    flex-direction: column;
  }
  
  .medico-cell, .recado-cell, .criador-cell, .data-cell, .visualizar-cell {
    width: 100%;
    padding: 5px;
  }
}

@media (max-width: 480px) {
  .mother-div {
    margin: 10px !important;
  }
  
  .white-container, .header-container {
    margin: 10px;
    padding: 15px;
  }
  
  .icon-title {
    font-size: 24px !important;
    height: 32px !important;
    width: 32px !important;
  }
  
  .title-content {
    font-size: 20px;
  }
}