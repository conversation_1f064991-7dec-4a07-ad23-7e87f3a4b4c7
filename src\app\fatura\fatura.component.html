<div class="fatura-container">
    <div class="card main-card">
      <div class="card-header">
        <div class="header-left">
          <div class="icon-container">
            <span class="material-icons">receipt</span>
          </div>
          <h1 class="page-title">Fatura</h1>
        </div>
        <button class="btn btn-link" onclick='history.go(-1)'>
          <span class="material-icons">arrow_back</span>
        </button>
      </div>
  
      <div class="card-body">
        <!-- Informações básicas da fatura -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">Informações da Fatura</h2>
          </div>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Número</mat-label>
                  <input matInput placeholder="Número" id="Numero" type="number" [(ngModel)]="objFatura.desNumero">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Descrição</mat-label>
                  <input matInput placeholder="Descrição" [(ngModel)]="objFatura.descricao">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Tipo de Fatura</mat-label>
                  <mat-select [(ngModel)]="objFatura.idTipoFatura">
                    <mat-option *ngFor="let item of ListaTiposFatura" [value]="item.id">
                      {{item.des}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            <div class="form-row">
                <div class="form-group col-md-4">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Data Abertura</mat-label>
                    <input matInput [(ngModel)]="objFatura.dtaCadastro"
                    placeholder="Data Abertura" type="date"
                    (keyup)="validadores.mascaraData($event)" maxlength="10">
                </mat-form-field>
                </div>
                <div class="form-group col-md-4">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Data Fechamento</mat-label>
                    <input matInput [(ngModel)]="objFatura.dtaFechamento"
                    placeholder="Data Fechamento" type="date"
                    (keyup)="validadores.mascaraData($event)" maxlength="10">
                  </mat-form-field>
                </div>
                
                <div class="form-group col-md-4">
                  <mat-form-field appearance="outline" class="full-width">
                    <mat-label>Data Previsão de Pagamento</mat-label>
                    <input matInput [(ngModel)]="objFatura.dtaPrevisaoPagamento"
                    placeholder="Data Previsão de Pagamento" type="date"
                    (keyup)="validadores.mascaraData($event)" maxlength="10">
                  </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de valores ou detalhes específicos -->
        <div class="section">
            <div class="section-header">
              <h2 class="section-title">Detalhes Específicos</h2>
            </div>
            <div class="section-content">
              <div class="form-row">
                <div class="form-group" [ngClass]="{'col-md-6': true}">
                  <div [ngSwitch]="tipoSelecionado">
                    <div *ngSwitchCase="1">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Convênio</mat-label>
                        <mat-select [(ngModel)]="objFatura.idConvenio">
                          <mat-option *ngFor="let item of listaConvenios" [value]="item.idConvenio">
                            {{item.desConvenio}}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    
                    <div *ngSwitchCase="2">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Plano</mat-label>
                        <mat-select [(ngModel)]="objFatura.idPlano">
                          <mat-option *ngFor="let item of listaConvenios" [value]="item.idConvenio">
                            {{item.desConvenio}}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    
                    <div *ngSwitchCase="3">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Profissional</mat-label>
                        <mat-select [(ngModel)]="objFatura.idProfessional">
                          <mat-option *ngFor="let item of ListaMedicos" [value]="item.idConvenio">
                            {{item.desConvenio}}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                    
                    <div *ngSwitchCase="4">
                      <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Tipo de Comissão</mat-label>
                        <mat-select [(ngModel)]="objFatura.idTipoComissao">
                          <mat-option *ngFor="let item of listaConvenios" [value]="item.idConvenio">
                            {{item.desConvenio}}
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                
                <div class="form-group col-md-6">
                  <div class="auto-reopen-option">
                    <div class="auto-reopen-text">
                      <span>Ao fechar essa fatura, abrir automaticamente uma nova</span>
                    </div>
                    <app-toggle></app-toggle>
                  </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <button class="btn btn-success" (click)="SalvarFatura()">
              <span class="material-icons">save</span>
              Salvar
            </button>
          </div>
    </div>
</div>