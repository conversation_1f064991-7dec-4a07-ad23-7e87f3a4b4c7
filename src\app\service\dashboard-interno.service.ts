import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { DadosDashboardInt } from '../model/dashboard-interno';
import { catchError, of, Observable } from 'rxjs';
import { SpinnerService } from './spinner.service';

@Injectable({
  providedIn: 'root'
})
export class DashboardInternoService {

constructor(
  private http: HttpClient,
  private spinner: SpinnerService
) { }

  public BuscarErros(){
    this.spinner.show();
    return this.http.get(environment.apiEndpoint + '/dashboardInterno/LogErros').pipe(
      catchError((e) => {
        return of("Erros no carregamento", e);
      })
    );
  }
  
  public GetDashboardInt(): Observable<DadosDashboardInt> {
    this.spinner.show();
    return this.http.get<DadosDashboardInt>(environment.apiEndpoint + '/dashboardInterno/DashInterno');
  }
}
