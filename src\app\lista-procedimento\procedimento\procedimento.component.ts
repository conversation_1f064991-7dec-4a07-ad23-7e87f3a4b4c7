import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog as MatDialog, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { FaturaModelview } from 'src/app/model/fatura';
import { ProcedimentoModelview } from 'src/app/model/procedimento';
import { ConvenioService } from 'src/app/service/convenio.service';
import { FaturaService } from 'src/app/service/fatura.service';
import { ProcedimentoService } from 'src/app/service/procedimento.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { EnumStatusProcedimento, EnumStatusProcedimentoDescricao } from 'src/app/Util/EnumProcedimento';
import { ValidadoreseMascaras } from 'src/app/Util/validadores';
import { ListaProcedimentoComponent } from '../lista-procedimento.component';
import { ModalTemplateComponent } from 'src/app/Modais/modal-template/modal-template.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';

@Component({
    selector: 'app-procedimento',
    templateUrl: './procedimento.component.html',
    styleUrls: ['./procedimento.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      ModalTemplateComponent,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatFormFieldModule,
      MatIcon,
      MatSelectModule,
      MatRadioModule
    ]
})
export class ProcedimentoComponent {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private procedimentoService: ProcedimentoService,
    private faturaService: FaturaService,
    private formBuilder: FormBuilder,
    private matDialog: MatDialog,
    public dialogRef: MatDialogRef<ProcedimentoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { idConsulta: number; idConvenio: number; idFatura: number; idProcedimento: number; }
  ) { 
      this.idClinica = this.usuarioLogadoService.getIdUltimaClinica()!;
      this.idConsulta = this.data.idConsulta;
      this.idProcedimento = this.data.idProcedimento;
      this.idFatura = this.data.idFatura;
      this.idConvenio = this.data.idConvenio;
  }

  formProcedimento?: FormGroup;
  objProcedimento: ProcedimentoModelview = new ProcedimentoModelview();

  idConsulta: number;
  idProcedimento: number;
  idFatura: number;
  idConvenio: number;
  idClinica: number;
  
  listaStatusProcedimento = EnumStatusProcedimentoDescricao;
  listaFatura: FaturaModelview[] = [];
  listaConvenios: any = [];

  async ngOnInit() {
    

    if (this.idProcedimento > 0){
      await this.GetDadosProcedimento(this.idProcedimento)
    }

    this.GetListaFatura();
    this.GetListaConvenios();
    this.CriarFormGroup();
  }

  CriarFormGroup(){
    const dataHj = new Date();
    // const dataFormateada = dataHj.toDateString().split('T')[0];
    const horaFormateada = dataHj.toTimeString().slice(0, 5);

    if (this.objProcedimento.idProcedimento! > 0){
      this.formProcedimento = this.formBuilder.group({
        dtaInicio:            [this.objProcedimento.dtaInicio, Validators.required],
        dtaPeriodoInicio:     [this.objProcedimento.dtaPeriodoInicio, Validators.required],
        dtaPeriodoFim:        [this.objProcedimento.dtaPeriodoFim, Validators.required],
        flgLancarItens:       [this.objProcedimento.flgLancarItens],
        idFatura:             [this.objProcedimento.idFatura, Validators.required],
        idConvenio:           [this.objProcedimento.idConvenio, Validators.required],
        codProcedimento:      [this.objProcedimento.codProcedimento, Validators.required],
        desProcedimento:      [this.objProcedimento.desProcedimento, Validators.required],
        qtd:                  [this.objProcedimento.qtd, [Validators.required, Validators.min(1)]],
        vlrM2filme:           [this.objProcedimento.vlrM2filme, Validators.required],
        vlrCustoOp:           [this.objProcedimento.vlrCustoOp, Validators.required],
        vlrHonorarios:        [this.objProcedimento.vlrHonorarios, Validators.required],
        vlrParticular:        [this.objProcedimento.vlrParticular, Validators.required],
        vlrConvenio:          [this.objProcedimento.vlrConvenio, Validators.required],
        idStatusProcedimento: [this.objProcedimento.idStatusProcedimento, Validators.required],
        idConsulta:           [this.objProcedimento.idConsulta, Validators.required]
      });
    }

    else{
      this.formProcedimento = this.formBuilder.group({
        dtaInicio:            [dataHj, Validators.required],
        dtaPeriodoInicio:     [horaFormateada, Validators.required],
        dtaPeriodoFim:        [horaFormateada, Validators.required],
        flgLancarItens:       [false],
        idFatura:             [this.idFatura, Validators.required],
        idConvenio:           [this.idConvenio, Validators.required],
        codProcedimento:      ['', Validators.required],
        desProcedimento:      ['', Validators.required],
        qtd:                  [1, [Validators.required, Validators.min(1)]],
        vlrM2filme:           [0, Validators.required],
        vlrCustoOp:           [0, Validators.required],
        vlrHonorarios:        [0, Validators.required],
        vlrParticular:        [0, Validators.required],
        vlrConvenio:          [0, Validators.required],
        idStatusProcedimento: [EnumStatusProcedimento.Solicitado, Validators.required],
        idConsulta:           [this.idConsulta, Validators.required]
      });
    }

  }

  async SalvarProcedimento(){
    if (this.formProcedimento!.valid){
      this.spinner.show();
      this.objProcedimento = { ...this.formProcedimento!.value };
      
      await this.procedimentoService.SalvarProcedimento(this.objProcedimento).subscribe((ret) => {
        ret;        
        this.snackBarAlert.sucessoSnackbar('Procedimento salvo com sucesso.')
        // this.AbrirListaProcedimento();
        this.dialogRef.close();
      }, err => {
        err;
        this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando salvar o procedimento.')
      })
      this.spinner.hide();
    }
    else{
      this.snackBarAlert.falhaSnackbar("Por favor preencher todos os campos.")
    }

  }

  async GetDadosProcedimento(idProcedimento: number){
    this.spinner.show();
    
    await this.procedimentoService.GetDadosProcedimento(idProcedimento).subscribe((ret) => {
      this.objProcedimento = ret;
      this.CriarFormGroup();
    }, err => {
      err
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando salvar o procedimento.')
    })
    this.spinner.hide();
  }

  async GetListaFatura(){
    this.spinner.show();
    await this.faturaService.GetListaFatura(false).subscribe((ret) => {
      this.listaFatura = ret;
    }, err => {
      err;
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar as faturas.')
    })
    this.spinner.hide();
  }

  async GetListaConvenios() {
    await this.convenioService.getConvenios(this.idClinica).subscribe((ret) => {
      this.listaConvenios = ret;
    })
  }

  GetValorTotal(){
    return this.objProcedimento.vlrM2filme! 
    + this.objProcedimento.vlrCustoOp! 
    + this.objProcedimento.vlrHonorarios!
    + this.objProcedimento.vlrParticular!
    + this.objProcedimento.vlrConvenio!
  }
  
  FecharModal() {
    this.dialogRef.close();
  }

  AbrirListaProcedimento(){
    this.dialogRef.close();
    this.matDialog.open(ListaProcedimentoComponent, {
      width: '710px',
      height: '90vh',
      data: this.idConsulta
    });
  }
}
