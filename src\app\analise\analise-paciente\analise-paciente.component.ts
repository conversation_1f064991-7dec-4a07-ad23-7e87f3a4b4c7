import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { ExamesAnaliseModelView } from 'src/app/model/analise';
import { analiseFormularioModel, listaPerguntasRespostas, listaRespostas } from 'src/app/model/formularios';
import { AnaliseService } from 'src/app/service/analise.service';
import { FormulariosService } from 'src/app/service/formulario.service';
import { SpinnerService } from 'src/app/service/spinner.service';

@Component({
    selector: 'app-analise-paciente',
    templateUrl: './analise-paciente.component.html',
    styleUrls: ['./analise-paciente.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCard,
      NgxSmartModalModule,
      TranslateModule,
      MatIconModule
    ]
})
export class AnalisePacienteComponent implements OnInit {

  constructor(
    private formulariosService: FormulariosService,
    private analiseService: AnaliseService,
    public ngxSmartModalService: NgxSmartModalService,
    private spinner: SpinnerService,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBar: AlertComponent,
  ) { }

  ImagemPessoa: any = "assets/build/img/userdefault.png";
  listaAnalise: any = [];
  naotemanalises: boolean = false;
  objExame = new ExamesAnaliseModelView();
  formsList: analiseFormularioModel[] = [];
  formularioSelecionadoModal = new analiseFormularioModel();
  listaPerguntas: listaPerguntasRespostas[] = [];
  listaPerguntaResposta: listaRespostas[] = [];

  idUsuarioLogado?: number;

  ngOnInit() {
    this.carregaFormularios();
    this.carregarAnalises();
  }

  carregaFormularios() {
    this.spinner.show();

    this.formulariosService.GetFormularioResponido(this.usuarioLogadoService.getIdPessoa()!).subscribe((ret) => {
      
      
      this.formsList = []
      this.formsList = ret;
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  carregarAnalises() {
    this.spinner.show();
    this.analiseService.GetAnalisesPaciente(this.usuarioLogadoService.getIdPessoa()!).subscribe((retorno) => {

      this.listaAnalise = []
      

      this.listaAnalise = retorno;
      if (this.listaAnalise.length == 0)
        this.naotemanalises = true;


      
      this.spinner.hide();
    }, err => {
      console.error(err)
    })

  }

  SubirArquivoConsulta(arquivo:any, exame:any) {

    this.objExame.idAnalise = exame.idAnalise
    this.objExame.idExame = exame.idExame
    this.readThis(arquivo.target);
  }
  readThis(inputValue: any): void {
    
    var file: File = inputValue.files[0];
    var myReader: FileReader = new FileReader();
    this.objExame.nmeArquivo = file.name;

    myReader.onloadend = () => {
      this.objExame.Arquivobase64 = myReader.result;
      this.salvarArquivo();
    }
    myReader.readAsDataURL(file);
  }
  salvarArquivo() {
    this.analiseService.SalvarArquivo(this.objExame).subscribe(() => {
      this.snackBar.sucessoSnackbar("Arquivo Carregado com sucesso");
      this.carregarAnalises();
      this.spinner.hide();
    }, err => {
      this.snackBar.falhaSnackbar("Erro ao salvar");
      console.error(err);
      this.spinner.hide();
    });
  }


  preparaFormulario(event: analiseFormularioModel) {
    
    this.formularioSelecionadoModal = event;
    if (!event.flgRespondido) {
      this.formulariosService.GetPerguntasParaResposta(event.idFormulario!, event.idAnalise!).subscribe((ret) => {
        this.listaPerguntaResposta = ret;
        this.abreModalFormulario('formularioNaoRespondido');
        this.spinner.hide();
      })
    }
    else {
      this.formulariosService.getPerguntasRespostas(event.idFormulario!, event.idAnalise!).subscribe((ret) => {
        this.listaPerguntas = ret;
        this.abreModalFormulario('formularioRespondido');
        this.spinner.hide();
      })
    }
  }

  abreModalFormulario(modal: string) {
    this.ngxSmartModalService.getModal(modal).open();
  }
  fechaModalFormulario(modal: string) {
    this.ngxSmartModalService.getModal(modal).close();
  }

  Salvar() {
    let salvar: boolean = true;
    let listaVazia: boolean = true;
    let erroMsg: string;
    this.listaPerguntaResposta.forEach(ele => {
      listaVazia = false;
      if (ele.idFormulario! < 1 && ele.idAnalise! < 1 && ele.idPerguntaFormulario! < 1) {
        salvar = false;
        erroMsg = "Erro no salvamento, ouvr um problema na construção dos dados"
      }

      if (ele.resposta == null || ele.resposta == "") {
        salvar = false;
        erroMsg = "Erro no salvamento, responda a todas as perguntas."
      }

    });

    if (salvar && !listaVazia) {
      this.formulariosService.SalvarRespostas(this.listaPerguntaResposta).subscribe((ret) => {
        if (ret) {
          this.snackBar.falhaSnackbar("Salvo com sucesso");
          this.carregaFormularios();
          this.spinner.hide();
        }
        else {
          this.snackBar.falhaSnackbar("Falha no salvamento");
          this.spinner.hide();
        }
      }, () => {
        this.snackBar.falhaSnackbar("Erro ao salvar");
        this.spinner.hide();
      })
      this.fechaModalFormulario('formularioNaoRespondido')
      this.spinner.hide();
    }
    else {
      this.snackBar.falhaSnackbar(erroMsg!);
      this.spinner.hide();
    }

  }
}
