.full-height{
  height: 150vh;
  width: 100vw;
}

ul li:before {
	left: 0;
	content: '\2022';
	color: #000;
	position: absolute;
  font-size: 20px;
  // margin-top: -10px;
}
.bar {
  width: 8px;
  height: 275px;
  border: 1px solid #AFA5A5;
  position: relative;
  margin: auto;
}
.bar::before {
  content: '';
  position: absolute;
  top: 283px;
  background-image: url(https://nsassets4.s3.amazonaws.com/landingpages/sound.png);
  background-repeat: no-repeat;
  left: -2px;
  width: 16px;
  height: 16px;
}
@media (max-width: 767px){
  .menor{
    padding-top: 30px
  }
  .full-height{
    height: 150vh;
    width: 100vw;
  }
}
// Galaxy S3
@media (width: 360px) and (height: 640px) {
    .Drawer-esquerdo {
      height: 162vw;
    }
  }
  // LG Optimus L70
  @media (width: 384px) and (height: 640px) {
    .Drawer-esquerdo {
      height: 152vw;
    }
  }
  // Laptop with HIDPI Screen
  @media (width: 1440px) and (height: 900px) {
    .Drawer-esquerdo {
      height: 58vw;
    }
  }
  // Laptop with MDPI Screen
  @media (width: 1280px) and (height: 800px) {
    .Drawer-esquerdo {
      height: 57.5vw;
    }
  }
 


  // Laptop with touch
  @media (width: 1280px) and (height: 950px) {
    .Drawer-esquerdo {
      height: 69.2vw;
    }
  }
  // Nexus 10
  @media (width: 800px) and (height: 1280px) {
    .Drawer-esquerdo {
      height: 152vw;
    }
  }
  // Nexus 6P
  @media (width: 412px) and (height: 732px) {
    .Drawer-esquerdo {
      height: 164vw;
    }
  }
  // Nexus 7
  @media (width: 600px) and (height: 960px) {
    .Drawer-esquerdo {
      height: 149.1vw;
    }
  }
  // Iphone 4
  @media (width: 320px) and (height: 480px) {
    .Drawer-esquerdo {
      height: 132.5vw;
    }
  }
  // Pixel 2
  @media (width: 411px) and (height: 731px) {
    .Drawer-esquerdo {
      height: 164vw;
    }
  }
  // Pixel 2 XL
  @media (width: 411px) and (height: 823px) {
    .Drawer-esquerdo {
      height: 186.5vw;
    }
  }
  // Iphone 5
  @media (width: 320px) and (height: 568px) {
    .Drawer-esquerdo {
      height: 160vw;
    }
  }
  // Iphone 6/7/8
  @media (width: 375px) and (height: 667px) {
    .Drawer-esquerdo {
      height: 163vw;
    }
  }
  // Iphone 6/7/8 PLUS
  @media (width: 414px) and (height: 736px) {
    .Drawer-esquerdo {
      height: 164.2vw;
    }
  }
  // Iphone X
  @media (width: 375px) and (height: 812px) {
    .Drawer-esquerdo {
      height: 201.5vw;
    }
  }
  // Ipad
  @media (width: 768px) and (height: 1024px) {
    .Drawer-esquerdo {
      height: 125vw;
    }
  }
  // Ipad Pro
  @media (width: 1024px) and (height: 1366px) {
    .Drawer-esquerdo {
      height: 127.2vw;
    }
  }
  //XGA
  @media (width: 1024px) and (height: 768px) {
    .Drawer-esquerdo {
      height: 68.7vw;
    }
  }
  //HD
  @media (width: 1280px) and (height: 720px) {
    .Drawer-esquerdo {
      height: 51.2vw;
    }
  }
  //HD VARIANTE
  @media (width: 1366px) and (height: 635px) {
    .Drawer-esquerdo {
      height: 41.8vw;
    }
  }
  //WXGA
  @media (width: 1366px) and (height: 768px) {
    .Drawer-esquerdo {
      height: 51.5vw;
    }
  }
  //FHD
  @media (width: 1920px) and (height: 1080px) {
    .Drawer-esquerdo {
      height: 52.9vw;
    }
  }
  //FHD VARIANTE
  @media (width: 1920px) and (height: 947px) {
    .Drawer-esquerdo {
      height: 46vw;
    }
  }
  @media (height: 644px) {
    .Drawer-esquerdo {
      height: 38.7vw;
    }
  }
  @media (height: 682px) {
    .Drawer-esquerdo {
      height: 45vw;
    }
  }
  @media (height: 667px) {
    .Drawer-esquerdo {
      height: 44vw;
    }
  }
  @media (height: 548px) {
    .Drawer-esquerdo {
      height: 36.5vw;
    }
  }
  @media (height: 860px) {
    .Drawer-esquerdo {
      height: 42.3vw;
    }
  }
  @media (height: 892px) {
    .Drawer-esquerdo {
      height: 44vw;
    }
  }
  @media (height: 993px) {
    .Drawer-esquerdo {
      height: 49.6vw;
    }
  }
  @media (height: 979px) {
    .Drawer-esquerdo {
      height: 47.6vw;
    }
  }
  @media (height: 900px) {
    .Drawer-esquerdo {
      height: 44.4vw;
    }
  }
  @media (height: 767px) {
    .Drawer-esquerdo {
      height: 37.4vw;
    }
  }
  @media (height: 799px) {
    .Drawer-esquerdo {
      height: 39.1vw;
    }
  }
  @media (height: 1440px) {
    .Drawer-esquerdo {
      height: 73.1vw;
    }
  }
  @media (height: 1307px) {
    .Drawer-esquerdo {
      height: 66.1vw;
    }
  }
  @media (height: 1339px) {
    .Drawer-esquerdo {
      height: 67.8vw;
    }
  }
  @media (max-width: 425px) {
    .Drawer-esquerdo {
      height: 36rem;
    }
}
@media (max-width: 710px) {
    .Drawer-esquerdo {
      height: 38rem;
    }
  }

@media (max-width: 1024px) {
    .Drawer-esquerdo {
      width: 100%;
      height: 54vw;
    }
}  

@media (min-width: 1680px) {
    .Drawer-esquerdo {
      height: 45.4vw!important;
    }
  }

  // .Drawer-esquerdo {
  //   width: 100%;
  //   height: 60vw;
  // }

  
  .Drawer-esquerdo {
    width: 100%;
    height: 46.4vw;
  }


  @media (max-width: 768px) {
    .Drawer-esquerdo {
      width: 100%;
      height: 52rem;
    }
  
    .background-login {
      height: 70vw;
    }
  }

  .camera {
    padding: 0;
    height: 100%;
    width: 85%!important;
    padding-left: 10px;
    margin-top: unset;
  }

  video {
    width: 46% !important;
    height: 10% !important;
    /* height: 300px; */
  }