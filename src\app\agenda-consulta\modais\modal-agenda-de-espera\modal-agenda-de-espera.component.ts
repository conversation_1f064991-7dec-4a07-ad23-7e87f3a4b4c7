import { PacienteService } from './../../../service/pacientes.service';
import { SpinnerService } from './../../../service/spinner.service';
import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { ModalInfoSobreUsuarioComponent } from 'src/app/Modais/modal-info-sobre-usuario/modal-info-sobre-usuario.component';
import { AgendaEspera } from 'src/app/model/agenda';
import { AgendaService } from 'src/app/service/agenda.service';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { finalize } from 'rxjs/operators';
import { MedicoService } from 'src/app/service/medico.service';
import { ModalCadastroHorariosComponent, ObjetoModalCadastroHorario } from 'src/app/Modais/modal-cadastro-horarios/modal-cadastro-horarios.component';

@Component({
  selector: 'app-modal-agenda-de-espera',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TruncatePipe,
    MatButtonModule,
    MatSelectModule,
    MatDialogModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    NgSelectModule,
    ReactiveFormsModule,
    TranslateModule,
    MatRadioModule,
    NgxMaskDirective
  ],
  providers: [
    provideNgxMask()
  ],
  templateUrl: './modal-agenda-de-espera.component.html',
  styleUrl: './modal-agenda-de-espera.component.scss'
})
export class ModalAgendaDeEsperaComponent implements OnInit {

  lsMedicos: any[] = [];
  lsPacientes: any[] = [];
  DadosPacientes: any[] = [];
  DadosInformUsuario: any = {};
  DadosAgendaEspera: any[] = [];
  objAgendaEspera: AgendaEspera = new AgendaEspera();
  ListaMedicos: any[] = [];
  pacienteValido: boolean = false;
  paciAgendaVasil: boolean = false;
  paciAgendaVal: boolean = false;
  idUltimaClinica = 1;
  IdMedico: number | null = null;
  IdTipoUsuario: number | null = null;
  constructor(
    private matDialogRef: MatDialogRef<ModalAgendaDeEsperaComponent>,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBarAlert: AlertComponent,
    private agendaService: AgendaService,
    private spinner: SpinnerService,
    private matDialog: MatDialog,
    private pacientesService: PacienteService,
    private medicoService: MedicoService,
    @Inject(MAT_DIALOG_DATA) public data: number | null = null,
  ) { }

  ngOnInit(): void {
    this.loadInitialData();
  }

  loadInitialData(): void {
    this.spinner.show();
    this.idUltimaClinica = this.usuarioLogadoService.getIdUsuarioAcesso()!;
    this.IdTipoUsuario = this.usuarioLogadoService.getIdTipoUsuario()!;

    if (this.IdTipoUsuario === 2)
      this.IdMedico = this.usuarioLogadoService.getIdMedico()!;
    else if (this.data != null)
      this.IdMedico = Number(this.data);
    this.CarregaAgendaEspera();
    this.carregarPacientes();
    this.carregarMedicos();
  }

  onSelectionChange() {
    this.CarregaAgendaEspera();
  }

  carregarPacientes() {
    this.spinner.show();
    this.pacientesService.GetPacienteAgenda('', this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe(
        (retorno: any) => {
          this.lsPacientes = retorno.filter((c: any) => !c.flgInativo);
          this.spinner.hide();
        },
        erro => {
          console.error(erro);
          this.spinner.hide();
        }
      );
  }

  carregarMedicos() {
    this.medicoService.getMedicos(null, this.idUltimaClinica).subscribe((retorno) => {
      this.lsMedicos = retorno
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  informacao(tipoModal: string = ""): void {
    this.DadosInformUsuario = {};
    if (tipoModal === 'Paciente') {
      if (!this.objAgendaEspera.IdPaciente) {
        this.snackBarAlert.falhaSnackbar("Selecione um paciente para visualizar as informações")
        return;
      }

      const dadosUsuario = this.DadosPacientes.filter((c: any) => c.idCliente === this.objAgendaEspera.IdPaciente);
      if (dadosUsuario.length > 0) {
        this.DadosInformUsuario = dadosUsuario[0];
        this.matDialog.open(ModalInfoSobreUsuarioComponent, {
          data: this.DadosInformUsuario,
          width: "40vmax",
        });
      }
    } else {
      if (!this.IdMedico) {
        this.snackBarAlert.falhaSnackbar("Selecione um médico para visualizar as informações")
        return;
      }
      const dadosMedico = this.ListaMedicos.filter((c: any) => c.idMedico === this.IdMedico);
      if (dadosMedico.length > 0) {
        this.DadosInformUsuario = dadosMedico[0];
        this.matDialog.open(ModalInfoSobreUsuarioComponent, {
          data: this.DadosInformUsuario,
          width: "40vmax",
        });
      }
    }
  }

  SalvarAgendaEspera(): void {
    if (!this.validarFormulario()) {
      return;
    }

    this.spinner.show();
    const espera = this.prepararDadosParaSalvar();

    espera.IdMedico = this.IdMedico!;

    this.agendaService.salvarAgendaEspera(espera)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: () => {
          this.CarregaAgendaEspera();
          this.limparFormulario();
          this.snackBarAlert.sucessoSnackbar("Agendamento salvo com sucesso");
        },
        error: (error) => {
          console.error('Erro ao salvar agendamento:', error);
          this.snackBarAlert.falhaSnackbar("Falha ao salvar o agendamento");
        }
      });
  }

  validarFormulario(): boolean {
    if (!this.objAgendaEspera.IdPaciente) {
      this.snackBarAlert.falhaSnackbar("Selecione um paciente");
      return false;
    }

    if (this.IdMedico === null || this.IdMedico === undefined) {
      this.snackBarAlert.falhaSnackbar("Selecione um médico");
      return false;
    }

    if (!this.objAgendaEspera.DtaConsulta) {
      this.objAgendaEspera.DtaConsulta = new Date();
    }

    return true;
  }

  prepararDadosParaSalvar(): AgendaEspera {
    const espera = new AgendaEspera();
    Object.assign(espera, this.objAgendaEspera);

    espera.IdUsuarioGerador = this.idUltimaClinica;
    espera.FlgInativo = false;
    espera.DtaCadastro = new Date();
    espera.idClinica = this.idUltimaClinica;

    return espera;
  }

  limparFormulario(): void {
    this.objAgendaEspera = new AgendaEspera();
    // Keep the date and doctor if they were previously set
    this.objAgendaEspera.DtaConsulta = new Date();
  }

  MedicoChange() {
    this.CarregaAgendaEspera();
  }

  CarregaAgendaEspera(): void {
    if (!this.IdMedico || this.IdMedico < 1) {
      return;
    }

    if (!this.objAgendaEspera.DtaConsulta)
      this.objAgendaEspera.DtaConsulta = new Date();

    this.spinner.show();

    this.agendaService.GetAgendaEspera(this.objAgendaEspera.DtaConsulta.toDateString(), this.IdMedico)
      .pipe(finalize(() => this.spinner.hide()))
      .subscribe({
        next: (retorno) => {
          if (retorno) {
            this.DadosAgendaEspera = retorno;;
          }
        },
        error: (err) => {
          console.error('Erro ao carregar agenda de espera:', err);
          this.snackBarAlert.falhaSnackbar('Erro ao carregar agenda de espera');
        }
      });
  }

  ValidaPaciAgenda(pacienteId: number | null = null): void {
    this.paciAgendaVasil = false;
    this.paciAgendaVal = !pacienteId || pacienteId <= 0;
  }

  validaPaciente(): void {
    this.pacienteValido = !!this.objAgendaEspera.IdPaciente;
  }

  AbrirModalExclusao(id: number): void {
    if (!id) return;

    const dialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      width: '350px',
      data: { title: 'Confirmar exclusão', message: 'Deseja realmente excluir este agendamento?' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.excluirAgendamento(id);
      }
    });
  }

  excluirAgendamento(id: number): void {
    this.spinner.show();

    this.agendaService.InativarAgendaEspera(id, this.idUltimaClinica).subscribe(() => {

      this.CarregaAgendaEspera();
      this.snackBarAlert.sucessoSnackbar("Agendamento em espera excluido")
      this.spinner.hide();

    }, err => {
      this.snackBarAlert.falhaSnackbar('Erro ao Carregar Agenda de Espera')
      console.error(err)
      this.spinner.hide();
    })
  }

  AdicionarAgendamento(obj: any): void {
    let dados = new ObjetoModalCadastroHorario();

    dados.IdMedico = obj.idMedico;
    dados.IdPaciente = obj.idPaciente;

    let dialog = this.matDialog.open(ModalCadastroHorariosComponent, {
      data: dados,
      width: "40vmax",
    })

    dialog.afterClosed().subscribe((retorno) => {
      if (retorno) {
        this.excluirAgendamento(obj.idAgenda);
        this.CarregaAgendaEspera();
      }
    })
  }

  fecharModal(): void {
    this.matDialogRef.close();
  }
}

// Additional component for confirmation dialogs
@Component({
  selector: 'app-confirmation-dialog',
  template: `
    <h2 mat-dialog-title>{{ data.title }}</h2>
    <mat-dialog-content>{{ data.message }}</mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Cancelar</button>
      <button mat-raised-button color="warn" [mat-dialog-close]="true">Confirmar</button>
    </mat-dialog-actions>
  `,
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule
  ]
})
export class ConfirmationDialogComponent {
  constructor(@Inject(MAT_DIALOG_DATA) public data: { title: string, message: string }) { }
}