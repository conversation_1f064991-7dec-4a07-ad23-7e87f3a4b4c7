<div style="display: flex; flex-direction: column;" class="modal-container">

        <div class="modal-header">
            <h2 mat-dialog-title class="modal-title">Relatório Agendamentos</h2>
        </div>
            
        <div class="modal-container">
            <div class="form-group">
                <label for="exampleInput" class="form-label">Nome arquivo</label>
                <input type="text" class="form-control" id="exampleInput" placeholder="Digite algo" [(ngModel)]="FileName">
            </div>
            
            <div style="display: flex; justify-content: space-evenly; align-items: center; gap: 1em; width: 100%;">
                <div class="form-group">
                    <label for="exampleInput" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="exampleInput" placeholder="Digite algo" [(ngModel)]="strDtInicio"
                        (change)="dtChangeInicio()">
                </div>
            
                <div class="form-group">
                    <label for="exampleInput" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="exampleInput" placeholder="Digite algo" [(ngModel)]="strDtFim"
                        (change)="dtChangeFim()">
                </div>
            </div>
            
            <p class="error-text" *ngIf="flgDiferencaDatas">
                A diferença entre a data de inicio e a data de fim não pode ser inferior a 6 meses.
            </p>
            <p class="error-text" *ngIf="flgDtInicioNaoPreenchida">
                A data de inicio é obrigatória.
            </p>
            <p class="error-text" *ngIf="flgDtFimNaoPreenchida">
                A data final é obrigatória.
            </p>
            <p class="error-text" *ngIf="flgDtFimMaiorQueInicio">
                A data final não pode ser anterior a data incial.
            </p>
            
            <div class="container mt-3">
                <mat-form-field appearance="outline" class="w-100">
                    <mat-label>Selecione um médico</mat-label>
                    <mat-select [(ngModel)]="idMedicoSelecionado">
                        <mat-option [value]="0">
                            Nenhum
                        </mat-option>
                        <mat-option *ngFor="let option of ListaMedicos" [value]="option.idMedico">
                            {{ option.nome }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        
        <mat-dialog-actions class="modal-footer">
            <button (click)="FecharModal()" class="btn-primary cancelar">
                Cancelar
            </button>
            <button mat-flat-button (click)="GerarRelatorio()" class="btn-primary">
                Gerar Relatório
            </button>
        </mat-dialog-actions>
</div>