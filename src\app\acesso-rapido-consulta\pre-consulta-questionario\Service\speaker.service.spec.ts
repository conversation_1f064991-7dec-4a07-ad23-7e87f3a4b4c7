import { TestBed } from '@angular/core/testing';
import { SpeakerService } from './speaker.service';

// Mock ElevenLabs client
jest.mock('@elevenlabs/elevenlabs-js', () => ({
  ElevenLabsClient: jest.fn().mockImplementation(() => ({
    textToSpeech: {
      convert: jest.fn().mockResolvedValue({
        getReader: jest.fn().mockReturnValue({
          read: jest.fn()
            .mockResolvedValueOnce({ value: new Uint8Array([1, 2, 3]), done: false })
            .mockResolvedValueOnce({ value: undefined, done: true })
        })
      })
    }
  }))
}));

// Mock environment
jest.mock('src/environments/environment', () => ({
  environment: {
    elevenlabs: 'test-api-key'
  }
}));

describe('SpeakerService', () => {
  let service: SpeakerService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SpeakerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with ElevenLabs', () => {
    expect(service).toBeDefined();
    // Service should be initialized without throwing errors
  });

  it('should return false for isSpeaking initially', () => {
    expect(service.isSpeaking()).toBe(false);
  });

  it('should handle setSpeed method', () => {
    expect(() => service.setSpeed('normal')).not.toThrow();
    expect(() => service.setSpeed('fast')).not.toThrow();
  });

  it('should handle setPitch method', () => {
    expect(() => service.setPitch('normal')).not.toThrow();
    expect(() => service.setPitch('high')).not.toThrow();
  });

  it('should handle cancel method', () => {
    expect(() => service.cancel()).not.toThrow();
  });

  it('should return a promise from testSpeech', () => {
    const result = service.testSpeech('test');
    expect(result).toBeInstanceOf(Promise);
  });

  it('should process text correctly with humanizeText', () => {
    // Test the private method indirectly through speak
    const testText = 'Hello,world!\nThis is a test.';
    // The method should not throw when processing text
    expect(() => service.testSpeech(testText)).not.toThrow();
  });
});
