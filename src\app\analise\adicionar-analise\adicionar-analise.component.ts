import { listaPerguntasRespostas } from './../../model/formularios';
import { Component, OnInit } from '@angular/core';

import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { <PERSON><PERSON><PERSON>, AnaliseMensagemModelView, ExamesAnaliseModelView } from 'src/app/model/analise';
import { PacienteService } from 'src/app/service/pacientes.service';
import { AnaliseService } from 'src/app/service/analise.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { ConsultaService } from 'src/app/service/consulta.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { FormulariosService } from 'src/app/service/formulario.service';
import { analiseFormularioModel } from 'src/app/model/formularios';
// import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import ClassicEditor from 'ckeditor-build-b64imageupload';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { MatInputModule } from '@angular/material/input';
import { MatOptionModule } from '@angular/material/core';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';
import { MatSelectModule } from '@angular/material/select';
import { MatDialog } from '@angular/material/dialog';
import { ChatInternoModalComponent } from './chat-interno-modal/chat-interno-modal.component';


@Component({
  selector: 'app-adicionar-analise',
  templateUrl: './adicionar-analise.component.html',
  styleUrls: ['./adicionar-analise.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIconModule,
    TranslateModule,
    NgSelectModule,
    MatFormFieldModule,
    MatLabel,
    MatCardModule,
    CKEditorModule,
    NgxSmartModalModule,
    PdfViewerModule,
    MatOptionModule,
    TruncatePipe,
    MatSelectModule
  ]
})
export class AdicionarAnaliseComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private pacientesService: PacienteService,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBarAlert: AlertComponent,
    private analiseService: AnaliseService,
    private localStorageService: LocalStorageService,
    private consultaService: ConsultaService,
    public ngxSmartModalService: NgxSmartModalService,
    private formulariosService: FormulariosService,
    private dialog: MatDialog,
  ) { }

  ImagemPessoa: any = "assets/build/img/userdefault.png";
  listaOrientacao = [{ valor: 1, descricao: "Orientação médica" }, { valor: 2, descricao: "Orientação nutricional" }, { valor: 3, descricao: "Orientação fisíca" }];
  objAnalise: Analise = new Analise();
  DadosPacientes: any = [];
  idObjAnalise: any;
  listaExames: any = [];
  examesSelecionados: number[] | null = null;
  exameArquivo = new ExamesAnaliseModelView();
  urlPDF: any;
  exameArquivoAberto: any;

  BackupFormularios: analiseFormularioModel[] = [];
  listaNovosFormularios: analiseFormularioModel[] = [];
  listaFormulariosAtribuidos: analiseFormularioModel[] = [];
  listaAllFormularios: analiseFormularioModel[] = [];
  totalFormularios?: number | null;

  listaPerguntas: listaPerguntasRespostas[] = [];
  listaMensagens: AnaliseMensagemModelView[] = [];
  idAnalise?: number | null;
  formularioSelecionadoModal = new analiseFormularioModel();
  objetoMensagem = new AnaliseMensagemModelView();


  title = "Observações"
  // Editor = ClassicEditor;
  config = {
    toolbar: ['heading', '|', 'undo', 'redo', '|', "outdent", "indent", 'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'link', 'blockQuote', 'imageUpload', 'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', 'imageInsert'] // , 'removeFormat'],
  };



  ngOnInit() {
    this.idAnalise = this.localStorageService.idAnalise;
    this.objAnalise = new Analise;

    this.CarregaPacientes();

    this.CarregaFormularios();


    if (this.idAnalise && this.idAnalise > 0)
      this.CarregarAnalise();

    this.listaExamesParaAnalise();
    this.totalFormularios = this.listaFormulariosAtribuidos.length;
  }

  updateListaAtribuios() {

    this.listaFormulariosAtribuidos = [];
    this.BackupFormularios.forEach(element => {
      this.listaFormulariosAtribuidos.push(element)
    });

    this.listaNovosFormularios.forEach(element => {
      const index = this.listaFormulariosAtribuidos.findIndex(item => item.idFormulario === element.idFormulario);
      if (index === -1) {
        this.listaFormulariosAtribuidos.push(element);
      }
    });

  }
  listaExamesParaAnalise() {
    this.spinner.show();
    this.analiseService.ListaExamesParaAnalise().subscribe((retorno) => {
      this.listaExames = []

      this.listaExames = retorno;
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  filtraFormularios() {

    let ids: number[] = [];

    this.listaFormulariosAtribuidos.forEach(element => {
      ids.push(element.idFormulario!);
    });
    this.listaAllFormularios.forEach(element => {
      if (ids.includes(element.idFormulario!)) {

        this.listaAllFormularios = this.listaAllFormularios.filter(obj => obj.idFormulario != element.idFormulario)
      }
    })

  }


  CarregaFormularios() {
    this.formulariosService.getAllFormularios().subscribe((ret) => {

      ;

      ret.forEach(element => {
        let objFormulario = new analiseFormularioModel();
        objFormulario.idFormulario = element.idFormulario;
        objFormulario.nomeFormulario = element.nomeFormulario;
        objFormulario.dtaCadastro = element.dtaCadastro;
        this.listaAllFormularios.push(objFormulario);
      });


      this.spinner.hide();

    })
  }
  CarregarAnalise() {
    this.analiseService.GetObjAnalise(this.idAnalise!).subscribe((retorno) => {

      this.objAnalise = retorno;
      //remove os exames solicitados;
      for (let i = 0; i < this.listaExames.length; i++) {
        if (this.objAnalise!.examesSelecionados!.filter(x => x.idExameClinica == this.listaExames[i].idExameClinica).length > 0)
          this.listaExames.splice(i--, 1);
      }


      if (this.objAnalise!.listaFormularios != null) {
        this.objAnalise!.listaFormularios.forEach(element => {
          this.listaFormulariosAtribuidos.push(element);
          this.BackupFormularios.push(element);
        });
        this.filtraFormularios();
      }

      this.localStorageService.clearByName("idAnalise");
      this.spinner.hide();
    }, erro => {
      console.error(erro);
      this.localStorageService.clearByName("idAnalise");
      this.spinner.hide();
    })
  }

  CarregaPacientes() {
    this.pacientesService.GetPacienteAgenda("", this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.DadosPacientes = []

      this.DadosPacientes = retorno.filter((c: any) => c.flgInativo != true);
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  Salvar() {
    if (!this.objAnalise!.idPaciente || !this.objAnalise!.idTipoOrientacao) {
      this.snackBarAlert.falhaSnackbar("Paciente e tipo de orientação.");
      return;
    }

    if (!this.objAnalise!.idAnalise)
      this.objAnalise!.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();


    this.objAnalise!.idPessoaEspecialista = this.usuarioLogadoService.getIdPessoa();

    this.idAnalise = this.objAnalise!.idAnalise;
    this.objAnalise!.idsExames = this.examesSelecionados;


    this.analiseService.SalvarAnalise(this.objAnalise!).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar("Registro Salvo com sucesso");
      this.limpar();
      this.spinner.hide();

    }, err => {
      this.snackBarAlert.falhaSnackbar("Erro ao salvar");
      console.error(err);
    });

    // if (this.totalFormularios != this.listaFormulariosAtribuidos.length) {
    //   this.listaFormulariosAtribuidos.forEach(element => {
    //     element.idAnalise = this.idAnalise;
    //   })

    //   this.formulariosService.salvaAnaliseFormularios(this.listaFormulariosAtribuidos).subscribe((ret) => {

    //     this.spinner.hide();
    //   })
    // }
  }

  limpar() {
    // this.Editor = new ClassicEditor;
    this.objAnalise = new Analise();
    this.objAnalise.observacao = "";
    this.examesSelecionados = null;
    this.idAnalise = null;
    this.BackupFormularios = [];
    this.listaNovosFormularios = [];
    this.listaAllFormularios = [];
    this.listaFormulariosAtribuidos = [];
    this.totalFormularios = null;
    this.listaPerguntas = [];
    this.formularioSelecionadoModal = new analiseFormularioModel()
    this.CarregaFormularios();
  }

  editar(objDetalheAnlise: any) {
    this.objAnalise!.observacao = objDetalheAnlise.observacao;
    this.objAnalise!.idAnaliseDetalhe = objDetalheAnlise.idAnaliseDetalhe;
    this.objAnalise!.idTipoOrientacao = objDetalheAnlise.idTipoOrientacao;


  }

  tiraBorada(event: any) {
    event.preventDefault();
    // event.target.style.border = "";
    (document.getElementById("divArquivo") as HTMLInputElement).style.border = "dotted cornflowerblue";
  }

  destacaBorda(event: any) {
    event.preventDefault();
    (document.getElementById("divArquivo") as HTMLInputElement).style.border =
      "dotted 2px cornflowerblue";
  }

  fileuploadesquerdo(event: DragEvent) {

    event.preventDefault();
    this.readThis(event.dataTransfer);
  }


  // SubirArquivoConsulta(arquivo) {

  //   this.readThis(arquivo.target);
  // }

  SolicitarAnalise() {


    if (!this.objAnalise!.idPaciente || !this.objAnalise!.idTipoOrientacao || (!this.examesSelecionados || this.examesSelecionados.length == 0)) {
      this.snackBarAlert.falhaSnackbar("Paciente, Tipo de Orientação e pelo menos um exame são obrigatórios para solicitação de análise.");
      return;
    }

    this.objAnalise!.idsExames = this.examesSelecionados;

    this.spinner.show();
    this.analiseService.SolicitarAnalise(this.objAnalise!).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar("Análise Solicitada com sucesso");
      this.limpar();
      this.spinner.hide();

    }, err => {
      this.snackBarAlert.falhaSnackbar("Erro ao salvar");
      this.spinner.hide();
      console.error(err);
    });
  }

  readThis(inputValue: any): void {

    var file: File = inputValue.files[0];
    var myReader: FileReader = new FileReader();
    this.exameArquivo.nmeArquivo = file.name;

    myReader.onloadend = () => {
      this.exameArquivo.Arquivobase64 = myReader.result;
      this.salvarArquivo();
    }
    myReader.readAsDataURL(file);
  }

  SubirArquivoConsulta(arquivo: any, exame: any) {

    this.exameArquivo.idExame = exame.idExame;
    this.readThis(arquivo.target);
  }

  salvarArquivo() {
    this.spinner.show();
    this.analiseService.SalvarArquivo(this.exameArquivo).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar("Arquivo Carregado com sucesso");
      this.CarregarAnalise();
      this.spinner.hide();
    }, err => {
      this.snackBarAlert.falhaSnackbar("Erro ao salvar");
      console.error(err);
      this.spinner.hide();
    });
  }

  BaixarArquivo() {


    this.spinner.show();
    this.consultaService.CarregaCaminhoArquivo(this.exameArquivoAberto.chaveArquivo, this.exameArquivoAberto.nmeArquivo).subscribe(
      (response) => {
        var application;
        this.download(response, this.exameArquivoAberto.nmeArquivo, application);
        this.spinner.hide();
      },
      () => {
        // this.spinnerAnexoDownload = false;
        this.spinner.hide();
      }
    );
  }

  ModalArquivo(exame: any) {

    this.exameArquivoAberto = exame;
    try {
      this.spinner.show();
      this.analiseService.BaixarArquivoExame(exame.chaveArquivo).subscribe(async (retorno) => {

        if (retorno != null) {
          this.urlPDF = retorno;
          this.ngxSmartModalService.getModal('arquivo').open();
        }
        this.spinner.hide();
      }, err => {
        this.spinner.hide();
        console.error("erro", err)
      })
    } catch (error) {
      this.spinner.hide();
    }


  }

  download(arq: any, nome: any, contentType: any) {
    if (!contentType) {
      contentType = "application/octet-stream";
    }
    var a = document.createElement("a");
    var blob = new Blob([arq], { type: contentType });
    a.href = window.URL.createObjectURL(blob);
    a.download = nome;
    
    a.click();
  }


  preparaFormulario(event: analiseFormularioModel) {

    this.formularioSelecionadoModal = event;
    if (event.flgRespondido) {
      this.formulariosService.getPerguntasRespostas(event.idFormulario!, this.idAnalise!).subscribe((ret) => {
        this.listaPerguntas = ret;
        this.abreModal('formularioViewer');
        this.spinner.hide();
      })
    }
    else {
      this.formulariosService.getPerguntas(event.idFormulario!).subscribe((ret) => {
        this.listaPerguntas = ret;
        this.abreModal('formularioViewer');
        this.spinner.hide();
      })
    }
  }

  abreModal(modalNome: string) {
    this.ngxSmartModalService.getModal(modalNome).open();
  }
  fechaModal(modalNome: string) {
    this.ngxSmartModalService.getModal(modalNome).close();
  }
  recuperaMensagens() {

    this.analiseService.getMensaems(this.idAnalise!).subscribe((ret) => {
      this.listaMensagens = ret;
      this.spinner.hide();
    })
  }

  EnviarMensagem() {


    this.objetoMensagem.idAnalise = this.objAnalise!.idAnalise;
    this.objetoMensagem.idPessoa = this.usuarioLogadoService.getIdPessoa();

    if (this.objetoMensagem.idAnalise == null || this.objetoMensagem.idPessoa == null || this.objetoMensagem.mensagem == "") {
      if (this.objetoMensagem.mensagem == "") {
        this.snackBarAlert.falhaSnackbar("Erro ao enviar mensagem, escreva primeiramente o conteudo da mensagem")
      }
      else {
        this.snackBarAlert.falhaSnackbar("Erro ao enviar mensagem, não foi possivel completar os dados")
      }

    }
    else {
      this.analiseService.salvaMensagemInterna(this.objetoMensagem).subscribe((ret) => {
        if (ret) {
          this.objetoMensagem = new AnaliseMensagemModelView();
          this.snackBarAlert.sucessoSnackbar("mensagem enviada");
          this.CarregaModalChatInterno();
          this.spinner.hide();
        }
        else {
          this.snackBarAlert.falhaSnackbar("Erro ao salvar");
          this.spinner.hide();
        }
      })
    }
  }

  CarregaModalChatInterno() {
    if (this.idAnalise == null || this.idAnalise < 1) {
      return;
    }

    else {
      this.dialog.open(ChatInternoModalComponent, {
        width: '30vmax',
        height: '70vh',
        maxWidth: '100vw',
        maxHeight: '90vh',
        panelClass: 'chat-dialog-container',
        data: { idAnalise: this.idAnalise! },
      });
    }
  }

  formatarData(data: string | Date): string {
    const dataEnvio = new Date(data);
    const agora = new Date();

    const diffEmMilissegundos = agora.getTime() - dataEnvio.getTime();
    const diffEmMinutos = Math.floor(diffEmMilissegundos / 60000);

    if (diffEmMinutos < 3) {
      return 'Agora';
    } else if (diffEmMinutos < 420) { // 7 horas = 7 * 60 minutos = 420 minutos
      const hora = String(dataEnvio.getHours()).padStart(2, '0');
      const minutos = String(dataEnvio.getMinutes()).padStart(2, '0');
      return `${hora}:${minutos}`;
    } else {
      const dia = String(dataEnvio.getDate()).padStart(2, '0');
      const mes = String(dataEnvio.getMonth() + 1).padStart(2, '0');
      const ano = dataEnvio.getFullYear();
      const hora = String(dataEnvio.getHours()).padStart(2, '0');
      const minutos = String(dataEnvio.getMinutes()).padStart(2, '0');

      return `${dia}/${mes}/${ano} - ${hora}:${minutos}`;
    }
  }
  exibir() {
  }
  public onReady(editor: any) {
    editor.plugins.get('FileRepository').createUploadAdapter = (loader: any) => {
      return new MyUploadAdapter(loader);
    };
  }
}

class MyUploadAdapter {
  constructor(private loader: any) { }

  upload() {
    return this.loader.file.then((file: any) => new Promise((resolve) => {
      this.readFile(file).then((base64: any) => {
        resolve({ default: base64 });
      });
    }));
  }

  readFile(file: File) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
      reader.readAsDataURL(file);
    });
  }

  abort() {
    // No implementation needed
  }
}



