export class IntegrationApiExameModelView {
    objPessoa: ObjPessoaExame | null = null;
    objRelatorio: ObjRelatorioExameApi | null = null;
}

export class LogRequisicaoExameModelView {
    Id: number | null = null;
    Requisicao: string = "";
    Resposta: string = "";
    DtCadastro: Date | null = null;
}

export class ObjRelatorioExameApi {
    Id: number | null = null;
    Status: string = "";
    PressaoSistolica: number | null = null;
    PressaoDiastolica: number | null = null;
    Oxigenacao: number | null = null;
    Batimento: number | null = null;
    Temperatura: number | null = null;
    Sintomas: string | null = null;
    Intensidade: number | null = null;
    DuracaoSintomas: number | null = null;
    Observacoes: string = "";
    DtaCadastro: Date | null = null;
    IdPessoaExame: number | null = null;
}

export class ObjPessoaExame {
    Id: number| null = null;
    Nome: string = "";
    DataNascimento: Date| null = null;
    Cpf: string = "";
    Sexo: boolean = true;
    DescAlergia: string = "";
}
