import { Component, Inject, ChangeDetectorRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { trigger, style, transition, animate } from '@angular/animations';
import { QuestionarioPreConsultaDados } from '../MapPalavrasModel';

interface FilterOption {
    type: string;
    label: string;
    icon: string;
    active: boolean;
}

interface EnhancedDataItem {
    label: string;
    value: string;
    category: string;
    categoryLabel: string;
    icon: string;
    timestamp?: Date;
    validationStatus: 'valid' | 'warning' | 'error';
}

@Component({
    selector: 'app-history-modal-dialog',
    templateUrl: './history-modal-dialog.component.html',
    styleUrls: ['./history-modal-dialog.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        MatButtonModule,
        MatIconModule,
        MatInputModule,
        MatFormFieldModule,
        MatTooltipModule,
        MatChipsModule,
        MatDialogModule,
        FormsModule,
        ReactiveFormsModule
    ],
    animations: [
        trigger('fadeInOut', [
            transition(':enter', [
                style({ opacity: 0 }),
                animate('300ms ease-in', style({ opacity: 1 }))
            ]),
            transition(':leave', [
                animate('200ms ease-out', style({ opacity: 0 }))
            ])
        ]),
        trigger('slideInOut', [
            transition(':enter', [
                style({ transform: 'translateY(-50px)', opacity: 0 }),
                animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)',
                    style({ transform: 'translateY(0)', opacity: 1 }))
            ]),
            transition(':leave', [
                animate('300ms ease-in',
                    style({ transform: 'translateY(-30px)', opacity: 0 }))
            ])
        ]),
        trigger('cardAnimation', [
            transition(':enter', [
                style({ transform: 'scale(0.8)', opacity: 0 }),
                animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)',
                    style({ transform: 'scale(1)', opacity: 1 }))
            ])
        ])
    ]
})
export class HistoryModalDialogComponent {
    searchTerm = '';
    availableFilters: FilterOption[] = [
        { type: 'personal', label: 'Dados Pessoais', icon: 'person', active: true },
        { type: 'medical', label: 'Informações Médicas', icon: 'medical_services', active: true },
        { type: 'contact', label: 'Contato', icon: 'contact_phone', active: true },
        { type: 'optional', label: 'Opcionais', icon: 'info', active: true }
    ];

    constructor(
        public dialogRef: MatDialogRef<HistoryModalDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: {
            dadosColetados: QuestionarioPreConsultaDados,
            snackBar?: any,
            cdr?: ChangeDetectorRef
        }
    ) { }

    onClose(): void {
        this.dialogRef.close();
    }

    getTotalCampos(): number {
        return 10;
    }

    getDadosPreenchidos(): Array<{ label: string, value: string }> {
        const labels: { [key: string]: string } = {
            nome: 'Nome',
            cpf: 'CPF',
            email: 'Email',
            telefone: 'Telefone',
            dataNascimento: 'Data de Nascimento',
            alergias: 'Alergias',
            sintomas: 'Sintomas',
            intensidadeDor: 'Intensidade da Dor',
            tempoSintomas: 'Tempo dos Sintomas',
            doencasPrevias: 'Doenças Prévias',
            observacoes: 'Observações'
        };
        return Object.keys(this.data.dadosColetados)
            .filter(key => {
                const value = this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados];
                return value && value.trim() !== '';
            })
            .map(key => ({
                label: labels[key],
                value: this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados]
            }));
    }

    getProgressPercentage(): number {
        const total = this.getTotalCampos();
        const filled = this.getDadosPreenchidos().length;
        return Math.round((filled / total) * 100);
    }

    onSearchChange(event: any): void {
        this.searchTerm = event.target.value;
    }

    clearSearch(): void {
        this.searchTerm = '';
    }

    toggleFilter(filter: FilterOption): void {
        filter.active = !filter.active;
    }

    getFilteredData(): EnhancedDataItem[] {
        let data = this.getEnhancedDadosPreenchidos();
        if (this.searchTerm) {
            const searchLower = this.searchTerm.toLowerCase();
            data = data.filter(item =>
                item.label.toLowerCase().includes(searchLower) ||
                item.value.toLowerCase().includes(searchLower)
            );
        }
        const activeCategories = this.availableFilters
            .filter(f => f.active)
            .map(f => f.type);
        data = data.filter(item => activeCategories.includes(item.category));
        return data;
    }

    getEnhancedDadosPreenchidos(): EnhancedDataItem[] {
        const categoryMap: { [key: string]: { category: string, categoryLabel: string, icon: string } } = {
            nome: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'person' },
            cpf: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'badge' },
            dataNascimento: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'cake' },
            email: { category: 'contact', categoryLabel: 'Contato', icon: 'email' },
            telefone: { category: 'contact', categoryLabel: 'Contato', icon: 'phone' },
            sintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'medical_services' },
            intensidadeDor: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'personal_injury' },
            tempoSintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'schedule' },
            alergias: { category: 'optional', categoryLabel: 'Opcionais', icon: 'medical_information' },
            observacoes: { category: 'optional', categoryLabel: 'Opcionais', icon: 'description' }
        };
        const labels: { [key: string]: string } = {
            nome: 'Nome',
            cpf: 'CPF',
            email: 'Email',
            telefone: 'Telefone',
            dataNascimento: 'Data de Nascimento',
            alergias: 'Alergias',
            sintomas: 'Sintomas',
            intensidadeDor: 'Intensidade da Dor',
            tempoSintomas: 'Tempo dos Sintomas',
            doencasPrevias: 'Doenças Prévias',
            observacoes: 'Observações'
        };
        return Object.keys(this.data.dadosColetados)
            .filter(key => {
                const value = this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados];
                return value && value.trim() !== '';
            })
            .map(key => {
                const categoryInfo = categoryMap[key] || { category: 'optional', categoryLabel: 'Outros', icon: 'info' };
                return {
                    label: labels[key] || key,
                    value: this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados],
                    category: categoryInfo.category,
                    categoryLabel: categoryInfo.categoryLabel,
                    icon: categoryInfo.icon,
                    timestamp: new Date(),
                    validationStatus: this.getValidationStatusForField(key) as 'valid' | 'warning' | 'error'
                };
            });
    }

    getValidationStatusForField(field: string): string {
        const value = this.data.dadosColetados[field as keyof QuestionarioPreConsultaDados];
        if (!value || value.trim() === '') return 'error';
        if (field === 'email' && !value.includes('@')) return 'warning';
        if (field === 'cpf' && value.length < 11) return 'warning';
        return 'valid';
    }

    trackByFn(index: number, item: EnhancedDataItem): string {
        console.log('index', index);
        return item.label + item.value;
    }

    highlightSearchTerm(text: string): string {
        if (!this.searchTerm) return text;
        const regex = new RegExp(`(${this.searchTerm})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    formatTimestamp(timestamp: Date): string {
        return timestamp.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    getValidationIcon(status: string): string {
        switch (status) {
            case 'valid': return 'check_circle';
            case 'warning': return 'warning';
            case 'error': return 'error';
            default: return 'info';
        }
    }

    getValidationLabel(status: string): string {
        switch (status) {
            case 'valid': return 'Válido';
            case 'warning': return 'Atenção';
            case 'error': return 'Erro';
            default: return 'Info';
        }
    }

    copyToClipboard(text: string): void {
        navigator.clipboard.writeText(text).then(() => {
            if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');
        }).catch(() => {
            if (this.data.snackBar) this.data.snackBar.falhaSnackbar('Erro ao copiar texto');
        });
    }

    editValue(item: EnhancedDataItem): void {
        const newValue = prompt(`Editar ${item.label}:`, item.value);
        if (newValue !== null && newValue !== item.value) {
            const field = Object.keys(this.data.dadosColetados).find(key => {
                const labels: { [key: string]: string } = {
                    nome: 'Nome',
                    cpf: 'CPF',
                    email: 'Email',
                    telefone: 'Telefone',
                    dataNascimento: 'Data de Nascimento',
                    alergias: 'Alergias',
                    sintomas: 'Sintomas',
                    intensidadeDor: 'Intensidade da Dor',
                    tempoSintomas: 'Tempo dos Sintomas',
                    doencasPrevias: 'Doenças Prévias',
                    observacoes: 'Observações'
                };
                return labels[key] === item.label;
            });
            if (field) {
                this.data.dadosColetados[field as keyof QuestionarioPreConsultaDados] = newValue;
                if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Valor atualizado com sucesso');
                if (this.data.cdr) this.data.cdr.detectChanges();
            }
        }
    }

    clearAllData(): void {
        if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {
            Object.keys(this.data.dadosColetados).forEach(key => {
                this.data.dadosColetados[key as keyof QuestionarioPreConsultaDados] = '';
            });
            if (this.data.snackBar) this.data.snackBar.sucessoSnackbar('Todos os dados foram limpos');
            if (this.data.cdr) this.data.cdr.detectChanges();
        }
    }

    exportData(event: Event): void {
        event.stopPropagation();
        const dataStr = JSON.stringify(this.data.dadosColetados, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'dados-preenchidos.json';
        a.click();
        window.URL.revokeObjectURL(url);
    }
}