import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-modal-info-sobre-usuario',
    templateUrl: './modal-info-sobre-usuario.component.html',
    styleUrls: ['./modal-info-sobre-usuario.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon,
      MatFormFieldModule,
      TranslateModule
    ]
})
export class ModalInfoSobreUsuarioComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ModalInfoSobreUsuarioComponent>,
    @Inject(MAT_DIALOG_DATA) public dados:any
  ) { }

  ngOnInit(): void {
    
    this.DadosInformUsuario = this.dados;
    
  }
  DadosInformUsuario: any = [];
}
