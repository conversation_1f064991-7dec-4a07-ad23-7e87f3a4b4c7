import { Injectable } from "@angular/core";
import { HttpClient } from '@angular/common/http';
import { msgResposta } from "../model/retorno-resposta";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs";
import { novoItem, tipoOrientacao } from "../model/itens";
import { SpinnerService } from "./spinner.service";

@Injectable({
    providedIn: 'root'
})
export class ItemService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) { }

    public SalvarNovoItem(objItem: novoItem): Observable<msgResposta> {
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/item/AdicionarItem', objItem)
    }

    public GetItens() {
        this.spinner.show();
        return this.http.get<novoItem[]>(environment.apiEndpoint + '/item/GetItem')
    }

    public deleteItem(id: number) {
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/item/DeleteItem', id)
    }

    public GetObjItem(IdItem: number) {
        this.spinner.show();
        return this.http.post<novoItem>(environment.apiEndpoint + '/item/GetObjItem', IdItem)
    }

    public GetTipoOrientacao() {
        this.spinner.show();
        return this.http.get<tipoOrientacao[]>(environment.apiEndpoint + '/item/GetTipoOrientacao')
    }
}