import { Injectable } from '@angular/core';
import * as cryptojs from "crypto-js";
import { environment } from 'src/environments/environment';

@Injectable({
    providedIn: 'root'
})
export class CryptoService {

	private key: string = "";

	constructor() {
		this.key = environment.secretKey;
	}

	public decrypt(value: string): string {        
		const decryptedOutput = cryptojs.AES.decrypt(value, this.key);
		const plainTextOutput = decryptedOutput.toString(cryptojs.enc.Utf8);
		return plainTextOutput.toString();
	}

	public encrypt(value: string): string {        
		return cryptojs.AES.encrypt(value, this.key).toString();
	}

}
