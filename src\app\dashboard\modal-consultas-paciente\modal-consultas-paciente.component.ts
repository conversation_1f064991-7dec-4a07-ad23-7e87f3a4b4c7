import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatCardModule } from '@angular/material/card';

export interface Consulta {
  id: number;
  nomeMedico: string;
  dataConsulta: Date;
  flgPresencial: boolean;
  linkConsulta?: string;
}

export interface ModalData {
  consultas: Consulta[];
  nomePaciente?: string;
}

@Component({
  selector: 'app-modal-consultas-paciente',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    MatCardModule
  ],
  templateUrl: './modal-consultas-paciente.component.html',
  styleUrl: './modal-consultas-paciente.component.scss'
})
export class ModalConsultasPacienteComponent {

  constructor(
    public dialogRef: MatDialogRef<ModalConsultasPacienteComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ModalData
  ) { }

  onClose(): void {
    this.dialogRef.close();
  }

  onAcessarConsulta(consulta: Consulta): void {
    if (consulta.linkConsulta) {
      window.open(consulta.linkConsulta, '_blank');
    }
  }

  formatarData(data: Date): string {
    return new Date(data).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getTipoConsulta(flgPresencial: boolean): string {
    return flgPresencial ? 'Presencial' : 'Online';
  }
}