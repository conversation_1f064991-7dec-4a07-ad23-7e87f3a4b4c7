import { Component } from '@angular/core';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { ConsultaService } from '../service/consulta.service';
import {
  subMonths, subDays, subWeeks
} from 'date-fns';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { Router } from '@angular/router';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { DadosConsultaHojeModelView, irParaConsulta } from '../model/consulta';
import { AgendaService } from '../service/agenda.service';
import { PacienteService } from '../service/pacientes.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { PagamentoService } from '../service/pagamento.service';
import { TelemedicinaComponentBase } from '../Util/component.base';
import { SignalHubService } from '../service/signalHub.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { ChartData, ChartEvent, ChartType } from 'chart.js';
import { SpinnerService } from '../service/spinner.service';
import { AppComponent } from '../app.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CalendarModule } from 'angular-calendar';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCardModule } from '@angular/material/card';
type CalendarPeriod = 'day' | 'week' | 'month';import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { NativeDateAdapter } from '@angular/material/core';

function subPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
  return {
    day: subDays,
    week: subWeeks,
    month: subMonths
  }[period](date, amount);
}

@Component({
  selector: 'app-colunadireita',
  templateUrl: './colunadireita.component.html',
  styleUrls: ['./colunadireita.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    MatIconModule,
    NgxSmartModalModule,
    MatTooltipModule,
    CalendarModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCardModule,
  ],
  providers: [
    { provide: DateAdapter, useClass: NativeDateAdapter },
    { provide: MAT_DATE_LOCALE, useValue: 'pt-BR' }
  ]
})



export class ColunadireitaComponent extends TelemedicinaComponentBase {

   selectedDate: Date = new Date();
  
  // Eventos de exemplo (opcional)
 appointments: any[] = [
    // Maio 2025
    { 
      date: new Date(2025, 4, 14), 
      title: 'Carlos Silva - Retorno',
      time: '09:00', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 14), 
      title: 'Maria Oliveira - 1ª Consulta',
      time: '10:00', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 14), 
      title: 'José Santos - Exames',
      time: '11:30', 
      duration: '30 min',
      type: 'exam', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 15), 
      title: 'Ana Costa - 1ª Consulta',
      time: '14:00', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 15), 
      title: 'João Pereira - Retorno',
      time: '15:00', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 16), 
      title: 'Mariana Souza - Emergência',
      time: '09:30', 
      duration: '60 min',
      type: 'emergency', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 17), 
      title: 'Pedro Alves - Retorno',
      time: '13:00', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 17), 
      title: 'Luísa Ferreira - 1ª Consulta',
      time: '16:15', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 20), 
      title: 'Roberto Dias - Exames',
      time: '10:30', 
      duration: '30 min',
      type: 'exam', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 20), 
      title: 'Carla Mendes - Retorno',
      time: '11:30', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 20), 
      title: 'Marcelo Lima - 1ª Consulta',
      time: '14:45', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 21), 
      title: 'Juliana Martins - Emergência',
      time: '08:30', 
      duration: '60 min',
      type: 'emergency', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 22), 
      title: 'Antônio Gomes - Retorno',
      time: '09:15', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'pending'
    },
    { 
      date: new Date(2025, 4, 22), 
      title: 'Beatriz Rocha - 1ª Consulta',
      time: '11:00', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 23), 
      title: 'Ricardo Torres - Exames',
      time: '15:30', 
      duration: '30 min',
      type: 'exam', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 24), 
      title: 'Fernanda Castro - Retorno',
      time: '10:00', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 27), 
      title: 'Gabriel Vieira - 1ª Consulta',
      time: '14:00', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 28), 
      title: 'Carolina Pinto - Emergência',
      time: '16:30', 
      duration: '60 min',
      type: 'emergency', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 4, 29), 
      title: 'Daniel Santos - Retorno',
      time: '11:15', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'pending'
    },
    { 
      date: new Date(2025, 4, 30), 
      title: 'Patrícia Almeida - 1ª Consulta',
      time: '09:45', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    
    // Junho 2025 (primeiros dias)
    { 
      date: new Date(2025, 5, 2), 
      title: 'Rodrigo Nunes - Exames',
      time: '10:30', 
      duration: '30 min',
      type: 'exam', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 5, 3), 
      title: 'Amanda Lopes - Retorno',
      time: '14:15', 
      duration: '30 min',
      type: 'return', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    },
    { 
      date: new Date(2025, 5, 4), 
      title: 'Felipe Cordeiro - 1ª Consulta',
      time: '09:00', 
      duration: '45 min',
      type: 'first', 
      specialty: 'Cardiologia',
      status: 'pending'
    },
    { 
      date: new Date(2025, 5, 5), 
      title: 'Camila Ribeiro - Emergência',
      time: '11:30', 
      duration: '60 min',
      type: 'emergency', 
      specialty: 'Cardiologia',
      status: 'confirmed'
    }
  ];
   onDateSelected(date: Date | null): void {
    if (date) {
      this.selectedDate = date;
      console.log('Data selecionada:', date);
    }
  }
  
  // Método para adicionar classes a datas com consultas
  dateClass = (date: Date): string => {
    // Verifica quantas consultas existem para esta data
    const appointmentsForDate = this.appointments.filter(appointment => 
      appointment.date.getDate() === date.getDate() &&
      appointment.date.getMonth() === date.getMonth() &&
      appointment.date.getFullYear() === date.getFullYear()
    );
    
    if (appointmentsForDate.length === 0) {
      return '';
    }
    
    // Verificar se há consultas de emergência
    const hasEmergency = appointmentsForDate.some(appointment => appointment.type === 'emergency');
    if (hasEmergency) {
      return 'appointment-date emergency-appointment';
    }
    
    // Verificar se há primeiras consultas
    const hasFirstVisit = appointmentsForDate.some(appointment => appointment.type === 'first');
    if (hasFirstVisit) {
      return 'appointment-date first-appointment';
    }
    
    // Para outras consultas
    return 'appointment-date regular-appointment';
  }
  
  // Método para obter consultas da data selecionada
  getAppointmentsForSelectedDate(): any[] {
    return this.appointments.filter(appointment => 
      appointment.date.getDate() === this.selectedDate.getDate() &&
      appointment.date.getMonth() === this.selectedDate.getMonth() &&
      appointment.date.getFullYear() === this.selectedDate.getFullYear()
    ).sort((a, b) => {
      // Ordenar por horário
      return a.time.localeCompare(b.time);
    });
  }
  
  // Método para obter a classe CSS com base no tipo de consulta
  getAppointmentClass(type: string): string {
    switch(type) {
      case 'first': return 'first-visit';
      case 'return': return 'return-visit';
      case 'emergency': return 'emergency-visit';
      case 'exam': return 'exam-visit';
      default: return '';
    }
  }
  
  // Método para obter a classe CSS com base no status da consulta
  getStatusClass(status: string): string {
    return status === 'confirmed' ? 'status-confirmed' : 'status-pending';
  }



  // Doughnut
  public doughnutChartLabels: string[] = ['Consultas Realizadas', 'Consultas Canceladas'];
  public doughnutChartData: ChartData<'doughnut'> = {
    labels: this.doughnutChartLabels,
    datasets: [
      {
        data: [150, 120],
        backgroundColor: [
          'rgb(54, 162, 235)',
          'rgb(201, 201, 201)'
        ],
      },
    ]
  };
  public doughnutChartType: ChartType = 'doughnut';

  // events
  public chartClicked({ }: { event: ChartEvent, active: {}[] }): void {
  }

  public chartHovered({ }: { event: ChartEvent, active: {}[] }): void {
  }

  constructor(
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private agendaService: AgendaService,
    private pacienteService: PacienteService,
    private appc: AppComponent,
    private consultaService: ConsultaService,
    private pagamentoService: PagamentoService,
    private router: Router,
    // public snackBar: MatSnackBar,
    public usuarioLogadoService: UsuarioLogadoService,
    private signalHubService: SignalHubService,
    private localStorageService: LocalStorageService,
  ) {
    super();

    this.pagamentoService.changeconsulta$
      .subscribe((perf: any) => {
        if (perf != null) {
          var consulta = new irParaConsulta();
          consulta.idPagamento = perf.idPagamento;
          consulta.idConsulta = this.pagamentoService.getIdconsulta()!;
          consulta.flgSomenteProntuario = false
          this.localStorageService.Consulta = consulta;
          this.router.navigate(['/streaming']);
        }
      });

    this.agendaService.atualizaDadosMes$
      .subscribe((resp: any) => {

        if (resp) {
          this.CarregaDados()
        }
      });

    this.pacienteService.atualizaDadosMes$
      .subscribe((resp: any) => {

        if (resp) {
          this.CarregaDados()
        } else {
          ;
        }
      });

    this.signalHubService.changeConsulta$
      .subscribe(() => {
        this.Carregaconsultas();
      });

    this.signalHubService.OnDisparaAlertFila
      .subscribe(() => {
        this.Carregaconsultas();
      });

    this.signalHubService.OnDisparaAlertFilaClinica
      .subscribe(() => {
        this.Carregaconsultas();
      });

    this.signalHubService.OnDisparaAlertConsulta
      .subscribe(() => {
        this.Carregaconsultas();
      });
  }

  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  concordo?: boolean;
  concordomsg?: boolean;

  dadosRelatorio: any = [];
  ListaConsultas: DadosConsultaHojeModelView[] = []

  mesanterior?: Date;
  tableText: any;
  CartasBoasvindas = 0;
  DadosUsuario: any;
  dadosMes: any;
  ImagemPessoa: any = "assets/build/img/avatar-medico.png";

  tempo: string = '00:20'
  view: CalendarPeriod = 'month';
  viewDate: Date = new Date();
  AvaliacaoAtual: number = 0;
  AvaliacaoAnterior: number = 0;
  ConsultasAtual: number = 0;
  ConsultasAnterior: number = 0;
  PacientesAtual: number = 0;
  PacientesAnterior: number = 0;
  TotalPacientes: number = 0;
  TotalConsultas: number = 0;

  ConsultasHoje: number = 0;

  AdmPermissao = false;
  MedicoPermissao = false;
  AtendentePermissao = false;
  PacientePermissao = false;
  consultaAgora = false;
  usuarioConsulta: any = []
  // usuario: Usuario;
  tipoUsuario?: string;
  idEmail = 0;
  consultas: any;
  isHubConnected: boolean = false;
  timeInterval: any;
  // locale: string = this.tradutor.store.currentLang;
  locale: string = "pt";
  FlgSomenteProntuario: boolean = false;

  stream = false;
  stream1 = false;
  conect = false;

  // ListaConsultasMedico : 

  ngOnInit() {


    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM) {
      this.tipoUsuario = 'ADM Sistema';
      this.AdmPermissao = true;
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      this.decrement();
      // this.CarregaQuantBoasVindas()
      this.CarregaRelatorioLegenda();
    } else if (this.usuarioLogadoService.getIdTipoUsuario() === EnumTipoUsuario.Atendente) {
      this.tipoUsuario = 'Atendente';
      this.AtendentePermissao = true;
      this.MedicoPermissao = false;
      this.PacientePermissao = true;
      this.decrement();
      this.CarregaRelatorioLegenda()
      // this.CarregaQuantBoasVindas()
    } else if (this.usuarioLogadoService.getIdTipoUsuario() === EnumTipoUsuario.Médico) {
      this.tipoUsuario = 'Médico';
      this.MedicoPermissao = true
      this.decrement();
      this.CarregaRelatorioLegenda()

      this.Carregaconsultas();
    } else if (this.usuarioLogadoService.getIdTipoUsuario() === EnumTipoUsuario.Paciente) {
      this.tipoUsuario = 'Paciente';
      this.PacientePermissao = true;
      // if (this.usuario.flgProntuario != true) {
      this.carregaconsulta_Paciente_ColunaDireita()
    }

    this.consultaService.atualizaLegenda$
      .subscribe(() => this.CarregaRelatorioLegenda());

    this.consultaService.atualizaComparativo$
      .subscribe(() => this.CarregaDados());

    this.CarregarDadosTab();
    this.CarregaDados()

    // this.playAudio();

    var imagem = this.usuarioLogadoService.getImagem64();
    if (imagem != null && imagem != "")
      this.ImagemPessoa = imagem;

  }
  carregaconsulta_Paciente_ColunaDireita() {
    this.consultaService.carregaconsulta_Paciente_ColunaDireita(this.usuarioLogadoService.getIdUsuarioAcesso()!).subscribe((ret) => {
      this.spinner.hide();
      this.consultas = ret;

    })
  }

  abriConsultas() {
    this.router.navigate(["consulta"])
  }

  playAudio() {
    let audio = new Audio();
    audio.src = "../../../assets/build/som/alert.mp3";
    audio.load();
    audio.play();
  }

  async Carregaconsultas() {
    var minutos = 20;
    this.consultaAgora = false;
    await this.consultaService.GetConsultaAlerta(this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdTipoUsuario(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.consultas = retorno;
      
      // if (retorno.length == 0) {
      //   this.ngxSmartModalService.getModal('consulta').close();
      // }
      this.usuarioConsulta = [];
      this.consultas.forEach((element: any) => {

        if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico && element.idPacientePessoa == this.usuarioLogadoService.getIdPessoa()) {
          return;
        }
        var teste = new Date(element.dtaconsulta)
        teste.setMinutes(teste.getMinutes() - minutos);
        var data = new Date()
        data.setMinutes(data.getMinutes() - minutos);

        if (this.tipoUsuario == 'Paciente') {
          if (!element.flgProntuario) {
            if (element.flgFilaEspera || (teste >= data && teste <= new Date())) {
              if (element.imagemPaciente != null) {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante

                })
              }
              else {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }

              this.consultaAgora = true;

              return;
            }
          }
        }
        else {
          if (element.flgFilaEspera || element.flgCheckin || (teste >= data && teste <= new Date())) {
            if (element.imagemPaciente != null) {
              this.usuarioConsulta.push({
                nome: element.paciente,
                idconsulta: element.idconsulta,
                dtaconsulta: element.dtaconsulta,
                tipoAgendamento: element.tipoconsulta,
                primeiraConsulta: element.flgConsulta,
                Clinica: element.nomeClinica,
                nomeMedico: element.medico,
                flgSomenteProntuario: element.flgSomenteProntuario,
                flgFilaEspera: element.flgFilaEspera,
                ImagemPessoa: element.imagemPaciente,
                idMedicoPessoa: element.idMedicoPessoa,
                idPacientePessoa: element.idPacientePessoa,
                IdPagamento: element.idPagamento,
                IdConvenio: element.idConvenio,
                valorConsulta: element.valorConsulta,
                flgExigePagamento: element.flgExigePagamento,
                PacienteSolicitante: element.pacienteSolicitante,
                UsuarioSolicitante: element.usuarioSolicitante
              })
            }
            else {
              this.usuarioConsulta.push({
                nome: element.paciente,
                idconsulta: element.idconsulta,
                dtaconsulta: element.dtaconsulta,
                tipoAgendamento: element.tipoconsulta,
                primeiraConsulta: element.flgConsulta,
                Clinica: element.nomeClinica,
                nomeMedico: element.medico,
                flgSomenteProntuario: element.flgSomenteProntuario,
                flgFilaEspera: element.flgFilaEspera,
                ImagemPessoa: element.imagemPaciente,
                idMedicoPessoa: element.idMedicoPessoa,
                idPacientePessoa: element.idPacientePessoa,
                IdPagamento: element.idPagamento,
                IdConvenio: element.idConvenio,
                valorConsulta: element.valorConsulta,
                flgExigePagamento: element.flgExigePagamento,
                PacienteSolicitante: element.pacienteSolicitante,
                UsuarioSolicitante: element.usuarioSolicitante
              })
            }

            this.consultaAgora = true;

            return;
          }

        }
      });


      // if (this.usuarioConsulta.length == 0)
      //   this.ngxSmartModalService.getModal('consulta').close();


    })



    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }


    // procura o adiciona o som

    this.timeInterval = setInterval(() => {

      this.consultaAgora = false;
      this.usuarioConsulta = [];
      if (this.consultas) {
        this.consultas.forEach((element: any) => {
          if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico && element.idPacientePessoa == this.usuarioLogadoService.getIdPessoa()) {
            return;
          }

          var teste = new Date(element.dtaconsulta)
          teste.setMinutes(teste.getMinutes() - minutos);
          var data = new Date()
          data.setMinutes(data.getMinutes() - minutos);

          if (this.tipoUsuario == 'Paciente') {
            if (!element.flgProntuario) {
              if (element.imagemPaciente != null) {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante

                })
              }
              else {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }

              this.consultaAgora = true;

              return;

            }
          }
          else {
            if (element.flgFilaEspera || element.flgCheckin || (teste >= data && teste <= new Date())) {
              if (element.imagemPaciente != null) {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }
              else {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }

              this.consultaAgora = true;

              return;
            }

          }
        });
        // if (this.usuarioConsulta.length == 0)
        //   this.ngxSmartModalService.getModal('consulta').close();

      }
    }, 60000);


  }


  Medicoentrouconsulta(id: any, flg: any) {
    this.consultaService.MedicoOnlinenaConsulta(id, this.usuarioLogadoService.getIdPessoa()).subscribe(() => {
      var consulta = new irParaConsulta();
      consulta.idConsulta = id;
      consulta.flgSomenteProntuario = flg
      this.localStorageService.Consulta = consulta;
      // sessionStorage.setItem("TelaStream", "true");


      this.router.navigate(['/streaming']);
    })
  }


  IrConsulta(id: any, flg: any) {
    if (id != "" && id != 0) {
      var Objconsulta = this.usuarioConsulta.filter((c: any) => c.idconsulta == id);
      if (Objconsulta[0].flgExigePagamento == true && Objconsulta[0].IdPagamento == null && this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
        this.pagamentoService.setIdPagador(Objconsulta[0].idPacientePessoa);
        this.pagamentoService.setIdRecebedora(Objconsulta[0].idMedicoPessoa);
        this.pagamentoService.setValor(Objconsulta[0].valorConsulta);
        this.pagamentoService.setIdconsulta(id);
        this.pagamentoService.setAbrirModal(true);
        this.ngxSmartModalService.getModal('ModalPagamento').open();
      }

      else {
        if (Objconsulta.length > 0 && Objconsulta[0].flgFilaEspera && this.usuarioLogadoService.getIdTipoUsuario() != EnumTipoUsuario.Paciente) {
          this.Medicoentrouconsulta(id, flg);

        }
        else {
          var consulta = new irParaConsulta();
          consulta.idConsulta = id;
          consulta.flgSomenteProntuario = flg
          this.localStorageService.Consulta = consulta;
          // sessionStorage.setItem("TelaStream", "true");
          if (flg) {

            this.conect = false;
            this.stream1 = true;
            this.stream = false;
            this.router.navigate(['/prontuario']);

          }
          else
            this.router.navigate(['/streaming']);
        }

      }


      // this.appc.ngOnInit();
    }
  }
  Motivocancelameto: string = "";
  idCancela?: number;
  cancelamento: boolean = false;
  CancelaValue(id: any) {

    this.Motivocancelameto = ''
    this.idCancela = id
    this.ngxSmartModalService.getModal('cancelarHorarioCoracao').open();

  }
  MotivoCampo() {
    if (this.cancelamento == true)
      this.cancelamento = false
  }
  CancelarConsulta() {

    if (this.Motivocancelameto == '' || this.Motivocancelameto == undefined) {
      this.cancelamento = true;
      return;
    }

    this.agendaService.InativarAgendamento(this.idCancela, this.usuarioLogadoService.getIdUsuarioAcesso(), this.Motivocancelameto).subscribe(async () => {
      this.consultaAgora = false;
      await this.Carregaconsultas();
      this.ngxSmartModalService.getModal('cancelarHorarioCoracao').close();
      // if (this.usuarioConsulta.length == 0)
      //   this.ngxSmartModalService.getModal('consulta').close();
      this.spinner.hide();

    }, err => {
      console.error(err);
      this.spinner.hide();
    })
  }

  // modalConsulta() {
  //   this.ngxSmartModalService.getModal('consulta').open();

  // }

  decrement(): void {
    this.changeDate(subPeriod(this.view, this.viewDate, 1));
  }

  changeDate(date: Date): void {
    this.mesanterior = date;
  }

  CarregarDadosTab() {
    this.tableText = []
    this.tableText.push({ Consulta: '10', Consultanextw: '12', Pacientes: '4', TotalB: 'R$', TotalL: 'R$' })
  }


  CarregaDados() {
    this.consultaService.GetDadosMesDashBoard(this.usuarioLogadoService.getIdPessoa(), this.usuarioLogadoService.getIdUltimaClinica(), this.usuarioLogadoService.getIdTipoUsuario()).subscribe((retorno) => {
      this.dadosMes = retorno;
      this.dadosMes.forEach((element: any) => {
        if (element.Mes == 'Atual') {
          if (element.Dados == 'Consultas')
            this.ConsultasAtual = element.qtd
          else if (element.Dados == "Pacientes")
            this.PacientesAtual = element.qtd
          else if (element.Dados == "Avaliações")
            this.AvaliacaoAtual = element.qtd
          else if (element.Dados == "Total Pacientes")
            this.TotalPacientes = element.qtd
        }
        else {
          if (element.Dados == 'Consultas')
            this.ConsultasAnterior = element.qtd
          else if (element.Dados == "Pacientes")
            this.PacientesAnterior = element.qtd
          else if (element.Dados == "Avaliações")
            this.AvaliacaoAnterior = element.qtd
          else if (element.Dados == "Total Consultas")
            this.TotalConsultas = element.qtd

        }

      });

    })

  }

  CarregaRelatorioLegenda() {

    this.consultaService.DadosRelatorio(this.usuarioLogadoService.getIdPessoa(), this.usuarioLogadoService.getIdUltimaClinica(), this.usuarioLogadoService.getIdTipoUsuario()).subscribe((retorno) => {

      this.dadosRelatorio = retorno[0]
    })

    // this.consultaService.GetConsultasHoje(this.usuarioLogadoService.getIdMedico()).subscribe((ret)=>{
    //   this.ListaConsultas = ret;
    //       //       // },erro=>{
    //   ;
    // })
  }



  // startSignalR() {

  //   this.hubConnection
  //     .start()
  //     .then(async (res) => {
  //       this.isHubConnected = true;
  //       const user = {
  //         idUsuario: this.usuarioLogadoService.getIdUsuarioAcesso(),
  //         idClinica: this.usuarioLogadoService.getIdUltimaClinica()
  //       };
  //       this.hubConnection.send('UserLogado', user);
  //     })
  //     .catch(err => {
  //       console.error('Start Error!', err);
  //       this.isHubConnected = false;
  //     });

  // }

  // initializeSignalIR() {

  //   const buildHubConn = new HubConnectionBuilder();
  //   buildHubConn.configureLogging(LogLevel.Information)
  //   buildHubConn.withUrl(environment.apiEndpoint + '/notify');
  //   this.hubConnection = buildHubConn.build();

  //   this.startSignalR();

  //   this.hubConnection.onclose((err) => {
  //     setTimeout(() => this.startSignalR(), 1000);
  //   });

  //   if (this.usuarioLogadoService.getIdTipoUsuario() === EnumTipoUsuario.Médico) {
  //     this.hubConnection.on('DisparaAlertFila', (res: string) => {
  //       this.Carregaconsultas();
  //     });
  //   }

  //   this.hubConnection.on('DisparaAlertConsulta', (res: string) => {
  //     this.Carregaconsultas();
  //   });

  // }

  // AlgumaMenssagem(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  abas: any;
  DtaFim?: string;
  DtaInicio?: string;
  dadosExcelFor: any;
  dadosExcel: any;
  FillerOpen = false;

  AbreTelaConsulta() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/consulta']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  formatarData_Medico(dtaConsulta: string): string {
    const data = new Date(dtaConsulta);
    const hoje = new Date();

    const diaConsulta = data.getDate();
    const mesConsulta = data.getMonth() + 1;
    const anoConsulta = data.getFullYear();
    const horaConsulta = this.adicionarZeroEsquerda_Data(data.getHours());
    const minutoConsulta = this.adicionarZeroEsquerda_Data(data.getMinutes());

    const diaHoje = hoje.getDate();
    const mesHoje = hoje.getMonth() + 1;
    const anoHoje = hoje.getFullYear();

    if (
      diaConsulta === diaHoje &&
      mesConsulta === mesHoje &&
      anoConsulta === anoHoje
    ) {
      return `Hoje ${horaConsulta}:${minutoConsulta}`;
    } else {
      return `${diaConsulta}-${mesConsulta}-${anoConsulta} ${horaConsulta}:${minutoConsulta}`;
    }
  }

  adicionarZeroEsquerda_Data(valor: number): string {
    return valor < 10 ? `0${valor}` : `${valor}`;
  }
  Agenda() {
    sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(['/calendario']);
    this.appc.ngOnInit();
  }
}
