<div class="container">
  <!-- MODAL DE CONFIRMAÇÃO -->
  <div *ngIf="flgmodal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Confirmação</h3>
      </div>
      <div class="modal-body">
        <p class="modal-text">Deseja realmente excluir esse local?</p>
      </div>
      <div class="modal-footer">
        <button class="btn-cancelar" (click)="Excluir(false)">Cancelar</button>
        <button class="btn-confirmar btn-excluir" (click)="Excluir(true)">Excluir</button>
      </div>
    </div>
  </div>

  <mat-card class="card-principal">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">near_me</span>
      </div>
      <h2 class="header-title">Cadastro de Locais</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">
      <div class="busca-container">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>Buscar</mat-label>
          <input matInput type="search" [(ngModel)]="filtroBusca" (keyup)="filtrarLocais()">
          <button mat-icon-button matSuffix class="btn-busca">
            <mat-icon>search</mat-icon>
          </button>
        </mat-form-field>
      </div>
      
      <div class="adicionar-container">
        <button class="btn-adicionar" (click)="adicionarlocais()">
          <mat-icon>add</mat-icon>
          <span>Adicionar Local</span>
        </button>
      </div>
    </div>

    <!-- LISTA DE LOCAIS -->
    <div class="lista-container">
      <div class="lista-scroll">
        <div class="local-card" *ngFor="let locais of listaLocais">
          <!-- INFO DO LOCAL -->
          <div class="local-info">
            <div class="local-avatar">
              <img src="{{ ImagemPessoa }}" class="img-circle" alt="Foto do local">
            </div>
            <div class="local-detalhes">
              <div class="info-item">
                <mat-icon>location_on</mat-icon>
                <span class="nome">{{locais.nome}}</span>
              </div>
              <div class="info-item">
                <mat-icon>phone</mat-icon>
                <span>{{locais.telefone}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span>{{locais.email}}</span>
              </div>
            </div>
          </div>

          <!-- DATA DE CADASTRO -->
          <div class="local-data">
            <div class="data-item">
              <label class="data-label">Data de Cadastro</label>
              <span class="data-valor">{{locais.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
            </div>
          </div>

          <div class="col-md-3 col-sm-12 col-xs-3 btns-tabela">
            <!-- <button mat-icon-button class="panel_button">
                  <mat-icon aria-label="Visualizar" title="Visualizar">remove_red_eye</mat-icon>
                </button> -->
            <!-- <button mat-icon-button class="panel_button" (click)="AbrirModalListaPacientes(locais!.idLocal!)">
              <mat-icon>send</mat-icon>
            </button> -->
            <button mat-icon-button class="panel_button" (click)="Editar(locais!.idLocal!)">
              <mat-icon aria-label="Editar linha selecionada" class="">edit</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Excluir" (click)="excluirLocal(locais!.idLocal!)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="listaLocais?.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhum local encontrado</p>
        </div>
      </div>
    </div>
  </mat-card>
</div>