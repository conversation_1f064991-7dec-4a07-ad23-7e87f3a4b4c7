import { Component, OnInit } from '@angular/core';

@Component({
    selector: 'app-relogio',
    templateUrl: './relogio.component.html',
    styleUrls: ['./relogio.component.scss'],
      standalone: true,
    imports: [
      
    ]
})
export class RelogioComponent implements OnInit {

  constructor() { }

  time: string = '';
  private intervalId: any;

  dataAtual:any;

  ngOnInit(): void {
    this.updateTime();
    this.intervalId = setInterval(() => this.updateTime(), 1000);

    this.dataAtual = this.formatDate();
  }

  ngOnDestroy() {
    clearInterval(this.intervalId);
  }

  formatDate(): string {
    const dateObject = new Date();
    const day = String(dateObject.getDate()).padStart(2, '0');
    const month = String(dateObject.getMonth() + 1).padStart(2, '0'); // Janeiro é 0
    const year = dateObject.getFullYear();

    return `${day}/${month}/${year}`;
  }

  updateTime() {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    this.time = `${hours}:${minutes}:${seconds}`;
  }
}
