<div class="container">
    <mat-card class="card-principal mother-div">
      <!-- CABEÇALHO -->
      <div class="header">
        <div class="header-icon">
          <span class="material-icons">domain</span>
        </div>
        <h2 class="header-title">{{ 'TELAPESQUISACLINICA.CLINICA' | translate }}</h2>
      </div>
  
      <!-- FILTROS -->
      <div class="filtros">
        <div class="busca-container">
          <mat-form-field appearance="outline" class="busca-field">
            <mat-label>{{ 'TELAPESQUISACLINICA.BUSCAR' | translate }}</mat-label>
            <input matInput [(ngModel)]='pesquisa' (keyup.enter)="CarregaTable()">
            <button mat-icon-button matSuffix (click)="CarregaTable()" class="btn-busca">
              <mat-icon>search</mat-icon>
            </button>
          </mat-form-field>
        </div>
  
        <div class="adicionar-container">
          <button mat-raised-button class="btn-adicionar" [routerLink]="['/cadastroclinica']">
            <mat-icon>add</mat-icon>
            <span>{{ 'TELAPESQUISACLINICA.ADICIONARCLINICA' | translate }}</span>
          </button>
        </div>
      </div>
      <div class="toggles-container">
        <mat-slide-toggle [(ngModel)]='Logo' class="toggle-item">
          {{ 'TELAPESQUISACLINICA.MOSTRARLOGO' | translate }}
        </mat-slide-toggle>
        <mat-slide-toggle [(ngModel)]='inativos' (change)="CarregaTable()" class="toggle-item">
          {{ 'TELAPESQUISACLINICA.INATIVOS' | translate }}
        </mat-slide-toggle>
      </div>
  
      <!-- LISTA DE CLÍNICAS - DESKTOP VIEW -->
      <div class="lista-container desktop-view">
        <div class="lista-scroll">
          <div class="clinica-card" *ngFor="let item of DadosTab">
            <!-- INFO DA CLÍNICA COM LOGO -->
            <div class="clinica-info" *ngIf="Logo">
              <div class="clinica-avatar">
                <img src="{{ item.logo == null ? imagemclinica : item.logo}}" class="img-circle" alt="Logo da clínica">
              </div>
              <div class="clinica-detalhes">
                <div class="info-item">
                  <mat-icon>domain</mat-icon>
                  <span class="nome">{{item.nomeClinica}}</span>
                </div>
                <div class="info-item">
                  <mat-icon>phone</mat-icon>
                  <span>{{item.tel}}</span>
                </div>
                <div class="info-item">
                  <mat-icon>mail</mat-icon>
                  <span>{{item.email}}</span>
                </div>
              </div>
            </div>
  
            <!-- INFO DA CLÍNICA SEM LOGO -->
            <div class="clinica-info" *ngIf="!Logo">
              <div class="clinica-detalhes sem-logo">
                <div class="info-item">
                  <mat-icon>domain</mat-icon>
                  <span class="nome">{{item.nomeClinica}}</span>
                </div>
                <div class="info-item">
                  <mat-icon>phone</mat-icon>
                  <span>{{item.tel}}</span>
                </div>
                <div class="info-item">
                  <mat-icon>mail</mat-icon>
                  <span>{{item.email}}</span>
                </div>
              </div>
            </div>
  
            <!-- DADOS ADICIONAIS -->
            <div class="clinica-dados">
              <div class="dados-item">
                <label class="dados-label">{{ 'TELAPESQUISACLINICA.DATADECADASTRO' | translate }}</label>
                <span class="dados-valor">{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
              </div>
              <div class="dados-item">
                <label class="dados-label">{{ 'TELAPESQUISACLINICA.CNPJ:' | translate }}</label>
                <span class="dados-valor">{{item.cnpj}}</span>
              </div>
              <div class="dados-item">
                <label class="dados-label">{{ 'TELAPESQUISACLINICA.ESPECIALIZACAO:' | translate }}</label>
                <span class="dados-valor">{{item.carac}}</span>
              </div>
            </div>
  
            <!-- AÇÕES -->
            <div class="clinica-acoes">
              <button mat-icon-button matTooltip="{{ 'TELAPESQUISACLINICA.EDITARCLINICA' | translate }}" (click)="editClinica(item.idclinica)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button class="remove" *ngIf="!inativos" matTooltip="{{ 'TELAPESQUISACLINICA.EXCLUIRCLINICA' | translate }}" (click)="ValorUsuario(item.idclinica)">
                <mat-icon>delete</mat-icon>
              </button>
              <button mat-icon-button *ngIf="inativos" matTooltip="{{ 'TELAPESQUISACLINICA.ATIVARCADASTRO' | translate }}" (click)="ValorUsuarioAtivar(item.idclinica)">
                <mat-icon>check</mat-icon>
              </button>
            </div>
          </div>
          
          <!-- MENSAGEM DE LISTA VAZIA -->
          <div class="lista-vazia" *ngIf="DadosTab?.length === 0">
            <mat-icon>sentiment_very_dissatisfied</mat-icon>
            <p>Nenhuma clínica encontrada</p>
          </div>
        </div>
      </div>
  
      <!-- LISTA DE CLÍNICAS - MOBILE VIEW -->
      <div class="lista-mobile mobile-view">
        <mat-card class="clinica-card-mobile" *ngFor="let item of DadosTab; let i = index">
          <div class="clinica-header-mobile">
            <div class="clinica-avatar-mobile" *ngIf="Logo">
              <img src="{{ item.logo == null ? imagemclinica : item.logo}}" class="img-circle" alt="Logo da clínica">
            </div>
            <div class="clinica-info-mobile">
              <h3 class="clinica-nome-mobile">{{item.nomeClinica}}</h3>
              <p class="clinica-titulo-mobile">{{ 'TELAPESQUISACLINICA.CLINICA' | translate }}</p>
            </div>
          </div>
  
          <mat-divider></mat-divider>
  
          <div class="clinica-dados-mobile">
            <div class="dados-mobile-item">
              <h4>Telefone</h4>
              <p>{{item.tel}}</p>
            </div>
            <div class="dados-mobile-item">
              <h4>E-mail</h4>
              <p>{{item.email}}</p>
            </div>
            <div class="dados-mobile-item">
              <h4>{{ 'TELAPESQUISACLINICA.DATADECADASTRO' | translate }}</h4>
              <p>{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</p>
            </div>
            <div class="dados-mobile-item">
              <h4>{{ 'TELAPESQUISACLINICA.CNPJ' | translate }}</h4>
              <p>{{item.cnpj}}</p>
            </div>
            <div class="dados-mobile-item">
              <h4>{{ 'TELAPESQUISACLINICA.ESPECIALIZACAO' | translate }}</h4>
              <p>{{item.carac}}</p>
            </div>
          </div>
  
          <!-- AÇÕES MOBILE -->
          <div class="acoes-mobile">
            <div class="acoes-buttons" [@openClose]="toggle[i] ? 'open': 'closed'">
              <button mat-mini-fab (click)="editClinica(item.idclinica)" 
                      matTooltip="{{ 'TELAPESQUISACLINICA.EDITARCLINICA' | translate }}">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-mini-fab *ngIf="!inativos" (click)="ValorUsuario(item.idclinica)" 
                      matTooltip="{{ 'TELAPESQUISACLINICA.EXCLUIRCLINICA' | translate }}">
                <mat-icon>delete</mat-icon>
              </button>
              <button mat-mini-fab *ngIf="inativos" (click)="ValorUsuarioAtivar(item.idclinica)" 
                      matTooltip="{{ 'TELAPESQUISACLINICA.ATIVARCADASTRO' | translate }}">
                <mat-icon>check</mat-icon>
              </button>
            </div>
            <button mat-fab class="toggle-button" (click)="openToggle(i)">
              <mat-icon>{{toggle[i] ? 'close' : 'more_vert'}}</mat-icon>
            </button>
          </div>
        </mat-card>
      </div>
  
      <!-- BOTÃO CARREGAR MAIS -->
      <div class="carregar-mais" *ngIf="(DadosTab != undefined && DadosTab.length > 0) && bOcultaCarregaMais == false">
        <button mat-flat-button class="btn-carregar" (click)="CarregarMais()">
          {{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}
        </button>
      </div>
    </mat-card>
  </div>
  
  <!-- MODAL EXCLUIR -->
  <ngx-smart-modal #excluirItem identifier="excluirItem" customClass="modal-container emailmodal">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Confirmação</h3>
      </div>
      <div class="modal-body">
        <p class="modal-text">{{ 'TELAPESQUISACLINICA.EXCLUIRESTECLIENTE' | translate }}</p>
        <p class="modal-subtexto">{{ 'TELAPESQUISACLINICA.ACLINICAFICAINATIVADA' | translate }}</p>
      </div>
      <div class="modal-footer">
        <button mat-flat-button class="btn-cancelar" (click)="excluirItem.close()">
          {{ 'TELAPESQUISACLINICA.NAO' | translate }}
        </button>
        <button mat-flat-button class="btn-confirmar excluir" (click)="InativarUsuario()">
          {{ 'TELAPESQUISACLINICA.SIM' | translate }}
        </button>
      </div>
    </div>
  </ngx-smart-modal>
  
  <!-- MODAL ATIVAR -->
  <ngx-smart-modal #ativarItem identifier="ativarItem" customClass="modal-container emailmodal">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Ativar Clínica</h3>
      </div>
      <div class="modal-body">
        <p class="modal-text">{{ 'TELAPESQUISACLINICA.ATIVARESTACLINICA' | translate }}</p>
        <p class="modal-subtexto">{{ 'TELAPESQUISACLINICA.USUARIOTERAACESSOAOSISTEMA' | translate }}</p>
      </div>
      <div class="modal-footer">
        <button mat-flat-button class="btn-cancelar" (click)="ativarItem.close()">
          {{ 'TELAPESQUISACLINICA.NAO' | translate }}
        </button>
        <button mat-flat-button class="btn-confirmar" (click)="AtivarUsuario()">
          {{ 'TELAPESQUISACLINICA.SIM' | translate }}
        </button>
      </div>
    </div>
  </ngx-smart-modal>