import { <PERSON>ere<PERSON> } from "./endereco";

export class novoLocal{
    nome?        : string;
    telefone?    : string;
    email?       : string;
    site?        : string;
    rua?         : string;
    numero?      : string;
    cep?         : string;
    bairro?      : string;
    observacoes? : string;
    idCidade?    : number;
    idClinica?   : number;
    sigla?       : string;

    idLocal?    : number;
}

export class TabLocal {
    idLocal?: number;
    nome?: string;
    observacaoLocal?: string;
    telefone?: string;
    email?: string;
    site?: string;
    idClinica?: number;
    dtaCadastro?: Date;
}

export class LocalModelView {
	idLocal?: number;
	nome?: string;
	observacaoLocal?: string;
	telefone?: string;
	email?: string;
	site?: string;
	idClinica?: number;
	dtaCadastro?: Date;
	endereco: Endereco = new Endereco();
}