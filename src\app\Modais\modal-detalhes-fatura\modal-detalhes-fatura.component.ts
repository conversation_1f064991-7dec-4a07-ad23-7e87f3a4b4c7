import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { ConsultaModelView, GuiaTissModelview, LoteGuia } from 'src/app/model/consulta';
import { ProcedimentoModelview } from 'src/app/model/procedimento';
import { ConsultaService } from 'src/app/service/consulta.service';
import { ConvenioService } from 'src/app/service/convenio.service';
import { FaturaService } from 'src/app/service/fatura.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { Uteis } from 'src/app/Util/uteis';
import { ValidadoreseMascaras } from 'src/app/Util/validadores';
import { ModalTemplateComponent } from '../modal-template/modal-template.component';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-modal-detalhes-fatura',
    templateUrl: './modal-detalhes-fatura.component.html',
    styleUrls: ['./modal-detalhes-fatura.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      ModalTemplateComponent,
      CommonModule,
      MatCheckboxModule,
      MatIcon
    ]
})
export class ModalDetalhesFaturaComponent implements OnInit {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private faturaService: FaturaService,
    private consultaService: ConsultaService,    
    public dialogRef: MatDialogRef<ModalDetalhesFaturaComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { idFatura: number, desFatura: string, flgFaturaProcedimento: boolean}
  ) { 
    this.idFatura = data.idFatura;
    this.desFatura = data.desFatura;
    this.flgFaturaProcedimento = data.flgFaturaProcedimento;
  }

  ngOnInit() {
    this.loteGuia.listaGuia = []

    this.flgFaturaProcedimento 
    ? this.GetListaProcedimentosFatura()
    : this.GetListaConsultaFatura();
  }

  listaProcedimento: ProcedimentoModelview[] = [];
  listaConsulta: ConsultaModelView[] = [];
  listaConsultaSelecionada: number[] = [];

  idFatura: number;
  desFatura: string;

  flgFaturaProcedimento: boolean;
  loteGuia: LoteGuia = new LoteGuia();


  FecharModal() {
    this.dialogRef.close();
  }

  async GetListaProcedimentosFatura(){
    this.spinner.show();
    await this.faturaService.GetListaProcedimentosFatura(this.idFatura).subscribe((ret) => {
      
      this.listaProcedimento = ret;
      this.spinner.hide();
    }, err => {
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar os procedimentos.',err)
      this.spinner.hide();
    })
  }

  async GetListaConsultaFatura(){
    this.spinner.show();
    await this.consultaService.GetListaConsultaFatura(this.idFatura).subscribe((ret) => {
      this.listaConsulta = ret;
      this.spinner.hide();
    }, err => {
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar as consultas.',err)
      this.spinner.hide();
    })
  }

  AdicionarProcedimento(event: any, idGuiaTiss: number) {
    
    if (event.checked) {
      let guia = new GuiaTissModelview;
      guia.idGuiaTiss = idGuiaTiss;
      guia.loteProcedimento = [];

      this.loteGuia.listaGuia?.push( guia );
    } else {
      this.loteGuia.listaGuia = this.loteGuia.listaGuia?.filter(x => x.idGuiaTiss !== idGuiaTiss);
    }

    
  }

  AdicionarConsulta(event: any, idConsulta: number) {
    if (event.checked) {
      this.listaConsultaSelecionada.push(idConsulta);
    } else {
      this.listaConsultaSelecionada = this.listaConsultaSelecionada.filter(x => x != idConsulta);
    }
  }

  BaixarZipXml() {
    this.spinner.show();
    this.loteGuia.idConvenio = this.listaProcedimento[0].idConvenio;
    
    
    this.consultaService.ConverterLoteGuiaZip(this.loteGuia).subscribe((response) => {
        const base64Zip = response.zipBase64; 
        const hashMD5 = response.hash;

        const byteCharacters = atob(base64Zip);
        const byteNumbers = new Uint8Array(byteCharacters.length);
        
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }

        const blob = new Blob([byteNumbers], { type: 'application/zip' });
        
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;

        a.download = `${hashMD5}.zip`; 
        
        document.body.appendChild(a);
        a.click();
        
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        this.spinner.hide();    
        this.dialogRef.close();           
    }, error => {
        console.error("Erro ao tentar baixar archivo.", error);
        this.spinner.hide();               
    });               
  } 

  async BaixarRelatorioListaConsulta(){
    
    if (this.listaConsultaSelecionada.length == 0){
      this.snackBarAlert.sucessoSnackbar('Por favor, selecione ao menos uma consulta.');
      return;
    };

    this.spinner.show();

    await this.consultaService.BaixarRelatorioListaConsulta(this.listaConsultaSelecionada).subscribe((ret) => {

      Uteis.BaixarBase64EmPDF(ret, `Relatorio ${this.desFatura}  ${(new Date().toISOString().split('T')[0])}`);

      this.spinner.hide();
    }, err => {
      this.snackBarAlert.falhaSnackbar('Ocurreu um erro tentando baixar o arquivo.',err)
      this.spinner.hide();
    });
  }

}
