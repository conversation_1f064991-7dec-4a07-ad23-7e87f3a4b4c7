import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { PermissoesService } from '../service/permissoes.service';
import { Perfil, PerfilPermissao } from '../model/perfil';
import { SelectionModel } from '@angular/cdk/collections';
import { MatTable, MatTableDataSource as MatTableDataSource, MatTableModule } from '@angular/material/table'
import { FormsModule,ReactiveFormsModule } from '@angular/forms';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';


@Component({
    selector: 'app-permissoes',
    templateUrl: './permissoes.component.html',
    styleUrls: ['./permissoes.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      MatTable,
      MatTableModule
    ]
})


export class PermissoesComponent implements OnInit {


  constructor(
    private spinner: SpinnerService,
    private permissoesService: PermissoesService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
  ) { }

  // usuario: Usuario;
  displayedColumns: string[] = ['DesCategoria', 'Permissao_Categoria', 'flgVisualizar', 'flgIncluir', 'flgEditar', 'flgInativar'];


  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';



  selection = new SelectionModel<PerfilPermissao>(true, []);
  inicio: number = 0;
  fim: number = 10;
  perfilEdicao:any;
  nomePerfil:any;
  dict = [];
  idPerfil: any = 0
  dadosPermissao: PerfilPermissao[] = []
  pesquisa: string = ''
  // dadosPermissao: Element = [];
  // dadosPermissaoEdicao: any = [];

  dataSource = new MatTableDataSource<PerfilPermissao>(this.dadosPermissao);
  ngOnInit() {
    this.idPerfil = this.localStorageService.idPerfil;
    this.localStorageService.clearByName("idPerfil");
    this.carregarDadosPermissoes()

  }

  carregarDadosPermissoes() {
    this.permissoesService.GetPermissoes(this.inicio, this.fim, this.usuarioLogadoService.getIdUltimaClinica(), this.usuarioLogadoService.getIdUsuarioAcesso(), this.pesquisa).subscribe((retorno) => {
      
      
      retorno.forEach((element:any) => {
        this.dadosPermissao.push({
          idPerfilPermissao: 0, idPermissao: element.permissao.idPermissao, DesCategoria: element.categoria, Permissao_Categoria: element.permissao.nomePermissao,
          flgVisualizar: false, flgIncluir: false, flgInativar: false, flgEditar: false
        })

      });
      this.dataSource.data = this.dadosPermissao;
      if (this.idPerfil)
        this.EditarPermissoes(this.idPerfil);
      

      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  public LimparTabela() {
    this.dataSource.data.forEach(element => {
      element.flgEditar = false;
      element.flgInativar = false;
      element.flgIncluir = false;
      element.flgVisualizar = false;
    });

    this.nomePerfil = ""
    this.campoNomeValido = true
    this.perfilEdicao = null;
  }

  btnSalvar() {
    this.ValidaNomePerfil()

    if (this.nomePerfil == "" || this.campoNomeValido == false) {
      this.snackBarAlert.falhaSnackbar("Informe o nome do Perfil")
      return
    }
    var existe = this.dataSource.data.filter(c => c.flgEditar == true || c.flgInativar == true || c.flgIncluir == true || c.flgVisualizar)
    if (existe.length == 0) {
      this.snackBarAlert.falhaSnackbar("Selecione alguma permissão")
      return
    }

    var perfil = new Perfil;
    perfil.NomePerfil = this.nomePerfil
    perfil.DictValoresPermissao = this.dataSource.data
    perfil.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()
    perfil.IdClinica = this.usuarioLogadoService.getIdUltimaClinica()
    if (this.perfilEdicao) {
      perfil.IdPerfil = this.perfilEdicao.idPerfil;
    }
    
    this.permissoesService.SalvarPerfil(perfil).subscribe((retorno) => {
      if (retorno == true) {
        this.LimparTabela()
        this.snackBarAlert.sucessoSnackbar( "Perfil de Permissões Salvo")
      }
      else {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar Perfil de Permissão")
      }

      
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })


  }

  EditarPermissoes(idPerfil:any) {
    this.permissoesService.GetPerfilEdicao(idPerfil).subscribe((retorno) => {
      
      
      this.perfilEdicao = retorno
      this.nomePerfil = this.perfilEdicao.nomePerfil
      this.permissoesService.GetPerfilPermissaoPorId(idPerfil).subscribe((retorno) => {
        if (retorno.length > 0) {
          retorno.forEach(((element:any) => {
            this.dataSource.data.forEach(elemento => {
              if (element.idPermissao == elemento.idPermissao) {
                elemento.idPerfilPermissao = element.idPerfilPermissao;
                elemento.flgEditar = element.flgEditar;
                elemento.flgInativar = element.flgExcluir;
                elemento.flgIncluir = element.flgInserir;
                elemento.flgVisualizar = element.flgVisualizar;
              }
            });

          }));
        }
      })
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    });
  }

  // SnackMensagem(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  campoNomeValido: boolean = true
  ValidaNomePerfil() {
    if (this.nomePerfil == null || this.nomePerfil == undefined || !this.nomePerfil.trim()) {

      this.campoNomeValido = false
    }
    else
      this.campoNomeValido = true
  }

}
