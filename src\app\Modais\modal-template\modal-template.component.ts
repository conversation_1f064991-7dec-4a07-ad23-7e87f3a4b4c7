
import { CommonModule } from '@angular/common';
import { Component, EventEmitter, HostListener, Input, Output } from '@angular/core';


@Component({
    selector: 'app-modal-template',
    templateUrl: './modal-template.component.html',
    styleUrls: ['./modal-template.component.scss'],
      standalone: true,
    imports: [
      CommonModule
    ]
})
export class ModalTemplateComponent {

  @Input('conteudo') conteudo: any;
  @Input('cabecalho') cabecalho?: string = '';

  @Output('fechar') fechar: EventEmitter<void> = new EventEmitter();

  @HostListener('window:keydown.escape', ['$event'])
  CapturarEsc(){
    this.fechar.emit()
  }


}
