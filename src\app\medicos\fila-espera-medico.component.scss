/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$success-color: #16a34a;
$warning-color: #f59e0b;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

.container {
  padding: 16px;
  max-height: 75vh;
  overflow: auto;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  min-width: 0;
  overflow-x: hidden;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  width: 100%;
  max-width: 100%;
  min-width: 0;
}

/* CABEÇALHO MELHORADO */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: $border-radius;
  color: white;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  flex-wrap: wrap;
  gap: 16px;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
  z-index: 1;
  min-width: 0;
  max-width: 100%;
}

.header-icon {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  mat-icon{
    font-size: 30px;
    width: 30px;
    height: 30px;
  }
}

.header-icon-material {
  color: white;
  font-size: 28px;
}

.header-content {
  flex: 1;
  min-width: 0;
  max-width: 100%;
}

.header-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-subtitle {
  font-size: 0.95rem;
  margin: 0;
  font-weight: 400;
  opacity: 0.9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1;
  flex-shrink: 0;
  min-width: 0;
  flex-wrap: wrap;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-size: 0.85rem;
  font-weight: 500;
  transition: all $transition;

  &.connected {
    background-color: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.3);
    color: #10b981;

    mat-icon {
      color: #10b981;
    }
  }

  &.disconnected {
    background-color: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
    color: #ef4444;

    mat-icon {
      color: #ef4444;
    }
  }

  mat-icon {
    font-size: 18px;
  }

  .status-text {
    font-size: 0.8rem;
  }
}

.header-actions button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  transition: all $transition;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-actions button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.header-actions button.active {
  background-color: rgba(255, 255, 255, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
  }
}

/* CONTROLES E ESTATÍSTICAS */
.controles-section {
  margin-bottom: 24px;
}

.estatisticas-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.estatistica-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all $transition;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  min-width: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    transition: all $transition;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  }

  &.primary {
    &::before {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    .estatistica-icon {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    }
    
    .estatistica-numero {
      color: #3b82f6;
    }
  }

  &.secondary {
    &::before {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .estatistica-icon {
      background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }
    
    .estatistica-numero {
      color: #10b981;
    }
  }

  &.tertiary {
    &::before {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    .estatistica-icon {
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }
    
    .estatistica-numero {
      color: #f59e0b;
    }
  }
}

.estatistica-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  min-width: 48px;
  max-width: 48px;

  mat-icon {
    color: white;
    font-size: 24px;
    width: 24px;
    height: 24px;
    line-height: 24px;
  }
}

.estatistica-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  max-width: 100%;
}

.estatistica-numero {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.estatistica-label {
  font-size: 0.875rem;
  color: $text-secondary;
  font-weight: 500;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* ESTILOS PARA STATUS DA CONEXÃO SIGNALR */
.estatistica-item mat-icon.connected {
  color: $success-color !important;
  animation: pulse-success 2s infinite;
}

.estatistica-item mat-icon.disconnected {
  color: $error-color !important;
  animation: pulse-error 2s infinite;
}

.estatistica-numero.connected {
  color: $success-color !important;
}

.estatistica-numero.disconnected {
  color: $error-color !important;
}

/* ANIMAÇÕES PARA STATUS DA CONEXÃO */
@keyframes pulse-success {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes pulse-error {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.acoes-principais {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.btn-chamar-proximo {
  background-color: #10b981;
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all $transition;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.btn-chamar-proximo:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.btn-atualizar {
  border-color: #3b82f6;
  color: #3b82f6;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-atualizar:hover:not(:disabled) {
  background-color: rgba(59, 130, 246, 0.1);
}

.btn-limpar {
  border-color: #ef4444;
  color: #ef4444;
  padding: 12px 20px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-limpar:hover:not(:disabled) {
  background-color: rgba(239, 68, 68, 0.1);
}

/* LOADING */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 24px;
}

.loading-text {
  margin-top: 16px;
  color: $text-secondary;
  font-size: 1rem;
}

/* CONTAINER DA FILA */
.fila-container {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  min-width: 0;
}

.fila-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  min-width: 0;
  flex-wrap: wrap;
  gap: 16px;
}

.fila-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  pointer-events: none;
}

.fila-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1;
  flex: 1;
  min-width: 0;
  max-width: 100%;
}

.fila-titulo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.fila-titulo mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
}

.fila-badge {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge-count {
  font-size: 0.875rem;
  font-weight: 700;
  color: white;
}

.fila-info {
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 1;
}

.total-registros {
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* SCROLL DA LISTA */
.pacientes-scroll {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px;
  background-color: $bg-color;
}

.pacientes-scroll::-webkit-scrollbar {
  width: 8px;
}

.pacientes-scroll::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.pacientes-scroll::-webkit-scrollbar-thumb {
  background-color: #3b82f6;
  border-radius: 10px;
}

/* CARDS DOS PACIENTES MELHORADOS */
.paciente-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all $transition;
  border-left: 4px solid #3b82f6;
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  min-width: 0;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.03) 100%);
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
  }

  &.primeiro-da-fila {
    border-left: 4px solid #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 1) 100%);
    
    &::after {
      background: linear-gradient(90deg, transparent 0%, rgba(16, 185, 129, 0.05) 100%);
    }

    &::before {
      content: '⭐';
      position: absolute;
      top: 12px;
      right: 12px;
      font-size: 18px;
      z-index: 2;
    }
  }

  &.paciente-prioritario {
    border-left-width: 5px;
    
    &:not(.primeiro-da-fila) {
      border-left-color: #f59e0b;
      
      &::after {
        background: linear-gradient(90deg, transparent 0%, rgba(245, 158, 11, 0.03) 100%);
      }
    }
  }
}

/* POSIÇÃO NA FILA MELHORADA */
.posicao-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 70px;
}

.posicao-numero {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 1.3rem;
  transition: all $transition;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  border: 3px solid rgba(255, 255, 255, 0.9);

  &.destaque {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    transform: scale(1.15);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    animation: pulse-next 2s infinite;
  }

  &.prioritario {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
  }
}

@keyframes pulse-next {
  0%, 100% {
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.6);
  }
}

.posicao-label {
  font-size: 0.75rem;
  color: $text-secondary;
  margin-top: 6px;
  text-align: center;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* INFORMAÇÕES DO PACIENTE MELHORADAS */
.paciente-info {
  flex: 1;
  min-width: 0;
}

.paciente-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.paciente-nome {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: 700;
  color: $text-primary;
  flex: 1;
  min-width: 0;
}

.paciente-nome mat-icon {
  color: #3b82f6;
  font-size: 22px;
  width: 22px;
  height: 22px;
  flex-shrink: 0;
}

.nome-texto {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.paciente-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.proximo {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    animation: pulse-badge 2s infinite;
  }
}

@keyframes pulse-badge {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.paciente-detalhes {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.detalhe-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: $text-secondary;
  min-width: 0;
  padding: 4px 0;

  &.tempo-espera {
    color: $text-primary;
    font-weight: 500;
  }

  mat-icon {
    color: #3b82f6;
    font-size: 18px;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
  }

  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* AÇÕES DO PACIENTE MELHORADAS */
.paciente-acoes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
  align-items: center;
}

.btn-convidar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  width: 56px;
  height: 56px;
  transition: all $transition;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.9);

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  }

  &.loading {
    animation: loading-pulse 1.5s infinite;
  }

  mat-icon {
    font-size: 24px;
    width: 24px;
    height: 24px;
  }
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.acao-label {
  font-size: 0.75rem;
  color: $text-secondary;
  font-weight: 600;
  text-align: center;
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-remover {
  background-color: #ef4444;
  color: white;
  width: 44px;
  height: 44px;
  transition: all $transition;
}

.btn-remover:hover {
  background-color: #dc2626;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* FILA VAZIA MELHORADA */
.fila-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 32px;
  text-align: center;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(255, 255, 255, 1) 100%);
  margin: 16px;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.fila-vazia::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="empty-dots" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="rgba(59,130,246,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23empty-dots)"/></svg>');
  pointer-events: none;
}

.vazia-content {
  position: relative;
  z-index: 1;
}

.vazia-icon {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  border: 3px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
}

.vazia-icon mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #3b82f6;
  opacity: 0.7;
}

.fila-vazia h3 {
  color: $text-primary;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0 0 12px 0;
  letter-spacing: -0.5px;
}

.fila-vazia p {
  color: $text-secondary;
  font-size: 1.1rem;
  margin: 0 0 32px 0;
  max-width: 450px;
  line-height: 1.6;
  font-weight: 400;
}

.vazia-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.btn-atualizar-vazio {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 14px 28px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  transition: all $transition;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.9);

  &:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
  }

  mat-icon {
    margin-right: 8px;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

/* RESPONSIVIDADE MELHORADA */
@media (max-width: 1024px) {
  .container {
    padding: 12px;
    max-width: 100%;
  }

  .estatisticas-container {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
  }

  .header-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 8px;
    max-width: 100vw;
  }

  .card-principal {
    padding: 16px;
    margin: 0;
  }

  .header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 16px;
    margin-bottom: 24px;
  }

  .header-left {
    flex-direction: column;
    align-items: center;
    gap: 16px;
    width: 100%;
  }

  .header-content {
    width: 100%;
    text-align: center;
  }

  .header-title {
    font-size: 1.25rem;
    white-space: normal;
  }

  .header-subtitle {
    white-space: normal;
  }

  .header-icon {
    margin-right: 0;
  }

  .header-actions {
    flex-direction: row;
    gap: 12px;
    justify-content: center;
    width: 100%;
  }

  .status-indicator {
    order: -1;
    font-size: 0.8rem;
  }

  .estatisticas-container {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .estatistica-item {
    padding: 16px;
    gap: 12px;
  }

  .acoes-principais {
    flex-direction: column;
    gap: 8px;
  }

  .fila-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
    padding: 16px;
  }

  .fila-header-left {
    justify-content: center;
    width: 100%;
  }

  .paciente-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 16px;
  }

  .posicao-container {
    min-width: auto;
  }

  .paciente-header {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    width: 100%;
  }

  .paciente-detalhes {
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .paciente-acoes {
    flex-direction: row;
    justify-content: center;
    min-width: auto;
  }

  .fila-vazia {
    padding: 60px 24px;
  }

  .vazia-icon {
    width: 80px;
    height: 80px;
  }

  .vazia-icon mat-icon {
    font-size: 36px;
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 4px;
    max-width: 100vw;
    overflow-x: hidden;
  }

  .card-principal {
    padding: 12px;
    border-radius: 8px;
  }

  .header {
    padding: 12px;
    border-radius: 8px;
  }

  .header-title {
    font-size: 1.1rem;
    line-height: 1.3;
  }

  .header-subtitle {
    font-size: 0.8rem;
  }

  .estatistica-item {
    padding: 12px;
    gap: 10px;
  }

  .estatistica-numero {
    font-size: 1.2rem;
  }

  .estatistica-icon {
    width: 36px;
    height: 36px;
    min-width: 36px;
    max-width: 36px;
  }

  .estatistica-icon mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
  }

  .pacientes-scroll {
    max-height: 50vh;
    padding: 8px;
  }

  .paciente-card {
    padding: 12px;
    margin-bottom: 8px;
    gap: 16px;
  }

  .posicao-numero {
    width: 44px;
    height: 44px;
    font-size: 1rem;
  }

  .paciente-nome {
    font-size: 0.95rem;
  }

  .detalhe-item {
    font-size: 0.8rem;
  }

  .btn-convidar {
    width: 44px;
    height: 44px;
  }

  .btn-convidar mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    padding: 5px;
  }

  .fila-vazia {
    padding: 32px 12px;
  }

  .fila-vazia h3 {
    font-size: 1.3rem;
  }

  .fila-vazia p {
    font-size: 0.9rem;
  }

  .btn-atualizar-vazio {
    padding: 10px 16px;
    font-size: 0.85rem;
  }
}

@media (max-width: 360px) {
  .container {
    padding: 2px;
  }

  .card-principal {
    padding: 8px;
  }

  .header {
    padding: 8px;
  }

  .header-title {
    font-size: 1rem;
  }

  .estatistica-item {
    padding: 8px;
  }

  .paciente-card {
    padding: 8px;
  }
}