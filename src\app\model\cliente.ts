import { UsuarioClinica } from './usuario';
import { Contato } from './contato';
import { Endereco } from './endereco';
import { Clinica } from './clinica';
import { Pessoa } from './pessoa';

export class Cliente {
    idCliente?: number | number;
    idUsuarioAcesso?: number;
    planoSaude?: string;
    idConvenio?: number;
    matricula?: string;
    procedencia?: string;
    flgInativo?: boolean;
    dtaCadastro?: Date;
    idUsuarioGerador?: number;
    observacaoInativacao?: string;
    codAcesso? :string
    idClinica?:number;
    pessoa?:Pessoa;
    flgBoasVindas?:boolean;
    // endereco: Endereco;
    // contato: Contato;
    // usuario: Usuario;
    DadosCorporal?: DadosMedicosUsuario;
    clinicas?: Clinica[];
}



export class DadosMedicosUsuario {
    IdDadosMedicosUsuario?: number;
    Peso?: string;
    Altura?: string;
    IMC?: string;
    Pressao?: string;
    Batimento?: string;
    Temperatura?: string;
    IdUsuario?: number;
    IdConsulta?: number;
    DtaCadastro?: Date;
    FlgInativo?: boolean
    IdUsuarioGerador?: number;

    contato?: Contato;
    endereco?: Endereco;


    imagem64?: string;
    removerFoto?: boolean;
    usuarioClinica?: UsuarioClinica[];
    clinicas?: Clinica[];
}

export class PacienteModelview {
	idCliente?: number;
	idPessoa?: number;
	idUsuarioAcceso?: number;
	planoSaude?: string;
	idConvenio?: number;
	matricula?: string;
	procedencia?: string;
	flgInativo?: boolean;
	dtaCadastro?: Date;
	idUsuarioGerador?: number;
	observacaoInativacao?: string;
	nome?: string;
	imagem64?: string;
	tipoUsuario?: string;
	email?: string;
	tel?: string;
    telMovel?: string;
    telComercial?: string;
	dtaNascimento?: Date;
	sexo?: string;
    cpf?: string;
    Spec?:string;
    idUsuarioacesso?: number;
    flgEmail? : boolean;

};
