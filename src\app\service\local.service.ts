
import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from '@angular/common/http';
import { LocalModelView, TabLocal } from "../model/local";
import { msgResposta } from "../model/retorno-resposta";
import { environment } from "src/environments/environment";
import { catchError, throwError } from "rxjs";
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class LocalService{
    constructor(
        private http: HttpClient,
        private spinner : SpinnerService
        
    ) { }

    public SalvarLocal(local: LocalModelView) {
        return this.http.post(environment.apiEndpoint + '/local/SalvarLocal', local)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }
    
    public GetLocais(){
        this.spinner.show();
        return this.http.get<TabLocal[]>(environment.apiEndpoint + '/local/GetLocais')
    }
    
    public deleteLocal(id: number){
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/local/DeleteLocal', id)
    }
    
    public GetDadosLocal(idLocal: number) {
        let params = new HttpParams();
        params = params.append('idLocal', idLocal);
        return this.http.get<LocalModelView>(environment.apiEndpoint + '/local/GetDadosLocal', {params})
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

}