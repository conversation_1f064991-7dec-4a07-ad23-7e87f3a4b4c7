<app-modal-cabecalho>
    <main>
        <section id="ListaMensagens">
            <header>
                <h3>Mensagens</h3>
            </header>
            <div *ngFor="let item of listaMensagem">
                <div class="message-card" (click)="SelecionaMensagem(item.mensagem)">
                    <span>
                        {{item.tituloMensagem}}
                    </span>
                </div>
            </div>
        </section>

        <section id="ConteudoMensagem">
            <header>
                <h3>Mensagem</h3>
            </header>
            <textarea placeholder="Escreva sua mensagem aqui." rows="18" 
            [(ngModel)]="objMensagemListaPaciente.mensagem">
            </textarea>
        </section>

        <section id="ListaPacientes">
            <header>
                <h3>Pacientes</h3>
                <div class="search-container">
                    <input type="text" placeholder="Buscar Paciente" (keyup.enter)="GetListaPaciente()" 
                    [(ngModel)]="filtroPaciente">
                    <button class="search-button" (click)="GetListaPaciente()">
                        <mat-icon>search</mat-icon>
                    </button>
                </div>
            </header>
            <div class="patients-container">
                <div *ngIf="listaPaciente.length < 1" class="empty-state">Nenhum paciente encontrado.</div>
                <ul>
                    <li class="patient-card" *ngFor="let item of listaPaciente" (click)="AdicionarPaciente(item.idCliente!)">
                        <div class="checkbox-container">
                            <input type="checkbox" [checked]="ValidaPacienteSelecionado(item.idCliente!)"/>
                        </div>
                        <div class="patient-data">
                            <span class="patient-name">{{item.nome}}</span>
                            <span class="patient-phone">{{item.telMovel}}</span>
                        </div>
                    </li>
                    <button class="load-more-button" (click)="GetListaPaciente(true)"
                    *ngIf="(listaPaciente != undefined &&  listaPaciente.length > 0) && flgBtnCarregarMais == false">
                        Carregar Mais
                    </button>
                </ul>
            </div>
        </section>
        
        <div id="PainelAcoes">
            <button class="action-button cancel-button" (click)="FecharModal()">Cancelar</button>
            <button class="action-button send-button" (click)="EnviarMensagem()">Enviar</button>
        </div>
    </main>
</app-modal-cabecalho>