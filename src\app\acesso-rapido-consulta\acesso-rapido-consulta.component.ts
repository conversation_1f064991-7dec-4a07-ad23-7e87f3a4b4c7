import { Component, OnInit } from '@angular/core';
import { ConsultaService } from '../service/consulta.service';
import { irParaConsulta } from '../model/consulta';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { AcessoRapidoConsultaService } from '../service/acesso-rapido-consulta.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { AcessoRapidoConsulta } from '../model/acesso-rapido-consulta';
import { AuthService } from '../login/auth.service';
import { ValidacaoService } from '../service/validacao.service';
import { UsuarioService } from '../service/usuario.service';
import { PagamentoService } from '../service/pagamento.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { PagamentoComponent } from '../pagamento/pagamento.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDivider } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-acesso-rapido-consulta',
  templateUrl: './acesso-rapido-consulta.component.html',
  styleUrls: ['./acesso-rapido-consulta.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    PagamentoComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDivider,
    TranslateModule,
    NgxSmartModalModule,
    MatIcon,
    RouterModule,
    MatCardModule
  ]
})
export class AcessoRapidoConsultaComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,

    private consultaService: ConsultaService,
    private router: Router,
    private route: ActivatedRoute,
    private acessoRapido: AcessoRapidoConsultaService,
    public ngxSmartModalService: NgxSmartModalService,
    private authService: AuthService,
    public validador: ValidacaoService,
    private usuarioService: UsuarioService,
    private pagamentoService: PagamentoService,
    private localStorageService: LocalStorageService,
    private snackBarAlert: AlertComponent,
  ) {
    this.pagamentoService.changeconsulta$
      .subscribe((perf: any) => {
        if (perf != null) {
          const consulta = new irParaConsulta();
          consulta.idPagamento = perf.idPagamento;
          consulta.idConsulta = perf.idConsulta;
          consulta.flgSomenteProntuario = false;
          this.CarregaConsulta();
        }
      });

  }

  codAcesso?: string;
  logininvalido?: boolean;
  DivInicial = true;
  idGuid = '';
  mensagemModal = '';
  DivEspera = false;
  flgIniciarConsulta?: boolean;
  ImagemPessoaConsulta = 'assets/build/img/avatar-medico.png';
  flgAntes?: boolean;
  seg?: number;
  min?: number;
  hr?: number;
  segundos: any = '00';
  minutos: any = '00';
  hora: any = '00';
  usuarioConsulta: any = [];
  timeInterval: any;
  ModoLogin = 'Cpf';

  ngOnInit() {

    this.localStorageService.clear();

    this.idGuid = this.route.snapshot.params.idGuid;

    if (!this.idGuid) {
      this.navigateLogin();
      return;
    }

    this.getCodigoAcesso();
    // this.CarregaConsulta()
  }
  iniciarConsulta(iniciarconsulta: any) {


    var consulta = new irParaConsulta();
    consulta.idConsulta = iniciarconsulta;
    consulta.flgSomenteProntuario = false;
    this.localStorageService.Consulta = consulta;
    // sessionStorage.setItem("TelaStream", "true");
    this.realizarLogin();
  }
  CarregaConsulta() {
    if (this.codAcesso) {
      this.consultaService.GetConsultaGuid(this.idGuid, this.codAcesso).subscribe((retorno) => {
        if (retorno == null) {
          this.snackBarAlert.falhaSnackbar('Cod.Acesso incorreto!')
          return;
        }
        if (retorno.flgExigePagamento == true && retorno.idPagamento == null) {

          this.pagamentoService.setIdPagador(retorno.idPessoaPaciente);
          this.pagamentoService.setIdRecebedora(retorno.idMedicoPaciente);
          this.pagamentoService.setValor(retorno.valorConsulta);
          this.pagamentoService.setIdconsulta(retorno.idConsulta);
          this.pagamentoService.setAbrirModal(true);
          this.ngxSmartModalService.getModal('ModalPagamento').open();
        } else {
          this.envioCod();
          this.usuarioConsulta = retorno;
          const minutos = 10;
          const teste = new Date(this.usuarioConsulta.dtaAgenda)
          teste.setMinutes(teste.getMinutes() - minutos);
          const data = new Date()
          data.setMinutes(data.getMinutes() - 20);
          if (teste <= new Date() && teste >= data) {
            this.flgIniciarConsulta = true;
            this.flgAntes = true;
          } else if (teste >= new Date()) {

            this.flgIniciarConsulta = false;
            this.flgAntes = true;
          } else {
            this.flgIniciarConsulta = false;
            this.flgAntes = false;

          }


          if (this.timeInterval) {
            clearInterval(this.timeInterval);
            this.timeInterval = null;
          }

          // procura o adiciona o som

          this.timeInterval = setInterval(() => {
            var teste = new Date(this.usuarioConsulta.dtaAgenda)
            teste.setMinutes(teste.getMinutes() - minutos);
            var data = new Date();
            data.setMinutes(data.getMinutes() - minutos);
            if (teste <= new Date() && teste >= data) {
              this.flgIniciarConsulta = true;
            }
          }, 60000);
        }

      });
    }
  }

  TempoCons() {
    var zero = 0
    this.seg
    this.min
    this.hr

    if (parseInt(this.segundos) > 0) {

      this.segundos = parseInt(this.segundos) - 1;
      this.segundos = zero + this.segundos;

    } else if (this.segundos == 0) {

      if (this.minutos > 0) {
        this.segundos = 59
        this.minutos = this.minutos - 1
        if (this.minutos < 10)
          this.minutos = parseInt('0') + this.minutos;

      } else if (this.minutos == 0) {
        if (this.hora > 0) {
          this.segundos = 59;
          this.minutos = 59;
          this.hora = this.hora - 1;
          if (this.hora < 10)
            this.hora = parseInt('0') + this.minutos;

        }
      }
    }
  }

  envioCod() {

    this.DivInicial = false;
    this.DivEspera = true;


  }


  validarCampo() {
    this.codAcesso = this.codAcesso!.trim();
    if (this.codAcesso && this.codAcesso.length > 3)
      return true;

    this.logininvalido = true;
    return false;
  }

  abrirModalErro(mensagem: string) {
    this.mensagemModal = mensagem;
    this.ngxSmartModalService.getModal('modalErroConsultaRapida').open();
  }

  fecharModalErro() {
    this.ngxSmartModalService.getModal('modalErroConsultaRapida').close();

    if (this.mensagemModal == mensagemErro.erroRequisicao || this.mensagemModal == mensagemErro.guiInvalido)
      this.snackBarAlert.falhaSnackbar("Ops... Parece que algo deu errado. Tente novamente.")
    this.router.navigate(['login']);
  }

  validarRetorno(retAcessoConsulta: AcessoRapidoConsulta) {

    if (!retAcessoConsulta) {
      this.abrirModalErro(mensagemErro.usuarioNaoEncontrado);
      this.snackBarAlert.falhaSnackbar('Ops... Parece que você não está em nosso sistema. Solicite outro link ou tente novamente.');
      return false;
    }

    if (retAcessoConsulta.codigoAcesso !== this.codAcesso) {
      this.abrirModalErro(mensagemErro.codigoAcessoInvalido);
      this.snackBarAlert.falhaSnackbar('Ops... Seu código de acesso não consta em nosso sistema. Solicite outro código ou tente novamente.');
      return false;
    }

    if (retAcessoConsulta.flgRealizada) {
      this.abrirModalErro(mensagemErro.consultaRealizada);
      this.snackBarAlert.falhaSnackbar('Ops... Parece que você já realizou a consulta.')
      return false;
    }

    return true;
  }

  getCodigoAcesso() {

    this.acessoRapido
      .getCodigoAcesso(this.idGuid)
      .subscribe(ret => {
        if (!ret) {
          this.snackBarAlert.falhaSnackbar('Acesso negado')
          this.navigateLogin();
          this.spinner.hide();

        }
        if (ret.flgRealizada == true) {
          this.snackBarAlert.falhaSnackbar('Consulta ja finalizada')
          this.navigateLogin();
          this.spinner.hide();

        }

      }, err => {
        console.error(err.message);
        this.navigateLogin();
        this.spinner.hide();
      });
  }

  navigateLogin() {
    this.router.navigate(['login']);
  }

  async realizarLogin() {

    const fncError = () => {
      this.logininvalido = true;
    };

    var sGuid = "";
    this.route.queryParams.subscribe(params => {
      sGuid = params['guid'];
    });
    const data: any = await this.authService.AutenticaUsuario(this.usuarioConsulta.cpfUsuario, this.usuarioConsulta.codAcesso, sGuid, this.ModoLogin);
    if (data.access_token != null) {

      this.localStorageService.token = data.access_token;
      const dataUsuario: any = await this.usuarioService.getPerfilUsuarioLogado().toPromise();
      if (dataUsuario == false)
        return fncError();

      this.usuarioService.AtualizaDadosUsuarioLogado(dataUsuario);
      this.localStorageService.Logado = true;
      this.localStorageService.Linguagem = 'pt'
      this.localStorageService.TelaPrivacidadeLogin = this.idGuid
      // Redirecionar para streaming simplificado do paciente
      this.router.navigate(['/streaming-paciente']);

      this.spinner.hide();
    }
    else {
      this.spinner.hide();
      return fncError();
    }
  }
}


const mensagemErro = {
  guiInvalido: "Ops... Parece que seu link não é válido. Solicite outro.",
  consultaRealizada: "Ops... Parece que você já realizou a consulta.",
  codigoAcessoInvalido: "Ops... Seu código de acesso não consta em nosso sistema. Solicite outro código ou tente novamente.",
  erroRequisicao: "Ops... Parece que algo deu errado. Tente novamente.",
  usuarioNaoEncontrado: "Ops... Parece que você não está em nosso sistema. Solicite outro link ou tente novamente."
}



