import { Time } from "@angular/common";
import { ProcedimentoModelview } from "./procedimento";
import { Cliente } from "./cliente";
import { Medico } from "./medico";
import { Convenio } from "./convenio";
import { Pessoa } from "./pessoa";

export class Consulta {
  IdConsulta?: number;
  DtaConsulta?: Date;
  DtaAgendamento?: Date;
  TempoConsulta?: Time;
  AnexoVideo?: string;
  ObservacaoPaciente?: string;
  AnotacaoAnonima?: any;
  string?: any;
  LocalizacaoPaciente?: string;
  FlgInativo?: boolean;
  DtaCadastro?: Date;
  DtaCancelamento?: Date;
  MotivoCa?: any;
  idUsuarioAcesso?: number;
  ncelamento?: string;
  idPessoaCancelamento?: Number;
  Pagamento?: string;
  CodigoConvenio?: string;
  ValorConsulta?: string;
  idConvenio?: number;
  IdPagamento?: number;
  flgExigePagamento?: boolean;
  IdPaciente?: number;
  IdMedico?: Number;
  IdUsuarioGerador?: Number;
  IdEspecialidade?: Number;
  idTipoAgendamento?: number;
  idClinica?: number;
  flgProntuario?: boolean;
  flgexigePagamento?: boolean;
  FlgProntuario?: boolean;
  FlgRetorno?: boolean;
  guidAcesso?: string;
  DesAnotacao?: string;
  idFatura?: number;
  FlgPeriodoOff?: boolean;
  DtaFinalizacaoAtendimento?: Date;
}
export class ObjGrafico {
  idClinica?: number;
  Mes?: Date;
}
export class DadosColunaDireita {
  nomeMedico?: string;
  dtaConsulta?: Date;
}

export class retornoFila {
  posicao?: number;
  idConsulta?: number;
}

export class ParticipantesConsulta {
  IdParticipantesConsulta?: number;
  IdParticipante?: Number;
  IdConsulta?: number;
  DtaCadastro?: Date;
  FlgInativo?: boolean;
}

export class ChatConsulta {
  idChatConsulta?: number;
  DesChat?: string;
  idConsulta?: number;
  DtaCadastro?: Date;
  FlgInativo?: boolean;
  idUsuarioAcesso?: number;
  vo?: boolean;
  idUsuarioGerador?: number;
}

export class QuesitoAvaliacao {
  IdQuesitoAvaliacao?: number;
  IdQuesito?: number;
  IdAvaliacao?: number;
  DtaCadastro?: Date;
  valorAvaliacao?: string;
  FlgInativo?: boolean;
}

export class AnexosArquivo {
  IdAnexosArquivo?: number;
  DesArquivo?: string;
  IdConsulta?: number;
  DtaCadas?: any;
  idUsuarioAcesso?: number;
  tro?: Date;
  idPessoaGerador?: number;
  FlgInativo?: boolean;
}

export class irParaConsulta {
  idConsulta?: number;
  flgSomenteProntuario?: boolean;
  idPagamento?: number;
}

export class objPesquisaconsulta {
  inicio?: number;
  fim?: number;
  pesquisaPaciente?: string;
  pesquisaMedico?: string;
  status?: string;
  DtaConsulta?: Date | null;
}

export class DadosConsultaHojeModelView {
  idConsulta?: number
  nomePaciente?: string
  dtaConsulta?: Date
  idMedico?: number
  especialidade?: string
}

export class ConsultaAvaliacaoModelView {
  idAvaliacao?: number;
  valor?: number | null;
  quesito?: string;
  obs?: string;
}

export class LoteConsultas {
  idConvenio?: number;
  listaConsultas?: DadosLoteConsulta[];
}
export class DadosLoteConsulta {
  idConsulta?: number;
  idMedico?: number;
  idPaciente?: number;
}

export class LoteProcedimentos {
  listaProcedimentos: number[] = [];
}

export class GuiaTissModelview {
  idGuiaTiss?: number;
  numGuiaPrestador?: string;
  numGuiaPrincipal?: string;
  numGuiaOperadora?: string;
  numSenha?: string;
  idFatura?: number;
  idClinica?: number;
  desClinica?: string;
  idMedico?: number;
  nomeMedico?: string;
  idTipoAtendimento?: number;
  desTipoAtendimento?: string;
  idIndicacaoAcidente?: number;
  desIndicacaoAcidente?: string;
  idTipoConsulta?: number;
  desTipoConsulta?: string;
  idRegimeAtendimento?: number;
  desRegintemAtendimento?: string;
  idSaudeOcupacional?: number;
  desSaudeOcupacional?: string;
  idCaraterAtendimento?: number;
  desCaraterAtendimento?: string;
  dtaAutorizacao?: Date;
  dtaValidadeSenha?: Date;
  flgRn?: boolean;
  flgInativo?: boolean;
  dtaCadastro?: Date;
  idConsulta?: number;
  loteProcedimento: ProcedimentoModelview[] = [];
  desFatura: string = "";
}

export class LoteGuia {
  idConvenio?: number;
  listaGuia?: GuiaTissModelview[];
}

export class ConsultaModelView {
  idConsulta?: number;
  dtaConsulta?: Date;
  dtaAgendamento?: Date;
  tempoConsulta?: string;
  anexoVideo?: string;
  observacaoPaciente?: string;
  anotacaoAnonima?: string;
  localizacaoPaciente?: string;
  flgInativo?: boolean;
  dtaCadastro?: Date;
  dtaCancelamento?: Date;
  motivoCancelamento?: string;
  idUsuarioCancelamento?: number;
  idMedico?: number;
  idPaciente?: number | null;
  flgRealizada?: boolean;
  flgPrimeiraConsulta?: boolean;
  flgAndamento?: boolean;
  pagamento?: string;
  codigoConvenio?: string;
  valorConsulta?: string | null;
  idConvenio?: number;
  flgRetorno?: boolean;
  idCid?: number;
  flgPacienteEntrouConsulta?: boolean;
  idPagamento?: number;
  flgExigePagamento?: boolean;
  desAnotacao?: string;
  idUsuarioGerador?: number;
  idEspecialidade?: number;
  desEspecialidade?: string;
  idTipoAgendamento?: number;
  desTipoAgendamento?: string;
  idClinica?: number | null;
  flgCheckInPaciente?: boolean;
  dtaCheckInPaciente?: Date;
  idUsuarioCheckIn?: number;
  flgProntuario?: boolean;
  flgFilaEspera?: boolean;
  guidAcesso?: string;
  idPessoaSolicitante?: number;
  codAcesso?: string;
  dtaConfirmacao?: Date;
  dtaEnvioPedidoConfirmacao?: Date;
  dtaFinalizacaoAtendimento?: Date;
  idFatura?: number;
  dtaChegadaPaciente?: Date;
  observacaoChegada?: string;
  flgPacienteChegouClinica?: boolean;
  medico?: Medico;
  paciente?: Cliente | null;
  convenio?: Convenio;
  listaAvaliacao: ConsultaAvaliacaoModelView[] = [];
  flgConsulta?: boolean;
  pessoaCancelamento?: Pessoa;
  valorAvaliacao?: number;
  idQuesito?: number;
}

export class DadosLifeLine {
  idConsulta?: number;
  tipoAgendamento?: string;
  flgConsultaFinalizada?: boolean | null;
  obsAgendamento?: string;
  obsConsulta?: string;
  anotacaoAnonima?: string;
  nomeMedico?: string;
  dtaConsulta?: string | null;
  dtaCadastro?: string;
  nomePaciente?: string;
  anexos?: AnexosArquivoModelview[];
  dadosCorpo?: DadosMedicoUsuarioModelview;
}

export class AnexosArquivoModelview {
  idAnexosArquivo?: number;
  desArquivo?: string;
  idConsulta?: number | null;
  dtaCadastro?: string | null;
  idUsuarioGerador?: number | null;
  flgInativo?: boolean | null;
  chaveArquivo?: string;
  flgVisualizacaoUsuario?: boolean | null;
  tipoArquivo?: string;
  idContasPagar?: number | null;
  idContasReceber?: number | null;
}

export class DadosMedicoUsuarioModelview {
  idDadosMedicosUsuario?: number;
  peso?: number | null;
  altura?: number | null;
  imc?: string;
  pressao?: string;
  batimento?: string;
  temperatura?: string;
  idUsuario?: number | null;
  idConsulta?: number | null;
  dtaCadastro?: string | null;
  flgInativo?: boolean | null;
  idUsuarioGerador?: number | null;
}