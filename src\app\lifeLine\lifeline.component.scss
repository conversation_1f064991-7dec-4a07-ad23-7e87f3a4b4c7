@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');


.background-life-line {
  background-image: url(/assets/build/img/background-primary-Grande.png) !important;
  background-position: bottom center;
  background-repeat: no-repeat;
  height: 300px;
  width: 939px;
  display: flex;
  height: 152px;
  border-radius: 10px;    
}
.margem-info {
  margin-top: 25px;
}
.tamanho {
  padding-right: 0px !important;
  padding-left: 0px !important;
  max-width: 939px !important;
  max-height: 95vh !important;
}
.fonteb {
  font-size: 41px;
  font-family: Cairo, sans-serif;
  color: #1265b9;

}

$primary: #348bc1;
.mini-mini {
  width: 10px;
  height: 10px;
  background: $primary;
}

.mini-mini:active {
  background: green;
}

hr.sep-1 {
  border: 0;
  margin-bottom: 0 !important;
  height: 4px;
  width: 90%;
  background-image: linear-gradient(to right, #ffffff, #178aff, #178aff, #178aff);
}

hr.sep-11 {
  border: 0;
  margin-bottom: 0 !important;
  height: 4px;
  width: 90%;
  background-image: linear-gradient(to left, #ffffff, #178aff, #178aff, #178aff);
}

hr.sep-2 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  border: 0;
  height: 2px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  background-image: linear-gradient(to right, #ffffff, #178aff, #178aff, #ffffff);
}

hr.sep-3 {
  border: 0;
  margin-bottom: 0 !important;
  margin-top: 0 !important;
  height: 2px;
  width: 100%;
  background-image: linear-gradient(to right, #ffffff, #178aff, #178aff, #ffffff);
}

hr.sep-4 {
  border: 0;
  margin-bottom: 2 !important;
  margin-top: 0 !important;
  height: 1px;
  width: 100%;
  background-image: linear-gradient(to right, #ffffff, #1265b9, #1265b9, #ffffff);
}

hr.sep-5 {
  border: 0;
  height: 2px;
  width: 100%;
  background-image: linear-gradient(to right, #ffffff, #1265b9, #1265b9, #ffffff);
}

.mat-drawer-inner-container {
  overflow: hidden !important;
}

.btnAtivo {
  background-color: green !important;
}


@media (max-width: 600px) {
  .naparece {
    display: none;
  }
  .background-life-line {
    width: auto;
  }
  .tamanho {
    max-width: 500px !important;
  }
  .background-life-line {
    height: 115px;
  }
  .margem-conteudo {
    margin-top: 40px;
  }
}
@media (max-width: 500px) {
  .tamanho {
    max-width: 425px !important;
  }
  .background-life-line {
    height: 100px;
  }
  .margem-conteudo {
    margin-top: 40px;
  }
}
@media (max-width: 425px) {
  .fonteb {
    font-size: 30px;
    float: left;
    // margin-left: 68px;
  }
  .tamanho {
    padding-right: 0px !important;
    padding-left: 0px !important;
    max-width: 320px !important;
    max-height: 71vh !important;
  }
  .background-life-line {
    width: auto;
    height: 80px;
  }
  .margem-info {
    margin-top: 70px;
    margin-left: 74px;
  }
  .margem-conteudo {
    margin-top: 40px;
  }
}
@media (max-width: 320px) {
  .tamanho {
    padding-right: 0px !important;
    padding-left: 0px !important;
    max-width: 939px !important;
    max-height: 71vh !important;
  }
}
.balao {
  background: #1265b9;
  border-radius: 15px;
  // width: 300px;
  border-top-left-radius: 12px;
  position: relative;
  min-height: 44px;
  margin-top: 10px;
  margin-bottom: 5px;
}
.balao:after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  border-left: 1px solid transparent;
  border-right: 20px solid transparent;
  border-top: 20px solid #1265b9;
  bottom: 28.5px;
  left: -6%;
  transform: rotate(90deg);
}
mat-tab-group{
  --mat-tab-header-with-background-background-color: #1265b9 !important;
}
.btn-primary{
  border-radius: 50%;
  width: 40px;
  height: 40px;

}