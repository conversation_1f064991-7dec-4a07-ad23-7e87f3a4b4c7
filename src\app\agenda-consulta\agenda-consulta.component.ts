import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { MedicoService } from 'src/app/service/medico.service';
import { Component, ChangeDetectorRef } from '@angular/core';
import { CalendarioComponent } from "../../templates/calendario/calendario.component";
import { SpinnerService } from '../service/spinner.service';
import { MatSelectModule } from '@angular/material/select';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Agendamento } from 'src/templates/calendario/calendario.models';
import { AgendaService } from '../service/agenda.service';
import { firstValueFrom } from 'rxjs';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ModalAgendaDeEsperaComponent } from './modais/modal-agenda-de-espera/modal-agenda-de-espera.component';
import { ModalCadastroHorariosComponent, ObjetoModalCadastroHorario } from '../Modais/modal-cadastro-horarios/modal-cadastro-horarios.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NgSelectModule } from '@ng-select/ng-select';
import { ModalMensagemDoDiaComponent } from './modais/modal-mensagem-do-dia/modal-mensagem-do-dia.component';
import { AlertComponent } from '../alert/alert.component';

@Component({
  selector: 'app-agenda-consulta',
  standalone: true,
  imports: [
    CalendarioComponent,
    MatSelectModule,
    CommonModule,
    FormsModule,
    TranslateModule,
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    NgSelectModule,
    ReactiveFormsModule,
  ],
  templateUrl: './agenda-consulta.component.html',
  styleUrl: './agenda-consulta.component.scss'
})

export class AgendaConsultaComponent {
  idTipoUsuarioLogado: number = 1;
  idUltimaClinica: number = 0
  lsMedicos: any[] = [];
  idMedico: number = 0;
  dtCalendario: Date = new Date();
  retAgenda: any[] = [];
  agendamentos: Agendamento[] = [];
  lsDiasTrabalhados: any = [];
  flgLegenda: boolean = false;

  constructor(
    private medicoService: MedicoService,
    private usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private changeDetectorRef: ChangeDetectorRef,
    private agendaService: AgendaService,
    private matDialog: MatDialog,
    private snackBarAlert: AlertComponent,
  ) {
    this.dtCalendario = new Date();
    this.idTipoUsuarioLogado = this.usuarioLogadoService.getIdTipoUsuario()!;
    this.idUltimaClinica = this.usuarioLogadoService.getIdUltimaClinica()!;

    this.CarregaMedicos();

    if (this.idTipoUsuarioLogado == 2) {
      this.idMedico = this.usuarioLogadoService.getIdMedico()!;
      this.carregarAgendamentos();
    }
  }

  toggleLegenda(): void {
    this.flgLegenda = !this.flgLegenda;
  }

  abrirModalMensagemDoDia() {
    this.matDialog.open(ModalMensagemDoDiaComponent, { width: "40vmax" });
  }


  CarregaMedicos() {
    this.medicoService.getMedicos(null, this.idUltimaClinica).subscribe((retorno) => {
      this.lsMedicos = retorno
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  async carregarAgendamentos() {
    this.spinner.show();
    await this.carregaAgendaMedico();

    this.agendamentos = this.converterParaAgendamento(this.retAgenda);

    this.changeDetectorRef.detectChanges();
    this.spinner.hide();
  }

  carregaListaDiasTrabalhados() {
    this.agendaService.GetDiasAgenda(this.idMedico).subscribe((ret) => {
      this.lsDiasTrabalhados = ret;
    })
  }

  async carregaAgendaMedico() {

    let dados = await firstValueFrom(this.agendaService.GetAgenda(this.idMedico, 0, this.dtCalendario.toDateString(), null))

    if (dados) {
      this.retAgenda = dados;
    }
  }

  onMedicoChange() {
    this.carregarAgendamentos();
  }

  ModalAgendaEspera() {
    let dadosMedico: number | null = null;
    if (this.idMedico)
      dadosMedico = this.idMedico;
    

    let dialogRef = this.matDialog.open(ModalAgendaDeEsperaComponent, {
      width: "40vmax",
      height: "70vh",
      data: dadosMedico
    })

    dialogRef.afterClosed().subscribe(() => {
      this.carregarAgendamentos();
    })
  }

  converterParaAgendamento(dadosAgenda: any[]): Agendamento[] {
    return dadosAgenda.map(item => {

      const dataAgenda = new Date(item.dtaAgenda);

      const horaInicio = new Date(dataAgenda);

      let horaFim = new Date(dataAgenda);
      horaFim.setHours(horaFim.getHours() + 1);
      let descricaoBase = ""
      if (!item.flgPeriodoOff)
        descricaoBase = `${item.nomePaciente} - ${item.motivo || 'Consulta'}`;
      else
        descricaoBase = "Time off";

      let tipoAgendamento = item.idTipoAgendamento || 1;
      let corEvento = '#4285F4';
      let descTipoAgendamento = "Consulta";

      if (item.flgInativa === true) {
        descTipoAgendamento = "Cancelado";
        corEvento = '#EA4335';
        descricaoBase += ' (Cancelado)';
        tipoAgendamento = 4;
      }
      else if (item.flgPeriodoOff === true) {
        descTipoAgendamento = "Time Off";
        corEvento = '#6a676b';
        descricaoBase = 'Time Off';
        tipoAgendamento = 5;
        horaFim = new Date(item.dtFinalAtendimento);

      }
      else if (item.flgFinalizada === true) {
        descTipoAgendamento = "Finalizado";
        corEvento = '#34A853';
        descricaoBase += ' (Finalizado)';
        tipoAgendamento = 3;
      } else if (item.flgEmAndamento === true) {
        descTipoAgendamento = "Em Andamento";
        corEvento = '#FBBC05';
        descricaoBase += ' (Em andamento)';
        tipoAgendamento = 2;
      }

      return {
        IdAgendamento: item.idAgenda,
        DtAgendamento: dataAgenda,
        horaInicioAgendamento: horaInicio,
        horaFinalAgendamento: horaFim,
        DescAgendamento: descricaoBase,
        TipoAgendamento: tipoAgendamento,
        DescTipoAgendamento: descTipoAgendamento,
        CorEvento: corEvento,
        CorTexto: '#FFFFFF',
        dadosOriginais: item
      };
    });
  }

  getCorPorTipoAgendamento(tipoId: number | null): string {
    const coresPorTipo: { [key: number]: string } = {
      1: '#4285F4',
      2: '#EA4335',
      3: '#FBBC05',
      4: '#34A853',
      5: '#6a676b'
    };

    if (tipoId && coresPorTipo[tipoId]) {
      return coresPorTipo[tipoId];
    }

    return '#4285F4';
  }

  onDiaTrocado(novoDia: Date) {
    this.dtCalendario = novoDia;
    this.changeDetectorRef.detectChanges();
  }

  onMesTrocado(novoMes: Date) {
    this.dtCalendario = novoMes;
    this.changeDetectorRef.detectChanges();
  }

  AbrirModalAgendamento() {
    let dados = new ObjetoModalCadastroHorario();

    if (this.idMedico)
      dados.IdMedico = this.idMedico;

    let dialog = this.matDialog.open(ModalCadastroHorariosComponent, {
      width: "40vmax",
      data: dados
    })

    dialog.afterClosed().subscribe(() => {
      this.carregarAgendamentos();
    })
  }

  onHorarioClicado(evento: { data: Date, hora: string }) {
    this.dtCalendario = evento.data;
    let dt = new Date();

    if (evento.data < dt) {
      this.snackBarAlert.falhaSnackbar("Não é possível cadastrar agendamentos passados.");
      return;
    }

    let dados = new ObjetoModalCadastroHorario();

    if (this.idMedico)
      dados.IdMedico = this.idMedico;

    dados.dtaAgendamento = evento.data;

    this.abrirModalCadastroHorario(dados);

  }

  onAgendamentoClicado(agendamento: Agendamento) {
    let dt = new Date();

    if (agendamento.DtAgendamento < dt) {
      this.snackBarAlert.falhaSnackbar("Não é possível editar agendamentos passados.");
      return;
    }

    let dados = new ObjetoModalCadastroHorario();
    dados.IdAgendamento = agendamento.IdAgendamento!;

    if (this.idMedico)
      dados.IdMedico = this.idMedico;

    this.abrirModalCadastroHorario(dados);
  }

  abrirModalCadastroHorario(dados: ObjetoModalCadastroHorario) {
    let dialog = this.matDialog.open(ModalCadastroHorariosComponent, {
      width: "40vmax",
      data: dados
    })

    dialog.afterClosed().subscribe(() => {
      this.carregarAgendamentos();
      this.changeDetectorRef.detectChanges();
    })

  }
}