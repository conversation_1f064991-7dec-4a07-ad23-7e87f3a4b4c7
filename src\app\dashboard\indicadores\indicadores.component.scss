@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

.qd-video {
    border: 1px solid #4d4d4d;
    border-radius: 6px;
    box-shadow: 0px 1px 8px #4d4d4d;
    /* margin-bottom: 30px; */
    margin: 0 0 30px 0;
}

.mother-div {
    margin-left: 20px!important;
    margin-top: 10px;
    z-index: unset;
}

// .custom-card{
//   margin-left: 15px;
//   margin-right: 15px;
// }
.qd-video div:last-child {
    background-color: #fff;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}
.center {
    margin: auto;
    width: 50%;
    padding: 10px;
  }
p.assista {
    font-size: 15px;
    font-weight: 600;
    color: #0b1825;
    padding-left: 20px;
    font-family: "Montserrat", "Open Sans", Arial, sans-serif;
}
.margem-estrela{
    margin-top: 1%; 
    display: flex;
    margin: 30px auto;
    justify-content: center; 
    text-align: center; 
    align-items: center;
}

.TitleTutorial {
    margin-top: 30px;
    margin-bottom: 30px;
    font-family: "Cairo", "Open Sans", <PERSON><PERSON>, sans-serif;
    font-weight: 300;
    text-align: center;
    font-size: 26px;
}
.Card {
    color: #214163;
}
.Card h3 {
    font-family: Cairo, sans-serif;
}
.custom-card {
    padding: 0;
}
.pmob {
    border-radius: 5px;
    padding: 20px;
    //box-shadow: 2px 2px 2px 2px #bbb;
    cursor: pointer;
}
hr.sep-1 {
    border: 0; 
    margin-bottom: 0 !important;
    height: 4px; 
    // width: 50%;
    background-image: linear-gradient(to right, #fff, #1265b9 , #1265b9 , #fff);
    border-radius: 10px;
    // margin: 0 270px;
  }
  .number-consul {
      font-family: Cairo, sans-serif;
      font-weight: 500;
  }
  .text-graficos {
      font-family: Cairo, sans-serif;
      font-weight: 500;
  }

.background-life-line {
    background-image: url(/assets/build/img/background-primary-Grande.png) !important;
    background-position: bottom center;
    background-repeat: no-repeat;
    height: 300px;
    width: 1001px;
}

.h-84 {
    height: 75px;
    background: #348bc1;
}

.h4-perg {
    padding-top: 20px;
    color: white;
}

$primary: #348bc1;
.mini-mini {
    width: 10px;
    height: 10px;
    background: $primary;
}

.icon-item {
    float: right;
    left: 130px;
    text-align: right;
    display: -ms-grid;
    display: flex;
    bottom: 100px;
    position: absolute;
}

.icon-align {
    margin-right: 5px;
    margin-left: 10px;
    margin-top: 8px;
    font-size: 30px;
}

.avisos {
    background: #fb292e;
    color: white;
}

.mensagens {
    background: #0091f2;
    color: white;
}

.consultas {
    background: #01ccac;
    color: white;
}

.agendamento {
    background: #1265b9;
    color: white;
}

.exames {
    background: #f1c12f;
    color: white;
}

.dashcard-desktop {
    flex: 0 0 25%;
    max-width: 20%;
    margin-left: 10px;
    margin-right: 10px;
    cursor: pointer;
}

.dash-icon {
    font-size: 2rem;
}

.dash-text {
    font-size: 1.5rem;
}

.video-font-content {
    font-size: 15px;
    padding-left: 15px;
    padding-right: 15px;
    font-weight: bolder;
    color: #173552;
    margin-top: 105px;
}


/* Big Size  */


/* Badge Material  */

.mat-badge-warn .mat-badge-content {
    color: #fff;
    background: unset !important;
    position: absolute;
    left: 4px;
    top: 3px !important;
}

.mat-badge-accent .mat-badge-content {
    color: #fff;
    background: unset !important;
    position: absolute;
    left: 4px;
    top: 3px !important;
}

.mat-badge-content {
    color: #fff;
    background: unset !important;
    position: absolute;
    left: 4px;
    top: 3px !important;
}

.mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content {
    right: -13px;
}

.img-logo-inicio {
    width:250px; 
    margin-left: 10px;
}

.btnConsul {
    background: rgb(122, 163, 229);
}

.btnFinan {
    background:rgb(122, 163, 229);
}

.aval-gold {
    border: 1px solid #1265b9; 
    margin-right: 20px; 
    border-radius: 50px;
}
.div-gold-text {
    text-align: center;
}

.aval-prata {
    border: 1px solid #1265b9; 
    margin-right: 20px; 
    border-radius: 50px;
}

.div-prata-text {
    text-align: center;
}

.aval-bronze {
    border: 1px solid #1265b9;
    border-radius: 50px;
}

.div-bronze-text {
    text-align: center;
}

.btns-graficos {
    margin-top: 20%; 
    margin: 0 auto; 
    justify-content: center; 
    text-align: center;
}

.div-info-graficos {
    background: rgb(51, 102, 204);
    border-radius: 50px; 
    margin: 0 auto;
    justify-content: center; 
    cursor: pointer; 
    font-size: 18px;
    color: #fff;
}


@media (max-width: 2560px) and (min-width: 2000px) {
    .video-font-content {
        padding-top: 32%;
        font-size: 18px;
    }
    .TitleTutorial {
        font-size: 2.25rem;
    }
    p.assista {
        font-size: 20px;
    }
    .h-v {
        height: 400px;
    }
}

@media (min-width: 1530px) {
    .icon-item {
        left: 164px !important;
    }
}

@media (min-width: 1500px) and (max-width: 2000px) {
    .h-v {
        height: 245px;
    }
    .mat-badge-medium.mat-badge-overlap.mat-badge-after .mat-badge-content {
        right: -4px;
    }
    .icon-item {
        left: 230px;
    }
}

@media (max-width: 1440px) {
    .h-v {
        height: 230px;
    }
}

@media (max-width: 1330px) {
    .col-6 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
    }
}

@media (max-width: 1120px) {
    .title-agend {
        margin-left: -8px;
    }
}

@media (max-width: 1024px) {
    .video-font-content {
        padding-top: 0px;
        font-size: 13px;
        padding-bottom: 15px;
    }
    p.assista {
        font-size: 12px;
        line-height: 20px;
    }
    .icon-item {
        left: 50px;
    }
}

@media (max-width:1010px) {
    .title-agend {
        margin-left: -11px;
    }
}

@media (max-width: 991px) {
    .video-font-content {
        padding-top: 0px;
    }
    .video-content {
        padding: 0 !important;
    }
    p.assista {
        padding-bottom: 25px;
    }
    .icon-item {
        left: 73px;
    }
    .card-dash {
        border: 1px solid #b7b7b7;
        padding: 15px;
        border-radius: 5px;
        color: #0d4073;
        display: flex;
        cursor: pointer;
        margin: 20px 20px;
        // box-shadow:0 4px 10px 0 rgba(0,0,0,0.2), 0 4px 20px 0 rgba(0,0,0,0.19);
    }
    .Title-dash-bold {
        margin-left: auto !important;
        font-size: 1.5rem !important;
        margin-right: 10px;
        margin-bottom: auto;
        margin-top: auto;
    }
    .Title-dash {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 10px;
        font-weight: 600;
        font-size: 1.2rem;
    }
    .circle {
        width: 50px;
        height: 50px;
        // background: #000;
        color: white;
        padding: 13px;
        border-radius: 50%;
    }
    .circle-lifeline {
        background-color: #f1c12f;
        width: 50px;
        height: 50px;
        color: white;
        padding: 13px;
        border-radius: 50%;
    }
   
}

@media (max-width: 860px) {
    .btns-aval {
        max-width: 210px;
        padding: 3px;
    }
}

@media (max-width: 800px) {
    .margem-estrela {
        display: block;
        margin: 50px auto;
    }
    .btns-aval {
        margin: 0 auto;
        max-width: 200px;
        margin-bottom: 10px;
    }
}

@media (max-width: 768px) {
    hr.sep-1 {
        margin-left: 20px;
    }
}

@media (max-width: 767px) {
    .btns-aval {
        margin-bottom: 10px;
    }
  
}

@media (max-width: 720px) {
    .icon-item {
        left: 500px;
    }
    .video-font-content {
        margin-top: 0px;
    }
}

@media (max-width: 710px) {
    .video-font-content {
        padding-top: 0px;
    }
}

@media (max-width: 690px) {
    .icon-item {
        left: 500px;
    }
}

@media (max-width: 600px) {
    .btns-aval {
        max-width: 200px;
        margin: 10px auto;
    }
}

@media (max-width: 540px) {
    .TitleTutorial {
        padding-left: 0 !important;
        text-align: center;
    }
   
}

@media (max-width: 430px) {
    .adjust-height {
        height: 105px;
    }
    .Title-dash {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 10px;
        font-weight: 600;
        font-size: 12px;
    }
    .pmob{
        padding: unset;
    }
    p.assista {
        padding-bottom: 0px;
    }
    .video-font-content {
        padding-bottom: 15px;
        margin-top: 0;
        padding-top: 0px;
        font-size: 10.6px;
    }
    .mobile-content {
        padding-left: 10px;
        padding-right: 10px;
    }
    .image-controller {
        padding: 20%;
    }
    .table thead th {
        font-weight: normal;
        background-color: #1265b9;
        ;
        color: #fff;
        font-size: 15px;
        border: 0;
    }
    .icon-item {
        left: 200px;
    }
    .image-resize {
        width: 25%;
    }
    .icon-item {
        float: right;
        left: 245px;
        text-align: right;
        display: -ms-grid;
        display: flex;
        position: absolute;
        bottom: 65px;
    }
    .button-card {
        height: 106px;
    }
}


@media (max-width: 375px) {
    .video-font-content {
        padding-bottom: 0 !important;
        padding-top: 2px !important;
    }
    .icon-item {
        left: 215px;
        text-align: right;
        display: -ms-grid;
        display: flex;
        position: absolute;
        bottom: 85px;
    }
    .button-card {
        height: 79px;
        margin-bottom: 10px;
    }
}

@media (max-width: 360px) {
    .img-logo-inicio {
        width:200px; 
        margin-left: 0px;
        text-align: center;
        justify-content: center;
        margin-top: 10px;
    }
    .Title-dash-bold {
        font-size: 14px !important;
    }
}


@media (max-width: 320px) {
    .icon-item {
        left: 162px;
    }
    .Title-dash-bold {
        margin-left: 7px !important;
    }

}

@media (max-height: 667px) {
    .custom-card {
        padding-bottom: 10px !important;
        height: auto;
    }
    .TitleTutorial {
        padding-top: 6px !important;
        padding-left: 20px;
        padding-bottom: 18px !important;
    }
}

@media (max-height: 630px) {
    .custom-card {
        padding-bottom: 0px !important;
        height: auto;
    }
    .TitleTutorial {
        padding-top: 0px !important;
        padding-left: 20px;
        padding-bottom: 0px !important;
    }
    .dashcard-desktop {
        flex: 0 0 25%;
        max-width: 20%;
        margin-left: 10px;
        margin-right: 10px;
        cursor: pointer;
        height: 107px;
    }
    .flipper {
        margin-top: -10px!important;
    }
    label {
        margin-left: 5px!important;
    }
}

.pmob:hover{ 
     transition: padding 0.15s linear;
    -moz-transition: padding 0.15s linear;
    -webkit-transition: padding 0.15s linear;
    box-shadow: 2px 2px 2px 3px #bbb;
}
// HISTORICOOOO
.contents-table {
    // Do not use margin here. It will be overwritten.
    position: absolute; // <-- must have.
    padding: 2rem;
}

.contents-table.sticky {
    position: fixed; // <-- must have.
    top: 0; // <-- must have.
}

.contents-table {
    list-style: none;
    margin: 0;
}

.contents-table a {
    border-radius: 4px;
    display: block;
    padding: 0.3rem 0.6rem;
    color: #444;
    text-decoration: none;
}

.contents-table a.active {
    background-color: #000;
    color: #fff;
}

.mail-content {
    border-top-left-radius: 30px;
    border-top-right-radius: 30px;
    bottom: 110px;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
}

.columnify>.table-column {
    flex: 0 1 auto;
    width: 220px;
}

// Columnify https://stackoverflow.com/a/47220287
.columnify {
    display: block;
}

.columnify>* {
    flex: 1;
}

@media (min-width: 992px) {
    .no-deskmob {
        display: none!important;
    }
}

@media (max-width: 779px) {
    .no-deskmob2 {
        display: none!important;
    }
}

@media (max-width: 991px) {
    .no-mobhalf {
        display: none!important;
    }
}

.teste {
    width: 750px !important;
    height: 200px !important;
}

.svg {
    width: 200px;
    height: auto;
}

.ngx-charts {
    width: 750px !important;
    height: 200px !important;
}

.svg.ngx-charts {
    width: 250px !important;
    height: 200px !important;
}