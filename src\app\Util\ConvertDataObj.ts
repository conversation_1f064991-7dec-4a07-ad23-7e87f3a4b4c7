import { objEnvioDataApi } from "../model/modelo-data-api";

export function ConvertDataObj(dt:any, flgTipoData:any): objEnvioDataApi {

	const dataInicio = flgTipoData? formataPorData(dt) : formataPorString(dt);
	
	let dados = new objEnvioDataApi();
	
	dados = {
		dia: dataInicio.dia,
		mes: dataInicio.mes,
		ano: dataInicio.ano
	};
	
	return dados;
}

export function ConvertObjData(data: objEnvioDataApi): Date {
	return new Date(data.ano, data.mes - 1, data.dia)
}


function formataPorData(data: Date): { dia: number, mes: number, ano: number } {
    const dia = data.getDate();
    const mes = data.getMonth() + 1; 
    const ano = data.getFullYear();

    return { dia, mes, ano };
}

function formataPorString(data: string): { dia: number, mes: number, ano: number } {
	const [ano, mes, dia] = data.split("-").map(Number);

	return { dia, mes, ano };
}