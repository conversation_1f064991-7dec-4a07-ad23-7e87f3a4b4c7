import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { FiltraExamesFormularios, analiseFormularioModel, formulariosModel, ids_formularioRespostas, listaPerguntasRespostas, listaRespostas } from '../model/formularios';
import { mensagemRetorno } from '../model/mensagemRetorno';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class FormulariosService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getAllFormularios(){
        this.spinner.show();
        return this.http.get<formulariosModel[]>(environment.apiEndpoint + '/formulario/getAllFormularios');
    }
    
    public getFormularioById(id: number){
        this.spinner.show();
        return this.http.post<formulariosModel>(environment.apiEndpoint + '/formulario/getFormularioById', id)
    }
    
    public saveFormulario(objFormulario: formulariosModel){
        this.spinner.show();
        return this.http.post<mensagemRetorno>(environment.apiEndpoint + '/formulario/saveFormulario', objFormulario);
    }
    
    public deleteFormulario(objFormulario: number){
        this.spinner.show();        
        return this.http.post<mensagemRetorno>(environment.apiEndpoint + '/formulario/deleteFormulario', objFormulario);
    }
    
    public getFormulariosByIdAnalise(id: number){
        this.spinner.show();
        return this.http.post<analiseFormularioModel[]>(environment.apiEndpoint + '/formulario/getFormulariosByIdAnalise', id);
    }
    public salvaAnaliseFormularios(listaSalvamento: analiseFormularioModel[]){
        this.spinner.show();
        return this.http.post<boolean>(environment.apiEndpoint + '/formulario/salvaAnaliseFormularios', listaSalvamento);
    }
    
    public getPerguntas(idFormulario: number){
        this.spinner.show();
        return this.http.post<listaPerguntasRespostas[]>(environment.apiEndpoint + '/formulario/getPerguntas', idFormulario)
    }
    
    public getPerguntasRespostas(idFormulario: number, idAnalise: number){
        this.spinner.show();
        
        let ids = new ids_formularioRespostas();
        ids.idFormulario = idFormulario;
        ids.idAnalise = idAnalise;
        
        return this.http.post<listaPerguntasRespostas[]>(environment.apiEndpoint + '/formulario/getPerguntasRespostas', ids)
    }
    
    public GetFormularioResponido (idUsuario: number){
        this.spinner.show();
        
        return this.http.post<analiseFormularioModel[]>(environment.apiEndpoint + '/formulario/GetFormularioResponido', idUsuario)
    }
    
    public GetPerguntasParaResposta (idFormulario: number, idAnalise: number){
        this.spinner.show();
        
        let ids = new ids_formularioRespostas();
        ids.idFormulario = idFormulario;
        ids.idAnalise = idAnalise;
        return this.http.post<listaRespostas[]>(environment.apiEndpoint + '/formulario/GetPerguntasParaResposta', ids)
    }
    
    public SalvarRespostas (lista: listaRespostas[]){
        this.spinner.show();
        return this.http.post<boolean>(environment.apiEndpoint + '/formulario/SalvarRespostas', lista)
    }

    public getIdsFormsExames (idAnalise?: number){
        return this.http.post<FiltraExamesFormularios>(environment.apiEndpoint + '/formulario/getIdsFormsExames', idAnalise)
    }
}