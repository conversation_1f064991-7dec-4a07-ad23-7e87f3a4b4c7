import { Component, OnInit } from "@angular/core";
import { UsuarioService } from "src/app/service/usuario.service";
import { ValidacaoService } from "src/app/service/validacao.service";
import { UsuarioLogadoService } from "src/app/auth/usuarioLogado.service";
import { NgxSmartModalModule, NgxSmartModalService } from "ngx-smart-modal";
import { Pessoa } from "src/app/model/pessoa";
import { Cliente } from "src/app/model/cliente";
import { Contato } from "src/app/model/contato";
import { PacienteService } from "src/app/service/pacientes.service";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from "@angular/forms";
import { ConsultaService } from "src/app/service/consulta.service";
import { ValidadoreseMascaras } from "src/app/Util/validadores";
import { AgendaService } from "src/app/service/agenda.service";
import { AuthService } from "src/app/login/auth.service";
import { Router, ActivatedRoute, RouterModule } from "@angular/router";
import { LocalStorageService } from "src/app/service/LocalStorageService";
import { ClinicaService } from "src/app/service/clinica.service";
import { SignalHubService } from "src/app/service/signalHub.service";
import { SpinnerService } from "src/app/service/spinner.service";
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from "@angular/common";
import { MatDivider } from "@angular/material/divider";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatCardModule } from "@angular/material/card";

@Component({
    selector: "app-acesso-rapido-covid",
    templateUrl: "./acesso-rapido-covid.component.html",
    styleUrls: ["./acesso-rapido-covid.component.scss"],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      RouterModule,
      MatFormFieldModule,
      MatDivider,
      NgxSmartModalModule,
      TranslateModule,
      MatCardModule
    ]
})
export class AcessoRapidoCovidComponent implements OnInit {
  constructor(
    public ngxSmartModalService: NgxSmartModalService,
    private usuarioLogadoService: UsuarioLogadoService,
    public validador: ValidacaoService,
    private usuarioService: UsuarioService,
    // public snackBar: MatSnackBar,
    public pacientesService: PacienteService,
    public consultaService: ConsultaService,
    private validacao: ValidadoreseMascaras,
    public agendaService: AgendaService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    public translate: TranslateService,
    private localStorageService: LocalStorageService,
    public clinicaService: ClinicaService,
    private signalHubService: SignalHubService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,

  ) {
    translate.setDefaultLang("pt");
    this.Linguagem = this.localStorageService.Linguagem;
  }

  mensagemPaciente?: string;
  campoEmailVazil = false;
  campoExitente?: string;

  // actionButtonLabel: string = "Fechar";
  // salvoSucess: string = "Cadastro salvo com Sucesso. ✔";
  // ErroSalvar: string = "Erro ao salvar!";
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = "right";
  // verticalPosition: MatSnackBarVerticalPosition = "bottom";
  // concordo: boolean;
  // concordomsg: boolean;

  PacienteAcesso: any = [];
  codAcesso?: string;
  logininvalido?: boolean;
  DivInicial = true;
  DivCronometro = false;
  idGuid = "";
  mensagemModal = "";
  DivEspera = false;
  flgIniciarConsulta?: boolean;
  usuarioConsulta: any = [];
  timeInterval: any;
  ModoLogin = "Cpf";
  cliente: any;
  showMessageError = false;
  Teinvalido = false;
  Linguagem: string = "";

  TelMovVal?: boolean;
  TelMovValVasil = false;
  TelMovExistente = false;
  campoEmailexistente = false;
  campoEmailInvalido = false;
  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;
  idClinica: number = 40;

  campoCodInvalido: boolean = false;
  campoCodVazil: boolean = false;
  ngOnInit() {
    localStorage.clear();
    sessionStorage.clear();

    this.idGuid = this.route.snapshot.routeConfig!.path!;

    //   if (window.location.hostname == "camposverdes.medicinaparavoce.com.br")
    //   this.idClinica  =1
    //   else if (window.location.hostname == "atendimento.medicinaparavoce.com.br")
    //   this.idClinica  =40
    // else
    // this.idClinica  = 1

    if (this.idGuid == "gobox/:cpf")
      this.tentarCarregarPorCPF();

    if (!this.Linguagem || this.Linguagem == "null") this.Linguagem = "pt";
  }

  tentarCarregarPorCPF() {
    var cpf = this.route.snapshot.params.cpf;
    if (cpf) {
      this.PacienteAcesso.cpf = cpf;
      this.PacienteAcesso.CodAcesso = "GOBOX";
      this.validarCpf();
    }
  }

  SubmitNovoPaciente() {
    try {
      this.validarCampos();
      if (this.showMessageError == true) {
        return;
      }

      var pessoa = new Pessoa();
      var cliente = new Cliente();
      var contato = new Contato();
      if (this.cliente != null) {
        if (this.cliente.paciente) {
          cliente.idCliente = this.cliente.paciente.idCliente;
        } else
          cliente.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

        pessoa.idPessoa = this.cliente.pessoa.idPessoa;
        pessoa.idContato = this.cliente.pessoa.idContato;
        contato.idContato = this.cliente.contato.idContato;
      } else
        pessoa.idUsuarioGerador = contato.idUsuarioGerador = cliente.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

      cliente.codAcesso = (document.getElementById("cod") as HTMLInputElement)["value"].toUpperCase();
      pessoa.cpf = this.PacienteAcesso.cpf;
      pessoa.nomePessoa = this.PacienteAcesso.nome;
      pessoa.email = this.PacienteAcesso.email;
      contato.telefoneMovel = this.PacienteAcesso.telefoneMovel;
      pessoa.contato = contato;
      cliente.pessoa = pessoa;
      cliente.idClinica = this.idClinica;

      this.pacientesService.salvarPacienteAgendaAcesso(cliente).subscribe(
        (retornoId) => {
          if (!retornoId) {
            this.snackBarAlert.falhaSnackbar('Erro ao salvar paciente')
          } else {
            this.usuarioService
              .validar_TELMOVEL_CPF_EMAIL_Acesso_Fila(
                this.PacienteAcesso.cpf,
                null,
                "CPF"
              )
              .subscribe(
                (retorno) => {
                  ;
                  this.cliente = null;
                  if (retorno != null) {
                    this.campoExitente = "CPF";
                    this.cliente = retorno;
                    this.realizarLogin();
                  } else {
                    this.DivEspera = true;
                  }
                },
                (err) => {
                  console.error(err);
                }
              );
          }
          this.spinner.hide();
        },
        (err) => {
          console.error(err);
          this.spinner.hide();
        }
      );
    } catch (error) {
      this.snackBarAlert.falhaSnackbar('Erro ao salvar paciente')
      console.error(error);
    }
  }

  public mascaraText(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    this.PacienteAcesso.nome = v;
    // (<HTMLInputElement>evento.target).value = v;
  }
  agendaFilaEspera() {
    var tele = this.cliente.contato.telefoneMovel.substring(
      this.cliente.contato.telefoneMovel.length - 4
    );
    if (this.codAcesso == tele) {
      var obj = (document.getElementById("cod") as HTMLInputElement)["value"].toUpperCase();
      if (this.cliente.paciente != null) {
        this.agendaService
          .AgendaFilaEspera(
            this.cliente.paciente.idCliente,
            this.idClinica,
            obj
          )
          .subscribe((retorno) => {
            if (retorno)
              this.realizarLogin();

            this.spinner.hide();
          });
        } else {
        var pessoa = new Pessoa();
        var cliente = new Cliente();
        var contato = new Contato();

        pessoa.idPessoa = this.cliente.pessoa.idPessoa;
        pessoa.idContato = this.cliente.pessoa.idContato;
        contato.idContato = this.cliente.contato.idContato;

        pessoa = this.cliente.pessoa;
        pessoa.contato = this.cliente.contato;
        cliente.pessoa = pessoa;

        cliente.idClinica = this.idClinica;

        cliente.codAcesso = (document
          .getElementById("cod") as HTMLInputElement)
        ["value"].toUpperCase();
        this.pacientesService
          .salvarPacienteAgendaAcesso(this.cliente)
          .subscribe((retorno) => {
            if (retorno) {
              this.realizarLogin();
              this.spinner.hide();
            }
          });
      }
    } else this.Teinvalido = true;
  }

  async realizarLogin() {
    const fncError = () => {
      this.logininvalido = true;
    };

    var sGuid = "";
    this.route.queryParams.subscribe((params) => {
      sGuid = params["guid"];
    });
    if (sGuid == undefined) sGuid = "";

    try {
      ;
      const data: any = await this.authService.AutenticaUsuario(
        this.cliente.pessoa.cpf,
        this.cliente.senha,
        sGuid,
        this.ModoLogin
      );
      ;
      ;

      if (data.access_token != null) {
        this.localStorageService.token = data.access_token;
        const dataUsuario: any = await this.usuarioService
          .getPerfilUsuarioLogado()
          .toPromise();
        if (dataUsuario == false) return fncError();

        this.usuarioService.AtualizaDadosUsuarioLogado(dataUsuario);
        this.spinner.hide();

        this.clinicaService
          .TrocarClinica(
            this.usuarioLogadoService.getIdUsuarioAcesso()!,
            this.idClinica
          )
          .subscribe(
            () => {
              this.alteraDadosUsuario();
              this.spinner.hide();
            },
            (err) => {
              console.error(err);
              this.spinner.hide();
            }
          );
      } else {
        this.spinner.hide();
        return fncError();
      }
    } catch (res) {
      this.spinner.hide();
      return fncError();
    }
  }

  async alteraDadosUsuario() {
    this.usuarioService.getPerfilUsuarioLogadoClinica().subscribe((retorno) => {
      this.usuarioService.AtualizaDadosUsuarioLogado(retorno);

      this.signalHubService.loginUsuario(retorno);
      this.ngxSmartModalService.getModal("UsuarioExistente").close();
      this.localStorageService.Logado = true;
      this.localStorageService.Linguagem = this.Linguagem;

      this.localStorageService.TelaPrivacidadeLogin = "AcessoAgenda";

      this.router.navigate(["filaespera"]);
      this.spinner.hide();
    });
  }

  // ErrorSalvar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];

  //     this.tradutor.get("TELAAGENDA.ERROAOSALVAR").subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(
  //         res,
  //         this.action ? this.actionButtonLabel : undefined,
  //         config
  //       );
  //     });
  //   }
  // }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }

  ValidarTelMovel(value:any) {
    this.TelMovVal = false;
    this.TelMovExistente = false;
    if (value != "") {
      this.TelMovValVasil = false;
      this.usuarioService
        .validar_TELMOVEL_CPF_EMAIL_Acesso_Fila(value, null, "TelMovel")
        .subscribe(
          (retorno) => {
            ;

            this.mensagemPaciente = "";
            this.cliente = null;
            if (retorno != null) {
              this.TelMovExistente = true;
              this.snackBarAlert.falhaSnackbar("Este telefone já está sendo utilizado!");
            }
          },
          (err) => {
            console.error(err);
          }
        );
    } else this.TelMovValVasil = true;
  }

  public ValidarEmail(value:any) {
    this.campoEmailInvalido = false;
    this.campoEmailexistente = false;
    if (value != "") {
      this.campoEmailVazil = false;
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        return;
      }
      this.campoEmailInvalido = false;
      this.usuarioService
        .validar_TELMOVEL_CPF_EMAIL_Acesso_Fila(value, null, "EMAIL")
        .subscribe(
          (retorno) => {
            this.mensagemPaciente = "";
            if (retorno != null) {
              this.campoEmailexistente = true;
              this.snackBarAlert.falhaSnackbar("Este endereço de email já está sendo utilizado!");
            }
          },
          (err) => {
            console.error(err);
          }
        );
    } else this.campoEmailVazil = true;
  }

  // SalvarSuces(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["success-snack"];

  //     //atualiza legenda
  //     this.consultaService.atualizaLegenda$.emit();
  //     this.tradutor.get("TELAAGENDA.CADASTROSALVO").subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(
  //         res,
  //         this.action ? this.actionButtonLabel : undefined,
  //         config
  //       );
  //     });
  //   }
  // }

  LimparCamposNovoPaciente() {
    this.DivEspera = false;
    this.PacienteAcesso = [];
    this.cpf.markAsUntouched();
    this.Nome.markAsUntouched();
    this.email.markAsUntouched();
    this.tel.markAsUntouched();
    this.TelMovVal = false;
    this.TelMovValVasil = false;
    this.TelMovExistente = false;
    this.campoEmailexistente = false;
    this.campoEmailInvalido = false;
    this.campoCPFVazil = false;
    this.campoCPFInvalido = false;
  }

  ValidaSUS(value:any) {
    this.validacao.validaCNS(value);
  }

  public validarCampos() {
    this.showMessageError = false;
    this.cpf.markAsTouched();
    this.Nome.markAsTouched();
    this.email.markAsTouched();
    this.DtaNasc.markAsTouched();
    this.tel.markAsTouched();

    if (
      this.PacienteAcesso.CodAcesso == undefined ||
      !this.PacienteAcesso.CodAcesso.trim()
    ) {
      this.campoCodInvalido == false;
      this.campoCodVazil = true;
    }

    if (
      this.PacienteAcesso.telefoneMovel == undefined ||
      !this.PacienteAcesso.telefoneMovel.trim()
    )
      this.TelMovValVasil = true;

    if (
      this.PacienteAcesso.email == undefined ||
      !this.PacienteAcesso.email.trim()
    )
      this.campoEmailVazil = true;

    if (this.PacienteAcesso.cpf == undefined || !this.PacienteAcesso.cpf.trim())
      this.campoCPFVazil = true;

    if (
      this.PacienteAcesso.nome == undefined ||
      !this.PacienteAcesso.nome.trim() ||
      this.PacienteAcesso.cpf == undefined ||
      !this.PacienteAcesso.cpf.trim() ||
      this.PacienteAcesso.telefoneMovel == undefined ||
      !this.PacienteAcesso.telefoneMovel.trim() ||
      this.campoEmailVazil == true ||
      this.campoEmailInvalido == true ||
      this.campoEmailexistente == true ||
      this.campoCPFVazil == true ||
      this.campoCPFInvalido == true ||
      this.TelMovValVasil == true ||
      this.TelMovVal == true ||
      this.TelMovExistente == true ||
      this.campoCodVazil == true ||
      this.campoCodInvalido == true
    ) {
      this.showMessageError = true;
    }
  }

  getErrorMessageNome() {
    return this.Nome.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.Nome.hasError("Nome")
        ? "TELAAGENDA.ERRONAOEVALIDO"
        : "";
  }

  getErrorMessagetel() {
    return this.tel.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.tel.hasError("Nome")
        ? "TELAAGENDA.ERRONAOEVALIDO"
        : "";
  }
  getErrorMessagesus() {
    return this.sus.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.sus.hasError("Nome")
        ? "TELAAGENDA.ERRONAOEVALIDO"
        : "";
  }
  Nome = new FormControl("", [Validators.required, Validators.required]);
  email = new FormControl("", [Validators.required, Validators.email]);
  cpf = new FormControl("", [
    Validators.minLength(10),
    Validators.maxLength(11),
    Validators.required,
  ]);
  DtaNasc = new FormControl("", [
    Validators.required,
    Validators.maxLength(11),
  ]);
  tel = new FormControl("", [Validators.required, Validators.maxLength(11)]);
  sus = new FormControl("", [Validators.required, Validators.maxLength(15)]);
  paci = new FormControl("", [Validators.required, Validators.maxLength(11)]);
  medi = new FormControl("", [Validators.required, Validators.maxLength(11)]);

  public mascaraCpf(mascara:any, evento: KeyboardEvent) {
    mascara;
    var valorEvento = (<HTMLInputElement>evento.target).value;
    // var i = valorEvento.length;
    var ao_cpf = valorEvento;
    ao_cpf = ao_cpf.replace(/\D/g, "");
    ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
    return ((<HTMLInputElement>evento.target).value = ao_cpf);
  }

  public validarCod() {

    // var obj = document.getElementById("cod")["value"].toUpperCase();
    var obj = this.PacienteAcesso.CodAcesso;
    ;

    if (obj != "COVID" && this.idGuid == "covid") {
      this.campoCodInvalido = true;
      this.campoCodVazil = false;
    } else if (obj != "ARAUZ" && this.idGuid == "arauz") {
      this.campoCodInvalido = true;
      this.campoCodVazil = false;
    } else if (obj != "APRESENTAÇÃO" && this.idGuid == "apresentacao") {
      this.campoCodInvalido = true;
      this.campoCodVazil = false;
    }
    else if (obj != "PRISMA" && this.idGuid == "prisma") {
      this.campoCodInvalido = true;
      this.campoCodVazil = false;
    }
    else if (obj != "GOBOX" && this.idGuid == "gobox") {
      this.campoCodInvalido = true;
      this.campoCodVazil = false;
    }
    else {
      this.campoCodInvalido = false;
      this.campoCodVazil = false;
    }
  }
  public validarCpf() {
    var value = this.PacienteAcesso.cpf;
    this.campoCPFInvalido = false;
    this.validarCod();
    if (value != "" && value != undefined) {
      this.campoCPFVazil = false;

      if (!this.validacao.cpf(value)) {
        this.campoCPFInvalido = true;
        return;
      }
      if (this.campoCodInvalido == true || this.campoCodVazil == true) return;

      this.usuarioService
        .validar_TELMOVEL_CPF_EMAIL_Acesso_Fila(value, null, "CPF")
        .subscribe(
          (retorno) => {
            ;
            this.mensagemPaciente = "";
            this.cliente = null;
            if (retorno != null) {
              this.campoExitente = "CPF";
              this.cliente = retorno;
              this.ngxSmartModalService.getModal("UsuarioExistente").open();
            } else {
              this.DivEspera = true;
            }
          },
          (err) => {
            console.error(err);
          }
        );
    } else this.campoCPFVazil = true;
  }

  public AceitarUsuarioExistente() {
    if (this.cliente != null) {
      this.PacienteAcesso.cpf = this.cliente.pessoa.cpf;
      this.PacienteAcesso.nome = this.cliente.pessoa.nomePessoa;
      this.PacienteAcesso.email = this.cliente.pessoa.email;
      this.PacienteAcesso.carterinha =
        this.cliente.paciente != null ? this.cliente.paciente.matricula : "";

      if (
        this.cliente.contato.telefoneMovel != null &&
        this.cliente.contato.telefoneMovel != "" &&
        this.cliente.contato.telefoneMovel != undefined
      ) {
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(
          /\D/g,
          ""
        );
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(
          /^(\d{2})(\d)/g,
          "($1) $2"
        );
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(
          /(\d)(\d{4})$/,
          "$1-$2"
        );
      }
      this.PacienteAcesso.telefoneMovel = this.cliente.contato.telefoneMovel;
    }
    this.ngxSmartModalService.getModal("UsuarioExistente").close();
  }

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v;
  }

  public NaoAceitarUsuarioExistente() {
    if (this.campoExitente == "CPF") {
      this.PacienteAcesso.cpf = "";
      this.campoExitente = "";
    } else if (this.campoExitente == "EMAIL") {
      this.PacienteAcesso.email = "";
      this.campoExitente = "";
    } else {
      this.PacienteAcesso.telefoneMovel = "";
      this.campoExitente = "";
    }
    this.ngxSmartModalService.getModal("UsuarioExistente").close();
  }
}
