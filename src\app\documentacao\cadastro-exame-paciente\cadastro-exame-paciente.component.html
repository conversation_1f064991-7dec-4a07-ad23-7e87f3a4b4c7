<mat-card appearance="outlined" class="mother-div div-cadastro-medicamentos" >

  <mat-card-content id="">
    <div class="row">

      <div class="col-md-12 col-xs-12 row" style="margin-left: 0px;" id="UsuarioForm">
        <div class="card col-md-12 col-sm-12 card-medicamentos" style="border-radius: 10px; ">
          <div class="card-body">

            <div class="row   ">
              <div class="col-md-3 col-sm-4 col-12">
                <button mat-stroked-button class=" flag-right" style="float: left; padding: 5px; margin-bottom: 10px;"
                  onclick='history.go(-1)'>
                  <mat-icon style="height: unset!important; margin-right: 9px;">arrow_back</mat-icon>
                  {{ 'TELAMEDICAMENTOS.VOLTAR' | translate }}
                </button>
              </div>

            </div>

            <div class="col-md-12 col-sm-3 col-xs-3 ">
            </div>
            <mat-card appearance="outlined">
              <div class="col-md-12 col-sm-12 col-xs-12 row div-cadast-medc">
                <mat-icon class="novos-medicam">
                  local_pharmacy</mat-icon>
                <h4 class="cadastrar-medicam">
                  Cadastros de Exames
                </h4>
              </div>


              <div class="col-md-12 col-sm-12 row mt-3 info-medic-card" style=" margin-left: 0; margin-right: 0;">
                <mat-form-field class="col-md-6 col-sm-12 col-xs-12" apperance="legacy">
                  <input matInput placeholder="Cod.Exames" name="cod"
                    [(ngModel)]="Dados.cod" maxlength="500" type="text">

                </mat-form-field>
                <mat-form-field class="col-md-6 col-sm-12 col-xs-12" apperance="legacy">
                  <input matInput placeholder="{{ 'TELAMEDICAMENTOS.DESCRICAO' | translate }}" name="descrição" required
                    [(ngModel)]="Dados.des" maxlength="500" type="text">
                  <mat-error *ngIf="Descricao.invalid">{{getErrorMessageDesc() | translate }}
                  </mat-error>
                </mat-form-field>

                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">
                  <button class="btn-primary " mat-raised-button style="color:white;" (click)="LimparCampos()"
                    style="margin-right: 2%;">
                    <mat-icon>clear</mat-icon> <span
                      class="legenda">{{ 'TELACADASTROMEDICO.LIMPAR' | translate }}</span>
                  </button>
                  <button class="btn-primary " mat-raised-button
                    style="color:white;margin-top: 10px;margin-bottom: 10px;" (click)="Submit()">
                    <mat-icon>save</mat-icon><span class="legenda">
                      {{ 'TELACADASTROMEDICO.SALVAR' | translate }}</span>
                  </button>
                </div>
              </div>
            </mat-card>
            <mat-card appearance="outlined" class="m-t-10">
              <div class="col-md-12 col-sm-12 col-xs-12 row div-buscar-legenda">
                <!-- <div class=" col-md-9 col-sm-8 col-xs-9 col-lg-9">
                                  <mat-form-field style="width: 98%; font-size: 17px;">
                                      <input matInput placeholder="{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}" id="inputBusca" name="pesquisa" (keyup)="CarregaMedicamentos()" value="" style="cursor:pointer;" [(ngModel)]="pesquisa">
                                  </mat-form-field>
                              </div> -->


                <div class="col-md-9 col-sm-8 col-xs-9 col-lg-9">
                  <mat-form-field class="col-md-11 col-sm-12 col-xs-12 linha-buscar-medc" style=" font-size: 17px;">
                    <input matInput placeholder="{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}" id="inputBusca"
                      name="pesquisa" (keyup.enter)="CarregaExames()" value="" style="cursor:pointer;"
                      [(ngModel)]="pesquisa">

                    <mat-icon aria-label="Buscar" class="col-md-1 custom-search buscar-icone"
                      (click)="CarregaExames()">search
                    </mat-icon>
                  </mat-form-field>
                </div>

                <div class="col-md-3 col-sm-4 col-xs-3 col-lg-3  coluna" style="height: 33px;">
                  <table style="position: absolute; z-index: 2; width: 99%;color: unset " class="tabela">
                    <thead>
                      <tr class="cor" style="border-bottom: 1px solid #ddd;">
                        <td class="text-center bold value-color" style="padding: 8px!important; cursor: pointer;"
                          (click)="legenda = !legenda">
                          {{ 'TELAAGENDACONTATO.LEGENDA' | translate }}
                          <mat-icon style="float: left;" *ngIf="legenda">expand_less
                          </mat-icon>
                          <mat-icon style="float: left;" *ngIf="!legenda">expand_more
                          </mat-icon>
                        </td>
                      </tr>
                    </thead>
                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                      <tr>
                        <td class=""
                          style=" font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                          <mat-icon style="vertical-align: sub;color: unset">edit</mat-icon>
                          <span style="vertical-align: super;margin-left: 5px;">
                            {{ 'TELAAGENDACONTATO.EDITAR' | translate }}</span>
                        </td>
                      </tr>
                    </tbody>

                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                      <tr>
                        <td class=""
                          style="font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                          <mat-icon style="vertical-align: sub;color: unset">delete</mat-icon>
                          <span
                            style="vertical-align: super;margin-left: 8px;">{{ 'TELAAGENDACONTATO.EXCLUIR' | translate }}</span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <table class="table no-mobile-card" id="DatatableCliente" style="margin-top: 4px; margin-bottom: 10px;">
                <thead style="display: none;">

                </thead>
                <tbody *ngFor="let item of DadosTable">
                  <tr>
                    <th style="width: 40%;">
                      Descrição Exame
                      {{item.exames.desExame}}
                    </th>
                    <th>
                      Cod.Exame
                      {{item.exames.codExame | truncate : 30 : "…"}}
                    </th>
                    <th style="width: 100px; text-align: center;">
                      <button mat-icon-button class="panel_button"
                        (click)="editMedicamento(item.exames.idExameClinica)"
                        title="{{'TELAMEDICAMENTOS.EDITAR' | translate}}">
                        <mat-icon aria-label="Atualizar Cadastro" class="">edit</mat-icon>
                      </button>
                      <button mat-icon-button class="panel_button"
                        (click)="valueRemedio(item.exames.idExameClinica)"
                        title="{{'TELAMEDICAMENTOS.EXCLUIR' | translate}}">
                        <mat-icon aria-label="Excluir Cadastro" class="">delete</mat-icon>
                      </button>
                    </th>
                </tbody>
              </table>
              <mat-card appearance="outlined" *ngFor="let item of DadosTable; let i = index" class="header-card">
                <div class="div_paciente text-center">
                  <h4 class="Title-b " style="font-size: 16px;">
                    {{ 'TELAMEDICAMENTOS.MEDICAMENTO:' | translate }} </h4>
                  <label class="label_paciente">
                    {{item.exames.desExame}}</label>
                  <h4 class="Title-b label_paciente" style="font-size: 16px;">
                    {{ 'TELAMEDICAMENTOS.POSOLOGIA:' | translate }}</h4>
                  <label class="label_paciente">
                    {{item.exames.codExame | truncate : 30 : "…"}}</label>
                </div>

                <!--espaço-->
                <br>


                <div class="grid-buttons">
                  <div [@openClose]="toggle[i] ? 'open': 'closed'" class="botoes-card-medic">
                    <button class="btn-primary buttons-mobilet" id="opcao1" mat-mini-fab
                      (click)="editMedicamento(item.codExame.idExameClinica)"
                      title="{{'TELAMEDICAMENTOS.EDITAR' | translate}}">
                      <mat-icon aria-label="Editar linha selecionada">edit</mat-icon>
                    </button>
                    <button class="btn-primary buttons-mobilet" id="opcao2" mat-mini-fab
                      (click)="valueRemedio(item.codExame.idExameClinica)"
                      title="{{'TELAMEDICAMENTOS.EXCLUIR' | translate}}">
                      <mat-icon aria-label="Deletar Linha selecionada">delete</mat-icon>
                    </button>
                  </div>
                  <button class="btn-primary btn-subir-medicam" mat-mini-fab (click)="openToggle(i)">
                    <mat-icon>keyboard_arrow_up</mat-icon>
                  </button>
                </div>
              </mat-card>

              <div class="col-sm-12 text-center">
                <button mat-flat-button class="btn-primary"
                  *ngIf="(DadosTable != undefined &&  DadosTable.length > 0) && bOcultaCarregaMais == false"
                  (click)="CarregarMais()">{{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}</button>
              </div>

            </mat-card>
          </div>
        </div>
      </div>
    </div>
  </mat-card-content>
</mat-card>




<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-header p-t-20 p-b-20">
      <h1 class="little-title fw-700">
          <mat-icon style="color:red">warning</mat-icon> Deseja relamente excluir esse Exame?
      </h1>
  </div>

  <mat-divider></mat-divider>
  <div class="row-button text-right " style="padding: 10px 0px">
      <button mat-flat-button (click)="excluirItem.close()" class="input-align btn btn-danger">
          {{ 'TELAMEDICAMENTOS.NAO' | translate }} </button>
      <button mat-flat-button (click)="InativarMedicamento()" class="input-align btn btn-success">
          {{ 'TELAMEDICAMENTOS.SIM' | translate }} </button>
  </div>
</ngx-smart-modal>
