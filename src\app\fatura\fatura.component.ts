import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { TipoFatura } from '../Util/EnumFatura';
import { ValidadoreseMascaras } from '../Util/validadores';
import { ConvenioService } from '../service/convenio.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { MedicoService } from '../service/medico.service';
import { FaturaModelview } from '../model/fatura';
import { FaturaService } from '../service/fatura.service';
import { ToggleComponent } from '../Util/toggle/toggle.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { parseDateString } from '../Util/ValidadorDataPorPeriodo';

@Component({
  selector: 'app-fatura',
  templateUrl: './fatura.component.html',
  styleUrls: ['./fatura.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    ToggleComponent,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDatepickerModule
  ]
})
export class FaturaComponent implements OnInit {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private medicoService: MedicoService,
    private faturaService: FaturaService,
    private router: Router
  ) {
    this.objFatura.dtaCadastro = new Date(),
      this.idClinica = this.usuarioLogadoService.getIdUltimaClinica()!
  }

  ListaTiposFatura = TipoFatura;
  listaConvenios: any = [];
  ListaMedicos: any = [];

  objFatura = new FaturaModelview

  tipoSelecionado = 1;
  idClinica: number;

  ngOnInit() {
    this.spinner.show();
    this.GetListaConvenios();
    this.GetListaMedicos();
  }

  async GetListaConvenios() {
    this.spinner.show();
    await this.convenioService.getConvenios(this.idClinica).subscribe((ret) => {
      this.spinner.hide();
      this.listaConvenios = ret;
    }, () => {
      this.spinner.hide();
    })
  }

  async GetListaMedicos() {
    this.spinner.show();
    await this.medicoService.getMedicos(0, this.idClinica).subscribe((ret) => {
      this.ListaMedicos = ret
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  async SalvarFatura() {

    this.spinner.show();
    
    this.objFatura.dtaAbertura = this.dtAbertura;
    this.objFatura.dtaFechamento = this.dtFechamento;
    this.objFatura.dtaPrevisaoPagamento = this.dtPrevisaoPagamento;

    await this.faturaService.SalvarFatura(this.objFatura).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar('Fatura salva com sucesso.')
      this.router.navigate(['/lista-faturas']);
      this.spinner.hide();
    }, () => {
      this.snackBarAlert.falhaSnackbar('Ocurreu um erro tentando salvar a fatura.')
      this.spinner.hide();
    })
  }

  strDtAbertura = "";
  dtAbertura = new Date();
  strDtFechamento = "";
  dtFechamento = new Date();
  strDtPrevisaoPagamento = "";
  dtPrevisaoPagamento = new Date();
  
  //#region nova data
  dtAberturaChange() {
    this.dtAbertura = parseDateString(this.strDtAbertura)!;
  }

  dtFechamentoChange() {
    this.dtFechamento = parseDateString(this.strDtFechamento)!;
  }

  dtPrevisaoPagamentoChange(){
    this.dtPrevisaoPagamento = parseDateString(this.strDtPrevisaoPagamento)!;
  }
  //#endregion
}
