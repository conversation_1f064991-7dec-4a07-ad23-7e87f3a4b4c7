// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}
.form-container {
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Cards
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: none;
  margin-bottom: 24px;
  overflow: hidden;
  border-top: 4px solid $primary-color !important;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
  max-height: 67vh;
  overflow: auto;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
}

// Headers
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

// Sections
.section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
}

// Profile Section
.profile-section {
  display: flex;
  align-items: center;
  gap: 20px;  
  flex-direction: column;
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.profile-image-wrapper {
  position: relative;
  margin-right: 24px;
  
  @media (max-width: 768px) {
    margin-right: 0;
    margin-bottom: 16px;
  }
}

.profile-image-label {
  display: block;
  position: relative;
  cursor: pointer;
}

.profile-img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid $primary-light;
  transition: $transition;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: $transition;
  
  .material-icons {
    color: white;
    font-size: 28px;
    opacity: 0.9;
  }
}

.profile-image-label:hover {
  .profile-img {
    border-color: $primary-color;
    transform: scale(1.03);
  }
  
  .profile-image-overlay {
    opacity: 1;
  }
}

input[type="file"] {
  display: none;
}

// Form
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -12px;
  margin-right: -12px;
  margin-bottom: 12px;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 12px;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 12px;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 12px;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.full-width {
  width: 100%;
}

// Error message
.error-message {
  color: $error-color;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  border-radius: 20px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary !important;
  
  &:hover {
    color: $primary-color;
  }
}

.btn-success {
  background-color: #0ad07c !important;
  color: white;
  padding: 10px 24px;
  border-radius: 5px !important;
  
  &:hover {
    background-color: darken($primary-color, 5%);
    transform: scale(1.05);
  }
}

.btn-link {
  background: none;
  color: $primary-color !important;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

.control-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: $primary-light;
  color: $primary-dark;
  border: none;
  border-radius: 20px;
  transition: $transition;
  
  &:hover {
    background-color: darken($primary-light, 5%);
    transform: translateY(-1px);
  }
  
  .material-icons {
    margin-right: 8px;
    font-size: 16px;
  }
}

// Modal styles
.modal-content {
  padding: 0;
  border-radius: $border-radius;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background: linear-gradient(135deg, $primary-color, darken($primary-color, 10%));
  
  .modal-title {
    color: white;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
  }
}

.modal-body {
  padding: 24px;
}

.image-controls {
  margin-bottom: 24px;
  
  .control-group {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 12px;
  }
}

.cropper-container {
  max-width: 300px;
  margin: 0 auto;
  background-color: rgba($primary-light, 0.2);
  padding: 16px;
  border-radius: 12px;
  border: 2px dashed $primary-color;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: center;
}

// Custom select styling
.modern-select {
  ::ng-deep {
    .ng-select-container {
      border-radius: 4px;
      border-color: $border-color;
      min-height: 52px;
      
      &:hover {
        border-color: $primary-light;
      }
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
    }
    
    .ng-option {
      padding: 10px 16px;
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .card-body {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .btn-success {
    width: 100%;
  }
}

// Material Form Field customization
::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba($primary-color, 0.2);
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-label {
    margin-top: -0.25em;
    color: $accent-color;
  }
  
  .mat-form-field-label.mat-focused {
    color: $primary-color !important;
  }
  
  .mat-form-field-subscript-wrapper {
    margin-top: 0.5em;
  }
  
  .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
    transform: translateY(0);
  }
  
  .mat-select-panel {
    border-radius: 8px !important;
  }
  
  .ng-select .ng-select-container {
    border-radius: 8px;
    border-color: rgba($primary-color, 0.2);
    min-height: 54px;
    transition: $transition;
    background-color: rgba(255, 255, 255, 0.7);
  }
  
  .ng-select.ng-select-focused .ng-select-container {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
  
  .ng-dropdown-panel {
    margin-top: 14px;
    box-shadow: $box-shadow !important;
  }
  
  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked {
    background-color: $primary-light;
    color: $primary-color;
    font-weight: 600;
  }
  
  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
    background-color: lighten($primary-light, 3%);
    color: $primary-color;
  }
  
  .mat-form-field-infix {
    padding: 0.75em 0 0.75em 0;
  }
  
  // Colorização de erro
  .mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline-thick {
    color: $error-color !important;
  }
  
  .mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-label {
    color: $error-color !important;
  }
  
  // Hover estados de input
  .mat-form-field-appearance-outline:hover:not(.mat-form-field-disabled):not(.mat-focused) .mat-form-field-outline {
    opacity: 1;
    color: rgba($primary-color, 0.5);
  }
  
  // Formulário disabled
  .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline {
    color: rgba(0, 0, 0, 0.1);
  }
  
  .ng-dropdown-panel .scroll-host {
    height: 140px;
  }
  
  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper {
    background-color: #fff;
  }
  
  .mdc-text-field--outlined .mdc-floating-label {
    font-size: 13px !important;
  }
  
  .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label, 
  .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above {
    color: $primary-dark;
  }
}