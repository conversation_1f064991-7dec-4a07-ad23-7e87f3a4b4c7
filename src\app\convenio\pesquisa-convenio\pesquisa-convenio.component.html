<div class="container mother-div">
    <!-- MODAL DE CONFIRMAÇÃO INATIVAR -->
    <div *ngIf="excluirItem.visible" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Confirmação</h3>
        </div>
        <div class="modal-body">
          <p class="modal-text">Você tem certeza que deseja inativar este convênio?</p>
        </div>
        <div class="modal-footer">
          <button class="btn-cancelar" (click)="excluirItem.close()">
            {{ 'TELAPESQUISACLINICA.NAO' | translate }}
          </button>
          <button class="btn-confirmar btn-excluir" (click)="InativarUsuario()">
            {{ 'TELAPESQUISACLINICA.SIM' | translate }}
          </button>
        </div>
      </div>
    </div>
  
    <!-- MODAL DE CONFIRMAÇÃO ATIVAR -->
    <div *ngIf="ativarItem.visible" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Confirmação</h3>
        </div>
        <div class="modal-body">
          <p class="modal-text">Você tem certeza que deseja ativar este convênio?</p>
          <p class="modal-subtexto">O convênio ficará ativo no sistema</p>
        </div>
        <div class="modal-footer">
          <button class="btn-cancelar" (click)="ativarItem.close()">
            {{ 'TELAPESQUISACLINICA.NAO' | translate }}
          </button>
          <button class="btn-confirmar" (click)="AtivarUsuario()">
            {{ 'TELAPESQUISACLINICA.SIM' | translate }}
          </button>
        </div>
      </div>
    </div>
  
    <mat-card class="card-principal">
      <!-- CABEÇALHO -->
      <div class="header">
        <div class="header-icon">
          <span class="material-icons">domain</span>
        </div>
        <h2 class="header-title">{{ 'TELAPESQUISACLINICA.CONVENIO' | translate }}</h2>
      </div>
  
      <!-- FILTROS -->
      <div class="filtros">
               
        <div class="busca-container">
          <mat-form-field appearance="outline" class="busca-field">
            <mat-label>{{ 'TELAPESQUISACLINICA.BUSCAR' | translate }}</mat-label>
            <input matInput type="search" [(ngModel)]='pesquisa' (keyup.enter)="CarregaTable()">
            <button mat-icon-button matSuffix class="btn-busca" (click)="CarregaTable()">
              <mat-icon>search</mat-icon>
            </button>
          </mat-form-field>
        </div>
        
        <div class="adicionar-container">
          <button class="btn-adicionar" [routerLink]="['/convenio']">
            <mat-icon>add</mat-icon>
            <span>{{ 'TELAPESQUISACLINICA.ADICIONARCONVENIO' | translate }}</span>
          </button>
        </div>
      </div>
      <div class="toggles-container">
        <mat-slide-toggle [(ngModel)]='inativos' (change)="CarregaTable()" class="toggle-item">
          {{ 'TELAPESQUISACLINICA.INATIVOS' | translate }}
        </mat-slide-toggle>
      </div>
      <!-- LISTA DE CONVÊNIOS - DESKTOP VIEW -->
      <div class="lista-container desktop-view">
        <div class="lista-scroll">
          <div class="convenio-card" *ngFor="let item of DadosTab">
            <!-- INFO DO CONVÊNIO -->
            <div class="convenio-info">
              <div class="info-item">
                <mat-icon>domain</mat-icon>
                <span class="nome">{{item.nomeConvenio}}</span>
              </div>
              <div class="info-item">
                <mat-icon>phone</mat-icon>
                <span>{{item.tel}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span>{{item.email}}</span>
              </div>
            </div>
  
            <!-- DADOS ADICIONAIS -->
            <div class="convenio-dados">
              <div class="dados-item">
                <label class="dados-label">{{ 'TELAPESQUISACLINICA.DATADECADASTRO' | translate }}</label>
                <span class="dados-valor">{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
              </div>
              <div class="dados-item">
                <label class="dados-label">{{ 'TELAPESQUISACLINICA.CNPJ:' | translate }}</label>
                <span class="dados-valor">{{item.cnpj}}</span>
              </div>
            </div>
  
            <!-- AÇÕES -->
            <div class="convenio-acoes">
              <button mat-icon-button matTooltip="{{ 'TELAPESQUISACLINICA.EDITARCLINICA' | translate }}" (click)="editClinica(item.idconvenio)">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button class="remove" *ngIf="!inativos" matTooltip="Inativar" (click)="ValorUsuario(item.idconvenio)">
                <mat-icon>delete</mat-icon>
              </button>
              <button mat-icon-button *ngIf="inativos" matTooltip="{{ 'TELAPESQUISACLINICA.ATIVARCADASTRO' | translate }}" (click)="ValorUsuarioAtivar(item.idconvenio)">
                <mat-icon>check</mat-icon>
              </button>
            </div>
          </div>
          
          <!-- MENSAGEM DE LISTA VAZIA -->
          <div class="lista-vazia" *ngIf="DadosTab?.length === 0">
            <mat-icon>sentiment_very_dissatisfied</mat-icon>
            <p>Nenhum convênio encontrado</p>
          </div>
        </div>
      </div>         
        <mat-card appearance="outlined" class="header-card no-desktop" *ngFor="let item of DadosTab; let i = index">

            <div class="div_paciente text-center">
               
                <h4 class="Title-b" style="font-size: 20px; font-weight: 400;"> {{item.nomeConvenio}}</h4>
                <small style="font-size: 14px;margin-left: -5px;vertical-align: inherit!important;">{{ 'TELAPESQUISACLINICA.CONVENIO2' | translate }}</small><br>

                <h4 class="Title-b" style="font-size: 18px; font-weight: 400;"><mat-icon class="material-icons" style="font-size: 17px;color: #0983ff;vertical-align: bottom; padding-top: 3px;">phone
                </mat-icon>{{ 'TELAPESQUISACLINICA.TELEFONE' | translate }}</h4>
                <small style="font-size: 14px;vertical-align: text-bottom;">{{item.tel}}</small><br>

                <h4 class="Title-b" style="font-size: 18px; font-weight: 400;"><mat-icon class="material-icons" style="font-size: 17px;color: #0983ff;vertical-align: bottom; padding-top: 4px;">mail
                </mat-icon>{{ 'TELAPESQUISACLINICA.EMAIL' | translate }}</h4>
                <small class="label_paciente" style="font-size: 14px;vertical-align: super;"> {{item.email}}</small>

                <h4 class="Title-b" style="font-size: 18px; font-weight: 400;">{{ 'TELAPESQUISACLINICA.DATADECADASTRO' | translate }}</h4>
                <label class="label_paciente"> {{item.dtaCadastro  | date: 'dd/MM/yyyy'}} </label><br>

                <h4 class="Title-b" style="font-size: 18px; font-weight: 400;">{{ 'TELAPESQUISACLINICA.CNPJ' | translate }}</h4>
                <label class="label_paciente"> {{item.cnpj}} </label><br>
            </div>


            <div class="grid-buttons" style="padding: unset">
                <div class="btns-grid-subir" (clickOutside)="toggle[i] ? fecharBotoesMobile() : null" [@openClose]="toggle[i] ? 'open' : 'closed'">
                    <button class="btn-primary buttons-mobilet" id="opcao7" mat-mini-fab
                        (click)="editClinica(item.idconvenio)" *ngIf="!inativos">
                        <mat-icon aria-label="Editar linha selecionada">edit</mat-icon>
                    </button>
                    <button class="btn-primary buttons-mobilet" id="opcao8" mat-mini-fab
                        (click)="ValorUsuario(item.idconvenio)" *ngIf="!inativos">
                        <mat-icon aria-label="Inativar Linha selecionada">delete</mat-icon>
                    </button>
                    <button class="btn-primary buttons-mobilet" id="opcao8" mat-mini-fab
                        (click)="ValorUsuarioAtivar(item.idconvenio)" *ngIf="inativos">
                        <mat-icon aria-label="Ativar Linha selecionada">check</mat-icon>
                    </button>
                </div>
                <button class="btn-primary button-subir-conv" mat-mini-fab (click)="openToggle(i)">
                    <mat-icon>keyboard_arrow_up</mat-icon>
                </button>
            </div>

        </mat-card>

        <div class="col-sm-12 text-center" style="margin-top: 60px;">
            <button mat-flat-button class="btn-primary"
                *ngIf="(DadosTab != undefined &&  DadosTab.length > 0) && bOcultaCarregaMais == false"
                (click)="CarregarMais()">{{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}</button>
        </div>
  
      
    </mat-card>
  </div>
  
  <!-- MODAIS ORIGINAIS (ESCONDIDOS) -->
  <ngx-smart-modal #excluirItem identifier="excluirItem" customClass="hidden-modal"></ngx-smart-modal>
  <ngx-smart-modal #ativarItem identifier="ativarItem" customClass="hidden-modal"></ngx-smart-modal>