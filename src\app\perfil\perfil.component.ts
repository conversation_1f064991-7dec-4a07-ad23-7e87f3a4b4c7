import { ControleModaisService } from './../service/controle-modais.service';
import { Component, OnInit } from '@angular/core';
import { UsuarioService } from '../service/usuario.service';
import { AlertComponent } from './../alert/alert.component';
import { TranslateModule } from '@ngx-translate/core';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from '../service/spinner.service';
import { Usuario } from '../model/usuario';
import { Pessoa } from '../model/pessoa';
import { Contato } from '../model/contato';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { CalendarDateFormatter } from 'angular-calendar';
import { MatIconModule } from '@angular/material/icon';
import { apenasNumeros, aplicarMascaraTelefone } from '../Util/uteis';

@Component({
  selector: 'app-perfil',
  templateUrl: './perfil.component.html',
  styleUrls: ['./perfil.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    TranslateModule,
    MatIconModule
  ],
  providers: [
    CalendarDateFormatter
  ]
})
export class PerfilComponent implements OnInit {

  ImagemPessoa: string = "assets/build/img/userdefault.png";
  DadosPerfil: any;
  idOutroPerfil: any = null;

  nome: string = '';
  email: string = '';
  telefone: string = '';
  telefoneMovel: string = '';
  telefoneComercial: string = '';
  senhaAtual: string = '';
  novaSenha: string = '';

  flgNumerosOuSimbolosNome = false;
  flgTelefoneInvalido = false;
  flgTelefoneMovelInvalido = false;
  flgSenhaInvalida = false;
  flgTelefoneComercialInvalido = false;
  flgSenhaAlterada = false;
  showMessageError = false;

  showSenhaAtual = false;
  showNovaSenha = false;

  constructor(
    private spinner: SpinnerService,
    private usuarioService: UsuarioService,
    private snackBarAlert: AlertComponent,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    private controleModais: ControleModaisService,
  ) { }

  ngOnInit(): void {
    this.idOutroPerfil = this.localStorageService.idPerfil;
    this.localStorageService.clearByName("idPerfil");

    if (this.idOutroPerfil) {
      this.CarregarPerfil(this.idOutroPerfil);
    } else {
      this.CarregarPerfil(this.usuarioLogadoService.getIdUsuarioAcesso()!);
    }
  }

  CarregarPerfil(id: number): void {
    this.usuarioService.CarregarPerfil(id).subscribe((retorno) => {
      if (retorno) {
        this.DadosPerfil = retorno;
        this.nome = retorno.nome;
        this.email = retorno.email;
        this.telefone = aplicarMascaraTelefone(retorno.telefone);
        this.telefoneMovel = aplicarMascaraTelefone(retorno.telefoneMovel);
        this.telefoneComercial = aplicarMascaraTelefone(retorno.telefoneComercial);
        if (retorno.imagem64) {
          this.ImagemPessoa = retorno.imagem64;
        }
      }
      this.spinner.hide();
    }, err => {
      console.error(err);
      this.spinner.hide();
    });
  }

  async openModalUploadFotos(): Promise<void> {
    const imagemRetornada = await this.controleModais.ModalUploadFoto(this.ImagemPessoa);

    if (imagemRetornada) {
      this.ImagemPessoa = imagemRetornada;
    }
  }

  validarCampos(): boolean {
    this.showMessageError = false;

    if (!this.nome || this.nome.length > 50) {
      this.snackBarAlert.falhaSnackbar('O campo nome é obrigatório e deve ter no máximo 50 caracteres.');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!this.email || !emailRegex.test(this.email)) {
      this.snackBarAlert.falhaSnackbar('Informe um e-mail válido.');
      return false;
    }

    if (this.telefone && (apenasNumeros(this.telefone).length < 10 || apenasNumeros(this.telefone).length > 11)) {
      this.snackBarAlert.falhaSnackbar('Informe um telefone válido.');
      return false;
    }

    if (!this.telefoneMovel || (apenasNumeros(this.telefoneMovel).length < 10 || apenasNumeros(this.telefoneMovel).length > 11)) {
      this.snackBarAlert.falhaSnackbar('Telefone móvel é obrigatório e deve ser válido.');
      return false;
    }

    ;
    if (this.telefoneComercial && (apenasNumeros(this.telefoneComercial).length < 10 || apenasNumeros(this.telefoneComercial).length > 11)) {
      this.snackBarAlert.falhaSnackbar('Informe um telefone comercial válido.');
      return false;
    }

    if (this.novaSenha) {
      if (!this.senhaAtual || this.senhaAtual !== this.DadosPerfil.senha) {
        this.snackBarAlert.falhaSnackbar('Para alterar a senha, a senha atual deve ser informada corretamente.');
        return false;
      }
    }

    return true;
  }

  Salvar(): void {
    if (!this.validarCampos()) {
      return;
    }

    const pessoa = new Pessoa();
    const alteracaoPerfilUsuario = new Usuario();
    const contato = new Contato();

    pessoa.idUsuarioAcesso = this.idOutroPerfil || this.usuarioLogadoService.getIdUsuarioAcesso();
    pessoa.idPessoa = this.DadosPerfil.idPessoa;
    pessoa.nomePessoa = this.nome;
    pessoa.email = this.email;

    contato.telefone = apenasNumeros(this.telefone);
    contato.telefoneMovel = apenasNumeros(this.telefoneMovel);
    contato.telefoneComercial = apenasNumeros(this.telefoneComercial);

    pessoa.contato = contato;
    pessoa.idContato = this.DadosPerfil.idContato;

    if (this.ImagemPessoa !== "assets/build/img/userdefault.png") {
      pessoa.foto = this.ImagemPessoa;
      alteracaoPerfilUsuario.imagem64 = this.ImagemPessoa;
    }

    if (this.novaSenha) {
      alteracaoPerfilUsuario.senha = this.novaSenha;
    }

    alteracaoPerfilUsuario.idUsuario = this.DadosPerfil.idUsuario;
    alteracaoPerfilUsuario.pessoa = pessoa;

    this.usuarioService.AtualizarPerfil(alteracaoPerfilUsuario).then(() => {
      this.snackBarAlert.sucessoSnackbar("Alteração realizada com sucesso.");
      this.CarregarPerfil(this.idOutroPerfil || this.usuarioLogadoService.getIdUsuarioAcesso());
    }, err => {
      console.error('Erro ao salvar perfil:', err);
      this.snackBarAlert.falhaSnackbar("Erro ao contatar o sistema, por favor tente mais tarde.");
    });
  }

  TelVal!: boolean;
  TelMovValVasil = false;
  TelMovLimpa = false;
  TelMovVal!: boolean;
  TelComVal!: boolean;
  idDelet!: number | null;

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }


  ValidaTelefone(tle: any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle: any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {

      this.TelMovValVasil = true;
      this.TelMovVal = false;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovValVasil = false;
      this.TelMovVal = false;
      this.TelMovLimpa = true;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
      this.TelMovValVasil = false;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneComercial(tle: any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
    }
    else
      this.TelComVal = false
  }
}
