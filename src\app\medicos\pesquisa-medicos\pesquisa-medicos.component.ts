import { AlertComponent } from './../../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { Router, RouterModule } from '@angular/router';

import { MedicoService } from 'src/app/service/medico.service';
import { EnvioEmailService } from 'src/app/service/envioEmail.service';
import { ConsultaService } from 'src/app/service/consulta.service';
import { FilaEsperaMedicoService } from 'src/app/service/fila-espera-medico.service';
import { SwiperConfigInterface } from 'ngx-swiper-wrapper';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// import { FadeIn } from 'src/app/Util/Fadein.animation';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { LifeLineService } from 'src/app/lifeLine/lifeline.service';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { Consulta } from 'src/app/model/consulta';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';


@Component({
  selector: 'app-pesquisa-medicos',
  templateUrl: './pesquisa-medicos.component.html',
  styleUrls: ['./pesquisa-medicos.component.scss'],
  animations: [trigger('openClose', [
    state('open', style({
      opacity: '1',
      display: 'block'
    })),
    state('closed', style({
      opacity: '0',
      display: 'none'
    })),
    transition('open => closed', [
      animate('0.2s')
    ]),
    transition('closed => open', [
      animate('0.2s')
    ]),
  ])
  ],
  standalone: true,
  imports: [
    MatInputModule,
    TruncatePipe,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    TranslateModule,
    MatFormFieldModule,
    MatIcon,
    RouterModule,
    NgxSmartModalModule,
    MatDividerModule,
    MatTooltipModule,
    MatSlideToggleModule
  ]
})
export class PesquisaMedicosComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private medicoService: MedicoService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private router: Router,
    private emailService: EnvioEmailService,
    private consultaService: ConsultaService,
    private tradutor: TranslateService,
    private lifeLineService: LifeLineService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    private filaEsperaMedicoService: FilaEsperaMedicoService

  ) { }
  // @Output() FadeIn: string;
  // isOpen = false;
  nomeMedico: string = ''
  idMedico: number = 0;
  dadosLifeLine: any;
  bOcultaCarregaMais = false;
  qtdRegistros = 10;
  DadosUsuario: any;
  idUsuarioDelet: any;
  idUsuario = 0;
  Delete: any;
  // name = 'SnackBarConfirmação';
  // legenda = false;
  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔';
  // emailenviado: string = 'Email Enviado. ✔';
  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  // sendemail: string = 'E-mail enviado com sucesso.  ✔'
  email?: boolean;
  ImagemPessoa: any = "assets/build/img/avatar-medico.png";
  flgEmail: boolean = false;
  DadosConsultaLifeLine: any
  dados = false;
  dadosAnonimo?: string;
  MedicoAnonimo?: string;
  Dataanonimo?: string;


  DadosTab: any = [];
  pacientesFila: any[] = [];
  idEmail = 0;
  pesquisa: string = ""
  Foto: boolean = false;
  inativos: boolean = false;
  mostrarFiltros: boolean = false;
  carregandoPacientes: boolean = false;
  // usuario: Usuario;

  ngOnInit() {
    this.CarregaTable();
    this.carregarPacientesFila();
  }

  toggleFiltros() {
    this.mostrarFiltros = !this.mostrarFiltros;
  }

  get medicosAtivos() {
    return this.DadosTab ? this.DadosTab.filter((m: any) => !m.flgInativo).length : 0;
  }

  carregarPacientesFila() {
    this.carregandoPacientes = true;
    this.filaEsperaMedicoService.listarPacientesNaFila().subscribe({
      next: (response) => {
        if (response.Sucesso) {
          this.pacientesFila = response.Pacientes || [];
          console.log('Pacientes na fila carregados:', this.pacientesFila);
        } else {
          this.snackBarAlert.falhaSnackbar("Erro ao carregar pacientes da fila");
        }
        this.carregandoPacientes = false;
      },
      error: (error) => {
        console.error('Erro ao carregar pacientes da fila:', error);
        this.snackBarAlert.falhaSnackbar("Erro ao conectar com a API de pacientes");
        this.carregandoPacientes = false;
      }
    });
  }

  toggle: any = {}

  CarregaTable() {
    try {
      this.bOcultaCarregaMais = false
      if (this.inativos == true) {
        this.medicoService.getGridMedicoInativos(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          this.DadosTab = retorno



          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Ops... Parece que algo deu errado. Tente novamente.");
          this.spinner.hide();
        })
      }
      else {
        this.medicoService.getGridMedico(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          this.DadosTab = retorno


          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Ops... Parece que algo deu errado. Tente novamente.");
          this.spinner.hide();
        })
      }
    } catch (error) {

      this.snackBarAlert.falhaSnackbar("Falha na conexão!");

    }
  }


  ValorMedico(value: any, nome: any) {
    this.idMedico = 0;
    this.nomeMedico = '';
    this.idMedico = value;
    this.nomeMedico = nome;
    this.ngxSmartModalService.getModal('CriaAgendamento').open();
  }


  AgendarConsulta() {
    if (this.idMedico > 0) {
      var agenda = new Consulta();
      agenda.IdMedico = this.idMedico
      this.localStorageService.ConsultaAgenda = agenda;
      this.ngxSmartModalService.getModal('CriaAgendamento').close();
      this.router.navigate(['/calendario']);

    }

  }


  CarregarMais() {
    try {
      if (this.inativos == true) {
        this.bOcultaCarregaMais = false;
        this.medicoService.getGridMedicoInativos(this.DadosTab.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          var dados = retorno;
          for (let index = 0; index < dados.length; index++) {
            this.DadosTab.push(dados[index]);
          }
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Ops... Parece que algo deu errado. Tente novamente.");
          this.spinner.hide();
        })
      }
      else {
        this.bOcultaCarregaMais = false;
        this.medicoService.getGridMedico(this.DadosTab.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          var dados = retorno;
          for (let index = 0; index < dados.length; index++) {
            this.DadosTab.push(dados[index]);
          }
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Ops... Parece que algo deu errado. Tente novamente.");
          this.spinner.hide();
        })
      }
    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão!");

    }
  }

  AtivarUsuario() {
    try {
      if (this.idMedico > 0)
        this.idUsuarioDelet = this.idMedico
      this.medicoService.AtivarMedico(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        this.Delete = retorno;
        this.idMedico = 0
        if (this.Delete == true) {
          this.ngxSmartModalService.getModal('ativarItem').close();

          this.tradutor.get('TELAPESQUISAMEDICO.USUARIOATIVOCOMSUCESSO').subscribe((res: string) => {
            ;
            this.snackBarAlert.sucessoSnackbar(res);
          });
          this.CarregaTable();
        }
        else {


          this.tradutor.get('TELAPESQUISAMEDICO.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
            ;
            this.snackBarAlert.falhaSnackbar(res);
          });
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
        this.tradutor.get('TELAPESQUISAMEDICO.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
          this.snackBarAlert.falhaSnackbar(res);
        });
      })

    } catch (error) {


      this.tradutor.get('TELAPESQUISAMEDICO.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
        ;
        this.snackBarAlert.falhaSnackbar(res);
      });
    }
  }

  InativarUsuario() {
    try {
      this.medicoService.inativarMedico(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        this.Delete = retorno;
        if (this.Delete == true) {
          var res = "Usuário inativo com sucesso!"
          this.ngxSmartModalService.getModal('excluirItem').close();
          this.snackBarAlert.sucessoSnackbar(res);
          this.CarregaTable();
          // this.ConcordoSnack(this.Delete);
        }
        else {


          this.tradutor.get('TELAPESQUISAMEDICO.ERROAOINATIVAR').subscribe((res: string) => {
            ;
            this.snackBarAlert.falhaSnackbar(res);
          });
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    } catch (error) {

      this.tradutor.get('TELAPESQUISAMEDICO.ERROAOINATIVAR').subscribe((res: string) => {
        ;
        this.snackBarAlert.falhaSnackbar(res);
      });
      this.spinner.hide();
    }

  }
  public ValorUsuarioAtivar(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('ativarItem').open();
  }

  public ValorUsuario(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }


  // ConcordoSnack(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     this.snackBar.open(this.message, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  //   this.concordo = value;
  //   this.concordomsg = false;
  // }


  public editUsuario(id: any) {
    if (id != "" && id != 0) {
      this.localStorageService.idMedico = id;
      this.router.navigate(['/medico']);
    }
  }

  lifeline(id: any) {
    var usuarioLifeline = {
      "tipoUsuario": EnumTipoUsuario.Médico,
      "idUsuario": id,
      // "qntLifeline": this.QntLifeLine,
    }
    this.lifeLineService.setModalLifeline(usuarioLifeline)





    //   this.consultaService.GetLifeLine('Médico', id).subscribe((retorno) => {
    //     

    //     this.ngxSmartModalService.getModal('LifeLine').open()
    //     this.dadosLifeLine = retorno
    //     this.DadosConsultaLifeLine = []
    //     this.dados = false
    //   }, err => {
    //     
    //   })
  }


  DadosPaciente(id: any) {
    this.dados = false
    this.DadosConsultaLifeLine = []
    this.consultaService.GetDadosLifeLine(id).subscribe((retorno) => {
      this.DadosConsultaLifeLine = retorno
      if (this.DadosConsultaLifeLine.anonimo != null) {
        this.dadosAnonimo = this.DadosConsultaLifeLine.anonimo
        this.MedicoAnonimo = this.DadosConsultaLifeLine.medico
        this.Dataanonimo = this.DadosConsultaLifeLine.dtaConsulta

      }
      this.dados = true

    })
  }

  mandaEmail() {
    if (this.idEmail != 0) {
      this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe((retorno) => {
        retorno;
        this.idUsuarioDelet = this.idEmail
        this.snackBarAlert.sucessoSnackbar("Email enviado com sucesso!");
        this.CarregaTable();
        this.ngxSmartModalService.getModal('emailUsuario').close();
        this.idEmail = 0
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }
  }

  mandaEmailAtivarUser() {
    if (this.idEmail != 0) {
      this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe((retorno) => {
        retorno;
        // this.idUsuarioDelet = this.idEmail
        if (this.idMedico > 0)
          this.idUsuarioDelet = this.idMedico

        this.medicoService.AtivarMedico(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
          this.Delete = retorno;
          this.idMedico = 0
          if (this.Delete == true) {
            this.snackBarAlert.sucessoSnackbar("Email enviado e usuário ativado com sucesso!");
            // this.AtivarUsuario()
            // this.EnviarEmailSnack(true);

            this.ngxSmartModalService.getModal('emailUsuario').close();
            this.idEmail = 0
            this.CarregaTable();
          }
          this.spinner.hide();
        })
        this.spinner.hide();


      }, () => {
        this.spinner.hide();
      })
    }
  }
  ModalEmail(id: any, idmedico: any) {
    this.idEmail = 0
    this.idEmail = id
    this.idMedico = 0
    this.idMedico = idmedico
    this.ngxSmartModalService.getModal('emailUsuario').open();
  }



  // EnviarEmailSnack(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAPESQUISAMEDICO.EMAILENVIADOV').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  //   this.flgEmail = false;
  // }


  // ErroCarregar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELAPESQUISAMEDICO.ERROAOCARREGAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }


  public config: SwiperConfigInterface = {
    direction: 'horizontal',
    keyboard: true,
    loop: false,
    slidesPerView: 5,
    mousewheel: true,
    scrollbar: false,
    navigation: false,
    pagination: true
  };

  PerfilMedico(obj: any) {
    if (obj != "" && obj != 0) {
      this.localStorageService.idPerfil = obj.idUsuarioacesso;
      this.router.navigate(['/perfil']);
    }

  }

  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTab[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.DadosTab[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index: any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.DadosTab[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTab[this.indexGlobal]['toggle'] = !this.DadosTab[this.indexGlobal]['toggle'];
  }


}
