<div class="analysis-container">
  <!-- Modal para visualizar PDF -->
  <ngx-smart-modal #arquivo identifier="arquivo" customClass="nsm-centered medium-modal emailmodal">
    <div class="pdf-container">
      <pdf-viewer [src]="urlPDF" [render-text]="true" [rotation]="0" [original-size]="false" [show-all]="true"
        [fit-to-page]="false" [zoom]="1" [zoom-scale]="'page-width'" [stick-to-page]="false" [render-text]="true"
        [external-link-target]="'blank'" [autoresize]="true" [show-borders]="false"
        style="display: block;"></pdf-viewer>
    </div>
    <div class="modal-footer">
      <button class="btn btn-primary" (click)="BaixarArquivo()">
        <span class="material-icons">download</span>
        Baixar Arquivo
      </button>
    </div>
  </ngx-smart-modal>

  <!-- Modal para visualizar formulário -->
  <ngx-smart-modal #formularioViewer identifier="formularioViewer" customClass="nsm-centered medium-modal emailmodal">
    <div class="form-viewer-container">
      <div class="form-viewer-content">
        <h2 class="form-viewer-title">{{formularioSelecionadoModal.nomeFormulario}}</h2>
        <div class="form-messages-container">
          <div class="form-qa-item" *ngFor="let dados of listaPerguntas">
            <div class="form-question">
              <span class="question-label">Pergunta:</span>
              <p>{{dados.pergunta}}</p>
            </div>
            <div class="form-answer" *ngIf="dados.flgRespondido">
              <span class="answer-label">Resposta:</span>
              <p>{{dados.resposta}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ngx-smart-modal>

  <!-- Modal Chat Interno -->
  <ngx-smart-modal #chatInternoModal identifier="chatInternoModal" customClass="medium-modal emailmodal">
    <div class="chat-modal-container">
      <div class="chat-modal-header">
        <h2 class="chat-modal-title">
          <span class="material-icons">chat</span>
          Chat Interno
        </h2>
      </div>

      <div class="chat-messages-container">
        <div class="chat-message system" *ngIf="listaMensagens.length < 1">
          <div class="message-content">
            <p class="message-sender">Sistema</p>
            <p class="message-text">Não foi enviada nenhuma mensagem no chat ainda</p>
            <p class="message-time">agora</p>
          </div>
        </div>

        <div class="chat-message-wrapper" *ngFor="let msg of listaMensagens; let even = even"
          [ngClass]="even ? 'message-left' : 'message-right'">
          <div class="chat-message" [ngClass]="msg.flgMensagemImportante ? 'message-important' : 'message-normal'">
            <div class="message-content">
              <p class="message-sender">{{msg.nomeMedico}}</p>
              <p class="message-text">{{msg.mensagem}}</p>
              <p class="message-time">{{formatarData(msg!.dtHoraEnvio!)}}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="chat-input-container">
        <mat-form-field appearance="outline" class="chat-input-field">
          <mat-label>Mensagem</mat-label>
          <textarea matInput #message maxlength="300" [(ngModel)]="objetoMensagem.mensagem"
            placeholder="Escreva a mensagem..."></textarea>
          <mat-hint align="end">{{message.value.length}} / 300</mat-hint>

          <div class="chat-input-options">
            <mat-checkbox [(ngModel)]="objetoMensagem.flgMensagemImportante" matTooltip="Mensagem com prioridade">
            </mat-checkbox>
          </div>
        </mat-form-field>

        <button class="btn btn-icon btn-send" matTooltip="Enviar" (click)="EnviarMensagem()">
          <span class="material-icons">send</span>
        </button>
      </div>
    </div>
  </ngx-smart-modal>

  <!-- Conteúdo principal -->
  <div class="card main-card">
    <div class="card-header">
      <div class="header-left">
        <div class="icon-container">
          <span class="material-icons">medical_services</span>
        </div>
        <h1 class="page-title">Análise</h1>
      </div>
      <button class="btn btn-link" onclick='history.go(-1)'>
        <span class="material-icons">arrow_back</span>
      </button>
    </div>

    <div class="card-body">
      <!-- Seção de dados do paciente -->
      <div class="section">
        <div class="patient-info">
          <div class="patient-photo">
            <img src="{{ ImagemPessoa }}" alt="Foto do Paciente" class="patient-img" />
          </div>
          
          <div class="patient-selector">
            <ng-select 
              class="modern-select"
              [items]="DadosPacientes" 
              placeholder="Selecione o paciente" 
              bindLabel="nome"
              bindValue="idCliente" 
              [clearable]="true" 
              [(ngModel)]="objAnalise!.idPaciente"
              [disabled]="objAnalise?.idAnalise != undefined">
            </ng-select>
          </div>
          
          <div class="orientation-type">
            <mat-form-field appearance="outline">
              <mat-label>Tipo de Orientação</mat-label>
              <mat-select [(ngModel)]="objAnalise!.idTipoOrientacao" required>
                <mat-option *ngFor="let item of listaOrientacao" [value]="item.valor">
                  {{item.descricao}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>

      </div>
      <div class="form-row">
        <div class="form-group full-width">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Exames</mat-label>
            <mat-select multiple [(ngModel)]="examesSelecionados">
              <mat-option *ngFor="let item of listaExames" [value]="item!.idExameClinica!">
                {{item.nmeExame | truncate : 40 : "…"}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
      
      <div class="form-row">
        <div class="form-group full-width">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Formulários</mat-label>
            <mat-select multiple [(ngModel)]="listaNovosFormularios" (selectionChange)="updateListaAtribuios()">
              <mat-option *ngFor="let item of listaAllFormularios" [value]="item">
                {{item.nomeFormulario! | truncate : 40 : "…"}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <!-- Seção de Exames Solicitados -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Exames Solicitados</h2>
        </div>
        <div class="section-content">
          <div class="items-list">
            <div class="item-card" *ngFor="let exames of objAnalise!.examesSelecionados">
              <div class="item-header">
                <div class="item-title">
                  <span class="material-icons">description</span>
                  <h3>{{exames.nmeExame}}</h3>
                </div>
                <div class="item-status" [ngClass]="exames.chaveArquivo ? 'status-success' : 'status-pending'">
                  {{exames.chaveArquivo ? 'Arquivo enviado' : 'Pendente'}}
                </div>
              </div>

              <div class="item-details">
                <p>Data de Cadastro: {{exames.dtaCadastro | date: 'dd/MM/yyyy'}}</p>
              </div>

              <div class="item-actions">
                <ng-container *ngIf="!exames.chaveArquivo">
                  <label for="file" class="btn btn-outline btn-upload">
                    <span class="material-icons">upload_file</span>
                    Carregar arquivo
                  </label>
                  <input type="file" id="file" (change)="SubirArquivoConsulta($event,exames)"
                    [disabled]="exames.chaveArquivo" accept=".pdf" />
                </ng-container>

                <button *ngIf="exames.chaveArquivo" class="btn btn-success" (click)="ModalArquivo(exames)">
                  <span class="material-icons">visibility</span>
                  Visualizar arquivo
                </button>
              </div>
            </div>

            <div class="empty-state" *ngIf="!objAnalise?.examesSelecionados?.length">
              <span class="material-icons">info</span>
              <p>Nenhum exame solicitado</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção de Formulários Solicitados -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Formulários Solicitados</h2>
        </div>

        <div class="col-md-12 col-sm-12 col-xs-12 div-medico" style="padding: 0; align-self: center;">

          <div style="width:100%; display:flex; padding:1%; gap: 2%; justify-content: space-evenly;">

            <div style="width:92%">
              <textarea [(ngModel)]="objAnalise.observacao"></textarea>

            </div>
          </div>
        </div>

      </div>

      <div class="card-footer">
        <div class="footer-left">
          <button class="btn btn-outline" (click)="CarregaModalChatInterno()" *ngIf="idAnalise">
            <span class="material-icons">chat</span>
            Chat Interno
          </button>
        </div>
        <div class="footer-right">
          <button class="btn btn-success" (click)="BaixarArquivo()" *ngIf="objAnalise!.chaveArquivo">
            <span class="material-icons">download</span>
            Baixar Arquivo
          </button>
          <button class="btn btn-solicitar" (click)="SolicitarAnalise()" *ngIf="!objAnalise!.idAnalise">
            <span class="material-icons">add_task</span>
            Solicitar Análise
          </button>
          <button class="btn btn-success" (click)="Salvar()">
            <span class="material-icons">save</span>
            Salvar
          </button>
          <button class="btn btn-outline" (click)="limpar()">
            <span class="material-icons">refresh</span>
            Limpar
          </button>
        </div>
      </div>

      <!-- Área do Profissional -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Área do Profissional</h2>
        </div>
        <div class="section-content">
          <div class="professional-area">
            <!-- Área do Editor (a ser implementado conforme necessidades) -->
            <div class="editor-container">
              <!-- Aqui entraria o componente de editor que estava comentado no código original -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<ngx-smart-modal #arquivo identifier="arquivo" customClass="nsm-centered medium-modal emailmodal">
  <pdf-viewer [src]="urlPDF" [render-text]="true" [rotation]="0" [original-size]="false" [show-all]="true"
    [fit-to-page]="false" [zoom]="1" [zoom-scale]="'page-width'" [stick-to-page]="false" [render-text]="true"
    [external-link-target]="'blank'" [autoresize]="true" [show-borders]="false"
    style="width: 1000px; height: 600px;"></pdf-viewer>
  <!-- <div class="modal-email">

      <div class="modal-info-email">
          <b>{{ 'TELAPESQUISAMEDICO.ENVIARESTEEMAIL' | translate }}</b> <br>
          <small style="color: #444; font-weight: 300;">{{ 'TELAPESQUISAMEDICO.EMAILCOMASENHA' | translate }}</small>


          <br *ngIf="inativos">
          <b *ngIf="inativos"
              style="color: red;  text-decoration: underline;">{{'TELAPESQUISAMEDICO.CADASTROESTAINATIVO' |
              translate}}</b>
          <br *ngIf="inativos">
          <small *ngIf="inativos"
              style=" color: red;  text-decoration: underline;">{{'TELAPESQUISAMEDICO.ATIVANOVAMENTE' |
              translate}}</small>

      </div>

      <hr class="sep-1" />

      <div class="row-button text-center p-20">
          <button mat-flat-button (click)="emailUsuario.close()" class="btn btn-danger btn-email">{{
              'TELAPESQUISAMEDICO.NAO' | translate }}</button>
          <button mat-flat-button *ngIf="!inativos" (click)="mandaEmail()" (click)="emailUsuario.close()"
              class="btn btn-primary btn-email">{{ 'TELAPESQUISAMEDICO.SIM' | translate }}</button>
          <button mat-flat-button *ngIf="inativos" (click)="mandaEmailAtivarUser()" (click)="emailUsuario.close()"
              class=" btn btn-primary btn-email">{{ 'TELAPESQUISAMEDICO.SIM' | translate }}</button>
      </div>

      <div class="modal-inativar-logo">
          <img class="logo-final-modal" src="assets/build/img/logo medicina para voce.png">
      </div>

  </div> -->

  <div class="row-button text-center p-20">
    <button mat-flat-button (click)="BaixarArquivo()" class="btn btn-danger btn-email">Baixar Arquivo</button>
  </div>
</ngx-smart-modal>
<ngx-smart-modal #formularioViewer identifier="formularioViewer" customClass="nsm-centered medium-modal emailmodal">
  <div id="formularioViewer">
    <div class="formulario-content">
      <h1 id="tituloModal">
        {{formularioSelecionadoModal.nomeFormulario}}
      </h1>
      <div class="chat-container">
        <div *ngFor="let dados of listaPerguntas">
          <div class="chat-message left">
            <p>Pergunta: {{dados.pergunta}} <br>
            </p>
            <!-- Tipo de Resposta: {{dados.tipoResposta}} -->
          </div>
          <div class="chat-message right" *ngIf="dados.flgRespondido">
            <p>• Resposta: {{dados.resposta}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</ngx-smart-modal>