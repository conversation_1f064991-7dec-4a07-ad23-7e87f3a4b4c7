import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Usuario } from '../model/usuario';
import { Observable, take } from 'rxjs';
import { Atendente } from '../model/atendente';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { UsuarioLogado } from '../auth/UsuarioLogado';
import { SignalHubService } from './signalHub.service';
import { LocalStorageService } from './LocalStorageService';
import { SpinnerService } from './spinner.service';
// import { Endereco } from '../model/endereco';

@Injectable({
    providedIn: 'root'
})
export class UsuarioService {

    public changeImage$: EventEmitter<any>;

    constructor(
        private http: HttpClient,
        private usuarioLogadoService: UsuarioLogadoService,
        private signalHubService: SignalHubService,
        private localStorageService: LocalStorageService,
        private spinner: SpinnerService,
    ) {

        this.changeImage$ = new EventEmitter();
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getTabelaUsuario(inicio:any, fim:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));


        return this.http.get(environment.apiEndpoint + '/Usuario/GetTabUsuario', { params });
        // return this.http.get(environment.apiEndpoint + '/Usuario')
        //     .toPromise();
    }
    public AceitaPrivacidade(id:any) {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Usuario/AceitaPrivacidade/' + id)
            .toPromise();
    }
    public inativarUsuario(id:any) {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Usuario/InativarUsuario/' + id)
            .toPromise();
    }

    public CarregarUsuario(id:any) {
        return this.http.get(environment.apiEndpoint + '/Usuario/' + id)
            .toPromise();
    }

    public EnviarArrayEmailImobiliaria(id:any): Observable<any> {
        return this.http.get(environment.apiEndpoint + '/Usuario/EmailUsuariosImobiliaria/' + id);
    }

    public EnviarEmaiUsuario(id:any): Observable<any> {
        return this.http.get(environment.apiEndpoint + '/Usuario/EnviarEmaiUsuario/' + id);
    }


    public CarregarPerfil(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Usuario/CarregaPerfilUsuarioLogado/' + id);
    }

    public salvarUsuario(usuario: Usuario): Observable<any> {

        return this.http.post(environment.apiEndpoint + '/Usuario/', usuario);
    }


    public salvarAtendente(atendente: Atendente, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Usuario/PostAtendente/' + idUsuario, atendente);
    }

    public salvarUsuarioImobiliaria(usuario: Usuario): Observable<any> {
        return this.http.post(environment.apiEndpoint + '/Usuario/postUsuarioImobiliaria', usuario);
    }

    async AtualizarPerfil(usuario: Usuario) {
        this.spinner.show();
        try {
            const res: any = await this.http.post(environment.apiEndpoint + "/Usuario/AtualizaSenha", usuario).toPromise();
            
            const usuarioLogado = this.usuarioLogadoService.getIdPessoa();
            if (usuarioLogado == res.user.idPessoa) {
                this.changeImage$.emit(res.user);
                
                this.usuarioLogadoService.getUsuarioLogado().pipe(take(1)).subscribe((ret) => {
                    if (ret!.idPessoa === res.user.idPessoa) {
                        const usuarioAtualizado = {...ret};
                        
                        usuarioAtualizado.nome = res.user.nomePessoa;
                        usuarioAtualizado.email = res.user.email;
                        
                        if (usuarioAtualizado.nome !== ret!.nome || usuarioAtualizado.email !== ret!.email) {
                            this.usuarioLogadoService.setUsuarioLogado(JSON.stringify(usuarioAtualizado));
                            this.changeImage$.emit(usuarioAtualizado);
                        }
                    }
                });
            }
            
            this.spinner.hide();
            return res;
        } catch (error) {
            this.spinner.hide();
            return error;
        }
    }


    // public AtualizarPerfil(usuario: Usuario): Observable<any> {

    //     return this.http.post(environment.apiEndpoint + '/Usuario/AtualizaSenha/' ,usuario);
    //     }

    reqHeader = new HttpHeaders({ 'No-Auth': 'True' });


    // public GetConsultaGuid(guid, codAcesso): Observable<any> {
    //     return this.http.get(environment.apiEndpoint + '/Agenda/GetConsultaGuid/' + guid + '/' + codAcesso, {headers: reqHeader });
    // }


    public validar_TELMOVEL_CPF_EMAIL(value: string, isPessoa: number | null, user: string): Observable<any> {
        let params = new HttpParams();
        params = params.append('value', String(value));
        params = params.append('idPessoa', String(isPessoa));
        params = params.append('user', String(user));

        return this.http.get(environment.apiEndpoint + '/Usuario/validar_TelMovel_CPF_EMAIL', { params });
    }

    public validar_TELMOVEL_CPF_EMAIL_Acesso_Fila(value: string, isPessoa: number | null, user: string): Observable<any> {
        let params = new HttpParams();
        params = params.append('value', String(value));
        params = params.append('idPessoa', String(isPessoa));
        params = params.append('user', String(user));

        return this.http.get(environment.apiEndpoint + '/Usuario/validar_TELMOVEL_CPF_EMAIL_Acesso_Fila', { params, headers: this.reqHeader });
    }

    public AtualizaPerfil(idUsuario: number, idclinica: number, Atualizacao: string): Observable<any> {
        let params = new HttpParams();
        params = params.append('idUsuario', String(idUsuario));
        params = params.append('idclinica', String(idclinica));
        params = params.append('Atualizacao', String(Atualizacao));

        return this.http.get(environment.apiEndpoint + '/Usuario/AtualizarLogin', { params });
    }

    usuLogado?: UsuarioLogado;

    public AtualizaDadosUsuarioLogado(dadosusuario: UsuarioLogado) {

        ;
        this.usuLogado = dadosusuario;
        this.usuLogado.clinicas = dadosusuario.clinicas;
        this.usuarioLogadoService.setUsuarioLogado(JSON.stringify(this.usuLogado));

        this.signalHubService.loginUsuario(dadosusuario);
    }

    public AtualizaDadosUsuarioNovaSenha(dadosusario:any) {
        ;
        var usuario = this.localStorageService.UsuarioLogado;
        usuario.nome = dadosusario.nome;
        usuario.cpf = dadosusario.cpf;
        usuario.email = dadosusario.email;
        usuario.idUsuario = dadosusario.idUsuario;
        usuario.Imagem64 = dadosusario.imagem64;
        usuario.idTipoUsuario = dadosusario.idTipoUsuario;
        usuario.flgPrimeiroAcesso = dadosusario.flgPrimeiroAcesso;
        usuario.idUltimaClinica = dadosusario.idClinica;
        usuario.flgProntuario = dadosusario.flgProntuario;

        this.localStorageService.UsuarioLogado = usuario;
    }



    public CarregaTipoUsuario(): Observable<any> {
        return this.http.get(environment.apiEndpoint + '/Usuario/TipoUsuario');
    }


    public getPerfilUsuarioLogado(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Usuario/GetUsuarioLogado/');
    }
    public getPerfilUsuarioLogadoClinica(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Usuario/GetUsuarioLogadoTrocaClinica');
    }


}


