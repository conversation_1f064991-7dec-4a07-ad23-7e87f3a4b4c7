// import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import ClassicEditor from 'ckeditor-build-b64imageupload';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { msgResposta } from 'src/app/model/retorno-resposta';
import { ItemService } from './../../service/Item.Service';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { Component, OnInit } from '@angular/core';
import { novoItem, tipoOrientacao } from 'src/app/model/itens';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';


@Component({
    selector: 'app-adicionar-itens',
    templateUrl: './adicionar-itens.component.html',
    styleUrls: ['./adicionar-itens.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCard,
      MatIcon,
      NgSelectModule,
      MatFormFieldModule,
      CKEditorModule,
      TranslateModule
    ]
})
export class AdicionarItensComponent implements OnInit {
  public data = '';

  constructor(    
    private spinner: SpinnerService,
    private localStorageService: LocalStorageService,
    private usuarioLogadoService: UsuarioLogadoService,
    private itemService: ItemService,
    // private snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,

  ) { }
  flgUser: boolean = false;
  objTipoOrientacao: tipoOrientacao[] = [];

  ImagemPessoa: any = "assets/build/img/userdefault.png";
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  actionButtonLabel: string = 'Fechar';
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  action: boolean = true;
  observacoesItem?:string;
  idObjItem?: number;
  strObservacao: string = "";
  retorno?: msgResposta;

  objItem = new novoItem();

  // Editor = ClassicEditor;
  Editor:any;
  config = {
    toolbar: ['heading', '|', 'undo', 'redo', '|', "outdent", "indent" ,'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'link', 'blockQuote', 'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', 'imageInsert' ] // , 'removeFormat'],
  };

  ngOnInit() {
    this.verificaUsuario();
    this.CarregaTipoOrientacao()

    this.idObjItem = this.localStorageService.idItem;
    if(this.idObjItem != null && this.idObjItem > 0){
      this.itemService.GetObjItem(this.idObjItem).subscribe((retorno) => {
        ;
        ;
        this.objItem = retorno;
        this.strObservacao = retorno.observacoes!
        this.spinner.hide();
      },erro => {console.error(erro)})
    }
    this.localStorageService.idItem = null;
    this.spinner.hide();
  }
  verificaUsuario() {
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM || this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.flgUser = true;
    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente || this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
      this.flgUser = false;
    }
  }

  Salvar() {
    if (this.VerificaSalvamento()) {
      this.objItem.observacoes = this.strObservacao;
      this.itemService.SalvarNovoItem(this.objItem).subscribe((retorno) => {
        this.retorno = retorno;
        this.snackBarAlert.sucessoSnackbar('Item adicionado com sucesso!')
        this.spinner.hide();
        this.Limpar() 
      })
    }
  }

  VerificaSalvamento() {
    if (this.objItem.idTipoOrientacao == null) {
      this.snackBarAlert.falhaSnackbar('Selecione o tipo de orientação referente a este item');
      return false;
    }
    if (this.objItem.nomeItem == '') {
      this.snackBarAlert.falhaSnackbar('Preencha o nome do item');
      return false;
    }
    if (this.strObservacao == null) {
      this.snackBarAlert.falhaSnackbar('Preencha as observações');
      return false;
    }
    if (this.objItem.site == '') {
      this.snackBarAlert.falhaSnackbar('Preencha o \bSite');
      return false;
    }
    // this.objItem.observacoes = this.observacoesItem;

    return true;
  }

  Limpar() {
    this.objItem = new novoItem();
    this.strObservacao = "";
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }
  CarregaTipoOrientacao(){
    this.itemService.GetTipoOrientacao().subscribe((retorno) => {
      this.objTipoOrientacao = retorno;
      this.spinner.hide();
    }, erro => {
      console.error(erro)
      this.spinner.hide();
    })
  }

}
