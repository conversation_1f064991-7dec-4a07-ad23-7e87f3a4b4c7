<header>
  <a [routerLink]="['/perfil']"><img src="/assets/build/img/logo faso Final  - Copia.png" class="logo-footer" style="margin-left: 4% !important;
      cursor: pointer;"></a>


</header>
<div class="background-login div-mother-padd" style="overflow: auto;">
  <div class="div-login col-md-12 row" *ngIf="DivInicial">
    <div class="col-md-3"></div>
    <div class="col-md-6 row div-resp-login" style="background: white;
    padding: 2%;">

    

      <div class="col-md-12" style="text-align: center;">
        <mat-card-title class="text-center">
          <img    class="img-login-logo" alt="logo">
        </mat-card-title>
      </div>


      

      <div class="col-md-3" *ngIf="!DivEspera"></div>
      <div class="col-md-6 div-input-resp">

        <!-- Login -->
        <!-- <mat-form-field class="input-login" appearance="outline">
          <input matInput placeholder="{{ 'TELALOGIN.CPF' | translate }}" id="cpf" name="cpf" required
               autocomplete="off" mask="000.000.000-00" maxlength="14">
            <mat-label><i class="fas fa-user-injured space-icon"></i> <i> {{ 'TELALOGIN.CPF' | translate }} </i>
          </mat-label> 
        </mat-form-field> -->


        <!-- <mat-form-field class="col-md-12 col-sm-12 input-spacing title-input" appearance="outline" style="height: 35px;"> -->
<!--          
          <input matInput placeholder="{{ 'TELACADASTROPACIENTE.CPF' | translate }}" name="CPF"
   (keyup.enter)='validarCpf()'  maxlength="14" required  
    (keypress)="mascaraCpf('###.###.###-##', $event)" (keyup)="mascaraCpf('###.###.###-##', $event)" 
            [(ngModel)]="PacienteAcesso.cpf">
            <mat-label><i> {{ 'TELALOGIN.CPF' | translate }} </i>
            </mat-label>
        </mat-form-field> -->

        <mat-form-field class="input-login" appearance="outline" >
          <input matInput placeholder="{{ 'TELALOGIN.CPF' | translate }}" id="cpf" name="cpf"
              (keypress)="mascaraCpf('###.###.###-##', $any($event.target).value)" autocomplete="off"
              (change)="mascaraCpf('###.###.###-##', $any($event.target).value);validarCpf()" mask="000.000.000-00" maxlength="14"
              [(ngModel)]="PacienteAcesso.cpf">
          <mat-label> <i> {{ 'TELALOGIN.CPF' | translate }} </i>
          </mat-label>
      </mat-form-field>

        <span class="ml-1 text-center cpf-invalido" *ngIf="campoCPFInvalido == true">CPF inválido</span>

        <span class="ml-1 text-center prec-preenc" *ngIf="campoCPFVazil == true && campoCPFInvalido != true">Esse campo precisa ser preenchido</span>
      </div>

      <mat-form-field class="col-md-6 title-input div-nome div-input-resp" *ngIf="DivEspera" appearance="outline">
    
        <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" name="Nome" required
          (keyup)="mascaraText($any($event.target).value)" (change)="mascaraText($any($event.target).value)" [(ngModel)]="PacienteAcesso.nome"
          [formControl]='Nome'>
        <mat-error class="erro-nome" *ngIf="Nome.invalid">{{getErrorMessageNome() | translate }}</mat-error>
        <mat-label> <i>Nome</i>
        </mat-label>
      </mat-form-field>

      <div class="col-md-4 col-sm-12 email-resp div-input-resp" style="padding: unset; height: 50px;" *ngIf="DivEspera">
        <mat-form-field appearance="outline" class="col-12  input-spacing campo-NovoCliente title-input" style="height: 31px;">
         
          <input matInput placeholder="{{ 'TELACADASTROCLINICA.EMAIL' | translate }}" type="email" name="Email"
            (change)="ValidarEmail($any($event.target).value)" required [(ngModel)]="PacienteAcesso.email">
            <mat-label> <i>E-mail</i></mat-label>
        </mat-form-field>
        <!-- <span class="ml-1 required" style="padding-left: 10px;" *ngIf="campoEmailInvalido == true">Email inválido.</span>
        <span class="ml-1 required" style="padding-left: 10px;" *ngIf="campoEmailexistente == true">Email já registrado.</span>
        <span class="ml-1 required" style="padding-left: 10px;"
          *ngIf="campoEmailVazil == true && campoEmailInvalido != true">Esse campo precisa ser preenchido.</span> -->


        <span class="ml-1 text-center email-inval" *ngIf="campoEmailInvalido == true">Email inválido.</span>


        <span class="ml-1 text-center email-inval" *ngIf="campoEmailexistente == true">Email já registrado.</span>


        <span class="ml-1 text-center email-inval" *ngIf="campoEmailVazil == true && campoEmailInvalido != true">Esse campo precisa ser preenchido.</span>


      </div>

      <!-- <mat-form-field class="col-md-4" *ngIf="DivEspera">
        <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" name="Telefone Movel"
          (keyup)="mascaraTelefone($event)" (change)='ValidarTelMovel($any($event.target).value)' required maxlength="15"
          [(ngModel)]="PacienteAcesso.telefoneMovel" [formControl]='tel'>
        <mat-error *ngIf="tel.invalid">{{getErrorMessagetel() | translate }}</mat-error>

      </mat-form-field> -->
      <div class="col-md-4 col-sm-12 celular-resp" style="padding: unset; height: 50px;" *ngIf="DivEspera">
        <mat-form-field class="col-12 input-spacing title-input " appearance="outline" style="height: 31px;">
        
          <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" name="Telefone Movel"
            (keyup)="mascaraTelefone($event)" (change)='ValidarTelMovel($any($event.target).value)' required maxlength="15"
            [(ngModel)]="PacienteAcesso.telefoneMovel"><mat-label> <i>Celular</i></mat-label>
        </mat-form-field>

        <span class="ml-1 text-center" *ngIf="TelMovExistente == true" style="font-size: 65%;color: #f44336;font-weight: 600;
            margin-left: 8% !important;">Telefone já registrado.</span>

        <span class="ml-1 text-center" *ngIf="TelMovVal == true" style
        ="font-size: 65%;color: #f44336;font-weight: 600;
            margin-left: 8% !important;">{{ 'TELACADASTROUSUARIO.TELEFONEINVALIDO' | translate }}</span>

        <span class="ml-1 text-center tel-preenc" *ngIf="TelMovValVasil == true && TelMovVal != true">{{ 'TELACADASTROUSUARIO.ESSECAMPOPRECISASERPREENCHIDO' | translate }}</span>
      </div>

      <mat-form-field class="col-md-4 title-input sus-input" *ngIf="DivEspera" appearance="outline">
    
        <input matInput placeholder="Carteirinha do SUS" (change)="ValidaSUS($any($event.target).value)" id="SUS"
          autocomplete="off" name="sus" [(ngModel)]="PacienteAcesso.carterinha"><mat-label> <i>Cart. SUS</i></mat-label>

      </mat-form-field>


    

      <div class="row-button  col-md-12 text-center btns-acesso-agenda">
        <button mat-flat-button class="btn-primary" *ngIf="DivEspera"
          (click)="SubmitNovoPaciente()">{{ 'TELALOGIN.ENTRAR' | translate }}</button>
        <span class=""> </span>

        <button mat-flat-button class="btn-primary" (click)='validarCpf()'
          *ngIf="!DivEspera">{{ 'TELALOGIN.ENTRAR' | translate }}</button>

        <button mat-flat-button class="btn-primary" style="
        margin-left: 10px;" (click)="LimparCamposNovoPaciente()">Limpar</button>
        <span class=""> </span>
      </div>

    

    </div>

    <div class="col-md-3"></div>
  </div>
</div>

<footer class="footer-faso-novo">
  <div class="telaGrande" style="margin-right: 50px; font-weight:400;">
    <img src="/assets/build/img/microsoftLogo.png" class="logoRodapefooter">
  </div>
  <div class="telaGrande" style="margin-right: 50px; font-weight:400; align-self: center;cursor: context-menu;">
    <a style="margin-right: 5px;">
      O Medicina Para Você é um site seguro
    </a>
  </div>
  <div>
    <a href="https://www.facebook.com/MedicinaParaVoce/posts/117102886621942" target="_blank">
      <i class="fa fa-facebook fa-fw fa-2x logoRodapefooter" style="color:#002e4b; cursor: pointer;"
        click="abrirFacebook">
      </i>
    </a>
    <a href="https://www.youtube.com/watch?v=ePea_b-M85c&feature=emb_title" target="_blank">
      <i class="fa fa-youtube fa-fw fa-2x" style="color:rgb(255, 0, 0); cursor: pointer; margin-left: 20px;">
      </i>
    </a>
    <a href="https://www.instagram.com/medicinaparavoce/" target="_blank">
      <i class="fa fa-instagram fa-fw fa-2x" style="color:black;cursor: pointer; margin-left: 20px;">
      </i>
    </a>
    <a href="https://twitter.com/medicinaparavc/status/1249782319075995649" target="_blank">
      <i class="fa fa-twitter fa-fw fa-2x" style="cursor: pointer; margin-left: 20px;">
      </i>
    </a>
  </div>
  <div class="telaGrande" style="margin-right: 50px; font-weight:400; align-self: center; cursor: context-menu;">
    <a style="margin-right: 5px;">
      © 2020 - Faso Fábrica de Software
    </a>
  </div>
</footer>


<ngx-smart-modal #UsuarioExistente identifier="UsuarioExistente" customClass="nsm-centered medium-modal"
  [closable]="false" [dismissable]="false" [escapable]="false">
  <div class="modal-header p-t-20 p-b-20">
    <div class="row">
      <div class=" col-12">
        <h1 class="little-title fw-700" style=" padding-left: 3vh; text-align: center">Cadastro encontrado com Sucesso!
          <br>Digite os 4 últimos dígitos do seu celular.</h1>
        <mat-form-field class="input-login" appearance="outline">
          <input matInput placeholder="Cod.Acesso" id="acesso" (keyup)="Teinvalido = false"
            (keyup.enter)="agendaFilaEspera()" autocomplete="off" (keyup)="mascaraNumeros($event)"
            (keypress)="mascaraNumeros($event)" maxlength="4" name="acesso" [(ngModel)]="codAcesso">
          <mat-label><i class="fas fa-user-injured space-icon"></i> <i>4 Últimos Dígitos
            </i></mat-label>
        </mat-form-field>
        <div class="danger-baloon" *ngIf="Teinvalido == true">
          <label style="color: red;" class="text-right">Número inválido</label></div>
      </div>
    </div>
  </div>
  <mat-divider></mat-divider>
  <div class="row-button text-center p-t-20">
    <button mat-flat-button (click)="agendaFilaEspera()" class="input-align btn btn-success">Acessar</button>
    <button mat-flat-button (click)="NaoAceitarUsuarioExistente()" class="input-align btn btn-danger">Cancelar</button>

  </div>
</ngx-smart-modal>


<!-- 
<ngx-smart-modal #UsuarioExistente identifier="UsuarioExistente" customClass="nsm-centered medium-modal"
  [closable]="false" [dismissable]="false" [escapable]="false">
  <div class="modal-header p-t-20 p-b-20">
    <div class="row">
      <div class=" col-12">
        <h1 class="little-title fw-700" style=" padding-left: 3vh; text-align: center">Cadastro encontrado com Sucesso!
          <br>Digite os 4 últimos dígitos do seu celular.</h1>
       
      </div>
    </div>
  </div>



  <mat-divider></mat-divider>
  <div class="row-button text-center p-t-20">
    <button mat-flat-button (click)="agendaFilaEspera()" class="input-align btn btn-success">Acessar</button>
    <button mat-flat-button (click)="NaoAceitarUsuarioExistente()" class="input-align btn btn-danger">Cancelar</button>

  </div>
</ngx-smart-modal> -->