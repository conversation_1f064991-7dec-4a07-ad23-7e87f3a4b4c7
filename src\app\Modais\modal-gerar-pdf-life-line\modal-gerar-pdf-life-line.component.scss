/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo principal do modal */
.modal-content {
    flex: 1;
    padding: 20px;
    background-color: $secondary-light;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: auto;
}

/* Instruções ao usuário */
.instructions {
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: rgba($primary-light, 0.2);
    padding: 12px 16px;
    border-radius: $border-radius;
    border-left: 4px solid $primary-color;
    
    mat-icon {
        color: $primary-color;
    }
    
    p {
        margin: 0;
        color: $primary-dark;
        font-size: 15px;
    }
}

/* Painel de seleção de datas */
.date-selection-panel {
    background-color: $card-bg;
    border-radius: $border-radius;
    padding: 24px;
}

.date-form {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
}

.form-group {
    flex: 1;
    min-width: 220px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    label {
        font-weight: 500;
        color: $text-primary;
        font-size: 14px;
    }
    
    .date-input {
        padding: 10px 12px;
        border: 1px solid $border-color;
        border-radius: $border-radius;
        font-size: 14px;
        color: $text-primary;
        transition: all $transition ease;
        
        &:focus {
            outline: none;
            border-color: $primary-color;
            box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
        }
    }
    
    &.has-error {
        .date-input {
            border-color: $error-color;
        }
    }
}

.error-message {
    color: $error-color;
    font-size: 12px;
    margin-top: 4px;
}

/* Painel de lista de consultas */
.appointment-list-panel {
    background-color: $card-bg;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 50vh;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    color: $text-secondary;
    text-align: center;
    
    mat-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        margin-bottom: 16px;
        color: $secondary-dark;
    }
    
    h5 {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
    }
}

.appointment-list {
    overflow-y: auto;
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.appointment-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: $border-radius;
    background-color: $secondary-light;
    border: 1px solid $border-color;
    transition: all $transition ease;
    cursor: pointer;
    
    &:hover {
        background-color: $secondary-color;
        transform: translateY(-2px);
        box-shadow: $box-shadow;
    }
    
    &.selected {
        background-color: rgba($primary-light, 0.2);
        border-color: $primary-color;
    }
}

.checkbox-container {
    margin-right: 16px;
    
    input[type="checkbox"] {
        width: 20px;
        height: 20px;
        accent-color: $primary-color;
        cursor: pointer;
    }
}

.appointment-details {
    flex: 1;
}

.doctor,
.date {
    display: flex;
    align-items: baseline;
    margin-bottom: 4px;
    
    .label {
        font-weight: 600;
        color: $text-primary;
        margin-right: 6px;
        min-width: 60px;
    }
    
    .value {
        color: $text-primary;
    }
}

.date .value {
    font-size: 14px;
    color: $text-secondary;
}

/* Painel de download */
.download-panel {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
}

.download-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background-color: $primary-color;
    color: white;
    border: none;
    border-radius: $border-radius;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    
    &:hover {
        background-color: $primary-dark;
        transform: translateY(-2px);
        box-shadow: $box-shadow;
    }
    
    mat-icon {
        font-size: 24px;
        height: 24px;
        width: 24px;
    }
}

/* Rodapé do modal */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    background-color: $secondary-color;
    border-top: 1px solid $border-color;
}

/* Botões de ação */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 110px;
}

.back-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.back-button:hover {
    background-color: darken($secondary-dark, 5%);
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: darken($secondary-dark, 5%);
}

.next-button,
.confirm-button {
    background-color: $primary-color;
    color: white;
}

.next-button:hover,
.confirm-button:hover {
    background-color: $primary-dark;
}

/* Responsividade */
@media (max-width: 768px) {
    .date-form {
        flex-direction: column;
        gap: 16px;
    }
    
    .form-group {
        min-width: 100%;
    }
    
    .action-button {
        min-width: 0;
        padding: 8px;
    }
    
    .action-button span {
        display: none;
    }
    
    .download-button {
        width: 100%;
    }
}