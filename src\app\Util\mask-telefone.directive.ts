import { Directive, HostListener, ElementRef, OnInit } from '@angular/core';

@Directive({
  selector: '[telefoneMask]'
})
export class MaskTelefoneDirective implements OnInit {
  
  constructor(private el: ElementRef) {}
  
  ngOnInit() {
    // Aplicar máscara ao valor inicial se houver
    setTimeout(() => {
      const input = this.el.nativeElement;
      input.value = this.aplicarMascara(input.value);
    });
  }
  
  @HostListener('input', ['$event'])
  onInputChange(event: any) {
    const input = event.target;
    const valor = input.value.replace(/\D/g, ''); // Remove não dígitos
    
    input.value = this.aplicarMascara(valor);
    
    // Atualiza o modelo
    const event2 = new Event('input', {bubbles: true});
    input.dispatchEvent(event2);
  }
  
  aplicarMascara(valor: string): string {
    if (!valor) return '';
    
    valor = valor.replace(/\D/g, '');
    
    if (valor.length > 11) {
      valor = valor.substring(0, 11);
    }
    
    // Verifica se é celular (com 9 dígitos) ou telefone fixo (com 8 dígitos)
    if (valor.length > 10) {
      // Celular: (00) 00000-0000
      return `(${valor.substring(0, 2)}) ${valor.substring(2, 7)}-${valor.substring(7, 11)}`;
    } else if (valor.length > 6) {
      // Fixo: (00) 0000-0000
      return `(${valor.substring(0, 2)}) ${valor.substring(2, 6)}-${valor.substring(6, 10)}`;
    } else if (valor.length > 2) {
      // Parcial: (00) 
      return `(${valor.substring(0, 2)}) ${valor.substring(2)}`;
    } else if (valor.length > 0) {
      // Só DDD: (0
      return `(${valor}`;
    } else {
      return '';
    }
  }
  
  @HostListener('blur')
  onBlur() {
    const valor = this.el.nativeElement.value;
    if (valor.length < 14) {
      // Se for menor que o formato mínimo (00) 0000-0000, pode estar incompleto
      // Você pode decidir limpar o campo ou manter o valor parcial
    }
  }
}