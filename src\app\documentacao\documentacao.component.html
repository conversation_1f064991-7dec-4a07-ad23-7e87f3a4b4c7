<!-- Modal Atestado -->
<ngx-smart-modal #InicializandoAtestado identifier="InicializandoAtestado" [dismissable]="false" [closable]="true"
    customClass="nsm-centered modern-modal health-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <h3 class="modal-title">{{ 'TELADOCUMENTACAO.GERARATESTADO' | translate }}</h3>
        </header>
        
        <main class="modal-container">
            <div class="form-row">
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline" *ngIf="!AtestadoStream">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOPACIENTEPELOCPF' | translate }}" 
                                name="CPf" mask="000.000.000-00" [(ngModel)]="CPF" (change)="CarregaPaciente()">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <ng-select [items]="ListaPaciente" 
                            placeholder="{{ 'TELADOCUMENTACAO.PACIENTE' | translate }} *"
                            bindLabel="nome" bindValue="idCliente" name="Paciente" [selectOnTab]="true" 
                            [(ngModel)]="IdPaciente" [formControl]='paci' aria-required="true" 
                            (blur)="ValidaPaciAtes($any($event.target).value)" class="custom-select" 
                            *ngIf="!AtestadoStream">
                        </ng-select>
                        <div class="error-message" *ngIf="paciAtesVal == true">
                            {{ 'TELADOCUMENTACAO.TELACAMPO' | translate }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column info-column">
                    <button *ngIf="!AtestadoStream" mat-icon-button class="info-button"
                        title="{{'TELADOCUMENTACAO.INFORMACOESPACIENTE' | translate}}" 
                        [disabled]="!IdPaciente" (click)="informacao('Paciente',IdPaciente)">
                        <mat-icon>info</mat-icon>
                    </button>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline" *ngIf="!AtestadoStream">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.DATA' | translate }}" 
                                name="Data" disabled [(ngModel)]="DtaAtestado" maxlength="10">
                        </mat-form-field>
                    </div>
                </div>
    
                <div class="col-md-6 col-sm-12 " *ngIf="!AtestadoStream">

                    <ng-select [items]="ListaMedicos" placeholder="{{ 'TELADOCUMENTACAO.MEDICOS' | translate }} *"
                        bindLabel="nomeMedico" bindValue="idMedico" name="medicos" [selectOnTab]="true"
                        [(ngModel)]="IdMedico" [formControl]='medi' aria-required="true"
                        (blur)="ValidaMediAtes($any($event.target).value)" class="col-12 input-spacing linha-medico"
                        style="height: 31px; font-size: 12px;">
                    </ng-select>
    
                    <span class="ml-1 text-center" *ngIf="mediAtesVal == true"
                        style="font-size: 63%;color: #f44336; padding-left: 11px;">{{ 'TELADOCUMENTACAO.TELACAMPO' |
                        translate }}</span>
                </div>
            </div>
            
            <div class="form-row" id="dias-atestado">
                <div class="form-field inline-field">
                    <label>{{ 'TELADOCUMENTACAO.ATIVIDADESNORMAIS' | translate }}</label>
                    <input type="text" maxlength="3" name="dias" [(ngModel)]="DiasAtestado" 
                        (keyup)="mascaraNumeros($event)" class="small-input">
                    <label>{{ 'TELADOCUMENTACAO.DIA' | translate }}</label>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.CID' | translate }}" 
                                name="CID" maxlength="6" [(ngModel)]="desCID" (keyup.enter)="CarregaCIDCampo()">
                            <button matSuffix mat-icon-button class="search-button" (click)="AbrirModalPesquisaCid()">
                                <mat-icon>search</mat-icon>
                            </button>
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.DESCRICAODOCID' | translate }}" 
                                name="CID" maxlength="6" [(ngModel)]="CID" disabled>
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="modal-footer">
            <button class="action-button cancel-button" 
                (click)="ngxSmartModalService.getModal('InicializandoAtestado').close()">
                <span>{{ 'TELADOCUMENTACAO.FECHAR' | translate }}</span>
            </button>
            
            <button class="action-button confirm-button" (click)="GerarAtestado()">
                <span>{{ 'TELADOCUMENTACAO.GERARSALVAR' | translate }}</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<!-- Modal Declaração -->
<ngx-smart-modal #InicializandoDeclaracao identifier="InicializandoDeclaracao" [dismissable]="false" [closable]="true"
    customClass="nsm-centered modern-modal health-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <h3 class="modal-title">{{ 'TELADOCUMENTACAO.GERARDECLARACAO' | translate }}</h3>
        </header>
        
        <main class="modal-container">
            <div class="form-row">
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline" *ngIf="!DeclaracaoStream">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOPACIENTEPELOCPF' | translate }}"
                                name="CPf" mask="000.000.000-00" [(ngModel)]="CPF" (change)="CarregaPaciente()">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <ng-select [items]="ListaPaciente" 
                            placeholder="{{ 'TELADOCUMENTACAO.PACIENTE' | translate }} *"
                            bindLabel="nome" bindValue="idCliente" name="Paciente" [selectOnTab]="true"
                            [(ngModel)]="IdPaciente" [formControl]='paci' aria-required="true"
                            (blur)="ValidaPaciDecl($any($event.target).value)" class="custom-select" 
                            *ngIf="!DeclaracaoStream">
                        </ng-select>
                        <div class="error-message" *ngIf="paciDeclVal == true">
                            {{ 'TELADOCUMENTACAO.TELACAMPO' | translate }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column info-column">
                    <button *ngIf="!DeclaracaoStream" mat-icon-button class="info-button"
                        title="{{'TELADOCUMENTACAO.INFORMACOESPACIENTE' | translate}}"
                        [disabled]="IdPaciente == 0 || IdPaciente == undefined"
                        (click)="informacao('Paciente',IdPaciente)">
                        <mat-icon>info</mat-icon>
                    </button>
                </div>
                
                <div class="form-column max-width">
                    <div class="form-field">
                        <mat-form-field appearance="outline" *ngIf="!DeclaracaoStream">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.DATA' | translate }}" 
                                name="Data" disabled [(ngModel)]="DtaAtestado" maxlength="10">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column ">
                    <div class="form-field">
                        <ng-select [items]="ListaMedicos" 
                            placeholder="{{ 'TELADOCUMENTACAO.MEDICOS' | translate }} *"
                            bindLabel="nomeMedico" bindValue="idMedico" name="medicos" [selectOnTab]="true"
                            [(ngModel)]="IdMedico" [formControl]='medi' aria-required="true"
                            (blur)="ValidaMediDecl($any($event.target).value)" class="custom-select" 
                            *ngIf="!DeclaracaoStream">
                        </ng-select>
                        <div class="error-message" *ngIf="mediDeclVal == true">
                            {{ 'TELADOCUMENTACAO.TELACAMPO' | translate }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-divider"></div>
            
            <div class="form-row">
                <label class="form-label">{{ 'TELADOCUMENTACAO.COMPARECEUNOPERIUDO' | translate }}</label>
            </div>
            
            <div class="form-row">
                <div class="form-field full-width">
                    <mat-form-field appearance="outline">
                        <input matInput maxlength="50" name="periudo" 
                            placeholder="{{ 'TELADOCUMENTACAO.PERIUDO' | translate }}"
                            required [(ngModel)]="periudoDeclaracao">
                        <mat-error *ngIf="periudo.invalid">
                            {{getErrorMessageperiudo() | translate }}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
        </main>
        
        <footer class="modal-footer">
            <button class="action-button cancel-button" 
                (click)="ngxSmartModalService.getModal('InicializandoDeclaracao').close(); limparCampoDeclar()">
                <span>{{ 'TELADOCUMENTACAO.FECHAR' | translate }}</span>
            </button>
            
            <button class="action-button confirm-button" (click)="GerarDeclaracao()">
                <span>{{ 'TELADOCUMENTACAO.GERARESALVAR' | translate }}</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<!-- Modal Receituário -->
<ngx-smart-modal #InicializandoReceituario id="Receituariomodal" identifier="InicializandoReceituario"
    [dismissable]="false" [closable]="true" customClass="nsm-centered modern-modal health-modal emailmodal ">
    <div class="modal-container receituario">
        <header class="modal-header">
            <h3 class="modal-title">{{ 'TELADOCUMENTACAO.RECEITUARIO' | translate }}</h3>
        </header>
        
        <main class="modal-container">
            <div class="form-row">
                <div class="form-field full-width">
                    <div class="search-container">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOMEDICAMENTO' | translate }}" 
                                name="nomeRemedio" [(ngModel)]="nomeRemedio" 
                                (keyup.enter)="checkmedicamentosPro ? GetListaMedicamentosClinica() : GetListaMedicamentos()">
                            <button matSuffix mat-icon-button class="search-button" 
                                (click)="checkmedicamentosPro ? GetListaMedicamentosClinica() : GetListaMedicamentos()">
                                <mat-icon>search</mat-icon>
                            </button>
                        </mat-form-field>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="checkbox-field">
                    <mat-slide-toggle 
                        (change)="checkmedicamentosPro ? GetListaMedicamentosClinica() : GetListaMedicamentos()"
                        [(ngModel)]='checkmedicamentosPro' aria-checked="false">
                        {{ 'TELADOCUMENTACAO.CHECKMEDICAMENTOS' | translate }}
                    </mat-slide-toggle>
                </div>
            </div>
            
            <div class="form-row">
                <div class="medicine-list-container">
                    <ul class="medicine-list">
                        <li *ngFor="let item of DadosRaceitaRemedios"
                            (click)="addMedicamentos(item.produto, item.apresentacao)" class="medicine-item">
                            <span class="medicine-name">{{item.produto| truncate : 45 : "…"}}</span>
                            <span class="medicine-description">{{item.apresentacao | truncate : 45 : "…"}}</span>
                        </li>
                        <li class="load-more-item" *ngIf="(DadosRaceitaRemedios != undefined &&  DadosRaceitaRemedios.length > 0)
                            && !checkmedicamentosPro && flgBtnCarregarMais">
                            <button class="load-more-button" (click)="GetListaMedicamentos(true)">
                                Carregar Mais
                            </button>
                        </li>
                        <li class="empty-state" *ngIf="DadosRaceitaRemedios.length == 0">
                            <span>Não foi encontrado nenhum medicamento.</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline" *ngIf="!ReceituarioStream">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOPACIENTEPELOCPF' | translate }}" 
                                name="CPf" mask="000.000.000-00" [(ngModel)]="CPF" (change)="CarregaPaciente()">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field with-icon">
                        <ng-select [items]="ListaPaciente" 
                            placeholder="{{ 'TELADOCUMENTACAO.PACIENTE' | translate }} *"
                            bindLabel="nome" bindValue="idCliente" name="Paciente" [selectOnTab]="true"
                            [(ngModel)]="IdPaciente" [formControl]='paci' aria-required="true"
                            (blur)="ValidaPaciReceit($any($event.target).value)" class="custom-select" 
                            *ngIf="!ReceituarioStream">
                        </ng-select>
                        <div class="error-message" *ngIf="paciReceitVal == true">
                            {{ 'TELADOCUMENTACAO.TELACAMPO' | translate }}
                        </div>
                        <button *ngIf="!ReceituarioStream" mat-icon-button class="info-button attached-info"
                            title="{{'TELADOCUMENTACAO.INFORMACOESPACIENTE' | translate}}"
                            [disabled]="IdPaciente == 0 || IdPaciente == undefined"
                            (click)="informacao('Paciente',IdPaciente)">
                            <mat-icon>info</mat-icon>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column max-width">
                    <div class="form-field">
                        <mat-form-field appearance="outline" *ngIf="!ReceituarioStream">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.DATA' | translate }}" 
                                name="Data" disabled [(ngModel)]="DtaAtestado" maxlength="10">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column ">
                    <div class="form-field">
                        <ng-select [items]="ListaMedicos" 
                            placeholder="{{ 'TELADOCUMENTACAO.MEDICOS' | translate }} *"
                            bindLabel="nomeMedico" bindValue="idMedico" name="medicos" [selectOnTab]="true"
                            [(ngModel)]="IdMedico" [formControl]='medi' aria-required="true"
                            (blur)="ValidaMediReceit($any($event.target).value)" class="custom-select" 
                            *ngIf="!ReceituarioStream">
                        </ng-select>
                        <div class="error-message" *ngIf="mediReceitVal == true">
                            {{ 'TELADOCUMENTACAO.TELACAMPO' | translate }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-row textareaDiv" >
                <div class="form-field full-width">
                    <mat-form-field appearance="outline" style="height: 115px !important;">
                        <textarea matInput
                            [className]="ReceituarioStream ? 'textarea-field consultation' : 'textarea-field general'"
                            name="txtMensagem" id="txtReceita" [(ngModel)]="CampoReceita"></textarea>
                    </mat-form-field>
                </div>
            </div>
        </main>
        
        <footer class="modal-footer">
            <button class="action-button cancel-button" 
                (click)="ngxSmartModalService.getModal('InicializandoReceituario').close(); limparPaciReceit()">
                <span>{{ 'TELADOCUMENTACAO.FECHAR' | translate }}</span>
            </button>
            
            <button class="action-button confirm-button" (click)="GerarReceita()">
                <span>Gerar Receita</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>

<!-- Modal Informativo Paciente -->
<ngx-smart-modal #InforUsuarioDocumentacao identifier="InforUsuarioDocumentacao" [closable]="false"
    customClass="nsm-centered modern-modal health-modal info-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <h3 class="modal-title">Informações do {{DadosInformUsuario.tipoUsuario}}</h3>
        </header>
        
        <div class="modal-divider"></div>
        
        <main class="modal-container">
            <div class="form-row">
                <div class="form-field full-width">
                    <mat-form-field appearance="outline">
                        <input matInput placeholder="{{ 'TELADOCUMENTACAO.NOME' | translate }}" disabled 
                            name="Nome" [(ngModel)]="DadosInformUsuario.nome">
                    </mat-form-field>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.EMAIL' | translate }}" 
                                type="email" disabled name="Email" [(ngModel)]="DadosInformUsuario.email">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.CELULAR' | translate }}" 
                                disabled name="Celular" mask="(00) 00000-0000" 
                                [(ngModel)]="DadosInformUsuario.celular">
                        </mat-form-field>
                    </div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.TELEFONE' | translate }}" 
                                disabled name="Telefone" mask="(00) 00000-0000" 
                                [(ngModel)]="DadosInformUsuario.tel">
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.TELEFONECOM' | translate }}" 
                                disabled name="TelComerciar" [(ngModel)]="DadosInformUsuario.telComer">
                        </mat-form-field>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="modal-footer">
            <button class="action-button cancel-button full-width" (click)="InforUsuarioDocumentacao.close()">
                <span>{{ 'TELADOCUMENTACAO.SAIR' | translate }}</span>
            </button>
        </footer>
        
        <div class="modal-logo">
            <img class="logo-image" />
        </div>
    </div>
</ngx-smart-modal>