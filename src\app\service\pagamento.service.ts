import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EfetuarPagamento, RetornoPagamento, Pagamento } from '../model/efetuar-pagamento';
import { environment } from 'src/environments/environment';
import { take } from 'rxjs/operators';
import { Subject, Observable } from 'rxjs';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class PagamentoService {

    abrirModal: Subject<boolean> = new Subject<boolean>();
    idRecebedora?: number | null;
    idPagador?: number | null;
    valor?: number | null;
    flgConsulta: boolean = false;
    idconsulta?: number | null;
    setAbrirModal(abrirModal: boolean) {
        this.abrirModal.next(abrirModal);
    }

    getAbrirModal() {
        return this.abrirModal.asObservable();
    }

    clearAbrirModal() {
        this.abrirModal.next(false);
    }
    setIdPagador(idPagador: number) {
        this.idPagador = idPagador;
    }

    getFlgConsulta() {
        return this.flgConsulta;
    }


    setFlgConsulta(flgConsulta: boolean) {
        this.flgConsulta = flgConsulta;
    }

    getIdPagador() {
        return this.idPagador;
    }

    setIdconsulta(idconsulta: number) {
        this.idconsulta = idconsulta;
    }

    getIdconsulta() {
        return this.idconsulta;
    }
    setIdRecebedora(idRecebedora: number) {
        this.idRecebedora = idRecebedora;
    }

    getIdRecebedora() {
        return this.idRecebedora;
    }

    setValor(valor: number) {
        this.valor = valor;
    }

    getValor() {
        return this.valor;
    }

    clearValor() {
        this.valor = null;
    }

    clearIdRecebedora() {
        this.idRecebedora = null;
    }

    public changeconsulta$: EventEmitter<any>;
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService,
    ) {
        this.changeconsulta$ = new EventEmitter();
    }

    efetuarPagamento(objPagamento: EfetuarPagamento) {
        this.spinner.show();
        // return this.http.post<RetornoPagamento>(environment.apiEndpoint + '/TransacaoFinanceira/EfetuarPagamento', objPagamento);
        var reqHeader = new HttpHeaders({ 'No-Auth': 'True' });
        return this.http.post<RetornoPagamento>(environment.apiEndpoint + '/TransacaoFinanceira/EfetuarPagamento', objPagamento, { headers: reqHeader })
            .toPromise().then((res?: RetornoPagamento) => {
                if (this.getIdconsulta() && res!.idPagamento) {
                    this.changeconsulta$.emit(res);
                }
                this.spinner.hide();
                return res;
            });
    }

    carregarCartao(idUsuario:any) {
        this.spinner.show();
        var reqHeader = new HttpHeaders({ 'No-Auth': 'True' });
        let dados = this.http.get(environment.apiEndpoint + '/TransacaoFinanceira/carregarCartao/' + idUsuario, { headers: reqHeader });
        this.spinner.show();
        return dados;
    }

    carregarPagamentosGerados(): Observable<any> {
        this.spinner.show();
        return this.http.get<any>(environment.apiEndpoint + '/Pagamento/GetPagamentos');
    }

    carregarPagamentosRecebidos(): Observable<any> {
        this.spinner.show();
        return this.http.get<any>(environment.apiEndpoint + '/Pagamento/GetRecebimentos');
    }
    carregarPagamentos() {
        this.spinner.show();

        return this.http.get<Pagamento[]>(environment.apiEndpoint + '/TransacaoFinanceira/CarregarTransacoes')
            .pipe(take(1));
    }

    carregarPagamentosGrafico() {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Pagamento')
            .pipe(take(1));
    }
}