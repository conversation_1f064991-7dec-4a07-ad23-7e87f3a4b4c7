import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { msgResposta } from '../model/retorno-resposta';
import { novaReceita } from '../model/receitas';
import { LocalStorageService } from '../service/LocalStorageService';
import { ReceitaService } from '../service/Receita.Service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-cadastro-receitas',
    templateUrl: './cadastro-receitas.component.html',
    styleUrls: ['./cadastro-receitas.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCard,
      MatFormFieldModule,
      MatIcon,
      TranslateModule
    ]
})
export class CadastroReceitasComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private localStorageService: LocalStorageService,
    private receitaService: ReceitaService,
    private router: Router,
  ) { }
  idReceita?: number;
  retorno?: msgResposta;
  listaReceitas: novaReceita[] = [];
  flgmodal: boolean = false;
  ImagemPessoa: any = "assets/build/img/userdefault.png";

  ngOnInit() {
    this.GetReceitas();
  }

  GetReceitas() {
    this.receitaService.GetReceitas().subscribe((retorno) => {
      this.listaReceitas = retorno;
      this.spinner.hide();
    }, erro => {
      console.error(erro)
      this.spinner.hide();
    }
    )
  }

  excluirReceita(id: number) {
    this.idReceita = id;
    this.flgmodal = true;
  }

  Excluir(selecao: boolean) {
    this.flgmodal = false
    if (selecao) {
      this.receitaService.DeleteReceita(this.idReceita!).subscribe((retorno) => {
        this.retorno = retorno;
        this.spinner.hide();
        this.GetReceitas();
      }, erro => console.error(erro))
    }
  }

  Editar(id: number) {
    this.localStorageService.idReceita = id;
    this.adicionarreceitas();
  }

  adicionarreceitas() {
    this.router.navigate(['/adicionarreceitas']);
  }

  filtroBusca: string = '';

  filtrarReceitas() {
    if (this.filtroBusca.trim() !== '') {
      this.listaReceitas = this.listaReceitas.filter(local => {
        return (
          local.nomPaciente!.toLowerCase().includes(this.filtroBusca.toLowerCase())
        );
      });
    } else {
      this.GetReceitas();
    }
  }
}
