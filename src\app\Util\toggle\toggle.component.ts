import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-toggle',
    templateUrl: './toggle.component.html',
    styleUrls: ['./toggle.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule
    ]
})
export class ToggleComponent implements OnInit {

  constructor(
  ) { }

  ngOnInit(): void {
  }

  @Input('flg') flg: boolean = false;
  // @Output('flgChange') public flg$: EventEmitter<boolean> = new EventEmitter();

  @Input() invertido: boolean = false;
  @Input() desabilitado: boolean = false;
  @Input('texto') texto: string = '';
  @Input('info') info: string = '';
  @Input('id') id: number = 0;

}
