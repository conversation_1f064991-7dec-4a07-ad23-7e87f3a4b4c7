import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { environment } from 'src/environments/environment';
import { LocalStorageService } from '../service/LocalStorageService';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ConsultaService } from '../service/consulta.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CriptografarUtil } from '../Util/Criptografar.util';
import { SignalHubGuestService } from '../service/signalHub-guest.service';
import { RelatorioExameApiService } from '../service/relatorio-exame-api.service';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { ModalConfirmacaoComponent } from '../Modais/modal-confirmacao/modal-confirmacao.component';

@Component({
  selector: 'app-streaming-paciente',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTooltipModule
  ],
  templateUrl: './streaming-paciente.component.html',
  styleUrls: ['./streaming-paciente.component.scss']
})
export class StreamingPacienteComponent implements OnInit, OnDestroy {

  FlgExibeInfo: boolean = true;

  // Variáveis principais (removido IdConsulta)
  FlgVideo: boolean = false;
  LinkVideo?: string;
  urlSafe?: SafeResourceUrl;
  sala?: string;

  // Dados do paciente
  nomePaciente: string = '';
  isLoggedIn: boolean = false;

  // Dados do questionário e coleta
  dadosQuestionario: any = null;
  dadosColeta: any = null;
  tokenPaciente: string = '';
  
  // Estados da interface
  videoConnected: boolean = false;
  micMuted: boolean = true;
  cameraOff: boolean = false;
  consultaFinalizada: boolean = false;
  
  // Timer da consulta
  tempoConsulta: string = '00:00';
  private timerInterval?: any;
  private segundosTotal: number = 0;

  constructor(
    private router: Router,
    private sanitizer: DomSanitizer,
    private localStorageService: LocalStorageService,
    private usuarioLogadoService: UsuarioLogadoService,
    private consultaService: ConsultaService,
    private spinner: SpinnerService,
    private alertComponent: AlertComponent,
    private signalHubGuestService: SignalHubGuestService,
    public matDialog: MatDialog,
    private relatorioExameApiService: RelatorioExameApiService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.consultaService;
    this.spinner;
    this.processarDadosCriptografados;

    this.verificarAutenticacao();
    this.carregarDadosConsulta();
    this.configurarSignalR();
    this.atualizarStatusConsulta();
    this.iniciarTimer();
  }

  ngOnDestroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    // Opcional: resetar status quando o componente é destruído
    this.resetarStatusConsulta();
  }

  private configurarSignalR() {
    // Conectar o paciente usando seu token
    if (this.tokenPaciente) {
      this.signalHubGuestService.connectGuestUser(this.tokenPaciente);
    }

    // Configurar listener para solicitações de dados do médico
    this.signalHubGuestService.OnSolicitacaoDadosPaciente.subscribe((solicitacao: any) => {
      console.log('📋 Solicitação de dados recebida do médico:', solicitacao);

      // Enviar dados automaticamente para o médico
      this.enviarDadosParaMedico(solicitacao.medicoId);
    });
  }

  private enviarDadosParaMedico(medicoId: string) {
    try {
      // Preparar dados do paciente para envio
      const dadosParaEnvio = {
        tokenPaciente: this.tokenPaciente,
        questionario: this.dadosQuestionario,
        coleta: this.dadosColeta,
        dadosPessoais: {
          nome: this.nomePaciente,
          isLoggedIn: this.isLoggedIn
        },
        timestamp: new Date().toISOString()
      };

      console.log('📤 Enviando dados para o médico:', dadosParaEnvio);

      // Enviar dados via SignalR
      this.signalHubGuestService.enviarDadosParaMedico(medicoId, dadosParaEnvio);

      console.log('✅ Dados enviados com sucesso para o médico');
    } catch (error) {
      console.error('❌ Erro ao enviar dados para o médico:', error);
    }
  }

  private verificarAutenticacao() {
    this.isLoggedIn = this.usuarioLogadoService.isLogged();

    if (this.isLoggedIn) {
      this.nomePaciente = this.usuarioLogadoService.getNomeUsuario() || 'Paciente';
    } else {
      // Para usuários não logados, tentar carregar nome do questionário criptografado
      try {
        const dadosQuestionario = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');
        if (dadosQuestionario) {
          const dados = JSON.parse(dadosQuestionario);
          this.nomePaciente = dados.nome || 'Paciente';
        } else {
          this.nomePaciente = 'Paciente';
        }
      } catch (error) {
        console.error('Erro ao carregar nome do paciente:', error);
        this.nomePaciente = 'Paciente';
      }
    }
  }

  /**
   * Atualiza o status do paciente para "em consulta"
   */
  private atualizarStatusConsulta() {
    // Obter token do paciente da URL ou localStorage
    const token = this.obterTokenPaciente();

    if (!token) {
      console.warn('⚠️ Token do paciente não encontrado - não foi possível atualizar status');
      return;
    }

    console.log('📋 Atualizando status do paciente para "em consulta"...');

    this.relatorioExameApiService.updatePatientStatus(token, 'em_consulta').subscribe({
      next: (response) => {
        if (response.success) {
          console.log('✅ Status do paciente atualizado com sucesso:', {
            paciente: response.patientName,
            statusAnterior: response.previousStatus,
            novoStatus: response.newStatus
          });

          // Opcional: mostrar notificação sutil para o paciente
          // this.alertComponent.infoSnackbar('Consulta iniciada - status atualizado');
        } else {
          console.warn('⚠️ Falha ao atualizar status do paciente:', response.message);
        }
      },
      error: (error) => {
        console.error('❌ Erro ao atualizar status do paciente:', error);
        // Não mostrar erro para o paciente, pois é uma operação em background
      }
    });
  }

  /**
   * Obtém o token do paciente da URL ou localStorage
   */
  private obterTokenPaciente(): string | null {
    // Primeiro, tentar obter da URL (query parameters)
    this.route.queryParams.subscribe(params => {
      if (params['token']) {
        this.tokenPaciente = params['token'];
      }
    });

    if (this.tokenPaciente) {
      console.log('🔑 Token obtido da URL/componente:', this.tokenPaciente);
      return this.tokenPaciente;
    }

    // Se não encontrou na URL, tentar localStorage criptografado
    const token = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');
    if (token) {
      console.log('🔑 Token obtido do localStorage criptografado:', token);
      return token;
    }

    // Tentar localStorage não criptografado como fallback
    const tokenFallback = localStorage.getItem('tokenPaciente');
    if (tokenFallback) {
      console.log('🔑 Token obtido do localStorage (fallback):', tokenFallback);
      return tokenFallback;
    }

    console.warn('⚠️ Token do paciente não encontrado em nenhuma fonte');
    return null;
  }

  /**
   * Reseta o status do paciente para "ativo" quando a consulta termina
   */
  private resetarStatusConsulta() {
    const token = this.obterTokenPaciente();

    if (!token) {
      return; // Não há token, não é possível resetar
    }

    console.log('🔄 Resetando status do paciente para "ativo"...');

    this.relatorioExameApiService.updatePatientStatus(token, 'ativo').subscribe({
      next: (response) => {
        if (response.success) {
          console.log('✅ Status do paciente resetado com sucesso:', {
            paciente: response.patientName,
            statusAnterior: response.previousStatus,
            novoStatus: response.newStatus
          });
        }
      },
      error: (error) => {
        console.error('❌ Erro ao resetar status do paciente:', error);
        // Não mostrar erro, pois é uma operação em background durante destruição
      }
    });
  }

  private carregarDadosConsulta() {
    try {
      const questionarioData = CriptografarUtil.obterLocalStorageCriptografado('questionario-pre-consulta');
      if (questionarioData) {
        try {
          this.dadosQuestionario = typeof questionarioData === 'string' ? JSON.parse(questionarioData) : questionarioData;
        } catch (parseError) {
          console.warn('⚠️ Erro ao fazer parse do questionário, usando dados brutos:', parseError);
          this.dadosQuestionario = questionarioData;
        }
      } 

      const coletaData = CriptografarUtil.obterLocalStorageCriptografado('VittalTecDados');
      if (coletaData) {
        try {
          this.dadosColeta = typeof coletaData === 'string' ? JSON.parse(coletaData) : coletaData;
        } catch (parseError) {
          console.warn('⚠️ Erro ao fazer parse da coleta, usando dados brutos:', parseError);
          this.dadosColeta = coletaData;
        }
      }

      const tokenFila = CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');
      if (tokenFila)
        this.tokenPaciente = typeof tokenFila === 'string' ? tokenFila : String(tokenFila);

      if (this.dadosQuestionario || this.tokenPaciente) {
        this.iniciarVideoconferencia();
      } else {
        console.error('❌ Dados insuficientes para iniciar videoconferência');
        this.alertComponent.falhaSnackbar('Erro: Dados do paciente não encontrados');
        this.voltarParaFilaEspera();
      }

    } catch (error) {
      console.error('❌ Erro ao carregar dados:', error);
      console.error('Stack trace:', error);
      this.alertComponent.falhaSnackbar('Erro ao carregar dados do paciente');
      this.voltarParaFilaEspera();
    }
  }

  private iniciarVideoconferencia() {
    try {
      // Gerar sala baseada no token do paciente ou timestamp
      const identificadorSala = this.tokenPaciente || Date.now().toString();
      this.sala = "medicinaparavoce" + identificadorSala;
      this.FlgVideo = true;

      if (window.location.hostname === "camposverdes.medicinaparavoce.com.br") {
        this.LinkVideo = `${environment.endPointTokboxCamposVerdes}&room=${this.sala}&iframe=true&userType=patient`;
      } else if (window.location.hostname === "atendimento.medicinaparavoce.com.br") {
        this.LinkVideo = `${environment.endPointTokboxCovid}&room=${this.sala}&iframe=true&userType=patient`;
      } else {
        this.LinkVideo = `${environment.endPointTokbox}&room=${this.sala}&iframe=true&userType=patient`;
      }

      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.LinkVideo);
      this.videoConnected = true;


    } catch (error) {
      console.error('Erro ao iniciar videoconferência:', error);
      this.alertComponent.falhaSnackbar('Erro ao conectar com o médico');
    }
  }

  private iniciarTimer() {
    this.timerInterval = setInterval(() => {
      this.segundosTotal++;
      const minutos = Math.floor(this.segundosTotal / 60);
      const segundos = this.segundosTotal % 60;
      this.tempoConsulta = `${minutos.toString().padStart(2, '0')}:${segundos.toString().padStart(2, '0')}`;
    }, 1000);
  }

  finalizarConsulta() {
    const dialogRef = this.matDialog.open(ModalConfirmacaoComponent, {
      data: 'Tem certeza que deseja finalizar a consulta?'
    });

    dialogRef.afterClosed().subscribe((result: boolean) => {
      if (result) {
        this.consultaFinalizada = true;
        this.FlgVideo = false;
        
        if (this.timerInterval) {
          clearInterval(this.timerInterval);
        }

        // Limpar dados da consulta
        this.localStorageService.clearByName('Consulta');
        
        // Redirecionar baseado no status de login
        if (this.isLoggedIn) {
          this.router.navigate(['/pre-consulta-questionario']);
        } else {
          this.voltarParaInicio();
        }
      }
    });
  }

  voltarParaFilaEspera() {
    this.router.navigate(['/fila-espera']);
  }

  private voltarParaInicio() {
    // Limpar dados temporários
    localStorage.removeItem('questionario-pre-consulta');
    this.router.navigate(['/pre-consulta-quesionario']);
  }

  finalizarERedirecionarFinal() {
    if (this.isLoggedIn) {
      this.router.navigate(['/pre-consulta-quesionario']);
    } else {
      this.voltarParaInicio();
    }
  }

  // Getter para status da conexão
  get statusConexao(): string {
    if (this.consultaFinalizada) return 'Consulta Finalizada';
    if (this.videoConnected) return 'Conectado';
    return 'Conectando...';
  }

  // Getter para cor do status
  get corStatus(): string {
    if (this.consultaFinalizada) return '#6c757d';
    if (this.videoConnected) return '#28a745';
    return '#ffc107';
  }

  // Métodos para acessar dados carregados
  getDadosQuestionario() {
    return this.dadosQuestionario;
  }

  getDadosColeta() {
    return this.dadosColeta;
  }

  getTokenPaciente() {
    return this.tokenPaciente;
  }

  // Método para obter resumo completo dos dados do paciente
  getResumoCompletoPaciente() {
    const resumo = {
      dadosPessoais: {
        nome: this.nomePaciente,
        isLoggedIn: this.isLoggedIn,
        token: this.tokenPaciente
      },
      questionario: this.dadosQuestionario,
      coleta: this.dadosColeta,
      videoconferencia: {
        sala: this.sala,
        conectado: this.videoConnected,
        tempoConsulta: this.tempoConsulta
      }
    };

    return resumo;
  }

  // Método para verificar se todos os dados necessários estão carregados
  get dadosCompletos(): boolean {
    return !!(this.dadosQuestionario || this.tokenPaciente);
  }

  // Método para obter dados formatados para exibição
  getDadosFormatados() {
    const dados: any = {};

    // Dados do questionário
    if (this.dadosQuestionario) {
      dados.questionario = {
        nome: this.dadosQuestionario.nome || 'Não informado',
        cpf: this.dadosQuestionario.cpf || 'Não informado',
        email: this.dadosQuestionario.email || 'Não informado',
        telefone: this.dadosQuestionario.telefone || 'Não informado',
        sintomas: this.dadosQuestionario.sintomas || 'Não informado',
        intensidadeDor: this.dadosQuestionario.intensidadeDor || 'Não informado',
        alergias: this.dadosQuestionario.alergias || 'Nenhuma',
        observacoes: this.dadosQuestionario.observacoes || 'Nenhuma'
      };
    }

    // Dados da coleta
    if (this.dadosColeta) {
      dados.coleta = {
        pressaoArterial: this.dadosColeta.pressaoArterial || 'Não coletado',
        frequenciaCardiaca: this.dadosColeta.frequenciaCardiaca || 'Não coletado',
        temperatura: this.dadosColeta.temperatura || 'Não coletado',
        saturacaoOxigenio: this.dadosColeta.saturacaoOxigenio || 'Não coletado',
        peso: this.dadosColeta.peso || 'Não coletado',
        altura: this.dadosColeta.altura || 'Não coletado'
      };
    }

    return dados;
  }

  // Método utilitário para processar dados criptografados
  private processarDadosCriptografados(dados: any, nomeChave: string): any {
    if (!dados) {
      return null;
    }

    try {
      if (typeof dados === 'string') {
        if (dados.startsWith('{') || dados.startsWith('[')) {
          return JSON.parse(dados);
        } else {
          return dados;
        }
      }

      // Se já é objeto, retornar diretamente
      return dados;

    } catch (parseError) {
      console.warn(`⚠️ Erro ao processar ${nomeChave}, usando dados brutos:`, parseError);
      return dados;
    }
  }
}
