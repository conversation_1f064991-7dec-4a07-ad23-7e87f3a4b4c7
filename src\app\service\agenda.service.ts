import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { TipoAgendamento, AgendaEspera } from '../model/agenda';
import { Consulta } from '../model/consulta';
import { Whatssap } from '../model/whats';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class AgendaService {

    public atualizaDadosMes$: EventEmitter<any>;


    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {

        this.atualizaDadosMes$ = new EventEmitter()
    }

    reqHeader = new HttpHeaders({ 'No-Auth': 'True' });

    public headers = new Headers({ 'Content-Type': 'application/json' });


    public GetAgenda(idMedico:any, TipoUsuario:any, Data:any, TipoPesquisa:any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('idMedico', String(idMedico));
        params = params.append('TipoUsuario', String(TipoUsuario));
        params = params.append('Data', String(Data));
        params = params.append('TipoPesquisa', String(TipoPesquisa));
        return this.http.get(environment.apiEndpoint + '/Agenda/GetAgendaConsulta', { params });
    }

    // public GetTipoAgendamentos(idMedico, TipoUsuario): Observable<any> {
    //     let params = new HttpParams();
    //     params = params.append('idMedico', String(idMedico));
    //     params = params.append('TipoUsuario', String(TipoUsuario));
    //     return this.http.get(environment.apiEndpoint + '/Agenda/GetTipoAgendamentos', { params });
    // }

    public GetTipoAgendamentos(inicio:any, fim:any, pesquisa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/TipoAgendamento', { params });
    }

    public getTipoAgendamento(idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/TipoAgendamento/GetTodosTiposAgendamentos', { params });

    }

    public salvarTipoAgendamento(tipoagendamento: TipoAgendamento): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/TipoAgendamento', tipoagendamento);
    }

    public InativarTipoAgendamento(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/TipoAgendamento/' + id + '/' + idUsuario);
    }
    public salvarAgendamento(agenda: Consulta): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Agenda/salvarAgendamento', agenda);
    }

    public EnvioCodAcesso(codAcesso:any) {
        // this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/agenda/EnvioCodAcesso', codAcesso);
    }

    // public salvarAgendamento(agenda: Agenda) {


    //     return this.http.post(environment.apiEndpoint + '/Agenda/salvarAgendamento', agenda)
    //         .toPromise().then((res: any) => {

    //             if (res == true)
    //                 this.atualizaDadosMes$.emit(res)

    //             return res;

    //         });
    // }

    public salvarAgendaEspera(agenda: AgendaEspera): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Agenda/salvarAgendaEspera', agenda);
    }

    public EnviarWhats(whats: Whatssap): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Agenda/envioWhats', whats);
    }


    public GetAgendaEspera(Data:any, idMedico:any): Observable<any> {
        Data;
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idMedico', String(idMedico));
        return this.http.get(environment.apiEndpoint + '/Agenda/GetAgendaEspera', { params });
    }
    public InativarAgendaEspera(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('id', String(id));
        params = params.append('idUsuario', String(idUsuario));
        return this.http.get(environment.apiEndpoint + '/Agenda/InativarAgendaEspera', { params });
    }
    
    public GetDiasAgenda(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Agenda/GetDiasAgenda/' + id);
    }


    public GetCancelamento(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Agenda/GetCancelamento/' + id);
    }
    
    
    public AgendaFilaEspera(idPaciente:any, idClinica:any, CodAcesso:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idPaciente', String(idPaciente));
        params = params.append('idClinica', String(idClinica));
        params = params.append('CodAcesso', String(CodAcesso));
        return this.http.delete(environment.apiEndpoint + '/Agenda/salvarAgendaFilaAcesso', { params, headers: this.reqHeader });
    }
    
    
    public AgendaPessoaSolicitante(idPessoa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idPessoa', String(idPessoa));
        params = params.append('idClinica', String(idClinica));
        return this.http.delete(environment.apiEndpoint + '/Agenda/salvarAgendaSolicitacao', { params });
    }
    
    
    
    public InativarAgendamento(id:any, IdUsuarioCancelamento:any, Motivo:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('id', String(id));
        params = params.append('IdUsuarioCancelamento', String(IdUsuarioCancelamento));
        params = params.append('Motivo', String(Motivo));
        return this.http.delete(environment.apiEndpoint + '/Agenda/InativarAgendamento', { params });
    }
    
    
    public getAgendaEdit(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Agenda/' + id);
    }
}
