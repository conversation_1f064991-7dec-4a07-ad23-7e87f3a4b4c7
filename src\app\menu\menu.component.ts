import { MedicoService } from 'src/app/service/medico.service';
import { AnaliseService } from './../service/analise.service';
import { Component, LOCALE_ID, HostListener, TemplateRef, ViewChild } from '@angular/core';
import { AppComponent } from '../app.component';
import { Router, RouterModule } from '@angular/router';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { ConsultaService } from '../service/consulta.service';
import { ValidacaoService } from '../service/validacao.service';
import { UsuarioService } from '../service/usuario.service';
import { ClinicaService } from '../service/clinica.service';
import { TelemedicinaComponentBase } from '../Util/component.base';
import { Adapter } from '../service/adapter';
import { SignalRGroupAdapter } from '../service/signalrGrupAdapter';
import { SignalRAdapter } from '../service/signalrapadter';
import { HttpClient } from '@angular/common/http';
import { TranslateModule, TranslatePipe, TranslateService } from '@ngx-translate/core';
import { EnvioEmailService } from '../service/envioEmail.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { documentosModalService } from '../documentacao/documentocao.service';
import { documentosModal } from '../model/DocumentosModal';
import { EnumTipoDocumentos } from '../Util/tipoDocumentos';
import { irParaConsulta } from '../model/consulta';
import { PagamentoService } from '../service/pagamento.service';
import { CommonModule, registerLocaleData } from '@angular/common';
import localePtBr from '@angular/common/locales/pt';
import { guiaModalService } from '../documentacao/guia-exame/guia-exame.service';
import { SignalHubService } from '../service/signalHub.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { AgendaService } from '../service/agenda.service';
import { ExamesModalService } from '../documentacao/cadastro-exame/cadastro-exame.service';
import { ChatService } from '../service/chat.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { ControleModaisService } from '../service/controle-modais.service';
import { GuiaExameComponent } from '../documentacao/guia-exame/guia-exame.component';
import { PagamentoComponent } from '../pagamento/pagamento.component';
import { PesquisaCidComponent } from '../pesquisa-cid/pesquisa-cid.component';
import { DocumentacaoComponent } from '../documentacao/documentacao.component';
import { CadastroExameComponent } from '../documentacao/cadastro-exame/cadastro-exame.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatDrawer, MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { ColunadireitaComponent } from '../colunadireita/colunadireita.component';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { MatListModule } from '@angular/material/list';
import { ChatAdapter, IChatParticipant } from 'ng-chat';
import { MatCardModule } from '@angular/material/card';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CalendarDateFormatter, DateAdapter as CalendarDateAdapter } from 'angular-calendar';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatNativeDateModule } from '@angular/material/core';
import { adapterFactory } from 'angular-calendar/date-adapters/date-fns';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { Location } from '@angular/common';


// Registrar localização PT-BR
registerLocaleData(localePtBr);

// Formatos de data personalizados para o Material
export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: 'app-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss'],
  standalone: true,
  providers: [
    // Provedores para o Angular Material DateAdapter
    { provide: MAT_DATE_LOCALE, useValue: 'pt-BR' },
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS },
    
    // Provedores para o angular-calendar DateAdapter
    CalendarDateFormatter,
    { 
      provide: CalendarDateAdapter, 
      useFactory: adapterFactory,
      deps: [LOCALE_ID]
    },
    { provide: LOCALE_ID, useValue: 'pt-BR' }
  ],
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxSmartModalModule,
    MatIconModule,
    MatBadgeModule,
    MatDrawer,
    MatDrawerContent,
    MatDrawerContainer,
    MatToolbarModule,
    MatFormFieldModule,
    MatLabel,
    TranslateModule,
    MatDividerModule,
    GuiaExameComponent,
    PagamentoComponent,
    PesquisaCidComponent,
    DocumentacaoComponent,
    CadastroExameComponent,
    MatMenuModule,
    RouterModule,
    ColunadireitaComponent,
    TruncatePipe,
    MatListModule,
    MatCardModule,
    TranslatePipe,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
  ]
})
export class MenuComponent extends TelemedicinaComponentBase {
  // Resto do seu código...
  screenWidth: number;
  showFiller?: boolean;
  FillerOpen = false;
  menuOpen = false;
  guiasMenuOpen = false;
  relatoriosMenuOpen = false;
  faturamentoMenuOpen = false;
  // stream$ = this.streamService.telastream$.asObservable();
  stream = false;
  qtdMsgChat = 0
  flgExibeQtdMsg: boolean = false;

  AtualizarQtdMsgChat() {
    this.chatService.CarregaQtdMsgNLidas().subscribe((ret) => {
      this.qtdMsgChat = ret.num!;
      if (ret.num! > 0) {
        this.chatService.geraNotificacao();
        this.flgExibeQtdMsg = true;
      }
      else
        this.flgExibeQtdMsg = false;

      
    })
  }

  toggleMenu() {
    this.menuOpen = !this.menuOpen;
  }

  toggleFaturaMenu() {
    this.faturamentoMenuOpen = !this.faturamentoMenuOpen;
  }

  toggleRelatoriosMenu() {
    this.relatoriosMenuOpen = !this.relatoriosMenuOpen;
  }

  toggleGuiasMenu() {
    this.guiasMenuOpen = !this.guiasMenuOpen;
  }


  constructor(
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private appc: AppComponent,
    private router: Router,
    private consultaService: ConsultaService,
    private clinicaService: ClinicaService,
    public validador: ValidacaoService,
    private usuarioService: UsuarioService,
    private tradutor: TranslateService,
    private http: HttpClient,
    private medicoService: MedicoService,
    private AnaliseService: AnaliseService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private emailService: EnvioEmailService,
    private usuarioLogadoService: UsuarioLogadoService,
    private documentosServiceModal: documentosModalService,
    private pagamentoService: PagamentoService,
    private GuiaModalService: guiaModalService,
    private signalHubService: SignalHubService,
    private localStorageService: LocalStorageService,
    private agendaService: AgendaService,
    private examesModalService: ExamesModalService,
    private chatService: ChatService,
    private controleModais : ControleModaisService,
    private location: Location

  ) {

    super();

    this.screenWidth = window.innerWidth;
    window.onresize = () => {
      this.screenWidth = window.innerWidth;
      if (this.screenWidth < 991)
        this.showFiller = true;
      else
        this.showFiller = false;
    };


    this.usuarioService.changeImage$
      .subscribe((perf: any) => {
        if (perf.imagem64 != null && perf.imagem64 != '') {
          this.ImagemPessoa = ''
          this.ImagemPessoa = perf.imagem64;
        }
        this.Nome = perf.nomePessoa;
      });

    this.medicoService.atualizaMensagemDia$
      .subscribe((resp: any) => {
        if (resp) {
          this.RecadodoDia()
        } 
      });

    this.signalHubService.OnChamaPacienteFilaOrientacao
      .subscribe((idConsulta) => {
        this.Carregaconsultas(idConsulta);
      });

    this.signalHubService.OnDisparaAlertConsulta
      .subscribe(() => {
        this.CarregaconsultasCoracao();
      });

    this.signalHubService.OnDisparaAlertFila
      .subscribe(() => {
        this.CarregaconsultasCoracao();
      });

    this.signalHubService.OnDisparaAlertFilaClinica
      .subscribe(() => {
        this.CarregaconsultasCoracao();
      });


    this.signalHubService.OnVisualizouMensagem
      .subscribe(() => {
        this.RecadodoDia()
      });


    this.signalHubService.OnMedicosLogados
      .subscribe(() => {
      });


    this.signalHubService.OnLogando
      .subscribe(() => {
        this.Logoff();
      });

    this.signalHubService.OnAtualizaChamaPacienteFila
      .subscribe(() => {
        this.carregaFila();
      });


    this.signalHubService.OnAtualizaMedicosOnline
      .subscribe(() => {
        this.CarregamedicosOnline();
      });
    this.signalHubService.OnAtualizaMensagem
      .subscribe(() => {
        this.AtualizarQtdMsgChat();
      });
    this.signalHubService.OnAtualizaQtdMensagem
      .subscribe(() => {
        this.AtualizarQtdMsgChat();
      });

    this.locale = this.tradutor.store.currentLang;
  }


  @ViewChild('drawer') drawer: any;
  ToggleDrawer() {

    this.drawer.toggle();

  }
  @ViewChild('emailsSistemaNovo') emailsSistemaNovo?: TemplateRef<any>;
  @ViewChild('emailUsuarioNovo') emailUsuarioNovo?: TemplateRef<any>;


  Motivocancelameto: string = "";
  idCancela?: number;
  cancelamento: boolean = false;

  flgChat: boolean = false;
  flgSolicitacao: boolean = false;
  flgReuniao: boolean = false;
  MedicosOnline?: number = 0
  ConsultaAgoraTeste = false;
  Objconsulta: any = [];

  PosicaoFila: number = 0
  idConsulta?: number

  UsuarioRecado: string = '';
  DesRecadoDia: string = '';
  DadosRecado: any = [];
  RecadoDia: boolean = false;
  QuantidadeConsultas: number = 0;
  public UsuarioAtendentes?: IChatParticipant[];
  title = 'app';
  userId: string = "offline-demo";
  users: any = [];
  public adapter: ChatAdapter = new Adapter();


  currentTheme = 'dark-theme';
  triggeredEvents:any = [];
  fileUploadUrl: string = `${SignalRAdapter.serverBaseUrl}/Chat2/UploadFile`;


  signalRAdapter?: SignalRGroupAdapter;
  // usuario: Usuario;

  ChatClick: boolean = false;
  locale: string = ''; 
  ChatIf: boolean = false;
  dashboard: boolean = false;
  nomeClinica: string = ""
  ImagemClinica: string = ""
  DadosClinicas: any = [];
  Nome: string = '';
  tipoUsuario: string = '';
  ImagemPessoaConsulta: any = "assets/build/img/avatar-medico.png";
  ImagemPessoa: any = "assets/build/img/userdefault.png";
  Notificacoes: any;
  panelOpenState?: boolean;
  panelOpenState2?: boolean;
  mostrarMenuLaterais = false;

  AdmPermissao = false;
  MedicoPermissao = false;
  AtendentePermissao = false;
  PacientePermissao = false;
  DadosClinicasUser: any = [];

  totalPendencaias = 0;
  textoPendencias?: string;
  CartasBoasvindas = 0;
  DadosUsuario: any;
  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';



  // Variáveis para ícone do coração
  usuarioConsulta: any = []
  //usuario: any;
  //tipoUsuario: string;
  idEmail = 0;
  consultasCoracao = [];
  timeInterval: any;
  consultaAgora = false;
  Coracao: any = "assets/build/img/avatar-medico.png";
  isMobile: boolean = false;
  isHubConnected: boolean = false;
  totalNotificacoes: number = 0;
  privaci = false
  mostrarMenu = false;
  recuperarSenha = false;
  stream2pront = false;
  stream1 = false;
  conect = false;

  documentacao: string = '';
  Chekin: boolean = false;
  ConsultaChekin = [];

  flgMultiClinicas: boolean = false;
  cpfUsuario:any;
  idClinicaUsuario:any;
  flgApareceMenu?: boolean;
  dialogRef: any;



  ngOnInit() {

    this.AtualizarQtdMsgChat();

    this.cpfUsuario = this.usuarioLogadoService.getCpf();
    this.idClinicaUsuario = this.usuarioLogadoService.getIdUltimaClinica()
    this.userId = this.usuarioLogadoService.getIdUsuarioAcesso()!.toString();

    if (this.usuarioLogadoService.getFlgHabilitaChat())
      this.flgChat = this.usuarioLogadoService.getFlgHabilitaChat()!;
    if (this.usuarioLogadoService.getFlgSolicitaOrientacao())
      this.flgSolicitacao = this.usuarioLogadoService.getFlgSolicitaOrientacao()!
    if (this.usuarioLogadoService.getFlgReuniao())
      this.flgReuniao = this.usuarioLogadoService.getFlgReuniao()!



    if (this.usuarioLogadoService.getClinicas()) {
      var clinicas = this.usuarioLogadoService.getClinicas()!;
      this.spinner.hide();
      if (clinicas.length > 1)
        this.flgMultiClinicas = true;
    }
    this.Nome = this.usuarioLogadoService.getNomeUsuario()!

    if (this.usuarioLogadoService.getImagem64() != '' && this.usuarioLogadoService.getImagem64() != null)
      this.ImagemPessoa = this.usuarioLogadoService.getImagem64()

    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM) {
      this.tipoUsuario = 'ADM Sistema';
      this.AdmPermissao = true;
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      this.CarregaQuantBoasVindas()

    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente) {
      this.tipoUsuario = 'Atendente';
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      this.AdmPermissao = false;
      // this.CarregaconsultasCoracao();
      this.CarregaQuantBoasVindas()

    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.tipoUsuario = 'Médico';
      this.MedicoPermissao = true;
      this.AdmPermissao = false;
      this.AtendentePermissao = false;
      this.PacientePermissao = true;
      this.RecadodoDia()
      if (this.isMobile) {
        this.CarregaconsultasCoracao();
      }
      this.carregaQtdPreferencias();

    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
      this.tipoUsuario = 'Paciente';
      this.MedicoPermissao = false;
      this.AdmPermissao = false;
      this.AtendentePermissao = false;
      this.PacientePermissao = true;
      // if (this.isMobile) {
      this.CarregaconsultasCoracao();
      // }


    }





    const userName = this.usuarioLogadoService.getNomeUsuario()!
    const userClinica = this.usuarioLogadoService.getIdUltimaClinica()!.toString()
    this.signalRAdapter = new SignalRGroupAdapter(userName, userClinica, this.http);

    this.CarregaClinicas();
    this.CarregarNot();

    if (JSON.parse(sessionStorage.getItem('Logado')!)) {
      ;
      this.mostrarMenu = true;
      if (sessionStorage.getItem('TelaStream') == 'true')
        this.Telastream(JSON.parse(sessionStorage.getItem('TelaStream')!));
      else if (sessionStorage.getItem('Conect') == 'true')
        this.TesteConecao(JSON.parse(sessionStorage.getItem('Conect')!));

      else if (sessionStorage.getItem('TelaStream1') == 'true')
        this.Telastream1(JSON.parse(sessionStorage.getItem('TelaStream1')!));
      else if (sessionStorage.getItem('Priva') == 'true') {
        this.privaci = true;
        this.mostrarMenu = false;
        this.conect = false;
        this.stream1 = false;
        // this.stream = false;

      }
      else {
        this.mostrarMenu = true;
        // this.stream = false;
        this.conect = false;
        this.privaci = false;
        this.stream1 = false;
      }
    }
    else if (JSON.parse(sessionStorage.getItem('Priva')!)) {
      this.privaci = true;
      this.mostrarMenu = false;
      this.conect = false;
      // this.stream = false;

    }
    else {
      ;
      this.mostrarMenu = false;
      // this.stream = false;
      this.stream1 = false;
      this.conect = false;
    }

  }
  async carregaQtdPreferencias() {

    let tipoOrientacao: number = this.usuarioLogadoService.getIdTipoOrientacao()!;
    // await this.medicoService.getOrientacaoDoMedico(this.usuarioLogadoService.getIdPessoa()).subscribe((ret)=>{
    //   tipoOrientacao = ret

    this.AnaliseService.GetQuantidadeAnalises(tipoOrientacao).subscribe((ret) => {
      this.totalPendencaias = ret

      this.textoPendencias = tipoOrientacao == 1 ? "Pendências Médicas:" : (tipoOrientacao == 2 ? "Pendências Nutriconal:" : "Pendências Física:")
      this.textoPendencias = this.textoPendencias + ' ' + this.totalPendencaias;
      this.spinner.hide();
    })
  }

  AnaliseMedico() {
    this.router.navigate(['/analise'])
  }

  // AlgumaMenssagem(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  async CarregaQuantBoasVindas() {


    await this.emailService.GetUsuarioBoasVindas(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      if (retorno.length > 0) {
        this.DadosUsuario = retorno
        this.CartasBoasvindas = this.DadosUsuario.length;
      }
      else {
        this.ngxSmartModalService.getModal('emailsSistemaNovo').close();
        this.ngxSmartModalService.getModal('emailUsuarioNovo').close();
      }
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })

    // await this.emailService.GetQuantEmail(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno: number) => {

    //   this.CartasBoasvindas = retorno
    // }, err => {
    //   
    // })
  }



  Exames() {
    this.examesModalService.setModalExames('')

  }
  

  async carregarUsuarioBoasvindas() {


      console.log('abhhhhhhhhhhhhhaidd')
      this.ngxSmartModalService.getModal('emailsSistemaNovo').open();



  }
  fecharModal() {
    this.ngxSmartModalService.getModal('emailUsuarioNovo').close();
    this.ngxSmartModalService.getModal('emailsSistemaNovo').open();
  }


  async mandaEmail() {
    try {  
      if (this.idEmail != 0) {
        this.spinner.show();
        this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe(() => {
          this.idEmail = 0;
          this.CarregaQuantBoasVindas()
          this.snackBarAlert.sucessoSnackbar('Email enviado com sucesso!')
          this.spinner.hide();

        }, () => {
          this.snackBarAlert.falhaSnackbar('Erro ao mandar email!')
          this.spinner.hide();
        })
      }
    } catch (error) {
      
      this.snackBarAlert.falhaSnackbar('Erro ao mandar email!')
      this.spinner.hide();

    }
  }

  ModalEmail(idEmail: number) {
    this.idEmail = 0;
    this.idEmail = idEmail;

    // this.ngxSmartModalService.getModal('emailUsuarioNovo').open();


  }

  async AbrirModarEnviarEmail(idEmail: number){
    this.idEmail = 0;
    this.idEmail = idEmail;

    const ret = await this.controleModais.ModalConfirmacao("Você tem certeza que deseja enviar esse email?\n O Usuário recebera o email com a senha");
    if (ret){
      this.mandaEmail();
    }
  }

  FecharModal(){
    this.dialogRef.close();
  }




  Privacidade(value: boolean) {
    if (value) {
      this.privaci = true;
      this.mostrarMenu = false;
      this.conect = false;
      this.stream1 = false;
      // this.stream = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      // this.stream = false;
      this.conect = false;
      this.stream1 = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }
  Telastream1(value: boolean) {
    if (value) {
      this.mostrarMenu = false;
      this.conect = false;
      // this.stream = false;
      this.stream1 = true;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      // this.stream = false;
      this.conect = false;
      this.stream1 = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }
  Telastream(value: boolean) {
    if (value) {
      this.mostrarMenu = false;
      this.conect = false;
      // this.stream = true;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      // this.stream = false;
      this.conect = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }
  TesteConecao(value: boolean) {
    if (value) {
      this.mostrarMenu = false;
      // this.stream = false;
      this.conect = true;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      // this.stream = false;
      this.conect = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }

  RecuperarSenha(value: boolean) {
    if (value) {
      
      this.mostrarMenu = false;
      this.recuperarSenha = true;
    }
    else {
      
      this.mostrarMenu = false;
      this.recuperarSenha = false;
    }

  }

  Permissoes() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/pesquisapermissoes']);
    this.resetMenuStates();

    this.FillerOpen = false;
    this.appc.ngOnInit();
  }


  Documentos() {
    this.ngxSmartModalService.getModal('documentos').open();

  }

  TesteConect() {
    sessionStorage.setItem("Conect", "true");
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    this.router.navigate(['/testeConexao']);
    this.FillerOpen = false;
    this.appc.ngOnInit();
  }

  ExamesClinica() {
    sessionStorage.setItem("Conect", "true");
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    this.router.navigate(['/examesClinica']);
    this.FillerOpen = false;
  }


  Inicio() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/']);
    this.FillerOpen = false;
    this.appc.ngOnInit();
    this.resetMenuStates();
  }

  Perfil() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/perfil']);
    this.FillerOpen = false;
    this.appc.ngOnInit();
    this.resetMenuStates();

  }
  PerfilClinica() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/perfilClinica']);
    this.FillerOpen = false;
    this.appc.ngOnInit();
    this.resetMenuStates();


  }
  RelatoriosUser() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/historicoUsuarios']);

    this.FillerOpen = false;
    this.appc.ngOnInit();
    this.resetMenuStates();

  }

  Formularios() {
    this.FillerOpen = false;
    this.router.navigate(['/formularios']);
    this.resetMenuStates();
    this.resetMenuStates();
  }
  cadastroreceitas() {
    this.FillerOpen = false;
    this.router.navigate(['/cadastroreceitas']);
    this.resetMenuStates();

  }
  cadastrolocais() {
    this.FillerOpen = false;
    this.router.navigate(['/cadastrolocais']);
    this.resetMenuStates();


  }
  cadastroitens() {
    this.FillerOpen = false;
    this.router.navigate(['/cadastroitens']);
    this.resetMenuStates();

  }
  IrListaFaturas() {
    this.FillerOpen = false;
    this.router.navigate(['/lista-faturas']);
    this.resetMenuStates();

  }
  IrListaSalas() {
    this.FillerOpen = false;
    this.router.navigate(['/listagemsalas']);
    this.resetMenuStates();

  }



  analise() {
    this.FillerOpen = false;
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");

    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente)
      this.router.navigate(['/analisepaciente']);
    else
      this.router.navigate(['/analise']);

    this.appc.ngOnInit();
    this.FillerOpen = false;
    this.resetMenuStates();

  }

  RecadosDia() {
    this.FillerOpen = false;
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/historicoRecados']);
    this.resetMenuStates();
    this.appc.ngOnInit();
    this.FillerOpen = false;

  }

  Relatorios() {
    this.FillerOpen = false;
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/relatorios']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
    this.resetMenuStates();

  }

  abrirModalBoasVindas(teste: documentosModal) {
    this.documentosServiceModal.setAbrirModal(teste);
  }

  Atestado() {

    var documentos = new documentosModal;
    documentos.TipoModal = EnumTipoDocumentos.Atestado;
    documentos.Caminho = 'Menu'
    this.abrirModalBoasVindas(documentos);
    this.resetMenuStates();

  }
  Declaracao() {
    var documentos = new documentosModal;
    documentos.TipoModal = EnumTipoDocumentos.Declaracao;
    documentos.Caminho = 'Menu'
    this.abrirModalBoasVindas(documentos);
    this.resetMenuStates();

  }
  Receituario() {
    var documentos = new documentosModal;
    documentos.TipoModal = EnumTipoDocumentos.Receituario;
    documentos.Caminho = 'Menu'
    this.abrirModalBoasVindas(documentos);
    this.resetMenuStates();

  }
  GuiaExames() {
    var documentos = new documentosModal;
    documentos.TipoModal = EnumTipoDocumentos.GuiaExames;
    documentos.Caminho = 'Menu'
    this.GuiaModalService.setAbrirModal(true);
    this.resetMenuStates();

  }

  async CarregamedicosOnline() {
    this.MedicosOnline = await this.medicoService.getMedicosLogados(this.usuarioLogadoService.getIdUltimaClinica());
    this.spinner.hide();
  }

  async ConsultaSolicitar() {
    this.carregaFila();
    this.MedicosOnline = await this.medicoService.getMedicosLogados(this.usuarioLogadoService.getIdUltimaClinica());
    this.ngxSmartModalService.getModal('SolicitarConsulta').open();
    this.spinner.hide();
  }


  carregaFila() {
    // MÉTODO DESABILITADO: Sistema de fila agora é baseado apenas em token para usuários não logados
    // Para usuários logados, usar o novo sistema de fila baseado em token
    console.warn('carregaFila() está obsoleto. Use o sistema de fila baseado em token.');
    this.spinner.hide();
  }

  Carregaconsultas(idConsulta:any) {

    this.consultaService.carregaConsultaFilaEspera(idConsulta).subscribe((retorno) => {
      ;

      this.Objconsulta = retorno
      this.ConsultaAgoraTeste = true;

      this.ngxSmartModalService.getModal('SolicitarConsulta').open();
    })
  }


  CancelaValue(id:any) {

    this.Motivocancelameto = ''
    this.idCancela = id
    this.ngxSmartModalService.getModal('cancelarHorarioCoracaoMenu').open();

  }
  MotivoCampo() {
    if (this.cancelamento == true)
      this.cancelamento = false
  }
  CancelarConsultaPadrao() {

    if (this.Motivocancelameto == '' || this.Motivocancelameto == undefined) {
      this.cancelamento = true;
      return;
    }

    this.agendaService.InativarAgendamento(this.idCancela, this.usuarioLogadoService.getIdUsuarioAcesso(), this.Motivocancelameto).subscribe(async () => {
      this.consultaAgora = false;
      await this.CarregaconsultasCoracao();
      this.ngxSmartModalService.getModal('cancelarHorarioCoracaoMenu').close();
      
      this.ngxSmartModalService.getModal('consultaMobile').close();
      if (this.usuarioConsulta.length > 0)
        this.ngxSmartModalService.getModal('consultaMobile').open();
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }

  IniciarConsulta() {
    var consulta = new irParaConsulta();
    consulta.idConsulta = this.Objconsulta[0].idconsulta;
    consulta.flgSomenteProntuario = false
    this.localStorageService.Consulta = consulta;
    this.router.navigate(['/streaming']);
    this.ngxSmartModalService.getModal('sairconsulta').close();
    this.ngxSmartModalService.getModal('SolicitarConsulta').close();
  }
  CancelarConsulta() {
    this.agendaService.InativarAgendamento(this.idConsulta, this.usuarioLogadoService.getIdUsuarioAcesso(), "Solicitante desistiu da Fila de Espera").subscribe(() => {
      this.ngxSmartModalService.getModal('sairconsulta').close();
      this.ngxSmartModalService.getModal('SolicitarConsulta').close();
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  OrientacaoMedica() {
    this.router.navigate(['/orientacoes']);
    this.appc.ngOnInit();
  }

  ReuniaoMedica() {
    this.router.navigate(['/agendareuniao']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  Consulta() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/consulta']);
    this.resetMenuStates();
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }


  Contatos() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/contatos']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
    this.resetMenuStates();

  }

  Painel() {
    this.router.navigate(['/Painel']);
    this.FillerOpen = false;
  }

  Faturas() {
    this.router.navigate(['/lista-faturas']);
    this.FillerOpen = false;
    this.faturamentoMenuOpen = false;
    this.resetMenuStates();

  }

IrParaFilaEspera() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/fila-espera-medico']);
    this.FillerOpen = false;
    this.appc.ngOnInit();
    this.resetMenuStates();
  }
  GuiaTiss() {
    this.router.navigate(['/lista-guia-tiss']);
    this.FillerOpen = false;
    this.faturamentoMenuOpen = false;
    this.resetMenuStates();

  }

  Convenios() {
    this.router.navigate(['/pesquisaconvenio']);
    this.faturamentoMenuOpen = false;
    this.FillerOpen = false;
    this.resetMenuStates();

  }

  Medicamentos() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/medicamentos']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
    this.resetMenuStates();

  }

  Agenda() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("Conect", "false");
    sessionStorage.setItem("TelaStream1", "false");
    this.router.navigate(['/calendario']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  Medicos() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/pesquisamedicos']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
    this.resetMenuStates();

  }

  Usuario() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/Atendente']);
    this.resetMenuStates();
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  Pacientes() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/pesquisapacientes']);
    this.resetMenuStates();

    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  clinica() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/clinicas']);
    this.appc.ngOnInit();
    this.resetMenuStates();
    this.FillerOpen = false;
  }

  // {path:'cadastroclinica', component: CadastroClinicaComponent},
  // {path:'clinicas', component: PesquisaClinicaComponent},

  // CentralAlerta(){
  //   sessionStorage.setItem("TelaStream", "false");
  //   this.router.navigate(['/inicio']);
  //   this.appc.ngOnInit();
  // }

  StreamVideos() {

    this.IrConsulta(2985);


    // sessionStorage.setItem("TelaStream", "false");
    // sessionStorage.setItem("TelaStream1", "false");
    // sessionStorage.setItem("Conect", "false");
    // this.router.navigate(['/streaming1']);
    // this.appc.ngOnInit();
  }
  prontuarioVideos() {

    this.router.navigate(['/prontuario']);
    this.resetMenuStates();

  }

  AbrirChat() {
    this.chatService.AbrirModalChat();
    this.resetMenuStates();

  }

  Perguntas() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/perguntas']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  tipoAgendamento() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/tipoAgendamento']);
    this.appc.ngOnInit();
    this.FillerOpen = false;

  }

  Convenio() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    this.router.navigate(['/pesquisaconvenio']);
    this.resetMenuStates();
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }


  privacidade() {
    sessionStorage.setItem("TelaStream", "false");
    sessionStorage.setItem("TelaStream1", "false");
    sessionStorage.setItem("Conect", "false");
    sessionStorage.setItem("Priva", "true");
    this.router.navigate(['/privacidade']);
    this.appc.ngOnInit();
    this.FillerOpen = false;
  }

  Logoff() {
    this.usuarioLogadoService.logout()
    localStorage.clear();
    sessionStorage.clear();
    this.router.navigate(['login'])
  }



  CarregarNot() {
    this.Notificacoes = []
    this.Notificacoes.push({ Desc: 'Sua proxima consulta esta distante', Status: 'mes', Data: '10/06/2019' }),
      this.Notificacoes.push({ Desc: 'Sua consulta esta proxima', Status: 'semana', Data: '10/05/2019' }),
      this.Notificacoes.push({ Desc: 'Você tem uma consulta hoje', Status: 'dia', Data: '25/05/2019' })

  }



  //#region Ícone coração, modal consulta, signalR
  consultas: any = [];
  async CarregaconsultasCoracao() {
    
    var minutos = 20;
    await this.consultaService.GetConsultaAlerta(this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdTipoUsuario(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.consultas = retorno;
      if (retorno.length == 0) {
        this.consultaAgora = false;
        this.ngxSmartModalService.getModal('consultaMobile').close();
      }

      
      
      this.usuarioConsulta = [];
      this.consultas.forEach((element:any) => {

        if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico && element.idPacientePessoa == this.usuarioLogadoService.getIdPessoa()) {
          return;
        }
        var teste = new Date(element.dtaconsulta)
        teste.setMinutes(teste.getMinutes() - minutos);
        var data = new Date()
        data.setMinutes(data.getMinutes() - minutos);

        if (this.tipoUsuario == 'Paciente') {
          if (!element.flgProntuario) {
            if (element.flgFilaEspera || (teste >= data && teste <= new Date())) {
              if (element.imagemPaciente != null) {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante

                })
              }
              else {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }

              this.consultaAgora = true;

              return;
            }
          }
        }
        else {
          if (element.flgFilaEspera || element.flgCheckin || (teste >= data && teste <= new Date())) {
            if (element.imagemPaciente != null) {
              this.usuarioConsulta.push({
                nome: element.paciente,
                idconsulta: element.idconsulta,
                dtaconsulta: element.dtaconsulta,
                tipoAgendamento: element.tipoconsulta,
                primeiraConsulta: element.flgConsulta,
                Clinica: element.nomeClinica,
                nomeMedico: element.medico,
                flgSomenteProntuario: element.flgSomenteProntuario,
                flgFilaEspera: element.flgFilaEspera,
                ImagemPessoa: element.imagemPaciente,
                idMedicoPessoa: element.idMedicoPessoa,
                idPacientePessoa: element.idPacientePessoa,
                IdPagamento: element.idPagamento,
                IdConvenio: element.idConvenio,
                valorConsulta: element.valorConsulta,
                flgExigePagamento: element.flgExigePagamento,
                PacienteSolicitante: element.pacienteSolicitante,
                UsuarioSolicitante: element.usuarioSolicitante
              })
            }
            else {
              this.usuarioConsulta.push({
                nome: element.paciente,
                idconsulta: element.idconsulta,
                dtaconsulta: element.dtaconsulta,
                tipoAgendamento: element.tipoconsulta,
                primeiraConsulta: element.flgConsulta,
                Clinica: element.nomeClinica,
                nomeMedico: element.medico,
                flgSomenteProntuario: element.flgSomenteProntuario,
                flgFilaEspera: element.flgFilaEspera,
                ImagemPessoa: element.imagemPaciente,
                idMedicoPessoa: element.idMedicoPessoa,
                idPacientePessoa: element.idPacientePessoa,
                IdPagamento: element.idPagamento,
                IdConvenio: element.idConvenio,
                valorConsulta: element.valorConsulta,
                flgExigePagamento: element.flgExigePagamento,
                PacienteSolicitante: element.pacienteSolicitante,
                UsuarioSolicitante: element.usuarioSolicitante
              })
            }

            this.consultaAgora = true;

            return;
          }
        }
      });
    })



    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }


    // procura o adiciona o som

    this.timeInterval = setInterval(() => {

      this.usuarioConsulta = [];
      if (this.consultas) {
        this.consultas.forEach((element:any) => {

          if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico && element.idPacientePessoa == this.usuarioLogadoService.getIdPessoa()) {
            return;
          }

          var teste = new Date(element.dtaconsulta)
          teste.setMinutes(teste.getMinutes() - minutos);
          var data = new Date()
          data.setMinutes(data.getMinutes() - minutos);

          if (this.tipoUsuario == 'Paciente') {
            if (!element.flgProntuario) {
              if (element.flgFilaEspera || (teste >= data && teste <= new Date())) {
                if (element.imagemPaciente != null) {
                  this.usuarioConsulta.push({
                    nome: element.paciente,
                    idconsulta: element.idconsulta,
                    dtaconsulta: element.dtaconsulta,
                    tipoAgendamento: element.tipoconsulta,
                    primeiraConsulta: element.flgConsulta,
                    Clinica: element.nomeClinica,
                    nomeMedico: element.medico,
                    flgSomenteProntuario: element.flgSomenteProntuario,
                    flgFilaEspera: element.flgFilaEspera,
                    ImagemPessoa: element.imagemPaciente,
                    idMedicoPessoa: element.idMedicoPessoa,
                    idPacientePessoa: element.idPacientePessoa,
                    IdPagamento: element.idPagamento,
                    IdConvenio: element.idConvenio,
                    valorConsulta: element.valorConsulta,
                    flgExigePagamento: element.flgExigePagamento,
                    PacienteSolicitante: element.pacienteSolicitante,
                    UsuarioSolicitante: element.usuarioSolicitante

                  })
                }
                else {
                  this.usuarioConsulta.push({
                    nome: element.paciente,
                    idconsulta: element.idconsulta,
                    dtaconsulta: element.dtaconsulta,
                    tipoAgendamento: element.tipoconsulta,
                    primeiraConsulta: element.flgConsulta,
                    Clinica: element.nomeClinica,
                    nomeMedico: element.medico,
                    flgSomenteProntuario: element.flgSomenteProntuario,
                    flgFilaEspera: element.flgFilaEspera,
                    ImagemPessoa: element.imagemPaciente,
                    idMedicoPessoa: element.idMedicoPessoa,
                    idPacientePessoa: element.idPacientePessoa,
                    IdPagamento: element.idPagamento,
                    IdConvenio: element.idConvenio,
                    valorConsulta: element.valorConsulta,
                    flgExigePagamento: element.flgExigePagamento,
                    PacienteSolicitante: element.pacienteSolicitante,
                    UsuarioSolicitante: element.usuarioSolicitante
                  })
                }

                this.consultaAgora = true;

                return;
              }
            }
          }
          else {
            if (element.flgFilaEspera || element.flgCheckin || (teste >= data && teste <= new Date())) {
              if (element.imagemPaciente != null) {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }
              else {
                this.usuarioConsulta.push({
                  nome: element.paciente,
                  idconsulta: element.idconsulta,
                  dtaconsulta: element.dtaconsulta,
                  tipoAgendamento: element.tipoconsulta,
                  primeiraConsulta: element.flgConsulta,
                  Clinica: element.nomeClinica,
                  nomeMedico: element.medico,
                  flgSomenteProntuario: element.flgSomenteProntuario,
                  flgFilaEspera: element.flgFilaEspera,
                  ImagemPessoa: element.imagemPaciente,
                  idMedicoPessoa: element.idMedicoPessoa,
                  idPacientePessoa: element.idPacientePessoa,
                  IdPagamento: element.idPagamento,
                  IdConvenio: element.idConvenio,
                  valorConsulta: element.valorConsulta,
                  flgExigePagamento: element.flgExigePagamento,
                  PacienteSolicitante: element.pacienteSolicitante,
                  UsuarioSolicitante: element.usuarioSolicitante
                })
              }

              this.consultaAgora = true;

              return;
            }

          }
        });
      }
    }, 60000);


  }

  playAudio() {
    let audio = new Audio();
    audio.src = "../../../assets/build/som/alert.mp3";
    audio.load();
    audio.play();
  }

  AlteraClinica() {
    this.clinicaService.getGridClinica(0, 100, null).subscribe((retorno: any = []) => {
      this.DadosClinicasUser = []
      var clinic:any = []
      retorno.forEach((element:any) => {
        this.usuarioLogadoService.getClinicas()!.forEach(elemento => {
          if (element.idclinica == elemento.idClinica) {
            if (element.cnpj)
              element.cnpj = element.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5");
            if (element.tel) {
              element.tel = element.tel.replace(/\D/g, "");
              element.tel = element.tel.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.tel = element.tel.replace(/(\d)(\d{4})$/, "$1-$2");

            }
            clinic.push(element);
          }
          this.spinner.hide();
        });

      });
      this.DadosClinicasUser = clinic;
      
      this.ngxSmartModalService.getModal('trocaClinicaMenu').open();
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    });
  }

  reflesh() {

    this.router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };
  }
  reloadComponent() {
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    this.router.onSameUrlNavigation = 'reload';
    this.router.navigate(['/']);
  }

  // async alteraDadosUsuario(idclinica) {

  //   const dataUsuario: any = await this.usuarioService.getPerfilUsuarioLogadoClinica().toPromise();
  dataUsuario: any;
  async alteraDadosUsuario() {
    this.usuarioService.getPerfilUsuarioLogadoClinica().subscribe((retorno) => {
      this.dataUsuario = retorno
      
      
      this.usuarioService.AtualizaDadosUsuarioLogado(this.dataUsuario);
      this.reloadComponent();
      this.ngxSmartModalService.getModal('trocaClinicaMenu').close();

      this.signalHubService.loginUsuario(this.dataUsuario);
      this.spinner.hide();
    });
  }

  async tocarClinic(idclinica:any) {
    this.clinicaService.TrocarClinica(this.usuarioLogadoService.getIdUsuarioAcesso()!, idclinica).subscribe(() => {
      this.alteraDadosUsuario();
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }


  CarregaClinicas() {
    try {

      this.clinicaService.getGridClinica(0, 100, null).subscribe((retorno: any) => {
        

        retorno.forEach((element:any) => {
          if (element.idclinica == this.usuarioLogadoService.getIdUltimaClinica()) {
            if (element.logo && element.logo != null)
              this.ImagemClinica = element.logo
            this.nomeClinica = element.nomeClinica
            this.DadosClinicas.push(element)
          }
        });

        
        
        this.spinner.hide();

      }, () => {
        this.spinner.hide();
      })


    } catch (error) {
      
    }
  }
  solicitacaoConsulta() {

    this.agendaService.AgendaPessoaSolicitante(this.usuarioLogadoService.getIdPessoa(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe(() => {

      this.carregaFila();
      this.spinner.hide();

    })

  }

  Medicoentrouconsulta(id:any, flg:any) {
    this.consultaService.MedicoOnlinenaConsulta(id, this.usuarioLogadoService.getIdPessoa()).subscribe(() => {
      var consulta = new irParaConsulta();
      consulta.idConsulta = id;
      consulta.flgSomenteProntuario = flg
      this.localStorageService.Consulta = consulta;
      // sessionStorage.setItem("TelaStream", "true");


      this.router.navigate(['/streaming']);
      this.resetMenuStates();

    })
  }


  IrConsulta(id:any) {
    if (id != "" && id != 0) {
      var Objconsulta = this.usuarioConsulta.filter((c:any) => c.idconsulta == id);
      if (this.usuarioConsulta.flgExigePagamento == true && this.usuarioConsulta.IdPagamento == null && this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
        this.pagamentoService.setIdPagador(this.usuarioConsulta.idPacientePessoa);
        this.pagamentoService.setIdRecebedora(this.usuarioConsulta.idMedicoPessoa);
        this.pagamentoService.setValor(this.usuarioConsulta.valorConsulta);
        this.pagamentoService.setIdconsulta(id);
        this.pagamentoService.setAbrirModal(true);
        this.ngxSmartModalService.getModal('ModalPagamento').open();
      }
      else {
        if (Objconsulta.length > 0 && Objconsulta[0].flgFilaEspera && this.usuarioLogadoService.getIdTipoUsuario() != EnumTipoUsuario.Paciente) {
          this.Medicoentrouconsulta(id, false);
        }
        else {
          // this.stream = true;
          this.conect = false;
          this.stream1 = false;

          ;
          ;
          var consulta = new irParaConsulta();
          consulta.idConsulta = id;
          consulta.flgSomenteProntuario = false
          this.localStorageService.Consulta = consulta;

          // sessionStorage.setItem("TelaStream", "true");
          this.router.navigate(['/streaming']);
          // this.appc.ngOnInit();
        }
        this.ngxSmartModalService.getModal('consultaMobile').close();

      }



    }
  }

  RelatorioAgendamentos(){
    this.controleModais.ModalRelatorioAgendamento();
  }

  switchTheme(theme: string): void {
    this.currentTheme = theme;
  }

  onEventTriggered(event: string, obj:any): void {

    
    this.triggeredEvents.push(event);
    if (obj == 'Abre') {
      this.ChatClick = true
    }
    else if (obj == 'fecha') {
      this.ChatClick = false
    }
    else
      this.ChatClick = false

  }

  Checkin(idConsulta:any) {


    this.consultaService.CheckinConsulta(idConsulta, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno:any) => {
      retorno;
    })


  }
  RecadodoDia() {
    this.medicoService.getRecadoDia(this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica(), '', '').subscribe((retorno) => {
      ;

      this.DadosRecado = []

      this.RecadoDia = false;
      this.DadosRecado = retorno;

      this.DadosRecado.forEach((element:any) => {
        if (element.flgVisuali == false)
          this.RecadoDia = true;
      });
      this.spinner.hide();

    })

  }

  MensagemdoDia(id:any) {
    var men = this.DadosRecado.filter((c:any) => c.idRecado == id)
    this.DesRecadoDia = men[0].recado;
    this.UsuarioRecado = men[0].usuarioGerador;


    this.medicoService.VisualizacaoRecadoDia(id, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      if (retorno) {
        this.RecadodoDia()
        this.medicoService.atualizaMensagemDia$.emit(retorno)
        this.ngxSmartModalService.getModal('mensagemDiaNova').open()
        this.spinner.hide();;
      }
    })

  }
  TodasMensagemdoDia() {

    this.router.navigate(['/historicoRecados']);
    this.resetMenuStates();


  }

  AbrirModalPagamento() {
    this.pagamentoService.setIdRecebedora(1);
    this.pagamentoService.setValor(0.01);
    this.pagamentoService.setAbrirModal(true);
    this.ngxSmartModalService.getModal('ModalPagamento').open();
  }
  FecharMenu() {
    this.FillerOpen = false;
    
  }




  notificarSom() {
    this.chatService.DisparaNotificacaoSonora();
  }

  notificarWindows() {
    this.chatService.DisparaNotificacaoWindows("Você recebeu uma nova mensagem no chat");
  }
  //Abrir o sidebar
  isOpen = false;

  abrir1 = false;
  isRotating1 = false;
  isDisabled1 = false;
  
  isDisabled2 = false;
  abrir2 = false;

  isDisabled3 = false;
  abrir3 = false;

  isDisabled4 = false;
  abrir4 = false;

  toggleSidebar() {
    this.isOpen = !this.isOpen;
    this.isDisabled1 = false;
    if(this.abrir1 == true){
      this.abrir1 = false;
    }
  }
  expanpadir(id = 0){
    if(this.isOpen == true){
      if(id == 1){
        this.abrir1 = !this.abrir1;
        this.abrir2 = false;
        this.abrir3 = false;
        this.abrir4 = false;
      }
      if(id == 2){
        this.abrir1 = false;
        this.abrir2 = !this.abrir2;
        this.abrir3 = false;
        this.abrir4 = false;      
      }
      if(id == 3){
        this.abrir1 = false;
        this.abrir2 = false;
        this.abrir3 = !this.abrir3;
        this.abrir4 = false;
      }
      if(id == 4){
        this.abrir1 = false;
        this.abrir2 = false;
        this.abrir3 = false;
        this.abrir4 = !this.abrir4;
      }
    }else{
      if(id == 1){
        this.isDisabled1= !this.isDisabled1;
      }
      if(id == 2){
        this.isDisabled2= !this.isDisabled2;
      }
      if(id == 3){
        this.isDisabled3= !this.isDisabled3;
        console.log(this.isDisabled3)
      }
      if(id == 4){
        this.isDisabled4= !this.isDisabled4;
      }
    } 
  }
  resetMenuStates() {
    // Close all expanded menus
    this.abrir1 = false;
    this.abrir2 = false;
    this.abrir3 = false;
    this.abrir4 = false;
    
    this.isDisabled1 = false;
    this.isDisabled2 = false;
    this.isDisabled3 = false;
    this.isDisabled4 = false;
    
      this.isOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    const sidebar = document.getElementById('sidenav');
    const buttonMenu = document.getElementById('buttonMenu')
    const buttonMenu2 = document.getElementById('buttonMenu2')
    const buttonMenu3 = document.getElementById('buttonMenu3')
    const buttonMenu4 = document.getElementById('buttonMenu4')

    if (this.isOpen && sidebar && !sidebar.contains(event.target as Node)) {
      this.isOpen = false;

      this.abrir1 = false;
      this.isRotating1 = false;
      this.isDisabled1 = false;
      
      this.isDisabled2 = false;
      this.abrir2 = false;
    
      this.isDisabled3 = false;
      this.abrir3 = false;

      this.isDisabled4 = false;
      this.abrir4 = false;

    }
    if(this.isDisabled1 && buttonMenu && !buttonMenu.contains(event.target as Node)) {
      this.isDisabled1 = false;
    }
    if(this.isDisabled2 && buttonMenu2 && !buttonMenu2.contains(event.target as Node)) {
      this.isDisabled2 = false;
    }
    if(this.isDisabled3 && buttonMenu3 && !buttonMenu3.contains(event.target as Node)) {
      this.isDisabled3 = false;
    }
    if(this.isDisabled4 && buttonMenu4 && !buttonMenu4.contains(event.target as Node)) {
      this.isDisabled4 = false;
    }
  }
  isActive(prefix: string): boolean {
  const path = this.location.path();

  // Dashboard (rota raiz)
  if (prefix === '') {
    return path === '';
  }

  // Garante que só ativa se for igual ou filho direto
  return path === '/' + prefix || path.startsWith('/' + prefix + '/');
}

}
