// Variables
$primary-color: #00ff80;
$secondary-color: #6c757d;
$success-color: #28a745;
$danger-color: #dc3545;
$warning-color: #ffc107;
$info-color: #17a2b8;
$light-color: #f8f9fa;
$dark-color: #343a40;
$white: #ffffff;
$border-radius: 12px;
$shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
$shadow-hover: 0 8px 30px rgba(0, 0, 0, 0.12);
$transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

// Modal Overlay
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

// Modal Container
.modal-container {
    width: 100%;
    max-width: 500px;
    max-height: 70vh;
    overflow-y: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

// Modal Card
.modal-card {
    background: $white;
    border-radius: $border-radius * 2;
    box-shadow: $shadow-hover;
    overflow: hidden;
    width: 100%;
    position: relative;
    animation: modalEnter 0.4s ease-out;
}

// Modal Header
.modal-header {
    background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 255, 128, 0.1);

    .header-left {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .header-text {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .close-button {
        color: #666;
        transition: all 0.2s ease;
        width: 40px;
        height: 40px;

        &:hover {
            color: #333;
            background-color: rgba(0, 0, 0, 0.04);
            transform: scale(1.1);
        }

        mat-icon {
            font-size: 20px;
        }
    }

    .icon-wrapper {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background: rgba(0, 255, 128, 0.1);
        border-radius: 50%;
        transition: $transition;
        flex-shrink: 0;

        &:hover {
            transform: scale(1.05);
            background: rgba(0, 255, 128, 0.15);
        }

        svg {
            width: 36px;
            height: 36px;
        }
    }

    .modal-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: $dark-color;
        margin: 0 0 0.25rem 0;
        line-height: 1.2;

        @media (max-width: 768px) {
            font-size: 1.3rem;
        }
    }

    .modal-subtitle {
        font-size: 1rem;
        color: $secondary-color;
        line-height: 1.6;
        margin: 0;
        max-width: 400px;
        margin: 0 auto;
    }
}

// Modal Content
.modal-content {
    padding: 1.5rem;
    min-height: 150px;
    max-height: calc(70vh - 200px);
    overflow-y: auto;

    @media (max-width: 768px) {
        padding: 1rem;
    }
}

// Steps Container
.steps-container {
    .step-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-left: 3px solid transparent;
        padding-left: 0.75rem;
        margin-left: 0.75rem;
        transition: $transition;

        &:not(:last-child) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        &.active {
            border-left-color: $primary-color;
            background: rgba(0, 255, 128, 0.05);
            border-radius: 0 $border-radius $border-radius 0;
            .step-label {
                color: #000000 !important;
            }
        }

        &.completed {
            border-left-color: $success-color;

            .step-icon mat-icon {
                color: $success-color;
            }
        }

        &.error {
            border-left-color: $danger-color;
            background: rgba(220, 53, 69, 0.05);
            border-radius: 0 $border-radius $border-radius 0;

            .step-icon mat-icon {
                color: $danger-color;
            }
        }

        .step-icon {
            margin-right: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;

            mat-icon {
                transition: $transition;

                &.rotating {
                    animation: rotate 2s linear infinite;
                    color: $primary-color;
                }
            }
        }

        .step-content {
            flex: 1;

            .step-label {
                font-weight: 500;
                color: $dark-color;
                font-size: 0.9rem;
            }
        }
    }

    .progress-container {
        margin-top: 1.5rem;

        ::ng-deep .mat-progress-bar-fill::after {
            background-color: $primary-color;
        }

        ::ng-deep .mat-progress-bar-buffer {
            background: rgba(0, 255, 128, 0.2);
        }
    }
}

// Error Container
.error-container {
    text-align: center;
    padding: 2rem 0;

    .error-card {
        background: rgba(220, 53, 69, 0.05);
        border: 1px solid rgba(220, 53, 69, 0.2);
        border-radius: $border-radius;
        padding: 2rem;
        max-width: 450px;
        margin: 0 auto;

        .error-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .error-icon {
            font-size: 2rem;
            color: $danger-color;
        }

        .error-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: $danger-color;
            margin: 0;
        }

        .error-message {
            color: $dark-color;
            line-height: 1.6;
            margin: 0 0 1.5rem 0;
            font-size: 0.95rem;
        }

        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;

            .retry-button {
                background: $danger-color;
                color: white;

                &:hover {
                    background: darken($danger-color, 10%);
                }

                mat-icon {
                    margin-right: 0.5rem;
                }
            }

            .cancel-button {
                color: $secondary-color;
                border-color: #ddd;

                &:hover {
                    background: rgba(0, 0, 0, 0.04);
                }
            }
        }
    }
}

// Data Preview Container
.data-preview-container {
    .preview-header {
        text-align: center;
        margin-bottom: 1.5rem;

        .preview-icon {
            color: $success-color;
            margin-bottom: 1rem;
        }

        .preview-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: $dark-color;
            margin: 0 0 0.5rem 0;
        }

        .preview-subtitle {
            color: $secondary-color;
            margin: 0;
        }
    }

    .preview-content {
        margin-top: 1.5rem;

        .data-table {
            .data-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 1rem;
                border-radius: $border-radius;
                margin-bottom: 0.5rem;
                background: $light-color;
                transition: $transition;

                &:hover {
                    background: rgba(0, 255, 128, 0.05);
                }

                .data-label {
                    font-weight: 600;
                    color: $dark-color;
                    flex: 1;
                }

                .data-value {
                    font-weight: 500;
                    color: $primary-color;
                    text-align: right;
                    font-size: 1.1rem;
                }
            }
        }

        .array-display {
            .array-items {
                .array-item {
                    background: $light-color;
                    border-radius: $border-radius;
                    padding: 1rem;
                    margin-bottom: 1rem;

                    strong {
                        color: $dark-color;
                        display: block;
                        margin-bottom: 0.5rem;
                    }
                }
            }
        }

        .json-container {
            background: $light-color;
            border-radius: $border-radius;
            padding: 1rem;
        }

        .json-display {
            background: transparent;
            border: none;
            font-family: "Courier New", monospace;
            font-size: 0.9rem;
            color: $dark-color;
            white-space: pre-wrap;
            word-break: break-word;
            margin: 0;
            line-height: 1.4;
        }
    }
}

// Success Container
.success-container {
    text-align: center;
    padding: 2rem 0;

    .success-card {
        background: rgba(40, 167, 69, 0.05);
        border: 1px solid rgba(40, 167, 69, 0.2);
        border-radius: $border-radius;
        padding: 2rem;

        .success-icon {
            color: $success-color;
            margin-bottom: 1rem;
        }

        .success-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: $success-color;
            margin: 0 0 1rem 0;
        }

        .success-message {
            color: $dark-color;
            line-height: 1.6;
            margin: 0;
        }
    }
}

// Modal Actions
.modal-actions {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(248, 249, 250, 0.5);

    @media (max-width: 768px) {
        padding: 1rem;
    }

    .action-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1rem;

        @media (max-width: 768px) {
            flex-direction: column;

            button {
                width: 100%;
            }
        }

        &.loading-actions {
            justify-content: center;

            .loading-text {
                display: flex;
                align-items: center;
                color: $secondary-color;
                font-weight: 500;
                margin: 0;

                .loading-icon {
                    animation: rotate 2s linear infinite;
                }
            }
        }
    }
}

// Buttons
.btn-secondary {
    color: $secondary-color;
    border-color: $secondary-color;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: $border-radius;
    transition: $transition;

    &:hover {
        background-color: $secondary-color;
        color: $white;
        transform: translateY(-1px);
    }
}

.btn-primary {
    background-color: $primary-color;
    color: $dark-color;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: $border-radius;
    box-shadow: 0 4px 12px rgba(0, 255, 128, 0.3);
    transition: $transition;

    &:hover:not(:disabled) {
        background-color: darken($primary-color, 10%);
        box-shadow: 0 6px 20px rgba(0, 255, 128, 0.4);
        transform: translateY(-2px);
    }

    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    &.full-width {
        width: 100%;
        justify-content: center;
    }
}

// Close Button
.close-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    transition: $transition;

    &:hover:not(:disabled) {
        background: $white;
        transform: scale(1.1);
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    mat-icon {
        color: $secondary-color;
    }
}

// Animations
@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// Material Customizations
::ng-deep {
    .mat-progress-bar {
        height: 6px;
        border-radius: 3px;
        overflow: hidden;
    }

    .mat-divider {
        border-top-color: rgba(0, 255, 128, 0.2);
        margin: 1.5rem 0;
    }

    // Customização específica para a modal VittalTec
    .vittaltec-modal-panel {
        .mat-dialog-container {
            padding: 0 !important;
            border-radius: 16px !important;
            overflow: hidden !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
        }
    }

    // Melhorias nos botões Material
    .mat-raised-button {
        border-radius: 8px !important;
        font-weight: 500 !important;
        text-transform: none !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;

        &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        }
    }

    .mat-stroked-button {
        border-radius: 8px !important;
        font-weight: 500 !important;
        text-transform: none !important;
    }

    .mat-icon-button {
        border-radius: 8px !important;
    }
}

// Responsive Design
@media (max-width: 768px) {
    .modal-overlay {
        padding: 0.5rem;
    }

    .modal-container {
        max-width: 95vw;
        max-height: 85vh;
    }

    .modal-card {
        margin: 0;
        max-height: 85vh;
    }

    .modal-header {
        padding: 1rem;

        .header-left {
            gap: 1rem;
        }

        .icon-wrapper {
            width: 50px;
            height: 50px;

            svg {
                width: 30px;
                height: 30px;
            }
        }

        .modal-title {
            font-size: 1.2rem;
        }

        .modal-subtitle {
            font-size: 0.8rem;
        }
    }

    .steps-container .step-item {
        margin-left: 0.5rem;
        padding-left: 0.75rem;
    }

    .data-preview-container .preview-content .data-table .data-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;

        .data-value {
            text-align: left;
        }
    }
}

@media (max-width: 480px) {
    .modal-header {
        padding: 1.5rem 1rem;

        .modal-title {
            font-size: 1.2rem;
        }
    }

    .modal-content {
        padding: 1rem;
    }

    .modal-actions {
        padding: 1rem;
    }
}

// Estilos para o preview dos dados coletados
.preview-content {
    padding: 16px 0;

    // Status section
    .status-section {
        margin-bottom: 20px;

        .status-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-radius: 8px;
            background-color: #f5f5f5;

            &.status-aprovado {
                background-color: #e8f5e8;
                color: #2e7d32;

                .status-icon {
                    color: #4caf50;
                }
            }

            .status-icon {
                margin-right: 8px;
                font-size: 20px;
            }

            .status-text {
                font-weight: 500;
                font-size: 14px;
            }
        }
    }

    // Patient data section
    .patient-data-section {
        .section-title {
            display: flex;
            align-items: center;
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 500;
            color: #333;

            mat-icon {
                margin-right: 8px;
                color: #666;
            }
        }

        .data-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 12px;

            @media (min-width: 600px) {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .data-item {
            display: flex;
            flex-direction: column;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
            transition: all 0.2s ease;

            &:hover {
                background-color: #f0f0f0;
                border-color: #d0d0d0;
            }

            &.vital-sign {
                border-left: 4px solid #2196f3;
            }

            .data-label {
                display: flex;
                align-items: center;
                font-size: 12px;
                font-weight: 500;
                color: #666;
                margin-bottom: 4px;
                text-transform: uppercase;
                letter-spacing: 0.5px;

                .data-icon {
                    margin-right: 6px;
                    font-size: 16px;
                    color: #2196f3;
                }
            }

            .data-value {
                font-size: 18px;
                font-weight: 600;
                color: #333;

                .unit {
                    font-size: 14px;
                    font-weight: 400;
                    color: #666;
                    margin-left: 4px;
                }
            }

            // Pressão arterial específica
            .pressure-value {
                display: flex;
                align-items: baseline;

                .systolic {
                    color: #f44336;
                }

                .diastolic {
                    color: #ff9800;
                }

                .separator {
                    margin: 0 4px;
                    color: #666;
                    font-weight: 400;
                }
            }
        }
    }

    // Timestamp section
    .timestamp-section {
        margin-top: 20px;
        padding-top: 16px;
        border-top: 1px solid #e0e0e0;

        .timestamp-item {
            display: flex;
            align-items: center;
            color: #666;
            font-size: 12px;

            .timestamp-icon {
                margin-right: 6px;
                font-size: 16px;
            }
        }
    }

    // Raw data section (fallback)
    .raw-data-section {
        .section-title {
            display: flex;
            align-items: center;
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 500;
            color: #333;

            mat-icon {
                margin-right: 8px;
                color: #666;
            }
        }

        .raw-data-container {
            background-color: #f5f5f5;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;

            .raw-data {
                font-family: "Courier New", monospace;
                font-size: 12px;
                color: #333;
                margin: 0;
                white-space: pre-wrap;
                word-break: break-word;
            }
        }
    }

    // No data section
    .no-data-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32px 16px;
        text-align: center;
        color: #666;

        .no-data-icon {
            font-size: 48px;
            margin-bottom: 12px;
            opacity: 0.5;
        }

        .no-data-text {
            margin: 0;
            font-size: 14px;
        }
    }
}

// Responsividade para telas menores
@media (max-width: 599px) {
    .preview-content {
        .patient-data-section {
            .data-grid {
                grid-template-columns: 1fr;
            }

            .data-item {
                .data-value {
                    font-size: 16px;
                }
            }
        }
    }
}
