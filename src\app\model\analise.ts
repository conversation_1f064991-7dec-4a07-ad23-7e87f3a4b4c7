import { analiseFormularioModel } from "./formularios";

export class Analise {
    idAnalise?:number;
    idPaciente?:number;
    idUsuarioGerador?:number;
    idAnaliseDetalhe?:number;
    idPessoaEspecialista?:number;
    idTipoOrientacao?:number;
    Arquivobase64:any;
    flgMensagemInterna?:boolean;
    flgMensagemImportante?:boolean;
    observacao:string = "";
    nomePaciente?:string;
    nomeMedico?:string;    
    analiseDetalheModelViewRepos?:AnaliseDetalhe[];
    nmeArquivo?:string;   
    chaveArquivo?:string; 
    idsExames?:number[] | null;  
    examesSelecionados?:ExamesAnaliseModelView[];
    listaFormularios?:analiseFormularioModel[];
    flgChatInterno?:boolean;
}

export class AnaliseDetalhe {
    idAnalise?:number;
    idPaciente?:number;
    idAnaliseDetalhe?:number;
    idPessoaEspecialista?:number;
    idTipoOrientacao?:number;
    dtaCadastro?:Date;

    observacao?:string;
    nomePaciente?:string;
    nomeMedico?:string;  
    tipoOrientacao?:string;  
}

export class ExamesAnaliseModelView {
    idExame?:number;
    idExameClinica?:number;
    idAnalise?:number;
    dtaCadastro?:Date;
    nmeExame?:string;
    chaveArquivo?:string;
    nmeArquivo?:string;
    Arquivobase64:any;
}

export class AnaliseMensagemModelView
{
    IdAnaliseMensagem?: number;
    idAnalise?: number;
    idPessoa?: number;
    idMedico?: number;
    mensagem: string = "";
    flgMensagemImportante?: boolean
    nomeMedico?: string;
    dtHoraEnvio?: Date;
}
