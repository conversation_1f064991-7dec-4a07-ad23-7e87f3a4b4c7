import { ReceitaService } from './../../service/Receita.Service';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { LocalStorageService } from './../../service/LocalStorageService';
import { Component, OnInit } from '@angular/core';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
// import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import ClassicEditor from 'ckeditor-build-b64imageupload';

import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { novaReceita } from 'src/app/model/receitas';
import { msgResposta } from 'src/app/model/retorno-resposta';
import { PacienteService } from 'src/app/service/pacientes.service';
import { tipoOrientacao } from 'src/app/model/itens';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-adicionar-receitas',
    templateUrl: './adicionar-receitas.component.html',
    styleUrls: ['./adicionar-receitas.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      NgSelectModule,
      CKEditorModule
    ]
})

export class AdicionarReceitasComponent implements OnInit {
  public data = '';
  constructor(
    private spinner: SpinnerService,
    private ReceitaService: ReceitaService,
    private pacientesService: PacienteService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    // private snackBar: MatSnackBar
    private snackBarAlert: AlertComponent
  ) { }
  idTipoOrientacao?: number;
  DadosPacientes: any = [];

  paci = new FormControl('', [Validators.required, Validators.maxLength(11)])
  paciControl = new FormControl();
  idConvenio?: number;

  idObjReceita?: number;

  objTipoOrientacao: tipoOrientacao[] = [];
  objReceita = new novaReceita();
  selectedPacienteId?: number;
  ImagemPessoa: any = "assets/build/img/userdefault.png";
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  actionButtonLabel: string = 'Fechar';
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  action: boolean = true;
  CPF = ""
  IdPaciente?: number;
  paciAgendaVasil?: boolean;
  paciAgendaVal?: boolean;
  flgUser: boolean = false;
  pacienteValido: boolean = true;
  FlgRetorno = false;
  receita?: string;
  retorno?: msgResposta;

  // Editor = ClassicEditor;
  config = {
    toolbar: ['heading', '|', 'undo', 'redo', '|', "outdent", "indent", 'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'link', 'blockQuote', 'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', 'imageInsert'] // , 'removeFormat'],
  };

  ngOnInit() {
    this.verificaUsuario()
    this.CarregaPacientes()
    this.CarregaTipoOrientacao()

    this.idObjReceita = this.localStorageService.idReceita;
    if (this.idObjReceita != null && this.idObjReceita > 0) {
      this.ReceitaService.GetObjReceita(this.idObjReceita).subscribe((retorno) => {
        this.objReceita = retorno;
        this.receita = this.objReceita.receita;
        this.localStorageService.idMedico = this.objReceita.idMedico!.toString();
        this.spinner.hide();
      }, erro => {
        console.error(erro)
        this.spinner.hide();
      })
    }
    this.localStorageService.idReceita = null
  }

  verificaUsuario() {
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM || this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.flgUser = true;
    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente || this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
      this.flgUser = false;
    }
  }

  Salvar() {
    if (this.VerificaSalvamento()) {
      this.ReceitaService.SalvarNovaReceita(this.objReceita).subscribe((retorno: any) => {
        this.retorno = retorno;
        this.snackBarAlert.sucessoSnackbar(this.retorno!.txtMensagem)
        window.history.back();
        this.spinner.hide();
      })
    }
    this.Limpar();
  }

  Limpar() {
    this.objReceita = new novaReceita();
    this.receita = "";
    this.paciControl.setValue(null);
  }

  VerificaSalvamento() {
    if (this.objReceita.idtipoOrientacao == null) {
      this.snackBarAlert.falhaSnackbar('Selecione o tipo de orientação referente a esta receita');
      return false;
    }
    if (this.objReceita.receita == "") {
      this.snackBarAlert.falhaSnackbar('informe a receita referente a este paciente');
      return false;
    }
    if (this.objReceita.idPaciente == null) {
      this.snackBarAlert.falhaSnackbar('informe o paciente desejado antes de continuar');
      return false;
    }
    this.objReceita.idMedico = this.usuarioLogadoService.getIdUsuarioAcesso()!;
    this.objReceita.receita = this.receita!;

    return true;
  }

  validaPaciente() {
    if (this.IdPaciente == null) {
      this.pacienteValido = false
    } else
      this.pacienteValido = true
  }
  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  CarregaPacientes() {
    this.pacientesService.GetPacienteAgenda("", this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.DadosPacientes = []


      this.DadosPacientes = retorno.filter((c:any) => c.flgInativo != true);

      if (this.CPF != '' && this.CPF != undefined && this.CPF != null) {
        if (this.DadosPacientes.length == 1)
          this.IdPaciente = this.DadosPacientes[0].idCliente

      }
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  CarregaTipoOrientacao() {
    this.ReceitaService.GetTipoOrientacao().subscribe((retorno) => {
      this.objTipoOrientacao = retorno;
      this.spinner.hide();
    }, erro => {
      console.error(erro)
      this.spinner.hide();
    })
  }

}
