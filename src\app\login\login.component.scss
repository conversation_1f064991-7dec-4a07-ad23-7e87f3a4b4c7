// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57; // Verde Saúde
$primary-light: #A3D9B1; // Verde claro suavizado
$primary-dark: #1F5F3D; // Verde escuro
$secondary-color: #F4F4F9; // Cinza Claro / Off-White
$secondary-light: #FFFFFF; // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5; // Cinza médio para hover/active
$accent-color: #4ECDC4; // Turquesa Claro (toque moderno)
$error-color: #FF6B6B; // Vermelho Pastel
$text-primary: #333333; // Cinza escuro para boa legibilidade
$text-secondary: #6B7280; // Cinza médio
$border-color: #E5E7EB; // Bordas suaves
$bg-color: #F9FAFB; // Fundo geral suave
$card-bg: #FFFFFF; // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

.circle-4 {
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, rgba($accent-color, 0.4) 0%, rgba($accent-color, 0) 70%);
  top: 40%;
  right: 20%;
  animation: floatAnimation 14s ease-in-out infinite 1s;
}

.circle-5 {
  width: 160px;
  height: 160px;
  background: radial-gradient(circle, rgba($primary-light, 0.3) 0%, rgba($primary-light, 0) 70%);
  bottom: 30%;
  left: 30%;
  animation: floatAnimation 18s ease-in-out infinite 4s;
}

.pill-3 {
  width: 50px;
  height: 120px;
  background: linear-gradient(45deg, $primary-light, $primary-color);
  top: 50%;
  left: 20%;
  transform: rotate(60deg);
  animation: rotateAnimation 18s linear infinite ;
}
.pill-4 {
  width: 50px;
  height: 120px;
  background: linear-gradient(45deg, $primary-light, $primary-color);
  top: 73%;
  left: 77%;
  transform: rotate(70deg);
  animation: rotateAnimation 15s linear infinite ;
}
.pill-5 {
  width: 40px;
  height: 150px;
  background: linear-gradient(45deg, $primary-color, $accent-color);
  top: 3%;
  left: 20%;
  transform: rotate(10deg);
  animation: rotateAnimation 30s linear infinite ;
}


.floating-dot {
  position: absolute;
  border-radius: 50%;
  background-color: white;
  opacity: 0.6;
  filter: blur(1px);
  animation: floatAndFade 10s infinite;
}

.dot-1 {
  width: 8px;
  height: 8px;
  top: 25%;
  left: 25%;
  animation-delay: 0s;
}

.dot-2 {
  width: 6px;
  height: 6px;
  top: 65%;
  left: 35%;
  animation-delay: 2s;
}

.dot-3 {
  width: 10px;
  height: 10px;
  top: 35%;
  right: 25%;
  animation-delay: 4s;
}

.dot-4 {
  width: 7px;
  height: 7px;
  bottom: 30%;
  right: 40%;
  animation-delay: 6s;
}

.dot-5 {
  width: 9px;
  height: 9px;
  bottom: 60%;
  left: 15%;
  animation-delay: 8s;
}

@keyframes floatAndFade {
  0% { 
    transform: translate(0, 0); 
    opacity: 0;
  }
  25% { 
    opacity: 0.6;
  }
  50% { 
    transform: translate(20px, -20px); 
    opacity: 0.9;
  }
  75% { 
    opacity: 0.6;
  }
  100% { 
    transform: translate(0, -40px); 
    opacity: 0;
  }
}// Elementos decorativos flutuantes
.floating-objects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.floating-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.5;
  filter: blur(8px);
}

.circle-1 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba($primary-light, 0.6) 0%, rgba($primary-light, 0) 70%);
  top: 20%;
  left: -150px;
  animation: floatAnimation 15s ease-in-out infinite;
}

.circle-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba($accent-color, 0.4) 0%, rgba($accent-color, 0) 70%);
  bottom: 10%;
  right: -200px;
  animation: floatAnimation 20s ease-in-out infinite reverse;
}

.circle-3 {
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba($primary-dark, 0.3) 0%, rgba($primary-dark, 0) 70%);
  top: 60%;
  left: 10%;
  animation: floatAnimation 12s ease-in-out infinite 2s;
}

.floating-pill {
  position: absolute;
  border-radius: 100px;
  opacity: 0.4;
  filter: blur(10px);
}

.pill-1 {
  width: 80px;
  height: 250px;
  background: linear-gradient(45deg, $primary-color, $accent-color);
  top: 15%;
  right: 10%;
  transform: rotate(30deg);
  animation: rotateAnimation 20s linear infinite;
}

.pill-2 {
  width: 60px;
  height: 180px;
  background: linear-gradient(45deg, $accent-color, $primary-color);
  bottom: 15%;
  left: 5%;
  transform: rotate(-20deg);
  animation: rotateAnimation 15s linear infinite reverse;
}

@keyframes floatAnimation {
  0% { transform: translate(0, 0); }
  50% { transform: translate(30px, 30px); }
  100% { transform: translate(0, 0); }
}

@keyframes rotateAnimation {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Reset e estilos globais
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: $bg-color;
}

// Layout principal
.background-login {
  min-height: 100vh;
  background-color: $bg-color;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  // Fundo animado com ondas e gradientes
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      linear-gradient(120deg, rgba($primary-color, 0.4) 0%, rgba($accent-color, 0.2) 100%),
      url("data:image/svg+xml,%3Csvg viewBox='0 0 1200 800' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='a' gradientUnits='userSpaceOnUse' x1='600' y1='25' x2='600' y2='777'%3E%3Cstop offset='0' stop-color='%232E8B57' stop-opacity='0'/%3E%3Cstop offset='1' stop-color='%234ECDC4' stop-opacity='0.3'/%3E%3C/linearGradient%3E%3ClinearGradient id='b' gradientUnits='userSpaceOnUse' x1='650' y1='25' x2='650' y2='777'%3E%3Cstop offset='0' stop-color='%232E8B57' stop-opacity='0'/%3E%3Cstop offset='1' stop-color='%234ECDC4' stop-opacity='0.3'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' gradientUnits='userSpaceOnUse' x1='700' y1='25' x2='700' y2='777'%3E%3Cstop offset='0' stop-color='%232E8B57' stop-opacity='0'/%3E%3Cstop offset='1' stop-color='%234ECDC4' stop-opacity='0.3'/%3E%3C/linearGradient%3E%3ClinearGradient id='d' gradientUnits='userSpaceOnUse' x1='750' y1='25' x2='750' y2='777'%3E%3Cstop offset='0' stop-color='%232E8B57' stop-opacity='0'/%3E%3Cstop offset='1' stop-color='%234ECDC4' stop-opacity='0.3'/%3E%3C/linearGradient%3E%3ClinearGradient id='e' gradientUnits='userSpaceOnUse' x1='800' y1='25' x2='800' y2='777'%3E%3Cstop offset='0' stop-color='%232E8B57' stop-opacity='0'/%3E%3Cstop offset='1' stop-color='%234ECDC4' stop-opacity='0.3'/%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath fill='url(%23a)' d='M0 0h1200v800H0z'/%3E%3Cpath d='M0 450q50-30 100 0t100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0v350H0z' opacity='.4'/%3E%3Cpath fill='url(%23b)' d='M0 475q50-25 100 0t100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0v325H0z' opacity='.5'/%3E%3Cpath fill='url(%23c)' d='M0 500q50-20 100 0t100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0v300H0z' opacity='.6'/%3E%3Cpath fill='url(%23d)' d='M0 525q50-15 100 0t100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0v275H0z' opacity='.7'/%3E%3Cpath fill='url(%23e)' d='M0 550q50-10 100 0t100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0 100 0v250H0z' opacity='.8'/%3E%3C/svg%3E");
    background-size: cover;
    background-position: center;
    z-index: -2;
    animation: gradientMovement 15s ease infinite;
  }
  
  // Partículas flutuantes melhoradas
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 5% 10%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 0.8%, transparent 1.5%),
      radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 0.7%, transparent 1.4%),
      radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 0.6%, transparent 1.3%),
      radial-gradient(circle at 60% 40%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 0.5%, transparent 1.2%),
      radial-gradient(circle at 80% 10%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 0.7%, transparent 1.4%),
      radial-gradient(circle at 10% 60%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 0.6%, transparent 1.3%),
      radial-gradient(circle at 90% 90%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 0.7%, transparent 1.5%),
      radial-gradient(circle at 30% 80%, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.4) 0.5%, transparent 1.2%),
      radial-gradient(circle at 70% 20%, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 0.7%, transparent 1.4%),
      radial-gradient(circle at 15% 25%, rgba($primary-light, 0.3) 0%, rgba($primary-light, 0.3) 0.6%, transparent 1.2%),
      radial-gradient(circle at 35% 55%, rgba($accent-color, 0.2) 0%, rgba($accent-color, 0.2) 0.7%, transparent 1.3%),
      radial-gradient(circle at 65% 75%, rgba($primary-light, 0.3) 0%, rgba($primary-light, 0.3) 0.5%, transparent 1.1%),
      radial-gradient(circle at 85% 25%, rgba($accent-color, 0.2) 0%, rgba($accent-color, 0.2) 0.6%, transparent 1.2%),
      radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.2) 0.5%, transparent 1%);
    background-size: 150% 150%;
    z-index: -1;
    animation: particlesMovement 25s linear infinite;
  }
}

// Animação para o efeito de gradiente em movimento
@keyframes gradientMovement {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// Animação para as partículas flutuantes
@keyframes particlesMovement {
  0% {
    background-position: 0% 0%;
  }
  33% {
    background-position: 30% 60%;
  }
  66% {
    background-position: 70% 40%;
  }
  100% {
    background-position: 0% 0%;
  }
}

// Removido o header conforme solicitado

// Login Container
.div-login {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  position: relative;
  z-index: 2;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;
  transition: transform $transition, box-shadow $transition;
  position: relative;
  z-index: 1;
  
  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 6px;
    background: linear-gradient(to right, $primary-color, $accent-color);
  }
}

// Form
.login-form {
  padding: 32px;
}

.app-title {
  color: $primary-color;
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  letter-spacing: -0.5px;
  margin-bottom: 8px;
  
  &::after {
    content: '';
    display: block;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, $primary-color, $accent-color);
    margin: 12px auto 0;
    border-radius: 3px;
  }
}

.input-group {
  margin-bottom: 20px;
  flex-direction: column;
}

label {
  display: block;
  color: $text-secondary;
  font-size: 14px;
  margin-bottom: 6px;
  margin-left: 10px;
  font-weight: 500;
}

.input-container {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: $text-secondary;
}

input {
  width: 100%;
  height: 48px;
  padding: 0 40px;
  border: 1px solid $border-color;
  border-radius: 10px;
  background-color: $secondary-light;
  color: $text-primary;
  transition: all $transition;
  font-size: 16px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
  }
  
  &::placeholder {
    color: $text-secondary;
    opacity: 0.7;
  }
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: $text-secondary;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition;
  
  &:hover {
    color: $primary-color;
  }
  
  &:focus {
    outline: none;
  }
}

// Error Message
.error-message {
  display: flex;
  align-items: center;
  background-color: rgba($error-color, 0.1);
  color: $error-color;
  padding: 10px 16px;
  border-radius: $border-radius;
  margin-bottom: 20px;
  font-size: 14px;
  
  i {
    margin-right: 8px;
  }
}

// Actions
.actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.btn-login {
  width: 100%;
  height: 48px;
  background: linear-gradient(45deg, $primary-color, $primary-color, $accent-color);
  background-size: 200% 200%;
  color: white;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  
  &:hover {
    background-position: right center;
    transform: translateY(-2px);
    box-shadow: 
      0 8px 25px rgba($primary-color, 0.4),
      0 0 15px rgba($accent-color, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  // Efeito de luz deslizante
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: 0.5s;
  }
  
  &:hover::after {
    left: 100%;
  }
  
  // Adicione um efeito de brilho pulsante
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: a0;
    background: radial-gradient(circle at center, 
      rgba($accent-color, 0.8) 0%, 
      rgba($primary-color, 0.2) 100%);
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease;
  }
  
  &:hover::before {
    opacity: 1;
    animation: pulse-button 2s infinite;
  }
}

@keyframes pulse-button {
  0% { transform: scale(0.95); }
  50% { transform: scale(1.1); }
  100% { transform: scale(0.95); }
}

.forgot-password {
  color: $primary-color;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition;
  
  &:hover {
    color: $primary-dark;
    text-decoration: underline;
  }
}

// Login Options
.login-options {
  margin-top: 24px;
  text-align: center;
}

.option-selector {
  display: inline-flex;
  background-color: $secondary-color;
  border-radius: 20px;
  padding: 4px;
  
  span {
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 16px;
    color: $text-secondary;
    transition: all $transition;
    
    &:hover {
      color: $text-primary;
    }
    
    &.active {
      background-color: $primary-color;
      color: white;
    }
  }
}

// Footer
.footer {
  width: 100%;
  background-color: $primary-color;
  padding: 12px 0;
  position: relative;
  z-index: 1;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  color: white;
}

.copyright {
  font-size: 14px;
  font-weight: 300;
}

.security {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 300;
  font-style: italic;
  
  i {
    margin-right: 8px;
  }
}

// Responsive Styles
@media (max-width: 768px) {
  .login-card {
    max-width: 100%;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .login-form {
    padding: 24px 16px;
  }
  
  .app-title {
    font-size: 24px;
  }
  
  .option-selector {
    width: 100%;
    justify-content: center;
    
    span {
      flex: 1;
      text-align: center;
    }
  }
}