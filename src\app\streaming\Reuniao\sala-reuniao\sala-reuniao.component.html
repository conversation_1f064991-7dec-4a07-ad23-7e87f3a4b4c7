<div>

  <!-- <div id="react" -->
  <div id="react" [className]="isOpen ?' VideoMobileOpenMenu no-desktop ': 'VideoMobileCloseMenu no-desktop'"
    *ngIf="showFillerStream">

    <!-- <iframe *ngIf="FlgVideo" [src]="urlSafe" allow="microphone; camera"></iframe> -->
  </div>

  <!-- <div id="react"  -->
  <div id="react" class="camera" *ngIf="!showFillerStream" style="height: 100vh;">

    <!-- <iframe *ngIf="FlgVideo" [src]="urlSafe" allow="microphone; camera"></iframe> -->


  </div>
</div>




<ngx-smart-modal #Inicializando [autostart]="true" identifier="Inicializando" [closable]="false" [dismissable]="false"
    [escapable]="false" customClass="nsm-centered medium-modal">

    <div class="no-mobile" style="display: flex; width: 491px !important;    background-size: 525px;
    background-position: bottom center;
    background-repeat: no-repeat;
    width: 460px;">

        <div style=" margin-right: auto; margin-left: auto;">
            <img src="assets/build/img/telemedicina.png" style="width: 490px;">
        </div>
    </div>
    <section class="example-section no-mobile">
        <mat-progress-bar class="example-margin" [value]="50" [bufferValue]="75">
        </mat-progress-bar>
    </section>

    <div class="modal-info no-mobile">

        <b> Bem-Vindo a Sala de Reunião, onde você podera tirar sua dúvidas.
        </b><br>
        <mat-divider></mat-divider>
    </div>

    <div class="no-desktop text-center">
        <b class="hight-content">
            {{ 'TELASTREAMING.NAOSETRATADEUMACONSULTA' | translate }}
        </b>
    </div>


    <mat-divider></mat-divider>
    <div class="row-button text-center p-20 ">

        <button mat-flat-button (click)="AbrirwebCan()"
            class="input-align btn button-fontm btn-primary big-btn">{{ 'TELASTREAMING.ACEITAREINICIAR' | translate }}
        </button>

        <button mat-flat-button (click)="recusarSair()" style="    margin-top: 2%;"
            class=" button-fontm input-align btn btn-danger big-btn">{{ 'TELASTREAMING.SAIR' | translate }}
        </button>
    </div>
</ngx-smart-modal>
