import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { GuiaTissModelview } from 'src/app/model/consulta';
import { ConvenioService } from 'src/app/service/convenio.service';
import { FaturaService } from 'src/app/service/fatura.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { ValidadoreseMascaras } from 'src/app/Util/validadores';
import { Uteis } from 'src/app/Util/uteis';
import { ModalTemplateComponent } from '../modal-template/modal-template.component';
import { CommonModule } from '@angular/common';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIcon } from '@angular/material/icon';
import { MatCard } from '@angular/material/card';

@Component({
    selector: 'app-modal-lista-guia-tiss',
    templateUrl: './modal-lista-guia-tiss.component.html',
    styleUrls: ['./modal-lista-guia-tiss.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      ModalTemplateComponent,
      CommonModule,
      FormsModule,
      MatCheckboxModule,
      MatIcon,
      MatCard
    ]
})
export class ModalListaGuiaTissComponent implements OnInit {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private faturaService: FaturaService,    

  ) { }

  ngOnInit() {
    this.GetListaGuiaTiss();
  }

  listaGuiaTiss: GuiaTissModelview[] = [];
  listaGuiaTissSelecionada: number[] = [];
 
  // FecharModal() {
  //   this.dialogRef.close();
  // }

  async GetListaGuiaTiss(){
    this.spinner.show();
    await this.faturaService.GetListaGuiaTiss().subscribe((ret) => {

      this.listaGuiaTiss = ret;
      this.spinner.hide();
    }, () => {
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar as guias.')
      this.spinner.hide();
      ;
    })
  }

  AdicionarGuiaTiss(event: any, id: number) {
    if (event.checked) {
      this.listaGuiaTissSelecionada.push(id);
    } else {
      this.listaGuiaTissSelecionada = this.listaGuiaTissSelecionada.filter(x => x != id);
    }
  }

  async BaixarRelatorioListaConsulta(){
    
    if (this.listaGuiaTissSelecionada.length == 0){
      this.snackBarAlert.sucessoSnackbar('Por favor, selecione ao menos uma consulta.');
      return;
    };
    

    this.spinner.show();

    await this.faturaService.BaixarRelatorioListaGuiaTiss(this.listaGuiaTissSelecionada).subscribe((ret) => {

      Uteis.BaixarFileEmPDF(ret,"Relatorio Guias");

      this.spinner.hide();
    }, () => {
      this.snackBarAlert.falhaSnackbar('Ocurreu um erro tentando baixar o arquivo.');
      this.spinner.hide();
    });
  }

}
