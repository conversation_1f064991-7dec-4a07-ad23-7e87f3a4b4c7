<div class="recipe-container">
  <div class="card main-card">
    <div class="card-header">
      <div class="header-left">
        <div class="icon-container">
          <span class="material-icons">description</span>
        </div>
        <h1 class="page-title">Cadastro de Receitas</h1>
      </div>
      <button class="btn btn-link" onclick='history.go(-1)'>
        <span class="material-icons">arrow_back</span>
      </button>
    </div>

    <div class="col-md-12 col-sm-12 col-xs-12 dados">
        
        <div class="section-content">
          <div class="form-row">
            <div class="form-group full-width">
              <ng-select 
                class="modern-select"
                [items]="DadosPacientes" 
                bindLabel="nome" 
                bindValue="idCliente" 
                placeholder="Selecione o paciente" 
                [clearable]="true" 
                [(ngModel)]="objReceita.idPaciente">
              </ng-select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group full-width">
              <ng-select 
                class="modern-select"
                [items]="objTipoOrientacao" 
                [(ngModel)]="objReceita.idtipoOrientacao" 
                placeholder="Tipo de orientação" 
                [clearable]="false" 
                [searchable]="false"
                bindLabel="tipoOrientacao" 
                bindValue="idTipoOrientacao">
              </ng-select>
            </div>
          </div>
          <div style="padding: 0 5px; height: max-content;">
            <textarea [(ngModel)]="receita" style="width: 100%; border: 1px solid #dedede; border-radius: 5px;"></textarea>
          </div>
        </div>
    </div>

    <div class="card-footer">
      <button class="btn btn-success" *ngIf="flgUser" (click)="Salvar()">
        <span class="material-icons">save</span>
        Salvar
      </button>
      <button class="btn btn-outline" (click)="Limpar()">
        <span class="material-icons">refresh</span>
        Limpar
      </button>
    </div>
  </div>
</div>