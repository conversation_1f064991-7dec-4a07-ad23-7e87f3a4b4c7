<div id="modal">
    <header>
        <div id="hdChatText">
            <mat-icon>forum</mat-icon>
            Mensagens
        </div>

        <span id="hdTextoCentro" *ngIf="this.idChatSelecionado">
            <p id="nomeUser">{{objUsuarioSelecionado.nomeUsuario}}</p>
            <p id="descUser">• {{objUsuarioSelecionado.tipoUsuario}} •</p>
        </span>

        <div id="hdBotoes">
            <button mat-icon-button [matMenuTriggerFor]="menu">
                <mat-icon>settings</mat-icon>
            </button>

            <mat-menu class="mat-menu" #menu="matMenu">
                <button class="config" mat-menu-item (click)="atualizaNotificacao_Sonora()" 
                    matTooltip="Quando ativado, se receber uma mensagem será gerado um aviso sonoro indicando uma nova mensagem recebida.">
                    <mat-icon>{{flgNotificacaoSonora? "notifications_off" : "notifications"}}</mat-icon>
                    <span class="spanMenu">{{flgNotificacaoSonora? "Desabilitar": "Habilitar"}} notificação sonora</span>
                </button>
                <button class="config" mat-menu-item (click)="atualizaNotificacao_Windows()" 
                    matTooltip="Quando ativado, se receber uma mensagem com o site minimizado você recebera um alerta do windows avisando de uma nova mensagem.">
                    <mat-icon>{{flgNotificacaoWindows? "notifications_off" : "notifications"}}</mat-icon>
                    <span class="spanMenu">{{flgNotificacaoWindows? "Desabilitar": "Habilitar"}} notificação do windows</span>
                </button>
            </mat-menu>

            <button id="btFechar" mat-icon-button (click)="fecharModal()">
                <mat-icon>close</mat-icon>
            </button>
        </div>
    </header>
    <content>
        <nav>
            <span class="input-group">
                <span class="input-group-prepend">
                    <span class="input-group-text"><mat-icon>search</mat-icon></span>
                </span>
                <input type="text" class="form-control" placeholder="Buscar contato..." [(ngModel)]="nomeBusca"
                    (keypress)="BuscarUsuario()" (ngModelChange)="BuscarUsuario()">
            </span>

            <div id="cardContato" *ngFor="let contato of lsContatos"
                (click)="selecionarUsuario(contato); contato.qtdMsgNLida = null"
                [ngClass]="contato.idUsuario == idChatSelecionado ? 'usuarioSelecionado': ''">
                <img style="border: 1px solid #000;" src="https://cdn-icons-png.freepik.com/512/3584/3584957.png?uid=R196262076&ga=GA1.1.796300145.1742296903" />

                <span id="txtContatoNome">
                    <b>{{contato!.nomeUsuario! | truncate: 31}}</b>
                    {{contato.tipoUsuario}}
                </span>

                <span id="qtdMsgContato"
                    *ngIf="contato.qtdMsgNLida && contato.qtdMsgNLida > 0">{{contato.qtdMsgNLida}}</span>
            </div>
        </nav>
        <div id="OcultarChat" *ngIf="!this.idChatSelecionado">
            <mat-icon>chat_bubble_outline</mat-icon>
            Selecione um contato para iniciar a conversa
        </div>
        <main *ngIf="this.idChatSelecionado">
            <div class="Mensagens" (scroll)="onScroll($event)">
                <div *ngFor="let msg of lsMensagens" id="LinhaMsg"
                    [ngClass]="msg.idRemetente == idUsuarioLogado ? 'remetente' : 'destinatario'">
                    <div class="msgDiv" *ngIf="msg.idRemetente == idUsuarioLogado">
                        <div class="contentMsg">
                            <span class="txtMensagem">
                                {{ msg.mensagem }}
                            </span>
                            <span class="infoMensagem" *ngIf="msg.dtaEnvio">
                                Você • {{ formatDate(msg.dtaEnvio!) }} <mat-icon matTooltip="Mensagem lida"
                                    *ngIf="msg.dtaLido" id="MsgLidaVerificado">done_all</mat-icon>
                            </span>
                        </div>
                        <span id="iconeUsario">
                            <img src="https://cdn1.iconfinder.com/data/icons/basic-22/512/1041_boy_c-1024.png" />
                        </span>
                    </div>

                    <div class="msgDiv" *ngIf="msg.idRemetente != idUsuarioLogado">
                        <span id="iconeUsario">
                            <img src="https://cdn1.iconfinder.com/data/icons/basic-22/512/1041_boy_c-1024.png" />
                        </span>
                        <div class="contentMsg">
                            <span class="infoMensagem" *ngIf="msg.dtaEnvio">
                                {{objUsuarioSelecionado!.nomeUsuario! | truncate: 15}} • {{ formatDate(msg.dtaEnvio!) }}
                            </span>
                            <span class="txtMensagem">
                                {{ msg.mensagem }}
                            </span>
                        </div>
                    </div>
                </div>

                <div class="loader-container" *ngIf="flgBtCarregarMais && flgExibeLoader">
                    <span class="loader"></span>
                </div>
            </div>

            <footer>
                <div class="input-group">
                    <textarea class="form-control" placeholder="Digite sua mensagem..." [(ngModel)]="msg"
                        (keydown.enter)="onEnter($any($event))"></textarea>

                    <div class="input-group-prepend" *ngIf="validaBtEnviarMsg()"
                        (click)="EnviarMensagem()">
                        <span class="input-group-text"><mat-icon class="ajuste-icon">send</mat-icon></span>
                    </div>
                </div>
            </footer>
        </main>
    </content>
</div>