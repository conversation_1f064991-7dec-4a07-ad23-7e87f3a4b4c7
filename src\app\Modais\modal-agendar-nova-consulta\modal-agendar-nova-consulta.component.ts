import { SpinnerService } from './../../service/spinner.service';
import { Component, OnInit } from '@angular/core';
import {
  FormControl, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { MatDialogRef as MatDialogRef, MatDialog as MatDialog } from '@angular/material/dialog';
import { Consulta } from 'src/app/model/consulta';
import { ConsultaService } from 'src/app/service/consulta.service';
import { ModalInfoSobreUsuarioComponent } from '../modal-info-sobre-usuario/modal-info-sobre-usuario.component';
import { MedicoService } from 'src/app/service/medico.service';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { AgendaService } from 'src/app/service/agenda.service';
import { ClinicaService } from 'src/app/service/clinica.service';
import { ModalAdicionarPacienteComponent } from '../modal-adicionar-paciente/modal-adicionar-paciente.component';
import { PacienteService } from 'src/app/service/pacientes.service';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { ConvenioService } from 'src/app/service/convenio.service';
import { CommonModule } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatRadioModule } from '@angular/material/radio';

@Component({
  selector: 'app-modal-agendar-nova-consulta',
  templateUrl: './modal-agendar-nova-consulta.component.html',
  styleUrls: ['./modal-agendar-nova-consulta.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIcon,
    TranslateModule,
    NgSelectModule,
    MatFormFieldModule,
    MatRadioModule
  ]
})
export class ModalAgendarNovaConsultaComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ModalAgendarNovaConsultaComponent>,
    private consultaService: ConsultaService,
    private matDialog: MatDialog,
    private medicoService: MedicoService,
    private usuarioLogadoService: UsuarioLogadoService,
    private agendaService: AgendaService,
    private spinner: SpinnerService,
    private clinicaService: ClinicaService,
    private pacienteService: PacienteService,
    private convenioService: ConvenioService
  ) { }
  //#region  Variaveis
  objConsulta = new Consulta();

  //#region  Boolean
  flgHabilitaPagamento: boolean = false;
  flgSomenteProntuario: boolean = false;
  flgProntuario: boolean = false;
  FlgRetorno: boolean = false;
  flgConvenioParticular: boolean = false;
  flgExigePagamento: boolean = false;
  paciAgendaVasil?: boolean;
  paciAgendaVal?: boolean;
  pacienteValido: boolean = true;
  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;
  loginComMedico: boolean = false;
  Edicao: boolean = false;
  Dtanasc?: boolean;
  DtanascLimpa = false;
  DtanascVasil = false;
  //#endregion

  //#region  string
  codConvenio?: string | null;
  valorConsulta?: string | null;
  valorConsultaParticular?: string;
  CPF = ""
  //#endregion

  //#region  number
  IdPaciente?: number;
  IdMedico?: number | null;
  idConvenio?: number;
  Especialidade?: number;
  tipoagendamento?: number;
  //#endregion

  //#region  FormControl
  codConv = new FormControl('', [Validators.required, Validators.maxLength(11)])
  valor = new FormControl('', [Validators.required, Validators.maxLength(11)])
  paci = new FormControl('', [Validators.required, Validators.maxLength(11)])
  medi = new FormControl('', [Validators.required, Validators.maxLength(11)])
  //#endregion

  //#region  Listas
  dadosConvenio: any = [];
  DadosClinicas: any = [];
  DadosPacientes: any = [];
  dadosNovoPaciente: any = [];
  DadosInformUsuario: any = [];
  DadosEspecialidadeMedico: any = []

  dadosTipoAgendamento: any;
  ListaMedicos: any;
  Dados: any;
  //#endregion
  //#endregion



  ngOnInit(): void {
    this.flgSomenteProntuario = this.usuarioLogadoService.getFlgProntuario()!;
    this.flgHabilitaPagamento = this.usuarioLogadoService.getFlgHabilitaPagamento()!;

    this.tipoAgendamento();
    this.CarregaMedicos();

    this.varificaTipoUsuario()

    this.getPagamentoClinica()
    this.CarregaPacientes()
    this.CarregaConvenio();

    this.Dados = []
    this.IdMedico = null
  }

  varificaTipoUsuario() {
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.loginComMedico = true

      this.medicoService.getMedicos(this.Especialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        this.ListaMedicos = retorno

        var medico = this.ListaMedicos.filter((c: any) => c.idUsuarioacesso == this.usuarioLogadoService.getIdUsuarioAcesso())
        this.IdMedico = medico[0].idMedico
        this.spinner.hide();


      }, () => {
        this.spinner.hide();
      })
    } else
      this.loginComMedico = false
    this.spinner.hide();

  }

  tipoAgendamento() {
    this.spinner.show();
    this.agendaService.getTipoAgendamento(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {


      this.dadosTipoAgendamento = retorno;

      this.spinner.hide();
    })
  }

  CarregaConvenio() {

    this.convenioService.getConvenios(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      this.dadosConvenio = retorno;
      this.spinner.hide();
    })
  }

  getPagamentoClinica() {
    this.clinicaService.getPagamentoClinica(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      // this.valorConsultaParticular = retorno

      if (retorno) {
        retorno = this.verificaCasaDecimal(retorno)
        retorno = this.aplicarMascaraValor(retorno)
        this.valorConsultaParticular = retorno
      }
      this.spinner.hide();

    });
  }

  AddnovoPaciente() {
    this.dadosNovoPaciente = []

    this.campoCPFVazil = false;
    this.campoCPFInvalido = false;

    this.CarregaClinicas();
    this.matDialog.open(ModalAdicionarPacienteComponent)
  }

  //#region CarregarDados
  CarregaMedicos() {
    this.medicoService.getMedicos(this.Especialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {



      this.ListaMedicos = retorno

      if (this.ListaMedicos.length == 1) {
        this.IdMedico = this.ListaMedicos[0].idMedico;
      }

      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  CarregaClinicas() {
    try {
      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {

        this.DadosClinicas = retornaClinicas


      }, () => {

        this.spinner.hide()
      })
    } catch (error) {

      this.spinner.hide()
    }
  }

  CarregaPacientes() {
    this.pacienteService.GetPacienteAgenda(this.CPF, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.DadosPacientes = []


      this.DadosPacientes = retorno.filter((c: any) => c.flgInativo != true);

      if (this.CPF != '' && this.CPF != undefined && this.CPF != null) {
        if (this.DadosPacientes.length == 1)
          this.IdPaciente = this.DadosPacientes[0].idCliente

      }
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }


  PreencheValorPagamento() {

    this.codConvenio = null
    this.valorConsulta = null

    if (this.idConvenio) {

      var convenio = this.dadosConvenio.filter((c: any) => c.idConvenio == this.idConvenio)

      if (convenio[0].desConvenio == "Particular") {
        // this.valorConsulta = this.valorConsultaParticular
        this.flgConvenioParticular = true
      }
      else {
        this.flgConvenioParticular = false
      }

      // this.valorConsulta = this.verificaCasaDecimal(convenio[0].valorConsulta)


      if (convenio[0].valorConsulta) {
        this.valorConsulta = convenio[0].valorConsulta;
        this.valorConsulta = this.verificaCasaDecimal(this.valorConsulta)
        this.valorConsulta = this.aplicarMascaraValor(this.valorConsulta)
      }



    } else
      this.flgConvenioParticular = true

  }

  verificaCasaDecimal(valor: any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

  aplicarMascaraValor(v: any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }
  //#endregion

  SalvarConsulta() {

  }

  ValidaPaciAgenda(pac: any) {

    pac = this.IdPaciente;

    if (pac > 0) {
      this.paciAgendaVasil = false;
      this.paciAgendaVal = false;
    }
    else {
      this.paciAgendaVasil = false;
      this.paciAgendaVal = true;
    }
  }

  RetornoConvenio(idPaciente: any, idConvenio: any) {

    if (!idPaciente || !idConvenio)
      return

    this.consultaService.GetRetornoConvenio(idPaciente, idConvenio).subscribe((retorno) => {

      this.FlgRetorno = retorno;
    })

  }

  validaPaciente() {
    if (this.IdPaciente == null) {
      this.pacienteValido = false
    } else
      this.pacienteValido = true
  }

  verificaConvenio() {
    if (this.codConv == null || this.codConv == undefined) {
      this.valorConsulta = null
    }
  }

  informacao(user: any, id: any) {
    this.DadosInformUsuario = {}
    if (user == 'Paciente') {

      var user = this.DadosPacientes.filter((c: any) => c.idCliente == id);
      this.DadosInformUsuario = user[0]


      this.matDialog.open(ModalInfoSobreUsuarioComponent, {
        data: this.DadosInformUsuario
      })

    }
    else {
      this.DadosInformUsuario = this.ListaMedicos.filter((c: any) => c.idMedico == id);
      this.matDialog.open(ModalInfoSobreUsuarioComponent, {
        data: this.DadosInformUsuario
      })
    }
  }

  //#region  Mascaras
  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  ValidaDtaEd(dta: any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.DtanascVasil = true;
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.DtanascVasil = false;
      this.Dtanasc = false;
      this.DtanascLimpa = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
      this.DtanascVasil = false;
    }
    else
      this.Dtanasc = false
  }
  //#endregion

  //#region Get ERRORS
  getErrorMessagepaci() {
    return this.paci.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.paci.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';

  }

  getErrorMessagemedi() {
    return this.medi.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.medi.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessagevalorConsulta() {
    return this.valor.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.valor.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessageCodConv() {
    return this.codConv.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.codConv.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }
  //#endregion
}
