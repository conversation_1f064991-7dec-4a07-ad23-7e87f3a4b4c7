<mat-card appearance="outlined" class="mother-div">
  <mat-card-title class="spacer-card" style="padding: unset">

    <div class="col-md-12" style="padding: unset">
      <mat-icon class="icon-title">feedback</mat-icon><a class="title-content fonte-tamanho">Históricos de Recados</a>
    </div>
  </mat-card-title>

  <mat-divider class="p-t-20"></mat-divider>

  <mat-card-content>
    <div class="col-md-4 col-sm-12" style="padding: unset; margin-top: 3%;">
      <mat-form-field class="col-12  " style=" font-size: 12px; height: 31px;">
        <input matInput placeholder="Busca por Data" name="DataConsulta" (keyup)="ValidaDta($any($event.target).value)"
          id="DtaConsulta" (keypress)="mascaraData($event)" [(ngModel)]="pesquisa" maxlength="10">
      </mat-form-field>

      <span class="aviso-span text-center" *ngIf="DtaErrado == true"
        style="font-size: 65%;color: #f44336;font-weight: 600;display:flex; margin-top: 5px;">{{ 'TELACADASTROMEDICO.ERRODATA' | translate }}</span>


    </div>

    <mat-card-content>
      <div class="col-md-12 col-sm-12 col-xs-12 p-t-20" style="padding: unset; margin-top: 30px;" *ngIf="Flgtable">
        <div class="col-md-12 col-sm-12 col-xs-12 no-mobile-card">
          <table class="table" id="DatatableCliente">
            <thead>


              <tr>
                <th class="">
                  Recado do Dia:</th>
                <th class="">
                  Criador do recado:</th>
                <th class="">
                  Visualizado Dia:</th>
              </tr>
            </thead>
            <tbody *ngFor="let item of DadosRecados">
              <!-- RecadoDoDia -->
              <tr  class="card_table">

                <td [className]="dtaLinha == item.dtaRecadoLinha ? 'RecadoDoDia' :'' " id="nome" style="width:30%">
                  <label class=" quebra"><b>{{item.dia | date: 'dd/MM/yyyy'}}</b></label>

                </td>
                <td [className]="dtaLinha == item.dtaRecadoLinha ? 'RecadoDoDia' :'' "  style="min-width:30%;">
                  {{item.usuarioGerador}}
                  <!-- {{ item.dtaCadastro }} -->
                </td>
                <td [className]="dtaLinha == item.dtaRecadoLinha ? 'RecadoDoDia' :'' "  *ngIf="item.flgVisuali" style=" text-align: center;" (click)="MensagemdoDia(item.idRecado)">
                  
                  <mat-icon style="vertical-align: sub;color: #0f86ff;cursor: pointer;">remove_red_eye</mat-icon>
                  {{item.dtaVisualizado | date:'dd/MM/yyyy HH:mm'}}
                  <!-- {{ item.dtaCadastro }} -->
                </td>
                <td [className]="dtaLinha == item.dtaRecadoLinha ? 'RecadoDoDia' :'' " 
                  style="font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important; text-align: center; cursor: pointer;"
                  *ngIf="!item.flgVisuali" (click)="MensagemdoDia(item.idRecado)">
                  <mat-icon style="vertical-align: sub;color: #0f86ff; cursor: pointer;" >remove_red_eye</mat-icon><span
                    style="vertical-align: super;margin-left: 8px;">Visualizar</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div *ngIf="!Flgtable" class="text-center" style="margin-top: 10%;">
        <h1>Não há recados. {{ DesDataDia }}</h1>
      </div>
    </mat-card-content>


    <!-- MODAL MENSAGEM DO DIA -->
    <ngx-smart-modal #mensagemDiaNova identifier="mensagemDiaNova"
      customClass="nsm-centered medium-modal form-modal emailmodal">
      <div class="modal-container">
        <div class='col-md-12' style="width: 100%;">
          <mat-form-field class="col-md-12 col-sm-12 col-xs-12  " style="margin-top: 20px;" appearance="outline"
            floatLabel="always">
            <mat-label>Mensagem do dia de {{UsuarioRecado}} </mat-label>
            <textarea matInput #input maxlength="500" name="Observação" disabled [(ngModel)]="DesRecadoDia"
              style="max-height: 250px; min-height: 200px; color: black;"></textarea>

          </mat-form-field>

        </div>
      </div>
    </ngx-smart-modal>