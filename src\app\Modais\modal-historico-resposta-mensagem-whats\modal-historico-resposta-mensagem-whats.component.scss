/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$warning-color: #FFD166; /* Amarelo alerta */
$success-color: #06D6A0; /* Verde sucesso */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* <PERSON><PERSON><PERSON> suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo principal */
.modal-content {
    flex: 1;
    padding: 20px;
    background-color: $secondary-light;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow-y: auto;
}

/* Seção de filtros */
.filters-section {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    padding: 16px;
    background-color: $secondary-color;
    border-radius: $border-radius;
    margin-bottom: 16px;
}

.filter-field {
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

label {
    font-size: 14px;
    color: $text-secondary;
    font-weight: 500;
}

.custom-select {
    padding: 10px 12px;
    border: 1px solid $border-color;
    border-radius: $border-radius;
    background-color: $secondary-light;
    color: $text-primary;
    font-size: 14px;
    transition: border-color $transition ease;
    width: 100%;
}

.custom-select:focus {
    outline: none;
    border-color: $primary-color;
}

/* Lista de consultas */
.appointments-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
}

.appointment-card {
    background-color: $card-bg;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    overflow: hidden;
    transition: box-shadow $transition ease;
}

.appointment-card:hover {
    box-shadow: $box-shadow;
}

.appointment-header {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    gap: 8px;
    border-bottom: 1px solid $border-color;
}

.toggle-button {
    background-color: transparent;
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: $primary-color;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
}

.toggle-button:hover:not([disabled]) {
    background-color: $secondary-color;
}

.toggle-button[disabled] {
    color: $secondary-dark;
    cursor: default;
}

.toggle-icon {
    transition: transform $transition ease;
}

.appointment-info {
    flex: 1;
}

.appointment-title {
    font-size: 15px;
    color: $text-primary;
    margin-bottom: 4px;
}

.appointment-date {
    font-size: 13px;
    color: $text-secondary;
}

.highlight {
    font-weight: 600;
    color: $primary-dark;
}

/* Container de mensagens */
.messages-container {
    padding: 16px;
    background-color: $secondary-color;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.message-item {
    display: flex;
    justify-content: space-between;
    background-color: $card-bg;
    border-radius: $border-radius;
    padding: 12px;
    border-left: 4px solid $primary-light;
}

.message-content {
    flex: 1;
    padding-right: 16px;
}

.message-text {
    font-size: 14px;
    color: $text-primary;
    margin-bottom: 4px;
}

.message-type {
    font-size: 12px;
    color: $text-secondary;
    font-style: italic;
}

.message-status {
    min-width: 160px;
    font-size: 13px;
    font-weight: 500;
    padding: 4px 10px;
    border-radius: 12px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-waiting {
    color: $warning-color;
    background-color: rgba($warning-color, 0.1);
    border: 1px solid rgba($warning-color, 0.3);
}

.status-answered {
    color: $success-color;
    background-color: rgba($success-color, 0.1);
    border: 1px solid rgba($success-color, 0.3);
}

.status-no-response {
    color: $text-secondary;
    background-color: rgba($text-secondary, 0.1);
    border: 1px solid rgba($text-secondary, 0.3);
}

/* Estado vazio */
.empty-state {
    text-align: center;
    color: $text-secondary;
    padding: 20px;
    font-style: italic;
    background-color: $secondary-light;
    border-radius: $border-radius;
    border: 1px dashed $border-color;
}

/* Rodapé do modal */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px;
    background-color: $secondary-color;
    border-top: 1px solid $border-color;
}

/* Botões de ação */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 120px;
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: darken($secondary-dark, 5%);
}

/* Responsividade */
@media (max-width: 768px) {
    .message-item {
        flex-direction: column;
        gap: 8px;
    }
    
    .message-status {
        align-self: flex-start;
    }
    
    .filters-section {
        flex-direction: column;
    }
    
    .action-button {
        min-width: 0;
        padding: 8px;
    }
    
    .action-button span {
        display: none;
    }
}