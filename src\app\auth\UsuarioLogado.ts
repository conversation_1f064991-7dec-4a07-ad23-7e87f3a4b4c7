import { Clinica } from '../model/clinica';

export interface UsuarioLogado {


    idPessoa: number
    idUsuarioAcesso:number;
    nome: string
    cpf: string
    email: string
    imagem64: string
    idTipoUsuario: number
    flgPrimeiroAcesso: boolean
    tokenLogado: string
    flgLogado: boolean
    flgProntuario: boolean
    idUltimaClinica: number

    clinicas: Clinica[];

    //     usuario: Usuario;
    //     pessoa: Pessoa;
}
