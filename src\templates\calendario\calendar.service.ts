import { Injectable } from '@angular/core';
import { Agendamento, DiaDoMes, ProcessedAgendamento } from './calendario.models';

@Injectable({
    providedIn: 'root'
})
export class CalendarService {

    constructor() { }

    // ===== SHARED METHODS =====
    formatarHora(data: Date): string {
        const horas = data.getHours().toString().padStart(2, '0');
        const minutos = data.getMinutes().toString().padStart(2, '0');
        return `${horas}:${minutos}`;
    }

    gerarCorAleatoria(tipoId: number): string {
        const coresTipoEspecifico = {
            1: '#4285F4',
            2: '#FBBC05',
            3: '#34A853',
            4: '#EA4335',
            5: '#6a676b',
        };

        if (tipoId === 1) return coresTipoEspecifico[1];
        if (tipoId === 2) return coresTipoEspecifico[2];
        if (tipoId === 3) return coresTipoEspecifico[3];
        if (tipoId === 4) return coresTipoEspecifico[4];
        if (tipoId === 5) return coresTipoEspecifico[5];

        const coresPorTipo = [
            '#4285F4', '#EA4335', '#FBBC05', '#34A853', '#9C27B0',
            '#FF9800', '#795548', '#607D8B', '#00BCD4', '#E91E63'
        ];

        if (tipoId > 0 && tipoId <= coresPorTipo.length) {
            return coresPorTipo[tipoId - 1];
        }

        const hue = (tipoId * 137) % 360;
        return `hsl(${hue}, 70%, 60%)`;
    }

    // ===== CALENDAR METHODS =====
    gerarCalendario(mesAtual: Date, agendamentos: Agendamento[]): DiaDoMes[] {
        const diasDoMes: DiaDoMes[] = [];
        const ultimoDia = new Date(mesAtual.getFullYear(), mesAtual.getMonth() + 1, 0);

        for (let dia = 1; dia <= ultimoDia.getDate(); dia++) {
            const data = new Date(mesAtual.getFullYear(), mesAtual.getMonth(), dia);
            diasDoMes.push({
                data: data,
                agendamentos: this.agruparAgendamentosPorDataETipo(data, agendamentos)
            });
        }

        return diasDoMes;
    }

    agruparAgendamentosPorDataETipo(data: Date, agendamentos: Agendamento[]): { [tipo: number]: { count: number, descricao: string } } {
        const dataStr = data.toDateString();
        const agrupados: { [tipo: number]: { count: number, descricao: string } } = {};

        agendamentos.forEach((agendamento) => {
            if (agendamento.DtAgendamento.toDateString() === dataStr) {
                if (!agrupados[agendamento.TipoAgendamento]) {
                    agrupados[agendamento.TipoAgendamento] = {
                        count: 1,
                        descricao: agendamento.DescTipoAgendamento || `Tipo ${agendamento.TipoAgendamento}`
                    };
                } else {
                    agrupados[agendamento.TipoAgendamento].count += 1;
                }
            }
        });

        return agrupados;
    }

    // ===== SCHEDULER METHODS =====
    gerarHorasDoDia(intervalo: number): string[] {
        const horasDoDia: string[] = [];
        for (let hora = 5; hora < 24; hora++) {
            for (let minuto = 0; minuto < 60; minuto += intervalo) {
                const horaFormatada = `${hora.toString().padStart(2, '0')}:${minuto.toString().padStart(2, '0')}`;
                horasDoDia.push(horaFormatada);
            }
        }
        return horasDoDia;
    }

    processarEventosDoDia(
        diaSelecionado: Date,
        agendamentos: Agendamento[],
        horasDoDia: string[],
        intervaloAgenda: number
    ): { [horaKey: string]: ProcessedAgendamento[] } {
        const eventosProcessados: { [horaKey: string]: ProcessedAgendamento[] } = {};
        const eventosPrimarios = new Set<number>();

        const eventosNoDia = agendamentos.filter(agendamento =>
            agendamento.DtAgendamento.toDateString() === diaSelecionado.toDateString()
        );

        horasDoDia.forEach(hora => {
            const [horaStr, minutoStr] = hora.split(':');
            const horaInicio = parseInt(horaStr);
            const minutoInicio = parseInt(minutoStr);

            const dataHoraInicio = new Date(diaSelecionado);
            dataHoraInicio.setHours(horaInicio, minutoInicio, 0, 0);

            const dataHoraFim = new Date(dataHoraInicio);
            dataHoraFim.setMinutes(dataHoraInicio.getMinutes() + intervaloAgenda);

            const eventosNaHora = eventosNoDia.filter(evento => {
                const eventoInicio = evento.horaInicioAgendamento;
                const eventoFim = evento.horaFinalAgendamento;
                return eventoInicio < dataHoraFim && eventoFim > dataHoraInicio;
            });

            if (eventosNaHora.length === 0) {
                eventosProcessados[hora] = [];
                return;
            }

            const grupos = this.agruparEventosSobrepostos(eventosNaHora);

            const horaEventosProcessados: ProcessedAgendamento[] = [];

            grupos.forEach(grupo => {
                const colunas = this.distribuirEventosEmColunas(grupo);
                const numColunas = colunas.length;

                grupo.forEach((evento) => {
                    const coluna = colunas.findIndex(col => col.includes(evento));
                    const largura = 1 / numColunas;

                    const horaEventoInicio = evento.horaInicioAgendamento;
                    const horaEventoFim = evento.horaFinalAgendamento;

                    const slotInicio = dataHoraInicio.getTime();
                    const slotFim = dataHoraFim.getTime();

                    const inicioEfetivo = Math.max(horaEventoInicio.getTime(), slotInicio);
                    const fimEfetivo = Math.min(horaEventoFim.getTime(), slotFim);

                    const topPercent = ((inicioEfetivo - slotInicio) / (slotFim - slotInicio)) * 100;
                    const heightPercent = ((fimEfetivo - inicioEfetivo) / (slotFim - slotInicio)) * 100;

                    const isStartHour = this.formatarHora(horaEventoInicio).startsWith(hora.substring(0, 2));
                    const notYetPrimary = !eventosPrimarios.has(evento.IdAgendamento || 0);
                    const isPrimary = isStartHour || notYetPrimary;

                    if (isPrimary && evento.IdAgendamento) {
                        eventosPrimarios.add(evento.IdAgendamento);
                    }

                    horaEventosProcessados.push({
                        ...evento,
                        width: largura,
                        left: coluna * largura,
                        top: topPercent,
                        height: heightPercent,
                        column: coluna,
                        isPrimary: isPrimary
                    });
                });
            });

            eventosProcessados[hora] = horaEventosProcessados;
        });

        eventosNoDia.forEach(evento => {
            if (evento.IdAgendamento && !eventosPrimarios.has(evento.IdAgendamento)) {
                for (const hora of horasDoDia) {
                    const eventosNaHora = eventosProcessados[hora] || [];
                    const eventoEncontrado = eventosNaHora.find(e => e.IdAgendamento === evento.IdAgendamento);
                    if (eventoEncontrado) {
                        eventoEncontrado.isPrimary = true;
                        eventosPrimarios.add(evento.IdAgendamento);
                        break;
                    }
                }
            }
        });

        return eventosProcessados;
    }

    agruparEventosSobrepostos(eventos: Agendamento[]): Agendamento[][] {
        if (eventos.length === 0) return [];

        const ordenados = [...eventos].sort((a, b) =>
            a.horaInicioAgendamento.getTime() - b.horaInicioAgendamento.getTime()
        );

        const grupos: Agendamento[][] = [];
        let grupoAtual: Agendamento[] = [ordenados[0]];

        for (let i = 1; i < ordenados.length; i++) {
            const evento = ordenados[i];

            const sobreposicao = grupoAtual.some(eventoGrupo =>
                evento.horaInicioAgendamento < eventoGrupo.horaFinalAgendamento &&
                eventoGrupo.horaInicioAgendamento < evento.horaFinalAgendamento
            );

            if (sobreposicao) {
                grupoAtual.push(evento);
            } else {
                grupos.push(grupoAtual);
                grupoAtual = [evento];
            }
        }

        grupos.push(grupoAtual);
        return grupos;
    }

    distribuirEventosEmColunas(eventos: Agendamento[]): Agendamento[][] {
        if (eventos.length === 0) return [];

        const ordenados = [...eventos].sort((a, b) => {
            const diffInicio = a.horaInicioAgendamento.getTime() - b.horaInicioAgendamento.getTime();
            if (diffInicio !== 0) return diffInicio;

            const duracaoA = a.horaFinalAgendamento.getTime() - a.horaInicioAgendamento.getTime();
            const duracaoB = b.horaFinalAgendamento.getTime() - b.horaInicioAgendamento.getTime();
            return duracaoB - duracaoA;
        });

        const colunas: Agendamento[][] = [];

        ordenados.forEach(evento => {
            let colunaIndex = -1;

            for (let i = 0; i < colunas.length; i++) {
                const coluna = colunas[i];

                const podeAdicionar = coluna.every(eventoExistente =>
                    evento.horaInicioAgendamento >= eventoExistente.horaFinalAgendamento ||
                    evento.horaFinalAgendamento <= eventoExistente.horaInicioAgendamento
                );

                if (podeAdicionar) {
                    colunaIndex = i;
                    break;
                }
            }

            if (colunaIndex === -1) {
                colunas.push([evento]);
            } else {
                colunas[colunaIndex].push(evento);

                colunas[colunaIndex].sort((a, b) =>
                    a.horaInicioAgendamento.getTime() - b.horaInicioAgendamento.getTime()
                );
            }
        });

        return colunas;
    }

    getEstiloEvento(evento: ProcessedAgendamento): any {
        const estilo: any = {
            top: `${evento.top}%`,
            height: `${evento.height}%`,
            width: `${evento.width * 100}%`,
            left: `${evento.left * 100}%`,
        };

        if (evento.CorEvento) {
            estilo.backgroundColor = evento.CorEvento;
        } else {
            estilo.backgroundColor = this.gerarCorAleatoria(evento.TipoAgendamento);
        }

        if (evento.CorTexto) {
            estilo.color = evento.CorTexto;
        }

        return estilo;
    }


}