import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatDialog as MatDialog, MatDialogRef as MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { AlertComponent } from '../alert/alert.component';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ProcedimentoComponent } from '../lista-procedimento/procedimento/procedimento.component';
import { ConvenioService } from '../service/convenio.service';
import { SpinnerService } from '../service/spinner.service';
import { ValidadoreseMascaras } from '../Util/validadores';
import { EnumCaraterAtendimentoDescricao, EnumIndicacaoAcidenteDescricao, EnumRegimeAtendimentoDescricao, EnumSaudeOcupacionalDescricao, EnumTipoAtendimentoDescricao } from '../Util/EnumGuiaTiss';
import { GuiaTissModelview } from '../model/consulta';
import { ConsultaService } from '../service/consulta.service';
import { ListaProcedimentoComponent } from '../lista-procedimento/lista-procedimento.component';
import { ProcedimentoModelview } from '../model/procedimento';
import { ModalTemplateComponent } from '../Modais/modal-template/modal-template.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-guia-tiss',
    templateUrl: './guia-tiss.component.html',
    styleUrls: ['./guia-tiss.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      ModalTemplateComponent,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatFormFieldModule,
      MatSelectModule,
      MatIcon,
    ]
})
export class GuiaTissComponent implements OnInit {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private formBuilder: FormBuilder,
    private consultaService: ConsultaService,
    private matDialog: MatDialog,
    public dialogRef: MatDialogRef<ProcedimentoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { idConsulta: number; idFatura: number; loteProcedimento: ProcedimentoModelview[] }
  ) { this.objGuiaTiss.loteProcedimento = data.loteProcedimento;
      this.idConsulta = data.idConsulta;
      this.idFatura = data.idFatura;
  }

  formGuiaTiss?: FormGroup;

  objGuiaTiss: GuiaTissModelview = new GuiaTissModelview();
  idConsulta: number;
  idFatura: number;
  enumCaraterAtendimentoDescricao = EnumCaraterAtendimentoDescricao;
  listaTipoAtendimento = EnumTipoAtendimentoDescricao;
  listaIndicacaoAcidente = EnumIndicacaoAcidenteDescricao;
  listaRegimeAtendimento = EnumRegimeAtendimentoDescricao;
  listaSaudeOcupacional = EnumSaudeOcupacionalDescricao;

  ngOnInit() {
    
    this.CriarFormGroup();
  }

  CriarFormGroup(){
    this.formGuiaTiss = this.formBuilder.group({
      idGuiaTiss:           [0],
      numGuiaPrestador:     ['', Validators.required],
      numGuiaPrincipal:     ['', Validators.required],
      numGuiaOperadora:     ['', Validators.required],
      numSenha:             ['', Validators.required],
      idFatura:             [this.idFatura, Validators.required],
      idClinica:            [2],
      desClinica:           [''],
      idMedico:             [5],
      nomeMedico:           [''],
      idTipoAtendimento:    [null, Validators.required],
      desTipoAtendimento:   [''],
      idIndicacaoAcidente:  [null, Validators.required],
      desIndicacaoAcidente: [''],
      idTipoConsulta:       [null],
      desTipoConsulta:      [''],
      idRegimeAtendimento:  [null, Validators.required],
      desRegintemAtendimento: [''],
      idSaudeOcupacional:   [null, Validators.required],
      desSaudeOcupacional:  [''],
      idCaraterAtendimento: [null],
      desCaraterAtendimento: [''],
      dtaAutorizacao:      [null],
      dtaValidadeSenha:    [null],
      flgRn:               [false], 
      flgInativo:          [false],
      idConsulta:          [this.idConsulta, Validators.required],
      dataHoraSolicitacao: [null], 
      indicacaoClinica:    [''],  
      caraterAtendimento:  ['']   
    });
  }

  FecharModal() {
    this.dialogRef.close();
  }

  async SalvarGuiaTiss(){
    
    if (this.formGuiaTiss?.valid){     
      this.spinner.show();
        this.objGuiaTiss = { ...this.formGuiaTiss.value };
        this.objGuiaTiss.loteProcedimento = this.data.loteProcedimento
      
      await this.consultaService.SalvarGuiaTiss(this.objGuiaTiss).subscribe(() => {
        this.snackBarAlert.sucessoSnackbar('Guia salva com sucesso.')
        this.dialogRef.close();
      }, () => {
        this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando salvar a guia.')
      })
      this.spinner.hide();
    }
    else{
      this.snackBarAlert.falhaSnackbar("Por favor preencher todos os campos.")
    }
  }

  AbrirListaProcedimento(){
    this.dialogRef.close();
    this.matDialog.open(ListaProcedimentoComponent, {
      width: '710px',
      height: '90vh',
      data: this.idConsulta
    });
  }

}
