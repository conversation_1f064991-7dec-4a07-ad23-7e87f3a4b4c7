import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { catchError, Observable, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { AcessoRapidoEmail } from '../model/acesso-rapido-consulta';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class EnvioEmailService {

    constructor(private http: HttpClient, private spinner: SpinnerService) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });




    public MandaEmailAcesso(objAcesso :AcessoRapidoEmail): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/EnvioEmail/envioEmailAcesso',objAcesso );
    }
    
    public GetUsuarioBoasVindas(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/EnvioEmail/GetUsuarioBoasVindas/' + id);
    }
    
    public GetQuantEmail(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/EnvioEmail/GetQuantEmail/' + id);
    }
    // public inativarPaciente(id): Observable<any> {
    //     return this.http.delete(environment.apiEndpoint + '/Paciente/InativarPaciente/' + id);
    // }

    // public salvarPaciente(cliente: Cliente): Observable<any> {

    //     return this.http.post(environment.apiEndpoint + '/Paciente/', cliente);
    // }

    public EnviarEmailBoasVindas(idUsuarioDestinatario: number){
        let params = new HttpParams();
        params = params.append('idUsuarioDestinatario', idUsuarioDestinatario);
        return this.http.get(environment.apiEndpoint + '/EnvioEmail/EnviarEmailBoasVindas', {params})
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }
}