import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class CidadeService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getCidades() {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Cidade')
            .toPromise();
    }
    
    // public getUF() {
    //     return this.http.get(environment.apiEndpoint + '/Uf')
    //         .toPromise();
    // }

}
