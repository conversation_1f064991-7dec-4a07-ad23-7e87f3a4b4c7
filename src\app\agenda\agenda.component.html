<ngx-smart-modal #mensagemDiaAgenda identifier="mensagemDiaAgenda" class="custom"
    customClass="nsm-centered medium-modal form-modal emailmodal" [dismissable]="false">
    <div class="modal-content modal-mensagemdia">
        <div _ngcontent-c6 class="col-md-12 mt-3 mb-2 frase-dia" style="margin-left: 5px; display: flex;">
            <mat-icon _ngcontent-c6 class="icon-title mat-icon notranslate material-icons mat-icon-no-color" role="img"
                aria-hidden="true">access_alarm</mat-icon>
            <a _ngcontent-c6 class="title-content mt-2 ">
                {{'TELAAGENDA.MENSAGEMDODIA2' | translate}} {{ DiaRecado}}
                {{'TELAAGENDA.PARA' | translate}} {{NomeMedico}}</a>

        </div>

        <div class='col-md-12 div-textarea-msn'>

            <mat-form-field class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 20px;" hintLabel="Máx. 500"
                floatLabel="always" appearance="outline">
                <mat-label>Digite a Mensagem</mat-label>
                <textarea matInput required maxlength="500" name="obs" [(ngModel)]="DesRecadoDia"
                    style="max-height: 250px; min-height: 100px; "></textarea>
                <mat-hint align="end">{{ 0 + DesRecadoDia.length
                    }}/500</mat-hint>
                <mat-error *ngIf="campoDesRecadoVazio==true">Esse campo deve ser
                    preenchido</mat-error>
            </mat-form-field>

            <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom text-center botao-enviar-dia">
                <button class="btn-primary " mat-raised-button style="margin-right: 2%; color:white;margin-bottom: 10px"
                    (click)="SalvarMensagem()">
                    <mat-icon>save</mat-icon>{{'TELAAGENDA.ENVIAR' | translate}}
                </button>
            </div>
        </div>

        <div class="col-12 mt-1 mb-1" *ngIf="DadosRecados.length > 0" style=" max-height: 220px;  overflow: auto;">
            <a _ngcontent-c6 class="title-content mt-2">Mensagens do dia</a>
            <table class="table table-striped table-hover" style="margin-bottom: 5px;">
                <tbody *ngFor="let item of DadosRecados" style="border-radius: 10px;">
                    <tr class="card_table no-mobile-card ">
                        <td style="min-width:30%;">
                            <b>Usuário Gerador: {{item.usuarioGerador}}</b>
                            <!-- {{ item.dtaCadastro }} -->
                        </td>
                        <td style=" text-align: center;">
                            <b>Data de Criação: {{item.dtaCriacao |
                                date:'dd/MM/yyyy HH:mm'}}</b>
                            <!-- {{ item.dtaCadastro }} -->
                        </td>
                        <td style=" text-align: center;">
                            <b title="{{item.recado}}">Mensagem: {{item.recado
                                }}</b>
                            <!-- {{ item.dtaCadastro }} -->
                        </td>
                        <td class title="Excluir Mensagem"
                            style="font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important; text-align: center; cursor: pointer;"
                            (click)="AbrirModalDeletarMensagemDia(item.idRecado)">
                            <mat-icon style="vertical-align: sub;color: #0f86ff;">clear</mat-icon>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</ngx-smart-modal>

<div class=" agenda-component">
    <div class="container-fluid ">
        <div class="row info-agenda-espec">
            <div class="col-md-6 col-sm-12 col-12 " apperance="legacy" style="margin-top: 5%;">
                <ng-select [items]="DadosEspecialidade" placeholder="Filtrar por Especialidade" style="font-size: 12px;"
                    bindLabel="desEspecialidade" (change)="FiltroEspecialidade()" bindValue="idEspecialidade"
                    name="especialidade" [selectOnTab]="true" [(ngModel)]="Especialidade">
                </ng-select>
            </div>

            <div class="col-md-6 col-sm-12 col-12 " apperance="legacy" style="margin-top: 5%;">
                <ng-select [items]="ListaMedicos" placeholder="{{ 'TELAAGENDA.MEDICO' | translate }}"
                    style="font-size: 12px;" [disabled]="loginComMedico" bindLabel="nomeMedico" bindValue="idMedico"
                    name="medicos" (change)="CarregaAgenda('dia')" [selectOnTab]="true" [(ngModel)]="IdMedico">
                </ng-select>
            </div>

            <div class="col-md-9"></div>
        </div>
    </div>

    <div class="container-fluid" *ngIf="Agenda">
        <div class="row text-center">

            <div class="col-md-4 col text-left ali" style="margin-top: 30px" *ngIf="Agenda">
                <h2 class="semana-mes-dia">
                    {{ viewDate | calendarDate:(view + 'ViewTitle'): locale}}
                </h2>
            </div>

            <div class="col-md-3 ">
                <div class="btn-group" style="height:30px;margin-top: 30px ">

                    <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
                        <mat-button-toggle value="anterior" (click)="changeView('month')"
                            [class.active]="view === 'month'"
                            style="background-color:#007bff; color:white; margin-top: -10px">
                            {{ 'TELAAGENDA.MES' | translate }}
                        </mat-button-toggle>
                        <mat-button-toggle value="proximo" (click)="changeView('day')" [class.active]="view === 'day'"
                            style="background-color:#007bff;color:white;margin-top: -10px">
                            {{ 'TELAAGENDA.DIA' | translate }}
                        </mat-button-toggle>
                    </mat-button-toggle-group>
                </div>
            </div>
            <div class="col-md-2 " apperance="legacy" style="margin-top: 2.9%;">
                <mat-form-field class="custom-dias">
                    <input matInput placeholder="{{ 'TELAAGENDA.CALCULARDIAS' | translate }}" name="Dias" maxlength="3"
                        id="CalculaDia" (change)="contadorDias($any($event.target).value)"
                        (keyup.enter)="contadorDias($any($event.target).value);" mask='0000'
                        (keyup)="mascaraNumeros($event)">
                </mat-form-field>
            </div>
            <div class="col-md-3 text-right ali botoes-hj-flechas" style="height:30px;margin-top: 30px ">
                <div class="btn-group">

                    <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
                        <mat-button-toggle value="atual" (click)="decrement()"
                            style="background-color:#007bff;color:white;margin-top: -10px">
                            <span class=" fa fa-chevron-left"> </span>
                        </mat-button-toggle>
                        <mat-button-toggle value="proximo" (click)="increment()" [disabled]="nextBtnDisabled"
                            style="border-left: none;background-color:#007bff;color:white;margin-top: -10px ">
                            <span class="fa fa-chevron-right"> </span>
                        </mat-button-toggle>

                        <mat-button-toggle class="ml-1 b-c" value="atual" (click)="today()"
                            style="background-color:#007bff;color:white;margin-top: -10px ">
                            {{ 'TELAAGENDA.HOJE' | translate }}
                        </mat-button-toggle>
                    </mat-button-toggle-group>

                </div>
            </div>
            <div class="col-md-3 agenda-esp">
                <div class="btn-group" *ngIf="view == 'day'">
                    <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
                        <mat-button-toggle class="b-c" value="atual" (click)="agendaesperaModal()"
                            style="background-color:#007bff;color:white;margin-top: -10px ">
                            {{'TELAAGENDA.AGENDADEESPERA' | translate}}
                        </mat-button-toggle>
                    </mat-button-toggle-group>
                </div>
            </div>
            <div class="col-md-6 msn-dia">

                <div class="btn-group" *ngIf="view == 'day'">
                    <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
                        <mat-button-toggle class="b-c" value="atual" (click)="MensagemModal()"
                            style="background-color:#007bff;color:white;margin-top: -10px ">
                            {{'TELAAGENDA.MENSAGEMDODIA' | translate}}
                        </mat-button-toggle>
                    </mat-button-toggle-group>
                </div>

                <div class="btn-group" *ngIf="view == 'day'">
                    <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
                        <mat-button-toggle class="b-c" value="atual" (click)="CriarNovoAgendamento()"
                            style="background-color:#007bff;color:white;margin-top: -10px ">
                            Criar encaixe
                        </mat-button-toggle>
                    </mat-button-toggle-group>
                </div>

            </div>
            <div class="col-md-3 coluna p-b-10">
                <table
                    style="margin-bottom: 22px; float: right;width: 100%; width: 90%; position: absolute; z-index: 2; background:#fff; "
                    class="col-table tabela">
                    <thead>
                        <tr style="border-bottom: 1px solid #ddd;">
                            <td class="text-center bold value-color" style="padding: 8px!important; cursor: pointer;"
                                (click)="legenda = !legenda">
                                {{ 'TELAAGENDA.LEGENDA' | translate }}
                                <mat-icon style="float: left;" *ngIf="legenda">expand_less</mat-icon>
                                <mat-icon style="float: left;" *ngIf="!legenda">expand_more</mat-icon>
                            </td>
                        </tr>
                    </thead>
                    <tbody *ngIf="legenda" style="pointer-events: none;">
                        <tr>
                            <td class
                                style=" font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">

                                <i class="fa fa-fw fa-times"></i> {{
                                'TELAAGENDA.CANCELARAGENDAMENTO' | translate }}
                            </td>
                        </tr>
                    </tbody>
                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class style="
                font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                <i class="fa fa-fw fa-pencil "></i> {{
                                'TELAAGENDA.EDITARAGEND' | translate }}
                            </td>
                        </tr>
                    </tbody>
                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class
                                style=" font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                <i class="fa fa-fw fa-eye "> </i> {{
                                'TELAAGENDA.MOTIVOCANCEL' | translate }}
                            </td>
                        </tr>
                    </tbody>

                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class
                                style="background-color: rgb(34, 139, 34);  border-color: rgb(0, 128, 0); color: white !important; font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                {{ 'TELAAGENDA.PRIMEIRACONSULTA' | translate }}
                            </td>
                        </tr>
                    </tbody>
                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class
                                style="
                background-color: rgb(204, 51, 51); border-color: rgb(255, 0, 0);  color: white !important;font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                {{ 'TELAAGENDA.CONSULTACANCELADA' | translate }}
                            </td>
                        </tr>
                    </tbody>
                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class
                                style="background-color: #d1e8ff; border-color: #1e90ff; color: #1e90ff; font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                {{ 'TELAAGENDA.CONSULTAPADRAO' | translate }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <br />
    </div>
</div>

<div class=" agenda-component">

</div>

<ng-template #marcarHorario>

    <div style="display: flex; justify-content: space-between; max-height: 5%;">
        <h2 mat-dialog-title style="font-weight: 700; color: #279EFF; margin-bottom: -2px;">{{
            'TELAAGENDA.MARCARHORARIO' | translate }}
        </h2>
        <button mat-icon-button (click)="fechaModal_MarcarHorario()">
            <mat-icon>close</mat-icon> </button>
    </div>

    <div class="row">

        <div class="col-sm-12 height-modal marcar-horario-modal"
            style="padding-left: 30px;  overflow: auto;  padding-right: 30px; padding-top: 30px;">
            <div class="row ">
                <div class="col-md-10 col-sm-10 col-xs-9 linha-paciente" style="padding: unset; height: 50px;"
                    [ngClass]="this.flgExcluiAgenda ? 'DesabilitaCampo': ''">
                    <ng-select [items]="DadosPacientes" placeholder="{{ 'TELAAGENDA.PACIENTE' | translate }}"
                        bindLabel="nome" aria-required="true" bindValue="idCliente" name="Paciente" [selectOnTab]="true"
                        [(ngModel)]="IdPaciente" aria-required="true"
                        (change)="ValidaPaciAgenda(IdPaciente); RetornoConvenio(IdPaciente,idConvenio) "
                        class="col-12 input-spacing" style="height: 31px; font-size: 12px; padding-right:0px;"
                        (blur)="validaPaciente()">
                    </ng-select>

                    <mat-error style="font-size: 10px; padding-left: 15px;margin-top: 5px; width: 180px;"
                        *ngIf="pacienteValido==false">
                        {{getErrorMessagepaci() | translate }}
                    </mat-error>
                </div>

                <button mat-icon-button title="{{ 'TELAAGENDA.INFORMACOESPACIENTE' | translate }}"
                    [disabled]="IdPaciente == 0 || IdPaciente == undefined"
                    (click)="informacao('Paciente',IdPaciente )">
                    <mat-icon aria-label="Editar linha selecionada" style="color: #1265b9;" class>info</mat-icon>
                </button>

                <button mat-icon-button title="{{ 'TELAAGENDA.ADICIONARPACIENTE' | translate }}"
                    (click)="AddnovoPaciente()">
                    <mat-icon aria-label="Editar linha selecionada" style="color: #1265b9;" class>control_point
                    </mat-icon>
                </button>

                <mat-form-field class="col-md-6 col-sm-6 col-12 linha-paccpf">
                    <input matInput placeholder="{{ 'TELAAGENDA.BUSQUEOPACIENTEPELOCPF' | translate }}" name="CPf"
                        (change)="CarregaPacientes()" mask="000.000.000-00" [(ngModel)]="CPF" maxlength="14">
                </mat-form-field>

                <div class="col-md-6 col-sm-6 col-xs-6 " *ngIf="!loginComMedico">
                    <ng-select [items]="ListaMedicos" style="font-size: 12px;" (change)="CarregaespecialidadeMedico()"
                        placeholder="{{ 'TELAAGENDA.MEDICOS' | translate }}" bindLabel="nomeMedico" bindValue="idMedico"
                        name="medicos" [selectOnTab]="true" [(ngModel)]="IdMedico"
                        aria-required="true">
                    </ng-select>
                    <mat-error style="font-size: 10px; margin-top: -15px;" *ngIf="medi.invalid">
                        {{getErrorMessagemedi() | translate }}
                    </mat-error>
                </div>

                <div class="col-md-6 col-sm-6 col-12">
                    <ng-select [items]="DadosEspecialidadeMedico" style="font-size: 12px;" (change)="CarregaMedicos()"
                        placeholder="{{ 'TELAAGENDA.ESPECIALIDADE' | translate }}" bindLabel="desEspecialidade"
                        bindValue="idEspecialidade" name="especialidade" [selectOnTab]="true"
                        notFoundText="Selecione o Médico" [(ngModel)]="Especialidade">
                    </ng-select>
                </div>

                <!-- Campo de Data  -->
                <div class="col-md-6 col-sm-6 col-12">
                    <mat-form-field appearance="outline">
                        <input matInput placeholder="{{ 'TELAAGENDA.DATAEHORA' | translate }}" name="Data e Hora"
                            [(ngModel)]="dtExibicao" disabled style="color: black;">
                    </mat-form-field>

                    <button mat-mini-fab (click)="escolherHorarioConsulta()" color="primary"
                        matTooltip="Escolher horário" style="margin-left: 3px;">
                        <mat-icon>schedule</mat-icon>
                    </button>
                </div>

                <div class="col-md-6 col-sm-6 col-xs-12" style="padding: unset" *ngIf="Edicao">
                    <mat-form-field class="col-md-12 col-sm-12">
                        <input matInput placeholder="{{ 'TELACONSULTAS.NOVADATA' | translate }}" name="Data Inicio"
                            id="Dtanova" (keypress)="mascaraData($event)" [(ngModel)]="Dados.dtanova"
                            (blur)="ValidaDtaEd($any($event.target).value)" maxlength="10">

                    </mat-form-field>
                    <span class="aviso-span text-center" *ngIf="Dtanasc == true"
                        style="font-size: 65%;color: #f44336;font-weight: 600;display:flex; margin-top: -11px;">{{
                        'TELACADASTROMEDICO.ERRODATA' | translate }}</span>
                    <span class="aviso-span text-center" *ngIf="DtanascVasil == true && Dtanasc != true"
                        style="font-size: 65%;color: #f44336;font-weight: 600;display: flex; margin-top: -11px;">{{
                        'TELACADASTROMEDICO.ERROCAMPO' | translate }}</span>
                </div>

                <div class="col-md-6 col-sm-6 col-xs-12" *ngIf="Edicao" style="height: 50px!important; padding: unset">
                    <mat-form-field class="col-md-12">
                        <input matInput placeholder="{{ 'TELAAGENDA.NOVOHORARIO' | translate }}" type="time"
                            id="horaEdcao" (blur)="ValidaTimerInter($any($event.target),$any($event.target).value)"
                            name="NovoHorario" [(ngModel)]="selectedTime">
                    </mat-form-field>
                    <span class="aviso-span text-center" *ngIf="hraEdit == true"
                        style="font-size: 65%;color: #f44336;font-weight: 600;display:flex; margin-top: -11px;">{{
                        'TELACADASTROMEDICO.ERRODATA' | translate }}</span>
                    <span class="aviso-span text-center" *ngIf="hraEditVasil == true && hraEdit != true"
                        style="font-size: 65%;color: #f44336;font-weight: 600;display: flex; margin-top: -11px;">{{
                        'TELACADASTROMEDICO.ERROCAMPO' | translate }}</span>
                </div>

                <div class="col-md-6 col-sm-6 col-12">
                    <ng-select [items]="dadosTipoAgendamento" style="font-size: 12px;"
                        placeholder="{{ 'TELAAGENDA.TIPOAGENDAMENTO' | translate }}" bindLabel="desTipoAgendamento"
                        bindValue="idTipoAgendamento" name="tipoAgendamento" [selectOnTab]="true"
                        [(ngModel)]="tipoagendamento" notFoundText="Nenhum tipo de Agendamento Cadastrado">
                    </ng-select>
                </div>

                <mat-form-field class="col-md-6 col-sm-6 col-12">
                    <input matInput placeholder="{{ 'TELAAGENDA.VALOR' | translate }}" name="valor" id="valorConsulta"
                        [required]='flgExigePagamento' [formControl]='valor'
                        (keypress)="mascaraValor($any($event.target).value)"
                        (change)="mascaraValor($any($event.target).value)"
                        (keyup)="mascaraValor($any($event.target).value)" [(ngModel)]="valorConsulta">
                    <mat-error *ngIf="flgExigePagamento">
                        {{getErrorMessagevalorConsulta() | translate }}
                    </mat-error>
                </mat-form-field>

                <!-- <div class="col-md-6 col-sm-6 col-xs-12">
                    <ng-select [items]="dadosConvenio" style="font-size: 12px;"
                        placeholder="{{ 'TELAAGENDA.CONVENIO' | translate }}" bindLabel="desConvenio"
                        bindValue="idConvenio" name="especialidade" [selectOnTab]="true" [(ngModel)]="idConvenio"
                        notFoundText="Cadastre convenios." (change)="PreencheValorPagamento()"
                        (change)="RetornoConvenio(IdPaciente,idConvenio)">
                    </ng-select>
                </div>

                <mat-form-field class="col-md-6 col-sm-6 col-12" *ngIf="flgConvenioParticular==false">
                    <input matInput placeholder="{{ 'TELAAGENDA.CODIGOCONVENIO' | translate }}" name="codConvenio"
                        [formControl]='codConv' [(ngModel)]="codConvenio" (change)="verificaConvenio()">
                </mat-form-field>

                <div class="col-md-6 col-sm-6 col-xs-12">
                    <ng-select [items]="listaFatura" style="font-size: 12px;" placeholder="Fatura" bindLabel="descricao"
                        bindValue="idFatura" name="especialidade" [selectOnTab]="true" [(ngModel)]="idFatura"
                        notFoundText="Cadastre faturas.">
                    </ng-select>
                </div> -->

                <div class="col-md-6 col-sm-12 col-12">
                    <mat-checkbox [(ngModel)]="FlgRetorno"><small>{{
                            'TELAAGENDA.RETORNO' | translate }}</small>
                    </mat-checkbox>
                </div>

                <mat-form-field class="col-md-12 col-sm-12 col-xs-12 " appearance="outline" style="margin-top: 20px;"
                    hintLabel="Máx. 150" floatLabel="always">
                    <mat-label>{{ 'TELAAGENDA.OBSERVACAO' | translate
                        }}</mat-label>
                    <textarea matInput #input maxlength="150" name="Observação" [(ngModel)]="DesAgendamento"
                        style="max-height: 105px; min-height: 30px;"></textarea>
                    <mat-hint align="end">{{ 0 + DesAgendamento.length
                        }}/150</mat-hint>
                </mat-form-field>
            </div>
            <div class="row-button p-t-20 cartao-pagam" *ngIf="!flgProntuario && flgHabilitaPagamento"
                style="margin: 0 auto; text-align: center;">
                <span>Exige Pagamento Cartão</span>
                <mat-slide-toggle class="botao" style="font-size: 12px; margin-left: 10px;"
                    [(ngModel)]='flgExigePagamento'>
                </mat-slide-toggle>
            </div>

            <div class="row-button p-t-20" style="margin-bottom: 10px; text-align: center;">
                <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
                    aria-selected="true" [(ngModel)]="flgProntuario" *ngIf="!flgSomenteProntuario">
                    <!-- *ngIf="!usuario.flgProntuario"  -->

                    <mat-radio-button class="example-radio-button" [value]="false">
                        Telemedicina
                    </mat-radio-button>

                    <mat-radio-button class="example-radio-button" [value]="true">
                        Presencial
                    </mat-radio-button>
                </mat-radio-group>

                <div class="btns-ns-horario">
                    <button mat-flat-button (click)="fechaModal_MarcarHorario()" (click)="LimpaMensErroPaci()"
                        class="input-align btn btn-danger">
                        {{ 'TELACONSULTAS.NAO' | translate }}
                    </button>
                    <button type="submit" mat-flat-button class="input-align btn btn-primary"
                        (click)="SalvarConsulta()">
                        {{ 'TELACONSULTAS.SIM' | translate }}
                    </button>
                </div>
            </div>
        </div>
    </div>

</ng-template>

<ngx-smart-modal #cancelarHorario identifier="cancelarHorario" customClass="nsm-centered medium-modal emailmodal">
    <div class>

        <div class="modal-info text-center">
            <b class>
                {{ 'TELAAGENDA.DESEJACANCELARESSEHORARIO' | translate }}
            </b>
        </div>

        <hr class="sep-1" />

        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 " style="margin-top: 20px;" hintLabel="Máx. 200"
            floatLabel="always">
            <mat-label>{{ 'TELAAGENDA.MOTIVODOCANCELAMENTO' | translate
                }}</mat-label>
            <textarea matInput #input maxlength="200" required name="Motivo Cancelamento"
                [(ngModel)]="Motivocancelameto" (change)="MotivoCampo()"
                style="max-height: 200px; min-height: 48px;padding: 5px; border: solid 1px #8e8e8e;"></textarea>
            <mat-hint align="end">{{Motivocancelameto.length +
                0}}/200</mat-hint>
        </mat-form-field>
        <div class="danger-baloon" *ngIf="cancelamento == true">
            <label style="color: red;" class="text-right">{{
                'TELAAGENDA.MOTIVODESERPREENCHIDO' | translate }}</label>
        </div>

        <div class="row-button text-center p-t-20 p-b-20">
            <button mat-flat-button (click)="cancelarHorario.close()" class="input-align btn btn-danger">
                {{ 'TELAAGENDA.NAO' | translate }} </button>
            <button mat-flat-button class="input-align btn btn-success" (click)=CancelarConsulta()>
                {{ 'TELAAGENDA.SIM' | translate }} </button>
        </div>

        <div class="logo-medicina-modal">
         </div>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #excecao identifier="excecao" customClass="nsm-centered medium-modal emailmodal" [dismissable]="false">
    <div class>

        <div class="modal-info p-t-20 p-b-20 info-dia-nao">

            <b>{{ 'TELAAGENDA.DIANAOCADASTRADO' | translate }}</b><br><br>

            <b>{{ 'TELAAGENDA.AGENDARMESMOASSIM' | translate }}</b>
            <!-- Esse Dia da Semana não esta cadastado para esse Médico deseja agendar um horário mesmo assim? -->
        </div>

        <hr class="sep-1" />

        <div class="row-button text-center p-20 btns-dia-nao">
            <button mat-flat-button (click)="excecaoModal()" class="input-align btn btn-danger">
                {{ 'TELAAGENDA.NAO' | translate }} </button>
            <button mat-flat-button class="input-align btn btn-success" (click)=exececaoDia()>
                {{ 'TELAAGENDA.SIM' | translate }} </button>
        </div>

        <div class="logo-medicina-modal">
         </div>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #Inicializando [autostart]="!loginComMedico" identifier="Inicializando" [dismissable]="false"
    customClass="nsm-centered medium-modal modal mt-1 emailmodal emailmodalAgenda" class="modal-escolhamedico">
    <div class=" height-modal div-escolhamedico">

        <div class="modal-info custom-mobile-title">
            <b> {{ 'TELAAGENDA.ESCOLHAOMEDICO' | translate }} </b><br>
        </div>

        <hr class="sep-1" />
        <div class="modal-info">
            <!-- Paciente:  -->
            <div class="col-md-12 col-sm-12 col-xs-12 input-spacing custom-mobile" style="padding: 5px;">
                <ng-select [dropdownPosition]="'top'" style="font-size: 12px;" [items]="DadosEspecialidade"
                    placeholder="{{ 'TELAAGENDA.FILTRARESPECIALIDADE' | translate }}" bindLabel="desEspecialidade"
                    (change)="CarregaMedicos()" bindValue="idEspecialidade" name="especialidade" [selectOnTab]="true"
                    [(ngModel)]="Especialidade">
                </ng-select>
            </div>

            <div class="col-md-12 col-sm-12 col-xs-12  input-spacing custom-mobile" style="padding: 5px;">
                <ng-select [dropdownPosition]="'top'" style="font-size: 12px;" [items]="ListaMedicos"
                    placeholder="{{ 'TELAAGENDA.MEDICOS' | translate }}" bindLabel="nomeMedico" bindValue="idMedico"
                    name="medicos" (change)="CarregaAgenda('dia')" [selectOnTab]="true" [(ngModel)]="IdMedico"
                    class="names-medicos">
                </ng-select>

            </div>
        </div>

        <div class="logo-medicina-modal">
         </div>

    </div>
</ngx-smart-modal>

<ng-template #ModalNovoPaciente style="padding: 0 !important;">
    <div class="col-md-12" style="width: 100% !important;">

        <div style="display: flex; justify-content: space-between; max-height: 5%;">
            <h2 mat-dialog-title style="font-weight: 700; color: #279EFF; margin-bottom: -2px;">
                Novo Paciente
            </h2>
            <button mat-icon-button (click)="fecharModalAddPaciente()">
                <mat-icon>close</mat-icon> </button>
        </div>

        <div class="modal-info" md-dialog-content>
            <!-- Paciente:  -->

            <hr class="sep-1" />

            <div class="col-md-12 col-sm-12 row mt-3" style=" margin-left: 0; margin-right: 0;">
                <!-- <mat-form-field class="col-md-6 col-sm-12 input-spacing campo-NovoCliente" appearance="legacy">
                    <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" name="CPF"
                        (change)="ValidarCpf($any($event.target).value)" mask="000.000.000-00" maxlength="14" required
                        [(ngModel)]="dadosNovoPaciente.cpf">

                        <span class="ml-1 text-center" *ngIf="campoCPFInvalido == true" style="font-size: 65%;color: #f44336;font-weight: 600;position: absolute;
                        left: 1px;top: 37px;">CPF inválido</span>
                        <span class="ml-1 text-center" *ngIf="campoCPFVazil == true && campoCPFInvalido != true"
                            style="font-size: 65%;color: #f44336;font-weight: 600;position: absolute;
                        left: 1px;top: 37px;">Esse campo precisa ser preenchido</span>
                        </mat-form-field> -->

                <div class="col-md-6 col-sm-12" style="padding: unset">
                    <mat-form-field class="col-md-12 col-sm-12 input-spacing" style="height: 31px;">
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.CPF' | translate }}" name="CPF"
                            (blur)='validarCpf($any($event.target).value)'
                            (keypress)="mascaraCpf('###.###.###-##', $event)" mask="000.000.000-00" maxlength="14"
                            required [(ngModel)]="dadosNovoPaciente.cpf">
                    </mat-form-field>

                    <span class="ml-1 text-center" *ngIf="campoCPFInvalido == true" style="
                        font-size: 65%;
                        color: #f44336;
                        font-weight: 500;
                        position: absolute;
                        left: 13px;
                        top: 32px;">CPF inválido</span>
                    <span class="ml-1 text-center" *ngIf="campoCPFVazil == true && campoCPFInvalido != true" style="
                        font-size: 65%;
                        color: #f44336;
                        font-weight: 500;
                        position: absolute;
                        left: 13px;
                        top: 32px;">Esse campo precisa ser preenchido</span>
                </div>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing campo-NovoCliente Nome-Pac">
                    <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" name="Nome" required
                        (keyup)="mascaraText($any($event.target).value)"
                        (change)="mascaraText($any($event.target).value)" [(ngModel)]="dadosNovoPaciente.nome"
                        [formControl]='Nome'>
                    <mat-error *ngIf="Nome.invalid">{{getErrorMessageNome() |
                        translate }}</mat-error>
                </mat-form-field>

                <div class="col-md-6 col-sm-12 campo-NovoCliente " style="padding: unset" required>
                    <mat-form-field class="col-12  input-spacing" style="height: 31px;">
                        <input matInput placeholder="{{ 'TELAAGENDA.DATADENASCIMENTO' | translate }}"
                            name="Data de Nascimento" id="DtaNasc" [(ngModel)]="dadosNovoPaciente.dtaNascimento"
                            maxlength="10" (keyup)="mascaraData($event)" (blur)="ValidaDta($any($event.target).value)">
                    </mat-form-field>
                    <span class="aviso-span text-center required" *ngIf="Dtanasc == true"
                        style="font-size: 65%;color: #f44336;display:flex;">{{
                        'TELACADASTROMEDICO.ERRODATA' | translate
                        }}</span>
                </div>

                <!-- <mat-form-field appearance="legacy" class="col-md-6 col-sm-12 input-spacing campo-NovoCliente">
                    <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email" name="Email" required
                        [(ngModel)]="dadosNovoPaciente.email" [formControl]='email'>
                    <mat-error *ngIf="email.invalid">{{getErrorMessageEmail() | translate }}</mat-error>
                    </mat-form-field> -->
                <div class="col-md-6 col-sm-12" style="padding: unset; height: 50px;">
                    <mat-form-field class="col-12  input-spacing campo-NovoCliente Email-Pac" style="height: 31px;">
                        <input matInput placeholder="{{ 'TELACADASTROCLINICA.EMAIL' | translate }}" type="email"
                            name="Email" (change)="ValidarEmail($any($event.target).value)" [formControl]='email'
                            required [(ngModel)]="dadosNovoPaciente.email">
                    </mat-form-field>
                    <span class="ml-1 required" style="padding-left: 10px;" *ngIf="campoEmailInvalido == true">Email
                        inválido</span>
                    <span class="ml-1 required" style="padding-left: 10px;"
                        *ngIf="campoEmailVazil == true && campoEmailInvalido != true">Esse
                        campo precisa ser
                        preenchido</span>
                </div>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing campo-NovoCliente Cel-Pac">
                    <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" name="Telefone Movel"
                        (keyup)="mascaraTelefone($event)" (change)='ValidarTelMovel($any($event.target).value)' required
                        maxlength="15" [(ngModel)]="dadosNovoPaciente.telefoneMovel" [formControl]='tel'>
                    <mat-error *ngIf="tel.invalid">{{getErrorMessagetel() |
                        translate }}</mat-error>
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 col-xs-12 input-spacing campo-NovoCliente">
                    <input matInput placeholder="{{ 'TELAAGENDA.PROCEDENCIA' | translate }}" name="Procedência"
                        [(ngModel)]="dadosNovoPaciente.procedencia">
                </mat-form-field>

                <div class="col-md-12 col-sm-12 col-xs-12" style="padding: unset; height: 50px;">
                    <mat-form-field class="col-12 input-spacing" style="height: 31px;">
                        <mat-select placeholder="{{ 'TELAAGENDA.CLINICASCORRESPONDENTE' | translate }}" multiple
                            name="clinicas" (blur)="ValidaClinicas($any($event.target).value)">
                            <mat-option *ngFor="let item of DadosClinicas;let i = index" [value]="item">
                                {{item.desClinica | truncate : 40 :
                                "…"}}</mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span class="ml-1 text-center" *ngIf="clinicaVal == true" class="required">{{
                        'TELAAGENDA.UMACLINICAPRECISASERSELECIONADA' | translate
                        }}</span>
                </div>

                <div class=" col-md-6 col-sm-12 col-xs-12 col-lg-6 button_custom mt-4" align="left">

                    <mat-checkbox style="margin-right: 10%;" [(ngModel)]="dadosNovoPaciente.flgBoasVindas">
                        {{ 'TELAAGENDA.ENVIARBOASVINDAS' | translate }}
                    </mat-checkbox>

                </div>
                <div class=" col-md-12 button_custom mt-4 botao"
                    style="margin: 0 auto; justify-content: center; text-align: center;">
                    <button class="btn-primary " mat-raised-button style="color:white;"
                        (click)="LimparCamposNovoPaciente()" style="margin-right: 10px;">
                        <mat-icon>clear</mat-icon><span class="custom-span title-btns">{{
                            'TELAAGENDA.LIMPAR' |
                            translate }}</span>
                    </button>

                    <button class="btn-primary " mat-raised-button style="color:white; margin-right: 10px"
                        (click)="SubmitNovoPaciente()">
                        <mat-icon>save</mat-icon> <span class="custom-span title-btns">{{
                            'TELAAGENDA.SALVAR' |
                            translate }}</span>
                    </button>
                </div>

            </div>
        </div>

        <div class="logo-medicina-modal">
         </div>

    </div>
</ng-template>

<ngx-smart-modal #InforUsuario identifier="InforUsuario" [force]="true"
    customClass="nsm-centered medium-modal modal form-modal-Cliente" style="padding: 0 !important; overflow: scroll;">
    <div class="modal-info-pac" style="overflow: auto;">
        <div class="col-md-12 header-infopac">

            <div class="col-12 " style=" align-self: flex-end;">

                <b class="title-infopac-modal"> Informações do
                    {{DadosInformUsuario.tipoUsuario}}</b>

            </div>
            <!--<div class="col-6" style="align-self: center;">
            <mat-icon style="color: white; font-size: 88px; margin-left: 30%;">
                perm_identity</mat-icon>
        </div>-->
        </div>
        <hr class="sep-1" />

        <div class="modal-info">

            <div class="col-md-12 col-sm-12 row mt-3" style=" margin-left: 0; margin-right: 0;">
                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled name="Nome"
                        [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" disabled name="CPF"
                        mask="000.000.000-00" [(ngModel)]="DadosInformUsuario.cpf" style="color: black;" maxlength="14">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email" disabled
                        name="Email" [(ngModel)]="DadosInformUsuario.email" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" disabled name="Celular"
                        mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.celular" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.TELEFONE' | translate }}" disabled name="Telefone"
                        mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.tel" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.TELEFONECOM' | translate }}" disabled
                        name="TelComerciar" mask="(00) 00000-0000" style="
          color: black;" [(ngModel)]="DadosInformUsuario.telComer">
                </mat-form-field>

                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">

                    <button class="btn-primary " mat-raised-button style="color:white;" (click)="InforUsuario.close()"
                        style="margin-right: 2%;">
                        <mat-icon>clear</mat-icon> {{ 'TELAAGENDA.SAIR' |
                        translate }}
                    </button>

                </div>

            </div>
        </div>
    </div>

    <div class="logo-medicina-modal">
        <img class="logo-modal-footer"   >
    </div>

</ngx-smart-modal>

<ngx-smart-modal #InforCancelamento identifier="InforCancelamento" customClass="nsm-centered medium-modal emailmodal">
    <div class="modal-cancelamento">

        <div class="modal-info title-cancelamento">
            <b> {{ 'TELAAGENDA.DADOSCANCELAMENTO' | translate }}</b><br>
        </div>

        <hr class="sep-1" />

        <div class="modal-info">
            <div class="col-md-12 col-sm-12 row mt-3" style=" margin-left: 0; margin-right: 0;">
                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.CANCELAMENTOFEITO' | translate }}" style="color: black"
                        name="Nome" disabled [(ngModel)]="DadosInformCancelament.nome">
                </mat-form-field>
                <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.DATACANCELAMENTO' | translate }}" style="color: black"
                        type="email" name="Email" disabled [(ngModel)]="DadosInformCancelament.Dta">
                </mat-form-field>
                <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                    <input matInput placeholder="{{ 'TELAAGENDA.MOTIVOCANCELAMENTO' | translate }}" style="color: black"
                        name="Cacelamento" disabled [(ngModel)]="DadosInformCancelament.Motivo">
                </mat-form-field>
                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">
                    <button class="btn-primary " mat-raised-button style="color:white;"
                        (click)="InforCancelamento.close()" style="margin-right: 2%;">
                        <mat-icon>clear</mat-icon> {{ 'TELAAGENDA.SAIR' |
                        translate }}
                    </button>
                </div>
            </div>
        </div>

        <div class="logo-medicina-modal">
         </div>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #excluirMensagemDia identifier="excluirMensagemDia" customClass="nsm-centered medium-modal emailmodal">
    <div class>

        <div class="modal-info">
            <b>Você tem certeza que deseja excluir esta Mensagem ?</b>
        </div>

        <hr class="sep-1" />

        <div class="row-button text-center p-20">
            <button mat-flat-button (click)="excluirMensagemDia.close()" class="input-align btn btn-danger">
                {{ 'TELAAGENDACONTATO.NAO' | translate }} </button>
            <button mat-flat-button (click)="DeletarMensagemDia()" class="input-align btn btn-success">
                {{ 'TELAAGENDACONTATO.SIM' | translate }} </button>
        </div>

        <div class="img-logo-medicina">
         </div>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #infoConsulta identifier="infoConsulta" customClass="nsm-centered medium-modal emailmodal">
    <div class="card-modal-rapido">

        <div class="header-modal-rapido" style="justify-content: center; text-align: center;">
            <div class="modal-info title-rapido">
                <b style="margin: 0 auto;">Acesso Rápido</b><br>
            </div>
            <!--<img class="img-relogio" src="assets/build/img/time.png">-->
        </div>

        <hr class="sep-1" />

        <div class="modal-info">
            <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">

                <div class="link-acesso">

                    <b> Link Acesso:</b><br>

                    <b class="linkando col-10"
                        style="text-align: center; justify-content: center; margin: 0 auto; max-width: 280px; word-wrap: break-word">{{modalInfo.guid}}</b><br><br>

                    <b> Cod.Acesso: {{modalInfo.CodAcesso}}</b>

                </div>

                <div class="col-md-12 div-botoes-rapido">

                    <button class=" button-interactive btn btn-primary "
                        style="margin-right: 2%; margin-top: 2px; padding: 9px;" title="Copiar Link e Código"
                        (click)="copyMessage('Link Acesso: ' +modalInfo.guid  +'\n' +  'Cod.Acesso: ' + modalInfo.CodAcesso)">
                        <i class="fa fa-files-o" style="
                    font-size: 25px; padding: 3px;"> </i>
                        <!-- <mat-icon> file_copy</mat-icon> -->
                    </button>

                    <button class="button-interactive btn btn-primary "
                        style="margin-right: 2%; margin-top: 2px; padding: 9px;" title="Enviar por Email"
                        (click)="EnviarEmailAcesso(modalInfo.idagenda)">
                        <i class="fa fa-envelope fa-fw fa-1x" style="font-size: 25px; padding: 3px;"> </i>
                        <!-- <mat-icon> email</mat-icon> -->
                        <!-- Enviar por Email  -->
                    </button>

                    <button class="button-interactive btn btn-primary " style="margin-top: 2px; padding: 9px;"
                        title="Enviar por Whatsapp " (click)="EnviarWhatsAcesso(modalInfo.idagenda)">
                        <i class="fa fa-whatsapp fa-fw fa-1x" style="
                 font-size: 25px; padding: 3px;"> </i>
                    </button>

                </div>
            </div>
        </div>

        <div class="img-logo-medicina">
         </div>
    </div>

</ngx-smart-modal>

<ngx-smart-modal #UsuarioExistente identifier="UsuarioExistente" customClass="nsm-centered medium-modal"
    [closable]="false" [dismissable]="false" [escapable]="false">
    <div class="modal-header p-t-20 p-b-20">
        <div class="row">
            <div class=" col-12">
                <h1 class="little-title fw-700" style=" padding-left: 3vh; text-align: center">{{mensagemPaciente}}</h1>
            </div>

        </div>
    </div>

    <mat-divider></mat-divider>
    <div class="row-button text-center p-t-20">
        <button mat-flat-button (click)="AceitarUsuarioExistente()" class="input-align btn btn-success">Aceitar</button>
        <button mat-flat-button (click)="NaoAceitarUsuarioExistente()"
            class="input-align btn btn-danger">Cancelar</button>

    </div>
</ngx-smart-modal>

<ngx-smart-modal #consultaAgenda class="modal-consulta-play" identifier="consultaAgenda"
    customClass="nsm-centered medium-modal emailmodal" style="border-radius: 10px;">
    <div class="card-consulta-modal">

        <div class="col-md-12  mb-4 header-consulta-modal">
            <div class="modal-info margin-titulo">
                <b class="title-consulta-modal"> {{ 'TELACOLUNADIREITA.CONSULTA'
                    | translate }} </b><br>
            </div>
            <!--<mat-icon style="color: white; font-size: 88px;  margin-left: 25%;"> calendar_today</mat-icon>-->
        </div>

        <hr class="sep-1" />
        <div class="container pb-2" style="max-height: 50vh !important; overflow-y: auto;">
            <div *ngFor="let item of usuarioConsultaAgenda" style="margin-top: 1rem !important;">
                <div class="row p-2" style="margin-bottom: 1rem !important;">
                    <div class="col-lg-9 infos-consult-modal">
                        <small>
                            <p style="margin-bottom: 0 !important">{{
                                'TELACOLUNADIREITA.CONSULTAAGENDADA' | translate
                                }}
                                {{item.dtaconsulta | date: 'dd/MM/yyyy HH:mm'
                                }}</p>
                        </small>
                        <small>
                            <p style="margin-bottom: 0 !important">
                                Tipo Consulta: {{item.flgProntuario
                                ==true?'Presencial' : 'Telemedicina'}}</p>
                        </small>
                        <small>
                            <p style="margin-bottom: 0 !important">Paciente:
                                {{item.nome}}</p>
                        </small>
                        <small>
                            <p style="margin-bottom: 0 !important">Clinica:
                                {{item.Clinica}}</p>
                        </small>
                    </div>

                    <div class="col-lg-3 botoes-acao-consulta"
                        style="margin-top: auto !important; margin-bottom: auto !important;">
                        <!--<button tmDarkenOnHover class="btn-primary buttons-mobilet" mat-mini-fab style="width: 30px;
                    touch-action: none;
                    user-select: none;
                    -webkit-user-drag: none;
                    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                    height: 30px;
                    margin-right: 4%;" matTooltip="{{'TELACONSULTAS.CANCELAR' | translate}}"
                        (click)="CancelaValue(item.idconsulta)">
                        <mat-icon style="
                        width: 23px !important;
                        margin-top: -7px;
                        font-size: 19px;" aria-label="Cancelar Consulta" class="svg-icon">delete</mat-icon>
                    </button>-->
                        <button tmDarkenOnHover class="btn-primary buttons-mobilet" mat-mini-fab style="width: 30px;
                    background-color: green !important;
                    touch-action: none;
                    user-select: none;
                    -webkit-user-drag: none;
                    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                    height: 30px;
                    margin-right: 4%;" matTooltip="{{'TELACONSULTAS.INICIARCONSULTA' | translate}}"
                            (click)="IrConsulta(item.idconsulta , item.flgSomenteProntuario)">
                            <mat-icon style=" width: 22px !important; margin-top: -9px; margin-left: -3px;"
                                aria-label="Iniciar consulta  choicer" class>
                                play_arrow</mat-icon>
                        </button>
                    </div>
                </div>
            </div>
        </div>
          
    </div>
</ngx-smart-modal>

<ng-template #excluirAgendamentoEspera customClass="nsm-centered medium-modal emailmodal">
    <div style="display: flex; justify-content: space-between; max-height: 5%;">
        <h2 mat-dialog-title style="font-weight: 700; color: #279EFF; margin-bottom: -2px;">Excluir
            agendamento
        </h2>
        <button mat-icon-button (click)="fechaModal_Excluir()">
            <mat-icon>close</mat-icon> </button>
    </div>

    <div class>

        <div class="modal-info div-title-excluir">
            <b>Você tem certeza que deseja excluir este Agendamento em Espera
                ?</b>
        </div>

        <hr class="sep-1" />

        <div class="row-button text-center p-20 btns-excluir-espera">
            <button mat-flat-button (click)="fechaModal_Excluir()" class="input-align btn btn-danger">
                {{ 'TELAAGENDACONTATO.NAO' | translate }} </button>
            <button mat-flat-button (click)="InativarAgendaEspera()" class="input-align btn btn-success">
                {{ 'TELAAGENDACONTATO.SIM' | translate }} </button>
        </div>

        <div class="img-logo-medicina">
            <img class="logo-final-modal" src="assets/build/img/logo medicina para voce.pn"g>
        </div>

    </div>
</ng-template>

<ng-template #agendaEspera>
    
</ng-template>