import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnInit, Output, Inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-modal-upload-fotos',
  templateUrl: './modal-upload-fotos.component.html',
  styleUrls: ['./modal-upload-fotos.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIcon,
  ]
})
export class ModalUploadFotosComponent implements OnInit {
  @Output() imagemCortada = new EventEmitter<string>();
  imageChangedEvent: any = '';
  imageBase64: string = ''; // Renomeado de croppedImage para imageBase64
  showImage = false;
  fileName: string = '';

  constructor(
    private dialogRef: MatDialogRef<ModalUploadFotosComponent>,
    @Inject(MAT_DIALOG_DATA) public dados: string | null = null
  ) { }

  ngOnInit(): void {
    if (this.dados) {
      this.imageBase64 = this.dados;
      this.showImage = true;
    }
  }

  onFileChange(event: any): void {
    if (event.target.files && event.target.files.length > 0) {
      this.fileName = event.target.files[0].name;

      // Ler o arquivo como base64
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imageBase64 = e.target.result;
        this.imageLoaded();
      };
      reader.readAsDataURL(file);
    }
  }

  imageLoaded(): void {
    this.showImage = true;
  }

  trocarImagem(): void {
    this.imageChangedEvent = '';
    this.imageBase64 = '';
    this.showImage = false;
    this.fileName = '';
  }

  confirmImage(): void {
    this.imagemCortada.emit(this.imageBase64);
    this.dialogRef.close(this.imageBase64);
  }

  FecharModal(): void {
    this.dialogRef.close(null);
  }

  loadImageFailed() {
    console.error('Falha ao carregar a imagem.');
  }
}