export class formulariosModel {
    idFormulario?: number;
    idTipoOrientacao?: number;
    idUsuarioGerador?: number;
    nomeTipoOrientacao?: string;
    nomeFormulario?: string;
    dtaCadastro?: Date;
    perguntasFormulario?: perguntas[];
    perguntasExcluidas?: perguntas[];
}

export class perguntas {
    idPergunta? : number;
    posicaoPergunta?: number;
    pergunta?: string;
    idTipoResposta?: number;
    tipoResposta?: string;
}

export class analiseFormularioModel{
    flgRespondido: boolean = false;
    dtaCadastro?: Date;
    idFormulario?: number;
    nomeFormulario?: string; 
    idAnalise?: number; 
}

export class ids_formularioRespostas{
    idFormulario?: number;
    idAnalise?: number;
}

export class listaPerguntasRespostas
{
    pergunta? : string;
    resposta? : string;
    tipoResposta? : string;
    idTipoResposta?: number;
    flgRespondido?: boolean;
}

export class listaRespostas{
    idFormulario? : number;
    idAnalise?    : number;

    idPerguntaFormulario?: number;

    idUsuarioGerador?: number;

    pergunta?:string;
    resposta?:string;

    idTipoResposta? : number;
    tipoResposta? : string;

    flgRespondido?: boolean;
}

export class FiltraExamesFormularios{
    idsExames?: number[];
    idsForms? : number[];
}