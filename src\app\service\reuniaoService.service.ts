import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class ReuniaoService {

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) { }

    public GetReuniaoGuid(guid:any, codAcesso:any): Observable<any> {
        this.spinner.show();
        var reqHeader = new HttpHeaders({ 'No-Auth': 'True' });
        return this.http.get(environment.apiEndpoint + '/Reuniao/GetReuniaoGuid/' + guid + '/' + codAcesso, { headers: reqHeader });
    }
    
    
    public CarregaReunião(idUsuario:any, idClinica:any , inicio:any, fim:any): Observable<any> {
        this.spinner.show();
        
        let params = new HttpParams();
        params = params.append('idUsuario', idUsuario);
        params = params.append('idClinica', idClinica);
        params = params.append('inicio', inicio);
        params = params.append('fim', fim);

        return this.http.get(environment.apiEndpoint + '/Reuniao/CarregaReunião', { params });
    }


}