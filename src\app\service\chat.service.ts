import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { Injectable } from '@angular/core';
import { MatDialog as MatDialog } from '@angular/material/dialog';
import { ChatComponent } from '../chat/chat.component';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { FiltroCarregaMensagem, MensagemModel, RetListaMensagem, RetUsuarioChat } from '../model/chat';
import { RetornoPadraoInt } from '../model/RetornoPadraoApi';
@Injectable({
  providedIn: 'root'
})


export class ChatService {
  public isPageVisible: boolean = true;
  private audio: HTMLAudioElement;

  constructor(
    private matDialog: MatDialog,
    private http: HttpClient,
    private localStorage: LocalStorageService
  ) {
    this.initializeVisibilityListener();
    this.audio = new Audio('https://medicoblobs.blob.core.windows.net/audios/Notificacoes.mp3');
  }

  geraNotificacao() {
    if (this.localStorage.flgNotificacaoSonora && !this.localStorage.flgModalChatAberta) {
      this.DisparaNotificacaoSonora();
    }
    if (this.localStorage.flgNotificacaoWindows) {
      this.DisparaNotificacaoWindows("Você recebeu uma nova mensagem");
    }
  }
  private initializeVisibilityListener() {
    document.addEventListener('visibilitychange', () => {
      this.isPageVisible = !document.hidden;
    });
  }

  DisparaNotificacaoSonora() {
    this.audio.play().catch(error => {
      console.error("Erro ao tocar o som:", error);
    });
  }

  DisparaNotificacaoWindows(body: string) {
    let title = "Bonecare";
    this.initializeVisibilityListener();
    if (!this.isPageVisible) {
      if (!("Notification" in window)) {
        ;
        return;
      }

      if (Notification.permission === "granted") {
        new Notification(title, { body });
      } else if (Notification.permission !== "denied") {
        Notification.requestPermission().then(permission => {
          if (permission === "granted") {
            new Notification(title, { body });
          }
        });
      }
    }
  }

  AbrirModalChat() {
    this.localStorage.flgModalChatAberta = true;
    const dialogRef = this.matDialog.open(ChatComponent, {
      width: '85vmax',
      height: '90vh',
      maxWidth: '95vmax'
    });

    dialogRef.afterClosed().subscribe( () => {
      this.localStorage.flgModalChatAberta = false;
    });
  }

  CarregaMensagemChat(objFiltro: FiltroCarregaMensagem) {
    return this.http.post<RetListaMensagem>(environment.apiEndpoint + '/Chat2/CarregaMensagemChat', objFiltro);
  }

  CarregaQtdMsgNLidas() {
    return this.http.get<RetornoPadraoInt>(environment.apiEndpoint + '/Chat2/CarregaQtdMensagem');
  }

  EnviarMensagem(msg: MensagemModel) {
    return this.http.post<boolean>(environment.apiEndpoint + '/Chat2/EnviarMensagem', msg);
  }

  CarregaContatos() {
    return this.http.get<RetUsuarioChat>(environment.apiEndpoint + '/Chat2/CarregaUsuariosChat');
  }
}



// sendNotification(title: string, body: string) {
//   if (!("Notification" in window)) {
//     ;
//     return;
//   }

//   // Verifica o estado da permissão
//   if (Notification.permission === "granted") {
//     // Se a permissão já foi concedida, exibe a notificação
//     new Notification(title, { body });
//   } else if (Notification.permission !== "denied") {
//     // Solicita permissão ao usuário
//     Notification.requestPermission().then(permission => {
//       if (permission === "granted") {
//         new Notification(title, { body });
//       }
//     });
//   }
// }