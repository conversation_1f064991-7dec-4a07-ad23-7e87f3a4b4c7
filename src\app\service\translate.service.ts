import { TranslateLoader } from '@ngx-translate/core';
import { Observable } from 'rxjs';
// import { from } from 'rxjs';
import { Injectable } from '@angular/core';
import ptJson from '../../assets/Traducoes/pt.json';
import enJson from '../../assets/Traducoes/en.json';
import esJson from '../../assets/Traducoes/es.json';

@Injectable()
export class TranslateService implements TranslateLoader {

  private langs: { [index: string]: any } = {};

  constructor() {
    this.langs["pt"] = ptJson;
    this.langs["en"] = enJson;
    this.langs["es"] = esJson; 
   }

  getTranslation(lang: string): Observable<any> {
    return new Observable(s => {
      s.next(this.langs[lang]);
    })
  }
}