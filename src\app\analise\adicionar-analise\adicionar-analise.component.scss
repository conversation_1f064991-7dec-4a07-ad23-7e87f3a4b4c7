// Responsive adjustments
@media (max-width: 768px) {
    .card-body {
      padding: 16px;
    }
    
    .section-content {
      padding: 16px;
    }
    
    .card-footer {
      flex-direction: column;
      align-items: stretch;
      
      .footer-left, .footer-right {
        flex-wrap: wrap;
      }
      
      .btn {
        flex: 1;
      }
    }
    
    .history-details {
      flex-direction: column;
      gap: 8px;
    }
    
    .history-actions {
      margin-left: 16px;
    }
    
    .chat-message {
      max-width: 85%;
    }
    
    .modal-content {
      max-width: 100%;
    }
    
    .modal-body {
      padding: 16px;
    }
    
    .modal-footer {
      padding: 12px 16px;
    }
  }// Variáveis - Paleta Moderna com Verde Saúde
  $primary-color: #2E8B57;        // Verde Saúde
  $primary-light: #A3D9B1;        // Verde claro suavizado
  $primary-dark: #1F5F3D;         // Verde escuro
  $secondary-color: #F4F4F9;      // Cinza Claro / Off-White
  $secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
  $secondary-dark: #DADDE5;       // Cinza médio para hover/active
  $accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
  $error-color: #FF6B6B;          // Vermelho Pastel
  $success-color: #4CAF50;        // Verde para sucesso
  $pending-color: #FFC107;        // Amarelo para pendente
  $text-primary: #333333;         // Cinza escuro para boa legibilidade
  $text-secondary: #6B7280;       // Cinza médio
  $border-color: #E5E7EB;         // Bordas suaves
  $bg-color: #F9FAFB;             // Fundo geral suave
  $card-bg: #FFFFFF;              // Fundo dos cards
  $border-radius: 12px;
  $box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  $transition: 0.2s;
  
  * {
    box-sizing: border-box;
  }
  
  .analysis-container {
    height: 87vh;
    @media (max-width: 768px) {
      padding: 16px;
    }
  }
  
  // Cards
  .card {
    background-color: $card-bg;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    border: 1px solid $border-color;
    margin-bottom: 24px;
    overflow: hidden;
  }
  
  .main-card {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid $border-color;
    background-color: $secondary-light;
  }
  
  .card-body {
    padding: 24px;
    height: 67vh;
    overflow: auto;
  }
  
  .card-footer {
    padding: 16px 24px;
    border-top: none;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .footer-left {
    display: flex;
    gap: 12px;
  }
  
  .footer-right {
    display: flex;
    gap: 12px;
    margin-left: auto;
  }
  
  // Headers
  .header-left {
    display: flex;
    align-items: center;
  }
  
  .icon-container {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba($primary-color, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    
    .material-icons {
      color: $primary-color;
      font-size: 20px;
    }
  }
  
  .page-title {
    margin: 0;
    font-size: 1.5rem;
    color: $primary-dark;
    font-weight: 600;
  }
  
  // Sections
  .section {
    margin-bottom: 32px;
  }
  
  .section-header {
    margin-bottom: 16px;
  }
  
  .section-title {
    font-size: 1.25rem;
    color: $primary-color;
    margin: 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 18px;
      background-color: $primary-color;
      margin-right: 8px;
      border-radius: 2px;
    }
  }
  
  .section-content {
    background-color: $secondary-light;
    border-radius: $border-radius;
    padding: 20px;
  }
  
  // Patient Section
  .patient-info {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;
    align-items: center;
  }
  
  .patient-photo {
    flex: 0 0 100%;
    display: flex;
    justify-content: center;
  }
  
  .patient-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid $primary-light;
  }
  
  .patient-selector {
    flex: 1;
    min-width: 250px;
  }
  
  .orientation-type {
    flex: 0 0 250px;
    
    @media (max-width: 992px) {
      flex: 1;
    }
  }
  
  // Form
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
    gap: 16px;
  }
  
  .form-group {
    flex: 1;
    min-width: 250px;
  }
  
  .full-width {
    width: 79%;
  }
  
  // Material overrides
  :host ::ng-deep {
    .mat-form-field-appearance-outline .mat-form-field-outline {
      color: $border-color;
    }
    
    .mat-form-field-label {
      color: $text-secondary;
    }
    
    .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
      color: $primary-color;
    }
    
    .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
      color: $primary-color;
    }
    
    .mat-select-value {
      color: $text-primary;
    }
    
    .mat-select-arrow {
      color: $primary-color;
    }
    
    .mat-input-element:disabled {
      color: $text-secondary;
    }
  }
  
  // ng-select overrides
  .modern-select {
    ::ng-deep {
      .ng-select-container {
        border-radius: 4px;
        border-color: $border-color;
        min-height: 52px;
        
        &:hover {
          border-color: $primary-light;
        }
      }
      
      .ng-placeholder {
        color: $text-secondary;
      }
      
      .ng-value {
        color: $text-primary;
      }
      
      .ng-dropdown-panel {
        border-radius: 8px;
        border-color: $border-color;
        box-shadow: $box-shadow;
      }
      
      .ng-option {
        padding: 10px 16px;
        &.ng-option-selected, &.ng-option-marked {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;
        }
      }
    }
  }
  
  // Items List
  .items-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .item-card {
    background-color: #fff;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    padding: 16px;
    transition: all $transition ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }
  }
  
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .item-title {
    display: flex;
    align-items: center;
    gap: 8px;
    
    h3 {
      margin: 0;
      font-size: 1rem;
      font-weight: 500;
      color: $text-primary;
    }
    
    .material-icons {
      color: $primary-color;
      font-size: 20px;
    }
  }
  
  .item-status {
    font-size: 0.875rem;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
  }
  
  .status-success {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }
  
  .status-pending {
    background-color: rgba($pending-color, 0.1);
    color: darken($pending-color, 20%);
  }
  
  .item-details {
    margin-bottom: 12px;
    
    p {
      margin: 4px 0;
      font-size: 0.875rem;
      color: $text-secondary;
    }
  }
  
  .item-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
  
  // Empty state
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    background-color: rgba($secondary-dark, 0.1);
    border-radius: $border-radius;
    
    .material-icons {
      font-size: 48px;
      color: $secondary-dark;
      margin-bottom: 16px;
    }
    
    p {
      color: $text-secondary;
      font-size: 1rem;
      margin: 0;
    }
  }
  
  // Buttons
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    border: none;
    transition: all $transition ease;
    cursor: pointer;
    font-size: 14px;
    
    .material-icons {
      font-size: 18px;
      margin-right: 8px;
    }
    
    &:focus {
      outline: none;
    }
  }
  
  .btn-primary {
    background-color: $primary-color;
    color: white;
    
    &:hover {
      background-color: darken($primary-color, 5%);
    }
  }
  .btn-solicitar{
    color: #fff;
    background-color: #178aff;
    &:hover{
      background-color: #1265b9; 
    }
    }
  
  .btn-outline {
    background-color: transparent;
    border: 1px solid $border-color;
    color: $text-secondary;
    
    &:hover {
      border-color: $primary-color;
      color: $primary-color;
      background-color: rgba($primary-color, 0.05);
    }
  }
  
  .btn-success {
    background-color: $success-color;
    color: white;
    
    &:hover {
      background-color: darken($success-color, 5%);
    }
  }
  
  .btn-link {
    background: none;
    color: $primary-color;
    padding: 6px 12px;
    font-weight: 500;
    text-decoration: none;
    
    &:hover {
      background-color: rgba($primary-color, 0.05);
    }
  }
  
  .btn-icon {
    padding: 8px;
    border-radius: 50%;
    
    .material-icons {
      margin-right: 0;
    }
  }
  
  .btn-upload {
    position: relative;
    overflow: hidden;
    cursor: pointer;
  }
  
  // File upload
  input[type="file"] {
    display: none;
  }
  
  // History list
  .history-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #fff;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    
    &:hover {
      background-color: rgba($primary-color, 0.02);
    }
  }
  
  .history-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px 24px;
  }
  
  .history-detail-item {
    .detail-label {
      font-weight: 500;
      color: $primary-color;
      margin-right: 6px;
    }
    
    .detail-value {
      color: $text-primary;
    }
  }
  
  .history-actions {
    display: flex;
    gap: 8px;
  }
  
  // Professional area
  .professional-area {
    min-height: 150px;
  }
  
  .editor-container {
    border: 1px solid $border-color;
    border-radius: 8px;
    min-height: 150px;
    padding: 16px;
    background-color: rgba($secondary-color, 0.5);
  }
  
  // Modal Style - Global
  .modal-content {
    width: 100%;
    max-width: 900px;
    background-color: #fff;
    border-radius: $border-radius;
    overflow: hidden;
  }
  
  .modal-header {
    padding: 16px 24px;
    background-color: $primary-color;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
  }
  
  .modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    padding: 16px 24px;
    background-color: rgba($secondary-color, 0.5);
    border-top: 1px solid $border-color;
  }
  
  // PDF Viewer Modal
  .pdf-container {
    display: flex;
    justify-content: center; /* Centraliza horizontalmente */
    align-items: center;     /* Centraliza verticalmente */
    height: 70vh;           /* Ocupa altura total da tela */
    width: 100vh;
    
    ::ng-deep pdf-viewer {
      height: 100%;
      width: 101%;
    }
  }
  
  // Form Viewer
  .form-messages-container {
    max-height: 60vh;
    overflow-y: auto;
    background-color: $secondary-color;
    border-radius: $border-radius;
    padding: 16px;
  }
  
  .form-viewer-title {
    margin: 0 0 24px 0;
    color: $primary-color;
    font-size: 1.25rem;
    font-weight: 600;
    text-align: center;
  }
  
  .form-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
    border-radius: 8px;
    background-color: #fff;
    max-height: 60vh;
  }
  
  .form-qa-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid $border-color;
    
    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }
  
  .form-question {
    background-color: rgba($primary-color, 0.05);
    padding: 12px 16px;
    border-radius: 8px 8px 8px 0;
    margin-bottom: 8px;
    
    .question-label {
      font-weight: 600;
      color: $primary-color;
      display: block;
      margin-bottom: 4px;
    }
    
    p {
      margin: 0;
      color: $text-primary;
    }
  }
  
  .form-answer {
    background-color: rgba($success-color, 0.05);
    padding: 12px 16px;
    border-radius: 8px 8px 0 8px;
    margin-left: 24px;
    
    .answer-label {
      font-weight: 600;
      color: $success-color;
      display: block;
      margin-bottom: 4px;
    }
    
    p {
      margin: 0;
      color: $text-primary;
    }
  }
  
  // Chat Modal
  .chat-header {
    background-color: $primary-color;
  }
  
  .chat-body {
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 60vh;
  }
  
  .chat-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column-reverse;
    background-color: $secondary-color;
    height: 75vh;
  }
  
  .chat-message-wrapper {
    margin-bottom: 16px;
    display: flex;
    
    &.message-left {
      justify-content: flex-start;
    }
    
    &.message-right {
      justify-content: flex-end;
    }
  }
  
  .chat-message {
    max-width: 70%;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    
    &.message-normal {
      background-color: rgba($accent-color, 0.1);
    }
    
    &.message-important {
      background-color: rgba($primary-color, 0.2);
    }
    
    &.system {
      background-color: rgba($text-secondary, 0.1);
      text-align: center;
      max-width: 100%;
      padding: 8px 16px;
      margin: 8px 0;
      border-radius: 8px;
    }
  }
  
  .message-content {
    padding: 12px 16px;
    
    .message-sender {
      font-weight: 600;
      color: $primary-dark;
      margin: 0 0 4px 0;
      font-size: 0.875rem;
    }
    
    .message-text {
      margin: 0 0 8px 0;
      color: $text-primary;
      word-break: break-word;
    }
    
    .message-time {
      font-size: 0.75rem;
      color: $text-secondary;
      text-align: right;
      margin: 0;
    }
  }
  
  .chat-input-container {
    padding: 16px;
    background-color: #fff;
    border-top: 1px solid $border-color;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .chat-input-field {
    width: 95%;
    height: 57x !important;
    
    textarea {
      min-height: 80px;
      resize: none;
    }
  }
  
  .chat-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
  }
  
  .btn-send {
    align-self: flex-end;
    background-color: $primary-color;
    color: white;
    
    &:hover {
      background-color: darken($primary-color, 5%);
    }
  }
  
  // ngx-smart-modal overrides
  ::ng-deep {
    .nsm-content {
      border-radius: $border-radius !important;
      width: auto !important;
    }
    
    .nsm-dialog {
      padding: 0 !important;
    }
    
  }