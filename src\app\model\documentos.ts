import { Clinica } from './clinica';

export class Atestado {
    IdAtestado?: number;
    DesAtestado?: string;
    IdMedico?: number | null;
    IdPaciente?: number | null;
    IdCid?: number;
    FlgDesCid?: boolean;
    QuantDias?: number | null;
    dtaCadastro?: Date;
    flgInativo?: boolean;
    IdUsuarioGerador?: number;
    IdConsulta?:number;

}

export class Declaracao {
    IdDeclaracao?: number;
    DesDeclaracao?: string;
    IdMedico?: number | null;
    IdPaciente?: number | null;
    Periodo?: string;
    dtaCadastro?: Date;
    flgInativo?: boolean;
    IdUsuarioGerador?: number;
    IdConsulta?:number;
}

export class Receita {
    IdReceita?: number;
    DesReceita?: string;
    IdMedico?: number | null;
    IdPaciente?: number | null;
    dtaCadastro?: Date;
    flgInativo?: boolean;
    flgEndereco?: boolean;
    IdUsuarioGerador?: number;
    IdConsulta?:number;
}


export class MedicamentosProgramados {
    IdMedicamentosProgramados?: number;
    MedicamentosProgramados?: string;
    Programacao?: string;
    FlgInativo?: boolean;
    DtaCadastro?: Date;
    IdUsuarioGerador?: number;
    FlgDestacarItem?: boolean;
    clinicas?: Clinica[];
}
