import { Component, OnInit, Output } from '@angular/core';
import { EnvioEmailService } from 'src/app/service/envioEmail.service';

import { Router, RouterModule } from '@angular/router';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { AtendenteService } from 'src/app/service/atendente.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// import { FadeIn } from 'src/app/Util/Fadein.animation';
import { trigger, state,  transition, animate, style } from '@angular/animations';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
// import { ENTER_CLASSNAME } from '@angular/animations/browser/src/util';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {MatTooltipModule} from '@angular/material/tooltip';

@Component({
    selector: 'app-pesquisa-usuario',
    templateUrl: './pesquisa-usuario.component.html',
    styleUrls: ['./pesquisa-usuario.component.scss'],
    animations: [trigger('openClose', [
            state('open', style({
                opacity: '1',
                display: 'block'
            })),
            state('closed', style({
                opacity: '0',
                display: 'none'
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ]),
        ])
    ],
      standalone: true,
    imports: [
      MatInputModule,
      MatSlideToggleModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      RouterModule,
      NgxSmartModalModule,
      MatDivider,
      TruncatePipe,
      MatSlideToggleModule,
      MatTooltipModule,
      MatTooltipModule
    ]
})
export class PesquisaUsuarioComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private atendenteService: AtendenteService,    
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private router: Router,
    private emailService: EnvioEmailService,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    private snackbarAlert: AlertComponent,

  ) { }



  @Output() FadeIn!: string;
  // isOpen = false;
  bOcultaCarregaMais = false;
  qtdRegistros = 10;
  DadosUsuario: any;
  idAtendenteDeletar: any;
  idAtendenteAtivar: any;
  idUsuario = 0;
  Delete: any;
  // name = 'SnackBarConfirmação';

  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔';
  // actionButtonLabel: string = 'Fechar';
  // emailenviado: string = 'Email Enviado. ✔';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  // sendemail: string = 'E-mail enviado com sucesso.  ✔'
  // email: boolean;
  // flgEmail: boolean = false;


  idcliente?: number;
  nomecliente?: string;
  idEspecialidade: number = 0;
  idMedico: number = 0;

  DadosTab: any = [];
  pesquisa: string = '';
  cpf?: string;
  nomePaciente?: string;
  DadosEspecialidade: any = [];
  ListaMedicos: any = [];
  dadosLifeLine: any;
  idEmail = 0;
  DadosConsultaLifeLine: any
  dados = false;
  dadosAnonimo?: string;
  MedicoAnonimo?: string;
  Dataanonimo?: string;
  Foto = false;
  legenda = false;
  inativos = false;

  ngOnInit() {

    this.CarregaTable();
  }

  toggle:any = {}
  CarregaTable() {

    try {
      if (this.inativos == true) {
        this.atendenteService.getGridAtendenteInativos(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          
          this.DadosTab = retorno
          this.DadosTab.forEach((element:any) => {
            element['toggle'] = false;
          });
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          else
            this.bOcultaCarregaMais = false;

          this.spinner.hide();
        }, () => {
          this.spinner.hide();
        })

      }
      else {
        this.atendenteService.getGridAtendente(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
          
          this.DadosTab = retorno
          this.DadosTab.forEach((element:any) => {
            element['toggle'] = false;
          });
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          else
            this.bOcultaCarregaMais = false;
            this.spinner.hide();
        }, () => {
          console.error("erro")
          this.spinner.hide();
        })


      }
    } catch (error) {

    }

  }
  idatendente: number = 0
  ModalEmail(id:any, idAtendente:any) {
    this.idEmail = 0
    this.idEmail = id
    this.idatendente = idAtendente
    this.ngxSmartModalService.getModal('emailUsuario').open();
  }

  mandaEmail() {
    if (this.idEmail != 0) {
      this.spinner.show();
      this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe(() => {

        this.snackbarAlert.sucessoSnackbar("Email enviado com sucesso!")
        this.CarregaTable();
        this.ngxSmartModalService.closeAll();
        this.idEmail = 0
        this.spinner.hide();
      }, err => {
        console.error(err)
        this.spinner.hide();
      })
    }
  }

  mandaEmailAtivarUser() {
    if (this.idEmail != 0) {
      this.spinner.show();
      this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe(() => {

        if (this.idatendente > 0)
          this.idAtendenteAtivar = this.idatendente;

        this.atendenteService.AtivarAtendente(this.idAtendenteAtivar, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
          this.Delete = retorno;
          this.idatendente = 0
          if (this.Delete == true) {
            this.snackbarAlert.sucessoSnackbar("Email enviado e Usuario Ativo!")

            this.ngxSmartModalService.closeAll();
            this.idEmail = 0
            this.CarregaTable();
          }
          this.spinner.hide();
        })
      }, err => {
        console.error(err)
        this.spinner.hide();
      })
    }
  }

  // EnviarEmailSnack(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAPESQUISAMEDICO.EMAILENVIADOV').subscribe((res: string) => {
  //       ;
  //       this.snackbarAlert.sucessoSnackbar(res);      
  //     });
  //   }
  //   this.flgEmail = false;
  // }

  public editUsuario(idAtendente:any) {
    if (idAtendente != "" && idAtendente != 0) {
      this.localStorageService.idAtendente = idAtendente;
      this.router.navigate(['/CadastroAtendente']);
    }

  }

  CarregarMais() {
    this.bOcultaCarregaMais = false;
    if (this.inativos == true) {
      this.atendenteService.getGridAtendenteInativos(this.DadosTab.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        var dados = retorno;
        for (let index = 0; index < dados.length; index++) {
          this.DadosTab.push(dados[index]);
        }
        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;

        this.spinner.hide();
      }, () => {
        console.error("erro")
        this.spinner.hide();
      })
    } else {
      this.atendenteService.getGridAtendente(this.DadosTab.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUsuarioAcesso() == 1 ? 0 : this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        var dados = retorno;
        for (let index = 0; index < dados.length; index++) {
          this.DadosTab.push(dados[index]);
        }
        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;
        else
          this.bOcultaCarregaMais = false;
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }
  }

  AtivarUsuario() {
    try {
      //remover id
      if (this.idatendente > 0)
        this.idAtendenteAtivar = this.idatendente;

      this.atendenteService.AtivarAtendente(this.idAtendenteAtivar, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        this.Delete = retorno;
        if (this.Delete == true) {
          this.idatendente = 0
          this.tradutor.get('TELAPESQUISAUSUARIO.USUARIOATIVOCOMSUCESSO').subscribe((res: string) => {
            ;
            this.ngxSmartModalService.getModal('ativarItem').close();
            this.snackbarAlert.sucessoSnackbar(res);
            this.CarregaTable();
          });
        }
        else {

          this.tradutor.get('TELAPESQUISAUSUARIO.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
            this.snackbarAlert.falhaSnackbar(res);
          });
        }
      }, () => {

        this.tradutor.get('TELAPESQUISAUSUARIO.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
          this.snackbarAlert.falhaSnackbar(res);
        });
      })

    } catch (error) {

      this.tradutor.get('TELAPESQUISAUSUARIO.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
        this.snackbarAlert.falhaSnackbar(res);
      });
    }
  }

  InativarUsuario() {
    try {
      this.atendenteService.InativarAtendente(this.idAtendenteDeletar, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        this.Delete = retorno;
        if (this.Delete == true) {

          // this.tradutor.get('TELAPESQUISAUSUARIO.USUARIOEXCLUIDOCOMSUCESSO').subscribe((res: string) => {
          //   ;
          var res = "Usuário inativo com sucesso!"
          this.ngxSmartModalService.getModal('excluirItem').close();
          this.snackbarAlert.sucessoSnackbar(res);
          this.CarregaTable();
          this.spinner.hide();
          // });
        }
        else {
          

          this.tradutor.get('TELAPESQUISAUSUARIO.ERROAOINATIVAR').subscribe((res: string) => {
            ;
            this.spinner.hide();
            this.snackbarAlert.falhaSnackbar(res);
          });
        }
      }, () => {
        this.spinner.hide();

      })
    } catch (error) {

      this.tradutor.get('TELAPESQUISAUSUARIO.ERROAOINATIVAR').subscribe((res: string) => {
        ;
        this.spinner.hide();
        this.snackbarAlert.falhaSnackbar(res);
      });
    }

  }
  public ModalAtivarAtendente(idAtendente:any) {
    this.idAtendenteAtivar = 0
    this.idAtendenteAtivar = idAtendente;
    this.ngxSmartModalService.getModal('ativarItem').open();
  }

  public ModalInativarAtendente(idAtendente:any) {
    this.idAtendenteDeletar = 0
    this.idAtendenteDeletar = idAtendente;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }


  // AlgumErro(value, menssage) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     this.snackBar.open(menssage, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  //   this.concordo = value;
  //   this.concordomsg = false;
  // }

  PerfilAtendente(obj:any) {
    if (obj != "" && obj != 0) {
      this.localStorageService.idPerfil = obj.idUsuarioacesso;
      this.router.navigate(['/perfil']);
    }
  }


  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTab[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.DadosTab[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index:any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.DadosTab[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTab[this.indexGlobal]['toggle'] = !this.DadosTab[this.indexGlobal]['toggle'];
  }
}

