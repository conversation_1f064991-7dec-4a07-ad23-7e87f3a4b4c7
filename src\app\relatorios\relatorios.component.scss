@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

// Variáveis - Esquema de cores verde
$primary-color: #2E8B57; // Verde principal
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; 
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Base Styles */
body {
  font-family: 'Cairo', sans-serif;
  color: $text-primary;
}

/* Main container - gray background */
.main-container {
  height: 87vh;
  overflow: auto ;
}

/* Main Card */
.report-card {
  background-color: #ffffff !important;
  border-radius: $border-radius !important;
  margin: 0 auto !important;
  padding: 0 !important;
  overflow: auto !important;
  border: none !important;
  max-height: 87vh;
  box-shadow: $box-shadow;
  border: 1px solid $border-color;
  border-top:4px solid #2E8B57 !important ;

}

/* White container for each section */
.white-container {
  background-color: $card-bg;
  margin: 15px;
  border-radius: $border-radius;
  overflow: hidden;
}

/* Header Section */
.report-header {
  padding: 20px;
}

.header-content {
  display: flex;
  align-items: center;
}

.header-icon {
  color: $primary-color;
  font-size: 30px;
  border: 2px solid $primary-color;
  border-radius: 50%;
  padding: 5px;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-title {
  color: $primary-color;
  font-size: 24px;
  font-weight: 600;
  margin-left: 15px;
}

.clinic-name {
  margin-left: 15px;
  font-weight: 500;
  color: $text-primary;
}

/* Dividers */
mat-divider {
  margin: 0 !important;
  border-top-color: $border-color !important;
}

/* Date Section */
.date-section {
  padding: 20px;
}

.date-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.date-field {
  width: 200px;
  position: relative;
}

.checkbox-container {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.checkbox-item {
  padding: 10px;
}

.error-text {
  color: $error-color;
  font-size: 12px;
  position: absolute;
  left: 15px;
  bottom: -20px;
}

/* Botão de configurar opções */
.config-button {
  background-color: $secondary-color !important;
  color: $primary-color !important;
  border: 2px dashed $primary-color !important;
  padding: 0 25px !important;
  height: 45px !important;
  border-radius: $border-radius !important;
  box-shadow: $box-shadow !important;
  display: flex !important;
  align-items: center !important;
  transition: $transition !important;
}

.config-button:hover {
  background-color: rgba(46, 139, 87, 0.1) !important;
  transform: translateY(-2px);
}

/* Options Section - Melhorada para Modal */
.options-modal {
  max-width: 90% !important;
  width: 1000px !important;
}

.options-modal-container {
  padding: 0 !important;
}

.modal-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  background-color: $secondary-color;
}

.options-section {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  overflow-y: visible;
}

.option-column {
  flex: 1;
  min-width: 250px;
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(46, 139, 87, 0.08);
  border-left: 3px solid $primary-color;
  margin-bottom: 10px;
}

.option-header {
  margin-bottom: 15px;
  border-bottom: 1px solid rgba(46, 139, 87, 0.2);
  padding-bottom: 10px;
  display: flex;
  align-items: center;
}

.option-title {
  color: $primary-color;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  padding-left: 8px;
}

.option-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 16px;
  background-color: $primary-color;
  border-radius: 2px;
}

.option-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  padding-left: 5px;
}

.option-item {
  font-size: 14px;
  color: $text-primary;
  transition: $transition;
  padding: 3px 0;
}

.option-item:hover {
  color: $primary-color;
}

/* Button Styles */
.button-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.generate-button {
  background-color: $primary-color !important;
  color: white !important;
  border: none !important;
  padding: 0 25px !important;
  height: 45px !important;
  border-radius: $border-radius !important;
  box-shadow: $box-shadow !important;
  display: flex !important;
  align-items: center !important;
  transition: $transition !important;
}

.generate-button:hover {
  background-color: $primary-dark !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 10px -1px rgba(0, 0, 0, 0.15), 0 3px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.button-text {
  margin-left: 8px;
}

/* Pattern Section */
.pattern-section {
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.pattern-name {
  flex: 1;
  min-width: 250px;
}

.pattern-buttons {
  display: flex;
  gap: 15px;
}

.new-button {
  background-color: $secondary-color !important;
  color: $primary-color !important;
  border: 1px solid $primary-color !important;
  padding: 0 20px !important;
  height: 45px !important;
  border-radius: $border-radius !important;
  display: flex !important;
  align-items: center !important;
  transition: $transition !important;
}

.new-button:hover {
  background-color: $primary-light !important;
}

.save-button {
  background-color: $primary-color !important;
  color: white !important;
  border: none !important;
  padding: 0 20px !important;
  height: 45px !important;
  border-radius: $border-radius !important;
  display: flex !important;
  align-items: center !important;
  transition: $transition !important;
}

.save-button:hover {
  background-color: $primary-dark !important;
}

/* Patterns List */
.patterns-list {
  padding: 20px;
  max-height: 300px;
  overflow-y: auto;
  background-color: $secondary-color;
}

.patterns-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 10px;
}

.pattern-row {
  background-color: $card-bg;
  border-radius: $border-radius;
  transition: $transition;
}

.pattern-row:hover {
  box-shadow: $box-shadow;
  transform: translateY(-2px);
}

.pattern-name-cell {
  padding: 15px;
  cursor: pointer;
  border-radius: $border-radius 0 0 $border-radius;
  font-weight: 500;
}

.pattern-label {
  font-weight: 600;
  color: $primary-color;
  margin-right: 5px;
}

.pattern-action-cell {
  width: 70px;
  text-align: center;
  border-radius: 0 $border-radius $border-radius 0;
}

.delete-button {
  background-color: transparent;
  color: $error-color;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: $transition;
}

.delete-button:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

/* Modal Styles */
.modal-container {
  border-radius: $border-radius;
  overflow: hidden;
}

.modal-header {
  background-color: $primary-light;
  padding: 20px;
  text-align: center;
}

.success-header {
  background-color: $primary-color;
}

.modal-title {
  color: $primary-color;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.success-header .modal-title {
  color: white;
}

.modal-actions {
  background-color: $card-bg;
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.modal-button {
  padding: 0 20px !important;
  height: 40px !important;
  border-radius: $border-radius !important;
}

.ok-button, .edit-button {
  background-color: $primary-color !important;
  color: white !important;
}

.cancel-button {
  background-color: $secondary-color !important;
  color: $text-primary !important;
}

/* Checkbox styling */
.green-checkbox .mat-checkbox-frame {
  border-color: $primary-color !important;
}

.green-checkbox .mat-checkbox-background, 
.green-checkbox .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
  background-color: $primary-color !important;
}

/* Form field styling */
.mat-form-field-appearance-outline .mat-form-field-outline {
  color: $border-color;
}

.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: $primary-color !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0;
}

.mat-form-field-wrapper {
  padding-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .date-container {
    flex-direction: column;
    align-items: center;
  }
  
  .checkbox-container {
    flex-direction: column;
    align-items: center;
  }
  
  .option-column {
    min-width: 100%;
  }
  
  .pattern-section {
    flex-direction: column;
  }
  
  .pattern-name {
    width: 100%;
  }
  
  .pattern-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .button-text {
    display: none;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .header-icon {
    margin-bottom: 10px;
  }
  
  .header-title, .clinic-name {
    margin-left: 0;
  }
  
  .white-container {
    margin: 10px;
  }
  
  .options-modal {
    max-width: 95% !important;
    width: 95% !important;
  }
}

@media (max-width: 480px) {
  .patterns-table {
    font-size: 14px;
  }
  
  .pattern-name-cell {
    padding: 10px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-button {
    width: 100%;
  }
  
  .option-column {
    padding: 10px;
  }
}
/* Estilos compartilhados */
.mat-card {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.mat-card-header {
  padding: 16px 20px;
  background-color: rgba(46, 139, 87, 0.05);
  border-bottom: 1px solid rgba(46, 139, 87, 0.1);
}

.mat-card-title {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #2E8B57 !important;
  position: relative;
  padding-left: 12px;
}

.mat-card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #2E8B57;
  border-radius: 2px;
}

/* Estilos para a tabela de atividades */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
}

.result-row {
  border-bottom: 1px solid #e0e0e0;
}

.result-row:last-child {
  border-bottom: none;
}

.result-row td {
  padding: 12px 8px;
  vertical-align: top;
}

.cell-label {
  font-weight: 500;
  color: #555;
  margin-right: 4px;
}

.user-cell {
  width: 20%;
}

.description-cell {
  width: 60%;
}

.date-cell {
  width: 20%;
  white-space: nowrap;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.load-more-button {
  background-color: rgba(46, 139, 87, 0.1);
  color: #2E8B57;
  font-weight: 500;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #2E8B57;
}

.load-more-button:hover {
  background-color: rgba(46, 139, 87, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(46, 139, 87, 0.1);
}

/* Estilos para os cartões de padrões */
.patterns-grid {
  margin-top: 20px;
  max-height: 41vh;
  overflow: auto;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.pattern-row {
  width: 100%;
}

.pattern-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  transition: .3s;
}

.pattern-card:hover:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #2E8B57;
}

.pattern-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(46, 139, 87, 0.1);
}

.pattern-card-content {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pattern-info {
  flex-grow: 1;
  padding-right: 10px;
}

.pattern-name {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 6px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pattern-details {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
}

.pattern-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #2E8B57;
}

.pattern-actions {
  display: flex;
  gap: 5px;
}

.pattern-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
}

.edit-button {
  color: #2E8B57;
}

.edit-button:hover {
  background-color: rgba(46, 139, 87, 0.1);
}

.delete-button {
  color: #dc2626;
}

.delete-button:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

.empty-patterns {
  text-align: center;
  padding: 30px;
  border-radius: 12px;
  background-color: #f9fafb;
  grid-column: 1 / -1;
}

.empty-patterns-icon {
  font-size: 40px;
  color: #cbd5e1;
  margin-bottom: 10px;
}

.empty-patterns-text {
  color: #64748b;
  font-size: 15px;
}

/* Responsividade */
@media (max-width: 768px) {
  .result-row {
    display: flex;
    flex-direction: column;
    padding: 12px 0;
  }
  
  .result-row td {
    width: 100%;
    padding: 6px 8px;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
}
.w-100{
 width: 100%;
 justify-content: center;
 display: flex;
 grid-column: 3 span;
  .btn-primary{
  background-color: $primary-color !important;
 }
}
.patterns-grid {
  margin-top: 20px;
  max-height: 41vh;
  overflow: auto;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.pattern-row {
  width: 100%;
}

.pattern-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  transition: .3s;
}

.pattern-card:hover:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #2E8B57;
}

.pattern-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(46, 139, 87, 0.1);
}

.pattern-card-content {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pattern-info {
  flex-grow: 1;
  padding-right: 10px;
}

.pattern-name {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 6px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pattern-details {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
}

.pattern-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #2E8B57;
}

.pattern-actions {
  display: flex;
  gap: 5px;
}

.pattern-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
}

.edit-button {
  color: #2E8B57;
}

.edit-button:hover {
  background-color: rgba(46, 139, 87, 0.1);
}

.delete-button {
  color: #dc2626;
}

.delete-button:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

.empty-patterns {
  text-align: center;
  padding: 30px;
  border-radius: 12px;
  background-color: #f9fafb;
  grid-column: 1 / -1;
}

.empty-patterns-icon {
  font-size: 40px;
  color: #cbd5e1;
  margin-bottom: 10px;
}

.empty-patterns-text {
  color: #64748b;
  font-size: 15px;
}

/* Responsividade */
@media (max-width: 768px) {
  .result-row {
    display: flex;
    flex-direction: column;
    padding: 12px 0;
  }
  
  .result-row td {
    width: 100%;
    padding: 6px 8px;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
}
.w-100{
 width: 100%;
 justify-content: center;
 display: flex;
 grid-column: 3 span;
  .btn-primary{
  background-color: $primary-color !important;
 }
}