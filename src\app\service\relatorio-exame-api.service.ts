import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class RelatorioExameApiService {

  private readonly apiUrl = `${environment.apiEndpoint}/RelatorioExameApi`;

  constructor(private http: HttpClient) { }

  /**
   * Registers patient data with CPF lookup and token generation
   */
  registerPatient(patientData: PatientRegistrationRequest): Observable<PatientRegistrationResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<any>(`${this.apiUrl}/register`, patientData, { headers })
      .pipe(
        map(response => this.mapRegistrationResponse(response)),
        catchError(this.handleError)
      );
  }

  /**
   * Retrieves patient data by token for doctor interface
   */
  getPatientDataByToken(token: string): Observable<DoctorPatientDataResponse> {
    if (!token) {
      return throwError('Token is required');
    }

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.getAuthToken()}`
    });

    return this.http.get<any>(`${this.apiUrl}/patient/${token}`, { headers })
      .pipe(
        map(response => this.mapPatientDataResponse(response)),
        catchError(this.handleError)
      );
  }

  /**
   * Updates patient data
   */
  updatePatient(patientData: PatientUpdateRequest): Observable<PatientUpdateResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.getAuthToken()}`
    });

    return this.http.put<any>(`${this.apiUrl}/update`, patientData, { headers })
      .pipe(
        map(response => this.mapUpdateResponse(response)),
        catchError(this.handleError)
      );
  }

  /**
   * Validates CPF and checks if it exists in the system
   */
  validateCpf(cpf: string): Observable<CpfValidationResponse> {
    if (!cpf) {
      return throwError('CPF is required');
    }

    return this.http.get<any>(`${this.apiUrl}/validate-cpf/${cpf}`)
      .pipe(
        map(response => this.mapCpfValidationResponse(response)),
        catchError(this.handleError)
      );
  }

  /**
   * Processes patient queue registration with automatic data storage
   */
  processQueueRegistration(queueData: QueueRegistrationData): Observable<QueueRegistrationResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<any>(`${this.apiUrl}/queue-registration`, queueData, { headers })
      .pipe(
        map(response => this.mapQueueRegistrationResponse(response)),
        catchError(this.handleError)
      );
  }

  /**
   * Updates patient consultation status
   */
  updatePatientStatus(token: string, status: string): Observable<PatientStatusUpdateResponse> {
    if (!token) {
      return throwError(() => 'Token is required');
    }

    if (!status) {
      return throwError(() => 'Status is required');
    }

    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    const requestBody = { status: status };

    return this.http.put<any>(`${this.apiUrl}/patient/${token}/status`, requestBody, { headers })
      .pipe(
        map(response => this.mapStatusUpdateResponse(response)),
        catchError(this.handleError)
      );
  }

  /**
   * Health check endpoint
   */
  healthCheck(): Observable<HealthCheckResponse> {
    return this.http.get<any>(`${this.apiUrl}/health`)
      .pipe(
        map(response => ({
          success: response.success,
          message: response.message,
          timestamp: new Date(response.timestamp),
          version: response.version
        })),
        catchError(this.handleError)
      );
  }

  // Private helper methods

  private getAuthToken(): string {
    // Get token from localStorage or your auth service
    return localStorage.getItem('authToken') || '';
  }

  private mapRegistrationResponse(response: any): PatientRegistrationResponse {
    return {
      success: response.success,
      message: response.message,
      token: response.token,
      id: response.id,
      patientData: response.data ? {
        nome: response.data.nome || '',
        cpf: response.data.cpf || '',
        email: response.data.email || '',
        telefone: response.data.telefone || '',
        hasPersonRecord: response.data.hasPersonRecord || false
      } : undefined,
      errors: response.errors || []
    };
  }

  private mapPatientDataResponse(response: any): DoctorPatientDataResponse {
    return {
      success: response.success,
      message: response.message,
      patient: response.patient ? {
        name: response.patient.name || '',
        cpf: response.patient.cpf || '',
        email: response.patient.email || '',
        phone: response.patient.phone || '',
        birthDate: response.patient.birthDate ? new Date(response.patient.birthDate) : null,
        age: response.patient.age || null,
        allergies: response.patient.allergies || '',
        previousDiseases: response.patient.previousDiseases || '',
        vitalSigns: response.patient.vitalSigns || {},
        symptoms: response.patient.symptoms || {},
        additionalObservations: response.patient.additionalObservations || '',
        registrationDate: new Date(response.patient.registrationDate),
        hasPersonRecord: response.patient.hasPersonRecord || false
      } : undefined,
      errors: response.errors || []
    };
  }

  private mapUpdateResponse(response: any): PatientUpdateResponse {
    return {
      success: response.success,
      message: response.message,
      patientData: response.data ? {
        id: response.data.id || 0,
        nome: response.data.nome || '',
        cpf: response.data.cpf || '',
        email: response.data.email || '',
        telefone: response.data.telefone || ''
      } : undefined,
      errors: response.errors || []
    };
  }

  private mapCpfValidationResponse(response: any): CpfValidationResponse {
    return {
      success: response.success,
      message: response.message,
      cpfInfo: response.cpf ? {
        isValid: response.cpf.isValid || false,
        existsInDatabase: response.cpf.existsInDatabase || false,
        personId: response.cpf.personId || null,
        formattedCpf: response.cpf.formattedCpf || ''
      } : undefined,
      errors: response.errors || []
    };
  }

  private mapQueueRegistrationResponse(response: any): QueueRegistrationResponse {
    return {
      success: response.success,
      message: response.message,
      token: response.token,
      queueInfo: response.queueInfo ? {
        patientName: response.queueInfo.patientName || '',
        queueType: response.queueInfo.queueType || '',
        queueTime: new Date(response.queueInfo.queueTime),
        clinicId: response.queueInfo.clinicId || 0,
        doctorId: response.queueInfo.doctorId || 0
      } : undefined,
      errors: response.errors || []
    };
  }

  private mapStatusUpdateResponse(response: any): PatientStatusUpdateResponse {
    return {
      success: response.success,
      message: response.message,
      patientName: response.patientName || '',
      previousStatus: response.previousStatus || '',
      newStatus: response.newStatus || '',
      errors: response.errors || []
    };
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }

    console.error('RelatorioExameApiService Error:', errorMessage);
    return throwError(() => errorMessage);
  }
}

// Interface definitions

export interface PatientRegistrationRequest {
  token: string;
  nome: string;
  cpf: string;
  email?: string;
  telefone?: string;
  dataNascimento?: string;
  alergias?: string;
  doencasPrevias?: string;
  sintomas?: string;
  intensidade?: string;
  duracaoSintomas?: string;
  observacoesAdicionais?: string;
  pressaoSistolica?: string; 
  pressaoDiastolica?: string;
  oxigenacao?: string;
  batimento?: string; 
  temperatura?: string;
}

export interface PatientUpdateRequest extends PatientRegistrationRequest {
  id: number;
  token: string;
}

export interface PatientRegistrationResponse {
  success: boolean;
  message: string;
  token?: string;
  id?: number;
  patientData?: {
    nome: string;
    cpf: string;
    email: string;
    telefone: string;
    hasPersonRecord: boolean;
  };
  errors: string[];
}

export interface DoctorPatientDataResponse {
  success: boolean;
  message: string;
  patient?: {
    name: string;
    cpf: string;
    email: string;
    phone: string;
    birthDate: Date | null;
    age: number | null;
    allergies: string;
    previousDiseases: string;
    vitalSigns: VitalSigns;
    symptoms: Symptoms;
    additionalObservations: string;
    registrationDate: Date;
    hasPersonRecord: boolean;
  };
  errors: string[];
}

export interface PatientUpdateResponse {
  success: boolean;
  message: string;
  patientData?: {
    id: number;
    nome: string;
    cpf: string;
    email: string;
    telefone: string;
  };
  errors: string[];
}

export interface CpfValidationResponse {
  success: boolean;
  message: string;
  cpfInfo?: {
    isValid: boolean;
    existsInDatabase: boolean;
    personId: number | null;
    formattedCpf: string;
  };
  errors: string[];
}

export interface QueueRegistrationData {
  patientData: PatientRegistrationRequest;
  queueType: string;
  clinicId?: number;
  doctorId?: number;
  queueTime: Date;
}

export interface QueueRegistrationResponse {
  success: boolean;
  message: string;
  token?: string;
  queueInfo?: {
    patientName: string;
    queueType: string;
    queueTime: Date;
    clinicId: number;
    doctorId: number;
  };
  errors: string[];
}

export interface VitalSigns {
  systolicPressure?: number;
  diastolicPressure?: number;
  bloodPressure?: string;
  oxygenation?: number;
  heartRate?: number;
  temperature?: number;
  temperatureFormatted?: string;
}

export interface Symptoms {
  description?: string;
  intensity?: string;
  duration?: string;
}

export interface PatientStatusUpdateResponse {
  success: boolean;
  message: string;
  patientName: string;
  previousStatus: string;
  newStatus: string;
  errors: string[];
}

export interface HealthCheckResponse {
  success: boolean;
  message: string;
  timestamp: Date;
  version: string;
}
