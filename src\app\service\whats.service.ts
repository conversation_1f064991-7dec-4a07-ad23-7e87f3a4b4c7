
import { catchError, Observable, throwError } from 'rxjs';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { SpinnerService } from './spinner.service';
import { ModelConsultaWhats, ObjConsultaChat, ObjMensagemEnvio, ObjMensagemListaPaciente, ObjMensagemWhatsappPadrao } from '../model/whats';
import { ObjetoPadrao } from '../model/RetornoPadraoApi';

@Injectable({
    providedIn: 'root'
})
export class WhatsService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService,
    ) { }

    public headers = new Headers({ 'Content-Type': 'application/json' });
    reqHeader = new HttpHeaders({ 'No-Auth': 'True' });


    public EnvioUnicoConfirmacaoConsultas(idConsulta: number): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Whats/EnvioUnicoConfirmacaoConsultas', idConsulta);
    }

    public EnviaListaConfirmacaoConsultas(listaIds: number[]): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Whats/EnviaListaConfirmacaoConsultas', listaIds);
    }

    public ConfirmarChegadaPaciente(obj: ObjetoPadrao): Observable<any> {
        return this.http.post(environment.apiEndpoint + '/Whats/ConfirmarChegadaPaciente', obj);
    }

    public SalvarObservacaoChegadaPaciente(obj: ObjetoPadrao): Observable<any> {
        return this.http.post(environment.apiEndpoint + '/Whats/SalvarObservacaoChegadaPaciente', obj);
    }

    public EnviarMensagensConsultas(obj: ObjMensagemEnvio): Observable<any> {
        return this.http.post<string>(environment.apiEndpoint + '/Whats/EnviarMensagensConsultas', obj);
    }

    public CarregaConsultaChat(): Observable<any> {
        return this.http.get<ObjConsultaChat>(environment.apiEndpoint + '/Whats/CarregaConsultaChat');
    }

    public CarregaConsultaWhats(lsIdsConsulta: number[]) {
        return this.http.post<ModelConsultaWhats[]>(environment.apiEndpoint + '/Consulta/CarregaConsultaWhats', lsIdsConsulta);
    }

    public EnviarMensagemListaPaciente(obj: ObjMensagemListaPaciente) {
        return this.http.post(environment.apiEndpoint + '/Whats/EnviarMensagemListaPaciente', obj)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public GetListaMensagemPadrao(idOrigemMensagem: number) {
        let params = new HttpParams();
        params = params.append('idOrigemMensagem', idOrigemMensagem);
        return this.http.get<ObjMensagemWhatsappPadrao[]>(environment.apiEndpoint + '/Whats/GetListaMensagemPadrao', {params})
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }
}