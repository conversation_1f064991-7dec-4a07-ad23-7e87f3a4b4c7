@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

$primary: #348bc1;

.tabela {
  margin-right: 30px;
  width: 100%;
  border-radius: 10px;
  border-collapse: inherit !important;
}

.tabela th {
  background-color: $primary;
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  border-bottom: 2px solid #ddd;
}

.tabela td {
  font-size: 14px;
  text-align: center;
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

tr:hover {
  background: #eef7ff !important;
}

.icon-page-title {
  margin-right: 10px;
}

.title-page {
  font-family: 'Cairo', sans-serif;
  font-size: 24px;
  font-weight: bold;
  color: $primary;
}

.div-scroll-table {
  max-height: 400px;
  overflow-y: auto;
}

.cabecalhotable {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: $primary;
}

.w3-hoverable tr:hover {
  background-color: #eef7ff !important;
}

.w3-table-all {
  border: 1px solid #ddd;
  border-radius: 10px;
  background-color: white;
}

tbody td {
  word-wrap: break-word;
  word-break: break-all;
}

tbody tr {
  transition: background-color 0.3s ease;
}

.card-dash {
  padding: 5px;
  flex: 1 1 25%;
  max-width: 25%; 
}

.erros-dash {
  background-color: rgba(82, 96, 255, 0.0705882353);
  border-radius: 10px;
  padding: 10px;
  display: flex;
  align-items: center;
}

.title-dashInt {
  padding-left: 10px;
}

.title-dashInt p {
  margin: 0;
  font-size: 13px;
  font-family: system-ui;
  font-weight: 500;
  color: #5260ff;
}

.title-dashInt span {
  font-size: 25px;
  font-family: system-ui;
  font-weight: 700;
  color: #5260ff;
}

.icons-dashInt {
  align-self: center;
  display: flex;
}
