import { Component, OnInit, Output } from '@angular/core';
import { ContatosService } from '../service/agendaContato.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import {
  FormControl, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { Contato } from '../model/contato';
import { FadeIn } from 'src/app/Util/Fadein.animation';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { MatRadioModule } from '@angular/material/radio';
import { AlertComponent } from '../alert/alert.component';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-agendacontato',
  templateUrl: './agendacontato.component.html',
  styleUrls: ['./agendacontato.component.scss'],
  animations: [FadeIn, trigger('openClose', [
    state('open', style({
      opacity: '1',
      display: 'block'
    })),
    state('closed', style({
      opacity: '0',
      display: 'none'
    })),
    transition('open => closed', [
      animate('0.2s')
    ]),
    transition('closed => open', [
      animate('0.2s')
    ]),
  ])
  ],
  host: { '[@FadeIn]': '' },
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatRadioModule,
    TranslateModule,
    MatFormFieldModule,
    MatIcon,
    NgxSmartModalModule,
    MatDivider,
    TruncatePipe,
    MatCardModule
  ]
})
export class AgendacontatoComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private contatoService: ContatosService,
    public ngxSmartModalService: NgxSmartModalService,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBar: AlertComponent

  ) { }

  @Output() FadeIn!: string;
  isOpen = false;
  legenda: boolean = false;
  dadosNovoContato: any = []
  DadosTab: any = [];
  pesquisa!: string;
  submitErro: boolean = false;
  Tipopesquisa: string = "Nome";
  Edcao: boolean = false;
  // usuario: Usuario;

  TelVal!: boolean;
  TelMovValVasil = false;
  TelMovLimpa = false;
  TelMovVal!: boolean;
  TelComVal!: boolean;
  idDelet!: number | null;

  qtdRegistros = 10;
  bOcultaCarregaMais = false;

  toggle: boolean[] = [];

  ngOnInit() {
    this.CarregaTable()
  }


  CarregaTable() {
    try {
      this.bOcultaCarregaMais = false;

      let txtPesquisa = this.pesquisa;

      if (this.Tipopesquisa == 'Tel')
        txtPesquisa = this.NaoEditado(this.pesquisa);

      this.contatoService.getContatos(txtPesquisa, this.Tipopesquisa, this.usuarioLogadoService.getIdUltimaClinica(), 0, this.qtdRegistros).subscribe((retorno) => {


        this.DadosTab = retorno
        this.toggle = new Array(this.DadosTab.length).fill(false);
        this.DadosTab.forEach((element: any) => {

          if (element.tel != null && element.tel != '' && element.tel != undefined)
            element.tel = this.Editado(element.tel);

          if (element.telMovel != null && element.telMovel != '' && element.telMovel != undefined)
            element.telMovel = this.Editado(element.telMovel);

          if (element.telCom != null && element.telCom != '' && element.telCom != undefined)
            element.telCom = this.Editado(element.telCom);
        });

        this.spinner.hide();

      }, () => {
        this.spinner.hide();
        console.error("erro")
      })
    } catch (error) {
      this.spinner.hide();

    }
  }

  CarregarMais() {
    if (this.Tipopesquisa == 'Tel')
      this.pesquisa = this.NaoEditado(this.pesquisa);

    this.contatoService.getContatos(this.pesquisa, this.Tipopesquisa, this.usuarioLogadoService.getIdUltimaClinica(), this.DadosTab.length, this.qtdRegistros).subscribe((retorno) => {

      var dados = retorno;
      const initialLength = this.DadosTab.length;

      for (let index = 0; index < dados.length; index++) {
        this.DadosTab.push(dados[index]);
      }

      this.toggle = [
        ...this.toggle,
        ...new Array(this.DadosTab.length - initialLength).fill(false)
      ];

      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;


      this.DadosTab.forEach((element: any) => {

        if (element.tel != null && element.tel != '' && element.tel != undefined)
          element.tel = this.Editado(element.tel);

        if (element.telMovel != null && element.telMovel != '' && element.telMovel != undefined)
          element.telMovel = this.Editado(element.telMovel);

        if (element.telCom != null && element.telCom != '' && element.telCom != undefined)
          element.telCom = this.Editado(element.telCom);
      });

      this.spinner.hide();
    }, () => {
      console.error("erro")
      this.spinner.hide();
    })

  }

  NaoEditado(numeroTelefone: any) {
    return numeroTelefone.replace(/\D/g, "");
  }

  Editado(numeroTelefone: any) {
    numeroTelefone = numeroTelefone.replace(/\D/g, "");
    numeroTelefone = numeroTelefone.replace(/^(\d{2})(\d)/g, "($1) $2");
    numeroTelefone = numeroTelefone.replace(/(\d)(\d{4})$/, "$1-$2");

    return numeroTelefone;
  }

  public mascaraText(evento: KeyboardEvent) {

    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    this.dadosNovoContato.nome = v;
    // (<HTMLInputElement>evento.target).value = v
  }

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }


  ValidaTelefone(tle: any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle: any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {

      this.TelMovValVasil = true;
      this.TelMovVal = false;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovValVasil = false;
      this.TelMovVal = false;
      this.TelMovLimpa = true;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
      this.TelMovValVasil = false;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneComercial(tle: any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:9\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
    }
    else
      this.TelComVal = false
  }
  mascaraPesquisa(obj: any) {
    console.log(obj)
  }
  item: any = null;
  LimparCampos() {
    this.dadosNovoContato = []
    this.nome.markAsUntouched()
    this.tel.markAsUntouched()
    this.email.markAsUntouched()
    this.TelVal = false;
    this.TelMovValVasil = false;
    this.TelMovLimpa = false;
    this.TelMovVal = false;
    this.TelComVal = false;
  }
  nome = new FormControl('', [Validators.required, Validators.maxLength(11)])
  tel = new FormControl('', [Validators.required, Validators.maxLength(11)])
  email = new FormControl('', [Validators.required, Validators.email])

  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELAAGENDACONTATO.ERROCAMPO' :
      this.email.hasError('email') ? 'TELAAGENDACONTATO.ERROEMAIL' :
        '';

  }
  getErrorMessagetel() {
    return this.tel.hasError('required') ? 'TELAAGENDACONTATO.ERROCAMPO' :
      this.tel.hasError('Telefone') ? 'TELAAGENDACONTATO.ERRONAOEVALIDO' :
        '';

  }
  getErrorMessagenome() {
    return this.nome.hasError('required') ? 'TELAAGENDACONTATO.ERROCAMPO' :
      this.nome.hasError('Nome') ? 'TELAAGENDACONTATO.ERRONAOEVALIDO' :
        '';

  }

  ValidaCampos(): boolean {
    this.submitErro = false;
    this.nome.markAsTouched();
    this.tel.markAsTouched();
    this.email.markAsTouched();
    const { nome, email, telefoneMovel } = this.dadosNovoContato;

    if (!nome || !email || !telefoneMovel ||
      this.email.invalid || this.TelMovVal ||
      this.TelVal || this.TelComVal) {
      this.submitErro = true;
      if (this.TelMovVal !== true && this.TelMovVal !== false || this.TelMovLimpa === false) {
        this.TelMovValVasil = true;
        this.TelMovLimpa = true;
      }
      return false;
    }
    return true;
  }


  NovoCont() {
    this.LimparCampos()
    this.ngxSmartModalService.getModal('ModalNovoContato').open();
  }

  valorDelet(idContato: any) {
    this.idDelet = idContato;
    this.ngxSmartModalService.getModal('excluirItem').open();

  }

  ExcluirContato() {
    try {
      //retirar id
      this.contatoService.inativarContato(this.idDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe(() => {
        this.idDelet = null
        this.ngxSmartModalService.getModal('excluirItem').close();
        this.ngxSmartModalService.getModal('ModalNovoContato').close();

        this.CarregaTable();
        this.spinner.hide();
      }
        , err => {
          console.error(err)
          this.spinner.hide();
        })
    } catch (error) {
      console.error(error)
      this.spinner.hide();
    }
  }


  salvarCont() {
    this.dadosNovoContato.flgInativo = false
    this.ValidaCampos()
    if (this.submitErro) {
      this.snackBar.falhaSnackbar("Erro nos campos selecionados");
      return;
    }

    var cont = new Contato()
    if (this.dadosNovoContato.idcontato)
      cont.idContato = this.dadosNovoContato.idcontato
    cont.flgInativo = this.dadosNovoContato.flgInativo;
    cont.nome = this.dadosNovoContato.nome;
    cont.telefone = this.NaoEditado(this.dadosNovoContato.telefone);
    cont.telefoneComercial = this.NaoEditado(this.dadosNovoContato.telefoneMovel);
    cont.telefoneMovel = this.NaoEditado(this.dadosNovoContato.telefoneCom);
    cont.email = this.dadosNovoContato.email;
    cont.site = this.dadosNovoContato.Site;
    cont.dtaCadastro = new Date();
    cont.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;
    cont.idClinica = this.usuarioLogadoService.getIdUltimaClinica()!;

    this.contatoService.salvarContato(cont).subscribe(() => {
      this.LimparCampos();
      this.CarregaTable();
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })


  }

  EdicaoCont(id: any) {
    if (id != undefined) {
      var dados = this.DadosTab.filter((c: any) => c.idcontato == id)

      this.Edcao = true
      this.dadosNovoContato.nome = dados[0].nome;
      this.dadosNovoContato.email = dados[0].email;
      this.dadosNovoContato.idcontato = dados[0].idcontato;

      this.dadosNovoContato.telefone = dados[0].tel;
      if (this.dadosNovoContato.telefone != null && this.dadosNovoContato.telefone != '' && this.dadosNovoContato.telefone != undefined) {
        this.dadosNovoContato.telefone = this.dadosNovoContato.telefone.replace(/\D/g, "");
        this.dadosNovoContato.telefone = this.dadosNovoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.dadosNovoContato.telefone = this.dadosNovoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.dadosNovoContato.telefoneMovel = dados[0].telMovel
      if (this.dadosNovoContato.telefoneMovel != null && this.dadosNovoContato.telefoneMovel != '' && this.dadosNovoContato.telefoneMovel != undefined) {
        this.dadosNovoContato.telefoneMovel = this.dadosNovoContato.telefoneMovel.replace(/\D/g, "");
        this.dadosNovoContato.telefoneMovel = this.dadosNovoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.dadosNovoContato.telefoneMovel = this.dadosNovoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.dadosNovoContato.telefoneCom = dados[0].telCom
      if (this.dadosNovoContato.telefoneCom != null && this.dadosNovoContato.telefoneCom != '' && this.dadosNovoContato.telefoneCom != undefined) {
        this.dadosNovoContato.telefoneCom = this.dadosNovoContato.telefoneCom.replace(/\D/g, "");
        this.dadosNovoContato.telefoneCom = this.dadosNovoContato.telefoneCom.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.dadosNovoContato.telefoneCom = this.dadosNovoContato.telefoneCom.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.dadosNovoContato.Site = dados[0].site

      this.ngxSmartModalService.getModal('ModalNovoContato').open();
    }

  }

  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTab[this.indexGlobal]['toggle']) {
        // this.toggle[this.indexGlobal] = false;
        this.DadosTab[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index: any) {

    if (this.indexGlobal != index) {
      // this.toggle[this.indexGlobal] = false;
      this.DadosTab[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    // this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTab[this.indexGlobal]['toggle'] = !this.DadosTab[this.indexGlobal]['toggle'];
  }

}
