var gulp = require('gulp');
var gzip = require('gulp-gzip');
var azure = require('gulp-azure-storage');

// gulp.task('compress', function() {
//   gulp.src(['./dist/**/*.*'])
//       .pipe(gzip({ append: false }))
//       .pipe(gulp.dest('./dist/compress'));
// });


// hOMOLOGAÇÃO
var devAzure = {
  account: 'testemedicinapravoce',
  key: 'GRqt2QIqHM1DK5LwisUHEVRaf/c5KRG40yCk7583Ln6MFNFEG/DQN0DbSM08/XSMDbEDiGuAD/Lf+AStHgKtFw==',
  container: '$web',
  contentEncoding: 'gzip'
};


gulp.task('gzip-dev-azure', function() {
	return gulp.src(['./dist/**/*.*'])
				.pipe(gzip({
					append: false,
					threshold: false
				}))
				.pipe(azure.upload({
					account: devAzure.account,
					key: devAzure.key,
					container: devAzure.container,
					contentSettings: {
						contentEncoding: devAzure.contentEncoding,
						cacheControl: 'public, max-age=108000'
					}
				}));
});

gulp.task('gzip-dev-index-azure', function() {
	return gulp.src(['./dist/index.html'])
				.pipe(azure.upload({
					account: devAzure.account,
					key: devAzure.key,
					container: devAzure.container,
					contentSettings: {
						cacheControl: 'public, max-age=108000'
					}
				}));
});

gulp.task('gzip-dev', gulp.series('gzip-dev-azure', 'gzip-dev-index-azure'));



var  PRODAzure = {
  account: 'bonecare',
  key: 'TtHiVcGFmJEui6wJEBS/UwDE/dGBUpUZdO95t0DL5myxorWTkFkcbULsOL7XfFIq10wjD+AnLtTe+AStrKaghg==',
  container: '$web',
  contentEncoding: 'gzip'
};


gulp.task('gzip-PRD-azure', function() {
	// return gulp.src(['./dist/avalia-ai-front/browser/**/*.*'])
  return gulp.src(['./dist/**/*.*'])
				.pipe(gzip({
					append: false,
					threshold: false
				}))
				.pipe(azure.upload({
					account: PRODAzure.account,
					key: PRODAzure.key,
					container: PRODAzure.container,
					contentSettings: {
						contentEncoding: PRODAzure.contentEncoding,
						cacheControl: 'public, max-age=108000'
					}
				}));
});

gulp.task('gzip-PRD-index-azure', function() {
	// return gulp.src(['./dist/avalia-ai-front/browser/index.html'])
  return gulp.src(['./dist/index.html'])
				.pipe(azure.upload({
					account: PRODAzure.account,
					key: PRODAzure.key,
					container: PRODAzure.container,
					contentSettings: {
						cacheControl: 'public, max-age=108000'
					}
				}));
});

gulp.task('gzip-PRD', gulp.series('gzip-PRD-azure', 'gzip-PRD-index-azure'));