import { Component, OnInit } from '@angular/core';
import '../../vendor/jitsi/external_api.js';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { WebcamModule } from 'ngx-webcam';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatInputModule } from '@angular/material/input';

declare var JitsiMeetExternalAPI: any;

@Component({
    selector: 'app-teste-conexao',
    templateUrl: './teste-conexao.component.html',
    styleUrls: ['./teste-conexao.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatSidenavModule,
      TranslateModule,
      WebcamModule
    ]
})
export class TesteConexaoComponent implements OnInit {

  constructor() { }

  domain: string = "meet.jit.si";
  options: any;
  api: any;
  // usuario: Usuario;
  isHubConnected: boolean = false;

  ngOnInit() {
  }





}
