<div class="container">
    <mat-card class="card-principal">
      <!-- CABEÇALHO -->
      <div class="header">
        <div class="header-icon">
          <span class="material-icons">post_add</span>
        </div>
        <h2 class="header-title">Cadastro de Salas</h2>
      </div>
  
      <!-- FILTROS -->
      <div class="filtros">
        <div class="busca-container">
          <mat-form-field appearance="outline" class="busca-field">
            <mat-label>Buscar</mat-label>
            <input matInput type="search" [(ngModel)]="pesquisaSala" (keyup.enter)="GetListaSala()">
            <button mat-icon-button matSuffix (click)="GetListaSala()" class="btn-busca">
              <mat-icon>search</mat-icon>
            </button>
          </mat-form-field>
        </div>
        
        <div class="adicionar-container">
          <button class="btn-adicionar" (click)="AdicionarSala()">
            <mat-icon>add</mat-icon>
            <span>Adicionar <PERSON></span>
          </button>
        </div>
      </div>
      <div class="toggles-container">
        <mat-slide-toggle [(ngModel)]="flgInativos" (change)="GetListaSala()" class="toggle-item">
          {{ 'TELAPESQUISAPACIENTES.INATIVOS' | translate }}
        </mat-slide-toggle>
      </div>
  
      <!-- LISTA DE SALAS -->
      <div class="lista-container">
        <div class="lista-scroll">
          <div class="sala-card" *ngFor="let Item of listaSalas">
            <!-- INFO DA SALA -->
            <div class="sala-info">
              <div class="info-item">
                <mat-icon>meeting_room</mat-icon>
                <span class="info-label">Sala:</span>
                <span class="info-value">{{Item.nome}}</span>
              </div>
              <div class="info-item">
                <mat-icon>tag</mat-icon>
                <span class="info-label">Número Sala:</span>
                <span class="info-value">{{Item.numero}}</span>
              </div>
            </div>
  
            <!-- AÇÕES -->
            <div class="sala-acoes">
              <button mat-icon-button matTooltip="Editar" (click)="editarSala(Item.idSala!)" *ngIf="!flgInativos">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button class="remove" matTooltip="Excluir" (click)="EditarStatusSala(Item)">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </div>
          
          <!-- MENSAGEM DE LISTA VAZIA -->
          <div class="lista-vazia" *ngIf="listaSalas.length === 0">
            <mat-icon>sentiment_very_dissatisfied</mat-icon>
            <p>Não foi encontrada nenhuma sala.</p>
          </div>
        </div>
      </div>
    </mat-card>
  </div>