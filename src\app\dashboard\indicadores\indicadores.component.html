<mat-card appearance="outlined" class="custom-card mat-body mother-div">

  <mat-card-title>
    <h4 class="TitleTutorial"> <b>Indicadores da Clínica</b> </h4>
  </mat-card-title>
  <mat-divider></mat-divider>

  <mat-card-content>



    <!-- <raw-chart style="width: 100%"  [chartData]="rawChartData" [dynamicResize]="true"
      [formatter]="rawFormatter"></raw-chart> -->


    <div class="row" *ngIf="tipoUsuario!= 'Paciente' && !flgGraficoZero">
      <div class="col-md-12 col-12" style="justify-content: center;display: flex; margin-top: 0; margin-bottom: -3%;">
        <!-- <ngx-charts-pie-grid [scheme]="colorScheme" [results]="single" [view]="view" [designatedTotal]="totalConsultas">
        </ngx-charts-pie-grid> -->
      </div>



      <div class="col-md-12 col-sm-6 col-sm-4 margem-estrela">

        <div class=" col-md-2 col-md-2  btns-aval aval-gold">

          <div class="div-gold-text">
            <small class="number-consul">
              <b style="color: #D4AF37;">{{dadosGold}}</b> Avaliações Gold
            </small>
          </div>

        </div>

        <div class="col-md-2 col-md-2  btns-aval aval-prata">

          <div class="div-prata-text">
            <small class="number-consul">
              <b style="color: #b4b4b4;">{{dadosSilver}}</b> Avaliações Prata
            </small>
          </div>

        </div>

        <div class="col-md-2 col-md-2  btns-aval aval-bronze">

          <div class="div-bronze-text">
            <small class="number-consul">
              <b style="color: #a04b06;">{{dadosBronze}}</b> Avaliações bronze
            </small>
          </div>

        </div>
      </div>

    </div>


    <div class="btns-graficos col-md-12">
      <div class="col-md-12" style="display: flex; justify-content: center; margin: 5% auto;">
        <div class="col-md-2"></div>
        <div class="col-md-3 row div-info-graficos" [ngClass]="{'btnConsul' : btnConsultas == true }"
          (click)="selecConsultas()">
          <small class="text-graficos" style="padding: 5px; justify-content: center; text-align: center;">
            Consultas
          </small>
        </div>


        <div class="col-md-1"></div>
        <div class="col-md-3 row div-info-graficos" [ngClass]="{'btnFinan' : btnFinancas == true }"
          (click)="selecFinancas()">
          <small class="text-graficos" style="padding: 5px; justify-content: center; text-align: center;">
            Finanças
          </small>
        </div>
        <div class="col-md-2"></div>
      </div>
    </div>


    <div class="row">
      <div class="btn-group" style="margin-left: 3%;" *ngIf='btnFinancas'>

        <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
          <mat-button-toggle value="atual" (click)="decrement()"
            style="background-color:#007bff;color:white;margin-top: -10px">
            <span class=" fa fa-chevron-left"> </span>
          </mat-button-toggle>
          <mat-button-toggle value="proximo"
            style="border-left: none;background-color:#007bff;color:white;margin-top: -10px ">
            <span> {{ DataGrafico | calendarDate:(viewMes + 'ViewTitle'): locale}}</span>
          </mat-button-toggle>
          <mat-button-toggle value="proximo" (click)="increment()"
            style="border-left: none;background-color:#007bff;color:white;margin-top: -10px ">
            <span class="fa fa-chevron-right"> </span>
          </mat-button-toggle>
        </mat-button-toggle-group>

      </div>
      <div class="col-md-12 col-sm-12 " *ngIf="btnFinancas">
        <!-- <h4 class="col-md-12 col-sm-12">{{nomeMesAnterior}}</h4> -->



        <!-- <raw-chart *ngIf='!graficoVasil' style="width: 100%" [chartData]="rawChartData" [dynamicResize]="true" [formatter]="rawFormatter">
        </raw-chart> -->


        <h1 *ngIf='graficoVasil' style='text-align: center;'>Não há registro nesse mês! </h1>
      </div>

      <!-- <div class="btn-group" *ngIf="graficoConsultaMesesVazio==false && btnConsultas">

            <mat-button-toggle-group name="fontStyle" aria-label="Font Style" style="height:30px; ">
                <mat-button-toggle value="atual" (click)="decrementMeses()"
                    style="background-color:#007bff;color:white;margin-top: -10px">
                    <span class=" fa fa-chevron-left"> </span>
                </mat-button-toggle>
                <mat-button-toggle value="proximo" (click)="incrementMeses()"
                    style="border-left: none;background-color:#007bff;color:white;margin-top: -10px ">
                    <span class="fa fa-chevron-right"> </span>
                </mat-button-toggle>
            </mat-button-toggle-group>

        </div> -->
      <div class="col-md-12 col-sm-12 " *ngIf="graficoConsultaMesesVazio==false && btnConsultas">




        <!-- <google-chart class="col-md-12 col-sm-12 " style="width: 100%" [dynamicResize]="true" [title]="chart.title"
          [type]="chart.type" [data]="chart.data" [columnNames]="chart.columnNames" [options]="chart.options">
        </google-chart> -->
      </div>
    </div>



    <div class="row justify-content-center no-mobhalf">

      <!-- class="dashcard-desktop text-center  flip-container consultas icon-pulse basic-pulses" -->
      <!-- [className]="alerta == true ? 'dashcard-desktop text-center consultas flip-container basic-pulses' : 'basic-pulses dashcard-desktop text-center consultas flip-container'" -->

      <!-- <div class="col-md-12 col-12 row">
              <div class="col-md-3 col-12 pmob center">
                  <div class="col-md-12 col-12 Card" style="text-align: center; cursor: pointer;"
                      (click)="Irconsulta()">
                      <mat-icon class="dash-icon">calendar_today</mat-icon>
                      <h3>Consultas</h3>
                      <label>Acesse para verificar suas consultas.</label>

                  </div>
              </div>

              <div class="col-md-3 col-12 pmob center">
                  <div class="col-md-12 col-12 Card" (click)="Perfil();" style="text-align: center; cursor: pointer;">
                      <mat-icon class="dash-icon">person</mat-icon>
                      <h3>Perfil</h3>
                      <label>Acesse para gerar os relatorios da clinica.</label>

                  </div>
              </div>

              <div class="col-md-3 col-12 pmob center">
                  <div class="col-md-12 col-12 Card" (click)="Agenda()" style="text-align: center; cursor: pointer;">
                      <mat-icon class="dash-icon">alarm_add</mat-icon>
                      <h3>Agendamento</h3>
                      <label>Agendamentos para os médicos.</label>

                  </div>
              </div>
          </div> -->

    </div>
  </mat-card-content>
</mat-card>
