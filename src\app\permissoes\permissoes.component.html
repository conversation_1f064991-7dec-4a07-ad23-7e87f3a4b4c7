<mat-card appearance="outlined" class="main mother-card" style="margin-left:15px;">

    <mat-card-content>

        <div class="row pad-form">
            <div class="col-md-2 col-sm-4 col-xs-12  text-left header spacer-content botao-m">
                <button mat-stroked-button class="header-button flag-right" onclick='history.go(-1)'>
                    <mat-icon style="height: unset!important; margin-right: 9px;">arrow_back</mat-icon>
                    voltar
                </button>
            </div>
            <div class="col-md-8 col-sm-8 col-xs-4 text-center  spacer-content mt-3" style="margin: 0px !important;">
                <h4 class="bold">Perfil <PERSON></h4>
            </div>
            <!-- <div class="col-md-2 col-sm-2 no-mobile ">
                <button mat-raised-button class="input-align btn-primary f-r" [routerLink]="['/permissao']">
                    <mat-icon style="color:white;">add</mat-icon> <a
                        class="ad-pp "><PERSON><PERSON><PERSON><PERSON></a>
                </button>
            </div> -->



            <div class="col-md-6 col-sm-12 col-xs-12 " style="margin-top: 15px;">
                <input class="form-control col-md-12" placeholder="Nome do Perfil" type="text" name="Nome" (blur)="ValidaNomePerfil()" [(ngModel)]="nomePerfil" id="Nome">
                <mat-error *ngIf="campoNomeValido==false">Este campo deve ser preenchido</mat-error>
            </div>

            <div class="col-md-6 text-right" style="margin-top: 15px;">
                <button class="btn-primary  " mat-raised-button style="color:white;" (click)="btnSalvar()" style="margin-right: 5%;     width: 90px;">
                    Salvar
                </button>
                <button class="btn-primary  " mat-raised-button style="color:white;" (click)="LimparTabela()" style="margin-right: 0%;     width: 90px;">
                    Limpar
                </button>
            </div>

        </div>



        <mat-table #table [dataSource]="dataSource" class="gridPerfil" style="margin-top: 15px;">

            <!-- Checkbox Column -->
            <!-- <ng-container matColumnDef="select">
    <th mat-header-cell *matHeaderCellDef>   
    </th>
    <td mat-cell *matCellDef="let row">
      <mat-checkbox (click)="$event.stopPropagation()"
                    (change)="$event ? selection.toggle(row) : null"
                    [checked]="selection.isSelected(row)">
      </mat-checkbox>
    </td>
  </ng-container> -->
            <!--- Note that these columns can be defined in any order.
                  The actual rendered columns are set as a property on the row definition" -->
            <!-- Weight Column -->
            <ng-container matColumnDef="DesCategoria">
                <mat-header-cell *matHeaderCellDef style="width: 26%;"> Categoria </mat-header-cell>
                <mat-cell *matCellDef="let element" style="width: 26%;"> {{element.DesCategoria}} </mat-cell>
            </ng-container>

            <!-- Symbol Column -->
            <ng-container matColumnDef="Permissao_Categoria">
                <mat-header-cell *matHeaderCellDef style="width: 26%;"> Permissão </mat-header-cell>
                <mat-cell *matCellDef="let element" style="width: 26%;"> {{element.Permissao_Categoria}} </mat-cell>
            </ng-container>

            <!-- Position Column -->
            <ng-container matColumnDef="flgVisualizar">
                <mat-header-cell *matHeaderCellDef class="perfilCelulaFlg">Visualizar</mat-header-cell>
                <mat-cell *matCellDef="let element" class="perfilCelulaFlg">
                    <mat-checkbox [(ngModel)]="element.flgVisualizar"></mat-checkbox>
                </mat-cell>
            </ng-container>

            <!-- Position Column -->
            <ng-container matColumnDef="flgIncluir">
                <mat-header-cell *matHeaderCellDef class="perfilCelulaFlg">Incluir</mat-header-cell>
                <mat-cell *matCellDef="let element" class="perfilCelulaFlg">
                    <mat-checkbox [(ngModel)]="element.flgIncluir"></mat-checkbox>
                </mat-cell>
            </ng-container>

            <!-- Position Column -->
            <ng-container matColumnDef="flgEditar">
                <mat-header-cell *matHeaderCellDef class="perfilCelulaFlg">Editar</mat-header-cell>
                <mat-cell *matCellDef="let element" class="perfilCelulaFlg">
                    <mat-checkbox [(ngModel)]="element.flgEditar"></mat-checkbox>
                </mat-cell>
            </ng-container>

            <!-- Position Column -->
            <ng-container matColumnDef="flgInativar">
                <mat-header-cell *matHeaderCellDef class="perfilCelulaFlg" style="padding: 0px;">Inativar</mat-header-cell>
                <mat-cell *matCellDef="let element" class="perfilCelulaFlg" style="padding: 0px;">
                    <mat-checkbox [(ngModel)]="element.flgInativar"></mat-checkbox>
                </mat-cell>
            </ng-container>

            <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
            <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>


        </mat-table>


        <!-- <div class="card col-md-12 col-sm-12" style="border:unset;padding: unset;">
            <div class="col-md-12 col-sm-12 col-xs-12  " style="max-height: 400px; overflow: auto;">
                <div class="row" style="margin-top: 50px"></div>


                <table *ngFor="let item of this.dadosPermissao; let i = index;" class="table no-mobile" id="DatatableCliente" style="margin-top: 10px; margin-bottom: 10px;">
                    <thead style="display: none;">
                        <tr>
                            <th class="" style="width: 42%">{{ 'TELAAGENDACONTATO.NOME' | translate }}</th>
                            <th class="" style="width: 25.6%;">
                                {{ 'TELAAGENDACONTATO.TIPOUSUARIO' | translate }}
                            </th>
                            <th class="text-center" style="width:10%">
                                {{ 'TELAAGENDACONTATO.ACOES' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="card_table" name="tablePermissoes">
                            <td style="display: none">{{item.permissao.idPermissao}}</td>

                            <td id="categoria" style="width: 28%">
                                Categoria: {{item.categoria}}
                            </td>
                            <td id="permissaoCategoria" class="" style="width: 40%">
                                Permissao Categoria: {{item.permissao.nomePermissao}}
                            </td>
                            <td class="" id="cbVidu" style="width: 8%">
                                <mat-checkbox id="Visualizar{{i}}" ><small>Visualizar</small>
                                </mat-checkbox>
                            </td>
                            <td class="" id="cbInclu" style="width: 8%">
                                <mat-checkbox id="Incluir{{i}}" ><small>Incluir</small>
                                </mat-checkbox>
                            </td>
                            <td class="" id="cbEditar" style="width: 8%">
                                <mat-checkbox id="Alterar{{i}}" ><small>Editar</small>
                                </mat-checkbox>
                            </td>
                            <td class="" id="cbInativar" style="width: 8%">
                                <mat-checkbox id="Excluir{{i}}" ><small>Inativar</small>
                                </mat-checkbox>
                            </td>

                        </tr>

                    </tbody>
                </table>
            </div>
        </div> -->


    </mat-card-content>



</mat-card>