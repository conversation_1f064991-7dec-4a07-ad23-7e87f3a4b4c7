<mat-card appearance="outlined" class="main" style="margin-left:15px;">

    <mat-card-title class="spacer-card" style="padding: unset">

        <div class="col-md-12" style="padding: unset; margin-top: 15px;">

            <mat-icon class="icon-title">payment</mat-icon>

            <a class="title-content fonte-tamanho title-contas-apagar">{{ 'TELAFINANCAS.CONTASPAGAR' | translate }}</a>

        </div>

    </mat-card-title>

    <mat-divider class="p-t-20"></mat-divider>

    <br />
    <div>
        <div class="row p-3">

            <div class="col-md-2 col-sm-12 col-xs-6 carregar-arquivo h-auto d-flow-root text-center"
                style="padding-left:16px !important; border: 0px dotted #999;">
                <!-- (dragover)="$event.preventDefault()" (drop)="fileuploadesquerdo($event)" -->
                <i class="fas fa-cloud-upload-alt fa-fw fa-4x" style="  cursor: grabbing ; color: #ccc;"></i>
                <p style="font-size:12px; font-weight:500; line-height:12px">

                    Utilize esta área para importar comprovantes arrastando e soltando seu arquivo aqui
                    <br>
                    <br> {{ 'TELASTREAMING.OU' | translate }} </p>

                <label for="file" class="btn btn-primary" style=" cursor: pointer; font-size:10px!important;">
                    Anexar Comprovante</label>
                <input type="file" style="width:100%;" id="file" accept=".xls,.xlsx,.pdf" style="display: none;" />
            </div>

            <div class=" col-md-10 col-sm-12 col-xs-6 div-infos-contas">

                <mat-form-field class="col-md-12">
                    <input matInput placeholder="{{ 'TELAFINANCAS.DESCRICAOCONTA' | translate }}" required name="Desc"
                        [(ngModel)]="objContasPagar.DesConta">
                        <span *ngIf="DescricaoVazia"  class="error">  Esse campo deve ser preenchido </span>
                </mat-form-field>

                <input-date [(ngModel)]="objContasPagar.DtaVencimento"
                    placeholder="{{ 'TELAFINANCAS.DATAVENCIMENTO' | translate }}" name="datae"></input-date>

                <!-- <mat-form-field class="col-md-3" appearance="legacy"> -->
                <!-- <input matInput placeholder="{{ 'TELAFINANCAS.DATAVENCIMENTO' | translate }}"
                        formControlName="DataVcm" [(ngModel)]="objContasPagar.DtaVencimento | dateInputFormat" type="date"
                        max="2100-01-01" min="1900-01-01" maxlength="10">

                    <mat-error *ngIf="objContasPagarForm.get ('DataCad').errors?.dataInorreta">
                        data inválida</mat-error> -->
                <!-- </mat-form-field> -->
                <!-- <span class="aviso-span text-center" *ngIf="DtaErrado == true"
                    style="font-size: 65%;color: #f44336;font-weight: 600;display:flex; margin-top: 5px;">{{ 'TELACADASTROMEDICO.ERRODATA' | translate }}</span> -->

                <!-- <mat-form-field class="col-md-3" appearance="legacy">
                    <input matInput placeholder="{{ 'TELAFINANCAS.DATAVENCIMENTO' | translate }}"
                        [(ngModel)]="objContasPagar.dataVencimento" type="date" max="2100-01-01" min="1900-01-01" maxlength="10"> -->

                <!-- <mat-error *ngIf="objContasPagarForm.get('Data').errors?.required"> {{ 'TELACADASTROMEDICO.ERRODATA' | translate }}</mat-error> -->
                <!-- <span class="aviso-span text-center" *ngIf="DtaErrado == true" style="font-size: 65%;color: #f44336;font-weight: 600;display:flex; margin-top: 5px;">{{ 'TELACADASTROMEDICO.ERRODATA' | translate }}</span> -->
                <!-- </mat-form-field> -->
                <!-- 
                <mat-form-field class="col-md-3" appearance="legacy">
                    <input matInput placeholder="{{ 'TELAFINANCAS.DATABAIXA' | translate }}" name="DataBx"
                        [(ngModel)]="objContasPagar.DtaBaixa" max="2100-01-01" min="1900-01-01" mask="00/00/0000"
                        maxlength="10">
                    <mat-error *ngIf="objContasPagarForm.get ('DataCad').errors?.dataInorreta">
                        data inválida</mat-error>
                </mat-form-field>
                 -->
                <input-date [(ngModel)]="objContasPagar.DtaBaixa"
                    placeholder="{{ 'TELAFINANCAS.DATABAIXA' | translate }}" name="datae"></input-date>

                <!-- <mat-form-field appearance="legacy" class="col-md-3">
            <ng-select [items]=" " style="font-size: 12px;" placeholder="{{ 'TELAFINANCAS.FORMAPAGAMENTO' | translate }} " name="Pagamento" bindValue="DadosPagamento.id" bindLabel="" notFoundText="preencha uma forma de pagamento" [selectOnTab]="true" (focus)="carregaFormaPagamento()">
            </ng-select>
        </mat-form-field> -->

                <!-- <mat-form-field class="col-md-3" appearance="legacy">
            <mat-label>{{ 'TELAFINANCAS.FORMAPAGAMENTO' | translate }}</mat-label>
            <mat-select (change)="alterarFormaPagamentoNovo()" id="selectFomaPagamento">
                <mat-option *ngFor="let item of listaFormaPagamento" value="item.id">{{ item.nome }}</mat-option>
            </mat-select>
        </mat-form-field> -->

                <mat-form-field class="col-md-3 input-spacing">
                    <mat-label>Forma de Pagamento</mat-label>
                    <select matNativeControl id="selectFomaPagamento" required name="formaPagamento"
                        [(ngModel)]="objContasPagar.IdTipoPagamento" class="col-md-3" name="cliente">
                        <option></option>
                        <option *ngFor="let item of listaFormaPagamento" value="{{ item.idTipoPagamento }}">
                            {{ item.desTipoPagamento }}</option>

                    </select>
                    <span *ngIf="DescricaoVazia"  class="error">  Escolha uma forma de pagamento</span>
                </mat-form-field>

                <mat-form-field class="col-md-3 input-spacing">
                    <mat-label>{{ 'TELAFINANCAS.PARCELAS' | translate }}</mat-label>
                    <mat-select name="NumParcelas" [disabled]="objContasPagar.IdTipoPagamento != 3"
                        [(ngModel)]="objContasPagar.NumParcelas">
                        <mat-option value="option"></mat-option>
                        <mat-option *ngFor="let item of listaParcelas;let i = index" [value]="item.valor">
                            {{item.descricao}}</mat-option>

                    </mat-select>
                </mat-form-field>

                <mat-form-field class="col-md-12">
                    <input matInput placeholder="{{ 'TELAFINANCAS.OBSERVACOESCONTA' | translate }}" name="ObsConta"
                        [(ngModel)]="objContasPagar.ObsConta">
                </mat-form-field>


                <mat-form-field class="col-md-3">
                    <input matInput placeholder="{{ 'TELAFINANCAS.FORNECEDOR' | translate }}" name='fornecedor'
                        [(ngModel)]="objContasPagar.IdFornecedor">
                </mat-form-field>
<!-- 
                <mat-form-field class="col-md-3" appearance="legacy">
                    <input matInput name="Valor" placeholder="{{ 'TELAFINANCAS.VALOR' | translate }}" required
                        [(ngModel)]="objContasPagar.VlrConta" currencyMask
                        [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }">
                        <span *ngIf="DescricaoVazia && objContasPagar.VlrConta == '' "  class="error"> Preencha um valor </span>
                </mat-form-field> -->

                <mat-form-field class="col-md-3">
                    <input matInput name="NotaFiscal" [(ngModel)]="objContasPagar.NotaFiscal"
                        placeholder="{{ 'TELAFINANCAS.NOTAFISCAL' | translate }}">
                </mat-form-field>

                <mat-form-field class="col-md-3">
                    <input matInput placeholder="{{ 'TELAFINANCAS.SERIENOTAFISCAL' | translate }}"
                        name="SerieNotaFiscal" [(ngModel)]="objContasPagar.SerieNotaFiscal">
                </mat-form-field>

                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4 div-btns-contas" align="right">
                    <button class="btn-primary " mat-raised-button style="color:white;" (click)="LimparCampos()"
                        style="margin-right: 2%;">
                        <mat-icon>clear</mat-icon> <span
                            class="legenda">{{ 'TELACADASTROMEDICO.LIMPAR' | translate }}</span>
                    </button>

                    <button class="btn-primary " mat-raised-button
                        style="color:white;margin-top: 10px;margin-bottom: 10px;" (click)="Submit()">
                        <mat-icon>save</mat-icon><span class="legenda">
                            {{ 'TELACADASTROMEDICO.SALVAR' | translate }}</span>
                    </button>
                </div>

            </div>

        </div>

    </div>

    <div class="anon_anot" *ngIf="DadosContasPagar!.length>0" style="overflow-x: scroll;">

        <div class="col-md-12 div-itenscad" style="padding: unset">

            <mat-icon class="icon-title">history</mat-icon>

            <a class="title-content fonte-tamanho">Itens cadastrados </a>

        </div>
        <br />

        <table class="table tabela-contas">
            <thead>
                <tr>
                    <td style="width: 40%;     border: unset;">
                        Conta
                    </td>

                    <td class="text-center" style="width: 20%; border: unset;">
                        Vencimento
                    </td>

                    <td class="text-center" style="width: 20%; border: unset;">
                        Valor
                    </td>

                    <td class="text-center" style="width: 20%; border: unset;">
                        Ações
                    </td>

                </tr>
            </thead>
            <tbody *ngFor="let item of DadosContasPagar">
                <tr>
                    <td>
                        {{ item.desConta }}
                    </td>

                    <td>
                        {{item.dtaVencimento | date: 'dd/MM/yyyy HH:mm'}}
                    </td>
                    <td>
                        {{ item.vlrConta }}
                    </td>

                    <td class="text-center tabela-acoes">
                        <i style="vertical-align: -webkit-baseline-middle; cursor: pointer; font-size: 20px"
                            class="fa fa-download text-left icone" title="Download arquivo"></i>

                        <i style="vertical-align: -webkit-baseline-middle; margin-left: 25px; cursor: pointer; font-size: 20px"
                            class="fa fa-edit text-left icone" title="Editar conta"
                            (click)="CarregarEdicaoContaPagar(item.idContasPagar)"></i>

                        <i style="vertical-align: -webkit-baseline-middle; margin-left: 25px; cursor: pointer; font-size: 20px"
                            class="fa fa-trash text-left icone" title="Excluir"
                            (click)="AbrirModalExclusao(item.idContasPagar)"></i>
                    </td>

                </tr>
            </tbody>
        </table>
    </div>

    <div style="border:1px solid #ddd;" *ngIf="DadosContasPagar?.length==0">
        <div style="padding: 15px;" class="text-center">

            <b>Não há registro</b>
        </div>

    </div>

</mat-card>
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal emailmodal">
    <div class="modal-header p-t-20 p-b-20">
        <h1 class="little-title fw-700">
            <mat-icon style="color:red">warning</mat-icon>
            Deseja realmente excluir essa conta?
        </h1>
    </div>

    <mat-divider></mat-divider>
    <div class="row-button text-right " style="padding: 10px 0px;">
        <button mat-flat-button class="input-align btn btn-danger"
            (click)="ngxSmartModalService.getModal('excluirItem').close()">
            Não </button>
        <button mat-flat-button class="input-align btn btn-success" (click)="DeletarContaPagar()">
            Sim </button>
    </div>
</ngx-smart-modal>