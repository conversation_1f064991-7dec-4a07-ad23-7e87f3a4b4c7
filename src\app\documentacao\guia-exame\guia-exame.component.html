<!-- Modal Guia de Exame -->
<ngx-smart-modal #GuiaExame identifier="GuiaExame" [dismissable]="false" (onClose)="limparCampoGuiaExame()"
    customClass="nsm-centered modern-modal health-modal emailmodal">
    <div class="modal-container">
        <header class="modal-header">
            <div class="modal-title-container">
                <h3 class="modal-title">GUIA DE SERVIÇO PROFISSIONAL/ SERVIÇO AUXILIAR DE DIAGNÓSTICO E TERAPIA SP/SADT</h3>
            </div>
        </header>
        
        <main class="modal-container">
            <div class="form-grid">
                <!-- Primeira linha -->
                <div class="form-column">
                    <div class="form-field">
                        <ng-select [items]="DadosPacientes"
                            placeholder="11 - Nome Paciente *"
                            bindLabel="nome" bindValue="nome" name="medicos" [selectOnTab]="true"
                            [(ngModel)]="objGuiaExames.Nome11" aria-required="true" class="custom-select">
                        </ng-select>
                        <div class="error-message" *ngIf="NomePaciente == true">
                            {{getErrorMessageNomeProSolicitante() | translate }}
                        </div>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput maxlength="35" name="CodigoCnes15" autocomplete="off"
                                placeholder="15 - Código CNES" required 
                                (keyup)="mascaraNumeros($event)" (keypress)="mascaraNumeros($event)" 
                                [(ngModel)]="objGuiaExames.CodigoCnes15">
                            <mat-error *ngIf="CodCNES.invalid">{{getErrorMessageCodCNES() | translate }}</mat-error>
                        </mat-form-field>
                    </div>
                </div>
                
                <!-- Segunda linha -->
                <div class="form-column">
                    <div class="form-field">
                        <ng-select [items]="ListaMedicos"
                            placeholder="16 - Nome do Profissional Solicitante *"
                            bindLabel="nomeMedico" bindValue="nomeMedico" name="medicos" [selectOnTab]="true"
                            [(ngModel)]="objGuiaExames.NomeProfissionalSolicitante16" aria-required="true" 
                            class="custom-select">
                        </ng-select>
                        <div class="error-message" *ngIf="Nomemedico == true">
                            {{getErrorMessageNomeProSolicitante() | translate }}
                        </div>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput maxlength="100" name="ConselhoProfissional17" autocomplete="off"
                                placeholder="17 - Conselho Profissional" required 
                                (keyup)="mascaraText($event,'ConselhoProfissional17')"
                                (keypress)="mascaraText($event,'ConselhoProfissional17')" 
                                [(ngModel)]="objGuiaExames.ConselhoProfissional17">
                            <mat-error *ngIf="ConsProfi.invalid">{{getErrorMessageConsProfi() | translate }}</mat-error>
                        </mat-form-field>
                    </div>
                </div>
                
                <!-- Terceira linha -->
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput maxlength="35" name="NumeroConselho18" autocomplete="off"
                                placeholder="18 - Número no Conselho" required 
                                (keyup)="mascaraNumeros($event)" (keypress)="mascaraNumeros($event)" 
                                [(ngModel)]="objGuiaExames.NumeroConselho18">
                            <mat-error *ngIf="NumConse.invalid">{{getErrorMessageNumConse() | translate }}</mat-error>
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <ng-select [items]="dadosUF"
                            placeholder="19 - UF"
                            bindLabel="siglasUf" bindValue="siglasUf" name="UF" [selectOnTab]="true" required
                            notFoundText="{{'TELACADASTROUSUARIO.UFNAOENCONTRADA' | translate}}"
                            [(ngModel)]="objGuiaExames.UF19" (change)="ValidaUf()" class="custom-select">
                        </ng-select>
                        <div class="error-message" *ngIf="ufInvalido == true">
                            Esse campo precisa ser preenchido
                        </div>
                    </div>
                </div>
                
                <!-- Quarta linha -->
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput maxlength="35" name="CodigoCboS20" autocomplete="off"
                                placeholder="20 - Código CBOS" required 
                                (keyup)="mascaraNumeros($event)" (keypress)="mascaraNumeros($event)" 
                                [(ngModel)]="objGuiaExames.CodigoCboS20">
                            <mat-error *ngIf="CodCBOS.invalid">{{getErrorMessageCodCBOS() | translate }}</mat-error>
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput maxlength="100" name="IndicacaoClinica24" autocomplete="off"
                                placeholder="24 - Indicação Clinica" required 
                                (keyup)="mascaraText($event, 'IndicacaoClinica24')"
                                (keypress)="mascaraText($event, 'IndicacaoClinica24')" 
                                [(ngModel)]="objGuiaExames.IndicacaoClinica24">
                            <mat-error *ngIf="IndicacaoClin.invalid">
                                {{getErrorMessageIndicacaoClin() | translate }}
                            </mat-error>
                        </mat-form-field>
                    </div>
                </div>
                
                <!-- Quinta linha -->
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput maxlength="35" name="CodigoProcedimento26_1" autocomplete="off"
                                placeholder="26 - Código do Procedimento -1" required 
                                (keyup)="mascaraNumeros($event)" (keypress)="mascaraNumeros($event)" 
                                [(ngModel)]="objGuiaExames.CodigoProcedimento26_1">
                            <mat-error *ngIf="CodProced.invalid">{{getErrorMessageCodProced() | translate }}</mat-error>
                        </mat-form-field>
                    </div>
                </div>
                
                <div class="form-column">
                    <div class="form-field">
                        <mat-form-field appearance="outline">
                            <input matInput name="DataAssinProcSerie86" autocomplete="off" required
                                placeholder="86 - Data da Assinatura do Solicitante" name="Data"
                                (keyup)="mascaraData($event)" (keypress)="mascaraData($event)"
                                (blur)="ValidaDta($any($event.target).value)" maxlength="10"
                                [(ngModel)]="objGuiaExames.DataAssinProcSerie86">
                            <mat-error *ngIf="DtaAssinatura.invalid">
                                {{getErrorMessageDtaAssinatura() | translate }}
                            </mat-error>
                        </mat-form-field>
                        <div class="error-message" *ngIf="dtaInvalida == true && !DtaAssinatura.invalid">
                            {{ 'TELACADASTROUSUARIO.DATAINVALIDA' | translate }}
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="modal-footer">
            <button class="action-button confirm-button" (click)="GerarGuiaExame()">
                <mat-icon>note_add</mat-icon>
                <span class="button-text">{{ 'TELADOCUMENTACAO.GERARESALVAR' | translate }}</span>
            </button>
            
            <button class="action-button reset-button" (click)="limparCampoGuiaExame()">
                <mat-icon>refresh</mat-icon>
                <span class="button-text">Limpar</span>
            </button>
        </footer>
    </div>
</ngx-smart-modal>