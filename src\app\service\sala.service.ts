import { ObjSala, SalaModelview } from './../model/salas';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { RetornoPadraoApi } from '../model/RetornoPadraoApi';

@Injectable({
  providedIn: 'root'
})
export class SalaService {

  constructor(
    private http: HttpClient
  ) { }

  public ListarSalas() {
    return this.http.get<ObjSala[]>(environment.apiEndpoint + '/sala/CarregaSalaMedico')
  }

  public CarregarSala(id: number) {
    let params = new HttpParams();
    params = params.append('idSala', id.toString());

    return this.http.get(environment.apiEndpoint + '/sala/CarregarSala', { params });
  }

  public AlterarStatusAtivoSala(id:any){
    let params = new HttpParams();
    params = params.append('idSala', id.toString());
    return this.http.get(environment.apiEndpoint + '/sala/AlterarStatusAtivoSala', { params });
  }
  
  public UpdateSala(ObjSala:any){
    return this.http.post(environment.apiEndpoint + '/sala/UpdateSala', ObjSala);
  }

  public SalvarSala(sala: SalaModelview){
    return this.http.post<RetornoPadraoApi>(environment.apiEndpoint + '/sala/SalvarSala', sala)
    .pipe(
      catchError((error) => {
        ;
        return throwError(() => error);
      })
    )
  }

  public GetDadosSala(idSala: number){
    let params = new HttpParams();
    params = params.append('idSala', idSala);
    return this.http.get<SalaModelview>(environment.apiEndpoint + '/sala/GetDadosSala', {params})
    .pipe(
      catchError((error) => {
        ;
        return throwError(() => error);
      })
    )
  }

  public GetListaSala(pesquisa: string, flgInativos: boolean = false) {
    let params = new HttpParams();
    params = params.append('pesquisa', pesquisa);
    params = params.append('flgInativos', flgInativos.toString());

    return this.http.get<SalaModelview[]>(environment.apiEndpoint + '/sala/GetListaSala', { params })
    .pipe(
      catchError((error) => {
        ;
        return throwError(() => error);
      })
    )
  }




}
