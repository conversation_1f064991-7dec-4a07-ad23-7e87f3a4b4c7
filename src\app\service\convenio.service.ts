

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Convenio } from '../model/convenio';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class ConvenioService {

    constructor(
        private http: HttpClient,
        private spinner : SpinnerService
    
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });




    public salvarConvenio(convenio: Convenio): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Convenio/', convenio);
    }



    public getGridConvenioInativos(inicio:any, fim:any, pesquisa:any,idUsuario:any,idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idUsuario', String(idUsuario));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Convenio/getGridConvenioInativos', { params });
    }

    public getGridConvenio(inicio:any, fim:any, pesquisa:any,idUsuario:any,idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idUsuario', String(idUsuario));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Convenio/getGridConvenio', { params });
    }
    public getConvenios(idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Convenio/getConvenios', { params });
    }

    public getConvenioEdit(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Convenio/GetConvenioEdcao/' + id);
    }


    public AtivarConvenio(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Convenio/AtivarConvenio/' + id + '/' + idUsuario);
    }

    public inativarConvenio(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Convenio/InativarConvenio/' + id + '/' + idUsuario);
    }
}