import { Injectable } from "@angular/core";
import { MatSnackBar as MatSnackBar, MatSnackBarConfig as MatSnackBarConfig, MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';


@Injectable({providedIn: 'root'})

export class AlertComponent {
    constructor(
        public snackBar: MatSnackBar,
    ){ }

  actionButtonLabel: string = 'Fechar';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';


  sucessoSnackbar(message:any, tempo = 0) {
    let config = new MatSnackBarConfig();
    config.verticalPosition = this.verticalPosition;
    config.horizontalPosition = this.horizontalPosition;

    if(tempo > 0)
      config.duration = tempo;
    else
      config.duration = this.setAutoHide ? this.autoHide : 0;

    config.panelClass = ['sucessoSnackbar'];
    return this.snackBar.open(message, this.action ? this.actionButtonLabel : undefined, config);
  }

  falhaSnackbar(message:any, tempo = 0) {
    let config = new MatSnackBarConfig();
    config.verticalPosition = this.verticalPosition;
    config.horizontalPosition = this.horizontalPosition;
    
    if(tempo > 0)
      config.duration = tempo;
    else
      config.duration = 10000; //10 segundos

    config.panelClass = ['falhaSnackbar'];
    return this.snackBar.open(message, this.action ? this.actionButtonLabel : undefined, config);
  }

  alertaSnackbar(message:any) {
    let config = new MatSnackBarConfig();
    config.verticalPosition = this.verticalPosition;
    config.horizontalPosition = this.horizontalPosition;
    config.duration = this.setAutoHide ? this.autoHide : 0;
    config.panelClass = ['alertaSnackbar'];
    return this.snackBar.open(message, this.action ? this.actionButtonLabel : undefined, config);
  }

  infoSnackbar(message: string, tempo = 0, hPosition?: MatSnackBarHorizontalPosition) {
    let config = new MatSnackBarConfig();
    config.verticalPosition = this.verticalPosition;
    config.horizontalPosition = hPosition ?? this.horizontalPosition;
    config.duration = this.setAutoHide ? this.autoHide : 0;
    config.panelClass = ['infoSnackbar'];

    if (tempo > 0)
      config.duration = tempo;
    else
      config.duration = this.setAutoHide ? this.autoHide : 0;

    return this.snackBar.open(message, 'OK', config);
  }
}