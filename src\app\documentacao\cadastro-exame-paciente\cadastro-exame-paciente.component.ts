import { UsuarioLogadoService } from "./../../auth/usuarioLogado.service";
import { Component, OnInit, Output } from "@angular/core";
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from "@angular/forms";
import {
  MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition,
  MatSnackBarVerticalPosition as MatSnackBarVerticalPosition,
} from "@angular/material/snack-bar";

import { ExamesService } from "src/app/service/exameService.service";
import { NgxSmartModalModule, NgxSmartModalService } from "ngx-smart-modal";
import { FadeIn } from "src/app/Util/Fadein.animation";
import { ExameClinica } from "src/app/model/exames";
import { SpinnerService } from "src/app/service/spinner.service";
import { AlertComponent } from "src/app/alert/alert.component";
import { CommonModule } from "@angular/common";
import { MatCardModule } from "@angular/material/card";
import { MatIcon } from "@angular/material/icon";
import { TranslateModule } from "@ngx-translate/core";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatDivider } from "@angular/material/divider";
import { MatInputModule } from "@angular/material/input";
import { TruncatePipe } from "src/app/Util/pipes/truncate.pipe";

@Component({
    selector: "app-cadastro-exame-paciente",
    templateUrl: "./cadastro-exame-paciente.component.html",
    styleUrls: ["./cadastro-exame-paciente.component.scss"],
    animations: [FadeIn],
    host: { "[@FadeIn]": "" },
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      NgxSmartModalModule,
      MatDivider,
      TruncatePipe
    ]
})
export class CadastroExamePacienteComponent implements OnInit {
  constructor(
    private spinner: SpinnerService,
    private usuarioLogadoService: UsuarioLogadoService,
    private examesService: ExamesService,
    // public snackBar: MatSnackBar,
    public ngxSmartModalService: NgxSmartModalService,
    private snackBarAlert: AlertComponent
  ) { }

  toggle:any = {};
  actionButtonLabel: string = "Fechar";
  salvoSucess: string = "Cadastro salvo com Sucesso. ✔";
  ErroSalvar: string = "Erro ao salvar!";
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = "right";
  verticalPosition: MatSnackBarVerticalPosition = "bottom";
  concordo?: boolean;
  concordomsg?: boolean;

  @Output() FadeIn?: string;
  // isOpen = false;
  showMessageError: boolean = false;
  Dados: any = [];
  posologia = "";
  // usuario: Usuario;
  DesItem: boolean = false;
  DadosClinicas: any;
  clinicas: any = new FormControl([]);
  clinicaVasil?: boolean;
  clinicaVal?: boolean;
  DadosTable: any = [];
  pesquisa = "";
  remedioDelet = 0;
  legenda = false;

  qtdRegistros = 10;
  bOcultaCarregaMais = false;

  ngOnInit() {
    // this.CarregaExames();
  }

  CarregaExames() {
    try {
      this.bOcultaCarregaMais = false;

      this.examesService
        .CarregaExamesGrid(
          0,
          this.qtdRegistros,
          this.pesquisa,
          this.usuarioLogadoService.getIdUltimaClinica()
        )
        .subscribe(
          (retorno) => {


            this.DadosTable = retorno;

            if (retorno.length < this.qtdRegistros)
              this.bOcultaCarregaMais = true;
            ;
            ;
            this.spinner.hide();
          },
          () => {
            // this.ErroCarregar(true)
            ;
            this.snackBarAlert.falhaSnackbar("Erro ao carregar!");
            this.spinner.hide();
          }
        );
    } catch (error) {
      ;
      this.snackBarAlert.falhaSnackbar("Erro ao carregar!");
      this.spinner.hide();
      // this.ErroCarregar(true)
    }
  }

  CarregarMais() {
    try {
      this.bOcultaCarregaMais = false;
      this.examesService
        .CarregaExamesGrid(
          this.DadosTable.length,
          this.qtdRegistros,
          this.pesquisa,
          this.usuarioLogadoService.getIdUltimaClinica()
        )
        .subscribe(
          (retorno) => {
            var dados = retorno;
            for (let index = 0; index < dados.length; index++) {
              this.DadosTable.push(dados[index]);
            }
            if (retorno.length < this.qtdRegistros)
              this.bOcultaCarregaMais = true;
            ;
            ;
            this.spinner.hide();
          },
          () => {
            // this.ErroCarregar(true)
            this.snackBarAlert.falhaSnackbar("Erro no retorno da especialidade!");
            ;
            this.spinner.hide();
          }
        );
    } catch (error) {
      ;
      this.spinner.hide();
    }
  }

  LimparCampos() {
    this.Dados = [];
    this.Descricao.markAsUntouched();
  }

  public Submit() {
    try {
      this.validarCampos();
      if (this.showMessageError) {
        return;
      }

      var objexame = new ExameClinica();
      objexame.CodExame = this.Dados.cod;
      objexame.DesExame = this.Dados.des;
      objexame.IdClinica = this.usuarioLogadoService.getIdUltimaClinica()!
      if (this.Dados.idExame) objexame.IdExameClinica = this.Dados.idExame;
      else
        objexame.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;

      this.examesService.SalvarExame(objexame).subscribe(
        () => {
          this.CarregaExames();
          this.LimparCampos();
          this.snackBarAlert.sucessoSnackbar("Salvo com Sucesso!");
          this.spinner.hide();
        },
        () => {
          this.snackBarAlert.falhaSnackbar("Erro ao salvar!");
          this.spinner.hide();
        }
      );
    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Erro ao salvar!");
      ;
      this.spinner.hide();
    }
  }
  Descricao = new FormControl("", [
    Validators.required,
    Validators.minLength(4),
  ]);
  Poso = new FormControl("", [Validators.required, Validators.minLength(4)]);

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }

  getErrorMessageDesc() {
    return this.Descricao.hasError("required")
      ? "TELAMEDICAMENTOS.ERROCAMPO"
      : this.Descricao.hasError("Nome")
        ? "TELAMEDICAMENTOS.ERRONAOEVALIDO"
        : "";
  }

  public validarCampos() {
    this.showMessageError = false;
    this.Descricao.markAsTouched();

    if (this.Dados.des == "" || this.Dados.des == undefined) {
      this.showMessageError = true;
      document.documentElement.scrollTop = 0;
    }
  }

  editMedicamento(id:any) {
    var remedio = this.DadosTable.filter(
      (c:any) => c.exames.idExameClinica == id
    );
    this.Dados.des = remedio[0].exames.desExame;
    this.Dados.cod = remedio[0].exames.codExame;
    this.Dados.idExame = remedio[0].exames.idExameClinica;


    document.documentElement.scrollTop = 0;
  }

  valueRemedio(id:any) {
    this.remedioDelet = id;
    if (this.remedioDelet > 0)
      this.ngxSmartModalService.getModal("excluirItem").open();
  }

  InativarMedicamento() {
    try {
      if (this.remedioDelet > 0) {
        this.examesService
          .inativarExame(
            this.remedioDelet
          )
          .subscribe(
            (retorno) => {
              var Delete = retorno;
              if (Delete == true) {
                this.remedioDelet = 0;
                this.CarregaExames();
                this.ngxSmartModalService.getModal("excluirItem").close();
                this.snackBarAlert.sucessoSnackbar("excluido com sucesso!");
              } else {
                ;
                this.snackBarAlert.falhaSnackbar("Erro ao inativar!");
              }
              this.spinner.hide();
            },
            () => {
              this.snackBarAlert.falhaSnackbar("Erro ao inativar!");
              this.spinner.hide();
            }
          );
      }
      this.spinner.hide();
    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Erro ao inativar!");
      ;
      this.spinner.hide();
    }
  }

  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTable[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.DadosTable[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index:any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.DadosTable[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTable[this.indexGlobal]['toggle'] = !this.DadosTable[this.indexGlobal]['toggle'];
  }
}
