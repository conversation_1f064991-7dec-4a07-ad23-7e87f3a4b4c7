export interface DiaDoMes {
    data: Date;
    agendamentos: { [tipo: number]: { count: number, descricao: string } };
}
export interface Agendamento {
    DtAgendamento: Date;
    horaInicioAgendamento: Date;
    horaFinalAgendamento: Date;
    DescAgendamento: string;
    TipoAgendamento: number;
    CorEvento?: string;
    CorTexto?: string;
    IdAgendamento?: number;
    DescTipoAgendamento?: string;
}

export interface ProcessedAgendamento extends Agendamento {
    width: number;
    left: number;
    top: number;
    height: number;
    column: number;
    isPrimary: boolean;
}



