<mat-card appearance="outlined" class="mother-div">
    <div class="white-container">
        <mat-card-title class="spacer-card">
            <div class="col-md-12 header-container">
                <mat-icon class="icon-title">assessment</mat-icon>
                <a class="title-content">{{ 'TELARELATORIOS.RELATORIOUSUARIO' | translate }}</a>
            </div>
        </mat-card-title>
    
        <mat-card-content class="white-container">
            <div class="row justify-content-center content-section">
                <div class="col-md-12 row">
                    <div class="col-md-3 col-sm-6 col-xs-12 select-field">
                        <ng-select [items]="DadosUsuario" 
                                placeholder="{{'TELARELATORIOS.TIPOUSUARIO' | translate}}"
                                bindLabel="user" 
                                bindValue="user" 
                                name="especialidade"
                                [selectOnTab]="true" 
                                required 
                                (change)="CarregaUsuario()" 
                                [(ngModel)]="tipoUsuario">
                        </ng-select>
                    </div>
    
                    <div class="col-md-3 col-sm-6 col-xs-12 select-field">
                        <ng-select [items]="UsuariosDados" 
                                placeholder="{{'TELARELATORIOS.USUARIO' | translate}}"
                                bindLabel="nome" 
                                bindValue="idUsuarioacesso" 
                                name="especialidade"
                                [selectOnTab]="true" 
                                required 
                                (change)="AlteraUser()" 
                                [(ngModel)]="User">
                        </ng-select>
                    </div>
    
                    <div class="col-md-6 col-sm-12" style="padding: 5px; display: flex; flex-direction: column;">
                        <div style="display: flex; justify-content: space-evenly; align-items: center; gap: 1em; width: 100%;">
                            <div class="form-group" style="width: 50%;">
                                <label for="dataInicial" style="font-size: small; padding-left: 1em; color: gray;">{{ 'TELARELATORIOS.DATAINICIO' | translate }}</label>
                                <input type="date" class="form-control" id="dataInicial" [(ngModel)]="strDtInicio"
                                    (change)="dtChangeInicio()">
                            </div>
                        
                            <div class="form-group" style="width: 50%;">
                                <label for="dataFinal" style="font-size: small; padding-left: 1em; color: gray;">{{ 'TELARELATORIOS.DATAFIM' | translate }}</label>
                                <input type="date" class="form-control" id="dataFinal" [(ngModel)]="strDtFim"
                                    (change)="dtChangeFim()">
                            </div>
                        </div>
                        <p style="font-size: smaller; color: #f44336; text-align: center; margin-top: 5px;" *ngIf="flgDiferencaDatas">
                            O limite máximo do periodo é 6 mesês apenas.
                        </p>
                        <p style="font-size: smaller; color: #f44336; text-align: center; margin-top: 5px;" *ngIf="flgDtInicioNaoPreenchida">
                            {{ 'TELACADASTROPACIENTE.ESSECAMPOPRECISASERPREENCHIDO' | translate }}
                        </p>
                        <p style="font-size: smaller; color: #f44336; text-align: center; margin-top: 5px;" *ngIf="flgDtFimNaoPreenchida">
                            {{ 'TELACADASTROPACIENTE.ESSECAMPOPRECISASERPREENCHIDO' | translate }}
                        </p>
                        <p style="font-size: smaller; color: #f44336; text-align: center; margin-top: 5px;" *ngIf="flgDtFimMaiorQueInicio">
                            Data Inicio deve ser menor que Data Fim
                        </p>
                    </div>
                </div>
    
                <div class="col-md-12 col-sm-12 text-content col-xs-6 button-container">
                    <button class="search-button" mat-raised-button (click)="pesquisarRelatorio()">
                        <mat-icon>search</mat-icon>
                        <span class="no-mobile">{{ 'TELARELATORIOS.PESQUISAR' | translate }}</span>
                    </button>
    
                    <button class="generate-button" mat-raised-button (click)="baixarExcel()">
                        <mat-icon>note_add</mat-icon>
                        <span class="no-mobile">{{ 'TELARELATORIOS.GERARRELATORIO' | translate }}</span>
                    </button>
                </div>
            </div>
        </mat-card-content>
    
        <!-- Componente de Atividades -->
        <div class="component-container">
            <mat-card style="box-shadow: none;">
                <mat-card-content class="white-container" *ngIf="mostrarTabela">
                    <div class="table-container">
                        <div class="patterns-grid">
                            <div *ngFor="let item of pesquisaAtividades" class="pattern-card">
                                <div class="pattern-card-content">
                                    <div class="pattern-info">
                                        <h4 class="pattern-name">{{item.nomeUsuario}}</h4>
                                        <div class="pattern-details">
                                            <mat-icon class="pattern-icon">description</mat-icon>
                                            <span>{{item.desAtividade}}</span>
                                        </div>
                                        <div class="pattern-details" style="margin-top: 5px;">
                                            <mat-icon class="pattern-icon">event</mat-icon>
                                            <span>{{item.dtaAtividade | date: 'dd/MM/yyyy HH:mm'}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-100">
                                <button mat-flat-button class="btn-primary"
                                    *ngIf="(pesquisaAtividades != undefined &&  pesquisaAtividades.length > 0) && bOcultaCarregaMais == false"
                                    (click)="CarregarMais()">{{ 'TELAPESQUISAUSUARIO.CARREGARMAIS' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                </mat-card-content>
            </mat-card>
        </div>
    </div>
</mat-card>

<!-- Modal de Geração Enviada -->
<ngx-smart-modal #GeracaoEnviada identifier="GeracaoEnviada" customClass="nsm-centered medium-modal emailmodal"
    [dismissable]=false>
    <div class="modal-container success-modal">
        <div class="modal-header success-header">
            <h3 class="modal-title">Seu relatório foi solicitado, logo ele será gerado!</h3>
        </div>
        <div class="modal-actions">
            <button class="ok-button modal-button" mat-raised-button (click)="GeracaoEnviada.close()">OK</button>
        </div>
    </div>
</ngx-smart-modal>