@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

$primary: #348bc1;
.tabela {
    margin-right: 30px;
}

.cor {
    background: white;
    color: #1265b9;
}
$primary: #348bc1;
tr:hover {
    background: #fff !important;
    cursor: pointer;
}.panel_button {
    border: 1px solid #dcdbdb !important;
    border-radius: 0px;
}
.title-permissoes {
    font-family: Cairo, sans-serif;
}
.table-data {
    min-width:215px;
}
.info-header b {
    font-family: Cairo, sans-serif;
    font-weight: 500;
    font-size: 16px;
    text-align: center;
}
.info-card-perm b {
    font-family: Cairo, sans-serif;
    font-weight: 500;
    font-size: 14px;
    text-align: center;
}
.card-infos-mobile {
    display: none;
}
hr.sep-1 {
    border: 0; 
    margin-bottom: 0 !important;
    height: 4px; 
    width: 100%;
    background-image: linear-gradient(to right, #fff, #1265b9, #1265b9, #fff);
    border-radius: 10px;
    margin-top: 0;
  }
  .modal-excluir-logo {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
    justify-content: center;
    text-align: center;
}
.logo-final-modal {
    max-width: 300px;  
    height: 35px; 
    margin-left: -1px;
    margin-bottom: 10px;
    margin-top: 10px;
  }

  .info-excluir-title {
      font-family: Cairo, sans-serif;
      text-align: center;
      font-weight: 600;
  }
  .modal-info-excluir {
      width: 300px;
  }

@media (max-width: 890px) {
    .title-permissoes {
        font-weight: 500;
        font-size: 18px !important;
    }
}

@media (max-width: 860px) {
    .btn-add-perm {
        margin-right: 0 !important;
    }
    .ad-pp {
        display: none;
    }
}

@media (max-width: 780px) {
    .mother-permissoes {
        padding: 15px !important;
    }
}

@media (max-width: 767px) {
    .div-buscar {
        max-width: 400px;
        padding: 0;
    }
    .div-btn-add {
        max-width: 100px;
        margin-left: 48px;
        padding: 0;
    }
    .icone-lupa {
        margin-top: -1%;
        margin-left: -10%;
    }
}

@media (max-width: 709px) {
    .div-buscar {
        max-width: 70%;
    }
}

@media (max-width: 655px) {
    .div-buscar {
        max-width: 66%;
    }
    .table-data {
        min-width:215px;
    }
}

@media (max-width: 604px) {
    .table-data {
        min-width: 200px;
    }
}

@media (max-width: 600px) {
    .div-buscar {
        max-width: 62%;
    }
    .card-infos-mobile {
        display: block;
    }
    .table-infos-desktop {
        display: none;
    }
    .mother-permissoes {
        background: #f5f5f5;
    }
    .grid-buttons {
        right: -25px;
        bottom: -8px;
    }
}

@media (max-width: 550px) {
    .div-buscar {
        max-width: 59%;
    }
    .table-data {
        min-width: 115px;
    }
}

@media (max-width: 522px) {
    .div-buscar {
        max-width: 55%;
    }
}

@media (max-width: 450px) {
    .div-btn-add {
        margin-left: 35px;
    }
    .div-buscar {
        max-width: 60%;
    }
    .icone-lupa {
        margin-top: -3%;
        margin-left: -21%;
    }
}

@media (max-width: 446px) {
    .div-btn-add {
        margin-left: 30px;
    }
}

@media (max-width: 434px) {
    .btn-add-perm {
        width: 100%;
    }
    .div-buscar {
        max-width: 100%;
    }
    .div-btn-add {
        max-width: 100%;
        margin-left: 0;
    }
    .icone-lupa {
        margin-top: -2%;
        margin-left: -12%;
    }
}

@media (max-width: 400px) {
    .icone-lupa {
        margin-left: -14%;
    }
}

@media (max-width: 375px) {
    .icone-lupa {
        margin-left: -16%;
    }
}

@media (max-width: 330px) {
    .icone-lupa {
        margin-left: -19%;
    }
}

@media (max-width: 320px) {
    .modal-excluir {
        width: 280px !important;
    }
}