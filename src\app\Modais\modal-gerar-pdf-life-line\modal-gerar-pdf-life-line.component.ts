import { objEnvioDataApi } from './../../model/modelo-data-api';
import { PacienteService } from 'src/app/service/pacientes.service';
import { MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { Component, OnInit } from '@angular/core';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { ControleModaisService } from 'src/app/service/controle-modais.service';
import { FormBuilder, FormGroup, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule, formatDate } from '@angular/common';
import { ConvertDataObj } from 'src/app/Util/ConvertDataObj';
import { Uteis } from 'src/app/Util/uteis';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-modal-gerar-pdf-life-line',
    templateUrl: './modal-gerar-pdf-life-line.component.html',
    styleUrls: ['./modal-gerar-pdf-life-line.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon
    ]
})
export class ModalGerarPdfLifeLineComponent implements OnInit {

  constructor(
    private dialogRef: MatDialogRef<ModalGerarPdfLifeLineComponent>,
    private pacienteService: PacienteService,
    private spinner: SpinnerService,
    private snackBar: AlertComponent,
    private controleModaisService: ControleModaisService,
    private fb: FormBuilder,
  ) {
    this.form = this.fb.group({
      startDate: ['', Validators.required],
      endDate: ['']
    }, { validators: this.dateRangeValidator });
  }

  //#region  variaveis
  IdPaciente?: number;

  NomePaciente: string = "";

  DtInicio: Date = new Date();
  DtFim: Date = new Date();

  EnumTelas = ModalLifeLineTela;
  TelaExibida = ModalLifeLineTela.PreencherCampos;

  listaConsultas: listagemConsultaLifeLine[] = [];

  form: FormGroup;
  //#endregion

  ngOnInit(): void {
    this.IdPaciente = this.controleModaisService.IdPaciente_LifeLine!;
    this.NomePaciente = this.controleModaisService.Nome_LifeLine!;

  }

  ListarConsultas() {
    if (this.form.valid) {
      this.DtInicio = this.form.get('startDate')?.value;
      this.DtFim = this.form.get('endDate')?.value;

      let dados = new GerarLifeLine();
      dados.objDtInicio = ConvertDataObj(this.DtInicio, false);

      
      if (this.DtFim)
        dados.objDtFim = ConvertDataObj(this.DtFim, false);

      else
        dados.objDtFim = null;

      dados.IdPaciente = this.IdPaciente;
      this.pacienteService.ListaConsultasLifeLine(dados).subscribe((ret) => {

        this.listaConsultas = ret;
        this.TelaExibida = this.EnumTelas.ConfirmarConsultasListadas;

      }, (erro) => {
        console.error("Erro ao listar consultas",erro);
      });
    }
  }

  varPDf:any;

  GerarPdf() {
    this.spinner.show();

    let dado = this.listaConsultas.find(x => x.flgChecked == true);

    if (!dado) {
      this.snackBar.falhaSnackbar("Para gerar o pdf é necessário ao menos 1 consulta selecionada.");
      this.spinner.hide();
      return;
    }

    let ids: number[] = [];

    this.listaConsultas.forEach(element => {
      ids.push(element.idConsulta!)
    });


    let obj = new GerarLifeLine();
    obj.IdPaciente = this.IdPaciente;
    obj.objDtInicio = ConvertDataObj(this.DtInicio, false);
    
    if (this.DtFim)
      obj.objDtFim = ConvertDataObj(this.DtFim, false);
    
    else
      obj.objDtFim = null;

    

    obj.listaIdsConsulta = ids;

    this.pacienteService.GerarPdfLifeLine(obj).subscribe((ret) => {
      this.varPDf = ret;

      this.TelaExibida = this.EnumTelas.DownloadDocumento;
      this.downloadPdf();
      this.snackBar.sucessoSnackbar("Gerado com sucesso");
      this.spinner.hide();
    }, (erro) => {
      console.error(erro);
      this.snackBar.falhaSnackbar("erro ao gerar o registro");
      this.spinner.hide();
    })
  }

  downloadPdf() {
    Uteis.BaixarFileEmPDF(this.varPDf, "Relatorio Paciente");
  }

  voltaDatas() {
    this.TelaExibida = this.EnumTelas.PreencherCampos;
  }

  formatDateString(dateString: string | Date): string {
    const date = new Date(dateString);
    const datePart = formatDate(date, 'dd/MM/yyyy', 'en-US');
    const timePart = formatDate(date, 'HH:mm', 'en-US');
    return `${datePart}  -  ${timePart}`;
  }

  addItem() {

    let dados = new listagemConsultaLifeLine();
    dados.descConsulta = "teste";
    dados.flgChecked = true;
    dados.idConsulta = 12;
    dados.dtConsulta = new Date();

    this.listaConsultas.push(dados);
  }

  fecharModal() {
    this.dialogRef.close();
  }

  //#region validator
  dateRangeValidator(formGroup: FormGroup) {
    const startDate = formGroup.get('startDate')?.value;
    const endDate = formGroup.get('endDate')?.value;

    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      return { dateRangeInvalid: true };
    }
    return null;
  }

  // onBlurEvent(event: any) {
  //   // const inputValue = event.target.value;
  // }

  //#endregion
}

export enum ModalLifeLineTela {
  PreencherCampos = 1,
  ConfirmarConsultasListadas = 2,
  DownloadDocumento = 3
}

export class GerarLifeLine {
  IdPaciente?: number;
  objDtInicio?: objEnvioDataApi;
  objDtFim?: objEnvioDataApi | null;
  listaIdsConsulta: number[] = [];
}

export class listagemConsultaLifeLine {
  idConsulta?: number;
  descConsulta?: string;
  dtConsulta?: Date;
  flgChecked?: boolean
}