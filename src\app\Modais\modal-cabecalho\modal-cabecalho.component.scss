:host{
  position: relative;
  overflow: hidden;
  width: 100%;
}

.Cabecalho {
  flex-wrap: wrap;
  }

.Cabecalho:has(.Titulo) {
  display: flex;
  width: 100%;
  position: relative;
  justify-content: space-between;
}
  

.Titulo{
  position: relative;
  font-weight: 700;
  color: #1f5f3d;
  font-size: 24px;
}

.BtnFechar {
  position: relative;
  display: flex;
  justify-content: center;
  float: right;
  width: 30px;
  height: 30px;
  background: none;
  border: none;
  font-size: 30px;
  cursor: pointer;
  flex-wrap: wrap;
  align-content: center;
}

hr{
  flex: 100%;
}