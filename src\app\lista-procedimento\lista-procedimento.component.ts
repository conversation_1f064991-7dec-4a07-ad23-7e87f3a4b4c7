import { Component, Inject, OnInit } from '@angular/core';
import { AlertComponent } from '../alert/alert.component';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ConvenioService } from '../service/convenio.service';
import { ProcedimentoService } from '../service/procedimento.service';
import { SpinnerService } from '../service/spinner.service';
import { ValidadoreseMascaras } from '../Util/validadores';
import { ProcedimentoModelview } from '../model/procedimento';
import { MAT_DIALOG_DATA, MatDialog as MatDialog, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { ProcedimentoComponent } from './procedimento/procedimento.component';
import { GuiaTissComponent } from '../guia-tiss/guia-tiss.component';
import { ModalTemplateComponent } from '../Modais/modal-template/modal-template.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
    selector: 'app-lista-procedimento',
    templateUrl: './lista-procedimento.component.html',
    styleUrls: ['./lista-procedimento.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      ModalTemplateComponent,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCheckboxModule 
    ]
})
export class ListaProcedimentoComponent implements OnInit {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private procedimentoService: ProcedimentoService,
    private matDialog: MatDialog,
    public dialogRef: MatDialogRef<ListaProcedimentoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { idConsulta: number, idFatura: number, idConvenio: number}
  ) { 
      this.idConsulta = data.idConsulta;
      this.idFatura = data.idFatura;
      this.idConvenio = data.idConvenio;
    }

  listaProcedimento: ProcedimentoModelview [] = [];
  listaProcedimentosSelecionados: ProcedimentoModelview [] = [];

  idConsulta: number;
  idFatura: number;
  idConvenio: number;

  flgModalTemplate = false;
  conteudoTemplate: any;
  cabecalhoModal? = "";

  ngOnInit() {
    if (this.idConsulta)
      this.GetListaProcedimentoConsulta()
  }

  async GetListaProcedimentoConsulta(){
    this.spinner.show();
    this.procedimentoService.GetListaProcedimentoConsulta(this.idConsulta).subscribe((ret) => {
      this.listaProcedimento = ret;
    }, () => {
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar os procedimentos.')
    })
    this.spinner.hide();
  }

  AbrirProcedimento(idProcedimento: number = 0){
    var dialog = this.matDialog.open(ProcedimentoComponent, {
      width: '710px',
      height: '90vh',
      data: {
        idConsulta: this.idConsulta,
        idConvenio: this.idConvenio,
        idFatura:   this.idFatura,
        idProcedimento: idProcedimento,
      }
    });

    dialog.afterClosed().subscribe(() => {
      this.GetListaProcedimentoConsulta();    
    });	
  }

  AbrirModalGuiaTiss(){
    if (this.listaProcedimentosSelecionados.length == 0){
      this.snackBarAlert.falhaSnackbar("Por favor selecionar um procedimento.")
    }

    else{
      this.matDialog.open(GuiaTissComponent, {
        width: '710px',
        height: '90vh',
        data: {
          idConsulta: this.idConsulta,
          idFatura: this.idFatura,
          loteProcedimento: this.listaProcedimentosSelecionados
        }
        
      });
    }
  }

  FecharModal() {
    this.dialogRef.close();
  }

  AdicionarProcedimento(event: any, idProcedimento: number) {
    
    if (event.checked) {
      let novoProcedimento = new ProcedimentoModelview;
      novoProcedimento.idProcedimento = idProcedimento;

      this.listaProcedimentosSelecionados.push( novoProcedimento );
    } else {
      this.listaProcedimentosSelecionados = this.listaProcedimentosSelecionados.filter(x => x.idProcedimento !== idProcedimento);
    }
  }

}


