<mat-card appearance="outlined" class="main" style="margin-left:15px;">

    <mat-card-title class="spacer-card div-header" style="padding: unset">
        <div class="col-md-12" style="padding: unset">
            <mat-icon class="icon-title">payment</mat-icon>
            <a class="title-content fonte-tamanho title-contasreceber">{{ 'TELAFINANCAS.CONTASRECEBER' | translate }}</a>
        </div>
    </mat-card-title>
    <mat-divider class="p-t-20"></mat-divider>
    <br />
    <div>
        <div class="row p-3">
            <div class="col-md-2 col-sm-12 col-xs-6 carregar-arquivo h-auto d-flow-root text-center"
                style="padding-left:16px !important; border: 0px dotted #999;">
                <!-- (drop)="fileuploadesquerdo($event)" -->
                <i class="fas fa-cloud-upload-alt fa-fw fa-4x" style="color: #ccc; cursor: grabbing;"></i>
                <p style="font-size:12px; font-weight:500; line-height:12px">
                    Utilize esta área para importar comprovantes arrastando e soltando seu arquivo aqui
                    <br>
                    <br> {{ 'TELASTREAMING.OU' | translate }} </p>
                <label for="file" class="btn btn-primary" style="font-size:10px!important;cursor: pointer;">
                    Anexar Comprovante</label>
                <input type="file" style="width:100%;" id="file" accept=".xls,.xlsx,.pdf"
                    style="display: none;cursor: pointer;" />
            </div>

            <div class=" col-md-10 col-sm-12 col-xs-6 div-infos-receber">

                <mat-form-field class="col-md-12">
                    <input matInput placeholder="{{ 'TELAFINANCAS.DESCRICAOCONTA' | translate }}" required name="Desc"
                        [(ngModel)]="objContasReceber.DesConta">
                        <mat-error *ngIf="DescricaoVazia">
                           Esse campo deve ser preenchido</mat-error>
                    </mat-form-field>

                <!-- <mat-form-field class="col-md-3" appearance="legacy">
                    <input matInput placeholder="{{ 'TELAFINANCAS.DATARECEBIMENTO' | translate }}"
                    name="DtaRecebimento" [(ngModel)]="objContasReceber.DtaRecebimento"
                        mask="00/00/0000" max="2100-01-01" min="1900-01-01" maxlength="10">
                    <mat-error *ngIf="objContasReceberForm.get('DtaRecebimento').errors?.dataInorreta">
                        data inválida</mat-error>
                </mat-form-field> -->
                <input-date [(ngModel)]="objContasReceber.DtaRecebimento"
                    placeholder="{{ 'TELAFINANCAS.DATARECEBIMENTO' | translate }}" name="datae"></input-date>

                <!-- <mat-form-field class="col-md-3" appearance="legacy">
                    <input matInput placeholder="{{ 'TELAFINANCAS.DATABAIXA' | translate }}" name="DtaBaixa"
                        [(ngModel)]="objContasReceber.DtaBaixa" mask="00/00/0000" max="2100-01-01" min="1900-01-01"
                        maxlength="10">
                    <mat-error *ngIf="objContasReceberForm.get('DtaBaixa').errors?.dataInorreta">
                        data inválida</mat-error>
                </mat-form-field> -->
                <input-date [(ngModel)]="objContasReceber.DtaBaixa"
                    placeholder="{{ 'TELAFINANCAS.DATABAIXA' | translate }}" name="datae"></input-date>

                <!-- <mat-form-field class="col-md-3 input-spacing">
                    <mat-label>Forma de Pagamento</mat-label>
                    <select matNativeControl id="selectFomaPagamento" required name="formaPagamento"
                        [(ngModel)]="objContasReceber.IdTipoPagamento" class="col-md-3" name="cliente"
                        (change)="alterarFormaPagamentoNovo($any($event.target).value)">
                        <option *ngFor="let item of listaFormaPagamento" value="{{ item.idTipoPagamento }}">
                            {{ item.desTipoPagamento }}</option>
                    </select>
                    <mat-error *ngIf="DescricaoVazia">
                      Escolha uma forma de pagamento</mat-error>
            
                </mat-form-field> 

                <mat-form-field class="col-md-3 input-spacing" appearance="legacy">
                    <mat-label>{{ 'TELAFINANCAS.PARCELAS' | translate }}</mat-label>
                    <mat-select name="NumParcelas" [disabled]="objContasReceber.IdTipoPagamento != '3'"
                        [(ngModel)]="objContasReceber.NumParcelas">
                        <mat-option value="option"></mat-option>
                        <mat-option *ngFor="let item of listaParcelas;let i = index" [value]="item.valor">
                            {{item.descricao}}</mat-option>
                    </mat-select>
                </mat-form-field>  -->

                <mat-form-field class="col-md-12">
                    <input matInput placeholder="{{ 'TELAFINANCAS.OBSERVACOESCONTA' | translate }}" name="ObsConta"
                        [(ngModel)]="objContasReceber.ObsConta">
                </mat-form-field>

                <mat-form-field class="col-md-3">
                    <input matInput placeholder="{{ 'TELAFINANCAS.FORNECEDOR' | translate }}" name="fornecedor"
                        [(ngModel)]="objContasReceber.IdFornecedor">
                </mat-form-field>

                <mat-form-field class="col-md-3">
                    <input matInput name="Valor" placeholder="{{ 'TELAFINANCAS.VALOR' | translate }}" required
                        [(ngModel)]="objContasReceber.VlrConta" >
                        <!-- currencyMask [options]="{ prefix: 'R$ ', thousands: '.', decimal: ',' }" -->
                        >
                        <mat-error *ngIf="DescricaoVazia">
                            Preencha o valor</mat-error>
                </mat-form-field>

                <mat-form-field class="col-md-3">
                    <input matInput placeholder="{{ 'TELAFINANCAS.NOTAFISCAL' | translate }}" name="NotaFiscal"
                        [(ngModel)]="objContasReceber.NotaFiscal">
                </mat-form-field>

                <mat-form-field class="col-md-3">
                    <input matInput placeholder="{{ 'TELAFINANCAS.SERIENOTAFISCAL' | translate }}"
                        name="SerieNotaFiscal" [(ngModel)]="objContasReceber.SerieNotaFiscal">
                </mat-form-field>

                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4 btns-div-receber" align="right">
                    <button class="btn-primary " mat-raised-button style="color:white;" (click)="LimparCampos()"
                        style="margin-right: 2%;">
                        <mat-icon>clear</mat-icon> <span
                            class="legenda">{{ 'TELACADASTROMEDICO.LIMPAR' | translate }}</span>
                    </button>

                    <button class="btn-primary " mat-raised-button
                        style="color:white;margin-top: 10px;margin-bottom: 10px;" (click)="Submit()">
                        <mat-icon>save</mat-icon><span class="legenda">
                            {{ 'TELACADASTROMEDICO.SALVAR' | translate }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="anon_anot" *ngIf="DadosContasReceber!.length>0" style="overflow-x: scroll;">
        <div class="col-md-12" style="padding: unset">
            <mat-icon class="icon-title">history</mat-icon>
            <a class="title-content fonte-tamanho">Itens cadastrados</a>
        </div>
        <br />

        <table class="table tabela-receber">
            <thead>
                <tr>
                    <td style="width: 40%;     border: unset;">
                        Conta
                    </td>
                    <td class="text-center" style="width: 20%; border: unset;">
                        Vencimento
                    </td>
                    <td class="text-center" style="width: 20%; border: unset;">
                        Valor
                    </td>
                    <td class="text-center" style="width: 20%; border: unset;">
                        Ações
                    </td>
                </tr>
            </thead>
            <tbody *ngFor="let item of DadosContasReceber">
                <tr>
                    <td>
                        {{ item.desConta }}
                    </td>
                    <td>
                        {{ item.dtaRecebimento | date: 'dd/MM/yyyy HH:mm'}}
                    </td>
                    <td>
                        {{ item.vlrConta }}
                    </td>
                    <td class="text-center div-icones-acao">
                        <i style="vertical-align: -webkit-baseline-middle; cursor: pointer; font-size: 20px"
                            class="fa fa-download text-left icone" title="Download arquivo"></i>

                        <i style="vertical-align: -webkit-baseline-middle; margin-left: 25px; cursor: pointer; font-size: 20px"
                            class="fa fa-edit text-left icone" title="Editar conta"
                            (click)="CarregarEdicaoContaReceber(item.idContasReceber)"></i>

                        <i style="vertical-align: -webkit-baseline-middle; margin-left: 25px; cursor: pointer; font-size: 20px"
                            class="fa fa-trash text-left icone" title="Excluir"
                            (click)="AbrirModalExclusao(item.idContasReceber)"></i>
                    </td>

                </tr>
            </tbody>
        </table>
    </div>

    <div style="border:1px solid #ddd;" *ngIf="DadosContasReceber?.length==0">
        <div style="padding: 15px;" class="text-center">
            <b>Não há registro</b>
        </div>
    </div>

</mat-card>
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal emailmodal">
    <div class="modal-header p-t-20 p-b-20">
        <h1 class="little-title fw-700">
            <mat-icon style="color:red">warning</mat-icon>
            Deseja realmente excluir essa conta?
        </h1>
    </div>

    <mat-divider></mat-divider>
    <div class="row-button text-right " style="padding: 10px 0px;">
        <button mat-flat-button class="input-align btn btn-danger"
            (click)="ngxSmartModalService.getModal('excluirItem').close()">
            Não </button>
        <button mat-flat-button class="input-align btn btn-success" (click)="DeletarContaReceber()">
            Sim </button>
    </div>
</ngx-smart-modal>