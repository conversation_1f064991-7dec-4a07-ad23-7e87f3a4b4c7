@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

h3 {
    margin: 15px 0 0;
}

.custom-padding {
    padding-bottom: 15px!important;
}

.overlay.nsm-overlay-open {
    overflow: hidden;
}

.tabela {
    margin-right: 30px;
}
.DesabilitaCampo{
    pointer-events: none;
}
.form-modal .nsm-content {
    padding: 0 !important;
    width: auto !important;
}

pre {
    background-color: #f5f5f5;
    padding: 15px;
}

.item {
    color: white;
}

.EscritaCss span {
    color: white !important;
}

.icone-custom {
    width: 440px;
    margin-top: 20px;
    color: white;
    font-size: 260px;
    margin-left: 19%;
}

.primeiraConsulta {
    background-color: #18b740 !important;
}

.Cancelada {
    background-color: red !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-label-wrapper {
    position: absolute;
    left: 0;
    box-sizing: content-box;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    // margin-top: -14px;
}

// .Selo-align {
//   position: absolute;
//   right: 151px;
//   width: 10%;
//   margin-top: 10px;
// }
.ng-select-container {
    border-bottom: 1px solid #dddd
}

.red-cell {
    background-color: #fff !important;
}

.blue-cell {
    background-color: #c0c0c0 !important;
}

.bg-block {
    background-color: #fafafa !important;
}

.bg-block .cal-time {
    opacity: 0.5;
}

.cal-day-view .cal-event .a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
    float: right;
    font-size: 21px;
    margin-top: -6px;
}

.cal-day-view .cal-hour:last-child :not(:last-child) .cal-hour-segment {
    cursor: pointer !important;
    border-bottom-color: #e1e1e1;
}
.img-relogio {
    width: 35%;
}
.logo-final-modal {
    max-width: 300px;  
    height: 35px; 
    margin: 30px 0px;
    text-align: center;
    justify-content: center;
  }
  .link-acesso b {
    font-family: Cairo, sans-serif;
  }
  .link-acesso {
      margin: 20px auto;
  }
  .title-rapido {
    font-family: Cairo, sans-serif;
    margin-top: 15%; 
    margin-right: 30%;
  }
  .title-rapido b {
    font-family: Cairo, sans-serif;
    font-weight: 400;
    color: #191970;
    font-size: 30px;
  }
  .div-title-excluir b {
    font-family: Cairo, sans-serif;
    font-weight: 400;
    color: #191970;
    font-size: 20px;
  }
  .div-title-excluir {
      max-width: 300px;
  }

  hr.sep-1 {
    border: 0; 
    margin-bottom: 0 !important;
    height: 4px; 
    width: 100%;
    background-image: linear-gradient(to right, #fff, #0000CD, #0000CD, #fff);
    border-radius: 10px;
    margin-top: 0;
  }
  .header-modal-rapido {
      margin-top: -20px;
  }
  .div-botoes-rapido {
      margin: 0 auto;
      text-align: center;
      justify-content: center;
  }
  .title-consulta-modal {
    font-family: Cairo, sans-serif;
    font-weight: 400;
    color: #191970;
    font-size: 30px;
  }
  .logo-final-consulta {
    max-width: 300px;  
    height: 35px; 
    margin-top: 10px;
    margin-bottom: 20px;
    margin-left: -10px;
  }
  .img-logo-medicina {
      max-width: 100%;
      margin: 0 auto;
      text-align: center;
      justify-content: center;
  }
  .header-consulta-modal {
    display: flex; 
    height: 70px; 
    width: 515px !important; 
    background-size: 547px !important;
  }
  .margin-titulo {
    margin-top: 25px;
    font-size: 27px;
  }
  .cal-day-view .cal-event {
      font-size: 18px;
  }
  .title-infopac-modal {
    font-family: Cairo, sans-serif;
    font-weight: 400;
    color: #191970;
    font-size: 20px;
    text-align: left;
  }
  .logo-modal-footer {
    max-width: 300px;  
    height: 35px; 
    margin-top: 10px;
    margin-bottom: 20px;
 }
 .logo-medicina-modal {
    max-width: 100%;
    margin: 0 auto;
    text-align: center;
    justify-content: center;
 }
 .header-infopac {
    display: flex; 
    height: 100px; 
    width: 100% !important;
    padding-bottom: 15px; 
    padding-top: 15px;
 }
 .semana-mes-dia {
    font-family: 'Roboto', sans-serif; 
    font-size:25px
 }
 .frase-dia-mobile {
     display: none;
 }
 .custom-mobile-title {
    font-family: Cairo, sans-serif;
}

.title-cancelamento {
    text-align: center;
    justify-content: center;
    align-items: center;
    padding-top: 20px;
    margin-bottom: -10px;
    font-family: Cairo, sans-serif;
    font-weight: 400;
    font-size: 20px;
}
.info-dia-nao b {
    font-family: Cairo, sans-serif;
    font-weight: 300;
}
.espera-mobile {
    display: none;
}
.btns-ns-horario {
    float: right;    
}
.title-marcar-hr {
    font-family: Cairo, sans-serif;
}
.title-novopac {
    font-family: Cairo, sans-serif;
    font-size: 24px;
    color: #191970;
}
.div-title {
    text-align: center; 
    padding-top: 20px;
    padding-bottom: 15px;
}
.modal-espera-agenda {
    max-width: 700px;
}
.div-textarea-ms {
    max-width: 700px;
}
.modal-mensagemdia {
    width: 680px;
}

.modal-content {
    margin-top: 0px;
}

@media (max-width: 600px) {
    .cal-day-view .cal-hour-segment,
    .cal-day-view mwl-calendar-day-view-hour-segment {
        // display: table-cell;
        overflow: unset;
    }
    .modal-cancelamento {
        max-width: 300px;
        margin: 0 auto;
    }
    .btns-dia-nao {
        margin-bottom: -20px;
    }
    .logo-modal-footer {
        margin-top: 20px;
    }
    .modal-content {
        margin-top: 0px;
    }
  
}






.cor {
    background: white;
    color: #1265b9;
}

.botaoAgenda {
    font-size: 22px !important;
    text-decoration: none !important;
    background-color: transparent !important;
    float: right !important;
    margin-top: -6px;
}

.form-modal-Cliente {
    margin-top: 15px !important;
}

.form-modal-Cliente .nsm-content {
    padding: 0 !important;
}

.form-modal-Cliente .nsm-dialog-btn-close {
    border: 0;
    height: 30px;
    width: 30px;
    background: white;
    color: #2d2d2d;
    position: absolute;
    top: -40px;
    right: 0px;
    font-size: 1.2em;
    cursor: pointer;
    border-radius: 50%;
    box-shadow: 0 1px 0px 1px rgba(0, 0, 0, 0.2), 0 1px 0px 0 rgba(0, 0, 0, 0.14), 0 1px 0px 0 rgba(0, 0, 0, 0.5);
}

@media (min-width: 1450px) {
    .custom-dias {
        width: 90%;
        margin-left: 30%;
        margin-top: -6% !important;
    }
}

@media (max-width: 1060px) {
    .modal-content {
        max-width: 800px;
    }
    .tel-contato {
        max-width: 100%;
    }
}

@media (max-width: 850px) {
    .modal-content {
        max-width: 600px;
    }
    .linha-pac-espera {
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
        margin-left: 60px;
    }
    .linha-cpf-buscar {
        max-width: 100%;
    }
    .info-icone {
        margin-left: 75px;
        margin-top: 3px;
    }
    .info-espera {
        margin-left: -20px;
    }
}

@media (max-width:800px) {
    .modal-content {
        max-width: 500px;
        border-radius: 10px;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    .mat-form-field-flex {
        width: 100% !important;
    }
    .linha-cpf-buscar {
        max-width: 94.8%;
        margin: 0 auto;
        padding: 0 15px;
    }
    .card-infos-agenda {
        display: inline-block;
        margin: 0 auto;
        padding: 15px;
    }
    .info-espera {
        max-width: 88%;
        float: left;
        margin-right: -64px;
        left: -45px;
    }
    .tel-contato {
        padding: 0 15px;
        margin: 0;
        max-width: 94.8%;
    }
    .linha-pac-espera {
        padding-left: 20px;
    }
}

@media (max-width: 780px) {
    .title-content {
        font-size: 1.2rem;
    }
}

@media (min-width: 769px) and (max-width: 1449px) {
    .custom-dias {
        width: 90%;
        margin-left: 30%;
    }
}

@media (max-width: 767px) {
    .ali {
        text-align: center !important;
    }
    .tabela {
        margin-right: 0px;
    }
    .coluna {
        margin-left: 26px;
        margin-right: 26px;
    }
    .botoes-hj-flechas {
        margin-bottom: 10px;
    }
    .agenda-esp {
        margin-bottom: 10px;
    }
    .msn-dia {
        margin-bottom: 10px;
    }
    .semana-mes-dia {
        padding: 22px;
        margin-top: -40px;
    }
    .info-agenda-espec {
        padding: 25px;
    }
    .custom-search {
        padding-left: 50%;
    }
    .marcar-horario-modal {
        height: 70vh;
    }
    .Nome-Pac {
        margin-top: 4%;
    }
    .Email-Pac {
        margin-top: 4%;
    }
    .Cel-Pac {
        margin-top: 4%;
    }
    div .form-modal-Cliente {
        max-width: 400px;
        overflow: scroll;
        justify-content: center;
        border-radius: 10px;
    }
    .info-espera {
        margin-left: 0px;
    }
    .linha-pac-espera {
        padding-left: 0px;
    }
    .modal-mensagemdia {
        width: 480px;
    }
}

@media (max-width: 779px) {
    .no-mobile-card {
        display: none;
    }
}

@media (max-width: 550px) {
    .modal-content {
        max-width: 300px;
        border-radius: 10px;
    }
    .botao-enviar-dia {
        max-width: 290px;
    }
    .frase-dia {
        display: none;
    }
    .frase-dia-mobile {
        display: inline-block;
        margin: 0 auto;
        text-align: center;
    }
    .dia-alinhar {
        text-align: center;
        margin: 0 auto;
        margin-left: 59px;
    }
    .icon-title {
        margin-left: -20px !important;
    }
    .div-title-espera {
        padding-left: 44px !important;
    }
    .info-espera {
        margin-right: -70px;
        max-width: 79%;
    }
    .linha-cpf-buscar {
        max-width: 91.8%;
    }
    .tel-contato {
        max-width: 91.8%;
    }
    .espera-mobile {
        display: inline-block;
    }
    .espera-desktop {
        display: none;
    }
}

@media (max-width: 523px) {
    .btn-add-pac {
        margin-left: -1%;
    }
}

@media (max-width: 500px) {
    .header-infopac {
        height: 60px; 
        padding-bottom: 0px; 
        padding-top: 0px;
     }
     .linha-paciente {
        max-width: 82%;
     }
}

@media (max-width: 467px) {
    .linha-paciente {
        max-width: 79%;
     }
}

@media (max-width: 460px) {
    div .form-modal-Cliente {
        max-width: 300px;
        justify-content: center;
        border-radius: 10px;
    }
    .div-title {
        text-align: center; 
        padding-top: 50px;
        padding-bottom: 15px;
    }
    .title-btns {
        display: none;
    }
}

@media (max-width: 456px) {
    .btns-ns-horario {
        float: none;
        display: block;
        margin: 0 auto;
        text-align: center;
        justify-content: center;
        
    }
}

@media (max-width: 425px) {
    .align-panel {
        padding: 25px;
    }
    .nsm-body {
        height: auto;
        overflow-y: scroll !important;
        overflow-x: hidden;
    }
    .background-vacation {
        background-image: url(/assets/build/img/background-yellow.png) !important;
        background-size: 612px;
        background-position: bottom center;
        background-repeat: no-repeat;
        height: 250px;
        width: 520px;
    }
    .img-responsive {
        display: block;
        max-width: 60%;
        height: auto;
        // margin-left: 45px;
        margin-right: auto;
    }
    .modal-info {
        padding: 15px;
        line-height: 20px;
        color: #666;
        // width: 370px !important;
    }
    .row-button {
        width: 100%;
        display: block !important;
        padding-bottom: 10px;
        padding-bottom: 40px;
    }
    .custom-mobile-title {
        width: 100% !important;
        font-family: Cairo, sans-serif;
    }
    .modal-content {
        margin-top: 0px;
    }
    .margin-titulo {
        width: auto;
    }
    .btns-excluir-espera {
        padding-bottom: 10px !important;
    }
    .modal-escolhamedico .div-escolhamedico {
        height: 48vh !important;
        overflow: unset;
    }
    .logo-modal-footer {
        margin-bottom: 10px;
    }
}

.nsm-dialog-btn-close {
    display: block;
    
}

@media (max-width: 490px) {
    .modal {
        width: 420px;
    }
    // .background-Iniciar {
    //   display:none!important;
    // }
    .button_responsive {
        margin-right: 08px;
    }
}

@media (max-width: 425px) {
    .modal {
        width: 100%;
        //margin-right: 4px;
    }
    .nsm-body {
        position: relative;
        flex: 1 1 auto;
        // width: 250px;
    }
    .button_responsive {
        margin-right: -50px;
    }
    .custom-span {
        display: none;
    }
}

@media (max-width:420px) {
    .card-modal-rapido {
        max-width: 280px;
        text-align: center;
        margin: 0 auto;
    }
    .link-acesso b {
        text-align: center;
        font-size: 14px;
    }
    .link-acesso {
        max-width: 200px;
        margin: 20px !important;
        text-align: center;
    }
    .img-relogio {
        width: 35%;
    }
    .title-rapido {
        font-family: Cairo, sans-serif;
        margin-right: 5%;
        font-size: 25px;
      }

}

/* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
.mat-button-toggle-checked {
    background-color: #007bff !important;
}

.active {
    background-color: #1265b9 !important;
    color: white !important;
    border: none !important;
    border-top-left-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}

.b-c:hover {
    background-color: #1265b9 !important;
}

@media (width: 320px) and (height: 480px) {
    .modal {
        width: 250px;
    }
    .row-button {
        width: 100%;
        display: block !important;
        padding-bottom: 10px;
        padding-bottom: 40px;
    }
    .nsm-body {
        position: relative;
        flex: 1 1 auto;
        // width: 250px;
        height: auto;
        overflow-y: scroll;
        overflow-x: hidden;
    }
    .button_responsive {
        margin-right: -50px;
    }
    // .icone-custom {
    //   width: 250px;
    //   margin-top: 20px;
    //   color: white;
    //   font-size: 150px;
    //   margin-left: 15%;
    // }
}

.col-table table {
    border: none !important;
}

tr:hover {
    background: none !important;
}

.col-table thead {
    font-size: 11px;
    background: #fff;
    color: #666;
}

.col-table i {
    font-size: 18px !important;
    height: 20px;
    width: 20px;
    color: #1265b9;
}

.col-table mat-icon {
    font-size: 20px !important;
    height: 20px;
    width: 20px;
    color: #1265b9;
}

.legend {
    font-size: 10px;
    color: #666;
    font-weight: bold;
}

.border-content {
    margin-top: 20px;
    justify-content: center;
    border-radius: 13px;
    border: 1px solid #ddd;
    display: flex;
    width: 70%;
    /* margin-right: 27px; */
}

.font-table {
    font-size: 12px;
    padding: 0px !important;
    font-weight: 700;
    color: #b1aeae;
    margin-top: 5px;
    text-transform: uppercase;
    display: flex;
}

.t-header {
    border-bottom: 1px solid #ddd;
    text-transform: uppercase;
    font-size: 11px;
    padding: 5px;
}

.b-r {
    border-right: 1px solid #ddd;
}

.f-table {
    font-size: 11px;
    padding: 0px !important;
    font-weight: 700;
    color: #b1aeae;
    margin-top: 5px;
    text-transform: uppercase;
}

.value-color {
    color: #1265b9 !important;
    font-size: 13px;
    padding: 0 !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
    text-align: center !important;
}

@media (max-width: 680px) {
    .custom-search {
        padding-left: 40%;
    }
}

@media (height: 640px) {
    .nsm-body {
        // height: 450px;
        overflow-y: scroll;
        overflow-x: hidden;
    }
    .modal-info {
        padding: 13px;
        line-height: 20px;
        font-size: 1rem;
        color: #666;
        text-align: center;
        width: 100%;
    }
}

@media (height: 568px) {
    .nsm-body {
        height: 390px;
        overflow-y: scroll;
        overflow-x: hidden;
    }
    .modal-info {
        padding: 0px;
        line-height: 20px;
        font-size: 1rem;
        color: #666;
        text-align: center;
    }
    .icone-custom {
        width: 250px;
        margin-top: 20px;
        color: white;
        font-size: 225px;
        margin-left: 0%;
    }
}

// @media (height: 667px) {
//   .nsm-body {
//     height: 506px;
//   }
// }
@media (max-width: 667px) {
    .coluna {
        margin-left: 25px;
        margin-right: 25px;
    }
}

@media (max-width: 991px) {
    .botoes-acao-consulta {
        text-align: center;
    }
}

@media (min-width: 750px) {
    table thead {
        /* background: #1265b9; */
        color: white !important;
    }
}

table thead {
    /* background: #1265b9; */
    color: white;
}

@media (max-width: 603px) {
    .coluna {
        margin-left: 24px;
        margin-right: 24px;
    }
}

@media (max-width: 636px) {
    .coluna {
        margin-left: 23px;
        margin-right: 23px;
    }
    .form-modal-Cliente .nsm-content {
        padding: 0 !important;
        width: 100%;
        top: 7vh;
    }
}

@media (max-width: 575px) {
    .linha-paccpf {
        margin-left: 0px;
    }
    .linha-paciente {
        width: 82%;
    }
}

@media (max-width: 570px) {
    .card-consulta-modal {
        max-width: 290px;
        margin: 0 auto;
    }
    .infos-consult-modal {
        margin-bottom: 20px;
    }
    .header-consulta-modal {
        height: 50px; 
      }
    .margin-titulo {
        margin-top: 15px;
      }
    .title-infopac-modal {
        margin-left: 20px;
    }  
  
      
}

@media (max-width: 469px) {
    .coluna {
        margin-left: 22px;
        margin-right: 22px;
    }
}

@media (max-width: 425px) {
    .cartao-pagam {
        margin-bottom: -30px !important;
    }
}

@media (max-width: 402px) {
    .coluna {
        margin-left: 21px;
        margin-right: 21px;
    }
}

@media (max-width: 336px) {
    .coluna {
        margin-left: 20px;
        margin-right: 20px;
    }
}

// ----------------MODAL DIA BLOCK MEDICO  E INICIAL-----------------------
@media (width: 414px) {
    .img-responsive {
        margin-left: 70px;
    }
    .custom-mobile-title {
        width: 100% !important;
        text-align: center;
    }
}

@media (width: 412px) {
    .img-responsive {
        margin-left: 68px;
    }
}

@media (width: 411px) {
    .img-responsive {
        margin-left: 75px;
    }
    .custom-mobile-title {
        width: 100% !important;
    }
}

@media (max-width: 400px) {
    .linha-paciente {
        max-width: 76%;
     }
}

@media (width: 360px) {
    .modal-info {
        width: 100% !important;
        text-align: left;
    }
    .custom-mobile-title {
        width: 100% !important;
    }
}

@media (max-width: 350px) {
    .linha-paciente {
        max-width: 74%;
     }
}

@media (max-width: 323px) {
    .linha-paciente {
        max-width: 72%;
     }
}

@media (width: 320px) {
    .custom-mobile-title {
        width: 100% !important;
    }
}

@media (max-width: 990px) {
    .add-no-mobile {
        display: none;
    }
}

@media (min-width: 991px) {
    .add-no-desktop {
        display: none;
    }
}


// .mat-form-field-wrapper {
//     width: 225px;
// }
//.tel-contato .mat-form-field-wrapper {
    //width: 224px;}

.background-acesso{
    background-image: url(/assets/build/img/background-red.png) !important;
    background-repeat: no-repeat;
    background-size: 100%;
    background-position: bottom center;
    background-repeat: no-repeat;
    width: 100%;

}
.div-escolhamedico{
    background-color: #fff;
    border-radius: 10px;
}
