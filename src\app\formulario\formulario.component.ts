import { Component, OnInit } from '@angular/core';
import { formulariosModel } from '../model/formularios';
import { FormulariosService } from '../service/formulario.service';
import { Router } from '@angular/router';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import {  MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';

@Component({
    selector: 'app-formulario',
    templateUrl: './formulario.component.html',
    styleUrls: ['./formulario.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatFormFieldModule, 
      MatIconModule,
      TranslateModule,
      NgxSmartModalModule,
      
    ]
})
export class FormularioComponent implements OnInit {
  constructor(
    private spinner: SpinnerService,
    private formulariosService: FormulariosService,
    private localStorageService: LocalStorageService,
    // public snackBar: MatSnackBar,
    private snackbarAlert: AlertComponent,
    private router: Router,
    public ngxSmartModalService: NgxSmartModalService
  ) { }


  formsList_busca: formulariosModel[] = [];
  formsList: formulariosModel[] = [];
  flgmodal: boolean = false;

  idFormulario?: number;
  formularioSelecionado?: formulariosModel;

  filtroBusca: string = '';

  ngOnInit() {
    this.carregaLista();
  }

  carregaLista() {
    this.formulariosService.getAllFormularios().subscribe((ret) => {
      this.formsList = ret;
      this.formsList_busca = ret;

      this.spinner.hide();
    })
  }

  editarFormulario(idFormulario: number) {
    this.localStorageService.idFormularios = idFormulario;
    this.abreTelaFormularios();
  }

  novoFormulario() {
    this.localStorageService.idFormularios = null;
    this.abreTelaFormularios();
  }

  abreTelaFormularios() {
    this.router.navigate(['/adicionarformularios']);
  }

  preparaDeleteFormulario(Formulario: formulariosModel) {
    this.formularioSelecionado = Formulario;
    this.flgmodal = true;
  }

  async excluirFormulario(op: boolean) {
    if (op) {
      let id = this.formularioSelecionado!.idFormulario;
      this.formulariosService.deleteFormulario(id!).subscribe((ret) => {
        this.snackbarAlert.sucessoSnackbar(ret.msgResposta)
        this.carregaLista();
        this.spinner.hide();
      })
    }
    this.formularioSelecionado = new formulariosModel();
    this.ngxSmartModalService.getModal('excluirFormulario').close();

  }
  excluir(Formulario: formulariosModel){
    this.formularioSelecionado = Formulario;
    this.ngxSmartModalService.getModal('excluirFormulario').open();
  }

  verificarTecla(event: KeyboardEvent) {
    if (event.keyCode == 8 || event.keyCode == 32) {
      this.filtrarLocais();
    }
  }

  filtrarLocais() {
    if (this.filtroBusca.trim() !== '') {
      this.formsList = this.formsList_busca.filter(formulario => {
        return (
          formulario.nomeFormulario!.toLowerCase().includes(this.filtroBusca.toLowerCase())
        );
      });
    } else {
      this.formsList = this.formsList_busca;
    }
  }
}
