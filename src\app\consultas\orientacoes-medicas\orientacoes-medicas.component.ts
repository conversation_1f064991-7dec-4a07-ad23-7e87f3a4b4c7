import { Component, OnInit } from '@angular/core';
import { ConsultaService } from 'src/app/service/consulta.service';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { trigger, style, state, transition, animate } from '@angular/animations';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { Router } from '@angular/router';
import { irParaConsulta } from 'src/app/model/consulta';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { AgendaService } from 'src/app/service/agenda.service';
import { SignalHubService } from 'src/app/service/signalHub.service';
import { MedicoService } from 'src/app/service/medico.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule, TranslatePipe } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';

@Component({
    selector: 'app-orientacoes-medicas',
    templateUrl: './orientacoes-medicas.component.html',
    styleUrls: ['./orientacoes-medicas.component.scss'],
    animations: [trigger('openClose', [
            state('open', style({
                opacity: '1',
                display: 'block'
            })),
            state('closed', style({
                opacity: '0',
                display: 'none'
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ]),
        ])
    ],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      NgxSmartModalModule,
      TranslateModule,
      MatFormFieldModule,
      MatTooltipModule,
      TranslatePipe
    ]
})
export class OrientacoesMedicasComponent implements OnInit {

  constructor(    
    private spinner: SpinnerService,
    public consultaService: ConsultaService,
    public usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    public ngxSmartModalService: NgxSmartModalService,
    private router: Router,
    public agendaService: AgendaService,
    private signalHubService: SignalHubService,
    public medicoService: MedicoService
  ) {

    this.signalHubService.OnChamaPacienteFilaOrientacao
    .subscribe((idConsulta) => {
      this.Carregaconsultas(idConsulta);
    });

    this.signalHubService.OnAtualizaChamaPacienteFila
    .subscribe(() => {
      this.carregaFila();
    });


    this.signalHubService.OnAtualizaMedicosOnline
    .subscribe(() => {
     this.CarregamedicosOnline();
    });
    
   }

  toggle:any = {}


  Motivocancelameto = ''
  idCancela?:number;
  qtdRegistros = 10;
  DadosSolicitacao: any = [];
  bOcultaCarregaMais = false;
  cancelamento = false;
  ConsultaAgoraTeste:boolean =false;
  Objconsulta : any =[];
  PosicaoFila:number =0;
  MedicosOnline?:number =0;
  idConsulta?: number;
  DadosInformCancelament:any =[];
  ngOnInit() {
    this.CarregaSolicitacoes()
  }


  CarregaSolicitacoes() {
    this.consultaService.CarregaSolicitacoesOrientacoes(this.usuarioLogadoService.getIdPessoa(), this.usuarioLogadoService.getIdUltimaClinica(), 0, this.qtdRegistros).subscribe((retorno) => {


      this.DadosSolicitacao = retorno
      
      

      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
    }, () => {
      
    })

  }
  infoCancelamento(id:any) {

    this.DadosInformCancelament = []
    var info = this.DadosSolicitacao.filter((c:any) => c.idConsulta == id);    

    this.DadosInformCancelament.nome = info[0].usuarioCanc
    this.DadosInformCancelament.Motivo = info[0].motivoCancelamento
    this.DadosInformCancelament.Dta = new Date(info[0].dtaCancelamento).toLocaleString();
    this.DadosInformCancelament.usuarioCancelamento = info[0].pessoaCancelamento

    this.ngxSmartModalService.getModal('InforCancelamento').open();

  }


  CarregarMais() {
    this.bOcultaCarregaMais = false;
    this.consultaService.CarregaSolicitacoesOrientacoes(this.usuarioLogadoService.getIdPessoa(), this.usuarioLogadoService.getIdUltimaClinica(), this.DadosSolicitacao.length, this.qtdRegistros).subscribe((retorno) => {
      var dados = retorno;
      for (let index = 0; index < dados.length; index++) {
        this.DadosSolicitacao.push(dados[index]);
        
      }
      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
    }, () => {
      
    })
  }

  MotivoCampo() {
    if (this.cancelamento == true)
      this.cancelamento = false;
  }

  CancelaValue(id:any) {
    this.Motivocancelameto = '';
    this.idCancela = id;
    this.ngxSmartModalService.getModal('cancelarHorario').open();

  }
  CancelarConsulta() {
    if (this.Motivocancelameto == '' || this.Motivocancelameto == undefined) {
      this.cancelamento = true;
      return;
    }

    this.agendaService.InativarAgendamento(this.idCancela, this.usuarioLogadoService.getIdUsuarioAcesso(), this.Motivocancelameto).subscribe(() => {
      this.CarregaSolicitacoes();
      this.consultaService.atualizaLegenda$.emit();
      this.ngxSmartModalService.getModal('cancelarHorario').close();
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }


  irConsulta(id:any){
    var consulta = new irParaConsulta();
    consulta.idConsulta = id;
    consulta.flgSomenteProntuario = false;
    // consulta.idPagamento = this.pagamentoService.
    this.localStorageService.Consulta = consulta;
       this.router.navigate(['/streaming']);

  }



  
  async CarregamedicosOnline(){
    this.MedicosOnline =  await this.medicoService.getMedicosLogados(this.usuarioLogadoService.getIdUltimaClinica());
    this.spinner.hide();
  }

  async  ConsultaSolicitar() {
    this.carregaFila();
    this.MedicosOnline =  await this.medicoService.getMedicosLogados(this.usuarioLogadoService.getIdUltimaClinica());
    this.ngxSmartModalService.getModal('SolicitarConsulta').open();
    this.spinner.hide();
  }
  solicitacaoConsulta() {

    this.agendaService.AgendaPessoaSolicitante(this.usuarioLogadoService.getIdPessoa(), this.usuarioLogadoService.getIdUltimaClinica()).subscribe(() => {
      this.carregaFila();
      this.spinner.hide();

    })

  }


  carregaFila() {
    // this.consultaService.filaEsperaSolicitante(this.usuarioLogadoService.getIdPessoa()).subscribe((retorno: retornoFila) => {
    //   this.PosicaoFila = retorno.posicao!;
    //   this.idConsulta = retorno.idConsulta;

      
      
      
    // })
  }

  Carregaconsultas(idConsulta:any) {   
    this.consultaService.carregaConsultaFilaEspera(idConsulta).subscribe((retorno) => {      
      ;      
      this.Objconsulta = retorno
      this.ConsultaAgoraTeste = true;
      
      this.ngxSmartModalService.getModal('SolicitarConsulta').open();
    })
  }
  IniciarConsulta() {
    var consulta = new irParaConsulta();
    consulta.idConsulta = this.Objconsulta[0].idconsulta;
    consulta.flgSomenteProntuario = false
    this.localStorageService.Consulta = consulta;
    this.router.navigate(['/streaming']);
        this.ngxSmartModalService.getModal('sairconsulta').close();
      this.ngxSmartModalService.getModal('SolicitarConsulta').close();
  }
  CancelarConsultaModal() {
    this.agendaService.InativarAgendamento(this.idConsulta, this.usuarioLogadoService.getIdUsuarioAcesso(), "Solicitante desistiu da Fila de Espera").subscribe(() => {
      this.ngxSmartModalService.getModal('sairconsulta').close();
      this.ngxSmartModalService.getModal('SolicitarConsulta').close();
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }


}
