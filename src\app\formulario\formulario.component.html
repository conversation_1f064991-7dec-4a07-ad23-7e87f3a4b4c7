<mat-card appearance="outlined" class="principal mother-div">
    <!-- Modal de confirmação de exclusão -->
    <div *ngIf="flgmodal" class="modal">
        <div class="container">
            <p class="modalTitulo">{{ formularioSelecionado?.nomeFormulario }}</p>
            <p class="modalTitulo">Deseja realmente excluir este formulário?</p>
            <div class="gp_bt_modal">
                <button (click)="excluirFormulario(false)" class="bt_n_modal">Cancelar</button>
                <button (click)="excluirFormulario(true)" class="bt_s_modal">Excluir</button>
            </div>
        </div>
    </div>

    <!-- Cabeçalho -->
    <div class="header">
        <div class="header-title">
            <div class="icon-page-title">
                <mat-icon>assignment</mat-icon>
            </div>
            <h1 class="title-page">Formulários</h1>
        </div>
    </div>

    <!-- Corpo -->
    <div class="content">
        <!-- Barra de ferramentas -->
        <div class="toolbar">
            <div class="pesquisa-form">
                <mat-form-field appearance="outline">
                    <input matInput 
                           placeholder="{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}" 
                           [(ngModel)]="filtroBusca" 
                           (input)="filtrarLocais()"
                           (keyup)="verificarTecla($event)">
                </mat-form-field>
            </div>

            <div class="action-buttons">
                <button mat-raised-button class="btn-adicionar" (click)="novoFormulario()">
                    <mat-icon>add</mat-icon>
                    <span class="ad-pp">Adicionar Formulário</span>
                </button>
            </div>
        </div>

        <!-- Lista de formulários -->
        <div class="container-lista-scroll">
            <div class="lista-scroll">
                <div class="forms-list">
                    <div class="linha-tudo" *ngFor="let objFormulario of formsList">
                        <div class="dados-tabela">
                            <div class="infos-itens">
                                <p>
                                    <mat-icon>assignment</mat-icon>
                                    <span>{{ objFormulario.nomeFormulario }}</span>
                                </p>
                                <p>
                                    <mat-icon>category</mat-icon>
                                    <span>{{ objFormulario.nomeTipoOrientacao }}</span>
                                </p>
                            </div>
                        </div>
        
                        <div class="data-tabela">
                            <p>
                                <mat-icon>event</mat-icon>
                                <span>Data de Cadastro: {{ objFormulario.dtaCadastro | date: 'dd/MM/yyyy' }}</span>
                            </p>
                        </div>
        
                        <div class="btns-tabela">
                            <button mat-icon-button class="panel_button" (click)="editarFormulario(objFormulario.idFormulario!)">
                                <mat-icon>edit</mat-icon>
                            </button>
        
                            <button mat-icon-button class="panel_button" (click)="excluir(objFormulario)">
                                <mat-icon>delete</mat-icon>
                            </button>
                        </div>
                    </div>
        
                    <!-- Mensagem quando não há formulários -->
                    <div *ngIf="formsList.length === 0" class="empty-list">
                        <mat-icon>sentiment_dissatisfied</mat-icon>
                        <p>Nenhum formulário encontrado</p>
                        <button mat-stroked-button color="primary" (click)="carregaLista()">Recarregar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</mat-card>
<ngx-smart-modal #excluirItem identifier="excluirFormulario" customClass="nsm-centered medium-modal emailmodal">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title warning-title">
          Confirmação
        </h2>
      </div>
      <div class="modal-body">
        <p class="modal-message">O Formulário será excluído permanentemente</p>
      </div>
      <div class="modal-footer">
        <button class="btn-cancelar" (click)="excluirItem.close()">
          NÃO
        </button>
        <button class="btn-excluir" (click)="excluirFormulario(true)">
          SIM
        </button>
      </div>
    </div>
  </ngx-smart-modal>