import { enableProdMode } from '@angular/core';
import { environment } from './environments/environment';
import { bootstrapApplication } from '@angular/platform-browser';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';
import { registerLocaleData } from '@angular/common';
import localePtBr from '@angular/common/locales/pt';

if (environment.production) {
  enableProdMode();
  if (window && !environment.enableLog) {
    window.console.log = () => { };
    window.console.info = () => { };
  }
}

// Registre o locale português
registerLocaleData(localePtBr);

bootstrapApplication(AppComponent, appConfig)
  .catch(err => console.error(err));

