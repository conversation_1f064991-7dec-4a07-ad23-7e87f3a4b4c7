:host{
    width: 100%;
    height: 100%;
    max-width: 710px;
    max-height: 90vh;
    flex-direction: column;
    display: flex;
    overflow: hidden;
}

section{
    height: 100%;
    width: 100%;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
}

main{
    flex: 1;
    height: 70vh;
    padding: 5px 0px;
    overflow-y: auto;
}

footer{
    flex-shrink: 0;
}

.Conteudo{
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    .CaixaPrincipal{
        display: flex;
        flex-wrap: wrap;
        flex: 100%;
        gap: 10px;

        mat-form-field{
            height: 45px;
        }

        .CaixaRotulo{
            flex: 100%;
            color:  #5260ff;
            font-weight: 600;
            margin-bottom: -5px;
            font-size: 14px;
        }

        .CaixaSecundaria{
            display: flex;
            flex-wrap: wrap;     
        }

        .CaixaToggle{
            display: flex;
            flex-wrap: wrap;
            flex: 100%;
            justify-content: flex-end;
            align-content: center;

            span{
                height: 20px;
            }
            app-toggle{
                height: 20px;
            }
        }

        .CaixaRadioBtn{
            display: flex;
            flex-wrap: wrap;
            flex: 100%;
            justify-content: center;
            align-content: center;
            /* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
            mat-radio-group{
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                flex: 100%;
                justify-content: space-around;
                align-content: center;
            }
            /* TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version. */
            mat-radio-button{
                height: 25px; 
                overflow: hidden;
                align-content: center;
            }
        }

        .Border{
            border: solid 1px gray;
            border-radius: 5px;
            padding: 5px;
            justify-content: space-around;
        }
    }
}

.CaixaBtn{
    align-content: end;

    button{
        display: flex;
        align-items: center;
        gap: 5px;
        padding: 8px;
        justify-self: end;
        width: 120px;
        border: none;
        background-color: #5260ff;
        color: #fff;
        border-radius: 5px;
        height: 40px;
        flex-flow: row wrap;
        border-collapse: collapse; 
        font-size: 14px;

        span{
            height: 20px;
        }
    }

    button:hover{
        background-color:#4750af;
        color: #fff;
}
}


.CaixaAcoes{
    display: flex;
    flex-wrap: wrap;
    flex: 100%;
    padding: 10px 0px 0px 0px;
    gap: 10px;
    justify-content: flex-end;
}