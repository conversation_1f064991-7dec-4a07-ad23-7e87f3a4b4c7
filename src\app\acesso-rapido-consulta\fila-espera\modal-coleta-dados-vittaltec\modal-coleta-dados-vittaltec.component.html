<div class="modal-overlay">
    <div class="modal-container">
        <div class="modal-card">

            <div class="modal-header">
                <div class="header-left">
                    <div class="icon-wrapper">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z" stroke="#00ff80"
                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none" />
                            <path d="M9 12L11 14L15 10" stroke="#00ff80" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" />
                        </svg>
                    </div>
                    <div class="header-text">
                        <h2 class="modal-title">Coleta de Dados VittalTec</h2>
                        <p class="modal-subtitle">Integração com dispositivo de análise</p>
                    </div>
                </div>
                <!-- <button mat-icon-button (click)="onClose()" class="close-button">
                    <mat-icon>close</mat-icon>
                </button> -->
            </div>

            <div class="modal-content">

                <div class="steps-container" *ngIf="isLoading || !showDataPreview">
                    <div class="step-item" *ngFor="let step of steps; let i = index" [class.active]="currentStep === i"
                        [class.completed]="step.completed" [class.error]="step.error">

                        <div class="step-icon">
                            <mat-icon *ngIf="step.completed && !step.error">check_circle</mat-icon>
                            <mat-icon *ngIf="step.error">error</mat-icon>
                            <mat-icon *ngIf="!step.completed && !step.error && currentStep === i"
                                class="rotating">sync</mat-icon>
                            <mat-icon
                                *ngIf="!step.completed && !step.error && currentStep !== i">radio_button_unchecked</mat-icon>
                        </div>

                        <div class="step-content">
                            <span class="step-label">{{step.label}}</span>
                        </div>
                    </div>

                    <div class="progress-container" *ngIf="isLoading">
                        <mat-progress-bar mode="indeterminate"></mat-progress-bar>
                    </div>
                </div>

                <div class="error-container" *ngIf="errorMessage && !isLoading">
                    <div class="error-card">
                        <div class="error-header">
                            <mat-icon class="error-icon">error_outline</mat-icon>
                            <h3 class="error-title">Ops! Algo deu errado</h3>
                        </div>
                        <p class="error-message">{{errorMessage}}</p>
                        <div class="error-actions">
                            <button mat-raised-button color="primary" (click)="startProcess()" class="retry-button">
                                <mat-icon>refresh</mat-icon>
                                Tentar Novamente
                            </button>
                            <button mat-stroked-button mat-dialog-close class="cancel-button">
                                Cancelar
                            </button>
                        </div>
                    </div>
                </div>

                <div class="data-preview-container" *ngIf="showDataPreview && capturedData">
                    <div class="preview-header">
                        <mat-icon class="preview-icon">assignment_turned_in</mat-icon>
                        <h3 class="preview-title">Dados Coletados com Sucesso</h3>
                    </div>

                    <mat-divider></mat-divider>

                    <div class="preview-content">
                        <!-- Status do processo -->
                        <div class="status-section" *ngIf="capturedData?.status">
                            <div class="status-item" [ngClass]="'status-' + capturedData.status.toLowerCase()">
                                <mat-icon class="status-icon">
                                    {{ capturedData.status === 'aprovado' ? 'check_circle' : 'info' }}
                                </mat-icon>
                                <span class="status-text">Status: {{ capturedData.status | titlecase }}</span>
                            </div>
                        </div>

                        <!-- Dados do paciente -->
                        <div class="patient-data-section" *ngIf="capturedData?.data">
                            <h4 class="section-title">
                                <mat-icon>person</mat-icon>
                                Dados do Paciente
                            </h4>

                            <div class="data-grid">
                                <!-- ID do Paciente -->
                                <div class="data-item" *ngIf="capturedData.data.IdPaciente">
                                    <div class="data-label">
                                        <mat-icon class="data-icon">badge</mat-icon>
                                        ID do Paciente:
                                    </div>
                                    <div class="data-value">{{ capturedData.data.IdPaciente }}</div>
                                </div>

                                <!-- Pressão Arterial -->
                                <div class="data-item vital-sign"
                                    *ngIf="capturedData.data.pressaoSistolica || capturedData.data.pressaoDiastolica">
                                    <div class="data-label">
                                        <mat-icon class="data-icon">favorite</mat-icon>
                                        Pressão Arterial:
                                    </div>
                                    <div class="data-value pressure-value">
                                        <span class="systolic">{{ capturedData.data.pressaoSistolica || '--' }}</span>
                                        <span class="separator">/</span>
                                        <span class="diastolic">{{ capturedData.data.pressaoDiastolica || '--' }}</span>
                                        <span class="unit">mmHg</span>
                                    </div>
                                </div>

                                <!-- Temperatura -->
                                <div class="data-item vital-sign" *ngIf="capturedData.data.temperatura">
                                    <div class="data-label">
                                        <mat-icon class="data-icon">device_thermostat</mat-icon>
                                        Temperatura:
                                    </div>
                                    <div class="data-value">
                                        {{ capturedData.data.temperatura | number:'1.1-1' }}°C
                                    </div>
                                </div>

                                <!-- Oxigenação -->
                                <div class="data-item vital-sign" *ngIf="capturedData.data.oxigenacao">
                                    <div class="data-label">
                                        <mat-icon class="data-icon">air</mat-icon>
                                        Saturação de O₂:
                                    </div>
                                    <div class="data-value">
                                        {{ capturedData.data.oxigenacao }}%
                                    </div>
                                </div>

                                <!-- Batimentos Cardíacos -->
                                <div class="data-item vital-sign" *ngIf="capturedData.data.batimento">
                                    <div class="data-label">
                                        <mat-icon class="data-icon">monitor_heart</mat-icon>
                                        Freq. Cardíaca:
                                    </div>
                                    <div class="data-value">
                                        {{ capturedData.data.batimento }} <span class="unit">bpm</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Timestamp de coleta (se disponível) -->
                        <div class="timestamp-section" *ngIf="capturedData?.timestamp">
                            <div class="timestamp-item">
                                <mat-icon class="timestamp-icon">schedule</mat-icon>
                                <span class="timestamp-text">
                                    Coletado em: {{ capturedData.timestamp | date:'dd/MM/yyyy HH:mm:ss' }}
                                </span>
                            </div>
                        </div>

                        <!-- Fallback para dados não estruturados -->
                        <div class="raw-data-section" *ngIf="!capturedData?.data && capturedData">
                            <h4 class="section-title">
                                <mat-icon>data_object</mat-icon>
                                Dados Coletados
                            </h4>
                            <div class="raw-data-container">
                                <pre class="raw-data">{{ capturedData | json }}</pre>
                            </div>
                        </div>

                        <!-- Mensagem quando não há dados -->
                        <div class="no-data-section" *ngIf="!capturedData">
                            <mat-icon class="no-data-icon">info_outline</mat-icon>
                            <p class="no-data-text">Nenhum dado disponível para visualização.</p>
                        </div>
                    </div>
                </div>

                <div class="success-container" *ngIf="areAllStepsCompleted() && !showDataPreview && !isLoading">
                    <div class="success-card">
                        <mat-icon class="success-icon">check_circle</mat-icon>
                        <h3 class="success-title">Processo Concluído!</h3>
                        <p class="success-message">
                            Todos os dados foram coletados e processados com sucesso.
                        </p>
                    </div>
                </div>

            </div>

            <div class="modal-actions">
                <div class="action-group" *ngIf="errorMessage && !isLoading">
                    <button mat-stroked-button (click)="onCancel()" class="btn-secondary">
                        <mat-icon>close</mat-icon>
                        Cancelar
                    </button>
                    <button mat-raised-button (click)="retry()" color="primary" class="btn-primary">
                        <mat-icon>refresh</mat-icon>
                        Tentar Novamente
                    </button>
                </div>

                <div class="action-group" *ngIf="showDataPreview">
                    <button mat-stroked-button (click)="onCancel()" class="btn-secondary">
                        <mat-icon>close</mat-icon>
                        Cancelar
                    </button>
                    <button mat-raised-button (click)="continueProcess()" color="primary" class="btn-primary">
                        <mat-icon>arrow_forward</mat-icon>
                        Confirmar e Continuar
                    </button>
                </div>

                <div class="action-group"
                    *ngIf="areAllStepsCompleted() && !showDataPreview && !isLoading && !errorMessage">
                    <button mat-raised-button (click)="onContinue()" color="primary" class="btn-primary full-width">
                        <mat-icon>check</mat-icon>
                        Finalizar
                    </button>
                </div>

                <div class="action-group loading-actions" *ngIf="isLoading">
                    <p class="loading-text">
                        <mat-icon class="loading-icon">hourglass_empty</mat-icon>
                        Processando...
                    </p>
                </div>
            </div>

        </div>
    </div>
</div>