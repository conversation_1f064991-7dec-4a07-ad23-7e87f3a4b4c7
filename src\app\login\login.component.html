<div class="background-login">
    <!-- Componente de partículas avançadas (opcional) -->
    <!-- <app-particles></app-particles> -->
    
    <!-- Elementos decorativos animados -->
    <div class="floating-objects">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
        <div class="floating-circle circle-4"></div>
        <div class="floating-circle circle-5"></div>
        <div class="floating-pill pill-1"></div>
        <div class="floating-pill pill-2"></div>
        <div class="floating-pill pill-3"></div>
        <div class="floating-pill pill-4"></div>
        <div class="floating-pill pill-5"></div>
        <div class="floating-dot dot-1"></div>
        <div class="floating-dot dot-2"></div>
        <div class="floating-dot dot-3"></div>
        <div class="floating-dot dot-4"></div>
        <div class="floating-dot dot-5"></div>
    </div>
    
    <div class="div-login">
        <div class="login-card">
            <form class="login-form">
                <div class="logo-container">
                    <h1 class="app-title">Bonecare</h1>
                </div>

                <div class="input-group" *ngIf="ModoLogin=='Cpf'">
                    <label for="cpf">CPF</label>
                    <div class="input-container">
                        <i class="fas fa-id-card input-icon"></i>
                        <input type="text" id="cpf" maxlength="14" name="cpf"
                            (keypress)="mascaraCpf('###.###.###-##', $any($event))" autocomplete="off"
                            (change)="mascaraCpf('###.###.###-##', $any($event))" mask="000.000.000-00" [(ngModel)]="login.cpf">
                    </div>
                </div>

                <div class="input-group">
                    <label for="senha">Senha</label>
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input [type]="showPassword ? 'text' : 'password'" id="senha" name="senha" autocomplete="off"
                            [(ngModel)]="login.senha" (keyup.enter)="realizarLogin()">
                        <button type="button" class="password-toggle" (click)="showPassword = !showPassword">
                            <i class="fas" [ngClass]="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                        </button>
                    </div>
                </div>

                <div class="error-message" *ngIf="logininvalido">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>{{ 'TELALOGIN.LOGININVALIDO' | translate }}</span>
                </div>

                <div class="actions">
                    <button type="button" class="btn-login" (click)="realizarLogin()">
                        <span>Entrar</span>
                    </button>
                    
                    <a class="forgot-password" (click)="recuperarSenha()">
                        {{ 'TELALOGIN.ESQUECEUSUASENHA' | translate }}
                    </a>
                </div>

            </form>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="copyright">
                © 2024 - Faso Fábrica de Software
            </div>
            <div class="security">
                <i class="fas fa-lock-alt"></i>
                <span>Bonecare é um site seguro</span>
            </div>
        </div>
    </footer>
</div>