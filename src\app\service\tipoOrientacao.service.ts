import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { tabTipoOrientacao } from '../model/tipoOrientacao';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class TipoOrientacaoService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getTipoOrientacao() {
        this.spinner.show();
        return this.http.get<tabTipoOrientacao[]>(environment.apiEndpoint + '/formulario/getTipoOrientacao');
    }
}