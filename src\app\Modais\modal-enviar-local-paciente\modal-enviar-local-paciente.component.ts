import { Component, Inject, OnInit } from '@angular/core';
import { PacienteModelview } from 'src/app/model/cliente';
import { ObjMensagemListaPaciente, ObjMensagemWhatsappPadrao } from 'src/app/model/whats';
import { PacienteService } from 'src/app/service/pacientes.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { WhatsService } from 'src/app/service/whats.service';
import { ValidadoreseMascaras } from 'src/app/Util/validadores';
import { AlertComponent } from 'src/app/alert/alert.component';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { EnumOrigemMensagem } from 'src/app/Util/EnumOrigemMensagem';
import { LocalService } from 'src/app/service/local.service';
import { LocalModelView } from 'src/app/model/local';
import { ModalCabecalhoComponent } from '../modal-cabecalho/modal-cabecalho.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';

@Component({
    selector: 'app-modal-enviar-local-paciente',
    templateUrl: './modal-enviar-local-paciente.component.html',
    styleUrls: ['./modal-enviar-local-paciente.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      ModalCabecalhoComponent,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatFormFieldModule,
      MatIcon,
      MatCardModule   
    ]
})
export class ModalEnviarLocalPacienteComponent implements OnInit {

  constructor(
    public pacienteService: PacienteService,
    private spinner: SpinnerService,
    private validadores: ValidadoreseMascaras,
    private whatsService: WhatsService,
    private snackbar: AlertComponent,
    public dialogRef: MatDialogRef<ModalEnviarLocalPacienteComponent>,
    private localService: LocalService,
    @Inject(MAT_DIALOG_DATA) public data: { idLocal: number }
  ) { this.idLocal = data.idLocal }

  objMensagemListaPaciente: ObjMensagemListaPaciente = new ObjMensagemListaPaciente();
  dadosLocal: LocalModelView = new LocalModelView();

  idLocal: number;

  listaPaciente: PacienteModelview[] = [];
  listaMensagem: ObjMensagemWhatsappPadrao[] = [];

  qtdPorPagina = 15;
  filtroPaciente = "";
  flgBtnCarregarMais = false;

  ngOnInit() {
    this.GetDadosLocal();
    this.GetListaPaciente();
    this.GetListaMensagemPadrao();
    this.objMensagemListaPaciente.listaPaciente = [];
  }

  GetListaPaciente(flgCarregarMais?: boolean){
    this.spinner.show();

    var inicio = flgCarregarMais ? this.listaPaciente.length : 0;
    var filtro = this.validadores.LimparMascara(this.filtroPaciente);

    this.pacienteService.GetListaPaciente(inicio, this.qtdPorPagina, filtro).subscribe((ret) => {
      ;

      if (flgCarregarMais){
        for (let i = 0; i < ret.length; i++) {
          this.listaPaciente.push(ret[i]);
        }
      } else {
        this.listaPaciente = ret;
      };

      this.listaPaciente.forEach(element => {
        if (element.tel != null && element.tel != '' && element.tel != undefined)
          element.tel = this.validadores.FormatarTelefone(element.tel);
      });

      this.flgBtnCarregarMais = ret.length < this.qtdPorPagina;

      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  };

  GetListaMensagemPadrao() {
    this.spinner.show();
    this.whatsService.GetListaMensagemPadrao(EnumOrigemMensagem.Local).subscribe((ret) => {
      this.listaMensagem = ret;
      

      this.spinner.hide();
    }, (erro) => {
      this.spinner.hide();
      this.snackbar.falhaSnackbar("Erro ao carregar dados",erro);
    })
  }

  EnviarMensagem(){
    
    if (!this.ValidaObjMensagem())
      return

    this.spinner.show();

    this.whatsService.EnviarMensagemListaPaciente(this.objMensagemListaPaciente).subscribe(() =>{
    this.snackbar.sucessoSnackbar("Envio concluido",);
    this.FecharModal();
    this.spinner.hide();
    },() => {
      this.snackbar.falhaSnackbar("Falha ao enviar as mensagens");
      this.spinner.hide();
    });
  };

  AdicionarPaciente(idCliente: number) {
    const index = this.objMensagemListaPaciente.listaPaciente!.findIndex(p => p.idCliente == idCliente);
    if (index > -1) {
      this.objMensagemListaPaciente.listaPaciente!.splice(index, 1);
    } else {
      var novoPaciente = new PacienteModelview();
      novoPaciente.idCliente = idCliente
      this.objMensagemListaPaciente.listaPaciente!.push(novoPaciente);
    }
  }
  

  ValidaPacienteSelecionado(idCliente: number): boolean {
    return this.objMensagemListaPaciente.listaPaciente!.some(p => p.idCliente == idCliente);
}

  FecharModal(){
    this.dialogRef.close();
  }

  SelecionaMensagem(mensagem: string){
    this.objMensagemListaPaciente.mensagem = this.ConstruirMensagem(mensagem);
  }

  GetDadosLocal(){
    this.spinner.show();
    this.localService.GetDadosLocal(this.idLocal).subscribe((ret) => {
      this.dadosLocal = ret;
      this.spinner.hide();
    }, () =>{
      this.spinner.hide();
    })
  }

  ConstruirMensagem(mensagem: string): string {
    
    const replacements: { [key: string]: string | null } = {
      '@NOMELOCAL': this.dadosLocal.nome!,
      '@RUA': this.dadosLocal.endereco.rua!,
      '@NUMERO': this.dadosLocal.endereco.numero!,
      '@COMPLEMENTO': this.dadosLocal.endereco.complemento!,
      '@BAIRRO': this.dadosLocal.endereco.bairro!,
      '@CEP': this.dadosLocal.endereco.cep!
    }

    return mensagem.replace(/@\w+/g, match => {
      const replacement = replacements[match];
      return replacement !== null && replacement !== undefined ? replacement : '';
  });
  }

  ValidaObjMensagem() : boolean{
    var ret = true;
    
    if (this.objMensagemListaPaciente.listaPaciente == null || this.objMensagemListaPaciente.listaPaciente.length == 0){
      this.snackbar.falhaSnackbar("Por favor selecione ao menos um paciente.");
      ret = false;
    };

    if (this.objMensagemListaPaciente.mensagem == undefined || this.objMensagemListaPaciente.mensagem.length == 0 ){
      this.snackbar.falhaSnackbar("Por favor escreva ou selecione uma mensagem.");
      ret = false;
    };

    return ret;
  }

}
