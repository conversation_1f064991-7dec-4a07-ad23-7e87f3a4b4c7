
import { catchError, Observable, throwError } from 'rxjs';
import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Cliente, DadosMedicosUsuario, PacienteModelview } from '../model/cliente';
import { SpinnerService } from './spinner.service';
import { GerarLifeLine, listagemConsultaLifeLine } from '../Modais/modal-gerar-pdf-life-line/modal-gerar-pdf-life-line.component';

@Injectable({
    providedIn: 'root'
})
export class PacienteService {

    public atualizaDadosMes$: EventEmitter<any>;

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {

        this.atualizaDadosMes$ = new EventEmitter()

    }
    public headers = new Headers({ 'Content-Type': 'application/json' });


    reqHeader = new HttpHeaders({ 'No-Auth': 'True' });

    public GetPacienteAgenda(pesquisa:any, idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Paciente/GetPacienteAgenda', { params });
    }


    public getPacienteEdit(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Paciente/' + id);
    }


    public getDadosPaciente(idPaciente:any, idConsulta:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Paciente/GetDadosCorpoPacientes/' + idPaciente + '/' + idConsulta);
    }

    public AtivarPaciente(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Paciente/AtivarPaciente/' + id + '/' + idUsuario);
    }

    public inativarPaciente(id:any, motivoExclusao:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Paciente/InativarPaciente/' + id + '/' + motivoExclusao + '/' + idUsuario);
    }

    public salvarDadosCorpo(dados: DadosMedicosUsuario): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Paciente/salvarDadosCorpo', dados);
    }

    public salvarPacienteAgendaAcesso(cliente: Cliente): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Paciente/salvarPacienteAgendaAcesso/', cliente, { headers: this.reqHeader });
    }

    public salvarPacienteAgenda(cliente: Cliente, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Paciente/SalvarPacienteAgenda/' + idUsuario, cliente);
    }
    public salvarPaciente(cliente: Cliente): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Paciente/', cliente);
    }

    public GetMotivoExclusao(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Paciente/GetMotivoExclusao/' + id);
    }

    public GerarPdfLifeLine(obj: GerarLifeLine) {
        
        return this.http.post(environment.apiEndpoint + '/Paciente/GerarPdfLifeLine', obj, { responseType: 'arraybuffer' });
    }
    public ListaConsultasLifeLine(obj: GerarLifeLine) {
        return this.http.post<listagemConsultaLifeLine[]>(environment.apiEndpoint + '/Paciente/ListaConsultasLifeLine', obj);
    }

    public GetListaPaciente(inicio: number, fim: number, pesquisa: string, flgInativos?: boolean) {
        let params = new HttpParams();
        params = params.append('inicio', (inicio));
        params = params.append('fim', (fim));
        params = params.append('pesquisa', (pesquisa));
        params = params.append('flgInativos', (flgInativos ?? false));

        return this.http.get<PacienteModelview[]>(environment.apiEndpoint + '/Paciente/GetListaPaciente', { params })
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    };
}