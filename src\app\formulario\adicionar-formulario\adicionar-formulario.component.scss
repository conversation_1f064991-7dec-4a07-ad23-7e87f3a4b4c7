// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}

.form-container {
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Cards
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: none;
  margin-bottom: 24px;
  overflow: hidden;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
  max-height: 67vh;
  overflow: auto;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
}

// Headers
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

// Sections
.section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
}

// Form
.form-group {
  margin-bottom: 20px;
  padding: 0 !important;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-left: 0px;
  margin-right: 0px;
  gap: 10px;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 34%;
  padding: 0 12px;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
.col-md-6.date{
  max-width: 10%;
}

.full-width-text {
  width: 500px;
}
.full-width{
  width: 100%;
}
.full-width.nome{
  width: 45%;
}

.date-field {
  width: 100%;
  text-align: right;

  input {
    text-align: right;
  }
}

// Material overrides
:host ::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-label {
    color: $text-secondary;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
    color: $primary-color;
  }
  
  .mat-select-value {
    color: $text-primary;
  }
  
  .mat-select-arrow {
    color: $primary-color;
  }
  
  .mat-input-element:disabled {
    color: $text-secondary;
  }
}

// ng-select overrides
.modern-select {
  ::ng-deep {
    .ng-select-container {
      border-radius: 4px;
      border-color: $border-color;
      min-height: 52px;
      
      &:hover {
        border-color: $primary-light;
      }
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
    }
    
    .ng-option {
      padding: 10px 16px;
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  border-radius: 20px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary !important;
  
  &:hover {
    color: $primary-color;
  }
}

.btn-success {
  background-color: #0ad07c !important;
  color: white;
  padding: 10px 24px;
  border-radius: 5px !important;
  
  &:hover {
    background-color: darken($primary-color, 5%);
    transform: scale(1.05);
  }
}

.btn-link {
  background: none;
  color: $primary-color !important;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

.btn-icon {
  background: none;
  padding: 6px;
  height: 36px;
  width: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .material-icons {
    margin-right: 0;
    color: $text-secondary;
  }
  
  &:hover {
    background-color: rgba($error-color, 0.1);
    
    .material-icons {
      color: $error-color;
    }
  }
}

.btn-add {
  margin-top: 16px;
}

// Questions
.questions-container {
  width: 100%;
}

.questions-list {
  min-height: 100px;
  border-radius: $border-radius;
  border: 1px dashed $border-color;
  background-color: $secondary-color;
  overflow: hidden;
}

.question-item {
  background-color: white;
  margin: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow $transition ease;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.question-content {
  display: flex;
  align-items: center;
  padding: 12px;
}

.drag-handle {
  color: $text-secondary;
  cursor: grab;
  padding: 0 8px;
}

.question-text {
  flex: 1;
  padding: 0 12px;
}

.question, .question-type {
  margin: 4px 0;
  font-size: 14px;
}

.question-label {
  font-weight: 500;
  color: $primary-color;
}

.delete-icon {
  color: $text-secondary;
}

.example-custom-placeholder {
  background-color: rgba($primary-light, 0.3);
  border: 2px dashed $primary-light;
  border-radius: 8px;
  min-height: 60px;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  margin: 8px;
}

// Modal
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-in-out;
}

.modal-content {
  background-color: white;
  border-radius: $border-radius;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(-30px); }
  to { opacity: 1; transform: translateY(0); }
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
}

.modal-title {
  margin: 0;
  color: #fff;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// For the drag & drop
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  background-color: white;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.questions-list.cdk-drop-list-dragging .question-item:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

// Add some responsiveness
@media (max-width: 576px) {
  .section-content {
    padding: 16px;
  }
  
  .question-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .question-text {
    padding: 12px 0;
    width: 100%;
  }
  
  .btn-icon {
    align-self: flex-end;
  }
}
// Material Form Field customization
::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: rgba(79, 70, 229, 0.2);
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-label {
    margin-top: -0.25em;
    color: $accent-color;
  }
  
  .mat-form-field-label.mat-focused {
    color: $primary-color !important;
  }
  
  .mat-form-field-subscript-wrapper {
    margin-top: 0.5em;
  }
  
  .mat-form-field-appearance-outline .mat-select-arrow-wrapper {
    transform: translateY(0);
  }
  
  .mat-select-panel {
    border-radius: 8px !important;
  }
  
  .ng-select .ng-select-container {
    border-radius: 8px;
    border-color: rgba(79, 70, 229, 0.2);
    min-height: 54px;
    transition: $transition;
    background-color: rgba(255, 255, 255, 0.7);
  }
  
  .ng-select.ng-select-focused .ng-select-container {
    border-color:#fff;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  }
  
  .ng-dropdown-panel {
    margin-top: 14px;
    box-shadow: $box-shadow !important;
  }
  
  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected,
  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked {
    background-color: $primary-light;
    color: $primary-color;
    font-weight: 600;
  }
  
  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
    background-color: lighten($primary-light, 3%);
    color: $primary-color;
  }
  
  .mat-form-field-infix {
    padding: 0.75em 0 0.75em 0;
  }
  
  // Colorização de erro
  .mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-outline-thick {
    color: $error-color !important;
  }
  
  .mat-form-field-appearance-outline.mat-form-field-invalid .mat-form-field-label {
    color: $error-color !important;
  }
  
  // Hover estados de input
  .mat-form-field-appearance-outline:hover:not(.mat-form-field-disabled):not(.mat-focused) .mat-form-field-outline {
    opacity: 1;
    color: rgba(79, 70, 229, 0.5);
  }
  
  // Formulário disabled
  .mat-form-field-appearance-outline.mat-form-field-disabled .mat-form-field-outline {
    color: rgba(0, 0, 0, 0.1);
  }
  .ng-dropdown-panel .scroll-host {
    height: 140px;
  }
  .ng-select.ng-select-single .ng-select-container .ng-arrow-wrapper{
    background-color: #fff;
  }
  .mdc-text-field--outlined .mdc-floating-label{
    font-size: 13px !important;
  }
  .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label, 
  .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{
    color: $primary-dark;
  }
}