import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { FormBuilder, Validators, FormGroup, AbstractControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PagamentoService } from '../service/pagamento.service';
import { EfetuarPagamento } from '../model/efetuar-pagamento';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'mpv-modal-pagamento',
    templateUrl: 'pagamento.component.html',
    styleUrls: ['pagamento.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatFormFieldModule,
      NgxSmartModalModule
    ]
})
export class PagamentoComponent implements OnInit {

    pagamentoForm:any = FormGroup;
    horizontalPosition: MatSnackBarHorizontalPosition = 'right';
    verticalPosition: MatSnackBarVerticalPosition = 'bottom';
    actionButtonLabel: string = 'Fechar';
    salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
    ErroSalvar: string = 'Erro ao salvar!';
    action: boolean = true;
    setAutoHide: boolean = true;
    autoHide: number = 6000;
    concordo?: boolean;
    concordomsg?: boolean;
    idPessoaRecebedora?: number | null;
    valor?: number | null;
    progress = false;
    idPessoaPagador?: number | null;
    FlgConsulta: boolean = false;
    idConsulta?: number | null;
    flgSalvaCartao: boolean = true;
    CCV: string = '';
    dtaCartaoExpira: string = '';
    NomeCartao: string = '';
    NumeroCartao: string = '';



    hiper =false;
    master =false;
    visa =false;



    // private spinner: SpinnerService,
    constructor(    
        public ngxSmartModalService: NgxSmartModalService,
        private formBuilder: FormBuilder,
        private pagamentoService: PagamentoService,
        // public snackBar: MatSnackBar,
        private snackBarAlert: AlertComponent
    ) {
        pagamentoService
            .getAbrirModal()
            .subscribe(ret => {
                if (ret != true)
                    return;
                this.idPessoaRecebedora = null;
                this.valor = null;
                this.FlgConsulta = false;
                this.idPessoaPagador = null
                this.limparFormulario();
                this.idConsulta = this.pagamentoService.getIdconsulta();
                this.FlgConsulta = this.pagamentoService.getFlgConsulta();
                this.idPessoaPagador = this.pagamentoService.getIdPagador();
                this.idPessoaRecebedora = this.pagamentoService.getIdRecebedora();
                this.valor = this.pagamentoService.getValor();
            });

    }

    ngOnInit(): void {
        this.instanciarFormulario();
    }

    // CarregaCartao(idUsuario) {
    //     this.pagamentoService.carregarCartao(idUsuario).subscribe((retorno) => {

    //     })

    // }
    nome = 'Renato Miranda';

    instanciarFormulario() {
        this.pagamentoForm = this.formBuilder.group({
            CartaoNome: ['',
                [
                    Validators.required,
                    Validators.minLength(5),
                    Validators.maxLength(50),
                    Validators.pattern('^[A-Za-zÀ-ú. ]+$'),
                    validaNome
                ]
            ],
            CartaoNumero: ['',
                [
                    Validators.required,
                    Validators.minLength(16),
                    Validators.maxLength(16)
                ]
            ],
            CartaoMesAno: ['',
                [
                    Validators.required,
                    Validators.minLength(4),
                    Validators.maxLength(4),
                    validaVencimentoCartao
                ]
            ],
            CartaoSeg: ['',
                [
                    Validators.required,
                    Validators.minLength(3),
                    Validators.maxLength(3)
                ]
            ],
            FraseFatura: ['',
                [
                    Validators.required,
                    Validators.minLength(3),
                    Validators.maxLength(9),
                ]
            ]
        });
    }

    markFormGroupTouched(form: FormGroup) {
        Object.values(form.controls).forEach(control => control.markAsTouched());
    }

    fecharModal() {
        this.ngxSmartModalService.getModal('ModalPagamento').close();
    }

    abrirModal() {
        this.ngxSmartModalService.getModal('ModalPagamento').open();
    }

    validarFormulario() {
        if (!this.idPessoaRecebedora || !this.valor) {
            this.snackBarAlert.falhaSnackbar('Preencha todos os campos!');
            this.fecharModal();
            this.ativarDesativarProgressButton();
            return false;
        }

        if (!this.pagamentoForm!.invalid)
            return true;

        this.markFormGroupTouched(this.pagamentoForm!);
        this.ativarDesativarProgressButton();
        return false;
    }

    limparFormulario() {
        // this.pagamentoForm.reset();
    }

    submit() {
        this.progress = true;
        if (!this.validarFormulario())
            return;
        const objPagamento = this.pagamentoForm.getRawValue() as EfetuarPagamento;
        objPagamento.idConsulta = this.idConsulta;
        objPagamento.IdPessoaRecebedora = this.idPessoaRecebedora;
        objPagamento.IdPagador = this.idPessoaPagador;
        objPagamento.ValorConsulta = this.valor;
        var valor = this.valor! / 0.01
        
        objPagamento.Valor = valor.toString();
        objPagamento.flgSalvaCartao = this.flgSalvaCartao;
        this.pagamentoService
            .efetuarPagamento(objPagamento)
            .then(ret => {
                ;
                if (ret?.ok) {

                    this.limparFormulario();
                    this.pagamentoService.clearIdRecebedora();
                    this.snackBarAlert.sucessoSnackbar('Pagamento efetuado com sucesso!');
                    this.fecharModal();
                    this.ativarDesativarProgressButton();
                    return;
                }

                this.ativarDesativarProgressButton();
                this.snackBarAlert.falhaSnackbar(ret?.mensagemErro);
            }, () => {
                this.ativarDesativarProgressButton();
                this.snackBarAlert.falhaSnackbar('Não foi possivél completar sua transação. Verifique.');
            });
    }

    ativarDesativarProgressButton() {
        this.progress = false;
        // this.progress = true;
    }

    // AlgumErro(mensagem) {
    //     let config = new MatSnackBarConfig();
    //     config.verticalPosition = this.verticalPosition;
    //     config.horizontalPosition = this.horizontalPosition;
    //     config.duration = this.setAutoHide ? this.autoHide : 0;
    //     config.panelClass = ['error-snack'];
    //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
    // }

    getCardFlag(cardnumber:any) {
        var cardnumber = cardnumber.replace(/[^0-9]+/g, '');

        var cards:any = {
            visa: /^4[0-9]{12}(?:[0-9]{3})/,
            mastercard: /^5[1-5][0-9]{14}/,
            diners: /^3(?:0[0-5]|[68][0-9])[0-9]{11}/,
            amex: /^3[47][0-9]{13}/,
            discover: /^6(?:011|5[0-9]{2})[0-9]{12}/,
            hipercard: /^(606282\d{10}(\d{3})?)|(3841\d{15})/,
            elo: /^((((636368)|(438935)|(504175)|(451416)|(636297))\d{0,10})|((5067)|(4576)|(4011))\d{0,12})/,
            jcb: /^(?:2131|1800|35\d{3})\d{11}/,
            aura: /^(5078\d{2})(\d{2})(\d{11})$/
        };

        for (var flag in cards) {
            if (cards[flag].test(cardnumber)) {
                
                return flag;
            }
        }

        return false;
    }
    bandeira :any;
    mascaraNumerocartao(evento:any) {
     this.bandeira =  this.getCardFlag(evento);
            this.NumeroCartao = evento
    }

    mascaraDatacartao(evento:any) {
                this.dtaCartaoExpira = evento
            }
        
            



    getCreditCardType(accountNumber:any) {
        var result = ''
        if (/^5[1-5]/.test(accountNumber)) {
            result = 'mastercard';
        } else if (/^4/.test(accountNumber)) {
            result = 'visa';
        } else if (/^(5018|5020|5038|6304|6759|676[1-3])/.test(accountNumber)) {
            result = 'maestro';
        } else {
            result = 'unknown'
        }
        return result;
    }

    // SalvarCadastro(mensagem) {

    //     let config = new MatSnackBarConfig();
    //     config.verticalPosition = this.verticalPosition;
    //     config.horizontalPosition = this.horizontalPosition;
    //     config.duration = this.setAutoHide ? this.autoHide : 0;
    //     config.panelClass = ['success-snack'];

    //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
    //     //   this.tradutor.get('TELACADASTROMEDICO.CADASTROSALVOCOMSUCESSO').subscribe((res: string) => {
    //     //     ;

    //     //   });

    // }


    focusFunction(){


        
      document.getElementById('card-credit')!.classList.add('hover');
        
      
    }


    focusOutFunction(){
  
        document.getElementById('card-credit')!.classList.remove('hover')
        
        // document.getElementById("MyElement").classList.add('MyClass');
        
        // document.getElementById("MyElement").classList.remove('MyClass');
    }
}

// 


export function validaVencimentoCartao(control: AbstractControl) {

    if (control.value == null)
        return null;

    const dataAtual = new Date();
    const anoAtual = parseInt(dataAtual.getFullYear().toString().substr(2, 2));
    const anoMaximo = anoAtual + 16;
    const mesAtual = dataAtual.getMonth();
    const mesMinimo = mesAtual;
    const mesMaximo = 13;

    const data = control.value;

    if (data.length != 4)
        return null;

    const mes = data.substr(0, 2);
    const ano = data.substr(2, 2);

    if ((ano == anoAtual && (mes > mesMinimo && mes < mesMaximo)) || (ano > anoAtual && ano < anoMaximo && mes < mesMaximo))
        return null;

    return { dataInorreta: true }
}

export function validaNome(control: AbstractControl) {

    if (control.value == null)
        return null;

    const nome = control.value.trim();

    if (nome.length > 4)
        return null;

    return { validaNome: true }

    
}