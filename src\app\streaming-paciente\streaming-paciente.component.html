<!-- Container principal da consulta do paciente -->
<div class="patient-consultation-container">
  
  <!-- Header com informações da consulta -->
  <div class="consultation-header">
    <div class="patient-info">
      <div class="patient-avatar">
        <mat-icon>person</mat-icon>
      </div>
      <div class="patient-details">
        <h2>{{ nomePaciente }}</h2>
        <span class="patient-type">{{ isLoggedIn ? 'Paciente Cadastrado' : 'Acesso Rápido' }}</span>
      </div>
    </div>
    
    <div class="consultation-status">
      <div class="status-indicator" [style.background-color]="corStatus">
        <span class="status-dot"></span>
      </div>
      <div class="status-info">
        <span class="status-text">{{ statusConexao }}</span>
        <span class="consultation-timer">{{ tempoConsulta }}</span>
      </div>
    </div>
  </div>

  <!-- <PERSON>rea principal do vídeo -->
  <div class="video-container" *ngIf="!consultaFinalizada">
    
    <!-- Iframe da videoconferência -->
    <div class="video-frame" *ngIf="FlgVideo && videoConnected">
      <iframe 
        [src]="urlSafe" 
        allow="microphone; camera; fullscreen"
        class="video-iframe"
        title="Consulta Médica">
      </iframe>
    </div>

    <!-- Loading state -->
    <div class="video-loading" *ngIf="!videoConnected">
      <div class="loading-spinner">
        <mat-icon class="spinning">refresh</mat-icon>
      </div>
      <p>Conectando com o médico...</p>
      <small>Aguarde um momento</small>
    </div>

    <!-- Controles do paciente -->
    <div class="patient-controls" *ngIf="videoConnected">
      <!-- Botão de finalizar consulta -->
      <div class="end-call-control">
        <button 
          mat-fab 
          color="warn"
          (click)="finalizarConsulta()"
          aria-label="Finalizar consulta"
          matTooltip="Finalizar consulta">
          <mat-icon>call_end</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <!-- Tela de consulta finalizada -->
  <div class="consultation-ended" *ngIf="consultaFinalizada">
    <div class="ended-content">
      <mat-icon class="ended-icon">check_circle</mat-icon>
      <h2>Consulta Finalizada</h2>
      <p>Obrigado por utilizar nossos serviços!</p>
      <p class="consultation-duration">Duração: {{ tempoConsulta }}</p>
      
      <div class="ended-actions">
        <button
          mat-raised-button
          color="primary"
          (click)="finalizarERedirecionarFinal()"
          class="primary-action">
          {{ isLoggedIn ? 'Voltar às Consultas' : 'Finalizar' }}
        </button>
      </div>
    </div>
  </div>

  <!-- Instruções para o paciente -->
  <div class="patient-instructions" (click)="FlgExibeInfo = false" *ngIf="FlgExibeInfo && videoConnected && !consultaFinalizada">
    <mat-card class="instructions-card">
      <mat-card-content>
        <h3>
          <mat-icon>info</mat-icon>
          Instruções
        </h3>
        <ul>
          <li>Mantenha-se em um local bem iluminado</li>
          <li>Fale claramente e próximo ao microfone</li>
          <li>Aguarde o médico iniciar a consulta</li>
          <li>Use os botões abaixo para controlar áudio e vídeo</li>
        </ul>
      </mat-card-content>
    </mat-card>
  </div>

</div>
