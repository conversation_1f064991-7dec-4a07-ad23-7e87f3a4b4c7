import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FilaEsperaMedicoService {
  
  constructor(private http: HttpClient) { }

  listarPacientesNaFila(): Observable<any> {
    return this.http.get(environment.apiEndpoint + '/fila-espera/listar-pacientes');
  }

  chamarProximoPaciente(): Observable<any> {
    return this.http.post(environment.apiEndpoint + '/fila-espera/chamar-proximo', {});
  }

  removerPacienteDaFila(token: string): Observable<any> {
    return this.http.delete(environment.apiEndpoint + `/fila-espera/remover-paciente/${token}`);
  }

  obterPaciente(token: string): Observable<any> {
    return this.http.get(environment.apiEndpoint + `/fila-espera/paciente/${token}`);
  }

  limparFila(): Observable<any> {
    return this.http.delete(environment.apiEndpoint + '/fila-espera/limpar-fila');
  }


}