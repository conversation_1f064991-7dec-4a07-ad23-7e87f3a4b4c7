:host{
  font-size: 14px ;
}
table {
  background: #fff;
}
.cartao {
  padding: 0px;
  height: auto;
}
.card_table {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

// .card_table:hover {
//     box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
//     transition: 0.3s;
// }
.spacer-card {
  background: #1265b9 !important;
  text-align: center;
}

#check-box {
  font-size: 16px;
  text-align: left;
  margin-top: 25px;
  margin-bottom: 18px;
  margin-left: 25px;
}

#input-align {
  float: right;
  margin-right: 45px;
  margin-top: -6px;
}

.imagem-privacidade {
  text-align: center;
  max-width: 250px;
  width: 100%;
  display: inline-block;
  margin: 10px 0;
  vertical-align: top;
}

.container-fluid {
  width: 60% !important;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  margin-top: 10px;
}

#check-box {
  display: flex;
}
#privacidade {
  margin-top: -30px; 
  margin-bottom: 10px;
}
.container {
  background:#f5f5f5;
  padding-right: 20px;
  padding-left: 16px;
}
.check-botoes {
  margin-left: 550px;
}

@media (max-width: 2145px) {
  .check-botoes {
    margin-left: 390px;
  }
}

@media (min-width: 2000px) {
  .cartao {
    padding: 0px;
    height: 109vh !important;
  }
}

@media (max-width: 1980px) {
  .check-botoes {
    margin-left: 140px;
  }
}

@media (max-width: 1510px) {
  .check-botoes {
    margin-left: 100px;
  }
}

@media (max-width: 1390px) {
  .check-botoes {
    margin-left: 50px;
  }
}

@media (max-width: 1290px) {
  .check-botoes {
    margin-left: 20px;
  }
}

@media (max-width: 1225px) {
  #input-align {
    margin-right: 15px;
  }
}

@media (max-width: 1170px) {
  #check-box {
    display: block;
    padding: 15px;
    margin-top: 5px;
  }
  .check-aceitar {
    max-width: 100%;
    text-align: center;
    margin-left: -25px;
  }
  .check-botoes {
    align-items: center;
    max-width: 100%;
    justify-content: center;
    margin-left: -20px;
    margin-top: 15px;
  }
}

@media (max-width: 980px) {
  #input-align {
    margin-right: 8px;
  }
  #check-box {
    margin-left: 0px;
  }
}

@media (max-width: 780px) {
  .check-botoes {
    margin-left: 0px;
    margin: 0 auto;
    justify-content: center;
    text-align: center;
    margin-top: 10px;
  }
  .check-aceitar {
    margin-left: 0px;
  }
}

@media (max-width: 342px) {
  #check-box {
    padding: 10px;
  }
}

@media (max-width: 340px) {
  .aceitar-termos {
    font-size: 14px;
  }
  .container {
    padding-left: 0;
    padding-right: 0;
  }
}

