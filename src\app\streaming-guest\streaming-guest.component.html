<div class="streaming-container">
  <!-- Header da consulta -->
  <div class="header-consulta">
    <div class="info-consulta">
      <h2>Consulta com Paciente</h2>
      <div class="participantes">
        <div class="medico-info">
          <mat-icon>person</mat-icon>
          <span>Dr. {{nomeMedico}}</span>
        </div>
        <div class="paciente-info">
          <mat-icon>patient_list</mat-icon>
          <span>{{nomePaciente}}</span>
        </div>
      </div>
    </div>
    
    <div class="status-consulta">
      <div class="status-indicator" [style.background-color]="corStatus">
        {{statusConexao}}
      </div>
      <div class="tempo-consulta">
        <mat-icon>schedule</mat-icon>
        <span>{{tempoConsulta}}</span>
      </div>
    </div>
  </div>

  <!-- Container principal com tabs -->
  <div class="main-content">
    <mat-tab-group>
      <!-- Tab da Videoconferência -->
      <mat-tab label="Videoconferência">
        <div class="video-container">
          <!-- <PERSON>rea do vídeo -->
          <div class="video-area" *ngIf="FlgVideo && urlSafe">
            <iframe 
              [src]="urlSafe" 
              frameborder="0" 
              allowfullscreen
              allow="camera; microphone" 
              class="video-iframe">
            </iframe>
          </div>

          <!-- Mensagem de carregamento -->
          <div class="loading-video" *ngIf="!videoConnected">
            <mat-icon>videocam</mat-icon>
            <p>Conectando com o paciente...</p>
          </div>

          <!-- Controles do vídeo -->
          
        </div>
      </mat-tab>

      <!-- Tab dos Dados do Paciente -->
      <mat-tab label="Dados do Paciente">
        <div class="dados-paciente">
          <!-- Dados do Questionário -->
          <mat-card *ngIf="getDadosQuestionario()" class="dados-card">
            <mat-card-header>
              <mat-card-title>Questionário Pré-Consulta</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="dados-grid">
                <div class="campo">
                  <label>Nome:</label>
                  <span>{{getDadosQuestionario().nome || 'Não informado'}}</span>
                </div>
                <div class="campo">
                  <label>CPF:</label>
                  <span>{{getDadosQuestionario().cpf || 'Não informado'}}</span>
                </div>
                <div class="campo">
                  <label>Email:</label>
                  <span>{{getDadosQuestionario().email || 'Não informado'}}</span>
                </div>
                <div class="campo">
                  <label>Telefone:</label>
                  <span>{{getDadosQuestionario().telefone || 'Não informado'}}</span>
                </div>
                <div class="campo campo-full">
                  <label>Sintomas:</label>
                  <span>{{getDadosQuestionario().sintomas || 'Não informado'}}</span>
                </div>
                <div class="campo">
                  <label>Intensidade da Dor:</label>
                  <span>{{getDadosQuestionario().intensidadeDor || 'Não informado'}}</span>
                </div>
                <div class="campo">
                  <label>Tempo dos Sintomas:</label>
                  <span>{{getDadosQuestionario().tempoSintomas || 'Não informado'}}</span>
                </div>
                <div class="campo campo-full">
                  <label>Alergias:</label>
                  <span>{{getDadosQuestionario().alergias || 'Nenhuma'}}</span>
                </div>
                <div class="campo campo-full">
                  <label>Doenças Prévias:</label>
                  <span>{{getDadosQuestionario().doencasPrevias || 'Nenhuma'}}</span>
                </div>
                <div class="campo campo-full">
                  <label>Observações:</label>
                  <span>{{getDadosQuestionario().observacoes || 'Nenhuma'}}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Dados da Coleta -->
          <mat-card *ngIf="getDadosColeta()" class="dados-card">
            <mat-card-header>
              <mat-card-title>Dados Vitais Coletados</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="dados-grid">
                <div class="campo">
                  <label>Pressão Arterial:</label>
                  <span>{{getDadosColeta().pressaoArterial || 'Não coletado'}}</span>
                </div>
                <div class="campo">
                  <label>Frequência Cardíaca:</label>
                  <span>{{getDadosColeta().frequenciaCardiaca || 'Não coletado'}}</span>
                </div>
                <div class="campo">
                  <label>Temperatura:</label>
                  <span>{{getDadosColeta().temperatura || 'Não coletado'}}</span>
                </div>
                <div class="campo">
                  <label>Saturação O2:</label>
                  <span>{{getDadosColeta().saturacaoOxigenio || 'Não coletado'}}</span>
                </div>
                <div class="campo">
                  <label>Peso:</label>
                  <span>{{getDadosColeta().peso || 'Não coletado'}}</span>
                </div>
                <div class="campo">
                  <label>Altura:</label>
                  <span>{{getDadosColeta().altura || 'Não coletado'}}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>

          <!-- Mensagem se não há dados -->
          <div *ngIf="!getDadosQuestionario() && !getDadosColeta()" class="sem-dados">
            <mat-icon>info</mat-icon>
            <p>Dados do paciente não disponíveis nesta sessão.</p>
            <p>Token do paciente: {{getTokenPaciente()}}</p>
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- Botões de ação -->
  <div class="actions-bar">
    <button mat-raised-button (click)="voltarParaFilaEspera()">
      <mat-icon>arrow_back</mat-icon>
      Voltar para Fila
    </button>
    
    <button mat-raised-button color="warn" (click)="finalizarConsulta()">
      <mat-icon>call_end</mat-icon>
      Finalizar Consulta
    </button>
  </div>
</div>
