import {
  HubConnectionBuilder,
  LogLevel,
  HubConnection,
  HubConnectionState,
} from '@microsoft/signalr';

import { Injectable, EventEmitter } from '@angular/core';
import { environment } from 'src/environments/environment';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { EnumTipoUsuario } from '../Util/tipoUsuario';

@Injectable({
  providedIn: 'root',
})
export class SignalHubService {
  hubConnection: HubConnection;

  public AtualizaPainelAtendimento: EventEmitter<string> = new EventEmitter<string>();

  public OnDisparaAlertFila: EventEmitter<string> = new EventEmitter<string>();
  public OnDisparaAlertConsulta: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnChamaPacienteFila: EventEmitter<string> = new EventEmitter<string>();
  public OnDisparaAlertFilaClinica: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnAtualizaChamaPacienteFila: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnDisparaAlertNovaConsultaConsulta: EventEmitter<
    string
  > = new EventEmitter<string>();
  public OnVisualizouMensagem: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnLogando: EventEmitter<string> = new EventEmitter<string>();
  public OnMedicosLogados: EventEmitter<string> = new EventEmitter<string>();
  public OnAtualizaMensagem: EventEmitter<number> = new EventEmitter<number>();
  public OnChamaPacienteFilaOrientacao: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnAtualizaMedicosOnline: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnFinalizaConsulta: EventEmitter<string> = new EventEmitter<string>();
  public OnFinalizaOrientacao: EventEmitter<string> = new EventEmitter<
    string
  >();
  public OnAtualizaQtdMensagem: EventEmitter<void> = new EventEmitter<void>();
  public OnAlertAnexo: EventEmitter<string> = new EventEmitter<string>();

  // Event Emitters para comunicação médico-paciente
  public OnSolicitacaoDadosPaciente: EventEmitter<any> = new EventEmitter<any>();
  public OnDadosPacienteRecebidos: EventEmitter<any> = new EventEmitter<any>();

  public changeConsulta$: EventEmitter<any>;
  constructor(private usuarioLogadoService: UsuarioLogadoService) {
    const buildHubConn = new HubConnectionBuilder();
    buildHubConn.configureLogging(LogLevel.Information);
    buildHubConn.withUrl(environment.apiEndpoint + '/notify');
    // buildHubConn.withAutomaticReconnect();
    this.hubConnection = buildHubConn.build();
    // this.hubConnection.serverTimeoutInMilliseconds = 10000;

    this.changeConsulta$ = new EventEmitter();
    this.initializeSignalIR();
  }

  private sendUsuarioSignalR(usuario: any) {
    if (usuario) {
      const user = {
        idUsuario: usuario.idUsuarioAcesso,
        idClinica: usuario.idUltimaClinica,
        idTipoUsuario: usuario.idTipoUsuario,
        Token: usuario.tokenLogado,
      };
      this.hubConnection.send('UserLogado', user);
    }
  }

  public loginUsuario(usuario: any = null) {
    if (usuario) {
      this.sendUsuarioSignalR(usuario);
    } else {
      this.usuarioLogadoService.getUsuarioLogado()
        .subscribe((u) => {
          ;
          this.sendUsuarioSignalR(u);
        });
    }
  }


  private qtdTentativaConectarApi = 0; //Contador - a cada 3 tentativas o tempo q leva para tentar novamente conectar a api aumenta
  private tempoBaseConectarApi = 1000; //tempo inicial para conectar a api

  private async startSignalR() {
    if (!this.usuarioLogadoService.getFlgLogado()) {
      ;
      return;
    }

    return this.hubConnection
      .start()
      .then(async () => {
        this.tempoBaseConectarApi = 1000;
        this.qtdTentativaConectarApi = 0; //A tentativa de conexão foi um sucesso reseto as tentativas.
        this.loginUsuario();
        this.changeConsulta$.emit(true);
      })
      .catch((err) => {
        console.error("Start Error!", err);

        // Depois de 6 tentativas o tempo base vira minuto
        if (this.qtdTentativaConectarApi == 3 && this.tempoBaseConectarApi < 300000) {
          this.qtdTentativaConectarApi = 1
          this.tempoBaseConectarApi = 300000
        }

        // Aumenta o tempo de espera a cada 3 falhas
        this.qtdTentativaConectarApi++;
        const delay = this.tempoBaseConectarApi * Math.pow(2, Math.floor(this.qtdTentativaConectarApi / 3));
        ;


        setTimeout(() => this.startSignalR(), delay);
      });
  }

  private async initializeSignalIR() {
    const self = this;

    this.startSignalR();

    this.hubConnection.onclose(() => {

      ;
      setTimeout(() => self.startSignalR()

        , 1000);

    });

    this.hubConnection.on('DisparaAlertFila', (res: string) => {
      if (
        this.usuarioLogadoService.getIdTipoUsuario() === EnumTipoUsuario.Médico
      ) {
        this.OnDisparaAlertFila.emit(res);
      }
    });

    this.hubConnection.on('MedicosLogados', (res: string) => {
      this.OnMedicosLogados.emit(res);
    });

    this.hubConnection.on('AtualizaMensagem', (res: number) => {
      this.OnAtualizaMensagem.emit(res);
    });

    this.hubConnection.on('AtualizaMedicosOnline', (res: string) => {
      this.OnAtualizaMedicosOnline.emit(res);
    });

    this.hubConnection.on('DisparaAlertConsulta', (res: string) => {
      this.OnDisparaAlertConsulta.emit(res);
    });

    this.hubConnection.on('ChamaPacienteFila', (res: string) => {
      this.OnChamaPacienteFila.emit(res);
    });

    this.hubConnection.on('ChamaPacienteFilaOrientacao', (res: string) => {
      this.OnChamaPacienteFilaOrientacao.emit(res);
    });

    this.hubConnection.on('DisparaAlertFilaClinica', (res: string) => {
      this.OnDisparaAlertFilaClinica.emit(res);
    });

    this.hubConnection.on('AtualizaPainelAtendimento', () => {
      this.AtualizaPainelAtendimento.emit();
    });

    this.hubConnection.on('DisparaAlertNovaConsultaConsulta', (res: string) => {
      this.OnDisparaAlertNovaConsultaConsulta.emit(res);
    });

    this.hubConnection.on('AtualizaChamaPacienteFila', (res: string) => {
      this.OnAtualizaChamaPacienteFila.emit(res);
    });

    this.hubConnection.on('AlertAnexo', (res: string) => {
      this.OnAlertAnexo.emit(res);
    });
    this.hubConnection.on('FinalizaConsulta', (res: string) => {
      this.OnFinalizaConsulta.emit(res);
    });

    this.hubConnection.on('FinalizaOrientacao', (res: string) => {
      this.OnFinalizaOrientacao.emit(res);
    });
    this.hubConnection.on('VisualizouMensagem', (res) => {
      if (this.usuarioLogadoService.getIdUsuarioAcesso() === res || res == null) {
        this.OnVisualizouMensagem.emit(res);
      }
    });

    this.hubConnection.on('Logando', (res) => {
      this.OnLogando.emit(res);
    });

    // Listeners para comunicação médico-paciente
    this.hubConnection.on('SolicitarDadosPaciente', (medicoId: string, tokenPaciente: string) => {
      console.log('Solicitação de dados recebida do médico:', { medicoId, tokenPaciente });
      this.OnSolicitacaoDadosPaciente.emit({ medicoId, tokenPaciente });
    });

    this.hubConnection.on('DadosPacienteRecebidos', (dadosPaciente: any) => {
      console.log('Dados do paciente recebidos:', dadosPaciente);
      this.OnDadosPacienteRecebidos.emit(dadosPaciente);
    });
  }

  public enviaServer(metodo: string, ...args: any[]) {
    this.hubConnection.send(metodo, args);
  }

  /**
   * Verifica se a conexão SignalR está ativa
   */
  public isConnected(): boolean {
    return this.hubConnection && this.hubConnection.state === HubConnectionState.Connected;
  }

  /**
   * Aguarda até que a conexão SignalR esteja estabelecida
   * @param maxWaitTime Tempo máximo de espera em milissegundos (padrão: 10 segundos)
   * @returns Promise que resolve quando conectado ou rejeita se timeout
   */
  public async waitForConnection(maxWaitTime: number = 10000): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnected()) {
        resolve();
        return;
      }

      const startTime = Date.now();
      const checkConnection = () => {
        if (this.isConnected()) {
          resolve();
        } else if (Date.now() - startTime > maxWaitTime) {
          reject(new Error('Timeout: Não foi possível estabelecer conexão SignalR'));
        } else {
          setTimeout(checkConnection, 100); // Verifica a cada 100ms
        }
      };

      checkConnection();
    });
  }

  /**
   * Força uma nova tentativa de conexão se não estiver conectado
   */
  public async ensureConnection(): Promise<void> {
    if (!this.isConnected()) {
      console.log('🔄 Conexão SignalR não está ativa. Tentando reconectar...');
      await this.startSignalR();
      await this.waitForConnection();
    }
  }

  // Convida paciente para reunião via SignalR usando ID da consulta
  public async convidarPacienteParaReuniao(idConsulta: number): Promise<void> {
    try {
      // Verificar se a conexão está ativa
      if (!this.isConnected()) {
        console.log('⚠️ Conexão SignalR não está ativa. Tentando estabelecer conexão...');
        await this.ensureConnection();
      }

      // Enviar convite
      console.log(`📞 Enviando convite para consulta ID: ${idConsulta}`);
      await this.hubConnection.invoke('ConvidarPacienteParaReuniao', idConsulta);
      console.log('✅ Convite enviado com sucesso!');
    } catch (err) {
      console.error('❌ Erro ao convidar paciente para reunião:', err);
      throw err; // Re-throw para que o componente possa tratar o erro
    }
  }

  // Convida paciente para reunião via SignalR usando token de conexão
  public async convidarPacienteComToken(token: string, nomePaciente?: string): Promise<void> {
    try {
      // Verificar se a conexão está ativa
      if (!this.isConnected()) {
        console.log('⚠️ Conexão SignalR não está ativa. Tentando estabelecer conexão...');
        await this.ensureConnection();
      }

      // Enviar convite
      console.log(`📞 Enviando convite para ${nomePaciente || 'paciente'} com token: ${token}`);
      await this.hubConnection.invoke('ConvidarPacienteComToken', token, nomePaciente);
      console.log('✅ Convite enviado com sucesso!');
    } catch (err) {
      console.error('❌ Erro ao convidar paciente com token:', err);
      throw err; // Re-throw para que o componente possa tratar o erro
    }
  }

  // Método para solicitar dados do paciente (usado pelo médico)
  public async solicitarDadosPaciente(tokenPaciente: string): Promise<void> {
    try {
      if (!this.isConnected()) {
        console.log('⚠️ Conexão SignalR não está ativa. Tentando estabelecer conexão...');
        await this.ensureConnection();
      }

      const medicoId = this.usuarioLogadoService.getIdUsuarioAcesso()?.toString() || 'medico';
      console.log(`📋 Solicitando dados do paciente com token: ${tokenPaciente}`);
      await this.hubConnection.invoke('SolicitarDadosPaciente', medicoId, tokenPaciente);
      console.log('✅ Solicitação de dados enviada com sucesso!');
    } catch (err) {
      console.error('❌ Erro ao solicitar dados do paciente:', err);
      throw err;
    }
  }

  // Método para enviar dados do paciente para o médico (usado pelo paciente)
  public async enviarDadosParaMedico(medicoId: string, dadosPaciente: any): Promise<void> {
    try {
      if (!this.isConnected()) {
        console.log('⚠️ Conexão SignalR não está ativa. Tentando estabelecer conexão...');
        await this.ensureConnection();
      }

      console.log(`📤 Enviando dados para o médico: ${medicoId}`);
      await this.hubConnection.invoke('EnviarDadosPaciente', medicoId, dadosPaciente);
      console.log('✅ Dados enviados com sucesso!');
    } catch (err) {
      console.error('❌ Erro ao enviar dados para o médico:', err);
      throw err;
    }
  }
}

