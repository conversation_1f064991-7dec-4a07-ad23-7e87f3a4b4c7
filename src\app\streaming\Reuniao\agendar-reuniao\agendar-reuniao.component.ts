import { AlertComponent } from './../../../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { ReuniaoService } from 'src/app/service/reuniaoService.service';
import { animate, transition, state, style, trigger } from '@angular/animations';
import { SpinnerService } from 'src/app/service/spinner.service';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-agendar-reuniao',
    templateUrl: './agendar-reuniao.component.html',
    styleUrls: ['./agendar-reuniao.component.scss'],
    animations: [
        trigger('openClose', [
            state('open', style({
                opacity: '1',
                display: 'block'
            })),
            state('closed', style({
                opacity: '0',
                display: 'none'
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ]),
        ])
    ],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      NgxSmartModalModule,
      MatCardModule,
      TranslateModule
    ]
})


export class AgendarReuniaoComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    public usuarioLogadoService: UsuarioLogadoService,
    private router: Router,
    public reuniaoService: ReuniaoService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent
  ) { }


  toggle:any = {}


  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  modalInfo: any = [];
  Motivocancelameto = ''
  idCancela?: number;
  qtdRegistros = 10;
  DadosSolicitacao: any = [];
  bOcultaCarregaMais = false;
  cancelamento = false;
  ConsultaAgoraTeste: boolean = false;
  Objconsulta: any = [];
  PosicaoFila: number = 0;
  MedicosOnline: number = 0;
  idConsulta?: number;
  DadosInformCancelament: any = [];
  url_atual?: string;
  irConsulta?: boolean;

  ngOnInit() {
    this.url_atual = window.location.origin;
    this.CarregaSolicitacoes()
  }


  CarregaSolicitacoes() {
    this.reuniaoService.CarregaReunião(this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica(), 0, this.qtdRegistros).subscribe((retorno) => {

      this.DadosSolicitacao = retorno
      
      

      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })

  }
  async ConsultaSolicitar() {
    this.ngxSmartModalService.getModal('SolicitarConsulta').open();
  }
  copyMessage(val: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.snackBarAlert.sucessoSnackbar("Link copiado!")
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  CarregaReuniao(id:any, codAcesso:any) {
    if (id) {
      this.reuniaoService.GetReuniaoGuid(id, codAcesso).subscribe((retorno) => {
        if (retorno == null) {
          this.spinner.hide();
          return;
        }
        else {
          this.spinner.hide();
          const url = this.router.serializeUrl(
            this.router.createUrlTree(['/reuniao'])
          );

          window.open(url, '_blank');
        }

      });
    }
  }


  modalInformativo(id:any) {
    var info = this.DadosSolicitacao.filter((c:any) => c.idReuniao == id);
    this.modalInfo.idConsulta = id;
    this.modalInfo.guid = this.url_atual + "/acessoreuniao/" + info[0].idReuniao;
    this.modalInfo.CodAcesso = info[0].codAcesso;
    this.ngxSmartModalService.getModal('infoConsulta').open();

  }

  CarregarMais()
  {
    
  }
  
}
