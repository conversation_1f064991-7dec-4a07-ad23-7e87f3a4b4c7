export class ProcedimentoModelview {
    idProcedimento?: number;
    dtaPeriodoInicio?: Date;
    dtaPeriodoFim?: Date;
    flgLancarItens?: boolean;
    idStatusProcedimento?: number;
    desProcedimento?: string;
    qtd?: number;
    vlrM2filme?: number;
    vlrCustoOp?: number;
    vlrHonorarios?: number;
    vlrParticular?: number;
    vlrConvenio?: number;
    desCaracteristicaProcedimento?: string;
    flgInativo?: boolean;
    dtaCadastro?: Date;
    codProcedimento?: string;
    idFatura?: number;
    desFatura?: string;
    idConvenio?: number;
    desConvenio?: string;
    idConsulta?: number;
    dtaInicio?: Date;
    idGuiaTiss?: number;
    idUsuarioMedico?: number;
    nomeMedico?: string;
    idUsuarioPaciente?: number;
    nomePaciente?: string;
}