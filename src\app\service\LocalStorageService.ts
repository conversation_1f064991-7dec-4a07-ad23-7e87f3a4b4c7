import { Injectable } from '@angular/core';
import { CryptoService } from './crypto.service';
import { irParaConsulta } from '../model/consulta';

@Injectable({
	providedIn: 'root'
})
export class LocalStorageService {

	constructor(private cryptoService: CryptoService) {


	}

	private decryptSession(name: string): any {
		try {
			return JSON.parse(this.cryptoService.decrypt(sessionStorage.getItem(name)!));
		} catch {
			return undefined;
		}
	}

	private encryptSession(name: string, value: any) {
		if (!value) {
			this.clearByName(name);
		} else {
			sessionStorage.setItem(name, this.cryptoService.encrypt(JSON.stringify(value)));
		}
	}

	public clear() {
		localStorage.clear();
		sessionStorage.clear();
	}

	public clearByName(name: string) {
		localStorage.removeItem(name);
		sessionStorage.removeItem(name);
	}


	private decrypt(name: string): any {
		try {
			return JSON.parse(this.cryptoService.decrypt(localStorage.getItem(name)!));
		} catch {
			return undefined;
		}
	}

	private encrypt(name: string, value: any) {
		if (!value) {
			this.clearByName(name);
		} else {
			localStorage.setItem(name, this.cryptoService.encrypt(JSON.stringify(value)));
		}
	}

	hasUsuarioLogado() {
		return !!this.getUsuarioLogado();
	}

	setUsuarioLogado(usuario: any) {
		this.encrypt("UserLogado", usuario);
	}

	getUsuarioLogado() {
		return this.decrypt("UserLogado");
	}

	//UsuarioLogado
	get UsuarioLogado(): any {
		return this.decrypt("UsuarioLogado");
	}

	set UsuarioLogado(value: any) {
		this.encrypt("UsuarioLogado", value);
	}

	get token(): string {
		return this.decrypt('userToken') || '';
	}

	set token(value: string) {
		this.encrypt('userToken', value);
	}

	//Logado
	get Logado(): boolean {
		return this.decrypt('Logado') || false;
	}

	set Logado(value: boolean) {
		this.encrypt('Logado', value);
	}

	// flgModalChatAberta
	get flgModalChatAberta(): boolean {
		return this.decrypt('flgModalChatAberta') || false;
	}

	set flgModalChatAberta(value: boolean) {
		this.encrypt('flgModalChatAberta', value);
	}
	// flgNotificacaoWindows
	get flgNotificacaoWindows(): boolean {
		return this.decrypt('flgNotificacaoWindows') || false;
	}

	set flgNotificacaoWindows(value: boolean) {
		this.encrypt('flgNotificacaoWindows', value);
	}

	// flgNotificacaoSonora
	get flgNotificacaoSonora(): boolean {
		return this.decrypt('flgNotificacaoSonora') || false;
	}

	set flgNotificacaoSonora(value: boolean) {
		this.encrypt('flgNotificacaoSonora', value);
	}

	//Linguagem
	get Linguagem(): string {
		return this.decrypt('Linguagem') || '';
	}

	set Linguagem(value: string) {
		this.encrypt('Linguagem', value);
	}

	//TelaPrivacidadeLogin
	get TelaPrivacidadeLogin(): string {
		return this.decrypt('TelaPrivacidadeLogin') || '';
	}

	set TelaPrivacidadeLogin(value: string) {
		this.encrypt('TelaPrivacidadeLogin', value);
	}


	//Session

	//idConsulta
	get reuniao(): number {
		return this.decryptSession('Reuniao') || false;

	}
	set reuniao(value: number) {
		this.encryptSession('Reuniao', value);
	}


	get Consulta(): irParaConsulta {
		return this.decryptSession('Consulta') || false;
	}

	set Consulta(value: irParaConsulta) {
		this.encryptSession('Consulta', value);
	}

	//idAtendente
	get idAtendente(): string {
		return this.decryptSession('idAtendente') || false;
	}
	set idAtendente(value: string) {
		this.encryptSession('idAtendente', value);
	}

	//idClinica
	get idClinica(): string {
		return this.decryptSession('idClinica') || false;
	}
	set idClinica(value: string) {
		this.encryptSession('idClinica', value);
	}

	//idPerfil
	get idPerfil(): string {
		return this.decryptSession('idPerfil') || false;
	}
	set idPerfil(value: string) {
		this.encryptSession('idPerfil', value);
	}

	//ConsultaAgenda
	get ConsultaAgenda(): any {
		return this.decryptSession('ConsultaAgenda') || false;
	}
	set ConsultaAgenda(value: any) {
		this.encryptSession('ConsultaAgenda', value);
	}

	//idMedico
	get idMedico(): string {
		return this.decryptSession('idMedico') || false;
	}
	set idMedico(value: string) {
		this.encryptSession('idMedico', value);
	}

	//idFormularios
	get idFormularios(): number {
		return this.decryptSession('idFormularios') || false;
	}
	set idFormularios(value: number | null) {
		this.encryptSession('idFormularios', value);
	}

	//idLocal
	get idLocal(): number {
		return this.decryptSession('idLocal') || false;
	}
	set idLocal(value: number | null) {
		this.encryptSession('idLocal', value);
	}

	//idSala
	get idSala(): number | null {
		return this.decryptSession('idSala') || null;
	}
	set idSala(value: number | null) {
		this.encryptSession('idSala', value);
	}

	//idItem
	get idItem(): number {
		return this.decryptSession('idItem') || false;
	}
	set idItem(value: number | null) {
		this.encryptSession('idItem', value);
	}

	//idReceita
	get idReceita(): number {
		return this.decryptSession('idReceita') || false;
	}
	set idReceita(value: number | null) {
		this.encryptSession('idReceita', value);
	}

	//idCliente
	get idCliente(): string {
		return this.decryptSession('idCliente') || false;
	}
	set idCliente(value: string) {
		this.encryptSession('idCliente', value);
	}

	//idConvenio
	get idConvenio(): string {
		return this.decryptSession('idConvenio') || false;
	}
	set idConvenio(value: string) {
		this.encryptSession('idConvenio', value);
	}

	// qtdMensagemChat
	get qtdMensagemChat(): number | null {
		const value = this.decryptSession('qtdMensagemChat');
		return value !== null ? Number(value) : null;
	}

	set qtdMensagemChat(value: number | null) {
		if (value === null || value === 0) {
			this.encryptSession('qtdMensagemChat', null);
		} else {
			this.encryptSession('qtdMensagemChat', value);
		}
	}

	get idAnalise(): number {
		return this.decryptSession('idAnalise') || null;
	}
	set idAnalise(value: number) {
		this.encryptSession('idAnalise', value);
	}

	// Buscar Usuário
	getIdPessoaUsuarioLogado(): number | null {
		const buscarUsuario = this.getUsuarioLogado();
		if (!buscarUsuario) {
			console.error('Nenhum usuário logado encontrado ou falha na descriptografia');
			return null;
		}
		if (buscarUsuario && buscarUsuario.idPessoa) {
			return buscarUsuario.idPessoa;
		}
		console.error('idPessoa não encontrado no objeto de usuário logado');
		return null;
	}
}
