<!-- <PERSON>u lateral, fechado e aberto -->
<mat-drawer-container class="menu-container " style="background-color: #f6f9fe;">
  <mat-drawer #drawer class="sidenav" id="sidenav" role="navigation" mode="side" [opened]="true" [class.active]="isOpen"
    style="border-right: none !important;">
    <div class="contanier">
      <!--B icone Bonecare -->
      <p (click)="Inicio();"
        style="color: #1F5F3D; text-align: center; font-size: 32px; font-weight: 800; margin: 8px; margin-left: 10px; padding: 0; cursor: pointer;">
        B</p>
      <!--Seta pra abrir -->
      <div class="align-content" style="text-align: center;" (click)="toggleSidebar()">
        <li class="space menuA">
          <mat-icon class="seta" style="color: #fff !important;"
            [ngClass]="{'arrow_forward_ios': !FillerOpen, 'arrow_back_ios': Filler<PERSON><PERSON>}">
            menu
          </mat-icon>
        </li>
      </div>

      <!--Menu-->
      <div class="menu-scroll">
        <ul>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('')}" style="margin-top: 0px !important;"
            *ngIf="!FillerOpen" (click)="Inicio();" matTooltip="{{ 'TELAMENU.TITULO' | translate }}"
            matTooltipPosition="right">
            <mat-icon>home</mat-icon>
            <span>Home</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('formularios')}" *ngIf="!FillerOpen && MedicoPermissao" (click)="Formularios();"
            matTooltip="Formulários" matTooltipPosition="right">
            <mat-icon >assignment_turned_in</mat-icon>
            <span>Formulários</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('perfilClinica')}" *ngIf="!FillerOpen && MedicoPermissao" (click)="PerfilClinica();"
            matTooltip="{{ 'TELAMENU.TITULO' | translate }}" matTooltipPosition="right">
            <mat-icon >domain</mat-icon>
            <span>Clínica</span>
          </li>
          <li class="paginas menuA" *ngIf="!FillerOpen && MedicoPermissao" (click)="AbrirChat();"
            matTooltip="Chat" matTooltipPosition="right">
            <mat-icon >forum</mat-icon>
            <span>Chat</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('perfil')}" *ngIf="!FillerOpen"
            (click)="Perfil();" matTooltip="Perfil" matTooltipPosition="right">
            <mat-icon>person</mat-icon>
            <span>Perfil</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('analisepaciente') || isActive('analise')}"
            *ngIf="!FillerOpen" (click)="analise();" matTooltip="Análise" matTooltipPosition="right">
            <mat-icon>medical_services</mat-icon>
            <span>Análise</span>
          </li>
<li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('fila-espera-medico')}" *ngIf="!FillerOpen && MedicoPermissao" (click)="IrParaFilaEspera();"
            matTooltip="Fila de Espera" matTooltipPosition="right">
            <mat-icon>people_alt</mat-icon>
            <span>Fila de Espera</span>
          </li>
          <li *ngIf="!FillerOpen && MedicoPermissao">
            <ul>
              <li class="paginas menuA" id="buttonMenu"
                [ngClass]="{'activeMenu ': isActive('pesquisamedicos') || isActive('Atendente') || isActive('pesquisapacientes') || isActive('clinicas') || isActive('cadastroitens') 
              || isActive('cadastroreceitas') || isActive('pesquisaconvenio') || isActive('lista-faturas') || isActive('listagemsalas')}" *ngIf="!FillerOpen"
                (click)="expanpadir(1);" matTooltip="Cadastro" matTooltipPosition="right">
                <mat-icon matBadgeColor="primary">group_add</mat-icon>
                <span>Cadastro</span>
              </li>
              <li>
                <div class="menuExpande" id="menuExpande"  [class.aparecer]="abrir1">
                  <ul>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Medicos();"
                      matTooltip="{{ 'TELAMENU.MEDICOS' | translate }}" matTooltipPosition="right">
                      <mat-icon>local_hospital</mat-icon>
                      <a>{{ 'TELAMENU.MEDICOS' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Usuario();"
                      matTooltip="{{ 'TELAMENU.ATENDIMENTO' | translate }}" matTooltipPosition="right">
                      <mat-icon>face</mat-icon>
                      <a>{{ 'TELAMENU.ATENDIMENTO' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Pacientes();"
                      matTooltip="{{ 'TELAMENU.PACIENTES' | translate }}" matTooltipPosition="right">
                      <mat-icon>group</mat-icon>
                      <a>{{ 'TELAMENU.PACIENTES' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="cpfUsuario == 99999999999" (click)="clinica();"
                      matTooltip="{{ 'TELAMENU.CLINICAS' | translate }}" matTooltipPosition="right">
                      <mat-icon>queue</mat-icon>
                      <a>{{ 'TELAMENU.CLINICAS' | translate }}</a>
                    </li>
                    <!-- <li class="menuA buttonExpandido"  *ngIf="MedicoPermissao" (click)="cadastrolocais();"
                        matTooltip="{{ 'TELAMENU.TITULO' | translate }}" matTooltipPosition="right">
                        <mat-icon >near_me</mat-icon>
                        <a>Locais</a>
                      </li> -->
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="cadastroitens();"
                      matTooltip="Itens" matTooltipPosition="right">
                      <mat-icon>post_add</mat-icon>
                      <a>Itens</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="cadastroreceitas();"
                      matTooltip="Receitas" matTooltipPosition="right">
                      <mat-icon>description</mat-icon>
                      <a>Receitas</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="AtendentePermissao" (click)="Convenio();"
                      matTooltip="convênio" matTooltipPosition="right">
                      <mat-icon>feed</mat-icon>
                      <a>Convenio</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="AtendentePermissao" (click)="IrListaFaturas();"
                      matTooltip="Faturas" matTooltipPosition="right">
                      <mat-icon>text_snippet</mat-icon>
                      <a>Faturas</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="AtendentePermissao" (click)="IrListaSalas();"
                      matTooltip="Salas" matTooltipPosition="right">
                      <mat-icon>sensor_door</mat-icon>
                      <a>Salas</a>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </li>
          <li *ngIf="MedicoPermissao">
            <ul>
              <li class="paginas menuA" id="buttonMenu2"
                [ngClass]="{'activeMenu ': isActive('relatorios') || isActive('historicoUsuarios') || isActive('historicoRecados')}"
                [class.active3]="abrir1" *ngIf="!FillerOpen" (click)="expanpadir(2);" matTooltip="Relatorio"
                matTooltipPosition="right">
                <mat-icon matBadgeColor="primary">toc</mat-icon>
                <span>Relatorio</span>
              </li>
              <li>
                <div class="menuExpande" id="menuExpande" [class.aparecer]="abrir2">
                  <ul>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Relatorios();"
                      matTooltip="Relatório Clínica" matTooltipPosition="right">
                      <mat-icon>note_add</mat-icon>
                      <a>{{ 'TELAMENU.RELATORIOS' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="RelatoriosUser();"
                      matTooltip="Relátorio do Usuário" matTooltipPosition="right">
                      <mat-icon>note_add</mat-icon>
                      <a>Relátorio do Usuário</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="RecadosDia();"
                      matTooltip="Relatório do dia" matTooltipPosition="right">
                      <mat-icon>note_add</mat-icon>
                      <a>Relatório do dia</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="RelatorioAgendamentos();"
                      matTooltip="Relatório Agendamento" matTooltipPosition="right">
                      <mat-icon>note_add</mat-icon>
                      <a>Relatório Agendamento </a>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </li>
          <li *ngIf="!FillerOpen && MedicoPermissao">
            <ul>
              <li class="paginas menuA" id="buttonMenu3" [class.active4]="abrir2" *ngIf="!FillerOpen"
                (click)="expanpadir(3);" matTooltip="Guias" matTooltipPosition="right">
                <mat-icon matBadgeColor="primary">book</mat-icon>
                <span>Guias</span>
              </li>
              <li>
                <div class="menuExpande" id="menuExpande" [class.aparecer]="abrir3">
                  <ul>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Atestado();"
                      matTooltip="{{ 'TELADOCUMENTACAO.ATESTADO' | translate }}" matTooltipPosition="right">
                      <mat-icon>note_add</mat-icon>
                      <a>{{ 'TELADOCUMENTACAO.ATESTADO' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Declaracao();"
                      matTooltip="{{ 'TELADOCUMENTACAO.DECLARACAO' | translate }}" matTooltipPosition="right">
                      <mat-icon>assignment</mat-icon>
                      <a>{{ 'TELADOCUMENTACAO.DECLARACAO' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Receituario();"
                      matTooltip="{{ 'TELADOCUMENTACAO.RECEITUARIO' | translate }}" matTooltipPosition="right">
                      <mat-icon>subject</mat-icon>
                      <a>{{ 'TELADOCUMENTACAO.RECEITUARIO' | translate }}</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="GuiaExames();"
                      matTooltip="Guia de exames" matTooltipPosition="right">
                      <mat-icon>search</mat-icon>
                      <a>Guia de exames</a>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </li>
          <li *ngIf="!FillerOpen && MedicoPermissao">
            <ul>
              <li class="paginas menuA"
                [ngClass]="{'activeMenu ': isActive('/lista-faturas') || isActive('pesquisaconvenio') || isActive('lista-guia-tiss')}"
                id="buttonMenu4" [class.active5]="abrir3" *ngIf="!FillerOpen" (click)="expanpadir(4);"
                matTooltip="Faturamento" matTooltipPosition="right">
                <mat-icon matBadgeColor="primary">request_quote</mat-icon>
                <span>Faturamento</span>
              </li>
              <li>
                <div class="menuExpande" id="menuExpande" [class.aparecer]="abrir4">
                  <ul>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Faturas();" matTooltip="Faturas"
                      matTooltipPosition="right">
                      <mat-icon>description</mat-icon>
                      <a>Faturas</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="Convenio();"
                      matTooltip="Convenio" matTooltipPosition="right">
                      <mat-icon>receipt_long</mat-icon>
                      <a>Convenio</a>
                    </li>
                    <li class="menuA buttonExpandido" *ngIf="MedicoPermissao" (click)="GuiaTiss();"
                      matTooltip="GuiaTiss" matTooltipPosition="right">
                      <mat-icon>description</mat-icon>
                      <a>GuiaTiss</a>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('consulta')}" *ngIf="!FillerOpen"
            [class.active6]="abrir4" (click)="Consulta();" matTooltip="Consulta" matTooltipPosition="right">
            <mat-icon>find_in_page</mat-icon>
            <span>Consulta</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('prontuario')}"
            *ngIf="cpfUsuario == '99999999999'" (click)="prontuarioVideos();" matTooltip="Prontuário"
            matTooltipPosition="right">
            <mat-icon>assignment</mat-icon>
            <span>Prontuário</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('streaming') || isActive('consultaMobile')}"
            *ngIf="cpfUsuario == '99999999999'" (click)="StreamVideos();"
            matTooltip="{{ 'TELAMENU.STREAMVIDEOS' | translate }}" matTooltipPosition="right">
            <mat-icon>movie</mat-icon>
            <span>{{ 'TELAMENU.STREAMVIDEOS' | translate }}</span>
          </li>
          <li class="paginas menuA" [ngClass]="{'activeMenu ': isActive('contatos')}" *ngIf="MedicoPermissao"
            (click)="Contatos();" matTooltip="Contato" matTooltipPosition="right">
            <mat-icon>contact_phone</mat-icon>
            <span>Contato</span>
          </li>
          <!-- <li class="paginas menuA" *ngIf="MedicoPermissao" (click)="Medicamentos();"
            matTooltip="{{ 'TELAMENU.TITULO' | translate }}" matTooltipPosition="right">
            <mat-icon >medication</mat-icon>
            <span>Medicamentos</span>
          </li> -->
          <li class="paginas menuA" *ngIf="MedicoPermissao" (click)="Painel();" matTooltip="Painel"
            matTooltipPosition="right">
            <mat-icon>table</mat-icon>
            <span>Painel</span>
          </li>
        </ul>
      </div>
    </div>

    <!--fechado Cadastros-->
    <div class="align-content" style="top: -33px">
        <div  class="expanpadirFecahdo" id="expanpadirFecahdo" [class.disabled]="isDisabled1">
          <div class="menuFechado">
            <div class="list-item  ">
              <mat-list-item class="menu-item  sub-menu1" (click)="Medicos();">
                <mat-icon class="mat-icon-submenu color-text">local_hospital</mat-icon>
                <a class="text-menu color-text">{{ 'TELAMENU.MEDICOS' | translate }}</a>
              </mat-list-item>
            </div>
            <!--Usuario-->
            <div class="list-item  " *ngIf="MedicoPermissao">
              <mat-list-item class="menu-item sub-menu1" (click)="Usuario();">
                <mat-icon class="mat-icon-submenu color-text">face</mat-icon>
                <a class="text-menu color-text">{{ 'TELAMENU.ATENDIMENTO' | translate }}</a>
              </mat-list-item>
            </div>
            <!--Pacientes-->
            <div class="list-item  " *ngIf="MedicoPermissao">
              <mat-list-item class="menu-item  sub-menu1" (click)="Pacientes();">
                <mat-icon class="mat-icon-submenu color-text">group</mat-icon>
                <a class="text-menu color-text">{{ 'TELAMENU.PACIENTES' | translate }}</a>
              </mat-list-item>
            </div>
            <!--clinica-->
            <div class="list-item  " *ngIf="cpfUsuario == 99999999999">
              <mat-list-item class="menu-item sub-menu1" (click)="clinica();">
                <mat-icon class="mat-icon-submenu color-text">queue</mat-icon>
                <a class="text-menu color-text">{{ 'TELAMENU.CLINICAS' | translate }}</a>
              </mat-list-item>
            </div>
            <!--Locais-->
            <!-- <div class="list-item  " *ngIf="MedicoPermissao">
              <mat-list-item class="menu-item sub-menu1" (click)="cadastrolocais();">
                <mat-icon class="mat-icon-submenu color-text">near_me</mat-icon>
                <a class="text-menu color-text">Locais</a>
              </mat-list-item>
            </div> -->
          <!--Itens-->
          <div class="list-item  " *ngIf="MedicoPermissao">
            <mat-list-item class="menu-item sub-menu1" (click)="cadastroitens();">
              <mat-icon class="mat-icon-submenu color-text">post_add</mat-icon>
              <a class="text-menu color-text">Itens</a>
            </mat-list-item>
          </div>
          <!--Receitas-->
          <div class="list-item  " *ngIf="MedicoPermissao">
            <mat-list-item class="menu-item sub-menu1" (click)="cadastroreceitas();">
              <mat-icon class="mat-icon-submenu color-text">description</mat-icon>
              <a class="text-menu color-text">Receitas</a>
            </mat-list-item>
          </div>

          <div class="list-item  " *ngIf="AtendentePermissao">
            <mat-list-item class="menu-item sub-menu1" (click)="Convenio();">
              <mat-icon class="mat-icon-submenu color-text">feed</mat-icon>
              <a class="text-menu color-text">Convenio</a>
            </mat-list-item>
          </div>

          <div class="list-item  " *ngIf="AtendentePermissao">
            <mat-list-item class="menu-item sub-menu1" (click)="IrListaFaturas();">
              <mat-icon class="mat-icon-submenu color-text">text_snippet</mat-icon>
              <a class="text-menu color-text">Faturas</a>
            </mat-list-item>
          </div>

          <div class="list-item  " *ngIf="AtendentePermissao">
            <mat-list-item class="menu-item sub-menu1" (click)="IrListaSalas();">
              <mat-icon class="mat-icon-submenu color-text">sensor_door</mat-icon>
              <a class="text-menu color-text">Salas</a>
            </mat-list-item>
          </div>
        </div>
      </div>
    </div>
    <!--submenu fechado Relatorio -->
    <div class=" mat-menu-adjust">
      <div class="expanpadirFecahdo modal2" id="expanpadirFecahdo" [class.disabled]="isDisabled2">
        <div class="menuFechado">
          <div class="list-item  " *ngIf="MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="Relatorios()">
              <mat-icon class="mat-icon-submenu color-text">note_add</mat-icon>
              <a class="text-menu color-text">{{'TELAMENU.RELATORIOSCLINICA' | translate}}</a>
            </mat-list-item>
          </div>
          <!--Relatorio Usuario-->
          <div class="list-item  " *ngIf="MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="RelatoriosUser()">
              <mat-icon class="mat-icon-submenu color-text">note_add</mat-icon>
              <a class="text-menu color-text">{{'TELAMENU.RELATORIOSUSUARIO' | translate}}</a>
            </mat-list-item>
          </div>
          <!--Relatorio recado-->
          <div class="list-item  " *ngIf="MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="RecadosDia()">
              <mat-icon class="mat-icon-submenu color-text">note_add</mat-icon>
              <a class="text-menu color-text">Relatório recados do dia</a>
            </mat-list-item>
          </div>
          <!--Relatorio Agendamento-->
          <div class="list-item  " *ngIf="MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="RelatorioAgendamentos()">
              <mat-icon class="mat-icon-submenu color-text">note_add</mat-icon>
              <a class="text-menu color-text">Relatório Agendamento</a>
            </mat-list-item>
          </div>
        </div>
      </div>
    </div>


    <!--submenu fechado Guias -->
    <div class="align-content" *ngIf="!FillerOpen && MedicoPermissao" matTooltip="Guias" matTooltipPosition="right">
      <!--Atestado-->
      <div class="expanpadirFecahdo modal3" id="expanpadirFecahdo" [class.disabled]="isDisabled3">
        <div class="menuFechado">
          <div class="list-item  " *ngIf="AtendentePermissao || MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="Atestado()">
              <mat-icon class="mat-icon-submenu color-text">note_add</mat-icon>
              <a class="text-menu color-text">{{ 'TELADOCUMENTACAO.ATESTADO' | translate }}</a>
            </mat-list-item>
          </div>
          <!--Declaracao-->
          <div class="list-item  " *ngIf="AtendentePermissao || MedicoPermissao">
            <mat-list-item class="menu-item sub-menu1" (click)="Declaracao()">
              <mat-icon class="mat-icon-submenu color-text">assignment</mat-icon>
              <a class="text-menu color-text">{{ 'TELADOCUMENTACAO.DECLARACAO' | translate }}</a>
            </mat-list-item>
          </div>
          <!--Receituario-->
          <div class="list-item  " *ngIf="AtendentePermissao || MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="Receituario()">
              <mat-icon class="mat-icon-submenu color-text">subject</mat-icon>
              <a class="text-menu color-text" style="padding-right: 20px;">{{ 'TELADOCUMENTACAO.RECEITUARIO' | translate
                }}</a>
            </mat-list-item>
          </div>
          <!--GuiaExames-->
          <div class="list-item  " *ngIf="AtendentePermissao || MedicoPermissao">
            <mat-list-item class="menu-item  sub-menu1" (click)="GuiaExames()">
              <mat-icon class="mat-icon-submenu color-text">search</mat-icon>
              <a class="text-menu color-text" style="padding-right: 20px;">Guia Exames</a>
            </mat-list-item>
          </div>
        </div>
      </div>
    </div>

    <!--submenu Faturamento -->
    <div class="align-content" *ngIf="!FillerOpen" matTooltip="Faturamento" matTooltipPosition="right">

      <div class="expanpadirFecahdo modal4" id="expanpadirFecahdo" [class.disabled]="isDisabled4">
        <div class="menuFechado">
          <div class="list-item  ">
            <mat-list-item class="menu-item  sub-menu1" (click)="Faturas()">
              <mat-icon class="mat-icon-submenu color-text">description</mat-icon>
              <a class="text-menu color-text">Faturas</a>
            </mat-list-item>
          </div>
          <!--Convênio-->
          <div class="list-item  ">
            <mat-list-item class="menu-item sub-menu1" (click)="Convenio()">
              <mat-icon class="mat-icon-submenu color-text">receipt_long</mat-icon>
              <a class="text-menu color-text">Convênio</a>
            </mat-list-item>
          </div>

          <div class="list-item  ">
            <mat-list-item class="menu-item sub-menu1" (click)="GuiaTiss()">
              <mat-icon class="mat-icon-submenu color-text">description</mat-icon>
              <a class="text-menu color-text">GuiaTiss</a>
            </mat-list-item>
          </div>
        </div>
      </div>
    </div>
  </mat-drawer>
  <!--Fim Menu lateral, fechado e aberto -->


 <mat-drawer-content class="conteudo-central" >
    <div >
      <mat-toolbar class="header-color-toolbar"
        style="background-color: #f0f9f6; z-index: 3; justify-content: space-between; height: 55px;">
            <div class="col-8 panel_menu" style="padding: 0;">
            </div>

            <div class="col-4 text-right fast_panel" style="display: flex; padding: 0;">
              <li class="dropdown li-search" style="padding: 0 5px;" *ngIf="CartasBoasvindas > 0 && AtendentePermissao ">
                <button mat-icon-button data-toggle="dropdown" class="notifica-carta" title="{{'TELAPESQUISAMEDICO.EMAILBOASVINDAS'| translate}}"
                  (click)="carregarUsuarioBoasvindas()">
                  <mat-icon  style=" color: #ffffff;">mail_outline</mat-icon>

                </button>
              </li>
              <!-- *ngIf="tipoUsuario =='Médico'" -->
              <li class="dropdown li-search" style="padding: 0 5px;" *ngIf="totalPendencaias > 0">
                <button mat-icon-button data-toggle="dropdown" title="{{textoPendencias}}"
                  class="notifica-carta" (click)="AnaliseMedico()">
                  <mat-icon [matBadge]="totalPendencaias" matBadgeColor="warn"
                    style=" color: #ffffff;">error_outline</mat-icon>
                </button>
              </li>
              <li class="dropdown li-search " style="padding: 0 5px;">
                <!-- <button class="notifica-sino" data-toggle="dropdown" href="#" title="Notificações">
                  <mat-icon [matBadge]="RecadoDia == true ? '!' : ''">notifications</mat-icon>
                </button> -->
                <!-- dropdown-config -->
                <ul class="dropdown-menu painel-config  "
                  [className]="consultaAgora == true ? 'dropdown-menu painel-config dropdown-configComCora':'dropdown-menu painel-config dropdown-config'"
                  style="overflow: auto">
                  <li *ngFor="let item of DadosRecado">
                    <div (click)="MensagemdoDia(item.idRecado)"
                      style="font-size: 12px;text-align: center; background-color: #2E8B57; color: white;">
                      <span>Nova Mensagem dia
                        {{item.dia | date: 'dd/MM/yyyy'}}
                      </span>
                    </div>
                  </li>
                  <li>
                    <div style="text-align: center;" (click)="TodasMensagemdoDia()">
                      <span style="color: #2E8B57;">Visualizar Todas
                      </span>
                    </div>
                  </li>
                </ul>
              </li>

            

              <li class="desktop-none margem"
                *ngIf="usuarioConsulta.length > 0  &&  !AdmPermissao && !AtendentePermissao && consultaAgora && !stream  && !stream1"
                style="margin-left: 10px;">
              </li>

              <li class="dropdown li-search" >
                <button (click)="Logoff()" class=" sair" >
                  <mat-icon>logout</mat-icon>
                </button>
              </li>
            </div>
      </mat-toolbar>

      <div role="main" class="row div-principal-dash" *ngIf="!stream && !stream1">
        
        <div class="col-lg-9 col-md-12 col-sm-12 col-xs-12 conteudo-meio">
          <router-outlet></router-outlet>
        </div>
        <div class="col-lg-3 col-md-12 CardsDireita" style="padding: 13px 5px; background-color: #edfaf4; height: 100vh;">
          <app-colunadireita></app-colunadireita>
        </div>
      </div>

      <!-- <app-streaming *ngIf="stream" style="z-index: 9999;"></app-streaming>
      <app-streaming1 *ngIf="stream1" style="z-index: 9999;"></app-streaming1> -->
      <app-documentacao></app-documentacao>
      <!-- <app-lifeline></app-lifeline> -->
      <app-cadastro-exame></app-cadastro-exame>
      <app-pesquisa-cid></app-pesquisa-cid>
    </div>
  </mat-drawer-content>

</mat-drawer-container>

<mpv-modal-pagamento></mpv-modal-pagamento>
<app-guia-exame></app-guia-exame>

<!-- <ng-chat *ngIf="signalRAdapter && signalRAdapter.userId && ChatIf" [adapter]="signalRAdapter"
  [groupAdapter]="signalRAdapter" [userId]="signalRAdapter.userId" [isViewportOnMobileEnabled]="true"
  [title]="'Atendentes'" [historyEnabled]="false" [pollFriendsList]="true" searchPlaceholder="Buscar"
  messagePlaceholder="Escreva sua mensagem" [browserNotificationsEnabled]="false" [theme]="currentTheme"
  [isCollapsed]="ChatClick" [fileUploadUrl]="fileUploadUrl"
  (onParticipantClicked)="onEventTriggered('ParticipantClicked triggered','clique')"
  (onParticipantChatOpened)="onEventTriggered('ParticipantChatOpened triggered','Abre')"
  (onParticipantChatClosed)="onEventTriggered('ParticipantChatClosed triggered','fecha')"
  (onMessagesSeen)="onEventTriggered('MessageSeen triggered', 'fecha')">
</ng-chat> -->

<ngx-smart-modal #trocaClinicaMenu identifier="trocaClinicaMenu"
  customClass="nsm-centered medium-modal emailmodal clinic_modal nsm-dialog-animation-btt Button-sheet">

  <div class="modal-info">
    <b> Clinicas </b>
  </div>

  <div class="col-md-12 col-sm-12 col-xs-12 table-scroller">
    <!-- *ngFor="let item of DadosClinicasUser" TESTAR TABLE SCROLLER RESPONSIVO-->


    <table id="DatatableCliente" *ngFor="let item of DadosClinicasUser" (click)="tocarClinic(item.idclinica)"
      [className]="item.idclinica == idClinicaUsuario ? 'table no-desktop success-check':'table no-desktop'">
      <thead style="display: none;">
        <tr>
          <th class="">{{ 'TELAMENU.NOME' | translate }}</th>
          <th class="">{{ 'TELAMENU.TIPOUSUARIO' | translate }}</th>
          <th class="text-center">{{ 'TELAMENU.ACOES' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr class="card_table">
          <td id="paciente" style="width: 30%">
            <div class="" style="width:85px;">
              <img src="{{ImagemClinica}}" class="img-responsive">
            </div>
          </td>

          <td class="" id="data" style="width: 60%">
            <div class=" row" style="margin-left: auto">
              <div class="col-12 text-left" style="padding: unset">
                <b class="content-clinica">{{item.nomeClinica}}</b>

              </div>
            </div>

            <div class=" row" style="margin-left: auto">
              <div class="col-12 text-left" style="padding: unset">
                <b class="Title-b content-clinica">{{ 'TELAMENU.CNPJ' | translate }}</b>
                <small class="label_paciente content-clinica"> {{item.cnpj}}</small>
              </div>
            </div>

            <div class=" row" style="margin-left: auto">
              <div class="col-12 text-left" style="padding: unset">
                <b class="Title-b content-clinica">{{ 'TELAMENU.ESPECIALIZACAO' | translate }}</b>
                <small class="label_paciente content-clinica"> {{item.carac}}</small>
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</ngx-smart-modal>

<!-- MODAL MENSAGEM DO DIA -->
<ngx-smart-modal #mensagemDiaNova identifier="mensagemDiaNova"
  customClass="nsm-centered medium-modal form-modal emailmodal">
  <div class="modal-container">
    <div class='col-md-12' style="width: 100%;">
      <mat-form-field class="col-md-12 col-sm-12 col-xs-12  " style="margin-top: 20px;" appearance="outline"
        floatLabel="always">
        <mat-label>Mensagem do dia de {{UsuarioRecado}} </mat-label>
        <textarea matInput #input maxlength="500" name="Observação" disabled [(ngModel)]="DesRecadoDia"
          style="max-height: 250px; min-height: 200px; color: black;"></textarea>

      </mat-form-field>

    </div>
  </div>
</ngx-smart-modal>

<ng-template #emailUsuarioNovo identifier="emailUsuarioNovo" customClass="nsm-centered medium-modal emailmodal">
  <div class="background-email" style="display: flex;">

    <div>
      <img src="assets/build/img/email.png" class="img-responsive">
    </div>
  </div>


  <div class="modal-info">
    <b> {{ 'TELACOLUNADIREITA.ENVIARESSEEMAIL' | translate }} </b> <br>
    <small>{{ 'TELACOLUNADIREITA.RECEBERAOEMAILCOMASENHA' | translate }}</small>

  </div>


  <mat-divider></mat-divider>
  <div class="row-button text-center p-20">
    <button mat-flat-button (click)="fecharModal()" class="input-align btn btn-danger">
      {{ 'TELACOLUNADIREITA.NAO' | translate }} </button>
    <button mat-flat-button (click)="mandaEmail()" class="input-align btn btn-primary">
      {{ 'TELACOLUNADIREITA.SIM' | translate }} </button>
  </div>
</ng-template>


<!-- MODAL EMAIL BOAS VINDAS -->

<ngx-smart-modal #emailsSistemaNovo identifier="emailsSistemaNovo" class="emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <mat-icon class="material-icons" aria-hidden="true">group</mat-icon>
      <h3>CARTA DE BOAS VINDAS</h3>

    </div>
    <div class="modal-body ">
      <div class="card-body" style="padding: 0;">
        <table class="table table-striped table-hover" id="DatatableCliente" style="margin-bottom: 5px;">
          <tbody *ngFor="let item of DadosUsuario" style="border-radius: 10px;">
            <tr class="card_table " style="border-radius: 10px;">
              <td id="paciente" class="text-left" style="background:white!important;border: none; width:250px;">
                <div>
                  <p _ngcontent-c18 class="Title-b ">{{item.tipoUsuario}}</p>
                  <span _ngcontent-c18 class="label_paciente " style="font-size: 100%;">{{item.nome}}</span><br>
                  <span class="label_paciente " style="font-size: 100%;">{{item.email}}</span>
                </div>
              </td>
              <td class="text-center  " id="date" style="background: white; border: none;">
                <div class="text-center">
                  <p _ngcontent-c18 class="Title-b">{{ 'TELACOLUNADIREITA.DATADECADASTRO' | translate }}</p>
                  <span class="label_paciente " _ngcontent-c18 style="font-size: 100%;">{{item.dtaCadastro |
                    date:'dd/MM/yyyy HH:mm'
                    }}</span>
                  <span></span>
                </div>
              </td>
              <td class="text-center border-right " id="acoes" style="background: white; border: none!important;">
                <button mat-icon-button class="panel_button" (click)="AbrirModarEnviarEmail(item.idUsuario)"
                  title="{{'TELACOLUNADIREITA.ENVIAREMAIL' | translate}}">
                  <mat-icon aria-label="Enviar e-mail" style="color: #2E8B57 !important;"
                    class="mat-icon ng-star-inserted botaoEnviar primary">
                    mail_outline</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <mat-card appearance="outlined" class="col-sm-12 no-desktop card-margin-mobile" style="border: 1px solid #ddd;"
        *ngFor="let item of DadosUsuario">
        <mat-card-header style="padding-top:10px;">
          <mat-card-title>{{item.nome}}</mat-card-title>
          <mat-card-subtitle>{{item.tipoUsuario}}</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content style="padding:0px;" class="custom">
          <div class="row">
            <div class="col-md-6 col-sm-6 col-xs-6">
              <li><b>{{ 'TELACOLUNADIREITA.CPF:' | translate }} </b> {{item.cpf}}</li>
            </div>
            <div class="col-md-6 col-sm-6 col-xs-6">
            </div>
          </div>
        </mat-card-content>
      </mat-card>

    </div>
    <div class="modal-footer">
      <button class="action-button cancel-button" (click)="ngxSmartModalService.getModal('emailsSistemaNovo').close()">
        Fechar
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- Modal Enviar Email -->


<ngx-smart-modal #consultaMobile identifier="consultaMobile" customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-info" style="background: #f5f5f5; border-radius: 10px; padding: 0 !important;">
    <div style="margin-top: 1rem !important;">
      <div class="modal-titulo-consulta">
        <h3 class="titulo-consulta">Consulta(s)</h3>
      </div>

      <hr class="sep-1">
      <div class="container pb-2" style="max-height: 50vh !important; overflow-y: auto; background-color: #fff;">
        <div *ngFor="let item of usuarioConsulta" style="margin-top: 1rem !important;">
          <div class="row p-2 card-consulta"
            style="margin-bottom: 1rem !important; padding: 0 !important; margin: 0 auto;">
            <div class="col-sm-7 text-align info-consulta">
              <small>
                <p style="margin-bottom: 0 !important">Consulta para
                  {{item.dtaconsulta | date: 'dd/MM/yyyy HH:mm' }}</p>
              </small>

              <small *ngIf='item.UsuarioSolicitante != null'>
                <p style="margin-bottom: 0 !important">
                  Tipo Agendamento: Orientação médica.</p>

              </small>
              <small *ngIf="item.PacienteSolicitante != null && MedicoPermissao && item.nome != null">
                <p style="margin-bottom: 0 !important; max-width: 100% !important;">Paciente:
                  {{item.nome | truncate : 20 : "…"}}</p>
              </small>
              <small *ngIf="item.PacienteSolicitante == null && MedicoPermissao && item.nome != null">
                <p style="margin-bottom: 0 !important; max-width: 100% !important;">Solicitante:
                  {{item.nome | truncate : 20 : "…"}}</p>
              </small>

              <small>
                <p style="margin-bottom: 0 !important">
                  Tipo Consulta: {{item.flgProntuario ==true?'Presencial' : 'Telemedicina'}}</p>

              </small>


              <small>
                <p style="margin-bottom: 0 !important" *ngIf="MedicoPermissao">Clinica: {{item.Clinica}}</p>
              </small>

              <small *ngIf="item.nomeMedico != ''">
                <p style="margin-bottom: 0 !important">
                  Médico: {{item.nomeMedico}}</p>
              </small>

            </div>
            <div class="col-sm-3" style="margin-top: auto !important; margin-bottom: auto !important;">

              <button class="btn-primary buttons-mobilet" id="opcao2" mat-mini-fab style="width: 30px;
                    touch-action: none;
                    user-select: none;
                    -webkit-user-drag: none;
                    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                    height: 30px;
                    margin-right: 4%;" matTooltip="{{'TELACONSULTAS.CANCELAR' | translate}}"
                (click)="CancelaValue(item.idconsulta)"
                *ngIf="item.flgInativo != true  &&  item.flgrealizada != true && MedicoPermissao">
                <mat-icon style="
                        width: 23px !important;
                        margin-top: -7px;
                        font-size: 19px;" aria-label="Cancelar Consulta" class="svg-icon">delete</mat-icon>
              </button>

              <button tmDarkenOnHover class="btn-primary buttons-mobilet" mat-mini-fab style="width: 30px;
                background-color: green !important; touch-action: none; user-select: none; -webkit-user-drag: none;-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                height: 30px; margin-right: 4%;" matTooltip="{{'TELACONSULTAS.INICIARCONSULTA' | translate}}"
                (click)="IrConsulta(item.idconsulta)">
                <mat-icon style=" width: 22px !important;
                    margin-top: -10px;" aria-label="Iniciar consulta  choicer" class="">
                  play_arrow</mat-icon>
              </button>

            </div>
          </div>
        </div>
      </div>
    </div>


  </div>

</ngx-smart-modal>




<ngx-smart-modal #SolicitarConsulta identifier="SolicitarConsulta" customClass="nsm-centered medium-modal emailmodal">
  <div class="col-md-12 background-Iniciar mb-4 no-mobile"
    style="display: flex; height: 100px; width: 515px !important; background-size: 547px !important;">
    <div class="modal-info" style="margin-top: 45px; font-size: 27px;">
      <b> Orientação </b><br>
    </div>
    <mat-icon style="color: white; font-size: 88px;  margin-left: 25%;"> calendar_today</mat-icon>
  </div>

  <div class="col-md-12 mb-4 no-desktop">
    <div class="modal-info" style="margin-top: 3%; font-size: 27px;">
      <b style="font-family: Cairo,sans-serif;"> Orientação Médica </b>
      <br>
      <p style="margin-top: 4%; margin-bottom: -38px;"> Voçê está numa sala de espera virtual.</p>
    </div>
  </div>

  <div class="modal-info no-mobile">

    <hr class="sep-2" />

    <div style="margin-top: 1rem !important;">
      <div class="row col-sm-12" style="margin-bottom: 1rem !important;">
        <div class="col-sm-12" *ngIf="PosicaoFila == 0">
          <p style="margin-bottom: 0 !important">Médicos On-line: {{MedicosOnline}}</p>
          <br>
        </div>
        <div class="col-sm-12" style="margin-top: auto !important; margin-bottom: auto !important;">
          <div class="col-12 panel-button-dropdown" *ngIf="PosicaoFila == 0">
            <button class=" button-interactive btn btn-primary" (click)="solicitacaoConsulta()">
              Solicitar Orientação </button>
          </div>
          <div class="col-12 row panel-button-dropdown" *ngIf="PosicaoFila != 0">
            <div class="col-md-6  ">
              <p style="float: left;">Médicos On-line: {{MedicosOnline}}</p><br>
              <p style="float: left;"> Sua Posição : </p>
            </div>
            <div class=" col-md-6">
              <button mat-fab class="avisos button-contact basic-pulses-red" *ngIf="!ConsultaAgoraTeste"
                style="background: #00c3f7; color: white;">
                <h1 style="
                            margin-top: -9px;"> {{PosicaoFila}}</h1>
              </button>


              <button mat-fab class="avisos button-contact basic-pulses-red" *ngIf="ConsultaAgoraTeste"
                style="background: green; color: white;" (click)="IniciarConsulta()">
                <h1 style="font-size: 17px;"> Entrar</h1>
              </button>

              <button mat-flat-button class="btn-danger" style=" float: right;  margin-top: 8%;"
                (click)="ngxSmartModalService.getModal('sairconsulta').open()">Desistir</button>

            </div>
            <div class="col-12  ">

              <span class=""> </span>
            </div>

          </div>

        </div>
      </div>
      <hr class="sep-2" />
    </div>
  </div>

  <div class="modal-info no-desktop">

    <hr class="sep-2" />

    <div class="row">
      <div class="col-md-12" *ngIf="PosicaoFila == 0" style="margin-top: 4%;">
        <p style="margin-bottom: 0 !important">Médicos On-line: {{MedicosOnline}}</p>

        <br>
      </div>
      <div class="col-md-12" style="margin-top: auto !important; margin-bottom: auto !important;">
        <div class="col-12 " *ngIf="PosicaoFila == 0">
          <button class=" button-interactive btn btn-primary" (click)="solicitacaoConsulta()" style="
                    margin-bottom: 12px;">
            Solicitar Orientação </button>

        </div>
        <div *ngIf="PosicaoFila != 0">
          <div class="col-md-12  ">
            <p style="margin-top: 4%;">Médicos On-line: {{MedicosOnline}}</p>

            <p> Sua Posição : </p>
          </div>
          <div class=" col-md-12">
            <button mat-fab class="avisos button-contact basic-pulses-red" *ngIf="!ConsultaAgoraTeste"
              style="background: #00c3f7; color: white;">
              <h1 style="margin-top: -5px;"> {{PosicaoFila}}</h1>
            </button>
            <br>
            <button mat-fab class="avisos button-contact basic-pulses-red" *ngIf="ConsultaAgoraTeste"
              style="background: green; color: white;" (click)="IniciarConsulta()">
              <h1 style="font-size: 17px;"> Entrar</h1>
            </button><br>
            <button mat-flat-button class="btn-danger" style="margin-top: 8%; margin-bottom: 4%;"
              (click)="ngxSmartModalService.getModal('sairconsulta').open()">Desistir</button>
          </div>
          <div class="col-12  ">
            <span class=""> </span>
          </div>
        </div>
      </div>
    </div>
    <hr class="sep-2" />
  </div>
</ngx-smart-modal>

<ngx-smart-modal #cancelarHorarioCoracaoMenu identifier="cancelarHorarioCoracaoMenu"
  customClass="nsm-centered medium-modal emailmodal">
  <div class="background-delete" style="width:100%;">
    <div style="justify-content: center;  display: flex;">
      <img src="assets/build/img/time.png">
    </div>

  </div>
  <div class="modal-info text-center">
    <b class="">
      {{ 'TELACONSULTAS.DESEJACANCELARESSEHORARIO' | translate }}
    </b>


    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 " style="margin-top: 20px;" appearance="outline"
      floatLabel="always" hintLabel="Máx. 200">
      <mat-label>{{ 'TELACONSULTAS.MOTIVODOCANCELAMENTO' | translate }}</mat-label>
      <textarea matInput name="Motivo Cancelamento" [(ngModel)]="Motivocancelameto" (change)="MotivoCampo()"
        style="max-height: 200px; min-height: 48px;" maxlength="200"></textarea>
      <mat-hint align="end">{{Motivocancelameto.length + 0}}/200</mat-hint>
    </mat-form-field>
    <div class="danger-baloon" *ngIf="cancelamento == true">
      <label style="color: red;" class="text-right">{{ 'TELACONSULTAS.MOTIVODESERPREENCHIDO' | translate }}</label>
    </div>

  </div>

  <div class="row-button text-center p-t-20 p-b-20">
    <button mat-flat-button (click)="cancelarHorarioCoracaoMenu.close()" class="input-align btn btn-danger">
      {{ 'TELACONSULTAS.NAO' | translate }}
    </button>
    <button mat-flat-button class="input-align btn btn-success" (click)=CancelarConsultaPadrao()>
      {{ 'TELACONSULTAS.SIM' | translate }} </button>
  </div>

</ngx-smart-modal>




<ngx-smart-modal #sairconsulta identifier="sairconsulta" customClass="nsm-centered medium-modal" [closable]="false"
  [dismissable]="false" [escapable]="false">
  <div class="modal-header p-t-20 p-b-20">
    <div class="row">
      <div class=" col-12">
        <h1 class="little-title fw-700" style=" padding-left: 3vh; text-align: center">Ao sair sua consulta sera
          cancelada.</h1>
      </div>
    </div>
  </div>

  <mat-divider></mat-divider>
  <div class="row-button text-center p-t-20">
    <button mat-flat-button (click)="CancelarConsulta()" class="btn-primary">Sair</button>
    <button mat-flat-button (click)="sairconsulta.close()" class="btn-primary" style="
margin-left: 10px;">Cancelar</button>

  </div>
</ngx-smart-modal>


<ngx-smart-modal #modalOrientacao identifier="modalOrientacao" customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-container" style="margin: 0 !important; padding-bottom: 15px;">
    <div class="col-12" style="display: flex; justify-content: space-between; padding: 10px 15px;">
      <div class="col-md-11" style="display: flex; padding: 0;">
        <mat-icon class="material-icons" aria-hidden="true" style="color: #2E8B57;">group</mat-icon>
        <p class="title-content"></p>
      </div>
      <div class="col-1">
        <mat-icon class="material-icons" aria-hidden="true" style="color: #2E8B57;"
          onclick="document.getElementById('modalOrientacao').style.display='none'">close</mat-icon>
      </div>
    </div>
    <div class="col-12 mt-1 mb-1">
      <div class="card ">
        <div class="card-body" style="padding: 0;">
          <table class="table table-striped table-hover" id="DatatableCliente" style="margin-bottom: 5px;">

            <tbody *ngFor="let item of DadosUsuario" style="border-radius: 10px;">
              <tr class="card_table " style="border-radius: 10px;">
                <td id="paciente" class="text-left" style="background:white!important;border: none; width:250px;">
                  <div>
                    <p _ngcontent-c18 class="Title-b ">{{item.tipoUsuario}}</p>
                    <span _ngcontent-c18 class="label_paciente " style="font-size: 100%;">{{item.nome}}</span><br>
                    <span class="label_paciente " style="font-size: 100%;">{{item.email}}</span>
                  </div>
                </td>
                <td class="text-center  " id="date" style="background: white; border: none;">
                  <div class="text-center">
                    <p _ngcontent-c18 class="Title-b">{{ 'TELACOLUNADIREITA.DATADECADASTRO' | translate }}</p>
                    <span class="label_paciente " _ngcontent-c18 style="font-size: 100%;">{{item.dtaCadastro |
                      date:'dd/MM/yyyy HH:mm'
                      }}</span>
                    <span></span>
                  </div>
                </td>
                <td class="text-center border-right " id="acoes" style="background: white; border: none!important;">
                  <button mat-icon-button class="panel_button"
                    (click)="ModalEmail(item.idUsuario);ngxSmartModalService.getModal('emailsSistemaNovo').close() "
                    title="{{'TELACOLUNADIREITA.ENVIAREMAIL' | translate}}">
                    <mat-icon aria-label="Enviar e-mail" style="color: #2E8B57 !important;"
                      class="mat-icon ng-star-inserted botaoEnviar primary">
                      mail_outline</mat-icon>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <mat-card appearance="outlined" class="col-sm-12 no-desktop card-margin-mobile" style="border: 1px solid #ddd;"
          *ngFor="let item of DadosUsuario">
          <mat-card-header style="padding-top:10px;">
            <mat-card-title>{{item.nome}}</mat-card-title>
            <mat-card-subtitle>{{item.tipoUsuario}}</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content style="padding:0px;">
            <div class="row">
              <div class="col-md-6 col-sm-6 col-xs-6">
                <li><b>{{ 'TELACOLUNADIREITA.CPF:' | translate }} </b> {{item.cpf}}</li>
              </div>
              <div class="col-md-6 col-sm-6 col-xs-6">
                <!-- <li style="display:flex; justify-content: center;">
                                    <button mat-icon-button (click)="EnviarEmail(item.idUsuario)"
                                        *ngIf="item.flgEmail != true">
                                        <mat-icon aria-label="Enviar senha para Usuarios" class="button-interative">
                                            mail_outline</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="ValorUsuario(item.idUsuario)">
                                        <mat-icon aria-label="Deletar Linha selecionada" class="button-interative">
                                            delete</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="editUsuario(item.idUsuario)">
                                        <mat-icon aria-label="Editar linha selecionada" class="button-interative">edit
                                        </mat-icon>
                                    </button>
                                </li> -->
              </div>

            </div>
          </mat-card-content>

        </mat-card>
      </div>
    </div>
  </div>
</ngx-smart-modal>