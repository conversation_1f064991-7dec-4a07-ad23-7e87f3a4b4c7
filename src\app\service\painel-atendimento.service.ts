import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { HttpClient, HttpParams } from '@angular/common/http';
import { objChamadoAtendimento, objNovoAtendimento, objTipoAtendimento } from '../model/painel-atendimento';
@Injectable({
  providedIn: 'root'
})
export class PainelAtendimentoService {

  constructor(
    private http: HttpClient
  ) { }

  public GetUltimasConsultas() {
    return this.http.get<objChamadoAtendimento[]>(environment.apiEndpoint + '/painel/GetUltimasConsultas')
  }

  public ChamarPacientePainel(obj: objNovoAtendimento) {
    return this.http.post<boolean>(environment.apiEndpoint + '/painel/ChamarPacientePainel', obj);
  }

  public rechamarPacientePainel(id:any) {
    
    let params = new HttpParams();
    params = params.append('idConsulta', id);
    return this.http.get(environment.apiEndpoint + '/painel/rechamarPacientePainel', { params });
  }

  public listarTiposAtendimentos() {
    return this.http.get<objTipoAtendimento[]>(environment.apiEndpoint + '/atendimento/carregaLista')
  }
}
