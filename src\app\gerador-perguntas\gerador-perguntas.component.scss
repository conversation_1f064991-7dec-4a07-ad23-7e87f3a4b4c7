// .panel_button {
//   border: 1px solid #dcdbdb !important;
//   border-radius: 0px;
// }
// .card_table {
//   border: 1px solid rgba(0, 0, 0, 0.2);
// }
// .card_table:hover {
//   box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
//   transition: 0.3s;
// }
// table{
//   background-color: #f5f5f5;
// }
// .tr:hover {
//   background: #fff !important;
//   cursor: pointer;
// }
// .separar{
//   border: #f5f5f5;
//   width: 100%;
//   display:  table;
//   margin-top:10px;
//   border: unset;
//   }
$primary: #348bc1;
.tabela {
    margin-right: 30px;
}

mat-form-field {
    margin-right: 12px;
}

$primary: #348bc1;
tr:hover {
    background: #fff !important;
    cursor: pointer;
}

.fonte-tamanho {
    font-size: 20px;
}

small {
    color: #666;
    font-size: 13px;
}

#paciente {
    width: 20%;
    margin-right: 10px;
    border-left: 1px solid #ddd;
}

#cpf {
    width: 10%;
    margin-top: auto;
    margin-bottom: auto;
    border-left: 1px solid #ddd;
}

#data {
    margin-bottom: auto;
    margin-top: auto;
    width: 8%;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
}

.img-circle {
    border-radius: 50%;
    width: 80px !important;
    height: 80px !important;
}

#acoes {
    width: 21%;
}

.date {
    margin-top: 10px !important;
}

.md-chip {
    margin-left: 20px;
}

.spacer-card {
    padding: 5px;
    padding-left: 20px;
    padding-top: 30px;
}

.panel_initial {
    border-bottom-left-radius: 0px !important;
    border: 1px solid #ddd;
    border-top-left-radius: 0px !important;
}

.finish_panel {
    border-bottom-right-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border: 1px solid #ddd;
}

.Title-b {
    font-weight: bolder;
    color: #0983ff;
}

.panel_button {
    border: 1px solid #dcdbdb !important;
    border-radius: 0px;
}

.input-align {
    margin-left: 10px;
}

table {
    background: #fff;
}

.card_table {
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card_table:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.div_img {
    margin-left: 25px;
}

.div_paciente {
    margin-top: auto;
    margin-bottom: auto;
    margin-left: 25px;
}

.quebra {
    word-break: break-all;
}

.label-paciente {
    margin-top: auto;
    margin-bottom: auto;
}

.star_point {
    font-size: 20px;
    color: #ffc107;
}

.mat-mdc-icon-button:hover {
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.19);
}

.cor {
    background: white;
    color: #1265b9;
}

@media (min-width: 601px) {
    .coluna {
        margin-left: -14px;
        margin-bottom: -20px;
        margin-top: 10px;
    }
}

@media (max-width: 320px) {
    .margem-c {
        margin-left: -10px;
    }
    .col-table table {
        border: none !important;
    }
    tr:hover {
        background: unset;
    }
    .col-table thead {
        font-size: 11px;
        background: #fff;
        color: #666;
    }
    .col-table i {
        font-size: 18px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .col-table mat-icon {
        font-size: 20px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .value-color {
        // color: #1265b9 !important;
        font-size: 13px;
        padding: 0 !important;
        padding-left: 5px !important;
        padding-right: 5px !important;
        text-align: center !important;
    }
}

@media (max-width: 1380px) {
    .svg-icon {
        width: 20px;
    }
    // .mat-icon-button {
    //   width: 22px !important;
    //   height: 40px !important;
    // }
    // .material-icons {
    //   font-size: 18px !important;
    // }
}

.align-button {
    // position: absolute;
    top: 40px;
    margin-left: 5.5%;
}

.align-buttonII {
    margin-left: 38px;
    top: 40px;
}

.mini-mini {
    width: 10px;
    height: 10px;
    background: $primary;
}

.mini-miniI {
    margin-right: 109px;
}

.mini-miniII {
    margin-right: 77px;
}

.mini-miniIII {
    margin-right: 110px;
}

.fab button.mainb {
    position: absolute;
    width: 35px;
    height: 35px;
    border-radius: 30px;
    background-color: #1265b9;
    right: 0;
    bottom: -8px;
    z-index: 20;
}

.fab button {
    cursor: pointer;
    width: 35px;
    height: 35px;
    border-radius: 30px;
    background-color: #0983ff;
    border: none;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    font-size: 24px;
    color: white;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}

.fab ul li label {
    margin-top: 3px !important;
    margin-right: -10px;
    margin-left: 20px;
    border-top-right-radius: 0px;
    width: 80px;
    font-size: 12px;
    height: 28px;
}

.fab button.mainb:active+ul,
.fab button.mainb:focus+ul {
    bottom: 45px;
}

@media (max-width: 425px) {
    .md-chip {
        margin-left: 0;
    }
}

@media (max-width: 600px) {
    .div_paciente {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 25px !important;
    }
    .fonte-tamanho {
        font-size: 16px;
        vertical-align: text-top;
    }
    .mat-mdc-card {
        padding: 10px !important;
    }
    .coluna {
        margin-left: 0px;
        margin-right: 0px;
        margin-bottom: -15px;
        margin-top: 15px;
    }
    .header-card {
        margin-top: 20px;
        margin-bottom: 0px;
    }
}

@media(max-width:480px) {
    .text-align {
        text-align: center;
    }
}