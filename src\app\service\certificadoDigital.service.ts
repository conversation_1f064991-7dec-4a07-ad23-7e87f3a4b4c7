import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { SpinnerService } from './spinner.service';


@Injectable({
    providedIn: 'root'
})
export class CertificadoDigitalService {

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });



    public getListaCertificados(idMedico:any): Observable<any> {
        // let params = new HttpParams();
        // params = params.append('inicio', String(inicio));
        // params = params.append('fim', String(fim));
        // params = params.append('pesquisa', String(pesquisa));
        // params = params.append('idMedico', String(idMedico));
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/CertificadoDigital/GetListaCertificados/' + idMedico);
    }
    
    public CadastrarCertificado(certificado:any): Observable<any> {
        certificado;
        this.spinner.show();
        return of(null);
        // return this.http.post(environment.apiEndpoint + '/CertificadoDigital/CadastrarCertificado', certificado);
    }
}    