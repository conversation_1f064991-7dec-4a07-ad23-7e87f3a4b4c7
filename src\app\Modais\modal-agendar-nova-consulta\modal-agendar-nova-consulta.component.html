<div style="display: flex; justify-content: space-between;">
    <h2 mat-dialog-title style="font-weight: 700; color: #279EFF; margin-bottom: -2px;">Agendar nova consulta</h2>
    <button mat-icon-button (click)="dialogRef.close();"> <mat-icon>close</mat-icon> </button>
</div>

<div class="medium-modal form-modal emailmodal modal-agenda-horario">
    <div class="row">

        <div class="col-sm-12 height-modal marcar-horario-modal"
            style="padding-left: 30px;  overflow: auto;  padding-right: 30px; padding-top: 30px;">
            <h4 class="little-title fw-700 title-marcar-hr" style="text-align: center;">
                {{ 'TELAAGENDA.MARCARHORARIO' | translate }}
            </h4>


            <div class="row ">


                <div class="col-md-10 col-sm-10 col-xs-9 linha-paciente" style="padding: unset; height: 50px;">
                    <ng-select [items]="DadosPacientes" placeholder="{{ 'TELAAGENDA.PACIENTE' | translate }}"
                        bindLabel="nome" aria-required="true" bindValue="idCliente" name="Paciente" [selectOnTab]="true"
                        [(ngModel)]="IdPaciente"  aria-required="true"
                        (change)="ValidaPaciAgenda(IdPaciente); RetornoConvenio(IdPaciente,idConvenio) "
                        class="col-12 input-spacing" style="height: 31px; font-size: 12px; padding-right:0px;"
                        (blur)="validaPaciente()">
                    </ng-select>

                    <mat-error style="font-size: 10px; padding-left: 15px;margin-top: 5px; width: 180px;"
                        *ngIf="pacienteValido==false">
                        {{getErrorMessagepaci() | translate }}
                    </mat-error>
                </div>


                <button mat-icon-button class="col-md-1" title="{{ 'TELAAGENDA.INFORMACOESPACIENTE' | translate }}"
                    [disabled]="IdPaciente == 0 || IdPaciente == undefined"
                    (click)="informacao('Paciente',IdPaciente )">
                    <mat-icon aria-label="Editar linha selecionada" style="color: #1265b9;" class="">info</mat-icon>
                </button>

                <button mat-icon-button class="col-md-1 panel_button btn-add-pac"
                    title="{{ 'TELAAGENDA.ADICIONARPACIENTE' | translate }}" (click)="AddnovoPaciente()">
                    <mat-icon aria-label="Editar linha selecionada" style="color: #1265b9;" class="">control_point
                    </mat-icon>
                </button>

                <mat-form-field class="col-md-6 col-sm-6 col-12 linha-paccpf">
                    <input matInput placeholder="{{ 'TELAAGENDA.BUSQUEOPACIENTEPELOCPF' | translate }}" name="CPf"
                        (change)="CarregaPacientes()" mask="000.000.000-00" [(ngModel)]="CPF" maxlength="14">
                </mat-form-field>

                <div class="col-md-6 col-sm-6 col-xs-6 " *ngIf="!loginComMedico">
                    <ng-select [items]="ListaMedicos" style="font-size: 12px;"
                        placeholder="{{ 'TELAAGENDA.MEDICOS' | translate }}" bindLabel="nomeMedico" bindValue="idMedico"
                        name="medicos" [selectOnTab]="true" [(ngModel)]="IdMedico" [formControl]='medi'
                        aria-required="true">
                    </ng-select>
                    <mat-error style="font-size: 10px; margin-top: -15px;" *ngIf="medi.invalid">
                        {{getErrorMessagemedi() | translate }}
                    </mat-error>
                </div>

                <div class="col-md-6 col-sm-6 col-12">
                    <ng-select [items]="DadosEspecialidadeMedico" style="font-size: 12px;" (change)="CarregaMedicos()"
                        placeholder="{{ 'TELAAGENDA.ESPECIALIDADE' | translate }}" bindLabel="desEspecialidade"
                        bindValue="idEspecialidade" name="especialidade" [selectOnTab]="true"
                        notFoundText="Selecione o Médico" [(ngModel)]="Especialidade">
                    </ng-select>
                </div>

                <mat-form-field class="col-md-6 col-sm-6 col-12">
                    <input matInput placeholder="{{ 'TELAAGENDA.DATAEHORA' | translate }}" name="Data e Hora"
                        [(ngModel)]="Dados.dtahora" disabled style="color: black;">
                </mat-form-field>

                <div class="col-md-6 col-sm-6 col-xs-12" style="padding: unset" *ngIf="Edicao">
                    <mat-form-field class="col-md-12 col-sm-12">
                        <input matInput placeholder="{{ 'TELACONSULTAS.NOVADATA' | translate }}" name="Data Inicio"
                            id="Dtanova"[(ngModel)]="Dados.dtanova"
                            (blur)="ValidaDtaEd($any($event.target).value)" maxlength="10">

                    </mat-form-field>
                    <span class="aviso-span text-center" *ngIf="Dtanasc == true"
                        style="font-size: 65%;color: #f44336;font-weight: 600;display:flex; margin-top: -11px;">{{
                        'TELACADASTROMEDICO.ERRODATA' | translate }}</span>
                    <span class="aviso-span text-center" *ngIf="DtanascVasil == true && Dtanasc != true"
                        style="font-size: 65%;color: #f44336;font-weight: 600;display: flex; margin-top: -11px;">{{
                        'TELACADASTROMEDICO.ERROCAMPO' | translate }}</span>
                </div>

                <div class="col-md-6 col-sm-6 col-xs-12" *ngIf="Edicao" style="height: 50px!important; padding: unset">
                    <mat-form-field class="col-md-12">
                        <input matInput placeholder="{{ 'TELAAGENDA.NOVOHORARIO' | translate }}" type="time"
                            id="horaEdcao"
                            name="NovoHorario">
                    </mat-form-field>
                </div>

                <div class="col-md-6 col-sm-6 col-12">
                    <ng-select [items]="dadosTipoAgendamento" style="font-size: 12px;"
                        placeholder="{{ 'TELAAGENDA.TIPOAGENDAMENTO' | translate }}" bindLabel="desTipoAgendamento"
                        bindValue="idTipoAgendamento" name="tipoAgendamento" [selectOnTab]="true"
                        [(ngModel)]="tipoagendamento" notFoundText="Nenhum tipo de Agendamento Cadastrado">
                    </ng-select>
                </div>
                <div class="col-md-6 col-sm-6 col-xs-12">
                    <ng-select [items]="dadosConvenio" style="font-size: 12px;"
                        placeholder="{{ 'TELAAGENDA.CONVENIO' | translate }}" bindLabel="desConvenio"
                        bindValue="idConvenio" name="especialidade" [selectOnTab]="true"
                        (change)="RetornoConvenio(IdPaciente,idConvenio)" [(ngModel)]="idConvenio"
                        notFoundText="Cadastre convenios." (change)="PreencheValorPagamento()">
                    </ng-select>
                </div>
                <mat-form-field class="col-md-6 col-sm-6 col-12">
                    <input matInput placeholder="{{ 'TELAAGENDA.VALOR' | translate }}" name="valor" id="valorConsulta"
                        [required]='flgExigePagamento' (keypress)="mascaraValor($any($event.target).value)"
                        (change)="mascaraValor($any($event.target).value)" (keyup)="mascaraValor($any($event.target).value)" [(ngModel)]="valorConsulta">
                    <mat-error *ngIf="flgExigePagamento">
                        {{getErrorMessagevalorConsulta() | translate }}
                    </mat-error>
                </mat-form-field>

                <mat-form-field class="col-md-6 col-sm-6 col-12" *ngIf="flgConvenioParticular==false">
                    <input matInput placeholder="{{ 'TELAAGENDA.CODIGOCONVENIO' | translate }}" name="codConvenio"
                        [formControl]='codConv' [(ngModel)]="codConvenio" (change)="verificaConvenio()">

                </mat-form-field>
                <div class="col-md-6 col-sm-12 col-12">
                    <mat-checkbox [(ngModel)]="FlgRetorno"><small>{{ 'TELAAGENDA.RETORNO' | translate }}</small>
                    </mat-checkbox>
                </div>


                <mat-form-field class="col-md-12 col-sm-12 col-xs-12 " appearance="outline" style="margin-top: 20px;"
                    hintLabel="Máx. 150" floatLabel="always">
                    <mat-label>{{ 'TELAAGENDA.OBSERVACAO' | translate }}</mat-label>
                    <textarea matInput #input maxlength="150" name="Observação" [(ngModel)]="objConsulta.DesAnotacao"
                        style="max-height: 105px; min-height: 30px;"></textarea>
                    <mat-hint align="end">{{ 0 + objConsulta.DesAnotacao!.length }}/150</mat-hint>
                </mat-form-field>
            </div>
            <div class="row-button p-t-20 cartao-pagam" *ngIf="!flgProntuario && flgHabilitaPagamento"
                style="margin: 0 auto; text-align: center;">
                <span>Exige Pagamento Cartão</span>
                <mat-slide-toggle class="botao" style="font-size: 12px; margin-left: 10px;"
                    [(ngModel)]='flgExigePagamento'>
                </mat-slide-toggle>
            </div>

            <div class="row-button p-t-20" style="margin-bottom: 10px; text-align: center;">
                <mat-radio-group aria-labelledby="example-radio-group-label" class="example-radio-group"
                    aria-selected="true" [(ngModel)]="flgProntuario" *ngIf="!flgSomenteProntuario">
                    <!-- *ngIf="!usuario.flgProntuario"  -->

                    <mat-radio-button class="example-radio-button" [value]="false">
                        Telemedicina
                    </mat-radio-button>

                    <mat-radio-button class="example-radio-button" [value]="true">
                        Presencial
                    </mat-radio-button>
                </mat-radio-group>

                <div class="btns-ns-horario">
                    <!-- (click)="marcarHorario.close()" (click)="LimpaMensErroPaci()" -->
                    <button mat-flat-button class="input-align btn btn-danger">
                        {{ 'TELACONSULTAS.NAO' | translate }}
                    </button>
                    <button type="submit" mat-flat-button class="input-align btn btn-primary" (click)=SalvarConsulta()>
                        {{ 'TELACONSULTAS.SIM' | translate }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>