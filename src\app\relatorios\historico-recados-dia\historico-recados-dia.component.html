<mat-card appearance="outlined" class="mother-div">
    <div class="white-container">
        <mat-card-title class="spacer-card">
            <div class="col-md-12 header-container">
                <mat-icon class="icon-title">assessment</mat-icon>
                <a class="title-content">Gerar Relatório de Recados do Dia</a>
            </div>
        </mat-card-title>
    
        <mat-divider></mat-divider>
        
        <mat-card-content class="white-container">
            <div class="row justify-content-center content-section">
                <div class="col-md-12 filters-row">
                    <div class="col-md-6 col-sm-12 select-field">
                        <ng-select 
                            [disabled]="desativaCampoMedico==true" 
                            [items]="UsuariosDados" 
                            placeholder="Médicos"
                            bindLabel="nome" 
                            bindValue="idMedico" 
                            name="especialidade"
                            [selectOnTab]="true" 
                            required 
                            (change)="mostrarTabela=false" 
                            [(ngModel)]="User">
                        </ng-select>
                    </div>
    
                    <div class="col-md-3 col-sm-12 date-field">
                        <mat-form-field appearance="outline">
                            <input matInput 
                                placeholder="{{ 'TELARELATORIOS.DATAINICIO' | translate }}" 
                                #DtaInicioIn
                                id="DataInicioConsulta" 
                                name="DataConsulta" 
                                (keyup)="ValidaDta(DtaInicioIn.value)"
                                mask="00/00/0000" 
                                [(ngModel)]="DtaInicio" 
                                maxlength="10">
                        </mat-form-field>
                        <span class="error-text" *ngIf="DtaInicioErrado == true">
                            {{ 'TELACADASTROPACIENTE.DATAINVALIDA' | translate }}
                        </span>
                    </div>
    
                    <div class="col-md-3 col-sm-12 date-field">
                        <mat-form-field appearance="outline">
                            <input matInput 
                                placeholder="{{ 'TELARELATORIOS.DATAFIM' | translate }}" 
                                id="DataFimConsulta"
                                #DataFimConsultaInput4
                                name="DataConsulta" 
                                (keyup)="ValidaFimDta(DataFimConsultaInput4.value)" 
                                mask="00/00/0000"
                                [(ngModel)]="DtaFim" 
                                maxlength="10">
                        </mat-form-field>
                        <span class="error-text" *ngIf="DtaFimErrado == true">
                            {{ 'TELARELATORIOS.DATAINVALIDA' | translate }}
                        </span>
                    </div>
                </div>
                
                <div class="col-md-12 col-sm-12 button-container">
                    <button class="search-button" mat-raised-button (click)="CarregaRecados()">
                        <mat-icon>search</mat-icon>
                        <span class="no-mobile">{{ 'TELARELATORIOS.PESQUISAR' | translate }}</span>
                    </button>
                </div>
            </div>
        </mat-card-content>
    
        <mat-divider></mat-divider>
    
        <mat-card-content class="white-container" *ngIf="mostrarTabela==true">
            <div class="table-container">
                <table class="results-table">
                    <tbody>
                        <tr *ngFor="let item of DadosRecados" class="result-row">
                            <td class="medico-cell">
                                <div [ngClass]="{'recado-do-dia': dtaLinha == item.dtaRecadoLinha}">
                                    <span class="cell-label">Médico:</span> {{item.nomeMedico}}
                                </div>
                            </td>
                            <td class="recado-cell" title="{{item.recado}}">
                                <div [ngClass]="{'recado-do-dia': dtaLinha == item.dtaRecadoLinha}">
                                    <span class="cell-label">Recado:</span> {{item.recado | truncate : 15 : "…"}}
                                </div>
                            </td>
                            <td class="criador-cell">
                                <div [ngClass]="{'recado-do-dia': dtaLinha == item.dtaRecadoLinha}">
                                    <span class="cell-label">Criador:</span> {{item.usuarioGerador}}
                                </div>
                            </td>
                            <td class="data-cell">
                                <div [ngClass]="{'recado-do-dia': dtaLinha == item.dtaRecadoLinha}">
                                    <span class="cell-label">Data:</span> {{item.dia | date:'dd/MM/yyyy'}}
                                </div>
                            </td>
                            <td class="visualizar-cell">
                                <div *ngIf="item.flgVisuali" [ngClass]="{'recado-do-dia': dtaLinha == item.dtaRecadoLinha}">
                                    <mat-icon class="visualizar-icon" title="Visualizar" (click)="visualizarMensagemDoDia(item.idRecado)">
                                        remove_red_eye
                                    </mat-icon>
                                    <span>Visualizado: {{item.dtaVisualizado | date:'dd/MM/yyyy HH:mm'}}</span>
                                </div>
                                <div *ngIf="!item.flgVisuali" class="visualizar-btn" (click)="visualizarMensagemDoDia(item.idRecado)"
                                    [ngClass]="{'recado-do-dia': dtaLinha == item.dtaRecadoLinha}">
                                    <mat-icon class="visualizar-icon" title="Visualizar">remove_red_eye</mat-icon>
                                    <span>Visualizar</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="load-more-container">
                    <button mat-flat-button class="load-more-button"
                        *ngIf="(DadosRecados != undefined && DadosRecados.length > 0) && bOcultaCarregaMais == false"
                        (click)="CarregarMais()">
                        {{ 'TELAPESQUISAUSUARIO.CARREGARMAIS' | translate }}
                    </button>
                </div>
            </div>
        </mat-card-content>
    </div>
</mat-card>

<!-- Modal de Visualização de Mensagem -->
<ngx-smart-modal #visualizarMensagemDia identifier="visualizarMensagemDia"
    customClass="nsm-centered medium-modal form-modal emailmodal">
    <div class="modal-container">
        <div class="modal-header">
            <h3 class="modal-title">Mensagem do dia de {{UsuarioRecado}}</h3>
        </div>
        <div class="modal-container">
            <mat-form-field class="message-field" appearance="outline" floatLabel="always">
                <textarea matInput #input maxlength="500" name="Observação" disabled [(ngModel)]="DesVisuRecadoDia"
                    class="message-textarea"></textarea>
            </mat-form-field>
        </div>
        <div class="modal-actions">
            <button class="close-button modal-button" mat-raised-button (click)="visualizarMensagemDia.close()">
                Fechar
            </button>
        </div>
    </div>
</ngx-smart-modal>

<!-- Modal de Geração Enviada -->
<ngx-smart-modal #GeracaoEnviada identifier="GeracaoEnviada" customClass="nsm-centered medium-modal emailmodal"
    [dismissable]=false>
    <div class="modal-container success-modal">
        <div class="modal-header success-header">
            <h3 class="modal-title">Seu relatório foi solicitado, logo ele será gerado!</h3>
        </div>
        <div class="modal-actions">
            <button class="ok-button modal-button" mat-raised-button (click)="GeracaoEnviada.close()">OK</button>
        </div>
    </div>
</ngx-smart-modal>