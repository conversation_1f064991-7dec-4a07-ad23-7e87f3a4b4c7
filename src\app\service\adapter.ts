import { ChatAdapter, IChatGroupAdapter, Group, Message, ChatParticipantStatus, ParticipantResponse, ChatParticipantType, IChatParticipant } from 'ng-chat';
import { Observable, of } from 'rxjs';
import { delay } from "rxjs/operators";
import { Injectable } from '@angular/core';


@Injectable({
    providedIn: 'root'
})
export class Adapter extends ChatAdapter implements IChatGroupAdapter {


  
  public static mockedParticipants: IChatParticipant[] = [
    {
      participantType: ChatParticipantType.User,
      id: 1,
      displayName: "Usuario Master",
      avatar: "http://medicoblobs.blob.core.windows.net/medico/51b210d4-afbc-4f84-adbc-d1dafb6a8d77",
      status: ChatParticipantStatus.Online
    },
   ];

  listFriends(): Observable<ParticipantResponse[]> {
    
    // this.http.get(environment.apiEndpoint + '/Atendente/GetCarregaAtendente/'+id);
    return of(Adapter.mockedParticipants.map(user => {
      let participantResponse = new ParticipantResponse();
      participantResponse.participant = user;
      participantResponse.metadata = {
        totalUnreadMessages: Math.floor(1)
      }

      return participantResponse;
    }));
  }

  getMessageHistory(destinataryId: any): Observable<Message[]> {
    destinataryId;
    let mockedHistory: Array<Message>;

    mockedHistory = [
      {
        fromId: 1,
        toId: 999,
        message: "Olá, basta digitar qualquer mensagem abaixo para testar este módulo Angular.",
        dateSent: new Date()
      }
    ];

    return of(mockedHistory).pipe(delay(2000));
  }

  sendMessage(message: Message): void {
    
    setTimeout(() => {
      
      let replyMessage = new Message();

      replyMessage.message = "Sua mensagem foi recebida aguarde que logo entraremos em contato.";
      replyMessage.dateSent = new Date();

      if (isNaN(message.toId)) {
        let group = Adapter.mockedParticipants.find(x => x.id == message.toId) as Group;

        let randomParticipantIndex = Math.floor(Math.random() * group.chattingTo.length);
        replyMessage.fromId = group.chattingTo[randomParticipantIndex].id;

        replyMessage.toId = message.toId;

        this.onMessageReceived(group, replyMessage);
      }
      else {
        replyMessage.fromId = message.toId;
        replyMessage.toId = message.fromId;

        let user = Adapter.mockedParticipants.find(x => x.id == replyMessage.fromId);

        this.onMessageReceived(user!, replyMessage);
      }
      
    }, 10000000);
  }

  groupCreated(group: Group): void {
    
    Adapter.mockedParticipants.push(group);

    Adapter.mockedParticipants = Adapter.mockedParticipants.sort((first, second) =>
      second.displayName > first.displayName ? -1 : 1
    );
    
    this.listFriends().subscribe(response => {
      
      this.onFriendsListChanged(response);
    });
  }
}
