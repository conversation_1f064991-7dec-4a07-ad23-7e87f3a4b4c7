{"compileOnSave": false, "compilerOptions": {"outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "ES2022", "module": "ES2022", "typeRoots": ["node_modules/@types"], "types": ["node", "jasmine", "lodash", "google.visualization", "crypto-js"], "lib": ["ES2022", "dom"], "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": "./", "esModuleInterop": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true}, "exclude": ["src/environments/environment.dev.ts", "src/environments/environment.teste.ts", "src/environments/environment.prod.ts"]}