import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { UsuarioService } from 'src/app/service/usuario.service';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';
import { ValidadoreseMascaras } from 'src/app/Util/validadores';

@Component({
    selector: 'app-modal-adicionar-paciente',
    templateUrl: './modal-adicionar-paciente.component.html',
    styleUrls: ['./modal-adicionar-paciente.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatDividerModule,
      TranslateModule,
      MatIconModule,
      TruncatePipe,
      MatSelectModule
    ]
})
export class ModalAdicionarPacienteComponent implements OnInit {

  constructor(
    private validacao: ValidadoreseMascaras,
    private usuarioService: UsuarioService
  ) { }
  dadosNovoPaciente: any;

  FlgExibeModalUsuarioExistente: boolean = false;
  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;
  mensagemPaciente?: string;
  cliente: any
  campoExitente?: string;
  clinicas: any = new FormControl([]);
  DadosClinicas: any = [];

  ngOnInit(): void {
  }



  public validarCpf(value:any) {
    this.campoCPFInvalido = false
    if (value != "") {
      this.campoCPFVazil = false;


      if (!this.validacao.cpf(value)) {
        this.campoCPFInvalido = true;
        return;
      }

      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, null, 'CPF').subscribe((retorno) => {


        this.mensagemPaciente = '';
        this.cliente = null;
        if (retorno != null) {
          this.campoExitente = "CPF"
          this.mensagemPaciente = 'CPF já registrado no sistema. Deseja utilizar o paciente desse cadastro?';
          this.cliente = retorno;
          this.FlgExibeModalUsuarioExistente = true;
        }
      })
    }
    else
      this.campoCPFVazil = true;
  }

  AceitarUsuarioExistente() {

    if (this.cliente != null) {

      this.dadosNovoPaciente.cpf = this.cliente.pessoa.cpf;
      this.dadosNovoPaciente.nome = this.cliente.pessoa.nomePessoa;
      this.dadosNovoPaciente.dtaNascimento = this.cliente.pessoa.dtaNascimento != null ? new Date(this.cliente.pessoa.dtaNascimento).toLocaleDateString() : "";
      this.dadosNovoPaciente.email = this.cliente.pessoa.email;

      if (this.cliente.paciente != null) {
        this.dadosNovoPaciente.procedencia = this.cliente.paciente.procedencia;
      }
      if (this.cliente.contato.telefoneMovel != null && this.cliente.contato.telefoneMovel != '' && this.cliente.contato.telefoneMovel != undefined) {
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(/\D/g, "");
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.dadosNovoPaciente.telefoneMovel = this.cliente.contato.telefoneMovel;

      if (this.cliente.clinicas && this.cliente.clinicas.length > 0) {

        this.clinicas = [];
        this.cliente.clinicas.forEach((element:any) => {
          this.DadosClinicas.forEach((elemento:any) => {
            if (elemento.idClinica == element.idClinica)
              this.clinicas.push(elemento)
          })
        })
        this.clinicas = new FormControl(this.clinicas);
      }

    }
    this.FlgExibeModalUsuarioExistente = false;

  }
}
