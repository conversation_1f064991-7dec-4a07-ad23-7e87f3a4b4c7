@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

$primary: #348bc1;
.tabela {
    margin-right: 30px;
    margin-left: -60px;
    margin-top: -64px;
    position: absolute;
    z-index: 2; 
    width: 88%;
}
.legenda-btn {
    margin-left: 18px;
}

tr:hover {
    background: #fff !important;
}

.fonte-tamanho {
    font-size: 20px;
}

.quebra {
    word-break: break-all;
}

.fab button.mainb {
    position: absolute;
    width: 35px;
    height: 35px;
    border-radius: 30px;
    background-color: #1265b9;
    right: 0;
    bottom: -8px;
    z-index: 20;
}

.fab button {
    cursor: pointer;
    width: 35px;
    height: 35px;
    border-radius: 30px;
    background-color: #0983ff;
    border: none;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    font-size: 24px;
    color: white;
    -webkit-transition: 0.2s ease-out;
    -moz-transition: 0.2s ease-out;
    transition: 0.2s ease-out;
}

.fab ul li label {
    margin-top: 3px !important;
    margin-right: -10px;
    margin-left: 20px;
    border-top-right-radius: 0px;
    width: 80px;
    font-size: 12px;
    height: 28px;
}

.fab button.mainb:active+ul,
.fab button.mainb:focus+ul {
    bottom: 45px;
}

.fab ul li label {
    margin-top: 5px !important;
    margin-right: -10px;
    margin-left: 20px;
    border-top-right-radius: 0px;
    width: 105px;
    font-size: 12px;
}

small {
    color: #666;
    font-size: 13px;
}

#paciente {
    width: 20%;
    margin-right: 10px;
    border-left: 1px solid #ddd;
}

#cpf {
    width: 10%;
    margin-top: auto;
    margin-bottom: auto;
    border-left: 1px solid #ddd;
}

#data {
    margin-bottom: auto;
    margin-top: auto;
    width: 8%;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
}

.img-circle {
    border-radius: 50%;
    width: 80px !important;
    height: 80px !important;
}

#acoes {
    width: 21%;
}

.date {
    margin-top: 10px !important;
}

.md-chip {
    margin-left: 20px;
}

.spacer-card {
    padding: 5px;
    padding-left: 20px;
    padding-top: 30px;
}

.panel_initial {
    border-bottom-left-radius: 0px !important;
    border: 1px solid #ddd;
    border-top-left-radius: 0px !important;
}

.finish_panel {
    border-bottom-right-radius: 0px !important;
    border-top-right-radius: 0px !important;
    border: 1px solid #ddd;
}

.Title-b {
    font-weight: bolder;
    color: #0983ff;
}

.panel_button {
    border: 1px solid #dcdbdb !important;
    border-radius: 0px;
}

.input-align {
    margin-left: 10px;
}

table {
    background: #fff;
}

.card_table {
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card_table:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.div_img {
    margin-left: 25px;
}

.div_paciente {
    margin-top: auto;
    margin-bottom: auto;
    margin-left: 25px;
}

.label-paciente {
    margin-top: auto;
    margin-bottom: auto;
}

.star_point {
    font-size: 20px;
    color: #ffc107;
}

.mat-mdc-icon-button:hover {
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.19);
}

.cor {
    background: white;
    color: #1265b9;
}

.adc-agend {
    max-width: 250px;
    margin-left: -35%;
}
.btn-adc {
    margin-left: -2px;
}
.custom-search {
    margin-left: -36px;
    margin-top: -7px;
}
.div-mobile {
    display: none;
}
.div-desktop {
    display: flex; 
    justify-content: center;
}

@media (max-width: 1800px) {
    .adc-agend {
        margin-left: -33%;
    }
}

@media (max-width: 1630px) {
    .legenda-btn {
        margin-left: 12px;
    }
}

@media (max-width: 1560px) {
    .adc-agend {
        margin-left: -30%;
    }
}

@media (max-width: 1370px) {
    .legenda-btn {
        margin-left: 8px;
    }
    .adc-agend {
        margin-left: -28%;
    }
}

@media (max-width: 1300px) {
    .input-group {
        margin-left: 15px;
    }  
}

@media (max-width: 1150px) {
    .input-group-btn {
        margin-left: -20px;
    }
    .btn-adc {
        margin-left: 0px;
    }
    .pesquisar-agend {
        margin-left: -10px;
    }
}

@media (max-width: 1070px) {
    .legenda-btn {
        margin-left: -2px;
    }
}

@media (max-width: 1050px) {
    .input-group {
        margin-left: 40px;
    }
    .adc-agend {
        margin-left: -33%;
    }
}

@media (max-width: 860px) {
    .input-group {
        margin-left: 50px;
    }
    .tabela {
        margin-left: -15px;
    }
}

@media (max-width: 800px) {
    .div-mobile {
        display: block;
        margin: 0 auto;
        text-align: center;
        justify-content: center;
        align-items: center;
        padding: 0;
    }
    .div-desktop {
        display: none;
    }
    .pesquisar-agend {
        max-width: 96%;
        margin: 0;
        padding: 0;
        margin-left: -22px;
    }
    .adc-agend {
        max-width: 92.4%;
        margin: 0 auto;
        padding: 0;
    }
    .legenda-btn {
        margin: 0 auto;
    }
    .tabela {
        margin-left: -283%;
    }
    .input-group-btn {
        margin-left: -26px;
    }
    .mother-div {
        padding: 10px;
    }
}

@media (max-width: 780px) {
    .spacer-card {
        margin-top: 10px;
    }
}

@media (max-width: 770px) {
    .input-group-btn {
        margin-left: -30px;
    }
}

@media (max-width: 767px) {
    .tabela {
        margin-left: -187%;
    }
}

@media (max-width: 687px) {
    .input-group-btn {
        margin-left: -34px;
    }
    .tabela {
        margin-left: -185%;
    }
}

@media (max-width: 660px) {
    .tabela {
        margin-left: -167%;
    }
}

@media (max-width: 649px) {
    .no-mobile-card {
        display: none;
    }
    .card-info-agend {
        max-width: 95%;
        margin: 20px 15px;
    }
    .text-tipo-agend {
        font-family: Cairo, sans-serif;
        font-size: 18px;
        padding: 10px;
        text-align: left;
    }
    .tabela {
        left: -7%;
    }
    .div-mobile {
        padding: 5px;
    }
    .adc-agend {
        max-width: 93%;
    }
    .input-group-btn {
        margin-left: -40px;
    }
}

@media (min-width: 601px) {
    .coluna {
        margin-left: -14px;
        margin-bottom: -20px;
        margin-top: 10px;
    }
}

@media (max-width: 320px) {
    .margem-c {
        margin-left: -10px;
    }
    .col-table table {
        border: none !important;
    }
    tr:hover {
        background: unset;
    }
    .col-table thead {
        font-size: 11px;
        background: #fff;
        color: #666;
    }
    .col-table i {
        font-size: 18px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .col-table mat-icon {
        font-size: 20px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .value-color {
        // color: #1265b9 !important;
        font-size: 13px;
        padding: 0 !important;
        padding-left: 5px !important;
        padding-right: 5px !important;
        text-align: center !important;
    }
}

@media (max-width: 1380px) {
    .svg-icon {
        width: 20px;
    }
    // .mat-icon-button {
    //   width: 22px !important;
    //   height: 40px !important;
    // }
    // .material-icons {
    //   font-size: 18px !important;
    // }
}

.align-button {
    // position: absolute;
    top: 40px;
    margin-left: 5.5%;
}

.align-buttonII {
    margin-left: 38px;
    top: 40px;
}

.mini-mini {
    width: 10px;
    height: 10px;
    background: $primary;
}

.mini-miniI {
    margin-right: 109px;
}

.mini-miniII {
    margin-right: 77px;
}

.mini-miniIII {
    margin-right: 110px;
}

.fab ul li label {
    margin-top: 5px !important;
    margin-right: -10px;
    margin-left: 20px;
    border-top-right-radius: 0px;
    width: 105px;
    font-size: 12px;
}

@media (max-width: 425px) {
    .md-chip {
        margin-left: 0;
    }
    .fonte-tamanho {
        font-size: 15px;
        word-break: break-all;
        vertical-align: text-top;
    }
}

@media (max-width: 600px) {
    .div_paciente {
        margin-top: auto;
        margin-bottom: auto;
        margin-left: 25px !important;
    }
    .fonte-tamanho {
        font-size: 16px;
        vertical-align: text-top;
        font-family: Cairo, sans-serif;
        font-weight: 600;
    }
    .coluna {
        margin-left: 0px;
        margin-right: 0px;
        margin-bottom: -15px;
        margin-top: 15px;
    }
    .header-card {
        margin-top: 20px;
        margin-bottom: 0px;
    }
    .tabela {
        margin-left: -173%;
    }
    .btn-legenda {
        font-family: Cairo, sans-serif;
    }
    .input-group-btn {
        margin-left: -44px;
    }

}

@media (max-width: 575px) {
    .tabela {
        margin-left: 14%;
        max-width: 40%;
    }
    .mother-div {
        border-radius: 10px;
    }
}

@media (max-width: 542px) {
    .input-group-btn {
        margin-left: -46px;
    }
}

@media (max-width: 530px) {
    .input-group-btn {
        margin-left: -50px;
    }
    .adc-agend {
        max-width: 94%;
    }
}

@media (max-width: 520px) {
    .tabela {
        margin-left: 15%;
    }
}

@media (max-width: 504px) {
    .input-group-btn {
        margin-left: -52px;
    }
}

@media(max-width:480px) {
    .text-align {
        text-align: center;
    }
}

.button-interative {
    margin-right: 0 !important;
}

@media (max-width: 455px) {
    .pesquisar-agend {
        margin-left: -5px;
    }
    .adc-agend {
        margin-left: 2px;
    }
    .tabela {
        margin-left: 15%;
    }
}

@media (max-width: 448px) {
    .input-group-btn {
        margin-left: -53px;
    }
    .adc-agend {
        margin-left: 3px;
        max-width: 93%;
    }
}

@media (max-width: 421px) {
    .input-group-btn {
        margin-left: -54px;
    }
}

@media (max-width: 420px) {
    .btn-add-tipoagend {
        width: 100%;
        border-radius: 5px;
    }
    .adc-agend {
        margin-left: 0px;
        max-width: 100%;
    }
    .pesquisar-agend {
        margin-left: 0;
        max-width: 100%;
    }
    .input-group-btn {
        margin: 0 auto;
        justify-content: center;
        padding: 0;
    }
    .tabela {
        border-collapse: unset;
        border-radius: 5px;
        max-width: 82%;
        margin: 0 auto;
        padding: 0;
        right: -45px;
    }
}

@media (max-width: 400px) {
    .tabela {
        margin-left: 16%;
        max-width: 81%;
    }
}

@media (max-width: 370px) {
    .tabela {
        margin-left: 17%;
        max-width: 80%;
    }
    .div-mobile {
        padding: 2px;
    }
}

@media (max-width: 364px) {
    .adc-agend {
        margin-left: 0px;
    }
}

@media (max-width: 330px) {
    .tabela {
        max-width: 77%;
        margin: 0 auto;
        justify-content: center;
        text-align: center;
        padding: 0;
        left: -16px;
    }
}