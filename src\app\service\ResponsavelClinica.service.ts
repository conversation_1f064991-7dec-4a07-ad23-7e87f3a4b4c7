import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class ResponsavelClinicaService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public SalvarResponsavel(responsavel: any): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/ResponsavelClinica/SalvarResponsavel', responsavel);
        
    }
    
    public GetGridResponsaveis(idClinica:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ResponsavelClinica/GetGridResponsaveis/'+ idClinica);
        
    }
    
    public InativarResponsavel(idResponsavel:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/ResponsavelClinica/InativarResponsavel/' + idResponsavel);
    }
}