// Importando fontes
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');


// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin transition {
  transition: all $transition ease;
}

// Estilos Globais
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// Container principal
.principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  margin-bottom: 20px;
  border: none !important;
  border-top:4px solid #2E8B57 !important ;

}

// Cabeçalho
.header {
  margin-bottom: 24px;
  
  .header-title {
    display: flex;
    align-items: center;
  }
}

.title-page {
  font-weight: 600;
  color: $primary-color !important;
  font-size: 20px;
  text-transform: uppercase;
  padding-left: 12px;
  margin: 0;
}

.icon-page-title {
  background-color: rgba($primary-color, 0.1);
  border-radius: 100%;
  @include flex-center;
  width: 48px;
  height: 48px;
  
  .material-icons {
    color: $primary-color;
    font-size: 24px;
  }
}

// Toolbar com pesquisa e botões
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

// Barra de pesquisa
.pesquisa-form {
  flex: 1;
  min-width: 250px;
  
  mat-form-field {
    width: 100%;
    
    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
  }
  
  .custom-search {
    color: $primary-color;
    font-size: 20px;
  }
}

// Container da lista
.forms-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: scroll;
  max-height: 58vh;
  padding-right: 8px;
}
.scroll {
  background-color:$bg-color;
  border-radius:$border-radius;
  padding: 8px;

}

// Lista de formulários
.linha-tudo {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  @include transition;
  justify-content: space-between;
  gap: 16px;
  border: 1px solid $border-color;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow;
    background-color: rgba($primary-light, 0.05);
  }
}

// Informações dos formulários
.dados-tabela {
  display: flex;
  padding: 0;
}

.infos-itens {
  align-self: center;
  
  p {
    margin: 4px 0;
    font-size: 14px;
    color: $text-primary;
    display: flex;
    align-items: center;
    
    .material-icons {
      margin-right: 8px;
      color: $primary-color;
      font-size: 20px;
    }
  }
}

// Data de cadastro
.data-tabela {
  align-self: center;
  padding: 0;
  
  p {
    font-size: 14px;
    margin: 0;
    color: $text-secondary;
    display: flex;
    align-items: center;
    
    .material-icons {
      margin-right: 8px;
      color: $primary-color;
      font-size: 20px;
    }
  }
}

// Botões de ação
.btns-tabela {
  text-align: end;
  align-self: center;
  display: flex;
  justify-content: flex-end;
}

.panel_button {
  color: $primary-color;
  margin: 0 4px;
  @include transition;
  background-color: rgba($primary-color, 0.05);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  
  .material-icons {
    font-size: 20px;
    color: inherit;
  }
  
  &:hover {
    background-color: rgba($primary-color, 0.1);
    transform: scale(1.05);
  }
  
  &:last-child {
    color: $error-color;
    background-color: rgba($error-color, 0.05);
    
    &:hover {
      background-color: rgba($error-color, 0.1);
    }
  }
}

// Mensagem de lista vazia
.empty-list {
  padding: 40px 0;
  text-align: center;
  color: $text-secondary;
  
  .material-icons {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: $secondary-dark;
    margin: 0 auto 16px;
    display: block;
  }
  
  p {
    font-size: 16px;
    color: $text-secondary;
    margin-bottom: 16px;
  }
  
  button {
    background-color: transparent;
    color: $primary-color;
    border: 1px solid $primary-color;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    
    &:hover {
      background-color: rgba($primary-color, 0.05);
    }
  }
}

// Modal de confirmação
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  @include flex-center;
  
  .container {
    background-color: $card-bg;
    border-radius: $border-radius;
    padding: 32px;
    max-width: 450px;
    width: 90%;
    text-align: center;
    box-shadow: $box-shadow;
    
    .modalTitulo {
      font-size: 20px;
      font-weight: 600;
      color: $primary-color;
      margin-bottom: 20px;
      
      &:nth-child(2) {
        color: $text-primary;
        font-size: 16px;
        font-weight: 400;
      }
    }
    
    .gp_bt_modal {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 32px;
    }
    
    .bt_n_modal, .bt_s_modal {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      @include transition;
      cursor: pointer;
      border: none;
    }
    
    .bt_n_modal {
      background-color: $primary-color;
      color: $secondary-light;
      
      &:hover {
        background-color: $primary-dark;
        transform: translateY(-2px);
      }
    }
    
    .bt_s_modal {
      background-color: $error-color;
      color: $secondary-light;
      
      &:hover {
        background-color: darken($error-color, 10%);
        transform: translateY(-2px);
      }
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    width: 100%;
  }
  
  .btn-adicionar {
    width: 100%;
  }
  
  .linha-tudo {
    flex-direction: column;
    padding: 16px;
  }
  
  .dados-tabela, .data-tabela {
    width: 100%;
  }
  
  .btns-tabela {
    width: 100%;
    justify-content: flex-start;
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .principal {
    padding: 16px;
  }
  
  .title-page {
    font-size: 16px;
  }
  
  .icon-page-title {
    width: 40px;
    height: 40px;
    
    .material-icons {
      font-size: 20px;
    }
  }
  
  .infos-itens p, .data-tabela p {
    font-size: 12px;
  }
  
  .modal .container {
    padding: 24px;
    
    .modalTitulo {
      font-size: 18px;
      
      &:nth-child(2) {
        font-size: 14px;
      }
    }
    
    .bt_n_modal, .bt_s_modal {
      padding: 10px 20px;
      font-size: 14px;
    }
  }
}


.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}
// Modal styles
.modal-content {
  background-color: white;
  border-radius: $border-radius;
  overflow: hidden;
}

.modal-header {
  padding: 16px 24px;
  background-color: $primary-color;
  
  .modal-title {
    margin: 0;
    color: white;
    font-size: 1.25rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .warning-title {
    color: white;
  }
}

.modal-body {
  padding: 24px;
  
  .modal-message {
    margin: 0;
    font-size: 1rem;
    color: $text-primary;
  }
  
  .image-editor-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    justify-content: center;
  }
  
  .image-cropper-container {
    max-width: 300px;
    margin: 0 auto;
    
    ::ng-deep image-cropper {
      display: block;
      max-width: 100%;
    }
  }
}
.btn-excluir {
  background-color: #d85959;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-excluir:hover {
  background-color: #dc2626;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.alerta-inativo mat-icon {
  color: $error-color;
  margin-bottom: 8px;
}

.alerta-inativo p {
  color: $error-color;
  font-weight: 500;
}

.alerta-subtexto {
  font-size: 12px;
  margin-top: 4px;
}

.medico-nome-modal {
  font-weight: 600;
  color: $primary-color;
  font-size: 18px;
  margin-top: 8px;
}

.modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
  background-color: #f9fafb;
}

.btn-cancelar {
  background-color: #f3f4f6;
  color: $text-secondary;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all $transition;
}

.btn-cancelar:hover {
  background-color: #e5e7eb;
}
.container-lista-scroll{
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 8px;
  margin-bottom: 24px;
  height: 60vh;
}