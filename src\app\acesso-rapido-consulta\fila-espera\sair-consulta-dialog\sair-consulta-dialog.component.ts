// sair-consulta-dialog.component.ts
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-sair-consulta-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatDividerModule
  ],
  template: `
    <div class="modal-header p-t-20 p-b-20">
      <div class="row">
        <div class="col-12">
          <h1 class="little-title fw-700" style="padding-left: 3vh; text-align: center">
            Ao sair sua consulta será cancelada
            <br>você tem certeza que deseja sair?
          </h1>
        </div>
      </div>
    </div>

    <mat-divider></mat-divider>
    
    <div class="row-button text-center p-t-20" mat-dialog-actions>
      <button mat-flat-button (click)="confirmarSaida()" class="btn-primary">
        Sair
      </button>
      <button mat-flat-button (click)="cancelar()" class="btn-primary" style="margin-left: 10px;">
        Cancelar
      </button>
    </div>
  `,
  styleUrls: ['./sair-consulta-dialog.component.scss']
})
export class SairConsultaDialogComponent {

  constructor(
    private dialogRef: MatDialogRef<SairConsultaDialogComponent>
  ) { }

  confirmarSaida(): void {
    this.dialogRef.close('confirmar');
  }

  cancelar(): void {
    this.dialogRef.close('cancelar');
  }
}