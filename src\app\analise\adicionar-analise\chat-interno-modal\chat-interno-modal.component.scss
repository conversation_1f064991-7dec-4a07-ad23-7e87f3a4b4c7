/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$warning-color: #FFC107; /* Amarelo para mensagens importantes */
$warning-light: #FFF8E1; /* Fundo amarelo claro */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$my-message-bg: #E8F5E9; /* Fundo das minhas mensagens */
$other-message-bg: #F5F5F5; /* Fundo das mensagens de outros */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
    .modal-container{
        padding: 20px;
    }
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.view-only-badge {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: normal;
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo do chat */
.modal-content {
    flex: 1;
    padding: 20px;
    background-color: $secondary-light;
    overflow-y: auto;
    display: flex;
    flex-direction: column-reverse;
}

.messages-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    width: 100%;
}

/* Wrapper da mensagem para posicionamento */
.message-wrapper {
    display: flex;
    width: 100%;
    position: relative;
    margin-bottom: 8px;
    
    &.my-message {
        justify-content: flex-end;
    }
    
    &.other-message {
        justify-content: flex-start;
    }
}

/* Bolha da mensagem */
.message {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: $border-radius;
    background-color: $other-message-bg;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: all $transition ease;
    
    .my-message & {
        background-color: $my-message-bg;
        border-bottom-right-radius: 4px;
    }
    
    .other-message & {
        background-color: $other-message-bg;
        border-bottom-left-radius: 4px;
    }
    
    /* Estilo para mensagens importantes */
    &.important {
        background-color: $warning-light;
        border-left: 4px solid $warning-color;
    }
}

/* Componentes da mensagem */
.message-user {
    font-weight: 600;
    font-size: 14px;
    color: $primary-dark;
    margin-bottom: 6px;
    
    .other-message & {
        color: $text-primary;
    }
}

.message-text {
    color: $text-primary;
    font-size: 14px;
    line-height: 1.5;
    word-break: break-word;
    margin-bottom: 6px;
}

.message-timestamp {
    font-size: 11px;
    color: $text-secondary;
    text-align: right;
}

/* Estado vazio - nenhuma mensagem */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: $text-secondary;
    text-align: center;
}

.empty-icon {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: $secondary-dark;
    margin-bottom: 12px;
}

/* Rodapé do chat */
.modal-footer {
    padding: 16px 20px;
    background-color: $secondary-color;
    border-top: 1px solid $border-color;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.view-only-footer {
    display: flex;
    justify-content: flex-end;
}

/* Área de input da mensagem */
.message-input-container {
    width: 100%;
}

.message-input {
    width: 100%;
    height: 39px;
}

::ng-deep .message-input .mat-mdc-form-field-subscript-wrapper {
    height: 0;
}

::ng-deep .message-input textarea {
    resize: none !important;
}

/* Ações da mensagem */
.message-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

/* Checkbox de mensagem importante */
::ng-deep .important-checkbox .mdc-checkbox .mdc-checkbox__background {
    border-color: $primary-color !important;
}

::ng-deep .important-checkbox .mdc-checkbox--selected .mdc-checkbox__background {
    background-color: $primary-color !important;
    border-color: $primary-color !important;
}
::ng-deep .mat-mdc-text-field-wrapper {
    height: 35px
}
/* Botões de ação */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 100px;
}

.send-button {
    background-color: $primary-color;
    color: white;
    max-width: 200px;
    margin-top:10px ;
}

.send-button:hover:not(:disabled) {
    background-color: $primary-dark;
    transform: translateY(-2px);
}

.send-button:disabled {
    background-color: $secondary-dark;
    color: $text-secondary;
    cursor: not-allowed;
}

.close-chat-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.close-chat-button:hover {
    background-color: darken($secondary-dark, 5%);
}

/* Estilo da barra de rolagem */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: $secondary-color;
    border-radius: $border-radius;
}

.modal-content::-webkit-scrollbar-thumb {
    background: $primary-light;
    border-radius: $border-radius;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: $primary-color;
}

/* Responsividade */
@media (max-width: 768px) {
    .message {
        max-width: 90%;
    }
    
    .modal-content {
        padding: 12px;
    }
    
    .modal-footer {
        padding: 12px;
    }
    
    .message-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .action-button {
        width: 100%;
    }
}