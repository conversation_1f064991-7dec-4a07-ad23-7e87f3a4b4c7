import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-modal-informacao',
    templateUrl: './modal-informacao.component.html',
    styleUrls: ['./modal-informacao.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon
    ]
})
export class ModalInformacaoComponent implements OnInit {

  constructor(

    public dialogRef: MatDialogRef<ModalInformacaoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: objModalInformacaoContent
  ) { }

  ngOnInit(): void {
  }

  fecharModal() {
    this.dialogRef.close();
  }

}


export class objModalInformacaoContent{
  strTitulo: string = "";
  strCorpo: string = "";
}