import { LocalStorageService } from './../service/LocalStorageService';
import { SalaService } from './../service/sala.service';
import { Component, OnInit } from '@angular/core';
import { SalaModelview } from '../model/salas';
import { Router } from '@angular/router';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-listar-salas',
  templateUrl: './listar-salas.component.html',
  styleUrls: ['./listar-salas.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatLabel,
    MatIconModule,
    MatFormFieldModule,
    TranslateModule,
    MatSlideToggleModule
  ]
})
export class ListarSalasComponent implements OnInit {

  constructor(
    private router: Router,
    private spinner: SpinnerService,
    private salaService: SalaService,
    private snackbar: AlertComponent,
    private localstorage: LocalStorageService
  ) { }

  //#region Variaveis
  listaSalas: SalaModelview[] = [];
  flgmodal: boolean = false;
  flgInativos: boolean = false;
  pesquisaSala: string = "";

  //#endregion

  async ngOnInit() {
    await this.GetListaSala();
  }


  //#region AdicionarSala
  AdicionarSala() {
    this.localstorage.idSala = null;
    this.AcessarTelaCadastraSala();
  }
  //#endregion

  //#region EditarSala
  editarSala(id: number) {
    this.localstorage.idSala = id;
    this.AcessarTelaCadastraSala();
  }
  //#endregion

  //#region Lista
  // ordenar lista
  // carregar lista
  async GetListaSala() {
    this.spinner.show();

    await this.salaService.GetListaSala(this.pesquisaSala, this.flgInativos).subscribe((ret) => {
      this.listaSalas = ret;
      this.spinner.hide();
    }, () => {
      this.snackbar.sucessoSnackbar('Ocurreu um erro tentando carregar a sala.')
    })
    this.spinner.hide();
  }


  //#endregion



  //#region Ações
  AcessarTelaCadastraSala() {
    this.router.navigate(["/cadastrosala"])
  }
  //#endregion]

  EditarStatusSala(item: SalaModelview) {
    
    item.flgInativo = !item.flgInativo;
    this.spinner.show();
    this.salaService.SalvarSala(item).subscribe(
      () => {
        this.snackbar.sucessoSnackbar("Status alterado com sucesso.");
        this.GetListaSala();
        this.spinner.hide();
      },
      () => {
        this.snackbar.sucessoSnackbar("Falha ao alterar o status.");
        this.spinner.hide();
      }
    )
  }
}
