import { Injectable, EventEmitter } from '@angular/core';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';
import { Login } from '../model/login';
import { HttpParams, HttpHeaders, HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';


@Injectable({
    providedIn: 'root'
})
export class AuthService {

  // private usuarioAutenticado: boolean = false;

  mostrarMenuEmitter = new EventEmitter<boolean>();

  constructor(private router: Router, private http: HttpClient, ) { }

  fazerLogin(dadosLogin: Login) {
    if (dadosLogin.cpf === '111.111.111-11' && dadosLogin.senha === 'teste') {
      this.router.navigate(['/']);
    }
    else {
      sessionStorage.setItem("Logado", "true")
      window.location.href = "/dashboard";
    }
  }


  public EsqueciSenha(cpf: string, email: string): Observable<any> {
    let params = new HttpParams();
    params = params.append('email', String(email));
    params = params.append('cpf', String(cpf));

    return this.http.get(environment.apiEndpoint + '/Usuario/EsqueciSenha', { params });
  }

  AutenticaUsuario(userName:any, password:any, sGuid:any, ModoLogin:any) {
    
    var data = "Login=" + userName + "&Password=" + password + "&ModoLogin=" + ModoLogin + "&grant_type=password" + "&Guid=" + sGuid;
    var reqHeader = new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded', 'No-Auth': 'True' });
    return this.http.post(environment.apiEndpoint + "/Usuario/AutenticaUsuario", data, { headers: reqHeader }).toPromise();
  }

  // public Logoff(idUsuario) {
  //   return this.http.get(environment.apiEndpoint + "/Usuario/Logoff/" + idUsuario);
  // }

}