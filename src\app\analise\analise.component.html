<!-- CONTAINER PRINCIPAL -->
<div class="container">
  <mat-card class="card-principal">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">analytics</span>
      </div>
      <h2 class="header-title">Cadastro de Análises</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">
      <div class="busca-container small">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>Buscar</mat-label>
          <input matInput type="search" name="pesquisa" [(ngModel)]="pesquisa" (keyup.enter)="filtrarAnalise()">
          <button mat-icon-button matSuffix (click)="filtrarAnalise()" class="btn-busca">
          </button>
        </mat-form-field>
      </div>
      
      <button class="btn-adicionar" (click)="adicionaranalise()">
        <mat-icon>add</mat-icon>
        <span>Adicionar <PERSON></span>
      </button>
    </div>

    <!-- TOGGLES DE FILTRO -->
    <div class="filtro-toggles">
      <div class="toggle-item">
        <mat-slide-toggle [(ngModel)]="FlgOrientacaoMedicaPendente" name="orientacaoMedicaPendente"
          (change)="CarregaListaAnalise()">
          Orientação Médica Pendente
        </mat-slide-toggle>
      </div>

      <div class="toggle-item">
        <mat-slide-toggle [(ngModel)]="flgOrientacaoNutricionalPendente" name="orientacaoNutricionalPendente"
          (change)="CarregaListaAnalise()">
          Orientação Nutricional Pendente
        </mat-slide-toggle>
      </div>

      <div class="toggle-item">
        <mat-slide-toggle [(ngModel)]="flgOrientacaoFisicaPendente" name="orientacaoFisicaPendente"
          (change)="CarregaListaAnalise()">
          Orientação Física Pendente
        </mat-slide-toggle>
      </div>
    </div>

    <!-- LISTA DE ANÁLISES -->
    <div class="lista-container">
      <div class="lista-scroll">
        <!-- ITEM DE ANÁLISE -->
        <div class="analise-card" *ngFor="let analise of listaAnalise">
          <div class="analise-info">
            <div class="analise-avatar">
              <img [src]="ImagemPessoa" alt="Foto de Perfil">
            </div>
            <div class="analise-detalhes">
              <h3 class="analise-nome">{{analise.nomePaciente}}</h3>
              <p class="analise-data">Cadastro: {{analise.dtaCadastro | date: 'dd/MM/yyyy'}}</p>
            </div>
          </div>

          <div class="analise-status">
            <div class="status-item">
              <div class="status-icon">
                <img src="/assets/build/img/cuidados-de-saude.svg" alt="Médica">
              </div>
              <div class="status-indicator" [ngClass]="analise.flgOrientacaoMedica == 'Sim' ? 'status-ok' : 'status-pendente'">
                <mat-icon>{{analise.flgOrientacaoMedica == 'Sim' ? 'check_circle' : 'cancel'}}</mat-icon>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon">
                <img src="/assets/build/img/nutricionista.svg" alt="Nutricional">
              </div>
              <div class="status-indicator" [ngClass]="analise.flgOrientacaoNutricional == 'Sim' ? 'status-ok' : 'status-pendente'">
                <mat-icon>{{analise.flgOrientacaoNutricional == 'Sim' ? 'check_circle' : 'cancel'}}</mat-icon>
              </div>
            </div>

            <div class="status-item">
              <div class="status-icon">
                <img src="/assets/build/img/exercicio.svg" alt="Física">
              </div>
              <div class="status-indicator" [ngClass]="analise.flgOrientacaoFisica == 'Sim' ? 'status-ok' : 'status-pendente'">
                <mat-icon>{{analise.flgOrientacaoFisica == 'Sim' ? 'check_circle' : 'cancel'}}</mat-icon>
              </div>
            </div>
          </div>

          <div class="analise-acoes">
            <button mat-icon-button *ngIf="analise.flgMensagemImportante" (click)="abreModalChat(analise.idAnalise)" matTooltip="Abrir chat">
              <mat-icon>chat</mat-icon>
            </button>
            <button mat-icon-button (click)="editar(analise.idAnalise)" matTooltip="Editar análise">
              <mat-icon>edit</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="listaAnalise.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhuma análise encontrada</p>
        </div>
      </div>
    </div>
  </mat-card>
</div>

<!-- MODAL DO CHAT -->
<ngx-smart-modal #chatInternoModal identifier="chatInternoModal" customClass="chat-modal emailmodal" (onClose)="fechaModal('chatInternoModal')">
  <div class="chat-modal-content">
    <div class="chat-header">
      <h2>Chat Interno</h2>
    </div>

    <div class="chat-body">
      <!-- MENSAGEM DO SISTEMA QUANDO NÃO HÁ MENSAGENS -->
      <div class="mensagem-sistema" *ngIf="listaMensagens.length < 1">
        <div class="msg-wrapper">
          <p class="msg-autor">Sistema</p>
          <p class="msg-texto">Não há mensagens disponíveis no chat</p>
          <p class="msg-hora">agora</p>
        </div>
      </div>
      
      <!-- LISTA DE MENSAGENS -->
      <div *ngFor="let msg of listaMensagens; let even = even" [ngClass]="even ? 'msg-esquerda': 'msg-direita'">
        <div class="mensagem" [ngClass]="msg.flgMensagemImportante ? 'msg-importante' : 'msg-normal'">
          <p class="msg-autor">{{msg.nomeMedico}}</p>
          <p class="msg-texto">{{msg.mensagem}}</p>
        </div>
      </div>
    </div>
  </div>
</ngx-smart-modal>