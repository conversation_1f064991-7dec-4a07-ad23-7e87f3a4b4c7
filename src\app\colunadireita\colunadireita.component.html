<!-- Right Column - Desktop Stats Dashboard
<div class="right-column-container">
  <div class="stats-card" *ngIf="MedicoPermissao || AtendentePermissao">
    <div class="stats-header">
      <div class="stats-header-left">
        <p>{{ mesanterior! | calendarDate:(view + 'ViewTitle'):locale}}</p>
      </div>
      <div class="stats-header-right">
        <p>{{ viewDate! | calendarDate:(view + 'ViewTitle'):locale}}</p>
      </div>
    </div>

    <div *ngIf="MedicoPermissao" class="stats-section-title">
      <span>Médico</span>
    </div>
    
    <div class="stats-row">
      <div class="stats-item previous">
        <div class="stats-label">{{ 'TELACOLUNADIREITA.CONSULTAS' | translate }}</div>
        <div class="stats-value">{{ConsultasAnterior}}</div>
      </div>
      <div class="stats-icon">
        <mat-icon>calendar_today</mat-icon>
      </div>
      <div class="stats-item current">
        <div class="stats-value">{{ConsultasAtual}}</div>
        <div class="stats-label">{{ 'TELACOLUNADIREITA.CONSULTA' | translate }}</div>
      </div>
    </div>
    
    <div class="stats-row">
      <div class="stats-item previous">
        <div class="stats-label">{{ 'TELACOLUNADIREITA.VOTACOES' | translate }}</div>
        <div class="stats-value">{{AvaliacaoAnterior}}</div>
      </div>
      <div class="stats-icon">
        <mat-icon>star</mat-icon>
      </div>
      <div class="stats-item current">
        <div class="stats-value">{{AvaliacaoAtual}}</div>
        <div class="stats-label">{{ 'TELACOLUNADIREITA.VOTACOES' | translate }}</div>
      </div>
    </div>

    <div *ngIf="MedicoPermissao" class="stats-section-title">
      <span>Clínica</span>
    </div>
    
    <div class="stats-row">
      <div class="stats-item previous">
        <div class="stats-label">{{ 'TELACOLUNADIREITA.PACIENTES' | translate }}</div>
        <div class="stats-value">{{PacientesAnterior}}</div>
      </div>
      <div class="stats-icon">
        <mat-icon>people</mat-icon>
      </div>
      <div class="stats-item current">
        <div class="stats-value">{{PacientesAtual}}</div>
        <div class="stats-label">{{ 'TELACOLUNADIREITA.PACIENTES' | translate }}</div>
      </div>
    </div>

    <div class="stats-totals">
      <div class="total-item">
        <div class="total-label">{{ 'TELACOLUNADIREITA.TOTALDEPACIENTES' | translate }}</div>
        <div class="total-value">{{TotalPacientes}}</div>
      </div>
      <div class="total-item">
        <div class="total-label">{{ 'TELACOLUNADIREITA.TOTALDECONSULTAS' | translate }}</div>
        <div class="total-value">{{TotalConsultas}}</div>
      </div>
    </div>
  </div>

  <div class="profile-card">
    <div class="profile-header" *ngIf="!MedicoPermissao && !AtendentePermissao">
      <div class="profile-avatar">
        <img [src]="ImagemPessoa" alt="User avatar">
      </div>
      <div class="profile-info">
        <h3 class="profile-name">{{this.usuarioLogadoService.getNomeUsuario()}}</h3>
        <p class="profile-role">{{tipoUsuario}}</p>
      </div>
      <div class="profile-actions">
        <button class="settings-button" mat-icon-button>
          <mat-icon>settings</mat-icon>
        </button>
      </div>
    </div>

    <div class="appointments-list" *ngIf="!MedicoPermissao && !AtendentePermissao">
      <div class="appointment-card" *ngFor="let item of consultas">
        <div class="appointment-info">
          <h4 class="doctor-name">{{item.nomeMedico}}</h4>
          <p class="appointment-type">Orientação Médica</p>
          <p class="appointment-date">Data: {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}</p>
        </div>
        <div class="appointment-actions">
          <button mat-icon-button class="navigate-button" (click)="abriConsultas()">
            <mat-icon>navigate_next</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </div>

  <li class="notification-button" *ngIf="!AdmPermissao && !AtendentePermissao && consultaAgora">
    <button mat-fab class="pulse-button" (click)="ngxSmartModalService.getModal('consulta').open()">
      <img src="assets/build/img/coração-com-batimentos-png-1.png" alt="Pulse icon" class="pulse-icon">
    </button>
  </li>
  
</div> -->

<!-- Upcoming Appointments Card -->
<div class="right-column-container">
  <div class="calendar-container">
  <div class="calendar-card">
    <!-- Calendário compacto -->
    <div class="calendar-content">
      <mat-calendar 
        [selected]="selectedDate" 
        (selectedChange)="onDateSelected($event!)"
        [dateClass]="dateClass">
      </mat-calendar>
    </div>
    
    <!-- Seção de consultas para a data selecionada -->
    <div class="calendar-footer" *ngIf="selectedDate">
      <div class="date-display">
        <span class="day-num">{{ selectedDate | date:'dd' }}</span>
        <div class="month-year">
          <span class="month">{{ selectedDate | date:'MMMM' }}</span>
          <span class="year">{{ selectedDate | date:'yyyy' }}</span>
        </div>
      </div>
      
      <div class="appointments-title">
        <span>Consultas</span>
        <span class="appointment-count" *ngIf="getAppointmentsForSelectedDate().length > 0">
          ({{ getAppointmentsForSelectedDate().length }})
        </span>
      </div>
      
      <div class="appointments-list">
        <ng-container *ngFor="let appointment of getAppointmentsForSelectedDate()">
          <div class="appointment-item" [ngClass]="getAppointmentClass(appointment.type)">
            <div class="appointment-time">
              <span class="time">{{ appointment.time }}</span>
              <span class="duration">{{ appointment.duration }}</span>
            </div>
            <div class="appointment-info">
              <span class="appointment-title">{{ appointment.title }}</span>
              <div class="appointment-details">
                <span class="appointment-type" *ngIf="appointment.type === 'first'">Primeira Consulta</span>
                <span class="appointment-type" *ngIf="appointment.type === 'return'">Retorno</span>
                <span class="appointment-type" *ngIf="appointment.type === 'emergency'">Emergência</span>
                <span class="appointment-type" *ngIf="appointment.type === 'exam'">Exames</span>
                <span class="appointment-status" [ngClass]="getStatusClass(appointment.status)">
                  {{ appointment.status === 'confirmed' ? 'Confirmado' : 'Pendente' }}
                </span>
              </div>
            </div>
          </div>
        </ng-container>
        
        <!-- Mensagem quando não há consultas -->
        <div class="no-appointments" *ngIf="getAppointmentsForSelectedDate().length === 0">
          Nenhuma consulta para esta data
        </div>
      </div>
    </div>
  </div>
</div>
</div>