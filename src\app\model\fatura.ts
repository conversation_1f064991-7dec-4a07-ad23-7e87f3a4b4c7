
export class FaturaModelview {
    idFatura?: number;
    desNumero?: number;
    desDocumento?: string;
    dtaCadastro?: Date;
    dtaFechamento?: Date;
    dtaPrevisaoPagamento?: Date;
    descricao?: string;
    flgAbrirNovaFatura?: boolean;
    flgInativo?: boolean;
    idTipoFatura?: number;
    idConvenio?: number;
    desConvenio?: string;
    idTipoComissao?: number;
    idMedico?: number;
    idClinica?: number;
    dtaAbertura?: Date;
    flgFechada?: boolean;
    valor?: number;
    flgFaturaProcedimento?: boolean;
    idPlano?: number;
    idProfessional?: number;
}



export class DadosFaturamento {
    idFatura?: number;
    idConvenio?: number;
    valorConsulta?: number;
    codigoConvenio?: string;
}

export class ObjFaturamento extends DadosFaturamento {
    idConsulta?: number;
}

export class ObjFaturamentoLote extends DadosFaturamento {
    idsConsulta?: number[];
}