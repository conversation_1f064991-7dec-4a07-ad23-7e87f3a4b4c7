import { Component, OnInit } from '@angular/core';
import { ContasReceber } from 'src/app/model/contasReceber';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { FormControl, Validators, FormGroup, AbstractControl, FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ContasPagarReceberService } from 'src/app/service/contas-pagar-receber.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { InputDateComponent } from 'src/app/input-date/input-date.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { NgChartsModule } from 'ng2-charts';
import { MatSelectModule } from '@angular/material/select';


@Component({
    selector: 'app-contas-receber',
    templateUrl: './contas-receber.component.html',
    styleUrls: ['./contas-receber.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      InputDateComponent,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      MatDivider,
      TranslateModule,
      MatFormFieldModule,
      NgxSmartModalModule,
      NgChartsModule,
      MatSelectModule
    ]
})
export class ContasReceberComponent implements OnInit {
  observacoesConta: any;
  serieNotaFiscal: any;
  parcelas: any;
  notaFiscal: any;
  dataBaixa: any;
  descricaoConta: any;
  fornecedor: any;
  idUsuario: any;
  dataRecebimento: any;
  Dados?: any[];
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  autoHide?: number;
  setAutoHide: any;
  action: any;
  actionButtonLabel: any;
  showMessageError?: boolean;
  DtaErrado = false;
  DadosContasReceber?: any[];
  idcontaReceber?: number
  DescricaoVazia = false; 

  listaParcelas = [{ valor: 1, descricao: "1x" }, { valor: 2, descricao: "2x" }, { valor: 3, descricao: "3x" },
  { valor: 4, descricao: "4x" }, { valor: 5, descricao: "5x" }, { valor: 6, descricao: "6x" }, { valor: 7, descricao: "7x" }, { valor: 8, descricao: "8x" }
    , { valor: 9, descricao: "9x" }, { valor: 10, descricao: "10x" }, { valor: 11, descricao: "11x" }, { valor: 12, descricao: "12x" }, { valor: 13, descricao: "13x" }
    , { valor: 14, descricao: "14x" }, { valor: 15, descricao: "15x" }, { valor: 16, descricao: "16x" }, { valor: 17, descricao: "17x" }, { valor: 18, descricao: "18x" }]

  constructor(    
    private spinner: SpinnerService,
    private tradutor: TranslateService,
    private formBuilder: FormBuilder,    
    private contaPagarReceberService: ContasPagarReceberService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent
  ) {

  }
  objContasReceberForm?: FormGroup;

  tipoPagamento: number = 0;

  isParcela = false;

  DadosPagamento:any = [];

  listaFormaPagamento: any = [];

  dtaVencimento?: boolean;

  objContasReceber = new ContasReceber();

  myGroup = new FormGroup({
    firstName: new FormControl()
  });


  ngOnInit() {

    this.DadosContasReceber = [];

    this.GetTipoPagamento();

    this.ListarContaReceber();

    this.objContasReceberForm = this.formBuilder.group({
      Desc: ['', [Validators.required, Validators.minLength(5)]],
      Valor: ['', [Validators.required, maiorQueZero]],
      NotaFiscal: [''],
      SerieNotaFiscal: [''],
      ObsConta: [''],
      NumParcelas: [''],
      formaPagamento: [''],
      fornecedor: [''],
      DataCad: ['', validaData],
      DtaRecebimento: ['', validaData],
      DtaBaixa: ['', validaData]

    })

    this.objContasReceber = new ContasReceber();

  }
  // exibeMensagem(Mensagem, TipoMensagem)
  // {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition =  this.horizontalPosition;
  //     config.duration =  1500;

  //    if(TipoMensagem == "Sucesso")
  //       config.panelClass = ['success-snack'];
  //    else if(TipoMensagem == "Erro")
  //       config.panelClass = ['error-snack'];

  //     this.snackBarAlert.sucessoSnackbar(Mensagem);      
  // }
  GetTipoPagamento() {

    this.contaPagarReceberService.GetTipoPagamento().subscribe((retorno) => {
      
       
      this.listaFormaPagamento = retorno
      this.spinner.hide();

    }, () => {

      
      this.spinner.hide();
    })
  }
  DeletarContaReceber() {

    this.contaPagarReceberService.DeletarContaReceber(this.idcontaReceber).subscribe((retorno) => {
      

      if (retorno) {
        this.LimparCampos();
        this.ListarContaReceber();
        this.ngxSmartModalService.getModal('excluirItem').close();
        this.snackBarAlert.sucessoSnackbar("Excluído com sucesso");
      }
      this.spinner.hide();
    }, () => {
      
      this.spinner.hide();
    })
  }
  AbrirModalExclusao(id:any){

    this.idcontaReceber = id;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }
  EditarContaReceber(idContaReceber:any) {
    
    this.contaPagarReceberService.EditarContaReceber(idContaReceber).subscribe(() => {
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }
  ListarContaReceber() {
    this.contaPagarReceberService.ListarContaReceber().subscribe((retorno) => {
      this.DadosContasReceber = [];
      this.DadosContasReceber = retorno;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  CarregarEdicaoContaReceber(idcontasReceber:any) {

    this.contaPagarReceberService.CarregarEdicaoContaReceber(idcontasReceber).subscribe((retorno: any) => {
      this.objContasReceber = new ContasReceber();
      this.objContasReceber.IdContasReceber = retorno.idcontasReceber;
      this.objContasReceber.DesConta = retorno.descricaoconta;
      this.objContasReceber.DtaRecebimento = retorno.dtaVencimento ? new Date(retorno.dtaVencimento).toLocaleDateString() : "";
      this.objContasReceber.DtaBaixa = retorno.databaixa ? new Date(retorno.databaixa).toLocaleDateString() : "";
      this.objContasReceber.IdTipoPagamento = retorno.formapagamento;
      this.objContasReceber.IdFornecedor = retorno.fornecedor;
      this.objContasReceber.NotaFiscal = retorno.notafiscal;
      this.objContasReceber.ObsConta = retorno.obsconta;
      this.objContasReceber.NumParcelas = retorno.parcelas;
      this.objContasReceber.SerieNotaFiscal = retorno.serienotafiscal;
      this.objContasReceber.VlrConta = retorno.valor;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })

  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }

  ValidaDta(dta:any) {
    this.DtaErrado = false;

    var min = new Date('01/01/ 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaErrado = true;
        return;
      }
      // else if (this.DtaErrado == false)
      //   this.CarregaConsultas()

    }
    return;

  }

  SubirAnexoComprovante() {

  }
  forma: number = 0;

  alterarFormaPagamento(item:any) {
    
    if (item == 1)
      this.isParcela = true;
    else
      this.isParcela = false;
  }
  async carregaFormaPagamento() {
    this.DadosPagamento = [];

    this.tradutor.get('TELAFINANCAS.AVISTACARTAO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.AVISTADINHEIRO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.PARCELADO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.BOLETO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.OUTROS').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });

    ;
    ;
  }

  LimparCampos() {
    this.objContasReceber = new ContasReceber;

  }

  public Submit() {

    

    this.contaPagarReceberService.salvarContaReceber(this.objContasReceber!).subscribe((retorno) => {
      ;

      if (retorno) {
        this.DescricaoVazia = true 
        this.LimparCampos();
        this.ListarContaReceber();
        this.snackBarAlert.sucessoSnackbar("Salvo com sucesso");
      }
      this.spinner.hide();

    }, () => {
      this.spinner.hide();

      
    })
    this.DescricaoVazia = true;
  }
  

}

export function maiorQueZero(control: AbstractControl) {

  if (control.value > 0)
    return null;

  return { maiorZero: true }
}

export function validaData(control: AbstractControl) {

  var min = new Date('01/01/1900 12:00:00')
  var max = new Date('01/01/2100 12:00:00')

  var dtaAgora = new Date(control.value);

  if (control.value != "") {

    if (dtaAgora < min || dtaAgora > max) {
      return { dataInorreta: true }
    }
  }

  if (!dtaAgora)
    return null;

  return null;
}
