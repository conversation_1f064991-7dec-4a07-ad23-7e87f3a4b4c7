/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
  height: 87vh;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-icon {
  background-color: $primary-light;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-icon .material-icons {
  color: $primary-color;
  font-size: 24px;
}

.header-title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
}

/* FILTROS */
.filtros {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.toggle-container {
  display: flex;
  align-items: center;
}

.toggle-item {
  color: $primary-color;
}

.toggle-item ::ng-deep .mat-slide-toggle-bar {
  background-color: rgba(0, 0, 0, 0.1);
}

.toggle-item ::ng-deep .mat-slide-toggle-thumb {
  background-color: white;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-bar {
  background-color: rgba(46, 139, 87, 0.5) !important;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-thumb {
  background-color: $primary-color !important;
}

.busca-container {
  flex-grow: 1;
  max-width: 100%;
}

.busca-field {
  width: 100%;
}

.busca-field ::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

.busca-field ::ng-deep .mat-form-field-flex {
  background-color: $bg-color;
}

.busca-field ::ng-deep .mat-form-field-outline {
  color: $border-color;
}

.btn-busca {
  color: $primary-color;
  background-color: transparent;
}

.adicionar-container {
  display: flex;
  justify-content: flex-end;
}

.btn-adicionar:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* LEGENDA */
.legenda-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;
}

.btn-legenda {
  display: flex;
  align-items: center;
  background-color: $card-bg;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 4px 12px;
  color: $primary-color;
  font-weight: 500;
  transition: all $transition;
}

.btn-legenda:hover {
  background-color: $primary-light;
}

.legenda-container {
  display: flex;
  flex-direction: column;
  background-color: $card-bg;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  width: 300px;
  position: absolute;
  right: 24px;
  z-index: 10;
}

.legenda-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legenda-item:last-child {
  margin-bottom: 0;
}

.legenda-item mat-icon {
  color: $primary-color;
  margin-right: 8px;
  font-size: 18px;
}

/* LISTA DE CONVÊNIOS - DESKTOP */
.lista-container {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 8px;
  margin-bottom: 24px;
}

.lista-scroll {
  max-height: 57vh;
  overflow-y: auto;
  padding-right: 8px;
}

.lista-scroll::-webkit-scrollbar {
  width: 6px;
}

.lista-scroll::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.lista-scroll::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.convenio-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid transparent;
}

.convenio-card:hover {
  transform: translateY(-2px);
  box-shadow: $box-shadow;
  border-left: 4px solid $primary-color;
}

.convenio-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 280px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item mat-icon {
  font-size: 18px;
  color: $primary-color;
  margin-right: 8px;
}

.info-item .nome {
  font-weight: 600;
  color: $text-primary;
}

.convenio-dados {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 240px;
  margin: 0 16px;
}

.dados-item {
  margin-bottom: 8px;
}

.dados-item:last-child {
  margin-bottom: 0;
}

.dados-label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: $primary-color;
  margin-bottom: 2px;
}

.dados-valor {
  color: $text-secondary;
}

.convenio-acoes {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-width: 100px;
  justify-content: flex-end;
  .remove {
    color: #FF6B6B !important;
    background-color: rgba(255, 107, 107, 0.05) !important;
    mat-icon{
      color: #ff6b6b;
      &:hover{
        color: #ffa5a5;
      }
    }
  }
}

.convenio-acoes button {
  width: 36px;
  height: 36px;
  background-color: $primary-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition;
}

.convenio-acoes button:hover {
  background-color: $primary-color;
  transform: scale(1.1);
}

.convenio-acoes button:hover mat-icon {
  color: white;
}

.convenio-acoes mat-icon {
  color: $primary-color;
  font-size: 20px;
  transition: color $transition;
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: $text-secondary;
}

.lista-vazia mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* MOBILE VIEW */
.mobile-view {
  display: none;
}

/* LISTA DE CONVÊNIOS - MOBILE */
.lista-mobile {
  margin-bottom: 24px;
}

.convenio-card-mobile {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
}

.convenio-info-mobile {
  padding: 16px;
}

.convenio-nome-mobile {
  font-size: 18px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
  text-align: center;
}

.convenio-tipo-mobile {
  color: $text-secondary;
  margin: 0 0 16px 0;
  text-align: center;
  font-size: 14px;
}

.info-mobile-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.info-mobile-item {
  margin-bottom: 8px;
}

.info-mobile-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.info-mobile-header mat-icon {
  font-size: 18px;
  color: $primary-color;
  margin-right: 8px;
}

.info-mobile-item h4 {
  font-size: 14px;
  font-weight: 600;
  color: $primary-color;
  margin: 0 0 4px 0;
}

.info-mobile-item p {
  color: $text-secondary;
  margin: 0;
  font-size: 14px;
}

.acoes-mobile {
  position: relative;
  padding: 16px;
  text-align: right;
}

.acoes-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  position: absolute;
  right: 80px;
  bottom: 16px;
  max-width: calc(100% - 80px);
}

.acoes-buttons button {
  background-color: $primary-color;
  color: white;
}

.toggle-button {
  background-color: $primary-color;
  color: white;
}

/* BOTÃO CARREGAR MAIS */
.carregar-mais {
  text-align: center;
  margin-top: 24px;
}

.btn-carregar {
  background-color: $primary-color;
  color: white;
  padding: 8px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all $transition;
}

.btn-carregar:hover {
  background-color: $primary-dark;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* MODAIS */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: $border-radius;
  width: 90%;
  max-width: 400px;
  overflow: hidden;
  box-shadow: $box-shadow;
}

.modal-header {
  background-color: $primary-color;
  color: white;
  padding: 16px;
  text-align: center;
}

.modal-header h3 {
  margin: 0;
  font-weight: 500;
  font-size: 18px;
}

.modal-body {
  padding: 24px;
  text-align: center;
}

.modal-text {
  font-size: 16px;
  color: $text-primary;
  margin-bottom: 8px;
}

.modal-subtexto {
  font-size: 14px;
  color: $text-secondary;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 16px;
  background-color: #f9fafb;
}

.btn-cancelar {
  background-color: #f3f4f6;
  color: $text-secondary;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition;
}

.btn-cancelar:hover {
  background-color: #e5e7eb;
}

.btn-confirmar {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition;
}

.btn-confirmar:hover {
  background-color: $primary-dark;
}

.btn-excluir {
  background-color: $error-color;
}

.btn-excluir:hover {
  background-color: darken($error-color, 10%);
}

/* ESCONDER MODAIS ORIGINAIS */
.hidden-modal {
  display: none !important;
}

/* ANIMAÇÕES PARA BOTÕES MOBILE */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.acoes-buttons button {
  animation: slideIn 0.2s ease-out forwards;
}

/* RESPONSIVIDADE */
@media (max-width: 1024px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toggle-container, .busca-container, .adicionar-container {
    width: 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }
  
  .adicionar-container {
    margin-bottom: 0;
  }
  
  .btn-adicionar {
    width: 100%;
    justify-content: center;
  }
  
  .convenio-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .convenio-info, .convenio-dados, .convenio-acoes {
    width: 100%;
    margin: 0;
    margin-bottom: 16px;
  }
  
  .convenio-acoes {
    margin-bottom: 0;
    justify-content: flex-start;
  }
  
  .legenda-container {
    position: static;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .desktop-view {
    display: none;
  }
  
  .mobile-view {
    display: block;
  }
}