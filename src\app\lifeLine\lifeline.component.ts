import { Component, Inject } from '@angular/core';
import { ConsultaService } from '../service/consulta.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { LifeLineService } from './lifeline.service';
import { NgxSmartModalService } from 'ngx-smart-modal';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslatePipe } from '@ngx-translate/core';
import { MatIcon } from '@angular/material/icon';
import { Mat<PERSON>rawer, MatDrawerContainer, MatDrawerContent } from '@angular/material/sidenav';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { MatSelectModule } from '@angular/material/select';


@Component({
    selector: 'app-lifeline',
    templateUrl: './lifeline.component.html',
    styleUrls: ['./lifeline.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      MatIcon,
      MatDrawerContent,
      MatDrawerContainer,
      MatDrawer,
      MatTabsModule,
      TranslatePipe,
      TruncatePipe,
      MatSelectModule
    ]
})

export class LifelineComponent  {
    constructor(
        private spinner: SpinnerService,
        private consultaService: ConsultaService,
        private usuarioLogadoService: UsuarioLogadoService,
        private lifeLineService: LifeLineService,
        public ngxSmartModalService: NgxSmartModalService,
        private matDialogRef : MatDialogRef<LifelineComponent>,
        private snackBarAlert: AlertComponent,
        @Inject(MAT_DIALOG_DATA) public data: { idPaciente: number }) {
        this.idPaciente = data.idPaciente;
    }

    dadosLifeLine: any = []
    DadosConsultaLifeLine: any = []
    dadosCorpoPaciente: any = []


    dadosLife = false;
    dadosAnonimo?: string;
    MedicoAnonimo?: string;
    Dataanonimo?: string;

    showPanel = false;
    showLife = true;

    idUsu?: number;
    idTipoUsu?: number;
    QntLifeLine: number = 0;

    idTipoUsuarioLogado:any;
    arquivoPdfDownload: any;
    src = "";
    spinnerAnexoDownload: boolean = false;
    PacientePermissao: boolean = false;

    idPaciente: number;

    ngOnInit() {
        if (this.idPaciente){
            this.GetDadosLifeLinePaciente();
            return;
        }

        if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
            this.PacientePermissao = true;
        }

        let dados = this.lifeLineService.varModalLifeLine;
        this.idTipoUsu = dados.tipoUsuario;
        this.idUsu = dados.idUsuario;
        this.lifeline();
    }

    GetDadosLifeLinePaciente(){
        this.spinner.show();

        this.consultaService.GetDadosLifeLinePaciente(this.idPaciente).subscribe((ret) => {
            this.dadosCorpoPaciente = [];

            if (ret.length == 0) {
                this.snackBarAlert.falhaSnackbar("Sem registros")
                this.fecharModal()
            }
            this.dadosLifeLine = ret

            for (var i = 0, len = this.dadosLifeLine.length; i < len; ++i) {
                this.dadosLifeLine[i].posicaoRef = "#" + 1000 + i;
                this.dadosLifeLine[i].posicao = 1000 + i;

                this.dadosCorpoPaciente.push(ret[i].dadosCorpo)

            }

            // PONTO-TESTE
            // this.ngxSmartModalService.getModal('LifeLine').open()
            this.spinner.hide();
        }, () => {
            this.spinner.hide();
        })
    }


    lifeline() {
        this.idTipoUsuarioLogado = this.usuarioLogadoService.getIdTipoUsuario();

        this.consultaService.GetLifeLine(this.idTipoUsu, this.idUsu).subscribe((retorno) => {
            this.dadosCorpoPaciente = [];

            if (retorno.length == 0) {
                this.snackBarAlert.falhaSnackbar("Sem registros")
                this.fecharModal()
            }
            this.dadosLifeLine = retorno

            for (var i = 0, len = this.dadosLifeLine.length; i < len; ++i) {
                this.dadosLifeLine[i].posicaoRef = "#" + 1000 + i;
                this.dadosLifeLine[i].posicao = 1000 + i;

                this.dadosCorpoPaciente.push(retorno[i].dadosCorpo)

            }

            this.spinner.hide();
        }, () => {
            this.spinner.hide();
        })
    }

    DadosPaciente(id:any) {
        this.dadosLife = false
        this.DadosConsultaLifeLine = []
        this.consultaService.GetDadosLifeLine(id).subscribe((retorno) => {

            this.DadosConsultaLifeLine = retorno
            if (this.DadosConsultaLifeLine.anonimo != null) {
                this.dadosAnonimo = this.DadosConsultaLifeLine.anonimo
                this.MedicoAnonimo = this.DadosConsultaLifeLine.medico
                this.Dataanonimo = this.DadosConsultaLifeLine.dtaConsulta
            }
            this.dadosLife = true
            
        })
    }

    // LifeLineVazia() {

    //     let config = new MatSnackBarConfig();
    //     config.verticalPosition = this.verticalPosition;
    //     config.horizontalPosition = this.horizontalPosition;
    //     config.duration = this.setAutoHide ? this.autoHide : 0;
    //     config.panelClass = ['success-snack'];
    //     this.snackBarAlert.sucessoSnackbar("Nenhuma consulta registrada!")
    // }

    abreDate() {
        this.showPanel = !this.showPanel
        this.showLife = false;
    }

    abreLife() {
        this.showLife = !this.showLife
        this.showPanel = false;
    }


    BaixarArquivo(chave:any, nome:any, tipoArquivo:any) {
        this.spinnerAnexoDownload = true;
        this.consultaService.CarregaCaminhoArquivo(String(chave), nome).subscribe(
            (response) => {

                var application;

                this.download(response, nome, application, tipoArquivo);
                this.spinner.hide();
            }, () => {
                this.spinnerAnexoDownload = false;
                this.spinner.hide();
            });
    }

    download(arq:any, nome:any, contentType:any, tipoArquivo:any) {
        if (!contentType) {
            contentType = 'application/octet-stream';
        }
        this.arquivoPdfDownload = ""
        var a = document.createElement('a');
        var blob = new Blob([arq], { 'type': contentType });
        a.href = window.URL.createObjectURL(blob);
        this.src = a.href;
        a.download = nome;

        if (tipoArquivo == "pdf" || nome.includes('.PDF') || nome.includes('.pdf')) {
            this.arquivoPdfDownload = a
            this.ngxSmartModalService.getModal('PDF').open();
            this.spinnerAnexoDownload = false;
        }
        else {
            a.click();
            this.spinnerAnexoDownload = false;
        }

    }


    fecharModal() {
        this.matDialogRef.close();
    }
}
