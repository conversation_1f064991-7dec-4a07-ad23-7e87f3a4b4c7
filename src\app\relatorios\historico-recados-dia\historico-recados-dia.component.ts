import { AlertComponent } from './../../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { MedicoService } from 'src/app/service/medico.service';
import { TranslateModule } from '@ngx-translate/core';
import { ValidadoreseMascaras } from 'src/app/Util/validadores';
import { ParametrosGetRecadoDia } from 'src/app/model/medico';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';

@Component({
    selector: 'app-historico-recados-dia',
    templateUrl: './historico-recados-dia.component.html',
    styleUrls: ['./historico-recados-dia.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      MatDivider,
      MatFormFieldModule,
      TranslateModule,
      NgSelectModule,
      NgxSmartModalModule,
      TruncatePipe
    ]
})
export class HistoricoRecadosDiaComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private medicoService: MedicoService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private validador: ValidadoreseMascaras,
    public ngxSmartModalService: NgxSmartModalService,
    private usuarioLogadoService: UsuarioLogadoService

  ) {
    this.medicoService.atualizaMensagemDia$
      .subscribe((resp: any) => {
        if (resp) {
          this.CarregaRecados()
        } 
      });
  }

  DtaFimErrado: boolean = false
  DtaInicioErrado: boolean = false
  abas: any;
  DtaFim?: string;
  DtaInicio?: string;
  UsuariosDados: any = [];
  // usuario: Usuario;
  mostrarTabela?: boolean;
  // name = 'SnackBarConfirmação';

  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔'
  // actionButtonLabel: string = 'Fechar';
  // emailenviado: string = 'Email Enviado. ✔';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  tipoUsuario?: string;
  Flgusuario: boolean = false;
  User?: number;
  showMessageError = false;
  DadosRecados: any = []

  DesVisuRecadoDia: string = '';
  UsuarioRecado: string = '';
  qtdRegistros = 10;
  bOcultaCarregaMais = false;

  desativaCampoMedico: boolean = false;
  dtaLinha: string = new Date().toLocaleDateString();


  ngOnInit() {
    this.DtaInicio = new Date().toLocaleDateString();
    this.DtaFim = new Date().toLocaleDateString();

    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.medicoService.getMedicos(0, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        this.UsuariosDados = retorno.filter((c:any) => c.idUsuarioacesso == this.usuarioLogadoService.getIdUsuarioAcesso());
        ;
        this.User = this.UsuariosDados[0].idMedico;
        this.desativaCampoMedico = true
        this.mostrarTabela = false
        this.CarregaRecados()
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Erro ao Carregar médico')
        this.spinner.hide();
      })
    }
    else {
      this.CarregaMedico();
      this.CarregaRecados();
      this.spinner.hide();
    }
    
    this.spinner.hide();
  }


  CarregaMedico() {
    this.medicoService.getMedicos(0, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      
      

      if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
        this.UsuariosDados = retorno.filter((c:any) => c.idUsuarioacesso == this.usuarioLogadoService.getIdUsuarioAcesso());
        ;
        this.User = this.UsuariosDados[0].idMedico;
        this.desativaCampoMedico = true
      }
      else
        this.UsuariosDados = retorno

      this.mostrarTabela = false
      this.spinner.hide();
    }, () => {
      this.snackBarAlert.falhaSnackbar('Erro ao Carregar médico')
      this.spinner.hide();
    })
  }

  inicio?: Date;
  fim?: Date;
  CarregaRecados() {
    this.bOcultaCarregaMais = false;
    this.validarCampos();

    if (this.DtaInicioErrado || this.DtaFimErrado) {
      return;
    }
    this.validaDatas()

    if (!this.DtaFimErrado && !this.DtaInicioErrado) {
      var Dtainic = null
      var Dtafin = null
      if (this.DtaInicio) {
        this.DtaInicio = this.DtaInicio.replace('/', '').replace('/', '');
        Dtainic = this.DtaInicio.substring(0, 2) + '/' + this.DtaInicio.substring(2, 4) + '/' + this.DtaInicio.substring(4, 8)
      }
      if (this.DtaFim) {
        this.DtaFim = this.DtaFim.replace('/', '').replace('/', '')
        Dtafin = this.DtaFim.substring(0, 2) + '/' + this.DtaFim.substring(2, 4) + '/' + this.DtaFim.substring(4, 8)
      }

      this.inicio = this.validador.ConvertdataRelatorio(Dtainic)!;
      this.fim = this.validador.ConvertdataRelatorio(Dtafin)!;

      if (this.inicio > this.fim) {
        this.DtaFimErrado = true;
        this.DtaInicioErrado = true;
        this.snackBarAlert.falhaSnackbar("Data Inicio deve ser menor que Data Fim")

        return;
      }

      var parametrosRecado = new ParametrosGetRecadoDia()
      parametrosRecado.DtaInicio = this.inicio;
      parametrosRecado.DtaFim = this.fim;
      parametrosRecado.inicio = 0;
      parametrosRecado.fim = this.qtdRegistros;
      parametrosRecado.idMedico = this.User ? this.User : 0;
      parametrosRecado.idclinica = this.usuarioLogadoService.getIdUltimaClinica();
      parametrosRecado.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso();



      this.medicoService.getTodosRecadoDia(parametrosRecado).subscribe((retorno) => {
        ;
        this.DadosRecados = []
        this.DadosRecados = retorno;


        this.mostrarTabela = true
        if (retorno.length < this.qtdRegistros) {
          this.bOcultaCarregaMais = true
        }
        if (retorno.length == 0) {
          this.snackBarAlert.alertaSnackbar(this.User ? "Não há recados para esse médico" : "Não há recados nesse período!")
        }

        this.spinner.hide();
      })
    }
  }




  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }




  ValidaFimDta(dta:any) {
    this.DtaFimErrado = false;
    var min = new Date('01/01/1753 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaFimErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaFimErrado = true;
        return;
      }

    }
    else
      return;

  }

  ValidaDta(dta:any) {
    this.DtaInicioErrado = false;

    var min = new Date('01/01/1753 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaInicioErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaInicioErrado = true;
        return;
      }

    }
    else
      return;

  }

  validarCampos() {

    this.DtaFimErrado = false;
    this.DtaInicioErrado = false;

    if (!this.DtaFim)
      this.DtaFimErrado = true;

    if (!this.DtaInicio)
      this.DtaInicioErrado = true;

  }

  validaDatas() {

    var dataInicio = (document.getElementById("DataInicioConsulta") as HTMLInputElement)['value']
    var dataFim = (document.getElementById("DataFimConsulta") as HTMLInputElement)['value']
    // var valDataInicio = this.validador.ConvertdataRelatorio(dataInicio);
    // var valDataFim = this.validador.ConvertdataRelatorio(dataFim);
    this.ValidaDta(dataInicio)
    this.ValidaFimDta(dataFim)
  }

  CarregarMais() {
    this.bOcultaCarregaMais = false;

    var parametrosRecado = new ParametrosGetRecadoDia()
    parametrosRecado.DtaInicio = this.inicio;
    parametrosRecado.DtaFim = this.fim;
    parametrosRecado.inicio = this.DadosRecados.length;
    parametrosRecado.fim = this.qtdRegistros;
    parametrosRecado.idMedico = this.User ? this.User : 0;
    parametrosRecado.idclinica = this.usuarioLogadoService.getIdUltimaClinica();
    parametrosRecado.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso();

    this.medicoService.getTodosRecadoDia(parametrosRecado).subscribe((retorno) => {
      ;

      var dados = retorno;
      for (let index = 0; index < dados.length; index++) {
        this.DadosRecados.push(dados[index]);
      }

      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
      this.spinner.hide();
    })
  }

  visualizarMensagemDoDia(id:any) {

    var men = this.DadosRecados.filter((c:any) => c.idRecado == id)
    this.DesVisuRecadoDia = men[0].recado;
    this.UsuarioRecado = men[0].usuarioGerador;

    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {

      this.medicoService.VisualizacaoRecadoDia(id, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        if (retorno) {
          this.CarregaRecados()
          this.medicoService.atualizaMensagemDia$.emit(retorno)
          this.ngxSmartModalService.getModal('visualizarMensagemDia').open();
          this.spinner.hide();
        }
      })
    }
    else
      this.ngxSmartModalService.getModal('visualizarMensagemDia').open();

  }


}
