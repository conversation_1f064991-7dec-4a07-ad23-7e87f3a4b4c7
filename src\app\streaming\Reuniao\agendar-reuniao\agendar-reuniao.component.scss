@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Dosis&display=swap');


$primary: #348bc1;
.tabela {
    right: 16px;
    top: -40px;
}

.coluna {
    margin-top: 5px;
}

tr:hover {
    background: unset;
    cursor: pointer;
}

table thead {
    background: unset;
    color: unset;
}

.cor {
    background: white;
    color: #1265b9;
}

.pesquisa-mobile {
    display: none;
}

.custom-search {
    margin-left: -7%;
    margin-top: -5px;
}

.foto-bottom {
    margin-left: 2.5%;
    margin-top: 4px;
}
.medc-inativos {
    margin-right: 82%;
    margin-top: 4px; 
    float: right;
}
.nome-pessoa {
    font-size:17px
}

small {
    color: #666;
    font-size: 13px;
}

.margem-c {
    margin-left: -20px;
}

#paciente {
    width: 20%;
    margin-right: 10px;
    border-left: 1px solid #ddd;
}

.estrelas input[type="radio"] {
    display: none;
}

.estrelas label i.fa:before {
    content: "\f005";
    color: #fc0;
}

.estrelas input[type="radio"]:checked~label i.fa:before {
    color: #ccc;
}

#cpf {
    width: 10%;
    margin-top: auto;
    margin-bottom: auto;
    border-left: 1px solid #ddd;
}

#data {
    margin-bottom: auto;
    margin-top: auto;
    width: 8%;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
}

.img-circle {
    border-radius: 50%;
    width: 80px !important;
    height: 80px !important;
}

#acoes {
    width: 21%;
}

.date {
    margin-top: 10px !important;
}

.md-chip {
    margin-left: 20px;
}

.spacer-card {
    padding: 5px;
    padding-left: 20px;
    padding-top: 30px;
}

.panel_initial {
    border-bottom-left-radius: 10px !important;
    border-top-left-radius: 10px !important;
}

.finish_panel {
    border-bottom-right-radius: 10px !important;
    border-top-right-radius: 10px !important;
}

.Title-b {
    font-weight: bolder;
    color: #0983ff;
}
h4.Title-b {
    font-size: 16px
}

.panel_button {
    border: 1px solid #dcdbdb !important;
    border-radius: 0px;
}

.input-align {
    margin-left: 10px;
}

table {
    background: #fff;
}

.card_table {
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card_table:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.div_img {
    margin-left: 25px;
}

.div_paciente {
    margin-top: auto;
    margin-bottom: auto;
    margin-left: 25px;
}

.label-paciente {
    margin-top: auto;
    margin-bottom: auto;
}

.star_point {
    font-size: 20px;
    color: #ffc107;
}

.centralize-space {
    margin-right: auto;
    margin-left: auto;
    text-align: center;
    padding: 40px;
}

.mat-mdc-icon-button:hover {
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.19);
}
@media (max-width: 1800px) {
    .medc-inativos {
        margin-right: 80%;
    }
}


@media (max-width: 1380px) {
    .svg-icon {
        width: 20px;
    }
    // .mat-icon-button {
    //   width: 22px !important;
    //   height: 40px !important;
    // }
    // .material-icons {
    //   font-size: 18px !important;
    // }
}

.align-button {
    // position: absolute;
    top: 40px;
    margin-left: 5.5%;
}

.align-buttonII {
    margin-left: 38px;
    top: 40px;
}

.mini-mini {
    width: 10px;
    height: 10px;
    background: $primary;
}

.row-button {
    width: unset;
    padding-bottom: 20px;
}

.mini-miniI {
    margin-right: 109px;
}

.mini-miniII {
    margin-right: 77px;
}

.mini-miniIII {
    margin-right: 110px;
}

.fab ul li label {
    margin-top: 5px !important;
    margin-right: -10px;
    margin-left: 20px;
    border-top-right-radius: 0px;
    width: 135px;
    font-size: 12px;
}
.modal-consultas {
    background: #f5f5f5; 
    background-position: left; 
    border-radius: 10px; 
    max-width: 380px;
  }
  .modal-info {
    margin-top: -15px;  
    color: #191970;
    padding: 0 !important;
  }
  .modal-info2 {
    margin-top: -15px;
    font-size: 27px;
    color: #191970;
    
  }
  .titulo-orientacao {
    font-family: Cairo, sans-serif;
    font-weight: 400;
    color: #191970;
    margin-left: 65px;
    margin-top: -5px; 
    font-size: 28px;   
  }
  .titulo-orientacao2 {
    font-family: Cairo, sans-serif;
    font-weight: 400;
    color: #191970;
    top: 20px !important;
    font-size: 24px;
    margin-left: -70px;
  }

  .online-medic {
      font-size: 20px;
      text-align: center;
  }
  .posicao-medic {
    font-size: 16px;
    margin-top: 30px;
    margin-left: 18px;
  }
  .btn-posicao {
    margin-top: 13px;
    border-radius: none;
    box-shadow: none;
  }
  .num-posicao {
    margin-top: -19px;
    font-size: 28px;
  }
  .btn-desistir {
    margin-top: -10px !important;
    left: -20px;
    border-radius: 50px;
  }
  .logo-final-modal {
    max-width: 300px;  
    height: 35px; 
    margin-left: -1px;
    margin-bottom: 10px;
  }
  .logo-final-modal2 {
    display: none;  
  }
  hr.sep-1 {
    border: 0; 
    margin-bottom: 0 !important;
    height: 4px; 
    width: 100%;
    background-image: linear-gradient(to right, #fff, #0000CD, #0000CD, #fff);
    border-radius: 10px;
  }
  .modal-online {
      font-family: Cairo, sans-serif;
      font-size: 24px;
      margin-top: 30px;
  }
  .info-card-posicao {
      background: #fff;
      margin: 0 auto;
      margin-top: -15px;
  }
  .btn-solicitar {
      margin-top: 10px;
      margin-bottom: 20px;
  }
  .titulo-mobile-online {
      font-size: 18px;
      font-family: Cairo, sans-serif;
  }
  .btn-medicos-solic {
    background-color: #fff;
    margin: 0 auto;
    text-align: center;
  }
  .btn-solic {
      font-size: 14px;
      margin-top: -30px;
  }
  .text-online {
      font-family: Cairo, sans-serif;
  }
  .btn-danger {
    margin-top: 8%; 
    margin-bottom: 4%;
  }
  .posi-fila {
    background: transparent;
  }  
  .numeral-posi {
    margin-top: -5px; 
    font-size: 24px;
  }
  .circle-position {
    border: 4px solid darkblue;
    border-radius: 50%;
    height: 25vh;
    width: 25vw;
    margin-left: 60px;
  }
  .div-posicao {
      display: block;
      margin: 0 auto;
      text-align: center;
      height: 35vh;
  }

@media (max-width: 1585px) {
    .medc-inativos {
        margin-right: 78%;
    }    
}

@media (max-width: 1425px) {
    .medc-inativos {
        margin-right: 76%;
    }    
}

@media (max-width: 1310px) {
    .medc-inativos {
        margin-right: 74%;
    }    
}

@media (max-width: 1210px) {
    .medc-inativos {
        margin-right: 72%;
    }    
}

@media (max-width: 1115px) {
    .medc-inativos {
        margin-right: 68%;
    }    
    .foto-bottom {
        margin-left: 3.3%;
    }    
}

@media (max-width: 1015px) {
    .medc-inativos {
        margin-right: 65%;
    }    
    .foto-bottom {
        margin-left: 3.9%;
    }
}

@media (max-width: 991px) {
    .medc-inativos {
        margin-right: 69%;
    }    
}
@media (max-width: 920px) {
    .tabela-crm {
        display: block;
    }
}


@media (max-width: 915px) {
    .medc-inativos {
        margin-right: 67%;
    }    
}

@media (max-width: 880px) {
    .tabela-data {
        width: 150px;
    }
}

@media (max-width: 850px) {
    .medc-inativos {
        margin-right: 65%;
    }    
}

@media (max-width: 835px) {
    #paciente {
        min-width: 200px;
    }
}

@media (max-width: 799px) {
    .medc-inativos {
        margin-right: 65%;
    }    
}

@media(min-width: 786px) {
    #acoes {
        min-width: 217px;
    }
}

@media(max-width:885px) {
    #acoes {
        min-width: unset;
    }
}

@media(max-width:785px) {
    #paciente {
        min-width: 165px;
    }
}

@media (max-width: 780px) {
    .medic-title {
        font-size: 1.3rem;
    }
    .logo-final-modal2 {
        display: inline-block;
        max-width: 300px;  
        height: 35px; 
        margin-left: 40px;
        margin-bottom: 10px;
        margin-top: 10px;
    }
    .logo-final-modal {
        display: none;
    }
}

@media (max-width: 779px) {
    .input-align {
        margin-left: 71%;
        bottom: 95px;
    }
    .tabela {
        top: -95px;
    }
    .foto-resp {
        margin-left: 3%;
        margin-right: 3%;
    }
    .no-mobile-card {
        top: -50px;
    }
}

@media (max-width: 777px) {
    .desktop-none {
        margin-top: 35px;
    }
    .medic-title {
        margin-left: -3px;
    }
    .input-align {
        top: -120px;
    }
    .tabela {
        top: -125px;
    }
    .no-mobile-card {
        top: -90px;
    }
    .custom-search {
        margin-left: -17%;
    }
 
}

@media (max-width: 767px) {
    .desktop-none {
        margin-top: 15px;
    }
    .pesquisar-bottom {
        margin-left: 190px;
        top: -40px;
        width: 260px;
    }
    .custom-search {
        margin-left: -15%;
    }
    .input-align {
        margin-bottom: -10x;
    }
    .no-mobile-card {
        top: -80px;
    }
}

@media (max-width: 730px) {
    .tabela {
        left: 15px;
    }
    .desktop-none {
        right: 1px;
    }
    .botoes-acao {
        max-width: 290px;
    }
}

@media (max-width: 710px) {
    .input-align {
        right: 5px;
    }
    .botoes-acao {
        max-width: 280px;
    }
}

@media (max-width: 700px) {
    .input-align {
        right: 8px;
    }
    .botoes-acao {
        max-width: 270px;
    }
}

@media (max-width: 690px) {
    .input-align {
        right: 15px;
    }
    .botoes-acao {
        max-width: 180px;
    }
    .tabela {
        left: 10px;
    }
}

@media (max-width: 665px) {
    .input-align {
        right: 20px;
    }
}

@media (max-width: 645px) {
    .tabela {
        left: 10px;
    }
}

@media (max-width: 635px) {
    .input-align {
        right: 27px;
    }
}


@media (max-width: 610px) {
    .input-align {
        right: 35px;
    }
    .foto-resp {
        margin-left: 4%;
    }    
}


@media (max-width: 602px) {
    .no-mobile-card {
        left: -6px;
    } 
}

@media (max-width:600px) {
    .pesquisa-mobile {
        display: block;
    }
    .pesquisa-form {
        display: none;
    }
    .busca-mobile {
        width: 464px;
        height: 35px;
        font-size: 14px;
        border-radius: 5px;
        border: 1px solid #999;
        padding-left: 20px;
        margin-top: 10px;
    }
    .custom-search {
        margin-left: -9%;
        color: #444;
        margin-top: 15px;
    }
    .input-align {
        margin-top: 80px;
    }
    .mother-div {
        background: #f7f5e6;
        border-radius: 10px;
    }
    .medic-title {
        color: #555;
        font-family: Cairo,sans-serif;
    }
    .input-align{
        right: 45px;
    }
    .tabela {
        margin-left: -335px;
        border-collapse: unset;
        border-radius: 8px;
        box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12);
        margin-top: 80px;
        left: 9%;
    }
    .legenda-bottom {
        border-radius: 5px;
        color: #1265b9;
        font-weight: 600;
    }
    .ad-pp{
        font-weight: 100;
    }

    .header-card{
        margin: 10px 20px;
        border-radius: 10px;
        margin-bottom: 30px;
    }
    .desktop-none {
        margin-bottom: -70px;
    }
    .icon-title {
        margin-left: 0px;
    }
    .titulo-medicos {
        font-family: Cairo,sans-serif;
        font-weight: 600;
        text-transform: uppercase;
        border: none;
        color: #1265b9;
        font-size: 20px;
    }
    .titulo-card {
        margin-top: 10px;
    }
    h4.Title-b {
        color: #1265b9;
        text-align: left;
        padding-left: 20px;
        font-weight: 500;
    }
    .nome-pessoa {
        font-size: 17px;
        color: #444;
        font-family: Cairo,sans-serif;
        font-weight: 200;
    }
    .circle-subir {
        margin-left: 20px;
    }
    .div_paciente {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
    .div_paciente br {
        display: none;
    }
    .logo-final-modal2 {
        margin-left: 50px;
    }
    .titulo-orientacao2 {
        font-size: 24px;
        margin-left: -5px;
    }
    .suaposition {
        font-size: 12px;
        margin-top: 20px;
        font-family: Cairo, sans-serif;
    }
    .modal-consultas {
        background: #f5f5f5;
        border-radius: 10px;
        padding: 0 !important;
        margin: 0px !important;
        width: 305px !important;

    }
    .posi-fila {
        box-shadow: none;
    }
    .desistir-botao {
        height: 35px;
        width: 80px;
        border-radius: 50px;
        top: -90px;
        margin: 0 auto;
    }
    .desistir-titulo {
        margin-left: -7px;
    }
    .numeral-posi {
        margin-top: -5px;
        color: darkblue;
        font-size: 30px;
      }
    .titulo-mobile-online {
        font-family: Cairo, sans-serif;
    }
    .circle-position {
        text-align: center;
        margin: 10px auto;
        border: 4px solid #ddd;
        border-radius: 50%;
        height: 20vh;
        width: 20vh;
    }   
    .btn-solic {
        margin-top: -10px;
    }
    .div-mobile-posicao {
        margin-top: auto !important; 
        margin-bottom: auto !important; 
        height: 35vh;
    }
}


@media (max-width: 586px) {
    .busca-mobile {
        max-width: 100%;
    }
    .input-align {
        right: 50px;
    }
    .tabela {
        margin-left: -325px;
    }    
}

@media (max-width: 575px) {
    .tabela {
        max-width: 220px;
        margin-left: -10px;
    }
    .circle-subir {
        top: 10px;
    }
    
}

@media (max-width: 535px) {
    .ad-pp {
        display: none;
    }
    .input-align {
        right: -15px;
    }
    .tabela {
        margin-left: -5px;
    }
    .custom-search {
        margin-left: -11%;
    }    
}


@media (max-width: 475px) {
    .foto-resp {
        margin-left: 5%;
    }
}

@media (max-width: 445px) {
    .tabela {
        margin-left: -2px;
    }
    .circle-subir {
        left: 5px;
    }
    .input-align {
        right: -10px;
    }
}

@media (max-width: 435px) {
    .tabela {
        margin-left: -2px;
    }
    .input-align {
        right: -8px;
    }
}

@media (max-width: 425px) {
    .md-chip {
        margin-left: 0;
    }
    .custom-search {
        margin-left: -13%;
    }
    .tabela {
        margin-left: -10px;
        max-width: 100%;
    }
    .ad-pp {
        display: inline-block;
    }
    .desktop-none {
        margin-bottom: -50px;
        top: -20px;
    }
    .input-align {
        top: -70px;
        left: -225px;
        width: 95.2%;
    }
    .top-title {
        margin-top: -15px;
    }
    .foto-resp {
        margin-left: 15%;
    }
    .header-card {
        margin: 10px 5px;
        margin-bottom: 20px;
    }    
    .medic-title {
        margin-left: 2px;
    }
    .botoes-subir {
        position: static;
    }
    h4.Title-b {
        font-size: 14px
    }
    .nome-pessoa {
        font-size:14px
    }
    .titulo-medicos {
        font-size: 17px;
    }
    .bottom-sobre {
        z-index: 1667 !important;
    }
}

@media (max-width: 415px) {
    .input-align {
        left: -68%;
    }
    .foto-resp {
        margin-left: 14%;
    }
}

@media (max-width: 410px) {
    .busca-mobile {
        max-width: 100%;
        margin-left: -10px;
    }
    .foto-resp {
        margin-left: 13%;
    }
    .icon-title {
        margin-left: -10px;
    }
}

@media (max-width: 405px) {
    .input-align {
        left: -210px;
    }
}

@media (min-width: 601px) {
    .no-desktop {
        display: none !important;
    }
}
@media (max-width: 600px) {
    .no-mobile {
        display: none !important;
    }
}

.date_I {
    margin-top: 10px !important;
    text-align: left !important;
}

@media (max-width: 320px) {
    .margem-c {
        margin-left: -10px;
    }
    .col-table table {
        border: none !important;
    }
    tr:hover {
        background: unset;
    }
    .col-table thead {
        font-size: 11px;
        background: #fff;
        color: #666;
    }
    .col-table i {
        font-size: 18px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .col-table mat-icon {
        font-size: 20px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .value-color {
        // color: #1265b9 !important;
        font-size: 13px;
        padding: 0 !important;
        padding-left: 5px !important;
        padding-right: 5px !important;
        text-align: center !important;
    }
}

@media (min-width: 601px) {
    .coluna {
        margin-left: -14px;
    }
}

@media (max-width: 1210px) and (min-width: 1169px) {
    .custom-font {
        font-size: 11px;
    }
}

@media (max-width: 1090px) and (min-width: 1052px) {
    .custom-font {
        font-size: 11px;
    }
}

@media (max-width: 780px) and (min-width: 731px) {
    .custom-font {
        font-size: 11px;
    }
    .custom-font2 {
        font-size: 10px;
    }
}

@media(max-width:480px) {
    .text-align {
        text-align: center;
    }
}

@media (max-width: 395px) {
    .input-align {
        left: -203px;
    }
}
@media (max-width: 392px){
    h4.Title-b{
        font-size: 12px;
    }
    .label_paciente {
        font-size: 12px;
    }
    .titulo-medicos {
        font-size: 16px;
    }
}
@media (max-width: 385px) {
    .input-align {
        left: -68.2%;
    }
}

@media (max-width:375px) {
    .foto-resp {
        margin-left: 8%;
    }
    .custom-search {
        margin-left: -16%;
    }

}

@media (max-width: 373px) {
    .custom-search {
        margin-left: -15%;
    }
    .foto-resp {
        margin-left: 8%;
    }
   
}

@media (max-width: 359px){
    h4.Title-b {
        font-size: 10px;
    }
    .label_paciente {
        font-size: 10px;
    }
    .foto-resp {
        margin-left: 7%;
    }
}

@media (max-width: 355px){
    .foto-resp {
        margin-left: 6%;
    }
    .custom-search {
        margin-left: -17%;
    }
}

@media (max-width:345px){
    .input-align {
        max-width: 97%;
    }
    .foto-resp {
        margin-left: 4%;
    }
    .busca-mobile {
        max-width: 100%;
        margin-left: -12px;
    }
}

@media (max-width: 340px) {
    .header-card {
        margin: 10px 0px;
        margin-bottom: 20px;
    }    
    .foto-resp {
        margin-left: 4%;
    }
    .logo-final-modal2 {
        height: 25px;
        margin-left: 70px !important;
        margin-bottom: 10px;
        margin-top: 10px;
    }
}
@media (max-width: 330px) {
    .input-align {
        max-width: 97%;
    }
    .busca-mobile {
        max-width: 100%;
        margin-left: -17px;
    }
    .foto-resp {
        margin-left: 1%;
    }
    .mother-div {
        padding-left: 5px;
        padding-right: 5px;
    }
}
@media (max-width: 323px) {
    .foto-resp {
        margin-left: -1%;
    }
    .icon-title {
        margin-left: -15px;
    }
    .custom-search {
        margin-left: -20%;
    }
}

@media (max-width: 320px) {
    .busca-mobile {
        max-width: 100%;
    }
    .logo-final-modal2
     {
        margin-left: 80px !important;
    }
}
hr.sep-1 {
    border: 0; 
    margin-bottom: 0 !important;
    height: 4px; 
    width: 100%;
    background-image: linear-gradient(to right, #fff, #0000CD, #0000CD, #fff);
    border-radius: 10px;
  }

hr.sep-2 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    border: 0; 
    height: 2px; 
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    background-image: linear-gradient(to right, #ffffff, #178aff, #178aff, #ffffff);
  }

.modal-consultas {
    background: #f5f5f5; 
    background-position: left; 
    border-radius: 10px; 
    max-width: 450px;
  }
  .modal-titulo-consulta {
    margin-top: 10px; 
    font-size: 27px; 
    color: #191970;
  }
  .titulo-consulta {
      font-family: Dosis, sans-serif;
      font-weight: 400;
      color: #191970;
      margin-left: 10px;
      margin-top: 5px;      
  }
  .info-consulta {
    font-family: Cairo, sans-serif;
  }
  .card-consulta {
    margin-bottom: 1rem !important;
    border: 1px solid #0000CD; 
    border-radius: 10px;
    max-width: 450px;
    margin: 5px; 
    background-color: #fff;
  }