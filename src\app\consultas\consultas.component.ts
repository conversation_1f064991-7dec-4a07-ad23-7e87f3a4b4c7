
import { ObjetoPadrao } from 'src/app/model/RetornoPadraoApi'; import { WhatsService } from './../service/whats.service';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ConsultaService } from '../service/consulta.service';
import { Router } from '@angular/router';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { AgendaService } from '../service/agenda.service';
import { MedicoService } from '../service/medico.service';
import { PacienteService } from '../service/pacientes.service';
import {
  FormControl, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { Avaliacao } from '../model/avaliacao';
import { QuesitoAvaliacao, irParaConsulta, obj<PERSON>esquisaconsulta, Consulta, ConsultaModelView } from '../model/consulta';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { ValidadoreseMascaras } from '../Util/validadores';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { ConvenioService } from '../service/convenio.service';
import { ClinicaService } from '../service/clinica.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { PagamentoService } from '../service/pagamento.service';
import { AcessoRapidoEmail } from '../model/acesso-rapido-consulta';
import { EnvioEmailService } from '../service/envioEmail.service';
import { SignalHubService } from '../service/signalHub.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { MatDialog as MatDialog } from '@angular/material/dialog';
import { MatCalendar, MatDatepickerModule } from '@angular/material/datepicker';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { ModalChamarPacientePainelComponent } from '../Modais/modal-chamar-paciente-painel/modal-chamar-paciente-painel.component';
import { ModalEnvioMensagensWhatsappComponent } from '../Modais/modal-envio-mensagens-whatsapp/modal-envio-mensagens-whatsapp.component';
import { ListaProcedimentoComponent } from '../lista-procedimento/lista-procedimento.component';
import { ControleModaisService } from '../service/controle-modais.service';
import { ModalConfirmaChegadaPacienteComponent } from '../Modais/modal-confirma-chegada-paciente/modal-confirma-chegada-paciente.component';
import { ModalAdicionaObservacaoConsultaComponent } from '../Modais/modal-adiciona-observacao-consulta/modal-adiciona-observacao-consulta.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatDividerModule } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatExpansionModule } from '@angular/material/expansion';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatNativeDateModule } from '@angular/material/core';
import { MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatSlideToggle } from '@angular/material/slide-toggle';


@Component({
  selector: 'app-consultas',
  templateUrl: './consultas.component.html',
  styleUrls: ['./consultas.component.scss'],
  animations: [
    trigger('openClose', [
      state('open', style({
        opacity: '1',
        display: 'block'
      })),
      state('closed', style({
        opacity: '0',
        display: 'none'
      })),
      transition('open => closed', [
        animate('0.2s')
      ]),
      transition('closed => open', [
        animate('0.2s')
      ]),
    ])
  ],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxSmartModalModule,
    MatFormFieldModule,
    NgSelectModule,
    MatDividerModule,
    TranslateModule,
    MatIcon,
    MatCardModule,
    MatTooltipModule,
    MatExpansionModule,
    TruncatePipe,
    MatSelectModule,
    MatDatepickerModule,
    MatRadioModule,
    MatNativeDateModule,
    MatExpansionPanelHeader,
    MatSlideToggle
  ]
})

export class ConsultasComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private router: Router,
    private consultaService: ConsultaService,
    public ngxSmartModalService: NgxSmartModalService,
    private agendaService: AgendaService,
    private medicoService: MedicoService,
    private pacientesService: PacienteService,
    private tradutor: TranslateService,
    private validador: ValidadoreseMascaras,
    private convenioService: ConvenioService,
    private clinicaService: ClinicaService,
    public usuarioLogadoService: UsuarioLogadoService,
    private pagamentoService: PagamentoService,
    private emailService: EnvioEmailService,
    public signalHubService: SignalHubService,
    private localStorageService: LocalStorageService,
    private matDialog: MatDialog,
    private whatsService: WhatsService,
    private snackbarAlert: AlertComponent,
    private controleModaisService: ControleModaisService,
  ) {
    this.signalHubService.OnDisparaAlertConsulta
      .subscribe(() => {
        this.CarregaAtualizacao();
      });
  }

  setStep(index: number) {
    this.step = index;
  }

  step = 0;
  FlgRetorno: boolean = false;
  consulta = ['Telemedicina', 'Presencial'];
  actionButtonLabel: string = 'Fechar';
  salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  ErroSalvar: string = 'Erro ao salvar!';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  concordo?: boolean;
  concordomsg?: boolean;
  Hoje: boolean = true;
  DadosModalCheckin: any;
  AlgumaObservacao = '';
  FormularioVotacao: any = [];
  idConsulta = 0;
  dta: string = ''
  DtaErrado = false;
  DadosInformUsuario: any = [];
  idCliente?: number;
  legenda = false;
  tipoUsuario?: string;
  consultas: ConsultaModelView[] = [];
  pesquisaPaciente: string = '';
  pesquisaMedico: string = '';
  qtdRegistros = 10;
  segundos: number = 0;
  minutos: number = 0;
  hora: number = 0;
  Atendente = false;
  dadosLifeLine: any
  Motivocancelameto = ''
  cancelamento = false;
  idCancela: any;
  nomePaciente = ""
  dtahora?: string;
  dtanova?: string | null;
  public selectedTime: any;
  IdMedico?: number;
  Especialidade?: number;
  DadosEspecialidade: any;
  ListaMedicos: any;
  DesAgendamento = ""
  tempo: any;
  idAgenda?: number | null;
  DataConsulta?: Date;
  HoraConsultas: any;
  status: any;
  statusDaConsulta: any;
  DadosConsultaLifeLine: any
  dados = false;
  dadosAnonimo?: string;
  MedicoAnonimo?: string;
  Dataanonimo?: string;
  DadosUsuario?: []
  nomecliente?: string;
  idEspecialidade?: number;
  idMedico?: number;
  DadosInformCancelament: any = [];
  DtaConsulta: string = new Date().toLocaleDateString();
  DtaConsultaFim: string = new Date().toLocaleDateString();
  Foto = false;
  quesitosAvaliacao: any;
  ObsAvaliacao?: string;
  idtipoagendamento?: number;
  dadosTipoAgendamento: any = []
  DadosPacientes: any = []
  CPF = ''
  IdPaciente?: number | null;
  DadosEspecialidadeMedico: any;
  quesitoText?: string;
  Dtanasc?: boolean;
  DtanascLimpa = false;
  DtanascVasil = false;
  hraEdit?: boolean;
  hraEditVasil = true;
  showMessageError = false;
  FlgSomenteProntuario: boolean = false;
  FlgProntuario: boolean = false;
  tipoConsulta?: string;
  valorConsulta?: string | null;
  DadosPagamento = [];
  idConvenio?: number | null;
  codConvenio?: string | null;
  dadosConvenio: any = [];
  valorConsultaParticular?: string;
  convenioValido: boolean = true;
  loginComMedico: boolean = false
  conclusao = ['Agendada', 'Finalizada', 'Cancelada'];
  DadosStatus: any = [];
  StatusAgendada: any;
  StatusFinalizada: any;
  StatusCancelada: any;
  bOcultaCarregaMais = false;
  url_atual?: string;
  flgExigePagamento = false;
  flgHabilitaPagamento: boolean = false;
  flgSomenteProntuario: boolean = false;
  naotemanalises: boolean = false;
  flgModalTemplate = false;
  conteudoTemplate: any;
  cabecalhoModal? = "";
  toggle: any = {}
  idTipoUsuario: number = 0;
  paci = new FormControl('', [Validators.required, Validators.maxLength(11)]);
  medi = new FormControl('', [Validators.required, Validators.maxLength(11)]);
  horario: boolean = false;
  conv = new FormControl('', [Validators.required, Validators.maxLength(11)]);
  valor = new FormControl('', [Validators.required, Validators.maxLength(11)]);

  @ViewChild('TemplateListaProcedimento', { static: false }) templateListaProcedimento: TemplateRef<any> | undefined;

  ngOnInit() {
    this.url_atual = window.location.origin;

    this.idTipoUsuario = this.usuarioLogadoService.getIdTipoUsuario()!;

    this.flgSomenteProntuario = this.usuarioLogadoService.getFlgProntuario()!;
    this.flgHabilitaPagamento = this.usuarioLogadoService.getFlgHabilitaPagamento()!;

    if (localStorage.getItem("dash")) {
      this.status = localStorage.getItem("dash")
      localStorage.removeItem("dash")
    }

    this.CarregaConsultas();

    if (this.idTipoUsuario != EnumTipoUsuario.Paciente) {

      this.CarregaMedicos();
      this.tipoAgendamento();
      this.CarregaPacientes();
      this.CarregaConvenio();
      this.getPagamentoClinica()
    }

    this.varificaTipoUsuario()

    if (this.idTipoUsuario == EnumTipoUsuario.ADM) {
      this.tipoUsuario = 'ADM Sistema';
    }
    else if (this.idTipoUsuario == EnumTipoUsuario.Atendente) {
      this.tipoUsuario = 'Atendente';
    }
    else if (this.idTipoUsuario == EnumTipoUsuario.Médico) {
      this.tipoUsuario = 'Médico';
    }
    else if (this.idTipoUsuario == EnumTipoUsuario.Paciente) {
      this.tipoUsuario = 'Paciente';
    }

  }
  copyMessage(val: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.snackbarAlert.sucessoSnackbar('Link copiado!');
  }

  carregaDadosStatus() {
    if (this.DadosStatus.length == 0) {
      this.tradutor.get('TELACONSULTAS.AGENDADA').subscribe((res: string) => {
        this.StatusAgendada = res;
      });

      this.tradutor.get('TELACONSULTAS.Finalizada').subscribe((res: string) => {
        this.StatusFinalizada = res
      });

      this.tradutor.get('TELACONSULTAS.CANCELADA').subscribe((res: string) => {
        this.StatusCancelada = res
      });

      this.DadosStatus = [this.StatusAgendada, this.StatusFinalizada, this.StatusCancelada]
    }
  }


  CarregaPacientes() {
    this.pacientesService.GetPacienteAgenda(this.CPF, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      try {
        this.DadosPacientes = []

        var users: any = [];
        retorno.forEach((element: any) => {
          element.cpf = element.cpf.replace(/(\d{3})(\d)/, "$1.$2");
          element.cpf = element.cpf.replace(/(\d{3})(\d)/, "$1.$2");
          element.cpf = element.cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
          users.push(element)
        });

        this.DadosPacientes = users.filter((c: any) => c.flgInativo != true);

        if (this.CPF != '' && this.CPF != undefined && this.CPF != null) {
          if (this.DadosPacientes.length == 1)
            this.IdPaciente = this.DadosPacientes[0].idCliente
          else
            this.IdPaciente = null
        }
        this.spinner.hide();
      }

      catch {
        this.spinner.hide();
      }
    }, () => {
      this.spinner.hide();
    })
  }

  getPagamentoClinica() {
    this.clinicaService.getPagamentoClinica(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      try {
        if (retorno) {
          retorno = this.verificaCasaDecimal(retorno)
          retorno = this.aplicarMascaraValor(retorno)
          this.valorConsultaParticular = retorno
        }

        this.spinner.hide();
      }
      catch {
        this.spinner.hide();
      }
    }, () => { this.spinner.hide() });
  }


  lifeline(id: any) {
    this.consultaService.GetLifeLine('Paciente', id).subscribe((retorno) => {
      try {
        this.ngxSmartModalService.getModal('LifeLine').open()
        this.dadosLifeLine = retorno;
        this.nomePaciente = this.dadosLifeLine[0].nomePaciente;
        this.spinner.hide();
      }
      catch {
        this.spinner.hide();
      }
    }, () => { this.spinner.hide() })
  }

  irPAgenda() {
    this.router.navigate(['/calendario']);
  }

  DadosPaciente(id: any) {
    this.dados = false;
    this.DadosConsultaLifeLine = []
    this.consultaService.GetDadosLifeLine(id).subscribe((retorno) => {
      try {
        this.DadosConsultaLifeLine = retorno;
        if (this.DadosConsultaLifeLine.anonimo != null) {
          this.dadosAnonimo = this.DadosConsultaLifeLine.anonimo;
          this.MedicoAnonimo = this.DadosConsultaLifeLine.medico;
          this.Dataanonimo = this.DadosConsultaLifeLine.dtaConsulta;
        }
        this.dados = true;
        this.spinner.hide();
      }
      catch {
        this.spinner.hide();
      }
    })
  }

  MotivoCampo() {
    if (this.cancelamento == true)
      this.cancelamento = false;
  }

  CancelaValue(id: any) {
    this.Motivocancelameto = '';
    this.idCancela = id;
    this.ngxSmartModalService.getModal('cancelarHorario').open();
  }

  CancelarConsulta() {
    if (this.Motivocancelameto == '' || this.Motivocancelameto == undefined) {
      this.cancelamento = true;
      return;
    }

    this.agendaService.InativarAgendamento(this.idCancela, this.usuarioLogadoService.getIdUsuarioAcesso(), this.Motivocancelameto).subscribe(() => {
      try {
        this.CarregaConsultas();
        this.consultaService.atualizaLegenda$.emit();
        this.ngxSmartModalService.getModal('cancelarHorario').close();
        this.spinner.hide();
      }

      catch {
        this.spinner.hide();
      }
    }, () => {
      this.spinner.hide();
    })
  }

  CarregaEspecialidade() {

    this.medicoService.getEspecialidade().then((retornaEspecialidade) => {
      try {
        this.DadosEspecialidade = retornaEspecialidade;
        this.spinner.hide();
      }
      catch {
        this.spinner.hide();
      }
    }, () => {
      this.spinner.hide();
    })
  }

  async CarregaMedicos() {
    await this.medicoService.getMedicos(this.Especialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      try {
        this.ListaMedicos = retorno
        this.spinner.hide();
      } catch {
        this.spinner.hide();
      }

    }, () => {
      this.spinner.hide();
    })
  }

  public Consulta(id: any, flg: any = false) {
    if (id != '' && id != 0) {
      const Objconsulta = this.consultas.filter(c => c.idConsulta == id);

      if (Objconsulta[0].flgExigePagamento == true && Objconsulta[0].idPagamento == null && this.idTipoUsuario == EnumTipoUsuario.Paciente) {
        this.pagamentoService.setIdPagador(Objconsulta[0].paciente!.idCliente!);
        this.pagamentoService.setIdRecebedora(Objconsulta[0].medico!.idMedico!);
        this.pagamentoService.setValor(parseInt(Objconsulta[0].valorConsulta!));
        this.pagamentoService.setIdconsulta(id);
        this.pagamentoService.setAbrirModal(true);
        this.ngxSmartModalService.getModal('ModalPagamento').open();
      } else {
        var consulta = new irParaConsulta();
        consulta.idConsulta = id;
        consulta.flgSomenteProntuario = flg
        this.localStorageService.Consulta = consulta;
        if (flg)
          this.atualizarPainel(id);
        else {
          this.router.navigate(['/streaming']);
        }
      }
    }
  }

  atualizarPainel(id: any) {
    this.matDialog.open(ModalChamarPacientePainelComponent, {
      data: id,
      disableClose: false
    });
  }

  getErrorMessageconv() {
    return this.conv.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.conv.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessagevalorConsulta() {
    return this.valor.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.valor.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessagepaci() {
    return this.paci.hasError('required') ? 'TELACONSULTAS.ERROCAMPO' :
      this.paci.hasError('Procedência') ? 'TELACONSULTAS.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessagemedi() {
    return this.medi.hasError('required') ? 'TELACONSULTAS.ERROCAMPO' :
      this.medi.hasError('Procedência') ? 'TELACONSULTAS.ERRONAOEVALIDA' :
        '';
  }


  ValidaDta(dta: any) {
    this.DtaErrado = false;
    var min = new Date('01/01/1753 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaErrado = true;
        return;
      }
      else if (this.DtaErrado == false)
        this.CarregaConsultas()

    }
    else if (dta == '' || this.dta != '' || this.dta != null)
      this.CarregaConsultas()
    else
      return;
  }

  formatarData(data: any) {
    var dia = data.split("/")[0];
    var mes = data.split("/")[1];
    var ano = data.split("/")[2];

    return ("0" + mes).slice(-2) + '/' + ("0" + dia).slice(-2) + '/' + ano;
  }

  CarregaConsultas() {
    this.consultas = []

    this.dta = ''
    this.bOcultaCarregaMais = false;
    if (this.status != undefined) {
      if (this.status == this.StatusAgendada) {
        this.statusDaConsulta = "Agendada"
      } else if (this.status == this.StatusFinalizada) {
        this.statusDaConsulta = "Finalizada"
      } else if (this.status == this.StatusCancelada) {
        this.statusDaConsulta = "Cancelada"
      }
      else
        this.statusDaConsulta = null
    }

    if (!this.DtaConsulta || this.DtaConsulta != new Date().toLocaleDateString())
      this.Hoje = false;
    else
      this.Hoje = true;

    if (this.DtaConsulta != "")
      var Dtainic = this.validador.Convertdata(this.DtaConsulta);

    var objConsulta = new objPesquisaconsulta();
    objConsulta.inicio = 0;
    objConsulta.fim = this.qtdRegistros;
    objConsulta.status = this.statusDaConsulta;
    objConsulta.pesquisaMedico = this.pesquisaMedico;
    objConsulta.pesquisaPaciente = this.pesquisaPaciente;
    objConsulta.DtaConsulta = this.DtaConsulta != "" ? Dtainic! : null;

    this.spinner.show();

    this.consultaService.GetListaConsulta(objConsulta).subscribe((retorno) => {
      try {
        this.consultas = retorno

        if (this.consultas.length == 0)
          this.naotemanalises = true;
        else
          this.naotemanalises = false;

        var minutos = 20;
        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;
        this.consultas.forEach(element => {
          if (element.paciente!.pessoa!.contato!.telefoneMovel) {
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/\D/g, "");
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
          }

          var tempHora = parseInt(element.tempoConsulta!.substring(1, 3))
          var tempMin = parseInt(element.tempoConsulta!.substring(3, 5))
          var teste = new Date(element.dtaConsulta!)
          var teste2 = new Date(element.dtaConsulta!)
          teste.setMinutes(teste.getMinutes() - minutos);
          var data = new Date()
          data.setMinutes(data.getMinutes() - minutos);
          var hconsulta = new Date(element.dtaConsulta!)

          if (teste <= new Date() && teste >= data) {
            element.flgConsulta = true
            element.flgAndamento = false;
          }
          else if (tempHora > 0) {
            element.flgAndamento = false;
            element.flgConsulta = false
            teste2.setHours(teste2.getHours() + tempHora, teste2.getMinutes() + tempMin);

            if (hconsulta <= new Date() && teste2 >= new Date()) {
              element.flgAndamento = true
              element.flgConsulta = false
            }

          }
          else if (tempHora == 0) {
            element.flgAndamento = false;
            element.flgConsulta = false
            teste2.setMinutes(teste2.getMinutes() + tempMin);
            if (hconsulta <= new Date() && teste2 >= new Date()) {
              element.flgAndamento = true
              element.flgConsulta = false
            }

          }
          else {
            element.flgAndamento = false;
            element.flgConsulta = false

          }

          element.valorAvaliacao = 0
          var ava = 0
          if (element.listaAvaliacao.length > 0) {
            element.listaAvaliacao.forEach(elementoAv => {
              if (ava == 0)
                ava = elementoAv.valor!
              else
                ava = elementoAv.valor! + ava
            });
            if (ava > 0)
              element.valorAvaliacao = ava / element.listaAvaliacao.length
          }

          if (element.flgCheckInPaciente && !element.flgRealizada && !element.flgInativo && !element.flgAndamento) {
            element.flgConsulta = true;
          }
        });
        this.spinner.hide();
      } catch (error) {
        this.spinner.hide();
      }
    }, () => {
      this.spinner.hide();
    })
  }

  CarregaAtualizacao() {
    if (this.status != undefined) {
      if (this.status == this.StatusAgendada) {
        this.statusDaConsulta = "Agendada"
      } else if (this.status == this.StatusFinalizada) {
        this.statusDaConsulta = "Finalizada"
      } else if (this.status == this.StatusCancelada) {
        this.statusDaConsulta = "Cancelada"
      }
      else
        this.statusDaConsulta = null
    }

    if (!this.DtaConsulta)
      this.Hoje = false;



    if (this.DtaConsulta != "")
      var Dtainic = this.validador.Convertdata(this.DtaConsulta);

    var objConsulta = new objPesquisaconsulta();
    objConsulta.inicio = 0;
    objConsulta.fim = this.qtdRegistros;
    objConsulta.status = this.statusDaConsulta;
    objConsulta.pesquisaMedico = this.pesquisaMedico;
    objConsulta.pesquisaPaciente = this.pesquisaPaciente;
    objConsulta.DtaConsulta = this.DtaConsulta != "" ? Dtainic! : null;


    this.consultaService.GetAtualizacao(objConsulta).subscribe((retorno) => {
      try {
        this.consultas = []

        this.consultas = retorno;

        if (this.consultas.length == 0)
          this.naotemanalises = true;
        else
          this.naotemanalises = false;

        var minutos = 20;
        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;

        this.consultas.forEach(element => {

          if (element.paciente!.pessoa!.contato!.telefoneMovel) {
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/\D/g, "");
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
          }

          var tempHora = parseInt(element.tempoConsulta!.substring(1, 3))
          var tempMin = parseInt(element.tempoConsulta!.substring(3, 5))
          var teste = new Date(element.dtaConsulta!)
          var teste2 = new Date(element.dtaConsulta!)
          teste.setMinutes(teste.getMinutes() - minutos);
          var data = new Date()
          data.setMinutes(data.getMinutes() - minutos);
          var hconsulta = new Date(element.dtaConsulta!)

          if (teste <= new Date() && teste >= data) {
            element.flgConsulta = true
            element.flgAndamento = false;
          }
          else if (tempHora > 0) {
            element.flgAndamento = false;
            element.flgConsulta = false
            teste2.setHours(teste2.getHours() + tempHora, teste2.getMinutes() + tempMin);

            if (hconsulta <= new Date() && teste2 >= new Date()) {
              element.flgAndamento = true
              element.flgConsulta = false
            }

          }
          else if (tempHora == 0) {
            element.flgAndamento = false;
            element.flgConsulta = false
            teste2.setMinutes(teste2.getMinutes() + tempMin);
            if (hconsulta <= new Date() && teste2 >= new Date()) {
              element.flgAndamento = true
              element.flgConsulta = false
            }

          }
          else {
            element.flgAndamento = false;
            element.flgConsulta = false

          }

          element.valorAvaliacao = 0
          var ava = 0
          if (element.listaAvaliacao.length > 0) {
            element.listaAvaliacao.forEach(elementoAv => {
              if (ava == 0)
                ava = elementoAv.valor!;
              else
                ava = elementoAv.valor! + ava
            });
            if (ava > 0)
              element.valorAvaliacao = ava / element.listaAvaliacao.length
          }

          if (element.flgCheckInPaciente && !element.flgRealizada && !element.flgInativo && !element.flgAndamento) {
            element.flgConsulta = true;
          }
        });
        this.spinner.hide();
      } catch (error) {
        this.spinner.hide();
      }
    }, () => { this.spinner.hide() })
  }

  CarregaMaisConsultas() {
    this.bOcultaCarregaMais = false;
    if (this.DtaConsulta != "")
      var Dtainic = this.validador.Convertdata(this.DtaConsulta);

    var objConsulta = new objPesquisaconsulta();
    objConsulta.inicio = this.consultas.length;
    objConsulta.fim = this.qtdRegistros;
    objConsulta.status = this.statusDaConsulta;
    objConsulta.pesquisaMedico = this.pesquisaMedico;
    objConsulta.pesquisaPaciente = this.pesquisaPaciente;
    objConsulta.DtaConsulta = this.DtaConsulta != "" ? Dtainic! : null;

    this.spinner.show();
    this.consultaService.GetListaConsulta(objConsulta).subscribe((retorno) => {
      try {
        var minutos = 20;

        for (let index = 0; index < retorno.length; index++) {
          this.consultas.push(retorno[index]);
        }
        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;

        this.consultas.forEach(element => {

          if (element.paciente!.pessoa!.contato!.telefoneMovel) {
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/\D/g, "");
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
            element.paciente!.pessoa!.contato!.telefoneMovel = element.paciente!.pessoa!.contato!.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
          }

          var tempHora = parseInt(element.tempoConsulta!.substring(1, 3))
          var tempMin = parseInt(element.tempoConsulta!.substring(3, 5))
          var teste = new Date(element.dtaConsulta!)
          var teste2 = new Date(element.dtaConsulta!)
          teste.setMinutes(teste.getMinutes() - minutos);
          var data = new Date()
          data.setMinutes(data.getMinutes() - minutos);
          var hconsulta = new Date(element.dtaConsulta!)

          if (teste <= new Date() && teste >= data) {
            element.flgConsulta = true
            element.flgAndamento = false;
          }
          else if (tempHora > 0) {
            element.flgAndamento = false;
            element.flgConsulta = false
            teste2.setHours(teste2.getHours() + tempHora, teste2.getMinutes() + tempMin);

            if (hconsulta <= new Date() && teste2 >= new Date()) {
              element.flgAndamento = true
              element.flgConsulta = false
            }

          }
          else if (tempHora == 0) {
            element.flgAndamento = false;
            element.flgConsulta = false
            teste2.setMinutes(teste2.getMinutes() + tempMin);
            if (hconsulta <= new Date() && teste2 >= new Date()) {
              element.flgAndamento = true
              element.flgConsulta = false
            }

          }
          else {
            element.flgAndamento = false;
            element.flgConsulta = false

          }

          element.valorAvaliacao = 0
          var ava = 0
          if (element.listaAvaliacao.length > 0) {
            element.listaAvaliacao.forEach(elementoAv => {
              if (ava == 0)
                ava = elementoAv.valor!;
              else
                ava = elementoAv.valor! + ava
            });
            if (ava > 0)
              element.valorAvaliacao = ava / element.listaAvaliacao.length
          }

          if (element.flgCheckInPaciente && !element.flgRealizada && !element.flgInativo && !element.flgAndamento) {
            element.flgConsulta = true;
          }
        });
        this.spinner.hide();
      } catch {
        this.spinner.hide();
      }
    }, () => { this.spinner.hide() })
  }

  tipoAgendamento() {
    this.agendaService.getTipoAgendamento(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      try {
        this.dadosTipoAgendamento = retorno;
        this.spinner.hide();
      } catch {
        this.spinner.hide();
      }
    }, () => {
      this.spinner.hide();
    })
  }

  CarregaespecialidadeMedico() {
    this.DadosEspecialidadeMedico = [];
    this.medicoService.getEspecialidadeMedicos(this.IdMedico).subscribe((retorno) => {
      try {
        this.DadosEspecialidadeMedico = retorno;
        this.spinner.hide();
      } catch {
        this.spinner.hide();
      }
    }, () => {
      this.spinner.hide();
    })
  }

  PreencheValorPagamento() {
    this.codConvenio = null
    this.valorConsulta = null

    if (this.idConvenio) {

      var convenio = this.dadosConvenio.filter((c: any) => c.idConvenio == this.idConvenio)

      if (convenio[0].desConvenio != "Particular" && convenio[0].valorConsulta) {
        this.valorConsulta = convenio[0].valorConsulta;
        this.valorConsulta = this.verificaCasaDecimal(this.valorConsulta)
        this.valorConsulta = this.aplicarMascaraValor(this.valorConsulta)
      }
    }
  }




  editarConsulta(id: any) {
    this.idConvenio = null
    this.codConvenio = ''
    this.FlgRetorno = false;
    this.valorConsulta = ''
    this.dtanova = null;
    this.selectedTime = [];
    this.flgExigePagamento = false;
    if (this.ListaMedicos.length > 0) {

      var edicao = this.consultas.filter(c => c.idConsulta == id)

      this.FlgProntuario = edicao[0].flgProntuario!;
      this.flgExigePagamento = edicao[0].flgExigePagamento!;
      this.IdPaciente = edicao[0].paciente!.idCliente;
      this.idtipoagendamento = edicao[0].idTipoAgendamento;
      this.tempo = edicao[0].tempoConsulta;
      this.DesAgendamento = edicao[0].desAnotacao == null ? '' : edicao[0].desAnotacao;
      this.idAgenda = edicao[0].idConsulta;
      this.Especialidade = edicao[0].idEspecialidade;
      this.IdMedico = edicao[0].idMedico;
      this.dtahora = new Date(edicao[0].dtaConsulta!).toLocaleString()
      this.dtanova = this.dtahora.substring(0, 10);
      this.selectedTime = this.dtahora.substring(11, 20);
      this.idConvenio = edicao[0].convenio!.idConvenio;
      this.codConvenio = edicao[0].codigoConvenio;
      this.FlgRetorno = edicao[0].flgRetorno!;

      if (edicao[0].valorConsulta) {
        this.valorConsulta = edicao[0].valorConsulta;
        this.valorConsulta = this.verificaCasaDecimal(this.valorConsulta)
        this.valorConsulta = this.aplicarMascaraValor(this.valorConsulta)
      }

      this.DataConsulta = edicao[0].dtaConsulta
      // 
      // 
      this.DtanascVasil = false;
      this.Dtanasc = false;
      this.hraEditVasil = false;


      this.ngxSmartModalService.getModal('editarHorario').open();

    }
    this.CarregaespecialidadeMedico();
  }
  DataHj() {

    if (this.Hoje) {
      this.DtaConsulta = new Date().toLocaleDateString();
      this.CarregaConsultas();
    }

    else {
      this.DtaConsulta = ""
      this.CarregaConsultas();

    }
  }


  ModalChekin(idConsulta: any) {

    this.DadosModalCheckin = 0
    if (idConsulta) {
      this.DadosModalCheckin = idConsulta
      this.ngxSmartModalService.getModal('ConfirmarChegada').open();

    }
    else {
      this.snackbarAlert.falhaSnackbar('Erro ao dar check-in')
    }
  }


  Checkin() {

    if (this.DadosModalCheckin > 0) {

      this.consultaService.CheckinConsulta(this.DadosModalCheckin, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe(() => {
        try {
          this.CarregaConsultas();
          this.consultaService.atualizaLegenda$.emit();
          this.ngxSmartModalService.getModal('ConfirmarChegada').close();
          this.spinner.hide()
        }
        catch {
          this.spinner.hide()

        }
      }, () => { this.spinner.hide() })

    }

  }


  SalvarConsulta() {
    this.validarCampos();
    if (this.showMessageError) {
      return;
    }
    try {

      var agenda = new Consulta();

      if (this.idAgenda! > 0 && this.idAgenda != undefined) {
        agenda.IdConsulta = this.idAgenda;

        if (this.dtanova) {

          if (this.selectedTime) {

            var datenova = this.validador.MontaData(this.dtanova.toString(), this.selectedTime);
            agenda.DtaConsulta = datenova;
          }
          else {

            var hora = new Date(this.DataConsulta!)
            var data = this.validador.Convertdata(this.dtanova.toString())


            datenova = this.validador.SetHorasData(data!, hora.toTimeString())!;
            agenda.DtaConsulta = datenova
          }
        }
        else if (this.selectedTime) {


          let datenova = this.validador.SetHorasData(this.DataConsulta!, this.selectedTime);
          agenda.DtaConsulta = datenova!;
        }
        else {
          agenda.DtaConsulta = this.DataConsulta;

        }

        if (this.Especialidade! > 0)
          agenda.IdEspecialidade = this.Especialidade;


        agenda.flgProntuario = this.FlgProntuario
        agenda.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
        agenda.idTipoAgendamento = this.idtipoagendamento;
        agenda.IdPaciente = this.IdPaciente!;
        agenda.IdMedico = this.IdMedico;
        agenda.DesAnotacao = this.DesAgendamento;
        agenda.TempoConsulta = this.tempo
        agenda.FlgInativo = false;
        agenda.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

        agenda.flgExigePagamento = this.FlgProntuario ? false : this.flgExigePagamento;
        if (this.flgExigePagamento && !this.valorConsulta) {

          this.valor.markAsTouched();
          return;
        }

        // agenda.ValorConsulta = document.getElementById('valorConsulta')['value'];


        if (this.valorConsulta) {
          this.valorConsulta = this.validador.removeMascara(this.valorConsulta);
          this.valorConsulta = this.valorConsulta!.replace(/(\d{1})(\d{1,2})$/, "$1.$2");
          agenda.ValorConsulta = this.valorConsulta;
        }



        agenda.CodigoConvenio = this.codConvenio!;
        agenda.FlgRetorno = this.FlgRetorno;
        agenda.idConvenio = this.idConvenio!;

        if (agenda.DtaConsulta)
          this.agendaService.salvarAgendamento(agenda).subscribe(() => {
            try {
              this.snackbarAlert.sucessoSnackbar('Agendamento salvo com sucesso')
              this.ngxSmartModalService.getModal('editarHorario').close();
              this.dtanova = null;
              this.DtanascVasil = false;
              this.hraEditVasil = false;
              this.selectedTime = null;
              this.CarregaConsultas();

              this.consultaService.atualizaLegenda$.emit();
              this.spinner.hide();
            } catch {
              this.spinner.hide();
            }
          }, () => {
            this.snackbarAlert.falhaSnackbar('Erro ao salvar agendamento')

            this.spinner.hide();
          })
      }

    } catch (error) {
      this.snackbarAlert.falhaSnackbar('Erro ao salvar agendamento')


    }
  }
  public MascaraDinheiro(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }

    (<HTMLInputElement>evento.target).value = v
  }
  public validarCampos() {

    this.showMessageError = false;
    this.VerificaHoraChange();
    this.ValidaDtaChange();
    if (this.Dtanasc == true || this.DtanascVasil == true
      || this.hraEdit == true || this.hraEditVasil == true) {

      this.showMessageError = true;
    }

    if (!this.IdPaciente || !this.IdMedico || this.Dtanasc || this.hraEditVasil || !this.idConvenio) {
      this.medi.markAsTouched();
      this.paci.markAsTouched();
      this.conv.markAsTouched();
      this.valor.markAsTouched();

      return;
    }

  }

  ValidaDtaChange() {
    const dta = (document.getElementById('Dtanova') as HTMLInputElement)['value'];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '' || dta == undefined) {
      this.DtanascVasil = true;
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.DtanascVasil = false;
      this.Dtanasc = false;
      this.DtanascLimpa = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
      this.DtanascVasil = false;
    }
    else
      this.Dtanasc = false
  }


  ValidaDtaEd(dta: any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.DtanascVasil = true;
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.DtanascVasil = false;
      this.Dtanasc = false;
      this.DtanascLimpa = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
      this.DtanascVasil = false;
    }
    else
      this.Dtanasc = false
  }

  VerificaHora(hora: any) {
    var hra = /^[0-9]{2}:[0-9]{2}$/;
    if (hora == '') {
      this.hraEditVasil = true;
      this.hraEdit = false;
    }
    else if (hra.test(hora)) {
      this.hraEditVasil = false;
      this.hraEdit = false;
    }
  }

  VerificaHoraChange() {
    const hora = (document.getElementById('SelectedTime') as HTMLInputElement)['value'];
    var hra = /^[0-9]{2}:[0-9]{2}$/;
    if (hora == '' || hora == undefined) {
      this.hraEditVasil = true;
      this.hraEdit = false;
    }
    else if (hra.test(hora)) {
      this.hraEditVasil = false;
      this.hraEdit = false;
    }
  }

  horarios() {
    var tempo = new Date();
    var hora = tempo.getHours();
    var minuto = tempo.getMinutes();
    var segundo = tempo.getSeconds();
    var temp = "" + ((hora > 12) ? hora - 12 : hora);
    if (hora == 0)
      temp = "12";
    temp += ((minuto < 10) ? ":0" : ":") + minuto;
    temp += ((segundo < 10) ? ":0" : ":") + segundo;
    temp += (hora >= 12) ? " P.M." : " A.M.";
    return temp;
  }

  informacao(Usuario: any, id: any) {

    this.DadosInformUsuario = {}
    if (Usuario == 'Paciente') {

      var user = this.DadosPacientes.filter((c: any) => c.idCliente == id);
      this.DadosInformUsuario = user[0]


      this.ngxSmartModalService.getModal('InforUsuario').open();
    }
    else {
      this.DadosInformUsuario = this.ListaMedicos.filter((c: any) => c.idMedico == id);
      this.ngxSmartModalService.getModal('InforUsuario').open();
    }
  }

  TempoCons() {
    if (this.segundos > 0) {
      this.segundos = this.segundos - 1

    }
    else if (this.segundos == 0) {

      if (this.minutos > 0) {
        this.segundos = 59
        this.minutos = this.minutos - 1

      }
      else if (this.minutos == 0) {
        if (this.hora > 0) {
          this.segundos = 59
          this.minutos = 59
          this.hora = this.hora - 1
        }
      }
    }
  }


  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }


  infoAvaliacao(id: any, idCliente: any) {
    this.quesitosAvaliacao = []
    this.ObsAvaliacao = ""
    this.consultaService.GetAvaliacaoConsulta(id).subscribe((retorno) => {
      try {
        if (retorno.length == 0) {
          this.CarregaQuisitosAvaliacao();
          if (this.tipoUsuario == 'Paciente') {
            this.idConsulta = id;
            this.idCliente = idCliente;
            this.ngxSmartModalService.getModal('Avaliacao').open();
            this.FormularioVotacao.forEach((element: any) => {
              if (element.nomeCampo == 'Atendimento') {
                this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
                  this.quesitoText = res;
                  return this.quesitoText;
                });
              }
              if (element.nomeCampo == 'TempoConsulta') {
                this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
                  this.quesitoText = res;
                  return this.quesitoText;
                });
              }
              if (element.nomeCampo == 'ValorConsulta') {
                this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
                  this.quesitoText = res;
                  return this.quesitoText;
                });
              }
              if (element.nomeCampo == 'UsabilidadeSistema') {
                this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe((res: string) => {
                  this.quesitoText = res;
                  return this.quesitoText;
                });
              }
            });

          }
          else
            this.ngxSmartModalService.getModal('AvaliacaoNaoFeita').open();

        }
        else {


          this.quesitosAvaliacao = retorno;
          this.ObsAvaliacao = retorno[0].obs;
          this.ngxSmartModalService.getModal('InforAvalicao').open();
        }
        this.spinner.hide();
      } catch (error) {
        this.spinner.hide();
      }
    })
  }


  infoCancelamento(id: any) {

    this.DadosInformCancelament = []
    var info = this.consultas.filter(c => c.idConsulta == id);


    this.DadosInformCancelament.nome = info[0].pessoaCancelamento!.nomePessoa
    this.DadosInformCancelament.Motivo = info[0].motivoCancelamento
    this.DadosInformCancelament.Dta = new Date(info[0].dtaCancelamento!).toLocaleString();

    this.ngxSmartModalService.getModal('InforCancelamento').open();

  }





  EnviarAvaliacao() {

    var form = document.getElementById('avaliacao');
    var qtdAtendimento = 0;
    var qtdTempoConsulta = 0;
    var qtdValorConsulta = 0;
    var qtdUsabiliddeSistema = 0;

    this.FormularioVotacao = [];

    const formElement = form as HTMLFormElement;
    for (var i = 0; i < formElement.elements.length; i++) {
      // var Campo = form["elements"][i]["name"];
      const inputElement = formElement.elements[i] as HTMLInputElement;
      var ValorCampo = inputElement['checked'];
      var IdCampo = Number(inputElement["value"]);

      if (IdCampo == 1) {
        if (ValorCampo == true) {
          qtdAtendimento = i;
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdAtendimento, nomeCampo: 'Atendimento' });
        } else if (i == 5 && qtdAtendimento == 0 && ValorCampo == false && IdCampo == 1) {
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdAtendimento, nomeCampo: 'Atendimento' });
        }
      }
      if (IdCampo == 2) {
        if (ValorCampo == true) {
          qtdTempoConsulta = i - 6;
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdTempoConsulta, nomeCampo: 'TempoConsulta' });
        } else if (i == 11 && qtdTempoConsulta == 0 && ValorCampo == false && IdCampo == 2) {
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdTempoConsulta, nomeCampo: 'TempoConsulta' });
        }
      }
      if (IdCampo == 3) {
        if (ValorCampo == true) {
          qtdValorConsulta = i - 12;
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdValorConsulta, nomeCampo: 'ValorConsulta' });
        }
        else if (i == 17 && qtdValorConsulta == 0 && ValorCampo == false && IdCampo == 3) {
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdValorConsulta, nomeCampo: 'ValorConsulta' });
        }
      }
      if (IdCampo == 4) {
        if (ValorCampo == true) {
          qtdUsabiliddeSistema = i - 18;
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdUsabiliddeSistema, nomeCampo: 'UsabiliddeSistema' });
        }
        else if (i == 23 && qtdUsabiliddeSistema == 0 && ValorCampo == false && IdCampo == 4) {
          this.FormularioVotacao.push({ Campo: IdCampo, valor: qtdUsabiliddeSistema, nomeCampo: 'UsabiliddeSistema' });
        }
      }

    }

    var teste: any = []

    if (this.FormularioVotacao.length == 0) {

    } else {
      this.FormularioVotacao.forEach((element: any) => {

        var quesito = new QuesitoAvaliacao()

        if (element.nomeCampo == 'Atendimento') {
          quesito.IdQuesito = 1
          quesito.valorAvaliacao = element.valor
          quesito.FlgInativo = false
          quesito.DtaCadastro = new Date()
        }

        else if (element.nomeCampo == 'TempoConsulta') {
          quesito.IdQuesito = 2
          quesito.valorAvaliacao = element.valor
          quesito.FlgInativo = false
          quesito.DtaCadastro = new Date()
        }

        else if (element.nomeCampo == 'ValorConsulta') {
          quesito.IdQuesito = 3
          quesito.valorAvaliacao = element.valor
          quesito.FlgInativo = false
          quesito.DtaCadastro = new Date()
        }

        else if (element.nomeCampo == 'UsabiliddeSistema') {
          quesito.IdQuesito = 4
          quesito.valorAvaliacao = element.valor
          quesito.FlgInativo = false
          quesito.DtaCadastro = new Date()
        }
        teste.push(quesito)
      });
    }

    var avaliacao = new Avaliacao()
    avaliacao.quesitosAvaliacao = teste;
    avaliacao.FlgInativo = false;
    avaliacao.IdConsulta = this.idConsulta;
    avaliacao.IdCliente = this.idCliente!;
    avaliacao.Observacao = this.AlgumaObservacao;

    this.consultaService.SalvaAvaliacao(avaliacao).subscribe(() => {
      try {
        this.CarregaConsultas()
        this.ngxSmartModalService.getModal('Avaliacao').close();
        this.spinner.hide();
      } catch (error) {
        this.spinner.hide();
      }
    }, () => { this.spinner.hide() })
  }

  modalInformativo(id: any) {
    var info = this.consultas.filter(c => c.idConsulta == id);

    this.modalInfo.idConsulta = id;
    this.modalInfo.guid = this.url_atual + "/atendimento/" + info[0].guidAcesso;
    this.modalInfo.CodAcesso = info[0].codAcesso;
    this.ngxSmartModalService.getModal('infoConsulta').open();

  }
  EnviarWhatsAcesso(idConsulta: any) {
    const objConsulta = this.consultas.filter(c => c.idConsulta == idConsulta);
    const objacesso = new AcessoRapidoEmail;
    objacesso.LinkAcesso = this.modalInfo.guid;
    objacesso.CodAcesso = this.modalInfo.CodAcesso;


    if (objConsulta[0].paciente!.pessoa!.contato!.telefoneMovel != null) {
      var celular = objConsulta[0].paciente!.pessoa!.contato!.telefoneMovel != null ? objConsulta[0].paciente!.pessoa!.contato!.telefoneMovel.replace(/[^0-9]/g, '') : "";

      var texto = "Link de Acesso: " + objacesso.LinkAcesso + "  \n Cod.Acesso: " + objacesso.CodAcesso + "  \n ";
      // texto = window.encodeURIComponent(texto.toString());

      window.open("https://api.whatsapp.com/send?phone=55" + celular + "&text=" + texto, "_blank");

    }
    else {

      this.snackbarAlert.falhaSnackbar('Erro ao enviar Whatsapp, Verifique o numero do paciente!')
    }
  }


  EnviarEmailAcesso(idConsulta: any) {
    const objConsulta = this.consultas.filter(c => c.idConsulta == idConsulta);
    const objacesso = new AcessoRapidoEmail;
    objacesso.Email = objConsulta[0].paciente!.pessoa!.email!;
    objacesso.LinkAcesso = this.modalInfo.guid;
    objacesso.CodAcesso = this.modalInfo.CodAcesso;
    objacesso.NomeMedico = objConsulta[0].medico!.pessoa!.nomePessoa!;
    objacesso.Paciente = objConsulta[0].paciente!.pessoa!.nomePessoa!;
    objacesso.DtaConsulta = new Date(objConsulta[0].dtaConsulta!).toLocaleString();

    if (!objacesso.Email) {

      this.snackbarAlert.falhaSnackbar('Email do paciente não existente, Verifique!')
      return;
    }
    this.emailService.MandaEmailAcesso(objacesso).subscribe(() => {
      try {
        this.snackbarAlert.sucessoSnackbar('Email com Acesso enviado!')

      } catch (error) {
        this.snackbarAlert.falhaSnackbar('Erro ao enviar Email, Verifique!')
        this.spinner.hide();
      }

    }, () => {
      this.snackbarAlert.falhaSnackbar('Erro ao enviar Email, Verifique!')

    })

  }


  CarregaConvenio() {
    this.convenioService.getConvenios(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      try {
        this.dadosConvenio = retorno;
        this.spinner.hide();
      } catch { this.spinner.hide(); }

    }, () => { this.spinner.hide(); })
  }


  CarregaQuisitosAvaliacao() {
    this.consultaService.getQuesitosAvaliacao().then((retorno) => {
      this.quesitosAvaliacao = retorno
      this.spinner.hide();
    }, () => { this.spinner.hide(); })
  }

  irTesteConexao() {
    window.open('/testeConexao', '_blank');
  }



  modalInfo: any = [];
  selectedDate: any;
  FlgDestroiCalendario: boolean = true;
  lsQtdCalendario = [];

  @ViewChild('calendar2') calendar!: MatCalendar<any>;

  onSelect(event: any) {
    ;
    this.FlgDestroiCalendario = false;
    this.DtaConsulta = event.toLocaleDateString();
    this.CarregaConsultas();

    let dataFinal = this.calcularDataProximoMes(event);

    this.calendar._goToDateInView(dataFinal, "month");
    this.DtaConsultaFim = dataFinal.toLocaleDateString();
    if (this.DtaConsulta != new Date().toLocaleDateString())
      this.Hoje = false;



    this.FlgDestroiCalendario = true;
  }

  calcularDataProximoMes(dataSelecionada: Date): Date {

    // Cria uma nova data baseada na data selecionada
    let novaData = new Date(dataSelecionada);

    // Adiciona um mês
    novaData.setMonth(novaData.getMonth() + 1);

    // Verifica se o dia da nova data é menor que o dia da data original
    if (novaData.getDate() < dataSelecionada.getDate()) {
      // Se sim, ajusta para o último dia do mês
      novaData.setDate(0); // 0 é o último dia do mês anterior
    }

    return novaData;
  }

  varificaTipoUsuario() {
    if (this.idTipoUsuario == EnumTipoUsuario.Médico) {
      this.loginComMedico = true

      this.medicoService.getMedicos(this.Especialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        try {
          this.ListaMedicos = retorno

          var medico = this.ListaMedicos.filter((c: any) => c.idUsuarioacesso == this.usuarioLogadoService.getIdUsuarioAcesso())
          this.IdMedico = medico[0].idMedico
          this.spinner.hide();
        } catch {
          this.spinner.hide();
        }

      }, () => {
        this.spinner.hide();
      })

    } else
      this.loginComMedico = false
    this.spinner.hide();

  }

  consolePrintVariavel(variavel: any){
    console.log("• consolePrintVariavel" , variavel);
  }



  // private hubConnection: HubConnection;
  // isHubConnected: boolean = false;
  // startSignalR() {

  //   this.hubConnection
  //     .start()
  //     .then((res) => {
  //       ;
  //       this.isHubConnected = true;
  //     })
  //     .catch(err => {
  //       console.error('FEDEU!', err);
  //       this.isHubConnected = false;
  //     });
  // }

  // initializeSignalIR() {
  //   const buildHubConn = new HubConnectionBuilder();
  //   buildHubConn.configureLogging(LogLevel.Information)
  //   buildHubConn.withUrl(environment.apiEndpoint + '/notify');
  //   this.hubConnection = buildHubConn.build();

  //   this.startSignalR();
  //   this.hubConnection.onclose((err) => {
  //     //console.error(err);
  //     setTimeout(() => this.startSignalR(), 1000);
  //   });
  //   this.hubConnection.on('DisparaAlertConsulta', (res: string) => {
  //     this.CarregaAtualizacao();

  //   });

  // }


  aplicarMascaraValor(v: any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor: any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
  }

  indexGlobal: number = 0;
  openToggle(index: any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
  }

  @ViewChild('ModalConfirmaçãoLista') modalTemplate!: TemplateRef<any>;
  tipoSelecaoFiltroModalConfirmacao: number = 2;
  listaConfirmcaoPaciente: any[] = [];
  listaConfirmcaoPacienteBuckup: any[] = [];

  dialogRef: any;

  fecharModal() {
    this.dialogRef.close();
  }

  abrirModal() {
    this.tipoSelecaoFiltroModalConfirmacao = 2;
    this.listaConfirmcaoPacienteBuckup = [];

    let listaIds: number[] = []

    this.consultas.forEach(element => {
      listaIds.push(element.idConsulta!);
    });

    this.controleModaisService.AbreModalRespostaWhats(this.consultas, listaIds)
  }

  modalEnvioMsg() {
    this.dialogRef = null;
    this.dialogRef = this.matDialog.open(ModalEnvioMensagensWhatsappComponent, {
      width: '75vmax',
      height: '70vh',
      data: {}
    });

    this.dialogRef.afterClosed().subscribe(() => { });
  }

  filtrarListaConfirmacaoPaciente() {
    this.listaConfirmcaoPaciente = [];

    if (this.tipoSelecaoFiltroModalConfirmacao == 1)
      this.listaConfirmcaoPacienteBuckup.forEach(element => {
        this.listaConfirmcaoPaciente.push(element);
      });

    else if (this.tipoSelecaoFiltroModalConfirmacao == 2)
      this.listaConfirmcaoPaciente = this.listaConfirmcaoPacienteBuckup.filter(x => x.status == "Confirmação não enviada");


    else if (this.tipoSelecaoFiltroModalConfirmacao == 3)
      this.listaConfirmcaoPaciente = this.listaConfirmcaoPacienteBuckup.filter(x => x.status == "Confirmada");


    else if (this.tipoSelecaoFiltroModalConfirmacao == 4)
      this.listaConfirmcaoPaciente = this.listaConfirmcaoPacienteBuckup.filter(x => x.status == "Cancelada");

    else if (this.tipoSelecaoFiltroModalConfirmacao == 5)
      this.listaConfirmcaoPaciente = this.listaConfirmcaoPacienteBuckup.filter(x => x.status == "Aguardando confirmação do paciente");

    else if (this.tipoSelecaoFiltroModalConfirmacao == 6)
      this.listaConfirmcaoPaciente = this.listaConfirmcaoPacienteBuckup.filter(x => x.status == "Finalizada");
  }

  salvarConfirmacaoConsultas(id: number) {
    this.whatsService.EnvioUnicoConfirmacaoConsultas(id).subscribe(
      (ret) => {
        try {
          if (ret) {
            this.CarregaConsultas();
            this.snackbarAlert.sucessoSnackbar('Mensagem de confirmação enviada!');
          }

          else
            this.snackbarAlert.falhaSnackbar('Falha ao enviar a mensagem de confirmação.');

          this.spinner.hide();

        } catch {
          this.spinner.hide();
        }

      },
      () => {
        this.snackbarAlert.falhaSnackbar('Falha ao enviar a mensagem de confirmação.');
        this.spinner.hide();
      })

  }

  salvarConfirmacaoConsultasLista() {
    var selectedIds: any;
    selectedIds = this.consultas
      .filter(consulta => consulta.dtaConfirmacao != null)
      .map(consulta => consulta.idConsulta);

    if (selectedIds.length > 0) {
      ;
      this.whatsService.EnviaListaConfirmacaoConsultas(selectedIds).subscribe(() => {
        try {
          this.CarregaConsultas();
          this.spinner.hide();
        } catch {
          this.spinner.hide();
        }
      }, () => { this.spinner.hide() })
    }
  }

  validaStatusClienteConsulta(dtaConsulta: any): number | undefined {
    const dataConsulta = new Date(dtaConsulta);

    if (isNaN(dataConsulta.getTime())) {
      return 5;
    }

    const agora = new Date();
    const delta = agora.getTime() - dataConsulta.getTime();

    if (delta < 0)
      return 1;

    else if (delta > 1800000 && delta <= 900000)
      return 2;

    else if (delta > 900000 && delta <= 1800000)
      return 3;

    else if (delta > 1800000 && delta < 3600000)
      return 4;

  }
  confirmarChegadaPaciente(idConsulta: number) {

    let dialogRef = this.matDialog.open(ModalConfirmaChegadaPacienteComponent, {
      data: idConsulta
    });

    dialogRef.afterClosed().subscribe((ret) => {
      if (ret)
        this.CarregaConsultas();

    })
  }

  AdicionaObservacaoConsultaComponent(idConsulta: number, obs: string) {

    let obj: ObjetoPadrao = new ObjetoPadrao();
    obj.num = idConsulta;
    obj.str = obs;

    let dialogRef = this.matDialog.open(ModalAdicionaObservacaoConsultaComponent, {
      data: obj,
      disableClose: true
    });

    dialogRef.afterClosed().subscribe((ret) => {
      ;
      if (ret)
        this.CarregaConsultas();

    })
  }




  validaExibieStatusConsulta(dtaConsulta: any): boolean {
    const dataConsulta = new Date(dtaConsulta);

    if (isNaN(dataConsulta.getTime())) {
      return false;
    }
    const agora = new Date();
    const delta = agora.getTime() - dataConsulta.getTime();

    if (delta > 3600000)
      return false;

    else
      return true;
  }

  AbrirModalListaProcedimento(consulta: ConsultaModelView) {
    this.dialogRef = null;
    this.dialogRef = this.matDialog.open(ListaProcedimentoComponent, {
      width: '710px',
      height: '90vh',
      data: {
        idConsulta: consulta.idConsulta,
        idConvenio: consulta.convenio!.idConvenio,
        idFatura: consulta.idFatura
      }
    });
  }

  AbrirModalFaturamentoGeral() {
    this.controleModaisService.ModalConfiguraFaturamentoGeral(this.consultas);
  }

  AbrirModalFaturamento(consulta: ConsultaModelView) {
    ;
    this.controleModaisService.idConsulta = consulta.idConsulta!;
    this.controleModaisService.ModalConfiguraFaturamentoPorId(consulta.idConsulta);
  }
}


