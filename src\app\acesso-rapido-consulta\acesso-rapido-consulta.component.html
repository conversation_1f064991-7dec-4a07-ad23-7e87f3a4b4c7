<header>
  <a  [routerLink]="['/perfil']"><img src="/assets/build/img/logo faso Final  - Copia.png"
          class="logo-footer" style="margin-left: 2% !important;
      cursor: pointer;"></a>


</header>
<div class="background-login">
  <div class="div-login" *ngIf="DivInicial">
    <div class="container-fluid">
      <div class="col-3 col-md-3"></div>
      <div class="col-lg-6 col-md-6 col-sm-12 col-12">
        <mat-card appearance="outlined" class="div-login col-12">
          <mat-card-title class="text-center">
                              width: 75% !important;" alt="logo" class="logo-controler">
          </mat-card-title>
          <mat-form-field class="input-login" appearance="outline">
            <input matInput placeholder="Cod.Acesso"  (keyup.enter)="CarregaConsulta()" id="acesso" autocomplete="off" name="acesso"
              [(ngModel)]="codAcesso">
            <mat-label><i class="fas fa-user-injured space-icon"></i> <i>Cod.Acesso
              </i></mat-label>
          </mat-form-field>
    
    
          <div class="danger-baloon" *ngIf="logininvalido == true">
            <label style="color: red;" class="text-right">{{ 'TELALOGIN.CODIGOACESSOINVALIDO' | translate }}</label></div>
          <div class="row-button ">
            <button mat-flat-button class="btn-primary"  (click)="CarregaConsulta()">{{ 'TELALOGIN.ENTRAR' | translate }}</button>
            <span class=""> </span>
          </div>
        </mat-card>
      </div>
      <div class="col-6 col-md-3"></div>
    </div>
    
  </div>

<div class="container-fluid">
  <div class="row">
    <div class="col-3 col-md-3 "></div>
    <div class="col-lg-6 col-md-6 col-sm-12 col-12">
      <div class="div-login1" *ngIf="DivEspera">
        <mat-card appearance="outlined" class="div-login1">
          <mat-card-title class="text-center">
            <!-- <h3>  <img src="assets/build/img/cardiogram.svg" class="logo-controler"> Medicina <img src="/assets/build/img/pulse-line.svg" class=""> Você</h3> -->
            <img src="assets/build/img/Logo-text.png" class="logo-controler">
          </mat-card-title>
          <div class="text-center" style="margin-top: 3%; margin-bottom: 3%;">
            <b> {{ 'TELAMENU.CONSULTAAGENDADA' | translate }} {{ 'TELAMENU.PARA' | translate }}
              {{usuarioConsulta.dtaAgenda  | date: 'dd/MM/yyyy HH:mm' }} </b> <br>
          </div>
          <div class="row">
            <div class="col-12 text-center" style="padding: 10px;">
              <img src={{ImagemPessoaConsulta}} class="img-circle" style="
              width: 15%;
          ">
            </div>
            <div class="col-12 body-config text-center">
              <b class="Title-config">
                {{usuarioConsulta.medico }}
              </b>
            </div>
          
    
            <div class="col-12 panel-button-dropdown text-center" *ngIf="flgIniciarConsulta">
              <button class=" button-interactive btn btn-primary" (click)="iniciarConsulta(usuarioConsulta.idConsulta)">
                Iniciar Atendimento</button>
            </div>
          </div>
          <div class="text-center" style="margin-top: 3%; margin-bottom: 3%;"  *ngIf="!flgIniciarConsulta && flgAntes">
            <!-- <b> Faltam  {{hora | number:'2.0'}}:{{minutos | number:'2.0'}}:{{segundos | number:'2.0'}}</b> <br> -->
            <b> Seu acesso sera liberado assim que o médico entrar na sala virtual.</b> <br>
          </div>
          <div class="text-center" style="margin-top: 3%; margin-bottom: 3%;"  *ngIf="!flgIniciarConsulta && !flgAntes">
            <!-- <b> Faltam  {{hora | number:'2.0'}}:{{minutos | number:'2.0'}}:{{segundos | number:'2.0'}}</b> <br> -->
            <b> Sua horário excedeu, entre em contato para realizar o reagendamento!</b> <br>
          </div>
          <div class="row-button padding-10" *ngIf="DivInicial">
            <button mat-flat-button class="btn-primary" (click)="CarregaConsulta()">{{ 'TELALOGIN.ENTRAR' | translate }}</button>
            <span class=""> </span>
          </div>
        </mat-card>
      </div>
    </div>
    <div class="col-6 col-md-3"></div>
  </div>
  
</div>
</div>


<mpv-modal-pagamento></mpv-modal-pagamento>

<ngx-smart-modal #modalErroConsultaRapida identifier="modalErroConsultaRapida" customClass="nsm-centered medium-modal emailmodal">
  <div class="col-md-12 background-Iniciar" style="display: flex; height: 100px; width: 515px !important">
    <div class="modal-info" style="margin-top: 48px;">
      <b> </b><br>
    </div>
    <mat-icon style="color: white; font-size: 88px;  margin-left: 25%;"> check</mat-icon>

  </div>


  <div class="modal-info">
    {{ mensagemModal }}
  </div>

  <mat-divider></mat-divider>
  <div style="text-align: center; margin-bottom: 3%; margin-top: 3%;">
    <button class=" button-interactive btn btn-primary" (click)="fecharModalErro()">
      Fechar </button>
  </div>
</ngx-smart-modal>


<footer class="footer-faso-novo">
  <div class="telaGrande" 
          style="margin-right: 50px; font-weight:400;"> 
      <img src="/assets/build/img/microsoftLogo.png"
          class="logoRodapefooter">
  </div>
  <div class="telaGrande" 
          style="margin-right: 50px; font-weight:400; align-self: center;cursor: context-menu;"> 
      <a style="margin-right: 5px;">
          {{ 'TELALOGIN.SITESEGURO' | translate }}
      </a> 
  </div>
  <div>
      <a href="https://www.facebook.com/MedicinaParaVoce/posts/117102886621942" target="_blank">
          <i class="fa fa-facebook fa-fw fa-2x logoRodapefooter" 
                  style="color:#002e4b; cursor: pointer;"
                  click="abrirFacebook">
          </i>
      </a>
      <a href="https://www.youtube.com/watch?v=ePea_b-M85c&feature=emb_title" target="_blank">
          <i class="fa fa-youtube fa-fw fa-2x" 
                  style="color:rgb(255, 0, 0); cursor: pointer; margin-left: 20px;">
          </i>
      </a>
      <a href="https://www.instagram.com/medicinaparavoce/" target="_blank">
          <i class="fa fa-instagram fa-fw fa-2x" 
                  style="color:black;cursor: pointer; margin-left: 20px;">
          </i>
      </a>
      <a href="https://twitter.com/medicinaparavc/status/1249782319075995649" target="_blank">
          <i class="fa fa-twitter fa-fw fa-2x" 
                  style="cursor: pointer; margin-left: 20px;">
          </i>
      </a>
  </div>
  <div class="telaGrande"
          style="margin-right: 50px; font-weight:400; align-self: center; cursor: context-menu;">
      <a style="margin-right: 5px;">
          © 2020 - Faso Fábrica de Software
      </a>
  </div>
</footer>







