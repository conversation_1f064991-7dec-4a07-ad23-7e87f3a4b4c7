import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { Contato } from '../model/contato';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class ContatosService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getContatos(pesquisa:any, tipoPesquisa:any, idClinica:any, inicio:any, fim:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('tipoPesquisa', String(tipoPesquisa));
        params = params.append('idClinica', String(idClinica));
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        
        return this.http.get(environment.apiEndpoint + '/Contato/getContatos', { params });
    }

    public salvarContato(contato: Contato): Observable<any> {

        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Contato/salvarContato', contato);
    }


    public inativarContato(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Contato/inativarContato/' + id + '/' + idUsuario);
    }
}
