<div class="modal-content modal-mensagemdia">
    <h2 mat-dialog-title>
        <mat-icon class="icon-title">access_alarm</mat-icon>
        {{'TELAAGENDA.MENSAGEMDODIA2' | translate}} {{ DiaRecado}}
        {{'TELAAGENDA.PARA' | translate}} {{NomeMedico}}
    </h2>

    <div mat-dialog-content class='div-textarea-msn'>
        <mat-form-field class="col-md-12 col-sm-12 col-xs-12"
            hintLabel="Máx. 500" floatLabel="always" appearance="outline">
            <mat-label>Digite a Mensagem</mat-label>
            <textarea matInput required maxlength="500" name="obs"
                [(ngModel)]="DesRecadoDia"
                style="max-height: 250px; min-height: 100px;"></textarea>
            <mat-hint align="end">{{ DesRecadoDia.length }}/500</mat-hint>
            <mat-error *ngIf="campoDesRecadoVazio">Esse campo deve ser
                preenchido</mat-error>
        </mat-form-field>

        <div class="button_custom text-center botao-enviar-dia">
            <button mat-raised-button color="primary"
                style="margin-right: 2%; color:white; margin-bottom: 10px"
                (click)="SalvarMensagem()">
                <mat-icon>save</mat-icon>{{'TELAAGENDA.ENVIAR' | translate}}
            </button>
        </div>
    </div>

    <div *ngIf="DadosRecados.length > 0"
        style="max-height: 220px; overflow: auto;">
        <h3>Mensagens do dia</h3>
        <table class="table table-striped table-hover">
            <tbody>
                <tr *ngFor="let item of DadosRecados"
                    class="card_table no-mobile-card">
                    <td style="min-width:30%;">
                        <b>Usuário Gerador: {{item.usuarioGerador}}</b>
                    </td>
                    <td style="text-align: center;">
                        <b>Data de Criação: {{item.dtaCriacao |
                            date:'dd/MM/yyyy HH:mm'}}</b>
                    </td>
                    <td style="text-align: center;">
                        <b title="{{item.recado}}">Mensagem: {{item.recado
                            }}</b>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div mat-dialog-actions>
        <button mat-button (click)="onNoClick()">Close</button>
    </div>
</div>