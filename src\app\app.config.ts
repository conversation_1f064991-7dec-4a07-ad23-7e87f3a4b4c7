import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { HTTP_INTERCEPTORS, provideHttpClient, withFetch, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateLoader, TranslateModule, TranslateService, TranslateStore } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient } from '@angular/common/http';
import { AuthInterceptor } from './service/auth.interceptor';
import { AppRoutes } from './app.routes';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { ProgressButtonModule } from '@syncfusion/ej2-angular-splitbuttons';

export function HttpLoaderFactory(http: HttpClient) {
    return new TranslateHttpLoader(http, './assets/Traducoes/', '.json');
}

export const appConfig: ApplicationConfig = {
    providers: [
        provideRouter(AppRoutes),
        provideAnimationsAsync(),
        provideHttpClient(withFetch()),
        provideHttpClient(withInterceptorsFromDi()),

        {
            provide: HTTP_INTERCEPTORS,
            useClass: AuthInterceptor,
            multi: true
        },
        NgxSmartModalService,
        TranslateService,
        TranslateStore,
        importProvidersFrom(
            ProgressButtonModule,
            MatMenuModule,
            ReactiveFormsModule,
            FormsModule,
            MatInputModule,
            MatFormFieldModule,
            NgxSmartModalModule.forRoot(),
            TranslateModule.forRoot({
                loader: {
                    provide: TranslateLoader,
                    useFactory: HttpLoaderFactory,
                    deps: [HttpClient]
                }
            })
        )
    ]
};
