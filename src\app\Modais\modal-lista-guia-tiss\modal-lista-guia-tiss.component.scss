/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 87vh;
}

/* CABEÇALHO */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-main {
  display: flex;
  align-items: center;
}

.header-icon {
  background-color: $primary-light;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-icon .material-icons {
  color: $primary-color;
  font-size: 24px;
}

.header-title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
}

.header-actions {
  display: flex;
  align-items: center;
}

.btn-voltar {
  display: flex;
  align-items: center;
  color: $primary-color;
  background: transparent;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color $transition;
}

.btn-voltar:hover {
  background-color: $primary-light;
}

.btn-voltar mat-icon {
  margin-right: 8px;
}

/* AÇÕES */
.actions-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.spacer {
  flex: 1;
}

.actions-buttons {
  display: flex;
  gap: 16px;
}

.btn-acao {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all $transition;
  height: 40px;
}

.btn-acao:hover {
  background-color: $primary-dark;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.btn-acao:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* TABELA */
.tabela-container {
  flex: 1;
  overflow: auto;
  border-radius: $border-radius;
  border: 1px solid $border-color;
  background-color: $bg-color;
}

.tabela-container::-webkit-scrollbar {
  width: 8px;
}

.tabela-container::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.tabela-container::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.tabela-faturas {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  min-width: 600px;
}

.tabela-faturas thead {
  position: sticky;
  top: 0;
  z-index: 10;
}

.tabela-faturas th {
  background-color: $primary-color;
  color: white;
  text-align: left;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
}

.tabela-faturas th:first-child {
  border-top-left-radius: $border-radius;
}

.tabela-faturas th:last-child {
  border-top-right-radius: $border-radius;
}

.tabela-faturas tbody tr {
  background-color: white;
  transition: background-color $transition;
}

.tabela-faturas tbody tr:hover {
  background-color: $primary-light;
}

.tabela-faturas td {
  padding: 12px 16px;
  border-bottom: 1px solid $border-color;
  font-size: 14px;
}

.cell-content {
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.coluna-check {
  width: 5%;
  text-align: center;
}

.coluna-descricao {
  width: 40%;
}

.coluna-convenio {
  width: 30%;
}

.coluna-total {
  width: 10%;
  text-align: right;
}

.coluna-acoes {
  width: 15%;
  text-align: center;
}

.btn-detalhar {
  background-color: transparent;
  color: $primary-color;
  transition: all $transition;
}

.btn-detalhar:hover {
  background-color: $primary-color;
  color: white;
  transform: scale(1.1);
}

/* LINHA VAZIA */
.linha-vazia td {
  padding: 48px 16px;
  text-align: center;
}

.mensagem-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: $text-secondary;
}

.mensagem-vazia mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* RESPONSIVIDADE */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 16px;
    width: 100%;
    justify-content: flex-end;
  }
  
  .actions-container {
    flex-direction: column;
  }
  
  .actions-buttons {
    margin-top: 16px;
    width: 100%;
    justify-content: flex-end;
  }
}

/* ESTILOS ADICIONAIS PARA MODAIS */
.mother-div {
  margin: 0 !important;
  box-shadow: 0 7px 25px rgba(0,0,0,0.20)!important;
  border: none !important;
  border-radius: 10px !important;
  padding: 15px;
}

section {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

main {
  flex: 1;
  height: 100%;
  padding: 5px 0px;
  overflow-y: auto;
}

footer {
  flex-shrink: 0;
}

header {
  width: 100%;
}

.ConteudoCabecalho {
  flex: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 5px;
  justify-content: space-between;
}

.ConteudoCabecalho button {
  box-shadow: 0 3px 1px -2px #0003,0 2px 2px #00000024,0 1px 5px #0000001f;
  padding: 5px 16px;
  border-radius: 5px;
}

.CaixaMatInput mat-form-field {
  height: 45px !important;
}

.CaixaBtn {
  align-content: end;
}

.CaixaBtn button {
  padding: 8px;
  justify-self: end;
  width: 100px;
  border: none;
  background-color: $primary-color;
  color: #fff;
  border-radius: 5px;
  height: 40px;
  flex-flow: row wrap;
  border-collapse: collapse; 
  font-size: 14px;
}

.CaixaBtn button:hover {
  background-color: $primary-dark;
  color: #fff;
}