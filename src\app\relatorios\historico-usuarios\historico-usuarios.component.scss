@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

// Variáveis - Esquema de cores verde
$primary-color: #2E8B57; // Verde principal
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; 
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Base Styles */
body {
  font-family: 'Cairo', sans-serif;
  background-color: $bg-color;
  color: $text-primary;
}

/* Main Card */
.mother-div {
  background-color: #fff !important;
  border: none !important;
  box-shadow: $box-shadow !important;
  border-radius: $border-radius !important;
  margin: 0 !important;
  border-top:4px solid #2E8B57 !important ;

}

/* Containers */
.white-container {
  background-color: $card-bg;
  margin: 15px;
}

/* Header Styles */
.spacer-card {
  padding: 0 !important;
  margin-bottom: 0 !important;
}

.header-container {
  display: flex;
  align-items: center;
  padding: 20px;
}

.icon-title {
  margin: 0 !important;
  background: transparent !important;
  color: $primary-color !important;
  align-self: center;
  vertical-align: middle;
  border-radius: 100% !important;
  font-size: 30px !important;
  border: 2px solid $primary-color !important;
  height: 38px !important;
  width: 38px !important;
  text-align: center;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-content {
  font-size: 24px;
  align-self: center;
  vertical-align: middle;
  margin-left: 10px;
  font-weight: 500;
  color: $primary-color;
  font-family: 'Cairo', sans-serif;
}

/* Content Section */
.content-section {
  margin-top: 20px;
}

/* Form Fields */
.select-field {
  align-self: center;
}

.date-field {
  padding: 5px;
  position: relative;
  margin-bottom: 15px;
}

mat-form-field {
  width: 100%;
}

.error-text {
  font-size: 11px;
  color: $error-color;
  position: absolute;
  left: 20px;
  top: 33px;
}

/* Button Styles */
.button-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 15px;
}

.search-button, .generate-button {
  box-shadow: 0 3px 1px -2px rgba(0,0,0,0.2), 0 2px 2px rgba(0,0,0,0.14), 0 1px 5px rgba(0,0,0,0.12);
  padding: 5px 16px;
  font-size: 14px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  transition: $transition;
}

.search-button {
  background-color: $primary-color !important;
  color: white !important;
}

.generate-button {
  background-color: $primary-color !important;
  color: white !important;
}

.search-button:hover, .generate-button:hover {
  background-color: $primary-dark !important;
  transform: translateY(-2px);
}

.search-button mat-icon, .generate-button mat-icon {
  margin-right: 8px;
  margin-bottom: 0 !important;
}

/* Table Styles */
.table-container {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 16px;
  margin-bottom: 30px;
}

.results-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 10px;
}

.result-row {
  background-color: $secondary-light;
  border-radius: $border-radius;
  transition: $transition;
}

.result-row:hover {
  box-shadow: $box-shadow;
  transform: translateY(-2px);
}

.user-cell, .description-cell, .date-cell {
  padding: 15px;
  font-weight: normal;
}

.user-cell {
  width: 30%;
  border-radius: $border-radius 0 0 $border-radius;
}

.description-cell {
  width: 40%;
}

.date-cell {
  border-radius: 0 $border-radius $border-radius 0;
}

.cell-label {
  font-weight: 600;
  color: $primary-color;
}

/* Load More Button */
.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.load-more-button {
  background-color: $primary-color !important;
  color: white !important;
  padding: 0 20px;
  border-radius: $border-radius;
  transition: $transition;
}

.load-more-button:hover {
  background-color: $primary-dark !important;
  transform: translateY(-2px);
}

/* Modal Styles */
.modal-container {
  border-radius: $border-radius;
  overflow: hidden;
}

.modal-header {
  background-color: $primary-light;
  padding: 20px;
  text-align: center;
}

.success-header {
  background-color: $primary-color;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-title {
  color: white;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.modal-actions {
  background-color: $card-bg;
  padding: 20px;
  display: flex;
  justify-content: center;
}

.ok-button {
  background-color: $primary-color !important;
  color: white !important;
  padding: 0 20px !important;
  height: 40px !important;
  border-radius: $border-radius !important;
  margin-bottom: 10px;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    text-align: center;
  }
  
  .icon-title {
    margin-bottom: 10px !important;
  }
  
  .title-content {
    margin-left: 0;
  }
  
  .no-mobile {
    display: none;
  }
  
  .button-container {
    flex-direction: column;
    align-items: center;
  }
  
  .search-button, .generate-button {
    width: 80%;
    justify-content: center;
    margin-bottom: 10px;
  }
  
  .result-row {
    display: flex;
    flex-direction: column;
  }
  
  .user-cell, .description-cell, .date-cell {
    width: 100%;
    border-radius: 0;
  }
  
  .user-cell {
    border-radius: $border-radius $border-radius 0 0;
  }
  
  .date-cell {
    border-radius: 0 0 $border-radius $border-radius;
  }
}

@media (max-width: 480px) {
  .white-container {
    margin: 10px;
    padding: 15px;
  }
  
  .header-container {
    padding: 15px;
  }
  
  .icon-title {
    font-size: 24px !important;
    height: 32px !important;
    width: 32px !important;
  }
  
  .title-content {
    font-size: 20px;
  }
}
/* Estilos compartilhados */
.mat-card {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

.mat-card-header {
  padding: 16px 20px;
  background-color: rgba(46, 139, 87, 0.05);
  border-bottom: 1px solid rgba(46, 139, 87, 0.1);
}

.mat-card-title {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #2E8B57 !important;
  position: relative;
  padding-left: 12px;
}

.mat-card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #2E8B57;
  border-radius: 2px;
}

/* Estilos para a tabela de atividades */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.results-table {
  width: 100%;
  border-collapse: collapse;
}

.result-row {
  border-bottom: 1px solid #e0e0e0;
}

.result-row:last-child {
  border-bottom: none;
}

.result-row td {
  padding: 12px 8px;
  vertical-align: top;
}

.cell-label {
  font-weight: 500;
  color: #555;
  margin-right: 4px;
}

.user-cell {
  width: 20%;
}

.description-cell {
  width: 60%;
}

.date-cell {
  width: 20%;
  white-space: nowrap;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.load-more-button {
  background-color: rgba(46, 139, 87, 0.1);
  color: #2E8B57;
  font-weight: 500;
  padding: 8px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #2E8B57;
}

.load-more-button:hover {
  background-color: rgba(46, 139, 87, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(46, 139, 87, 0.1);
}

/* Estilos para os cartões de padrões */
.patterns-grid {
  margin-top: 20px;
  max-height: 41vh;
  overflow: auto;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 10px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.pattern-row {
  width: 100%;
}

.pattern-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  transition: .3s;
}

.pattern-card:hover:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background-color: #2E8B57;
}

.pattern-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(46, 139, 87, 0.1);
}

.pattern-card-content {
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pattern-info {
  flex-grow: 1;
  padding-right: 10px;
}

.pattern-name {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 6px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pattern-details {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
}

.pattern-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #2E8B57;
}

.pattern-actions {
  display: flex;
  gap: 5px;
}

.pattern-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
}

.edit-button {
  color: #2E8B57;
}

.edit-button:hover {
  background-color: rgba(46, 139, 87, 0.1);
}

.delete-button {
  color: #dc2626;
}

.delete-button:hover {
  background-color: rgba(220, 38, 38, 0.1);
}

.empty-patterns {
  text-align: center;
  padding: 30px;
  border-radius: 12px;
  background-color: #f9fafb;
  grid-column: 1 / -1;
}

.empty-patterns-icon {
  font-size: 40px;
  color: #cbd5e1;
  margin-bottom: 10px;
}

.empty-patterns-text {
  color: #64748b;
  font-size: 15px;
}

/* Responsividade */
@media (max-width: 768px) {
  .result-row {
    display: flex;
    flex-direction: column;
    padding: 12px 0;
  }
  
  .result-row td {
    width: 100%;
    padding: 6px 8px;
  }
  
  .patterns-grid {
    grid-template-columns: 1fr;
  }
}
.w-100{
 width: 100%;
 justify-content: center;
 display: flex;
 grid-column: 3 span;
  .btn-primary{
  background-color: $primary-color !important;
 }
}