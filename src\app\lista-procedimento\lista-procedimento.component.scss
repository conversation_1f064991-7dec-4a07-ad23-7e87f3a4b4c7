// :host{
//     display: flex;
//     flex-direction: column;
//     flex-wrap: wrap;
//     height: 100%;
//     gap: 5px;
//     justify-content: normal;
//     overflow: hidden;
// }
.mother-div {
    margin: 0 !important;
    
    
    box-shadow: 0 7px 25px rgba(0,0,0,0.20)!important;
    border: none !important;
    border-radius: 10px !important;
    padding: 15px;
}

ngx-smart-modal button{
    outline: 0px;
    }
    

section{
    height: 100%;
    width: 100%;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
}

main{
    flex: 1;
    height: 100%;
    padding: 5px 0px;
    overflow-y: auto;
}

footer{
    flex-shrink: 0;
}

header{
    width: 100%;
}

.Con<PERSON>udoCabecalho{
    flex: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 5px;
    justify-content: space-between;

    .CaixaMatInput{
        mat-form-field{
            height: 45px !important;
        }
    }
    
    .CaixaBtn{
        align-content: end;

        button{
            padding: 8px;
            justify-self: end;
            width: 100px;
            border: none;
            background-color: #5260ff;
            color: #fff;
            border-radius: 5px;
            height: 40px;
            flex-flow: row wrap;
            border-collapse: collapse; 
            font-size: 14px;
        }

        button:hover{
            background-color:#4750af;
            color: #fff;
    }
    }
}



.TabelaPadrao {
    width: 100%;
    border-radius: 10px;
    height: 100%;
    overflow-y: auto;
    border: 1px solid rgb(236, 236, 236);
    box-shadow: 0 7px 25px rgba(0,0,0,0.08);

    table{
        width: 100%;
        border-radius: 10px; 
        border-collapse: inherit;
    };

    .Cabecalho{
        white-space: pre;
        background-color: #5260ff;
        color: #fff ;
        font-weight: 500;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;

        th{
            height: 40px;
            align-content: center;
            
            .CaixaHeader{
                display: flex;
                align-items: center;

                .BtnOrdenar{
                    color: #fff;
                    background-color:  #5260ff;
                    border: none;
                    cursor: pointer;
                    border-radius: 5px;
                    align-content: center;
                    
                    .IconeOrdenar{
                        font-size: 28px;
                    }
                }         
            }

            span{
                font-family: sans-serif; 
                font-weight: 600; 
                font-size: 15px;
            }
        };
        

        .HeaderLeft{
            border-top-left-radius: 5px;
            text-align: start;
        };

        .Header{
            max-width: 400px;  
            text-align: center;
        };

        .HeaderRight{
            border-top-right-radius: 5px
        };
    }

    .Corpo{
        td{
            height: 40px;
            align-content: center;
            max-width: 300px;
            font-size: 12px;
            text-wrap: wrap;
            overflow: hidden;
            text-overflow: ellipsis; 
            white-space: nowrap;
        };

        .Bold{
            font-weight: 500;
            font-size: 14;
        }

        .TextoCorpo{
            text-align: center;
        };

        .CaixaAcoes{
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 10px;

            .BtnAcao{
                cursor: pointer;
                display: flex; 
                flex-direction: row; 
                justify-content: center; 
                flex-wrap: wrap;

                .IconeAcao{
                    text-align: center; 
                    flex: 100%; 
                    height: 20px;

                    mat-icon{
                        font-size: 20px
                    }
                }

                .TextoAcao{
                    text-align: center; 
                    font-size: 10px; 
                    margin:  0px, 5px, 0px, 5px; 
                    flex: 100%; 
                }

            };
            
            .BtnAcao:hover{
                transform: scale(1.2);
                transition: 0.5s;
            };

            .Toogle{
                font-size: 14px; 
                cursor: pointer; 
                text-align: center; 
                display: flex; 
                justify-content: center; 
                align-items: center; 
                
            }
        }



    }


}
.TabelaPadrao::-webkit-scrollbar {
    width: 8px;
}

.TabelaPadrao::-webkit-scrollbar-thumb:vertical {
    background-color:  #5260ff;
    border-radius: 6px;
}

