<div class="container">
  <mat-card class="card-principal">
    <div class="header">
      <div class="header-left">
        <div class="header-icon">
          <mat-icon class="header-icon-material">queue</mat-icon>
        </div>
        <div class="header-content">
          <h2 class="header-title">Fila de Espera - Médico</h2>
          <p class="header-subtitle">Gerencie os pacientes aguardando atendimento</p>
        </div>
      </div>
      <div class="header-actions">
        <div class="status-indicator" [class.connected]="isSignalRConnected" [class.disconnected]="!isSignalRConnected">
          <mat-icon>{{isSignalRConnected ? 'wifi' : 'wifi_off'}}</mat-icon>
          <span class="status-text">{{isSignalRConnected ? 'Conectado' : 'Desconectado'}}</span>
        </div>
        <div class="header-icon" 
          [matTooltip]="autoRefresh ? 'Desativar atualização automática' : 'Ativar atualização automática'"
          (click)="toggleAutoRefresh()" [class.active]="autoRefresh">
          <mat-icon>{{autoRefresh ? 'sync' : 'sync_disabled'}}</mat-icon>
        </div>
      </div>
    </div>

    <div class="controles-section">
      <div class="estatisticas-container">
        <div class="estatistica-item primary">
          <div class="estatistica-icon">
            <mat-icon>people</mat-icon>
          </div>
          <div class="estatistica-info">
            <span class="estatistica-numero">{{totalPacientes}}</span>
            <span class="estatistica-label">Pacientes na fila</span>
          </div>
        </div>

        <div class="estatistica-item secondary">
          <div class="estatistica-icon">
            <mat-icon>schedule</mat-icon>
          </div>
          <div class="estatistica-info">
            <span class="estatistica-numero">{{ultimaAtualizacao | date:'HH:mm'}}</span>
            <span class="estatistica-label">Última atualização</span>
          </div>
        </div>

        <div class="estatistica-item tertiary" *ngIf="totalPacientes > 0">
          <div class="estatistica-icon">
            <mat-icon>person</mat-icon>
          </div>
          <div class="estatistica-info">
            <span class="estatistica-numero">{{proximoPaciente?.nomePaciente || 'N/A'}}</span>
            <span class="estatistica-label">Próximo paciente</span>
          </div>
        </div>
      </div>

      <div class="acoes-principais">

        <button mat-stroked-button class="btn-atualizar" (click)="carregarFilaEspera()" [disabled]="loading">
          <mat-icon>refresh</mat-icon>
          <span>Atualizar</span>
        </button>

        <button mat-stroked-button class="btn-limpar" (click)="limparFila()"
          [disabled]="loading || totalPacientes === 0" color="warn">
          <mat-icon>clear_all</mat-icon>
          <span>Limpar Fila</span>
        </button>
      </div>
    </div>

    <div class="loading-container" *ngIf="loading">
      <mat-spinner diameter="50"></mat-spinner>
      <p class="loading-text">Carregando fila de espera...</p>
    </div>

    <div class="fila-container" *ngIf="!loading">
      <div class="fila-header">
        <div class="fila-header-left">
          <h3 class="fila-titulo">
            <mat-icon>list</mat-icon>
            Pacientes na Fila
          </h3>
          <div class="fila-badge" *ngIf="totalPacientes > 0">
            <span class="badge-count">{{totalPacientes}}</span>
          </div>
        </div>
        <div class="fila-info">
          <span class="total-registros">
            {{totalPacientes}} paciente(s) aguardando
          </span>
        </div>
      </div>

      <div class="pacientes-scroll" *ngIf="totalPacientes > 0">
        <div class="paciente-card" *ngFor="let paciente of pacientesFila; let i = index"
          [class.primeiro-da-fila]="i === 0" [class.paciente-prioritario]="i < 3">

          <div class="posicao-container">
            <div class="posicao-numero" [class.destaque]="i === 0" [class.prioritario]="i < 3 && i > 0">
              {{paciente.posicaoFila || (i + 1)}}
            </div>
            <div class="posicao-label">{{i === 0 ? 'Próximo' : i === 1 ? '2º lugar' : i === 2 ? '3º lugar' : 'Posição'}}
            </div>
          </div>

          <div class="paciente-info">
            <div class="paciente-header">
              <div class="paciente-nome">
                <mat-icon>person</mat-icon>
                <span class="nome-texto">{{paciente.nomePaciente || 'Nome não informado'}}</span>
              </div>
              <div class="paciente-status" *ngIf="i === 0">
                <span class="status-badge proximo">PRÓXIMO</span>
              </div>
            </div>

            <div class="paciente-detalhes">
              <div class="detalhe-item">
                <mat-icon>cake</mat-icon>
                <span>{{paciente.idadePaciente || 'N/A'}} anos</span>
              </div>

              <div class="detalhe-item tempo-espera">
                <mat-icon>schedule</mat-icon>
                <span>Aguardando: <strong>{{formatarTempoEspera(paciente.tempoEspera)}}</strong></span>
              </div>

              <div class="detalhe-item" *ngIf="paciente.HoraEntrada">
                <mat-icon>access_time</mat-icon>
                <span>Entrada: {{paciente.HoraEntrada | date:'HH:mm'}}</span>
              </div>
            </div>
          </div>

          <div class="paciente-acoes">
            <!-- <button mat-mini-fab class="btn-dados"
              matTooltip="Visualizar dados do paciente"
              [disabled]="loadingConvite"
              (click)="visualizarDadosPaciente(paciente)"
              color="accent">
              <mat-icon>assignment</mat-icon>
            </button> -->

            <button mat-fab class="btn-convidar" [class.loading]="loadingConvite"
              [matTooltip]="loadingConvite ? 'Processando convite...' : 'Convidar para reunião e iniciar consulta'"
              [disabled]="loadingConvite" (click)="convidarParaReuniao(paciente)">
              <mat-icon *ngIf="!loadingConvite">video_call</mat-icon>
              <mat-spinner *ngIf="loadingConvite" diameter="24"></mat-spinner>
            </button>
            <span class="acao-label">Iniciar Consulta</span>
          </div>
        </div>
      </div>

      <div class="fila-vazia" *ngIf="totalPacientes === 0">
        <div class="vazia-content">
          <h3>Nenhum paciente na fila</h3>
          <p>A fila de espera está vazia no momento. Os pacientes aparecerão aqui quando se conectarem.</p>
          <div class="vazia-actions">
            <button mat-raised-button class="btn-atualizar-vazio" (click)="carregarFilaEspera()">
              <mat-icon>refresh</mat-icon>
              Verificar Novamente
            </button>
          </div>
        </div>
      </div>
    </div>
  </mat-card>
</div>