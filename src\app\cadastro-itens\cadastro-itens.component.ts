import { ItemService } from './../service/Item.Service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LocalStorageService } from '../service/LocalStorageService';
import { msgResposta } from '../model/retorno-resposta';
import { novoItem } from '../model/itens';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatCard } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { TranslatePipe } from '@ngx-translate/core';


@Component({
    selector: 'app-cadastro-itens',
    templateUrl: './cadastro-itens.component.html',
    styleUrls: ['./cadastro-itens.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatFormFieldModule,
      MatIcon,
      MatCard,
      TranslatePipe
    ]
})
export class CadastroItensComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private ItemService: ItemService,
    private localStorageService: LocalStorageService,
    private router: Router,
  ) { }
  idItem!: number;
  retorno!: msgResposta;
  listaItens: novoItem[] = [];
  flgmodal: boolean = false;
  flginativos: boolean = false;
  flgFoto: boolean = false;
  ImagemPessoa: any = "assets/build/img/userdefault.png";

  ngOnInit() {
    this.GetItens();
  }

  GetItens() {
    this.ItemService.GetItens().subscribe((retorno) => {
      this.listaItens = retorno;
      this.spinner.hide();
    }, erro => {
      console.error(erro)
      this.spinner.hide();
    })
  }

  excluirItem(idItem: number) {
    this.idItem = idItem;
    this.flgmodal = true;
  }

  Excluir(selecao: boolean) {
    this.flgmodal = false;
    if (selecao) {
      this.ItemService.deleteItem(this.idItem).subscribe((retorno) => {
        this.GetItens();
        this.retorno = retorno;
        this.spinner.hide();
      }, erro => {
        console.error(erro)
        this.spinner.hide();
      })
    }
  }

  Editar(idItem: number) {
    this.localStorageService.idItem = idItem;
    this.adicionaritens();
  }

  adicionaritens() {
    this.router.navigate(['/adicionaritens']);
  }

  filtroBusca: string = '';

  filtrarItem() {
    if (this.filtroBusca.trim() !== '') {
      this.listaItens = this.listaItens.filter(item => {
        return (
          item.nomeItem!.toLowerCase().includes(this.filtroBusca.toLowerCase())
        );
      });
    } else {
      this.GetItens();
    }
  }
}
