import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit, ViewChild } from '@angular/core';

import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';

import { CidadeService } from '../service/cidade.service';
import { FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Atendente } from '../model/atendente';
import { Endereco } from '../model/endereco';
import { Contato } from '../model/contato';
import { UsuarioService } from '../service/usuario.service';
import { AtendenteService } from '../service/atendente.service';
import { ClinicaService } from '../service/clinica.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ImageCropperComponent, ImageCroppedEvent, ImageTransform } from 'ngx-image-cropper';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { Pessoa } from '../model/pessoa';
import { ValidadoreseMascaras } from '../Util/validadores';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { UfClass } from '../Util/UFClass';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import {MatTooltipModule} from '@angular/material/tooltip';
type CalendarPeriod = 'day' | 'week' | 'month';

// function subPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
//   return {
//     day: subDays,
//     week: subWeeks,
//     month: subMonths
//   }[period](date, amount);
// }


@Component({
    selector: 'app-cadastro-usuario',
    templateUrl: './cadastro-usuario.component.html',
    styleUrls: ['./cadastro-usuario.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIconModule,
      TranslateModule,
      MatFormFieldModule,
      NgSelectModule,
      MatSelectModule,
      TruncatePipe,
      NgxSmartModalModule,
      MatTooltipModule
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CadastroUsuarioComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private cidadeService: CidadeService,
    private atendenteService: AtendenteService,
    // private pacienteService: PacienteService,
    private usuarioService: UsuarioService,
    // private validacaoService: ValidacaoService,
    // public snackBar: MatSnackBar,
    // private router: Router,
    public ngxSmartModalService: NgxSmartModalService,
    private clinicaService: ClinicaService,
    // private spinner: NgxSpinnerService, 
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private localStorageService: LocalStorageService,
    private snackbarAlert: AlertComponent,
  ) { }



  @ViewChild(ImageCropperComponent) imageCropper?: ImageCropperComponent;
  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;



  view: CalendarPeriod = 'month';
  viewDate: Date = new Date();
  ImagemPessoa: any = "assets/build/img/userdefault.png";
  fileToUpload?: File;
  showMessageError = false;
  showMessageSuccess = false;
  Dados: any;
  dadosUF = UfClass;
  dadosUFCarregaBanco: any;
  dadosCidade: any;
  dadosCidadeUf: any;
  errors = [];
  DadosUsuario: any;
  // usuario: Usuario;
  tipoUsuario: any;
  retornoAtendente: any;
  retornoPessoa: any;
  retornoEndereco: any;
  retornoContato: any;
  retornoUsuario: any;
  Dtanasc?: boolean;
  TelVal?: boolean;
  TelMovVal?: boolean;
  TelMovValVasil = false;
  TelMovLimpa = true;
  TelComVal?: boolean;
  clinicaVasil = true;
  clinicaVal?: boolean;
  clinicas: any = new FormControl([]);
  DadosClinicas: any = [];
  usuarioInvalidoEmail = false;
  usuarioInvalidoTelefone?: boolean;

  imageChangedEvent: any = '';
  croppedImage: any = '';

  showCropper = false;
  imagemCorte: any;

  DadosSexo:any = [];
  campoSexo?: string | null;
  fem:any;
  masc:any;

  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;

  ngOnInit() {
    this.Dados = [];
    var idAtendente = this.localStorageService.idAtendente;
    this.localStorageService.clearByName("idAtendente");

    if (idAtendente)
      this.CarregaAtendente(idAtendente);
    else
      this.CarregaClinicas();

  }

  carregaDadosSexo() {

    if (this.DadosSexo.length == 0) {
      this.tradutor.get('TELACADASTROUSUARIO.MASCULINO').subscribe((res: string) => {
        this.masc = res;
      });
      this.tradutor.get('TELACADASTROUSUARIO.FEMININO').subscribe((res: string) => {
        this.fem = res
      });
      this.DadosSexo = [this.masc, this.fem]
    }
  }


  CarregaClinicas() {
    try {
      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {
        this.DadosClinicas = retornaClinicas
        
        
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    } catch (error) {
      console.error(error)
      this.spinner.hide();
    }
  }

  public mascaraText(evento: KeyboardEvent, campo: string) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\d/g, "");

    if (campo == 'Nome')
      this.Dados.nome = v;
    if (campo == 'clinicas')
      (<HTMLInputElement>evento.target).value = v
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    (<HTMLInputElement>evento.target).value = v
  }


  AlterarImagemPessoa($event:any): void {
    this.readThis($event.target);
  }
  readThis(inputValue: any): void {
    var file: File = inputValue.files[0];
    var myReader: FileReader = new FileReader();

    myReader.onloadend = () => {
      this.ImagemPessoa = myReader.result;
    }
    myReader.readAsDataURL(file);
    // ;
  }


  public CidadePorUF() {
    try {

      this.cidadeService.getCidades().then((retornaCidade) => {
        this.dadosCidade = retornaCidade;
        this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == this.Dados.uf);
      }, () => {

        this.tradutor.get('TELACADASTROUSUARIO.ERROAOCARREGARCIDADE').subscribe((res: string) => {
          ;
          this.snackbarAlert.falhaSnackbar(res);
        });
        
      })
      this.spinner.hide();

    } catch (error) {

      this.tradutor.get('TELACADASTROUSUARIO.ERROAOCARREGARCIDADE').subscribe((res: string) => {
        ;
        this.snackbarAlert.falhaSnackbar(res);
      });
      this.spinner.hide();
    }
  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }


  idUsuarioExistente: any;
  mensagemPaciente?: string;
  campoExitente?: string;
  public AceitarUsuarioExistente() {

    if (this.idUsuarioExistente.atendente)
      this.CarregaAtendente(this.idUsuarioExistente.atendente.idAtendente)
    else {
      this.retornoPessoa = this.idUsuarioExistente.pessoa;
      this.retornoEndereco = this.idUsuarioExistente.endereco;
      this.retornoContato = this.idUsuarioExistente.contato;

      this.Dados.nome = this.retornoPessoa.nomePessoa;
      this.Dados.dtaNascimento = this.retornoPessoa.dtaNascimento ? new Date(this.retornoPessoa.dtaNascimento).toLocaleDateString() : "";
      this.Dados.cpf = this.retornoPessoa.cpf;

      this.carregaDadosSexo()
      if (this.retornoPessoa.sexo == "Masculino") {
        this.campoSexo = this.masc
      } else if (this.retornoPessoa.sexo == "Feminino") {
        this.campoSexo = this.fem
      } else
        this.campoSexo = null

      if (this.retornoContato != null) {
        if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefone = this.retornoContato.telefone;

        if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

        if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;

      }

      this.Dados.email = this.retornoPessoa.email;

      if (this.retornoEndereco) {
        this.Dados.rua = this.retornoEndereco.rua
        this.Dados.numero = this.retornoEndereco.numero
        this.Dados.complemento = this.retornoEndereco.complemento
        this.Dados.cep = this.retornoEndereco.cep
        this.Dados.bairro = this.retornoEndereco.bairro


        if (this.retornoEndereco.idCidade != null) {
          this.cidadeService.getCidades().then((retornaCidade) => {
            this.dadosCidade = retornaCidade
            var sigle = this.dadosCidade.filter((c:any) => c.idCidade == this.retornoEndereco.idCidade)
            this.Dados.uf = sigle[0].siglasUf

            this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == sigle[0].siglasUf);
            this.Dados.idCidade = this.retornoEndereco.idCidade
            this.spinner.hide();
          }, () => {

            this.tradutor.get('TELACADASTROUSUARIO.ERRONORETORNOCIDADEPORUF').subscribe(() => {});
            this.spinner.hide();
          })
        }
      }

      this.Dados.foto = this.retornoPessoa.foto;
      if (this.idUsuarioExistente.imagem64 != null && this.idUsuarioExistente.imagem64 != "")
        this.ImagemPessoa = this.idUsuarioExistente.imagem64;


      if (this.idUsuarioExistente.clinicas && this.idUsuarioExistente.clinicas.length > 0) {

        this.clinicas = [];
        this.idUsuarioExistente.clinicas.forEach((element:any) => {
          this.DadosClinicas.forEach((elemento:any) => {
            if (elemento.idClinica == element.idClinica)
              this.clinicas.push(elemento)
          })
        })
        this.clinicas = new FormControl(this.clinicas);
      }
    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();
  }
  public NaoAceitarUsuarioExistente() {
    if (this.campoExitente == 'CPF') {
      this.Dados.cpf = ""
      this.campoExitente = ""
    }
    else if (this.campoExitente == 'EMAIL') {
      this.Dados.email = ""
      this.campoExitente = ""
    }
    else {
      this.Dados.telefoneMovel = ''
      this.campoExitente = ""

    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();

  }

  public validarCpf(value:any) {
    var valuecpf = value.replace(/[\.-]/g, "");
    this.campoCPFInvalido = false;
    var idUsuario = null
    if (this.retornoPessoa != undefined)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {

      this.campoCPFVazil = false;

      if (!this.validacao.cpf(value)) {
        this.campoCPFInvalido = true;
        return;
      }
      this.campoCPFInvalido = false;

      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(valuecpf, idUsuario, 'CPF').subscribe((retorno) => {

        if (retorno != null) {
          this.campoExitente = "CPF"
          this.mensagemPaciente = 'CPF já registrado no sistema Deseja utilizar o atendente desse cadastro?';
          this.idUsuarioExistente = retorno;
          this.ngxSmartModalService.getModal('UsuarioExistente').open();

          // this.CarregaAtendente(retorno.iduser)

          // this.tradutor.get('TELACADASTROUSUARIO.USUARIOJAREGISTRADOVERIFIQUEACLINICA').subscribe((res: string) => {
          //   ;
          //   this.AlgumErro(true, res);
          // });
        }
      })
    }
    else
      this.campoCPFVazil = true;
  }

  campoEmailInvalido = false;
  campoEmailVazil = false;
  public validarEmail(value:any) {
    var idUsuario
    this.campoEmailInvalido = false;
    this.usuarioInvalidoEmail = false;
    if (this.retornoPessoa != undefined)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {
      this.campoEmailVazil = false;
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        return
      }
      this.campoEmailInvalido = false
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'EMAIL').subscribe((retorno) => {
        ;
        if (retorno != null) {
          this.campoExitente = "EMAIL"
          this.mensagemPaciente = 'Email já registrado no sistema Deseja utilizar o atendente desse cadastro?';
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();

          // this.usuarioInvalidoEmail = true;

          // this.tradutor.get('TELACADASTROUSUARIO.EMAILJAREGISTRADO').subscribe((res: string) => {
          //   ;
          //   this.AlgumErro(true, res);
          // });
        }
      })
    }
    else
      this.campoEmailVazil = true;
  }



  public validarTelMovel(value:any) {
    var idUsuario
    this.usuarioInvalidoTelefone = false;
    if (this.retornoPessoa != undefined && this.retornoPessoa != '' && this.retornoPessoa != null)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'TelMovel').subscribe((retorno) => {
        ;
        if (retorno != null) {
          this.campoExitente = "Tel"
          this.mensagemPaciente = 'Telefone já registrado no sistema Deseja utilizar o atendente desse cadastro?';
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();

          // this.usuarioInvalidoTelefone = true;

          // this.tradutor.get('TELACADASTROUSUARIO.TELEFONEJAREGISTRADO').subscribe((res: string) => {
          //   ;
          //   this.AlgumErro(true, res);
          // });
        }
      })
    }
  }


  public validarTelMovelChange() {
    var value = (document.getElementById('telcelular')as HTMLInputElement)['value'];
    var idPessoa
    this.usuarioInvalidoTelefone = false;
    if (this.retornoPessoa != undefined && this.retornoPessoa != '' && this.retornoPessoa != null)
      var idPessoa = this.retornoPessoa.idPessoa
    if (value != "") {

      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idPessoa, 'TelMovel').subscribe((retorno) => {
        ;

        if (retorno != null) {
          this.usuarioInvalidoTelefone = true;

          this.idUsuarioExistente = retorno;
          this.tradutor.get('TELACADASTROUSUARIO.TELEFONEJAREGISTRADO').subscribe((res: string) => {
            ;
            this.snackbarAlert.sucessoSnackbar(res);
          });
        }
      })
    }
  }



  ValidaDtaChange() {
    const dta = (document.getElementById('DtaNasc')as HTMLInputElement)['value'];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.Dtanasc = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }

  ValidaDta(dta:any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.Dtanasc = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }

  ValidaTelefone(tle:any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle:any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelMovVal = false;
      this.TelMovValVasil = true;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovVal = false;
      this.TelMovValVasil = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
      this.TelMovValVasil = false;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneComercial(tle:any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
    }
    else
      this.TelComVal = false
  }

  ValidaClinicas(cli:any = []) {
    this.clinicas.value.forEach((element:any) => {

      cli.push({
        idClinica: element.idClinica,
      })
    });
    if (cli.length > 0) {
      this.clinicaVasil = false;
      this.clinicaVal = false;
    }
    else {
      this.clinicaVasil = false;
      this.clinicaVal = true;
    }
  }

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraCpf(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 14) {

      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 11) {
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }

  NaoEditado(numeroTelefone:any) {
    if (numeroTelefone != null && numeroTelefone != '' && numeroTelefone != undefined)
      return numeroTelefone.replace(/\D/g, "");

    else return
  }

  LimparCampos() {

    this.clinicas = new FormControl([])
    this.Dados = [];
    this.campoSexo = null
    this.retornoPessoa = null

    this.ImagemPessoa = "assets/build/img/userdefault.png";
    (document.getElementById("DtaNasc") as HTMLInputElement)['value'] = ""
    this.Nome.markAsUntouched();
    this.email.markAsUntouched();
    this.TelVal = false;
    this.TelMovVal = false;
    this.TelMovValVasil = false;
    this.TelMovLimpa = false;
    this.TelComVal = false;
    this.Dtanasc = false;
    this.clinicaVasil = true;
    this.clinicaVal = false;

    this.campoEmailInvalido = false;
    // this.DtaNasc.markAsUntouched();

  }


  public validarCampos() {
    this.showMessageError = false;
    this.Nome.markAsTouched();
    this.email.markAsTouched();
    this.ValidaTelefoneMovel((document.getElementById("telcelular")as HTMLInputElement)['value']);
    this.DtaNasc.markAsTouched();

    if (!this.validacao.cpf(this.Dados.cpf)) {
      this.campoCPFInvalido = true;
      return;
    }
    if (this.Dados.email == undefined || !this.Dados.email.trim())
      this.campoEmailVazil = true;


    if (this.Dados.cpf == undefined || !this.Dados.cpf.trim())
      this.campoCPFVazil = true;

    if (this.Dados.nome == undefined || !this.Dados.nome.trim()
      || this.Dados.email == undefined || !this.Dados.email.trim()
      || this.Dados.cpf == undefined || !this.Dados.cpf.trim()
      || this.Dados.telefoneMovel == undefined || !this.Dados.telefoneMovel.trim()
      || this.Dtanasc == true
      || this.TelMovVal == true
      || this.TelVal == true || this.TelComVal == true
      || this.clinicaVal == true || this.clinicaVasil == true || this.campoEmailInvalido == true
      || this.campoEmailVazil == true || this.campoCPFInvalido == true || this.campoCPFVazil == true
    ) {

      this.showMessageError = true;

      if (this.TelMovVal != true && this.TelMovVal != false || this.TelMovLimpa == false) {
        this.TelMovValVasil = true;
        this.TelMovLimpa = true;
      }
      if (this.clinicaVal != true && this.clinicaVal != false) {
        this.clinicaVasil = true;
      }
      if (this.clinicaVasil == true) {
        this.clinicaVal = true;
      }
      document.documentElement.scrollTop = 0;
    }

  }
  Nome = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  DtaNasc = new FormControl('', [Validators.required, Validators.maxLength(10)])
  planos = new FormControl('', [Validators.required, Validators.maxLength(11)])
  sexo = new FormControl('', [Validators.required, Validators.maxLength(11)])
  convenio = new FormControl('', [Validators.required, Validators.maxLength(11)])
  mat = new FormControl('', [Validators.required, Validators.maxLength(11)])


  getErrorMessageNome() {
    this.showMessageSuccess = true;
    return this.Nome.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.Nome.hasError('Nome') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDO' :
        '';

  }


  getErrorMessageMat() {
    return this.mat.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.mat.hasError('Matrícula') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDA' :
        '';

  }


  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.email.hasError('email') ? 'TELACADASTROUSUARIO.ERROEMAIL' :
        '';

  }

  // geterrorMessageDate() {
  //   return this.dtaNascimento.hasError('required') ? 'Esse campo precisa ser preenchido' :
  //     this.dtaNascimento.hasError('Data de Nascimento') ? 'Não é valido' :
  //       '';
  // }

  geterrorMessagePlanoS() {
    return this.planos.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.planos.hasError('Plano de Saúde') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDO' :
        '';
  }

  geterrorMessageSexo() {
    return this.sexo.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.sexo.hasError('Sexo') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDO' :
        '';
  }

  geterrorMessageConv() {
    return this.convenio.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.convenio.hasError('Convênio') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDO' :
        '';
  }




  public CarregaAtendente(id:any) {
    try {

      this.atendenteService.getAtendente(id).subscribe((retorno) => {
        
        

        this.retornoAtendente = retorno.atendente;
        this.retornoPessoa = retorno.pessoa;
        this.retornoEndereco = retorno.endereco;
        this.retornoContato = retorno.contato;
        this.retornoUsuario = retorno.usuario;

        this.Dados.nome = this.retornoPessoa.nomePessoa;
        this.Dados.dtaNascimento = this.retornoPessoa.dtaNascimento ? new Date(this.retornoPessoa.dtaNascimento).toLocaleDateString() : "";
        this.Dados.cpf = this.retornoPessoa.cpf;

        this.carregaDadosSexo()
        if (this.retornoPessoa.sexo == "Masculino") {
          this.campoSexo = this.masc
        } else if (this.retornoPessoa.sexo == "Feminino") {
          this.campoSexo = this.fem
        } else
          this.campoSexo = null

        if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefone = this.retornoContato.telefone;

        if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

        if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;

        this.Dados.email = this.retornoPessoa.email;

        if (this.retornoEndereco) {
          this.Dados.rua = this.retornoEndereco.rua
          this.Dados.numero = this.retornoEndereco.numero
          this.Dados.complemento = this.retornoEndereco.complemento
          this.Dados.cep = this.retornoEndereco.cep
          this.Dados.bairro = this.retornoEndereco.bairro


          if (this.retornoEndereco.idCidade != null) {
            this.cidadeService.getCidades().then((retornaCidade) => {
              this.dadosCidade = retornaCidade
              var sigle = this.dadosCidade.filter((c:any) => c.idCidade == this.retornoEndereco.idCidade)
              this.Dados.uf = sigle[0].siglasUf

              this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == sigle[0].siglasUf);
              this.Dados.idCidade = this.retornoEndereco.idCidade
              this.spinner.hide();
            }, () => {
              this.tradutor.get('TELACADASTROUSUARIO.ERRONORETORNOCIDADEPORUF').subscribe(() => {});
              this.spinner.hide();
            })
          }
        }

        this.Dados.foto = this.retornoPessoa.foto;
        if (retorno.imagem64 != null && retorno.imagem64 != "")
          this.ImagemPessoa = retorno.imagem64;

        this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {
          this.DadosClinicas = retornaClinicas

          if (retorno.clinicas && retorno.clinicas.length > 0) {

            this.clinicas = [];
            retorno.clinicas.forEach((element:any) => {
              this.DadosClinicas.forEach((elemento:any) => {
                if (elemento.idClinica == element.idClinica)
                  this.clinicas.push(elemento)
              })
            })
            this.clinicas = new FormControl(this.clinicas);
          }
          this.spinner.hide();
        }, () => {
          console.error("erro no retorno especialidade")
          this.spinner.hide();
        })


        this.spinner.hide();

      }, () => {

        this.tradutor.get('TELACADASTROUSUARIO.ERROAOCARREGARUSUARIO').subscribe((res: string) => {
          ;

          this.snackbarAlert.falhaSnackbar(res);
        });
        this.spinner.hide();
      })
    } catch (error) {

      this.spinner.hide();
      this.tradutor.get('TELACADASTROUSUARIO.ERROAOCARREGARUSUARIO').subscribe((res: string) => {
        ;
        this.snackbarAlert.falhaSnackbar(res);
      });
    }
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }





  public OnSubmit() {

    this.ValidaDtaChange();
    this.validarTelMovelChange();
    this.ValidaClinicas();
    this.validarCampos();
    if (this.showMessageError) {
      return;
    }
    var pessoa = new Pessoa();
    var atendente = new Atendente();
    var endereco = new Endereco();
    var contato = new Contato();



    if (this.retornoPessoa) {
      if (this.retornoAtendente != null) {
        atendente.idAtendente = this.retornoAtendente.idAtendente;
        atendente.idPessoa = this.retornoPessoa.idPessoa;

        atendente.idUsuarioGerador = this.retornoAtendente.idUsuarioGerador;
        atendente.dtaCadastro = this.retornoAtendente.dtaCadastro;
      }

      if (this.retornoPessoa.idEndereco != null) {
        endereco.idEndereco = this.retornoPessoa.idEndereco;
        pessoa.idEndereco = this.retornoPessoa.idEndereco;

      }
      pessoa.idPessoa = this.retornoPessoa.idPessoa;
      pessoa.idContato = this.retornoPessoa.idContato;
      pessoa.idUsuarioAcesso = this.retornoPessoa.idUsuarioAcesso;
      if (this.retornoPessoa.idContato != null)
        contato.idContato = this.retornoPessoa.idContato;
      contato.idUsuarioGerador = this.retornoContato.idUsuarioGerador;
      endereco.dtaCadastro = this.retornoPessoa.dtaCadastro;
      contato.dtaCadastro = this.retornoContato.dtaCadastro;
      pessoa.dtaCadastro = this.retornoPessoa.dtaCadastro;
    }
    else {
      pessoa.idUsuarioGerador = contato.idUsuarioGerador = atendente.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    }

    pessoa.nomePessoa = this.Dados.nome;
    pessoa.dtaNascimento = (document.getElementById('DtaNasc')as HTMLInputElement)['value'] == '' ? null : this.validacao.Convertdata((document.getElementById('DtaNasc')as HTMLInputElement)['value']);
    pessoa.cpf = this.Dados.cpf;

    if (this.campoSexo == this.masc) {
      this.Dados.sexo = "Masculino"
    } else if (this.campoSexo == this.fem) {
      this.Dados.sexo = "Feminino"
    } else
      this.Dados.sexo = null

    pessoa.sexo = this.Dados.sexo;
    pessoa.foto = this.Dados.foto;
    if (this.ImagemPessoa != "assets/build/img/userdefault.png")
      pessoa.imagem64 = this.ImagemPessoa;


    contato.telefone = this.NaoEditado(this.Dados.telefone);
    contato.telefoneMovel = this.NaoEditado(this.Dados.telefoneMovel);
    contato.telefoneComercial = this.NaoEditado(this.Dados.telefoneComercial);
    pessoa.email = this.Dados.email;

    endereco.rua = this.Dados.rua
    endereco.numero = this.Dados.numero
    endereco.complemento = this.Dados.complemento
    endereco.cep = this.Dados.cep
    endereco.bairro = this.Dados.bairro
    endereco.idCidade = this.Dados.idCidade

    pessoa.endereco = endereco;
    pessoa.contato = contato;
    atendente.pessoa = pessoa;

    var Clinic:any = []
    this.clinicas.value.forEach((element:any) => {

      Clinic.push({
        idClinica: element.idClinica,
      })
    });

    if (Clinic.length > 0)
      atendente.clinicas = Clinic;

    //remover id

    this.usuarioService.salvarAtendente(atendente, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      
      if (retorno != true) {
        this.snackbarAlert.falhaSnackbar(retorno)
        
      }
      else {
        this.snackbarAlert.sucessoSnackbar('Cadastro realizado com sucesso!');
        this.DadosUsuario = []
        this.LimparCampos()

      }
      this.spinner.hide();

    }, () => {

      this.snackbarAlert.falhaSnackbar('Erro ao salvar Usuário');
      
      this.spinner.hide();
    })
  }

  // SalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELACADASTROUSUARIO.CADASTROSALVOCOMSUCESSO').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }


  // ErroSalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROUSUARIO.ERROAOSALVAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }



  AlterarImagemClinica($event:any): void {
    this.ngxSmartModalService.getModal('ModalFoto').open();
    this.imageChangedEvent = $event
    // this.readThis($event.target);
  }
  CortarImagem() {
    this.ngxSmartModalService.getModal('ModalFoto').close()
    this.ImagemPessoa = this.imagemCorte
  }

  LimpaCampoFile() {
    (document.getElementById('imageperfilusuario')as HTMLInputElement)['value'] = '';
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCorte = event.base64;
    ;
  }
  imageLoaded() {
    this.showCropper = true;
    
  }
  cropperReady() {
    
  }
  loadImageFailed() {
    ;
  }
  transform: ImageTransform = {
    rotate: 0,
    flipH: false,
    flipV: false
  };

  rotateLeft() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! - 90 };
  }

  rotateRight() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! + 90 };
  }

  flipHorizontal() {
    this.transform = { ...this.transform, flipH: !this.transform.flipH };
  }

  flipVertical() {
    this.transform = { ...this.transform, flipV: !this.transform.flipV };
  }

}
