<div class="item-container">
  <div class="card main-card">
    <div class="card-header">
      <div class="header-left">
        <div class="icon-container">
          <span class="material-icons">add_circle</span>
        </div>
        <h1 class="page-title">Adicionar Item</h1>
      </div>
      <button class="btn btn-link" onclick='history.go(-1)'>
        <span class="material-icons">arrow_back</span>
      </button>
    </div>

    <div class="card-body">
      <!-- Seção de dados do item -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Dados do Item</h2>
        </div>
        <div class="section-content">
          <div class="item-info">
            <div class="item-image">
              <label for="imageperfilusuario" class="image-upload-container">
                <img src="{{ ImagemPessoa }}" alt="Imagem do Item" class="item-img" />
                <div class="image-upload-overlay">
                  <span class="material-icons">photo_camera</span>
                </div>
              </label>
            </div>
            
            <div class="item-details">
              <div class="form-group full-width">
                <ng-select 
                  class="modern-select"
                  [items]="objTipoOrientacao" 
                  [(ngModel)]="objItem.idTipoOrientacao" 
                  placeholder="Tipo de orientação" 
                  [clearable]="false" 
                  [searchable]="false"
                  bindLabel="tipoOrientacao" 
                  bindValue="idTipoOrientacao">
                </ng-select>
              </div>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nome</mat-label>
                <input matInput placeholder="Nome do item" [(ngModel)]="objItem.nomeItem" type="text">
              </mat-form-field>
            </div>
            

            <mat-form-field class="col-md-6 col-sm-6 col-xs-6" appearance="outline"
              style="font-size: 12px; width: 100%; padding: 0 5px;">
              <mat-label><span>Site</span></mat-label>
              <input matInput placeholder="Site" [(ngModel)]="objItem.site" type="text">
            </mat-form-field>
          </div>

          <div style="padding: 0 5px;" id="perfil-forms">
            <textarea [(ngModel)]="strObservacao" style="width: 100%;"></textarea>

            <!-- <ckeditor [editor]="Editor" [(ngModel)]="strObservacao" [config]="config"></ckeditor> -->
            <!-- <ckeditor placeholder="Observações" [editor]="Editor" [(ngModel)]="objItem.observacoes" #obsEditor></ckeditor> -->
          </div>
          
          <div class="form-row">
            <div class="form-group full-width">
              <div class="editor-container">
                <ckeditor [editor]="Editor" [(ngModel)]="strObservacao" [config]="config"></ckeditor>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <button class="btn btn-success" (click)="Salvar()">
        <span class="material-icons">save</span>
        Salvar
      </button>
      <button class="btn btn-outline" (click)="Limpar()">
        <span class="material-icons">refresh</span>
        Limpar
      </button>
    </div>
  </div>
</div>