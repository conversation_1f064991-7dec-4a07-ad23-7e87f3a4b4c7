import { Component, OnInit } from '@angular/core';
import { ContasPagar } from 'src/app/model/contasPagar';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition} from '@angular/material/snack-bar';
import { FormControl, Validators, AbstractControl, FormGroup, FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ContasPagarReceberService } from 'src/app/service/contas-pagar-receber.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { InputDateComponent } from 'src/app/input-date/input-date.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-contas-pagar',
    templateUrl: './contas-pagar.component.html',
    styleUrls: ['./contas-pagar.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      InputDateComponent,
      MatCardModule,
      MatIcon,
      MatDivider,
      TranslateModule,
      MatFormFieldModule,
      MatSelectModule,
      NgxSmartModalModule,
    ]
})
export class ContasPagarComponent implements OnInit {
  observacoesConta: any;
  serieNotaFiscal: any;
  parcelas: any;
  notaFiscal: any;
  dataBaixa: any;
  descricaoConta: any;
  fornecedor: any;
  idUsuario: any;
  dataVencimento: any;
  DadosContasPagar?: any[];
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  actionButtonLabel: string = 'Fechar';
  action: boolean = true;
  showMessageError?: boolean;
  DtaErrado = false;
  idcontaPagar?: number;
  DescricaoVazia = false;
  listaParcelas = [{ valor: 1, descricao: "1x" }, { valor: 2, descricao: "2x" }, { valor: 3, descricao: "3x" },
  { valor: 4, descricao: "4x" }, { valor: 5, descricao: "5x" }, { valor: 6, descricao: "6x" }, { valor: 7, descricao: "7x" }, { valor: 8, descricao: "8x" }
    , { valor: 9, descricao: "9x" }, { valor: 10, descricao: "10x" }, { valor: 11, descricao: "11x" }, { valor: 12, descricao: "12x" }, { valor: 13, descricao: "13x" }
    , { valor: 14, descricao: "14x" }, { valor: 15, descricao: "15x" }, { valor: 16, descricao: "16x" }, { valor: 17, descricao: "17x" }, { valor: 18, descricao: "18x" }]

  constructor(
    private spinner: SpinnerService,    
    private tradutor: TranslateService,
    private formBuilder: FormBuilder,
    private contaPagarReceberService: ContasPagarReceberService,
    // public snackBar: MatSnackBar,
    public ngxSmartModalService: NgxSmartModalService,
    private snackBarAlert: AlertComponent,

  ) { }
  DadosPagamento:any = [];

  tipoPagamento: number = 0;

  listaFormaPagamento: any = [];

  isParcela = false;

  dtaVencimento: boolean = true;

  objContasPagar = new  ContasPagar();

  objContasPagarForm?: FormGroup;

  myGroup = new FormGroup({

    firstName: new FormControl()
  });


  // exibeMensagem(
  //   // Mensagem, TipoMensagem
  // ) {
  //   // let config = new MatSnackBarConfig();
  //   // config.verticalPosition = this.verticalPosition;
  //   // config.horizontalPosition = this.horizontalPosition;
  //   // config.duration = 1500;

  //   // if (TipoMensagem == "Sucesso")
  //   //   config.panelClass = ['success-snack'];
  //   // else if (TipoMensagem == "Erro")
  //   //   config.panelClass = ['error-snack'];
  //   this.snackBarAlert.sucessoSnackbar("Mensagem de confirmação enviada!");

  // }

  ngOnInit() {

    this.DadosContasPagar = [];

    this.GetTipoPagamento();

    this.ListarContaPagar();

    this.objContasPagarForm = this.formBuilder.group({
      Desc: ['', [Validators.required]],
      Valor: ['', [Validators.required, maiorQueZero]],
      NotaFiscal: [''],
      SerieNotaFiscal: [''],
      ObsConta: [''],
      NumParcelas: [''],
      formaPagamento: ['', [Validators.required]],
      fornecedor: [''],
      DataCad: ['', validaData],
      DataBx: ['', validaData],
      DataVcm: ['', validaData],

    })

    this.objContasPagar = new ContasPagar();

  }
  GetTipoPagamento() {

    this.contaPagarReceberService.GetTipoPagamento().subscribe((retorno) => {
      
      
      this.listaFormaPagamento = retorno
      this.spinner.hide();



    }, () => {

      
      this.spinner.hide();
    })
  }
  DeletarContaPagar() {

    this.contaPagarReceberService.DeletarContaPagar(this.idcontaPagar).subscribe((retorno) => {
      

      if (retorno) {
        this.LimparCampos();
        this.ListarContaPagar();
        this.ngxSmartModalService.getModal('excluirItem').close();
        this.snackBarAlert.sucessoSnackbar("Excluído com sucesso");

      }
      this.spinner.hide();

    }, () => {

      
      this.spinner.hide();
    })
  }


  AbrirModalExclusao(id:any) {

    this.idcontaPagar = id;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }

  EditarContaPagar(thiago:any) {

    this.contaPagarReceberService.EditarContaPagar(thiago).subscribe(() => {
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  ListarContaPagar() {

    this.contaPagarReceberService.ListarContaPagar().subscribe((retorno) => {

      ;
      ;
      this.DadosContasPagar = [];
      this.DadosContasPagar = retorno;
      this.spinner.hide();

    }, () => {

      
      this.spinner.hide();
    })
  }

  CarregarEdicaoContaPagar(idcontasPagar:any) {

    this.contaPagarReceberService.CarregarEdicaoContaPagar(idcontasPagar).subscribe((retorno: any) => {
      this.objContasPagar = new ContasPagar();
      this.objContasPagar.IdContasPagar = retorno.idcontasPagar;
      this.objContasPagar.DesConta = retorno.descricaoconta;
      this.objContasPagar.DtaVencimento = retorno.dtaVencimento ? new Date(retorno.dtaVencimento).toLocaleDateString() : "";
      this.objContasPagar.DtaBaixa = retorno.databaixa ? new Date(retorno.databaixa).toLocaleDateString() : "";
      this.objContasPagar.IdTipoPagamento = retorno.formapagamento;
      this.objContasPagar.IdFornecedor = retorno.fornecedor;
      this.objContasPagar.NotaFiscal = retorno.notafiscal;
      this.objContasPagar.ObsConta = retorno.obsconta;
      this.objContasPagar.NumParcelas = retorno.parcelas;
      this.objContasPagar.SerieNotaFiscal = retorno.serienotafiscal;
      this.objContasPagar.VlrConta = retorno.valor;
      this.spinner.hide();


    }, () => {
      this.spinner.hide();

    })

  }
  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }

  ValidaDta(dta:any) {
    this.DtaErrado = false;

    var min = new Date('01/01/ 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaErrado = true;
        return;
      }
      // else if (this.DtaErrado == false)
      //   this.CarregaConsultas()

    }
    return;

  }
  UploadArquivo() {

  }
  DownloadArquivo() {



  }

  teste() {
    
  }
  forma: number = 0;

  alterarFormaPagamento(item:any) {

    if (item == 1)
      this.isParcela = true;
    else
      this.isParcela = false;
  }
  async carregaFormaPagamento() {
    this.DadosPagamento = [];

    this.tradutor.get('TELAFINANCAS.AVISTACARTAO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.AVISTADINHEIRO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.PARCELADO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.BOLETO').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });
    this.tradutor.get('TELAFINANCAS.OUTROS').subscribe((res: string) => {
      this.DadosPagamento.push(res);
    });

    ;
    ;

  }

  LimparCampos() {

    this.objContasPagar = new ContasPagar;

  }

  public Submit() {

    if ( this.ValidaCampos()) {
      return;
    }

    

    // // this.objContasPagar.DtaVencimento = this.objContasPagar.DtaVencimento ?  this.validacao.Convertdata(this.objContasPagar.DtaVencimento) : null;
    // // this.objContasPagar.DtaBaixa = this.objContasPagar.DtaBaixa ?  this.validacao.Convertdata(this.objContasPagar.DtaBaixa) : null;
    // this.contaPagarReceberService.salvarContaPagar(this.objContasPagar).subscribe((retorno) => {
    //   ;

    //   if (retorno) {

    //     this.LimparCampos();
    //     this.ListarContaPagar();
    //     this.exibeMensagem("Salvo com sucesso", "Sucesso");
    //   }

    // }, err => {

    //   
    // })

  }
  ValidaCampos() : boolean {

var retorno = false;
    if (this.objContasPagar!.DesConta == undefined || !this.objContasPagar!.DesConta.trim())
        retorno = this.DescricaoVazia = true;
    if (this.objContasPagar!.IdTipoPagamento == undefined || !this.objContasPagar!.IdTipoPagamento == true)
        retorno =  this.DescricaoVazia = true;
    if (this.objContasPagar!.VlrConta == undefined || !this.objContasPagar!.VlrConta == true)
        retorno =  this.DescricaoVazia = true;

       return retorno;

  }
}

export function maiorQueZero(control: AbstractControl) {

  if (control.value > 0)
    return null;

  return { maiorZero: true }
}

export function validaData(control: AbstractControl) {

  var min = new Date('01/01/1900 12:00:00')
  var max = new Date('01/01/2100 12:00:00')

  var dtaAgora = new Date(control.value);

  if (control.value != "") {
    // var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

    // if (!patternValidaData.test(control.value)) {
    //   return { dataInorreta: true }
    // }
    if (dtaAgora < min || dtaAgora > max) {
      return { dataInorreta: true }
    }
  }

  if (!dtaAgora)
    return null;

  return null;
}