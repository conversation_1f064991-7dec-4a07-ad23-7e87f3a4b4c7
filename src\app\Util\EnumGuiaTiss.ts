export enum EnumSaudeOcupacional {    
    Admissional     = 1,
    Demissional     = 2,
    Periodico       = 3,
    RetornoTrabalho = 4,
    MudancaFuncao   = 5,
    PromocaoSaude   = 6
}

export const EnumSaudeOcupacionalDescricao = [
    {id: EnumSaudeOcupacional.Admissional, des: 'Admissional'},
    {id: EnumSaudeOcupacional.Demissional, des: 'Demissional'},
    {id: EnumSaudeOcupacional.MudancaFuncao, des: 'Periódico'},
    {id: EnumSaudeOcupacional.Periodico, des: 'Retorno ao Trabalho'},
    {id: EnumSaudeOcupacional.PromocaoSaude, des: 'Mudança de função'},
    {id: EnumSaudeOcupacional.RetornoTrabalho, des: 'Promoção a saúde'},
]

export enum EnumIndicacaoAcidente {    
    NaoAcidente = 1,
    Trabalho    = 2,
    Transito    = 3,
    Outros      = 4,
}

export const EnumIndicacaoAcidenteDescricao = [
    {id: EnumIndicacaoAcidente.NaoAcidente, des: 'Não Acidente'},
    {id: EnumIndicacaoAcidente.Trabalho, des: 'Trabalho'},
    {id: EnumIndicacaoAcidente.Transito, des: 'Transito'},
    {id: EnumIndicacaoAcidente.Outros, des: 'Outros'},
]

export enum EnumRegimeAtendimento {    
    Ambulatorial = 1,
    Domiciliar    = 2,
    Internacao    = 3,
    ProntoSocorro      = 4,
    Telessause = 5
}

export const EnumRegimeAtendimentoDescricao = [
    {id: EnumRegimeAtendimento.Ambulatorial, des: 'Ambulatorial'},
    {id: EnumRegimeAtendimento.Domiciliar, des: 'Domiciliar'},
    {id: EnumRegimeAtendimento.Internacao, des: 'Internação'},
    {id: EnumRegimeAtendimento.Telessause, des: 'Telessause'},
]

export enum EnumTipoAtendimento {    
    Remocao     = 1,
    PequenaCirurgia     = 2,
    OutrasTerapias       = 3,
    Consulta = 4,
    QuimioTerapia   = 5,
    Radioterapia   = 6,
    TRS = 7,
    PequenoAtendimento = 8,
    Exame = 9
}

export const EnumTipoAtendimentoDescricao = [
    {id: EnumTipoAtendimento.Remocao, des: 'Remoção'},
    {id: EnumTipoAtendimento.PequenaCirurgia, des: 'Pequena Cirurgia'},
    {id: EnumTipoAtendimento.OutrasTerapias, des: 'Outras Terapias'},
    {id: EnumTipoAtendimento.Consulta, des: 'Consulta'},
    {id: EnumTipoAtendimento.QuimioTerapia, des: 'Quimioterapia'},
    {id: EnumTipoAtendimento.Radioterapia, des: 'Radioterapia'},
    {id: EnumTipoAtendimento.TRS, des: 'Terapia Renal Subsitutiva(TRS)'},
    {id: EnumTipoAtendimento.PequenoAtendimento, des: 'Pequeno atendimento (sutura, gesso e outros)'},
    {id: EnumTipoAtendimento.Exame, des: 'Exame'},
]

export enum EnumCaraterAtendimento {
    Eletivo = 1,
    UrgenciaEmergencia = 2
}

export const EnumCaraterAtendimentoDescricao = [
    {id: EnumCaraterAtendimento.Eletivo, des: 'Eletivo'},
    {id: EnumCaraterAtendimento.UrgenciaEmergencia, des: 'Urgência/Emergência'},
]