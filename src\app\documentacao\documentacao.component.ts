import { Component, OnInit } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { MedicoService } from '../service/medico.service';
import { PacienteService } from '../service/pacientes.service';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { DocumentosService } from '../service/documentos.service';
import { Atestado, Declaracao, Receita } from '../model/documentos';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { saveAs } from 'file-saver';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { documentosModalService } from './documentocao.service';
import { documentosModal } from '../model/DocumentosModal';
import { EnumTipoDocumentos } from '../Util/tipoDocumentos';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { PesquisaCidService } from '../pesquisa-cid/pesquisa-cid.service';
import { CidService } from '../service/cid.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { NgSelectModule } from '@ng-select/ng-select';


@Component({
    selector: 'app-documentacao',
    templateUrl: './documentacao.component.html',
    styleUrls: ['./documentacao.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      NgxSmartModalModule,
      TranslateModule,
      MatFormFieldModule,
      MatIcon,
      MatDivider,
      TruncatePipe,
      NgSelectModule
    ]
})
export class DocumentacaoComponent implements OnInit {



  constructor(
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private medicoService: MedicoService,
    private pacientesService: PacienteService,
    private documentosService: DocumentosService,
    // public snackBar: MatSnackBar,
    private tradutor: TranslateService,
    private documentosServiceModal: documentosModalService,
    private usuarioLogadoService: UsuarioLogadoService,
    private pesquisaCidService: PesquisaCidService,
    private cisService: CidService,
    private snackBarAlert: AlertComponent


  ) {
    this.cisService.changeCID$
      .subscribe((perf: any) => {
        if (perf != null) {
          this.desCID = perf;
          this.CarregaCIDCampo();
        }
      });

  }
  idconsulta?: number;
  nomeRemedio: string = ''
  filtrarRemedio: any = [];
  filtraCID: any = []
  ListaCid: any = []
  IdCID?: number;
  desCID?: string;
  CID?: string;
  DesCID?: boolean;
  DiasAtestado?: number | null;
  ListaPaciente: any = []
  ListaMedicos: any = []
  IdPaciente?: number | null;
  IdMedico?: number | null;
  CPF: string = "";
  // usuario: Usuario;
  DadosInformUsuario: any = []
  DtaAtestado = new Date().toLocaleDateString();
  showMessageError: boolean = false;
  flgEndereco: boolean = false;
  DadosRaceitaPaciente: any = [];
  DadosRaceitaRemedios: any = [];
  CampoReceita: string = ''
  actionButtonLabel: string = 'Fechar';
  salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  ErroSalvar: string = 'Erro ao salvar!';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  concordo?: boolean;
  concordomsg?: boolean;
  periudoDeclaracao?: string;
  ModalFechada: boolean = false;
  paciAtesVasil?: boolean;
  paciAtesVal?: boolean;
  mediAtesVasil?: boolean;
  mediAtesVal?: boolean;
  cidAtesVasil?: boolean;
  cidAtesVal?: boolean;
  paciDeclVasil?: boolean;
  paciDeclVal?: boolean;
  mediDeclVasil?: boolean;
  mediDeclVal?: boolean;
  periDeclVasil?: boolean;
  periDeclVal?: boolean;
  paciReceitVasil?: boolean;
  paciReceitVal?: boolean;
  mediReceitVasil?: boolean;
  mediReceitVal?: boolean;
  // clearmodal: boolean = false;
  AtestadoStart: boolean = false;
  DeclaracaoStart: boolean = false;
  ReceituarioStart: boolean = false;
  checkmedicamentosPro: boolean = false;
  clearmodal: string[] = ["InicializandoAtestado", "InicializandoReceituario", 'InforUsuarioDocumentacao'];

  AtestadoStream: boolean = false;
  DeclaracaoStream: boolean = false;
  ReceituarioStream: boolean = false;
  
  flgBtnCarregarMais: boolean = false;
  qtdPorPagina: number = 30;

  ngOnInit() {
    this.CarregaMedicos();
    this.CarregaPaciente();

    this.documentosServiceModal
      .getAbrirModal()
      .subscribe((ret: documentosModal) => {

        if (ret.Caminho == 'Menu') {
          this.ModaisMenu(ret)

        }
        else {
          this.ModaisConsulta(ret)
        }
      });

  }
  ModaisMenu(objDocumentos: documentosModal) {
    if (objDocumentos.TipoModal == EnumTipoDocumentos.Atestado) {
      this.limparCampoAtest();

      this.AtestadoStream = false;
      this.IdPaciente = objDocumentos.idPaciente;
      this.IdMedico = objDocumentos.idMedico;

      this.ngxSmartModalService.getModal('InicializandoAtestado').open();

    }
    else if (objDocumentos.TipoModal == EnumTipoDocumentos.Declaracao) {
      this.limparCampoDeclar();
      this.DeclaracaoStream = false;
      this.IdPaciente = objDocumentos.idPaciente;
      this.IdMedico = objDocumentos.idMedico;
      this.ngxSmartModalService.getModal('InicializandoDeclaracao').open();

    }
    else if (objDocumentos.TipoModal == EnumTipoDocumentos.Receituario) {
      this.limparPaciReceit();
      this.ReceituarioStream = false;
      this.IdPaciente = objDocumentos.idPaciente;
      this.IdMedico = objDocumentos.idMedico;
      this.GetListaMedicamentos();
      this.ngxSmartModalService.getModal('InicializandoReceituario').open();

    }
  }

  ModaisConsulta(objDocumentos: documentosModal) {
    if (objDocumentos.TipoModal == EnumTipoDocumentos.Atestado) {
      this.IdPaciente = objDocumentos.idPaciente
      this.IdMedico = objDocumentos.idMedico
      this.CPF = ""
      this.CID = ""
      this.desCID = ""
      this.DiasAtestado = null
      this.AtestadoStream = true;

      this.idconsulta = objDocumentos.idConsulta;
      this.ngxSmartModalService.getModal('InicializandoAtestado').open();

    }
    else if (objDocumentos.TipoModal == EnumTipoDocumentos.Declaracao) {
      this.IdPaciente = objDocumentos.idPaciente
      this.IdMedico = objDocumentos.idMedico
      this.DeclaracaoStream = true;
      this.idconsulta = objDocumentos.idConsulta;
      this.ngxSmartModalService.getModal('InicializandoDeclaracao').open();

    }
    else if (objDocumentos.TipoModal == EnumTipoDocumentos.Receituario) {

      this.IdPaciente = objDocumentos.idPaciente
      this.IdMedico = objDocumentos.idMedico
      this.ReceituarioStream = true;
      this.idconsulta = objDocumentos.idConsulta;
      this.ngxSmartModalService.getModal('InicializandoReceituario').open();

    }
  }

  async CarregaMedicos() {
    await this.medicoService.getMedicos(null, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.ListaMedicos = retorno
      
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  async CarregaCID() {
    this.cisService.GetListaFiltaPorCod(0, 100, this.desCID).subscribe((retorno) => {
      this.ListaCid = retorno
      this.filtraCID = this.ListaCid.filter((c:any) => c.codCid == this.desCID!.toUpperCase())
      if (this.filtraCID.length > 0) {
        this.CID = this.filtraCID[0].desCid
      }
      else {
        this.tradutor.get('TELADOCUMENTACAO.CODNAOCORRESPONDEADOENCA').subscribe((res: string) => {
          ;
          this.CID = res;
        });
      }
      
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }


  async CarregaPaciente() {
    await this.pacientesService.GetPacienteAgenda(this.CPF, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.ListaPaciente = retorno
      

      if (this.CPF != '' && this.CPF != undefined && this.CPF != null) {
        if (this.ListaPaciente.length == 1)
          this.IdPaciente = this.ListaPaciente[0].idCliente

      }
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }
  CampoReceitaHTml?: string;

  addMedicamentos(item:any, item2:any) {

    if (this.checkmedicamentosPro) {
      if ((document.getElementById('txtReceita') as HTMLInputElement)['value'] == "") {

        this.CampoReceitaHTml = item + '<br><br>' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = item;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'] + '' + '\n\n' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'].replace(/\\n/g, '<br>');

        (document.getElementById('txtReceita') as HTMLInputElement).focus()
      }
      else {
        this.CampoReceitaHTml = this.CampoReceitaHTml + '<br><br>' + item + '<br><br>' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'] + '' + '\n\n\n\n' + item;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'] + '' + '\n\n' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'].replace(/\\n/g, '<br>');
        (document.getElementById('txtReceita') as HTMLInputElement).focus()
      }
    }
    else {

      if ((document.getElementById('txtReceita') as HTMLInputElement)['value'] == "") {

        this.CampoReceitaHTml = item + '<br><br>' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = item;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'] + '' + '\n\n' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'].replace(/\\n/g, '<br>');
        (document.getElementById('txtReceita') as HTMLInputElement).focus();

      }
      else {

        this.CampoReceitaHTml = this.CampoReceitaHTml + '<br><br>' + item + '<br><br>' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'] + '' + '\n\n\n\n' + item;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'] + '' + '\n\n' + item2;
        (document.getElementById('txtReceita') as HTMLInputElement)['value'] = (document.getElementById('txtReceita') as HTMLInputElement)['value'].replace(/\\n/g, '<br>');
        (document.getElementById('txtReceita') as HTMLInputElement).focus();
      }
    }
  }

  GetListaMedicamentos(flgCarregarMais?: boolean) {
    this.spinner.show();
    var inicio = flgCarregarMais ? this.DadosRaceitaRemedios.length : 0;
    this.documentosService.GetListaMedicamentos(this.nomeRemedio, inicio, this.qtdPorPagina).subscribe(
      (retorno) => {

        if (flgCarregarMais) {
          for (let i = 0; i < retorno.length; i++) {
            this.DadosRaceitaRemedios.push(retorno[i]);
          }
        } else {
          this.DadosRaceitaRemedios = retorno;
        };

        this.flgBtnCarregarMais = retorno.length >= this.qtdPorPagina;

        this.spinner.hide();
      },() => {
        this.spinner.hide();
      }
    );
  }

  GetListaMedicamentosClinica() {
    this.spinner.show();
    this.DadosRaceitaRemedios = []
    this.documentosService.GetMedicamentosClinica(this.nomeRemedio, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retornaClinicas: any = []) => {
      if (retornaClinicas.length > 0) {
        retornaClinicas.forEach((element:any) => {
          this.DadosRaceitaRemedios.push({
            produto: element.medicamento.medicamentosProgramados,
            apresentacao: element.medicamento.programacao,
            Flg: element.medicamento.flgDestacarItem
          })
        });

      }
      
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  CarregaCIDCampo() {
    if (this.desCID != '' && this.desCID != null) {
      this.filtraCID = [];
      this.CarregaCID()

    }
  }
  Inicializacao(obj:any) {

    this.idconsulta = 0;
    if (obj == 'Atestado') {
      this.CarregaCID()
      this.IdPaciente = null
      this.IdMedico = null
      this.CPF = ""
      this.CID = ""
      this.desCID = ""
      this.DiasAtestado = null
      this.ngxSmartModalService.getModal('InicializandoAtestado').open();
    }
    else if (obj == 'Declaração') {
      this.IdPaciente = null
      this.IdMedico = null
      this.periudoDeclaracao = "";
      this.CPF = ""
      this.ngxSmartModalService.getModal('InicializandoDeclaracao').open();

    }
    else if (obj == 'Receituario') {
      this.DadosRaceitaRemedios = [];
      this.IdPaciente = null
      this.IdMedico = null
      this.CampoReceita = ''
      this.flgEndereco = false;
      this.CPF = ''
      this.nomeRemedio = "";
      (document.getElementById('txtReceita') as HTMLInputElement)['value'] = '';
      this.ngxSmartModalService.getModal('InicializandoReceituario').open();

    }
  }

  ValidaPaciAtes(pac:any) {

    pac = this.IdPaciente;

    if (pac > 0) {
      this.paciAtesVasil = false;
      this.paciAtesVal = false;
    }
    else {
      this.paciAtesVasil = false;
      this.paciAtesVal = true;
    }
  }

  ValidaMediAtes(pac:any) {

    pac = this.IdMedico;

    if (pac > 0) {
      this.mediAtesVasil = false;
      this.mediAtesVal = false;
    }
    else {
      this.mediAtesVasil = false;
      this.mediAtesVal = true;
    }
  }

  ValidaCidAtes(pac:any) {

    pac = this.desCID;

    if (pac > 0) {
      this.cidAtesVasil = false;
      this.cidAtesVal = false;
    }
    else {
      this.cidAtesVasil = false;
      this.cidAtesVal = true;
    }
  }

  ValidaPaciDecl(pac:any) {

    pac = this.IdPaciente;

    if (pac > 0) {
      this.paciDeclVasil = false;
      this.paciDeclVal = false;
    }
    else {
      this.paciDeclVasil = false;
      this.paciDeclVal = true;
    }
  }

  ValidaMediDecl(pac:any) {

    pac = this.IdMedico;

    if (pac > 0) {
      this.mediDeclVasil = false;
      this.mediDeclVal = false;
    }
    else {
      this.mediDeclVasil = false;
      this.mediDeclVal = true;
    }
  }

  ValidaPaciReceit(pac:any) {

    pac = this.IdPaciente;

    if (pac > 0) {
      this.paciReceitVasil = false;
      this.paciReceitVal = false;
    }
    else {
      this.paciReceitVasil = false;
      this.paciReceitVal = true;
    }
  }

  ValidaMediReceit(med:any) {

    med = this.IdMedico;

    if (med > 0) {
      this.mediReceitVasil = false;
      this.mediReceitVal = false;
    }
    else {
      this.mediReceitVasil = false;
      this.mediReceitVal = true;
    }
  }



  paci = new FormControl('', [Validators.required, Validators.maxLength(11)])
  medi = new FormControl('', [Validators.required, Validators.maxLength(11)])
  cid = new FormControl('', [Validators.required, Validators.maxLength(11)])
  dias = new FormControl('', [Validators.required, Validators.maxLength(11)])
  periudo = new FormControl('', [Validators.required, Validators.maxLength(11)])
  getErrorMessageperiudo() {
    return this.periudo.hasError('required') ? 'TELADOCUMENTACAO.TELACAMPO' :
      this.periudo.hasError('Períudo') ? 'TELADOCUMENTACAO.TELANAOEVALIDA' :
        '';

  }
  getErrorMessagepaci() {
    return this.paci.hasError('required') ? 'TELADOCUMENTACAO.TELACAMPO' :
      this.paci.hasError('Procedência') ? 'TELADOCUMENTACAO.TELANAOEVALIDA' :
        '';

  }
  getErrorMessagemedi() {
    return this.medi.hasError('required') ? 'TELADOCUMENTACAO.TELACAMPO' :
      this.medi.hasError('Procedência') ? 'TELADOCUMENTACAO.TELANAOEVALIDA' :
        '';

  }
  getErrorMessagecid() {
    return this.cid.hasError('required') ? 'TELADOCUMENTACAO.TELACAMPO' :
      this.cid.hasError('Procedência') ? 'TELADOCUMENTACAO.TELANAOEVALIDA' :
        '';

  }

  getErrorMessagedias() {
    return this.dias.hasError('required') ? 'TELADOCUMENTACAO.TELACAMPO' :
      this.dias.hasError('Procedência') ? 'TELADOCUMENTACAO.TELANAOEVALIDA' :
        '';

  }

  informacao(user:any, id:any) {
    this.DadosInformUsuario = {}
    if (user == 'Paciente') {

      var user = this.ListaPaciente.filter((c:any) => c.idCliente == id);
 
      this.DadosInformUsuario = user[0]
      
      
      this.ngxSmartModalService.getModal('InforUsuarioDocumentacao').open();
    }
    else {
      this.DadosInformUsuario = this.ListaMedicos.filter((c:any) => c.idMedico == id);
      this.ngxSmartModalService.getModal('InforUsuarioDocumentacao').open();
    }
  }

  GerarDeclaracao() {

    this.validarCamposdeclaracao()
    if (this.showMessageError == true)
      return;

    var declaracao = new Declaracao();
    declaracao.flgInativo = false;
    declaracao.IdMedico = this.IdMedico;
    declaracao.IdPaciente = this.IdPaciente;
    declaracao.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    declaracao.Periodo = this.periudoDeclaracao;
    declaracao.IdConsulta = this.idconsulta;
    this.documentosService.GerarDeclaracao(declaracao).subscribe((retorno) => {


      if (retorno != null) {
        var mediaType = 'application/pdf';
        var blob = new Blob([retorno], { type: mediaType });
        var filename = 'Declaracao.pdf';
        saveAs(blob, filename);


        this.ngxSmartModalService.getModal('InicializandoDeclaracao').close();
      }
      this.spinner.hide();
      // this.showMessageSuccess = true;

    }, () => {
      this.spinner.hide();
    })


  }

  GerarAtestado() {
    this.validarCamposAtestado()

    if (this.DiasAtestado == null || this.DiasAtestado == undefined) {

      this.tradutor.get('TELADOCUMENTACAO.PREENCHERDIAS').subscribe((res: string) => {
        ;
        this.snackBarAlert.sucessoSnackbar(res);
      });
      this.showMessageError = true

    }
    if (this.desCID != null && this.desCID != '' && this.filtraCID.length == 0) {
      this.tradutor.get('TELADOCUMENTACAO.CIDINVALIDO').subscribe((res: string) => {
        ;
        this.snackBarAlert.falhaSnackbar(res);
      });
      this.showMessageError = true
    }
    if (this.showMessageError == true)
      return;
    var atestado = new Atestado();
    atestado.IdCid = this.desCID ? this.filtraCID[0].idCid : null;
    atestado.FlgDesCid = this.DesCID;
    atestado.flgInativo = false;
    atestado.IdMedico = this.IdMedico;
    atestado.IdPaciente = this.IdPaciente;
    atestado.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    atestado.QuantDias = this.DiasAtestado
    atestado.IdConsulta = this.idconsulta;
    this.documentosService.GerarAtestado(atestado).subscribe((retorno) => {
      if (retorno != null) {
        var mediaType = 'application/pdf';
        var blob = new Blob([retorno], { type: mediaType });
        var filename = 'Atestado.pdf';
        saveAs(blob, filename);


        this.ngxSmartModalService.getModal('InicializandoAtestado').close();
      }
      this.spinner.hide();
      // this.showMessageSuccess = true;

    }, () => {
      this.spinner.hide();
    })


  }
  GerarReceita() {
    this.validarCamposReceita()
    this.ValidaMediReceit(this.IdMedico)
    this.ValidaPaciReceit(this.IdPaciente)
    if (this.mediReceitVal == true || this.paciReceitVal == true) {
      this.snackBarAlert.falhaSnackbar('Preencha os campos obrigatórios');
      return
    }
    
    ;
    // var teste = document.getElementById('txtReceita')['value'].replace(/\<br>/g, "/n");

    this.CampoReceita = this.CampoReceitaHTml!

    if (this.CampoReceita == null || this.CampoReceita == undefined || !this.CampoReceita.trim()) {

      this.tradutor.get('TELADOCUMENTACAO.NAOHAMEDICAMENTOSNARECEITA').subscribe((res: string) => {
        ;
        this.snackBarAlert.falhaSnackbar(res);
      });
      this.showMessageError = true
    }

    if (this.showMessageError == true)
      return;
    var receita = new Receita();
    receita.flgEndereco = this.flgEndereco;
    receita.flgInativo = false;
    receita.IdMedico = this.IdMedico;
    receita.IdPaciente = this.IdPaciente;
    receita.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

    receita.DesReceita = this.CampoReceita
    receita.IdConsulta = this.idconsulta;
    this.documentosService.GerarReceita(receita).subscribe((retorno) => {
      if (retorno != null) {
        var mediaType = 'application/pdf';
        var blob = new Blob([retorno], { type: mediaType });
        var filename = 'Receita.pdf';
        saveAs(blob, filename);
        this.ngxSmartModalService.getModal('InicializandoReceituario').close();
      }
      // this.showMessageSuccess = true;
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })


  }
  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v
  }

  public validarCamposdeclaracao() {
    this.showMessageError = false;
    this.paci.markAsTouched();
    this.medi.markAsTouched();
    this.periudo.markAsTouched();

    if (this.IdPaciente == null || this.IdPaciente == undefined
      || this.IdMedico == null || this.IdMedico == undefined
      || this.periudoDeclaracao == '' || this.periudoDeclaracao == undefined
    ) {
      this.showMessageError = true;
      document.documentElement.scrollTop = 0;
    }

  }

  public validarCamposReceita() {
    this.showMessageError = false;
    this.paci.markAsTouched();
    this.medi.markAsTouched();

    if (this.IdPaciente == null || this.IdPaciente == undefined
      || this.IdMedico == null || this.IdMedico == undefined
    ) {


      this.showMessageError = true;
      document.documentElement.scrollTop = 0;
    }

  }

  public validarCamposAtestado() {


    this.showMessageError = false;
    this.medi.markAsTouched();
    this.cid.markAsTouched();
    this.mediAtesVal = false;
    this.paciAtesVal = false;


    if (this.IdPaciente == null || this.IdPaciente == undefined
      || this.IdMedico == null || this.IdMedico == undefined
      || this.paciDeclVal == true
      || this.mediDeclVal == true || this.periDeclVal == true
      || this.paciReceitVal == true || this.mediReceitVal == true
    ) {
      if (!this.AtestadoStream && this.IdMedico == undefined) {
        this.mediAtesVal = true;
      }
      if (!this.AtestadoStream && this.IdPaciente == undefined) {
        this.paciAtesVal = true;
      }
      this.showMessageError = true;
      document.documentElement.scrollTop = 0;
    }
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  // AlgumAcerto(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }



  limparPaciReceit() {
    this.paciReceitVal = false;
    this.mediReceitVal = false;
    this.DadosRaceitaRemedios = [];
    this.IdPaciente = null
    this.IdMedico = null
    this.CampoReceita = ''
    this.flgEndereco = false;
    this.CPF = ''
    this.nomeRemedio = "";
    this.CarregaPaciente()
  }



  limparCampoDeclar() {
    this.paciDeclVal = false;
    this.mediDeclVal = false;
    this.periudo.markAsUntouched();
    this.IdPaciente = null
    this.IdMedico = null
    this.periudoDeclaracao = "";
    this.CPF = ""
    this.CarregaPaciente()
  }



  limparCampoAtest() {

    this.paciAtesVal = false;
    this.mediAtesVal = false;
    this.cid.markAsUntouched();
    this.IdPaciente = null
    this.IdMedico = null
    this.CPF = ""
    this.CID = ""
    this.desCID = ""
    this.DiasAtestado = null
    this.CarregaPaciente()


  }

  AbrirModalPesquisaCid() {
    this.pesquisaCidService.setModalPesquisaCid();
  }

}
