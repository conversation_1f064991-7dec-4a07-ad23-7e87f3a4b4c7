<mat-card-title>
  <div class="row">
    <div class="col-md-12 col-lg-12 col-sm-12" style="display: flex;">
      <div class="icon-page-title" style="margin-right: 10px;">
        <span class="material-icons" style="color: #5260ff; font-size: 30px; width: 32px; height: 32px;">assignment</span>
      </div>
      <a class="title-page" style="font-family: 'Cairo', sans-serif; font-size: 24px; font-weight: bold; color: #348bc1;">Dashboard Interno</a>
    </div>
  </div>
</mat-card-title>

<!-- Seus cards para Dashboard -->
<div class="cards-medico">
  <div class="col-12" style="display: flex; flex-wrap: wrap; padding: 0;">

    <!-- Card de Clínicas Cadastradas -->
    <div class="card-dash col l3 m6 s12">
      <div class="erros-dash">
        <div class="title-dashInt">
          <p>Clínicas Cadastradas</p>
          <span>{{ dadosDashboard.totalClinicasAtivas }}</span>
        </div>
      </div>
    </div>

    <!-- Card de Pacientes -->
    <div class="card-dash col l3 m6 s12">
      <div class="erros-dash">
        <div class="title-dashInt">
          <p>Total de Pacientes</p>
          <span>{{ dadosDashboard.totalPacientes }}</span>
        </div>
      </div>
    </div>

    <!-- Card de Total de Erros -->
    <div class="card-dash col l3 m6 s12">
      <div class="erros-dash">
        <div class="title-dashInt">
          <p>Total de Erros</p>
          <span>{{ dadosDashboard.totalErros }}</span> 
        </div>
      </div>
    </div>

    <!-- Card de Médicos Cadastrados -->
    <div class="card-dash col l3 m6 s12">
      <div class="erros-dash">
        <div class="title-dashInt">
          <p>Médicos Ativos</p>
          <span>{{ dadosDashboard.totalMedicosAtivos }}</span> 
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Conteúdo Tabela -->
<div class="div-scroll-table" style="max-height: 600px; overflow-y: auto; border-radius: 10px; margin-top: 10px;">
  <div>
    <table
      class="w3-table-all w3-hoverable tabela"
      style="
        border-radius: 10px;
        border-collapse: inherit;
        width: 100%;
        margin-right: 30px;
      "
    >
      <colgroup class="datatable-scrollable-colgroup">
        <col style="width: 20%" />
        <col style="width: 20%" />
        <col style="width: 20%" />
        <col style="width: 20%" />
        <!-- <col style="width: 20%" /> -->
      </colgroup>
      <thead>
        <tr
          class="cabTabelaFixo cabecalhotable"
          style="
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #348bc1;
            color: #fff;
          "
        >
          <th style="padding: 10px; font-family: sans-serif; font-weight: bold; font-size: 14px; border-bottom: 2px solid #ddd;">Método</th>
          <th style="padding: 10px; font-family: sans-serif; font-weight: bold; font-size: 14px; border-bottom: 2px solid #ddd;">Descrição</th>
          <th style="padding: 10px; font-family: sans-serif; font-weight: bold; font-size: 14px; border-bottom: 2px solid #ddd;">Data</th>
          <th style="padding: 10px; font-family: sans-serif; font-weight: bold; font-size: 14px; border-bottom: 2px solid #ddd;">Usuário Gerador</th>
          <!-- <th style="padding: 10px; font-family: sans-serif; font-weight: bold; font-size: 14px; border-bottom: 2px solid #ddd;">Clínica</th> -->
        </tr>
      </thead>
      <tbody *ngIf="listaErrosFiltrada">
        <tr *ngFor="let item of listaErrosFiltrada" style="transition: background-color 0.3s ease;">
          <td style="font-size: 14px; padding: 10px; text-wrap: wrap; word-break: normal; border-bottom: 1px solid #ddd;">{{ item.metodo }}</td>
          <td style="font-size: 14px; padding: 10px; text-wrap: wrap; word-break: break-all; border-bottom: 1px solid #ddd;">{{ item.desErro | slice : 0 : 100 }}</td>
          <td style="font-size: 14px; padding: 10px; text-wrap: wrap; word-break: normal; border-bottom: 1px solid #ddd;">{{ item.dtaCadastro | date : 'dd/MM/yyyy - HH:mm' }}</td>
          <td style="font-size: 14px; padding: 10px; text-wrap: wrap; word-break: break-all; border-bottom: 1px solid #ddd;">{{ item.nomeUsuario }}</td>
          <!-- <td style="font-size: 14px; padding: 10px; text-wrap: wrap; word-break: break-all; border-bottom: 1px solid #ddd;">{{ item.nomeClinica }}</td> -->
        </tr>
      </tbody>
    </table>
  </div>
</div>