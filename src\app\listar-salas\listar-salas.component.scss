/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background-color: $bg-color;
  color: $text-primary;
}

.container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

/* CARD PRINCIPAL */
.card-principal {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 24px;
  overflow: hidden;
}

/* CABEÇALHO */
.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.header-icon {
  background-color: $primary-light;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.header-icon .material-icons {
  color: $primary-color;
  font-size: 24px;
}

.header-title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
}

/* FILTROS */
.filtros {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.toggle-container {
  display: flex;
  align-items: center;
}

.toggle-item {
  color: $primary-color;
}

.toggle-item ::ng-deep .mat-slide-toggle-bar {
  background-color: rgba(0, 0, 0, 0.1);
}

.toggle-item ::ng-deep .mat-slide-toggle-thumb {
  background-color: white;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-bar {
  background-color: rgba(46, 139, 87, 0.5) !important;
}

.toggle-item ::ng-deep .mat-checked .mat-slide-toggle-thumb {
  background-color: $primary-color !important;
}

.busca-container {
  flex-grow: 1;
  max-width: 100%;
}

.busca-field {
  width: 100%;
}

.busca-field ::ng-deep .mat-form-field-wrapper {
  margin-bottom: -1.25em;
}

.busca-field ::ng-deep .mat-form-field-flex {
  background-color: $bg-color;
}

.busca-field ::ng-deep .mat-form-field-outline {
  color: $border-color;
}

.btn-busca {
  color: $primary-color;
  background-color: transparent;
}

.adicionar-container {
  display: flex;
  justify-content: flex-end;
}




.btn-adicionar:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* LISTA DE SALAS */
.lista-container {
  background-color: $bg-color;
  border-radius: $border-radius;
  padding: 8px;
  margin-bottom: 24px;
}

.lista-scroll {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}

.lista-scroll::-webkit-scrollbar {
  width: 6px;
}

.lista-scroll::-webkit-scrollbar-track {
  background: $secondary-light;
  border-radius: 10px;
}

.lista-scroll::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.sala-card {
  background-color: white;
  border-radius: $border-radius;
  margin-bottom: 12px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all $transition;
  border-left: 4px solid transparent;
}

.sala-card:hover {
  transform: translateY(-2px);
  box-shadow: $box-shadow;
  border-left: 4px solid $primary-color;
}

.sala-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item mat-icon {
  font-size: 18px;
  color: $primary-color;
  margin-right: 8px;
}

.info-label {
  font-weight: 600;
  color: $text-primary;
  margin-right: 8px;
}

.info-value {
  color: $text-secondary;
}

.sala-acoes {
  display: flex;
  gap: 8px;
  .remove {
    color: #FF6B6B !important;
    background-color: rgba(255, 107, 107, 0.05) !important;
    mat-icon{
      color: #ff6b6b;
      &:hover{
        color: #ffa5a5;
      }
    }
  }
}

.sala-acoes button {
  width: 36px;
  height: 36px;
  background-color: $primary-light;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all $transition;
}

.sala-acoes button:hover {
  background-color: $primary-color;
  transform: scale(1.1);
}

.sala-acoes button:hover mat-icon {
  color: white;
}

.sala-acoes mat-icon {
  color: $primary-color;
  font-size: 20px;
  transition: color $transition;
}

/* LISTA VAZIA */
.lista-vazia {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: $text-secondary;
}

.lista-vazia mat-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

/* RESPONSIVIDADE */
@media (max-width: 1024px) {
  .filtros {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toggle-container, .busca-container, .adicionar-container {
    width: 100%;
    max-width: 100%;
    margin-bottom: 16px;
  }
  
  .adicionar-container {
    margin-bottom: 0;
  }
  
  .btn-adicionar {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .sala-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .sala-info {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .sala-acoes {
    width: 100%;
    justify-content: flex-end;
  }
}