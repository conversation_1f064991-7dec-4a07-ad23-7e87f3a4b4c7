import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { AgendaService } from 'src/app/service/agenda.service';
import { SpinnerService } from 'src/app/service/spinner.service';

@Component({
  selector: 'app-modal-cancelar-horario',
  standalone: true,
  imports: [
    MatSelectModule,
    CommonModule,
    FormsModule,
    TranslateModule,
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    NgSelectModule,
    ReactiveFormsModule],
  templateUrl: './modal-cancelar-horario.component.html',
  styleUrl: './modal-cancelar-horario.component.scss'
})
export class ModalCancelarHorarioComponent {
  cancelamento = false;
  Motivocancelameto = '';
  idCancelamento: number;
  constructor(
    private mdRef: MatDialogRef<ModalCancelarHorarioComponent>,
    private agendaService: AgendaService,
    private usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    @Inject(MAT_DIALOG_DATA) public data: { idCancelamento: number }
  ) {
    this.idCancelamento = data.idCancelamento
  }

  fecharModal(flg: boolean) {
    this.mdRef.close(flg);
  }

  MotivoCampo() {
    if (this.cancelamento == true)
      this.cancelamento = false
  }

  CancelarConsulta() {

    if (this.Motivocancelameto == '' || this.Motivocancelameto == undefined) {
      this.cancelamento = true;
      return;
    }

    this.agendaService.InativarAgendamento(this.idCancelamento, this.usuarioLogadoService.getIdUsuarioAcesso(), this.Motivocancelameto).subscribe(() => {
      this.mdRef.close(true);
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }
}
