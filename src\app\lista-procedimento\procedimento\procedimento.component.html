<app-modal-template [cabecalho]="'Procedimento'" (fechar)="FecharModal();">
</app-modal-template>
    <section>
        <main>
            <form >
                <div class="Conteudo">
                    <!-- Dados gerais do procedimento -->
                    <div class="CaixaPrincipal">
                        <div class="CaixaRotulo">
                            <span>Dados</span>
                        </div>
                        <div class="CaixaSecundaria" style="flex: 35%; gap: 5px">
                            <!-- Campo para a data de início -->
                            <mat-form-field style="width: 110px; height: 47.5px;" appearance="outline">
                                <mat-label>Data</mat-label>
                                <input type="date" formControlName="dtaInico" matInput>
                            </mat-form-field>

                            <!-- Campo para o horário de início -->
                            <mat-form-field style="width: 75px; height: 47.5px" appearance="outline">
                                <mat-label>Inicio</mat-label>
                                <input type="time" formControlName="dtaPeriodoInicio" matInput placeholder="00:00">
                            </mat-form-field>

                            <!-- Campo para o horário de fim -->
                            <mat-form-field style="width: 75px; height: 47.5px" appearance="outline">
                                <mat-label>Fim</mat-label>
                                <input type="time" formControlName="dtaPeriodoFim" matInput placeholder="00:00">
                            </mat-form-field>
                        </div>

                        <!-- Controle para o toggle "Lançar Itens" -->
                        <div class="CaixaSecundaria" style="flex: 50%;">
                            <div class="CaixaToggle">
                                <span>Lançar os itens associados ao procedimento.</span>
                                <!-- <app-toggle
                                [(flg)]="objProcedimento.flgLancarItens"
                                ></app-toggle> -->
                            </div>
                        </div>
                    </div>

                    <!-- Fatura e convênio -->
                    <div class="CaixaPrincipal">
                        <div class="CaixaSecundaria" style="flex: 40%;">
                            <!-- Campo para selecionar a fatura -->
                            <mat-form-field style="width: 100%;" appearance="outline">
                                <mat-label>Fatura</mat-label>
                                <mat-select formControlName="idFatura">
                                    <mat-option *ngFor="let item of listaFatura" [value]="item.idFatura">
                                        {{ item.descricao }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>

                        <div class="CaixaSecundaria" style="flex: 40%;">
                            <!-- Campo para selecionar o convênio -->
                            <mat-form-field style="width: 100%;" appearance="outline">
                                <mat-label>Convenio</mat-label>
                                <mat-select formControlName="idConvenio">
                                    <mat-option *ngFor="let item of listaConvenios" [value]="item.idConvenio">
                                        {{ item.desConvenio }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Dados detalhados do procedimento -->
                    <div class="CaixaPrincipal">
                        <div class="CaixaSecundaria" style="flex: 10%;">
                            <!-- Código do procedimento -->
                            <mat-form-field style="width: 150px;" appearance="outline">
                                <mat-label>Código</mat-label>
                                <input formControlName="codProcedimento" matInput placeholder="00.00.000-0">
                            </mat-form-field>
                        </div>

                        <div class="CaixaSecundaria" style="flex: 55%;">
                            <!-- Descrição do procedimento -->
                            <mat-form-field style="width: -webkit-fill-available;" appearance="outline">
                                <mat-label>Descrição do Procedimento</mat-label>
                                <input formControlName="desProcedimento" matInput placeholder="Descrição">
                            </mat-form-field>
                        </div>

                        <div class="CaixaSecundaria" style="flex: 5%;">
                            <!-- Quantidade -->
                            <mat-form-field style="width: 50px; text-align: center;" appearance="outline">
                                <mat-label>Qtd.</mat-label>
                                <input formControlName="qtd" matInput placeholder="Qtd">
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Composição de valores -->
                    <div class="CaixaPrincipal">
                        <div class="CaixaRotulo" style="flex: 48%;">
                            <span>Composição de valores</span>
                        </div>
                        <div class="CaixaRotulo" style="flex: 48%;">
                            <span>Valores do procedimento</span>
                        </div>
                        <div class="CaixaSecundaria Border" style="flex: 40%;">
                            <!-- Campos de valores de composição -->
                            <mat-form-field style="width: 80px;" appearance="outline">
                                <mat-label>M2 Filme</mat-label>
                                <input formControlName="vlrM2filme" matInput placeholder="00,00">
                            </mat-form-field>
                            <mat-form-field style="width: 80px;" appearance="outline">
                                <mat-label>Custo OP</mat-label>
                                <input formControlName="vlrCustoOp" matInput placeholder="00,00">
                            </mat-form-field>
                            <mat-form-field style="width: 80px;" appearance="outline">
                                <mat-label>Honorários</mat-label>
                                <input formControlName="vlrHonorarios" matInput placeholder="00,00">
                            </mat-form-field>
                        </div>

                        <div class="CaixaSecundaria Border" style="flex: 40%;">
                            <!-- Campos de valores totais -->
                            <mat-form-field style="width: 80px;" appearance="outline">
                                <mat-label>Particular</mat-label>
                                <input formControlName="vlrParticular" matInput placeholder="00,00">
                            </mat-form-field>
                            <mat-form-field style="width: 80px;" appearance="outline">
                                <mat-label>Convênio</mat-label>
                                <input formControlName="vlrConvenio" matInput placeholder="00,00">
                            </mat-form-field>
                            <mat-form-field style="width: 80px;" appearance="outline">
                                <mat-label>Total</mat-label>
                                <input matInput placeholder="00,00">
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Status do procedimento -->
                    <div class="CaixaPrincipal">
                        <div class="CaixaRotulo">
                            <span>Status</span>
                        </div>
                        <div class="CaixaSecundaria Border" style="flex: 100%;">
                            <!-- Radio buttons para status -->
                            <div class="CaixaRadioBtn">
                                <mat-radio-group formControlName="idStatusProcedimento">
                                    <mat-radio-button *ngFor="let item of listaStatusProcedimento" [value]="item.id">
                                        {{ item.des }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </main>
        <footer>
            <div class="CaixaAcoes">
                <div class="CaixaBtn">
                    <button (click)="SalvarProcedimento()">
                        <mat-icon>
                            save
                        </mat-icon>
                        <span>
                            Adicionar
                        </span>
                    </button>
                </div>
            </div>
        </footer>
    </section>
