// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$warning-color: #FFC107;        // Amarelo para alertas
$success-color: #4CAF50;        // Verde para sucesso
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}

.convenio-container {
  max-height: 87vh;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Alerts
.alert {
  border-radius: $border-radius;
  padding: 12px 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  
  p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .material-icons {
    font-size: 24px;
  }
}

.alert-success {
  background-color: rgba($success-color, 0.1);
  color: $success-color;
  border-left: 4px solid $success-color;
}

// Cards
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: none;
  margin-bottom: 24px;
  overflow: hidden;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// Headers
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

// Sections
.section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
}

// Convenio Info
.convenio-info {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.convenio-logo {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.image-upload-container {
  position: relative;
  display: block;
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid $primary-light;
  transition: all $transition ease;
  
  &:hover {
    .image-upload-overlay {
      opacity: 1;
    }
  }
}

.convenio-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba($primary-dark, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity $transition ease;
  
  .material-icons {
    color: white;
    font-size: 24px;
  }
}

.upload-label {
  margin-top: 8px;
  font-size: 14px;
  color: $text-secondary;
  font-weight: 500;
}

.convenio-details {
  flex: 1;
  min-width: 0;
}

// Form
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-group {
  padding: 0 8px;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.col-md-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.full-width {
  width: 100%;
}

.field-error {
  font-size: 12px;
  color: $error-color;
  margin-top: -8px;
  margin-bottom: 8px;
  padding-left: 4px;
}

// Material overrides
:host ::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-label {
    color: $text-secondary;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
    color: $primary-color;
  }
  
  .mat-input-element {
    color: $text-primary;
  }
  
  .mat-form-field-subscript-wrapper {
    overflow: visible;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
  }
}

// ng-select overrides
.modern-select {
  min-height: 55px;
  margin-bottom: 16px;
  
  ::ng-deep {
    .ng-select-container {
      border-radius: 4px;
      border-color: $border-color;
      min-height: 52px;
      
      &:hover {
        border-color: $primary-light;
      }
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
    }
    
    .ng-option {
      padding: 10px 16px;
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary;
  
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background-color: rgba($primary-color, 0.05);
  }
}

.btn-success {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-danger {
  background-color: $error-color;
  color: white;
  
  &:hover {
    background-color: darken($error-color, 5%);
  }
}

.btn-link {
  background: none;
  color: $primary-color;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

// Hide file input
input[type="file"] {
  display: none;
}

// Modal Styles
.modal-content {
  border-radius: $border-radius;
  background-color: white;
  overflow: hidden;
  width: 100%;
}

.modal-header {
  background-color: $primary-color;
  color: white;
  padding: 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  
  .material-icons {
    font-size: 20px;
  }
}

.warning-title {
  color: $warning-color;
  
  .warning-icon {
    color: $warning-color;
  }
}

.modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.terms-content {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid $border-color;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  p {
    margin-bottom: 16px;
  }
}

.warning-message {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  
  .terms-question {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
  }
  
  div {
    display: flex;
    gap: 16px;
  }
}

// Image cropper
.image-controls {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
}

.cropper-container {
  height: 300px;
  max-width: 100%;
  margin: 0 auto;
  border: 1px solid $border-color;
  border-radius: 8px;
  overflow: hidden;
}

// ngx-smart-modal overrides
::ng-deep {
  .nsm-content {
    border-radius: $border-radius !important;
    padding: 0 !important;
  }
  
  .nsm-dialog {
    padding: 0 !important;
  }
  
  .medium-modal {
    max-width: 800px !important;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .convenio-logo {
    margin-bottom: 24px;
  }
  
  .card-footer {
    flex-wrap: wrap;
    gap: 12px;
    
    .btn {
      flex: 1;
    }
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .image-controls {
    flex-wrap: wrap;
  }
}