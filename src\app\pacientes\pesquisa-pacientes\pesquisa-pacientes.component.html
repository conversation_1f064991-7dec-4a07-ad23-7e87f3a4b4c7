<div class="container">
  <mat-card class="card-principal mother-div">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">group</span>
      </div>
      <h2 class="header-title">{{ 'TELAPESQUISAPACIENTES.TITULO' | translate }}</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">
        
      <div class="busca-container">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>{{ 'TELAPESQUISAPACIENTES.BUSCAR' | translate }}</mat-label>
          <input matInput (keyup.enter)="GetListaPaciente()" [(ngModel)]="pesquisa">
          <button mat-icon-button matSuffix (click)="GetListaPaciente()" class="btn-busca">
            <mat-icon>search</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <div class="adicionar-container">
        <button mat-raised-button class="btn-adicionar" [routerLink]="['/paciente']">
          <mat-icon>add</mat-icon>
          <span>{{ 'TELAPESQUISAPACIENTES.ADICIONARPACIENTES' | translate }}</span>
        </button>
      </div>

      <div class="toggles-container">
          <mat-slide-toggle class="toggle-item" [(ngModel)]='Foto'>
            {{ 'TELAPESQUISAPACIENTES.MOSTRARFOTO' | translate }}
          </mat-slide-toggle>
          <mat-slide-toggle class="toggle-item" [(ngModel)]='inativos' (change)="GetListaPaciente()">
            {{ 'TELAPESQUISAPACIENTES.INATIVOS' | translate }}
          </mat-slide-toggle>
        </div>
    </div>

    <!-- LISTA DE PACIENTES - DESKTOP VIEW -->
    <div class="lista-container desktop-view">
      <div class="lista-scroll">
        <div class="paciente-card" *ngFor="let item of DadosTab">
          <!-- INFO DO PACIENTE COM FOTO -->
          <div class="paciente-info" *ngIf="Foto">
            <div class="paciente-avatar">
              <img *ngIf="item.sexo == 'Feminino'" src="{{item.imagem64 == null ? 'assets/build/img/userdefault.png' : item.imagem64}}" class="img-circle" alt="Foto do paciente">
              <img *ngIf="item.sexo != 'Feminino'" src="{{item.imagem64 == null ? 'assets/build/img/userdefault.png' : item.imagem64}}" class="img-circle" alt="Foto do paciente">
            </div>
            <div class="paciente-detalhes">
              <div class="info-item">
                <mat-icon>face</mat-icon>
                <span class="nome">{{item!.nome! | truncate : 15 : "…"}}</span>
              </div>
              <div class="info-item">
                <mat-icon>cake</mat-icon>
                <span>{{ item.dtaNascimento ? (item.dtaNascimento | date: 'dd/MM/yyyy') : 'Não informado' }}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span title="{{item.email}}">{{item.email ? (item.email | truncate : 15 : "…") : 'Não informado'}}</span>
              </div>
            </div>
          </div>

          <!-- INFO DO PACIENTE SEM FOTO -->
          <div class="paciente-info" *ngIf="!Foto">
            <div class="paciente-detalhes sem-foto">
              <div class="info-item">
                <mat-icon>face</mat-icon>
                <span class="nome">{{item!.nome! | truncate : 17 : "…" }}</span>
              </div>
              <div class="info-item">
                <mat-icon>cake</mat-icon>
                <span>{{ item.dtaNascimento ? (item.dtaNascimento | date: 'dd/MM/yyyy') : 'Não informado' }}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span title="{{item.email}}">{{item.email ? (item.email | truncate : 15 : "…") : 'Não informado'}}</span>
              </div>
            </div>
          </div>

          <!-- DADOS PESSOAIS -->
          <div class="paciente-dados" [ngClass]="{'com-foto': Foto, 'sem-foto': !Foto}">
            <div class="dados-item">
              <label class="dados-label">CPF</label>
              <span class="dados-valor">{{item.cpf}}</span>
            </div>
            <div class="dados-item">
              <label class="dados-label">{{ 'TELAPESQUISAPACIENTES.TELEFONE' | translate }}</label>
              <span class="dados-valor">{{item.tel}}</span>
            </div>
            <div class="dados-item" *ngIf="item.Spec != null">
              <label class="dados-label">Especialidade</label>
              <span class="dados-valor">{{item.Spec}}</span>
            </div>
            <div class="dados-item">
              <label class="dados-label">{{ 'TELAPESQUISAPACIENTES.DATADECADASTRO' | translate }}</label>
              <span class="dados-valor">{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
            </div>
          </div>

          <!-- AÇÕES -->
          <div class="paciente-acoes">
            <button mat-icon-button matTooltip="Perfil" *ngIf="permissaoBotaoPerfil" (click)="PerfilPaciente(item)">
              <mat-icon>account_circle</mat-icon>
            </button>
            <button mat-icon-button matTooltip="{{ 'TELAPESQUISAPACIENTES.LIFELINE' | translate }}" (click)="AbrirLifeLine(item.idCliente!)">
              <mat-icon>monitor_heart</mat-icon>
            </button>
            <button mat-icon-button matTooltip="{{ 'TELAPESQUISAPACIENTES.CRIARAGENDAMENTO' | translate }}" *ngIf="!inativos" (click)="ModalAgendarConsulta(item.idCliente, item.nome)">
              <mat-icon>list</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Exportar Life Line" *ngIf="!inativos" (click)="ModalExportarPdf(item.idCliente!, item.nome!)">
              <mat-icon>picture_as_pdf</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Motivo da Inativação" *ngIf="inativos" (click)="AbrirModalMotivoExclusao(item.idCliente)">
              <mat-icon>info</mat-icon>
            </button>
            <button mat-icon-button [matTooltip]="item.flgEmail ? 'TELAPESQUISAPACIENTES.EMAILENVIADO' : 'TELAPESQUISAPACIENTES.EMAILDEBOASVINDAS' | translate" (click)="ModalEmail(item)">
              <mat-icon *ngIf="!item.flgEmail">mail_outline</mat-icon>
              <mat-icon *ngIf="item.flgEmail">mark_email_read</mat-icon>
            </button>
            <button mat-icon-button matTooltip="{{ 'TELAPESQUISAPACIENTES.EDITAR' | translate }}" (click)="editUsuario(item.idCliente)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button class="remove" matTooltip="Inativar" *ngIf="!inativos" (click)="ValorUsuario(item.idCliente)">
              <mat-icon>delete</mat-icon>
            </button>
            <button mat-icon-button matTooltip="{{ 'TELAPESQUISAPACIENTES.ATIVAR' | translate }}" *ngIf="inativos" (click)="ValorUsuarioAtivar(item.idCliente)">
              <mat-icon>check</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="DadosTab?.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhum paciente encontrado</p>
        </div>
        
        <!-- BOTÃO CARREGAR MAIS -->
        <div class="carregar-mais" *ngIf="(DadosTab != undefined && DadosTab.length > 0) && bOcultaCarregaMais == false">
          <button mat-flat-button class="btn-carregar" (click)="GetListaPaciente(true)">
            {{ 'TELAPESQUISAPACIENTES.CARREGARMAIS' | translate }}
          </button>
        </div>
      </div>
    </div>

    <!-- LISTA DE PACIENTES - MOBILE VIEW -->
    <div class="lista-mobile mobile-view">
      <mat-card class="paciente-card-mobile" *ngFor="let item of DadosTab; let i = index">
        <div class="paciente-header-mobile">
          <div class="paciente-avatar-mobile" *ngIf="Foto">
            <img *ngIf="item.sexo == 'Feminino'" src="{{item.imagem64 == null ? 'assets/build/img/userdefault.png' : item.imagem64}}" class="img-circle" alt="Foto do paciente">
            <img *ngIf="item.sexo != 'Feminino'" src="{{item.imagem64 == null ? 'assets/build/img/userdefault.png' : item.imagem64}}" class="img-circle" alt="Foto do paciente">
          </div>
          <div class="paciente-info-mobile">
            <h3 class="paciente-nome-mobile">{{item.nome}}</h3>
            <p class="paciente-titulo-mobile">{{ 'TELAPESQUISAPACIENTES.PACIENTE' | translate }}</p>
          </div>
        </div>

        <mat-divider></mat-divider>

        <div class="paciente-dados-mobile">
          <div class="dados-mobile-item">
            <h4 class="Title-b">{{ 'TELAPESQUISAPACIENTES.TELEFONE' | translate }}</h4>
            <p>{{item.tel}}</p>
          </div>
          <div class="dados-mobile-item">
            <h4 class="Title-b">Email</h4>
            <p>{{item!.email! | truncate : 12 : "…"}}</p>
          </div>
          <div class="dados-mobile-item">
            <h4 class="Title-b">{{ 'TELAPESQUISAPACIENTES.DATADECADASTRO' | translate }}</h4>
            <p>{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</p>
          </div>
        </div>

        <!-- AÇÕES MOBILE -->
        <div class="acoes-mobile">
          <div class="acoes-buttons" [@openClose]="toggle[i] ? 'open': 'closed'">
            <button mat-mini-fab (click)="PerfilPaciente(item)" *ngIf="permissaoBotaoPerfil" matTooltip="Perfil">
              <mat-icon>account_circle</mat-icon>
            </button>
            <button mat-mini-fab (click)="AbrirLifeLine(item.idCliente!)" matTooltip="{{ 'TELAPESQUISAPACIENTES.LIFELINE' | translate }}">
              <mat-icon>monitor_heart</mat-icon>
            </button>
            <button mat-mini-fab (click)="ModalAgendarConsulta(item.idCliente, item.nome)" matTooltip="{{ 'TELAPESQUISAPACIENTES.CRIARAGENDAMENTO' | translate }}">
              <mat-icon>list</mat-icon>
            </button>
            <button mat-mini-fab (click)="ModalEmail(item)" [matTooltip]="item.flgEmail ? 'TELAPESQUISAPACIENTES.EMAILENVIADO' : 'TELAPESQUISAPACIENTES.EMAILDEBOASVINDAS' | translate">
              <mat-icon *ngIf="!item.flgEmail">mail_outline</mat-icon>
              <mat-icon *ngIf="item.flgEmail">mark_email_read</mat-icon>
            </button>
            <button mat-mini-fab (click)="editUsuario(item.idCliente)" matTooltip="{{ 'TELAPESQUISAPACIENTES.EDITAR' | translate }}">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-mini-fab *ngIf="!inativos" (click)="ValorUsuario(item.idCliente)" matTooltip="Inativar">
              <mat-icon>delete</mat-icon>
            </button>
            <button mat-mini-fab *ngIf="inativos" (click)="ValorUsuarioAtivar(item.idCliente)" matTooltip="{{ 'TELAPESQUISAPACIENTES.ATIVAR' | translate }}">
              <mat-icon>check</mat-icon>
            </button>
          </div>
          <button mat-fab class="toggle-button" (click)="openToggle(i)">
            <mat-icon>{{toggle[i] ? 'close' : 'more_vert'}}</mat-icon>
          </button>
        </div>
      </mat-card>
      
      <!-- BOTÃO CARREGAR MAIS MOBILE -->
      <div class="carregar-mais" *ngIf="(DadosTab != undefined && DadosTab.length > 0) && bOcultaCarregaMais == false">
        <button mat-flat-button class="btn-carregar" (click)="GetListaPaciente(true)">
          {{ 'TELAPESQUISAPACIENTES.CARREGARMAIS' | translate }}
        </button>
      </div>
    </div>
  </mat-card>
</div>

<!-- MODAL EMAIL -->
<ngx-smart-modal #emailUsuario identifier="emailUsuario" customClass="nsm-centered medium-modal emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Email de Acesso</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAPACIENTES.VOCETEMCERTEZAQUEDESEJAENVIARESSEEMAIL' | translate }}</p>
      <p class="modal-subtexto">{{ 'TELAPESQUISAPACIENTES.OUSUARIORECEBERAOEMAILCOMASENHA' | translate }}</p>
      
      <div class="alerta-inativo" *ngIf="inativos">
        <mat-icon>warning</mat-icon>
        <p>{{ 'TELAPESQUISAPACIENTES.CADASTROINATIVO' | translate }}</p>
        <p class="alerta-subtexto">{{ 'TELAPESQUISAPACIENTES.ATIVARNOVAMENTE' | translate }}</p>
      </div>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="emailUsuario.close()">
        {{ 'TELAPESQUISAMEDICO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" *ngIf="!inativos" (click)="mandaEmail()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" *ngIf="inativos" (click)="mandaEmailAtivarUser()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL EXCLUIR -->
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Confirmação</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">Você tem certeza que deseja inativar este usuário?</p>
      
      <mat-form-field appearance="outline" floatLabel="always" class="motivo-field">
        <mat-label>Motivo da Inativação</mat-label>
        <textarea matInput required name="motivoExclusao" [(ngModel)]="motivoExclusao"
          (blur)="MotivoExclusaoCampo()" (keydown)="campoExclusaoPreenchido = true"
          maxlength="200"></textarea>
        <mat-hint align="end">{{motivoExclusao.length + 0}}/200</mat-hint>
        <mat-error *ngIf="campoExclusaoPreenchido == false">Motivo deve ser preenchido</mat-error>
      </mat-form-field>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="excluirItem.close()">
        {{ 'TELAPESQUISAPACIENTES.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-excluir" (click)="InativarUsuario()">
        {{ 'TELAPESQUISAPACIENTES.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL ATIVAR -->
<ngx-smart-modal #ativarItem identifier="ativarItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Ativar Cadastro</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAPACIENTES.ATIVARESTEUSUARIO' | translate }}</p>
      <p class="modal-subtexto">{{ 'TELAPESQUISAPACIENTES.OUSUARIOTERAACESSOAOSISTEMA' | translate }}</p>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="ativarItem.close()">
        {{ 'TELAPESQUISAPACIENTES.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" (click)="AtivarUsuario()">
        {{ 'TELAPESQUISAPACIENTES.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL MOTIVO EXCLUSÃO -->
<ngx-smart-modal #MotivoExclusao identifier="MotivoExclusao" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Motivo da Inativação</h3>
    </div>
    <div class="modal-body">
      <mat-form-field appearance="outline" class="motivo-field">
        <mat-label>Inativado por:</mat-label>
        <input matInput disabled [(ngModel)]="usuarioExclusao">
      </mat-form-field>
      <mat-form-field appearance="outline" class="motivo-field">
        <mat-label>Motivo da Inativação</mat-label>
        <input matInput disabled [(ngModel)]="motivoDaExclusao">
      </mat-form-field>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="MotivoExclusao.close()">
        <mat-icon>clear</mat-icon> Sair
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL AGENDAMENTO -->
<ngx-smart-modal #ModalAgenda identifier="ModalAgenda" [dismissable]="false" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>{{ 'TELAAGENDA.ESCOLHAOMEDICO' | translate }}</h3>
    </div>
    <div class="modal-body">
      <div class="modal-form">
        <ng-select class="select-field" [dropdownPosition]="'top'" [items]="DadosEspecialidade"
          placeholder="{{ 'TELAAGENDA.FILTRARESPECIALIDADE' | translate }}" bindLabel="desEspecialidade"
          (change)="CarregaMedicos()" bindValue="idEspecialidade" name="especialidade" [selectOnTab]="true"
          [(ngModel)]="idEspecialidade">
        </ng-select>
        
        <ng-select class="select-field" [dropdownPosition]="'top'" [items]="ListaMedicos"
          placeholder="{{ 'TELAAGENDA.MEDICOS' | translate }}" bindLabel="nomeMedico" bindValue="idMedico"
          name="medicos" [selectOnTab]="true" [(ngModel)]="idMedico">
        </ng-select>
      </div>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-confirmar" (click)="AgendarConsulta()">
        {{ 'TELAPESQUISAPACIENTES.AGENDAR' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>