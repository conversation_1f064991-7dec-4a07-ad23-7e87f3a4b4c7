.toggle {
    display: inline-block;
    width: 40px;
    height: 20px;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
}

.toggle input {
    display: none; 
}

.slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s; 
}

.toggle input:checked + .slider {
    transform: translateX(20px);
}

.Inativo {
    background-color: #ccc;
}

.Ativo {
    background-color: #2196F3; 
}

.toggle:hover {
    filter: brightness(0.9);
}

.Disabled {
    filter: brightness(0.8) grayscale(60%);
    transform: none ;
    pointer-events: none; 
    cursor: none;
}

.Toggle{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}

span{
    font-size: 16px;
    font-weight: 500;
    max-width: 200px;
    text-wrap: wrap;
    word-wrap: break-word;
}

mat-icon{
    padding-left: 5px;
    font-size: 24px;
    overflow: visible;
    color: #5260ff;
    justify-self: end;
    position: absolute;
}

mat-icon:hover{
    cursor: pointer;
}

.Complementos{
    flex: 100%;
    display: grid; 
    align-items: center;
    text-align: center;
    justify-content: center;
}
