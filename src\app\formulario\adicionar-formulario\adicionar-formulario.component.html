<div class="form-container  mother-div">
    <!-- Modal para salvar formulário -->
    <div *ngIf="flgmodal_salvar" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">{{ objFormulario.nomeFormulario || 'Novo Formulário' }}</h2>
            </div>
            <div class="modal-body">
                <p>Deseja realmente salvar este formulário?</p>
            </div>
            <div class="modal-footer">
                <button (click)="salvar(false)" class="btn btn-outline">Cancelar</button>
                <button (click)="salvar(true)" class="btn btn-primary">Salvar</button>
            </div>
        </div>
    </div>

    <!-- Modal para nova pergunta -->
    <div *ngIf="flgmodal_pergunta" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">Nova Pergunta</h2>
            </div>
            <div class="modal-body">
                <div class="form-group col-md-6">
                    <mat-form-field appearance="outline" class="full-width-text">
                        <mat-label>Texto da pergunta</mat-label>
                        <input matInput placeholder="Digite o texto da pergunta" [(ngModel)]="objPergunta.pergunta">
                    </mat-form-field>
                </div>
                <div class="form-group">
                    <mat-form-field appearance="outline" class="full-width">
                        <mat-label>Tipo de resposta</mat-label>
                        <mat-select [(ngModel)]="objPergunta.idTipoResposta">
                            <mat-option *ngFor="let tipoResp of listTipoReposta" [value]="tipoResp.idTipoResposta">
                                {{ tipoResp.tipoReposta }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
            </div>
            <div class="modal-footer">
                <button (click)="adicionaPergunta(false)" class="btn btn-outline">Cancelar</button>
                <button (click)="adicionaPergunta(true)" class="btn btn-primary">Adicionar</button>
            </div>
        </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="card main-card">
        <div class="card-header">
            <div class="header-left">
                <div class="icon-container">
                    <span class="material-icons">description</span>
                </div>
                <h1 class="page-title">Adicionar Formulário</h1>
            </div>
            <button class="btn btn-link" onclick='history.go(-1)'>
                <span class="material-icons">arrow_back</span>
            </button>
        </div>

        <div class="card-body">
            <!-- Seção de dados do formulário -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">Dados do Formulário</h2>
                </div>
                <div class="section-content">
                    <div class="form-group">
                        <mat-form-field appearance="outline" class="full-width nome">
                            <mat-label>Nome do Formulário</mat-label>
                            <input matInput placeholder="Digite o nome do formulário" [(ngModel)]="objFormulario.nomeFormulario">
                        </mat-form-field>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-6">
                            <ng-select 
                                [items]="objTipoOrientacao" 
                                bindLabel="tipoOrientacao"
                                bindValue="idTipoOrientacao" 
                                placeholder="Selecione um tipo de orientação"
                                [(ngModel)]="id_tipoOrientacao"
                                class="modern-select">
                            </ng-select>
                        </div>
                        <div class="form-group col-md-6 date">
                            <mat-form-field appearance="outline" class="date-field">
                                <mat-label>Data de criação</mat-label>
                                <input matInput disabled [(ngModel)]="dtCreate">
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Seção de perguntas do formulário -->
            <div class="section">
                <div class="section-header">
                    <h2 class="section-title">Perguntas do Formulário</h2>
                </div>
                <div class="section-content">
                    <div class="questions-container">
                        <div cdkDropList class="questions-list" (cdkDropListDropped)="drop($any($event))">
                            <div class="question-item" *ngFor="let pergunta of listaPergunta" cdkDrag>
                                <div class="example-custom-placeholder" *cdkDragPlaceholder></div>
                                <div class="question-content">
                                    <div class="drag-handle">
                                        <span class="material-icons" cdkDragHandle>drag_indicator</span>
                                    </div>
                                    <div class="question-text">
                                        <p class="question"><span class="question-label">Pergunta:</span> {{ pergunta.pergunta }}</p>
                                        <p class="question-type">
                                            <span class="question-label">Tipo de resposta:</span> 
                                            {{ pergunta.idTipoResposta == 1 ? "Sim ou não" : 
                                               (pergunta.idTipoResposta == 2 ? "Descritivo" : "Numérico") }}
                                        </p>
                                    </div>
                                    <button class="btn btn-icon" (click)="remove(pergunta)">
                                        <span class="material-icons delete-icon">delete_outline</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="add-question-container">
                            <button class="btn btn-primary btn-add" (click)="modalNovaPergunta()">
                                <span class="material-icons">add</span>
                                Nova Pergunta
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-footer">
            <button class="btn btn-success" (click)='preparaSalvar()'>Salvar Formulário</button>
        </div>
    </div>
</div>