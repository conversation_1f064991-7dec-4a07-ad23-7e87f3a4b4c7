<main>
    <div class="divAgenda">
        <h2>Calendario</h2>
        <div class="divBotoes">
            <button type="button" class="btn btn-primary" (click)="ModalAgendaEspera()">Agenda de espera</button>
            <button type="button" class="btn btn-primary" (click)="AbrirModalAgendamento()">Criar encaixe</button>
        </div>
        <div class="escolherMedico" *ngIf="idTipoUsuarioLogado == 1 || idTipoUsuarioLogado == 3">
            <div class="mb-3 select-medico-personalizado">
                <span for="selectMedico" class="form-label">Selecione um Médico</span>
                <select id="selectMedico" class="form-select" [(ngModel)]="idMedico" (change)="onMedicoChange()">
                    <option *ngFor="let medico of lsMedicos" [value]="medico.idMedico">
                        {{ medico.nome }}
                    </option>
                </select>
            </div>
        </div>
    </div>
<app-calendario [agendamentos]="agendamentos" (diaTrocado)="onDiaTrocado($event)" (mesTrocado)="onMesTrocado($event)"
(horarioClicado)="onHorarioClicado($event)" (agendamentoClicado)="onAgendamentoClicado($event)" />
</main>
