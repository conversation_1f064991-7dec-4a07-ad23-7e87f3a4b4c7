// Variáveis - Esquema de cores verde
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; 
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

:host {
  padding: 24px;
}

// Header do modal
.modal-header {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.modal-title {
  color: white; /* Definindo explicitamente como branco ao invés de usar variável */
  margin: 0;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  font-size: 20px;
  z-index: 10; /* Garantindo que o título esteja acima de outros elementos */
  pointer-events: none; /* Evitando problemas com cliques */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Adicionando sombra para melhorar legibilidade */
}

// HR e divisores
hr {
  border-color: $border-color;
  margin-bottom: 20px;
}

// Grupos de formulário
.form-group {
  margin-bottom: 15px;
}

.form-label {
  font-size: small;
  padding-left: 1em;
  color: $text-secondary;
  display: block;
  margin-bottom: 5px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid $border-color;
  border-radius: 4px;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba($primary-color, 0.2);
  }
}

// Mensagens de erro
.error-text {
  font-size: smaller;
  color: $error-color;
  text-align: center;
  margin: 5px 0;
}

// Botão primário
.btn-primary {
  background-color: $primary-color !important;
  color: white !important;
  padding: 8px 20px;
  margin-top: 15px;
  border-radius: 4px;
  
  &:hover {
    background-color: $primary-dark !important;
  }
}

// Utilitários
.w-100 {
  width: 100%;
}

.mt-3 {
  margin-top: 15px;
}

// Estilos do Angular Material
::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color !important;
  }
  
  .mat-form-field-label {
    color: $text-secondary !important;
  }
  
  .mat-form-field.mat-focused .mat-form-field-label {
    color: $primary-color !important;
  }
  
  .mat-select-value {
    color: $text-primary !important;
  }
  
  .mat-select-arrow {
    color: $primary-color !important;
  }
  
  .mat-option.mat-selected:not(.mat-option-disabled) {
    color: $primary-color !important;
  }
}

// Responsividade
@media (max-width: 600px) {
  [style*="display: flex; justify-content: space-evenly"] {
    flex-direction: column;
    gap: 10px;
  }
  
  .btn-primary {
    width: 100%;
  }
}
.modal-container > .modal-container{
  padding: 20px;
}
.btn-primary{
  box-shadow: none;
}
.btn-primary.cancelar{
  background-color: #d6d6d6 !important; 
  &:hover{ background-color: #9d9d9d !important;
  }
}
.modal-footer{
  display: flex;
  justify-content: center;
  padding: 16px 20px;
  background-color: #F4F4F9;
  gap: 12px;
  border-top: 1px solid #dee2e6;
}