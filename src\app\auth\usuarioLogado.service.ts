import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { UsuarioLogado } from './UsuarioLogado';
import { Clinica } from '../model/clinica';
import { LocalStorageService } from '../service/LocalStorageService';



@Injectable({ providedIn: 'root' })
export class UsuarioLogadoService {

    private userSubject = new BehaviorSubject<UsuarioLogado | null>(null);


    private idPessoa?: number;
    private idUsuarioAcesso?: number;
    private nome?: string;
    private cpf?: string;
    private email?: string;
    private idTipoUsuario?: number;
    private flgPrimeiroAcesso?: boolean;
    private FlgHabilitaChat?: boolean;
    private FlgFilaAcesso?: boolean;
    private FlgSolicitaOrientacao?: boolean;
    private FlgProntuario?: boolean;
    private tokenLogado?: string;
    private flgLogado?: boolean;
    private idUltimaClinica?: number;
    private FlgHabilitaPagamento?: boolean;
    private FlgReuniao?: boolean;
    private idMedico?: number;
    private idTipoOrientacao?: number;



    private clinicas?: Clinica[];
    // private idUsuarioGerador: number;
    private imagem64?: string;



    constructor(private localStorageService: LocalStorageService,) {
        this.localStorageService.hasUsuarioLogado() &&
            this.decodeAndNotify();

    }

    setUsuarioLogado(usuario: any) {
        this.localStorageService.setUsuarioLogado(usuario);
        this.decodeAndNotify();
    }

    getUsuarioLogado() {
        return this.userSubject.asObservable();
    }

    private decodeAndNotify() {
        const user = JSON.parse(this.localStorageService.getUsuarioLogado());
        this.idPessoa = user.idPessoa;
        this.idUsuarioAcesso = user.idUsuarioAcesso;
        this.nome = user.nome;
        this.email = user.email;
        this.cpf = user.cpf;
        this.imagem64 = user.imagem64;
        this.idTipoUsuario = user.idTipoUsuario;
        this.flgPrimeiroAcesso = user.flgPrimeiroAcesso;
        this.idUltimaClinica = user.idUltimaClinica;
        this.clinicas = user.clinicas;
        this.tokenLogado = user.tokenLogado;
        this.flgLogado = user.flgLogado;
        this.FlgProntuario = user.flgProntuario;
        this.FlgHabilitaChat = user.flgHabilitaChat;
        this.FlgFilaAcesso = user.flgFilaAcesso;
        this.FlgSolicitaOrientacao = user.flgSolicitaOrientacao;
        this.FlgHabilitaPagamento = user.flgHabilitaPagamento;
        this.FlgReuniao = user.flgReuniao;
        this.idMedico = user.idMedico;
        this.idTipoOrientacao = user.idTipoOrientacao;

        this.userSubject.next(user);
    }


    logout() {
        this.localStorageService.clearByName("UserLogado");
        this.userSubject.next(null);
    }

    isLogged() {
        return this.localStorageService.hasUsuarioLogado();
    }

    getIdPessoa() {
        return this.idPessoa;
    }

    getFlgProntuario() {
        return this.FlgProntuario;
    }
    getIdUsuarioAcesso() {
        return this.idUsuarioAcesso;
    }
    getNomeUsuario() {
        return this.nome;
    }

    getCpf() {
        return this.cpf;
    }

    getEmail() {
        return this.email;
    }

    getIdTipoUsuario() {
        return this.idTipoUsuario;
    }

    getImagem64() {
        return this.imagem64;
    }

    getIdUltimaClinica() {
        return this.idUltimaClinica;
    }

    getFlgPrimeiroAcesso() {
        return this.flgPrimeiroAcesso;
    }

    setTokenLogado() {
        return this.tokenLogado;
    }


    getFlgLogado() {
        return this.flgLogado;
    }

    getTokenLogado() {
        return this.tokenLogado;
    }

    getClinicas() {
        return this.clinicas;
    }

    getFlgFilaAcesso() {
        return this.FlgFilaAcesso;
    }
    setFlgFilaAcesso() {
        return this.FlgFilaAcesso;
    }
    getFlgSolicitaOrientacao() {
        return this.FlgSolicitaOrientacao;
    }
    setFlgSolicitaOrientacao() {
        return this.FlgSolicitaOrientacao;
    }

    getFlgReuniao() {
        return this.FlgReuniao;
    }
    setFlgReuniao() {
        return this.FlgReuniao;
    }

    getFlgHabilitaChat() {
        return this.FlgHabilitaChat;
    }
    setFlgHabilitaChat() {
        return this.FlgHabilitaChat;
    }
    getFlgHabilitaPagamento() {
        return this.FlgHabilitaPagamento;
    }
    setFlgHabilitaPagamento() {
        return this.FlgHabilitaPagamento;
    }
    getCPFUsuario() {
        return this.cpf
    }

    getIdMedico() {
        return this.idMedico;
    }

    getIdTipoOrientacao() {
        return this.idTipoOrientacao;
    }

    public clear() {
        localStorage.clear();
        sessionStorage.clear();
    }
}

