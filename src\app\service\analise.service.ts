import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from '@angular/common/http';
import { msgResposta } from "../model/retorno-resposta";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs";
import { tipoOrientacao } from "../model/itens";
import { Analise, AnaliseMensagemModelView, ExamesAnaliseModelView } from "../model/analise";
import { SpinnerService } from "./spinner.service";
import { ExamesModelView } from "../model/exames";

@Injectable({
    providedIn: 'root'
})
export class AnaliseService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) { }

    public SalvarAnalise(objItem: Analise): Observable<msgResposta> {
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/Analise/AdicionarAnalise', objItem)
    }
    
    public SalvarAnalisePaciente(objItem: Analise): Observable<msgResposta> {
        this.spinner.show();        
        return this.http.post<msgResposta>(environment.apiEndpoint + '/Analise/AdicionarAnalisePaciente', objItem)
    }
    
    public SalvarArquivo(objItem: ExamesAnaliseModelView): Observable<msgResposta> {
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/Analise/SalvarArquivo', objItem)
    }
    
    public GetQuantidadeAnalises(IdTipo: number): Observable<number> {
        this.spinner.show();
        return this.http.post<number>(environment.apiEndpoint + '/Analise/GetQuantidadeAnalises', IdTipo)
    }
    public GetAnalises() {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Analise/GetAnalises')
    }
    
    public deleteItem(id: number) {
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/item/DeleteItem', id)
    }
    
    public GetObjAnalise(IdItem: number) {
        this.spinner.show();
        return this.http.post<any>(environment.apiEndpoint + '/Analise/GetAnalise', IdItem)
    }
    
    public GetTipoOrientacao() {
        this.spinner.show();
        return this.http.get<tipoOrientacao[]>(environment.apiEndpoint + '/item/GetTipoOrientacao')
    }
    
    public GetAnalisesPaciente(IdItem: number) {
        this.spinner.show();
        return this.http.post<any>(environment.apiEndpoint + '/Analise/GetAnalisesPaciente', IdItem)
    }
    
    public GetOrientacaoPaciente(IdItem: number, idTipoOrientacao: number) {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('id', String(IdItem));
        params = params.append('idTipoOrientacao', String(idTipoOrientacao));
        return this.http.get<any>(environment.apiEndpoint + '/Analise/GetOrientacaoPaciente', { params })
    }
    
    public SolicitarAnalise(objItem: Analise) {
        this.spinner.show();
        return this.http.post<any>(environment.apiEndpoint + '/Analise/SolicitarAnalise', objItem)
    }
    
    public ListaExamesParaAnalise() {
        this.spinner.show();
        return this.http.get<ExamesModelView[]>(environment.apiEndpoint + '/Analise/ListaExamesParaAnalise')
    }
    
    public GetExamesAnalise(IdItem: number) {
        return this.http.post<any>(environment.apiEndpoint + '/Analise/GetExamesAnalise', IdItem)
    }
    
    public BaixarArquivoExame(arquivo: any): Observable<any> {
        this.spinner.show();
        
        let params = new HttpParams();
        params = params.append('arquivo', String(arquivo));
        return this.http.get(environment.apiEndpoint + "/Analise/DownloadArquivo", { params, responseType: 'arraybuffer' });
    }
    
    public salvaMensagemInterna(objMensagem: AnaliseMensagemModelView) {
        this.spinner.show();
        return this.http.post<boolean>(environment.apiEndpoint + '/Analise/salvaMensagemInterna', objMensagem)
    }
    
    public getMensaems(idAnalise: number) {
        this.spinner.show();
        return this.http.post<AnaliseMensagemModelView[]>(environment.apiEndpoint + '/Analise/GetAllMensagensInternas', idAnalise)
    }
    
    public getMentsagensImportantes(idAnalise: number) {
        this.spinner.show();
        return this.http.post<AnaliseMensagemModelView[]>(environment.apiEndpoint + '/Analise/GetAllMensagensInternasImportantes', idAnalise)
    }
}