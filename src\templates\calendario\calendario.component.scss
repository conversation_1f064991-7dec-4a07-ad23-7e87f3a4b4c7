// Variables baseadas na paleta fornecida
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Estilo Container Principal */
.calendar-container {
  width: 100%;
  max-width: 1200px;
  height: 70vh;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  padding: 1rem;
  background-color: $bg-color;
  gap: 1rem;
  border-radius: 10px;
  @media (max-width: 1000px) {
    padding: 0 !important;
  }
}

/* Estilos dos Botões de Controle */
.calendar-controls {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 500;
  border: none;
  border-radius: $border-radius;
  padding: 0.625rem 1rem;
  cursor: pointer;
  transition: all $transition ease;
  background-color: $card-bg;
  color: $text-primary;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  &:hover {
    background-color: $secondary-light;
  }
  
  i {
    margin-right: 0.5rem;
  }
}

.btn-switch {
  background-color: $primary-light;
  color: $primary-color;
  
  &:hover {
    background-color: lighten($primary-light, 2%);
  }
}

.btn-today {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: $primary-dark;
  }
}

/* Estilos Comuns para Calendar e Schedule */
.calendar,
.schedule {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid $border-color;
}

.calendar-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: $text-primary;
  text-transform: capitalize;
}

.nav-btn {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  color: $text-primary;
  cursor: pointer;
  transition: background-color $transition;
  
  &:hover {
    background-color: $secondary-light;
    color: $primary-color;
  }
  
  i {
    font-size: 1.25rem;
  }
}

/* Estilos do Calendário Mensal */
.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 1rem 0.75rem 0.5rem;
  background-color: $card-bg;
}

.weekday {
  font-size: 0.875rem;
  font-weight: 500;
  color: $text-secondary;
  text-align: center;
  text-transform: uppercase;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  flex: 1;
  gap: 0.25rem;
  padding: 0.5rem;
}

.day {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 5rem;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: background-color $transition;
  border: 1px solid transparent;
  
  &:hover {
    background-color: $secondary-light;
    border-color: $border-color;
  }
  
  &.today {
    background-color: $primary-light;
    
    .day-number {
      color: $primary-color;
      font-weight: 600;
    }
  }
  
  &.other-month {
    opacity: 0.5;
  }
}

.day-number {
  align-self: flex-end;
  font-size: 0.875rem;
  font-weight: 500;
  color: $text-primary;
  margin-bottom: 0.5rem;
}

.events-indicator {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: auto;
}

.event-dot {
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
  display: inline-block;
}

.events-more {
  font-size: 0.75rem;
  background-color: $secondary-color;
  color: $text-secondary;
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Estilos da Agenda Diária */
.schedule-content {
  display: flex;
  flex: 1;
  overflow-y: auto;
}

.time-column {
  width: 5rem;
  flex-shrink: 0;
  border-right: 1px solid $border-color;
  background-color: $card-bg;
  z-index: 1;
}

.events-column {
  flex: 1;
  position: relative;
}

.time-slot {
  position: relative;
  height: 3.75rem;
}

.time-label {
  display: block;
  padding: 0.25rem 0.75rem;
  text-align: right;
  font-size: 0.75rem;
  color: $text-secondary;
  position: relative;
  top: -0.5rem;
}

.time-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.hour-line {
  position: absolute;
  top: 0;
  width: 100%;
  height: 1px;
  background-color: $border-color;
}

.half-hour-line {
  position: absolute;
  top: 50%;
  width: 100%;
  height: 1px;
  background-color: lighten($border-color, 5%);
}

.events-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
}

.event {
  position: absolute;
  border-radius: 6px;
  padding: 0.375rem 0.5rem;
  color: white;
  font-size: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: transform $transition;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  }
}

.event-time {
  font-weight: 600;
  font-size: 0.75rem;
  margin-bottom: 0.125rem;
  white-space: nowrap;
}

.event-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Media queries para responsividade */
@media (max-width: 768px) {
  .calendar-container {
    height: auto;
    min-height: 80vh;
  }
  
  .day {
    min-height: 4.5rem;
  }
  
  .calendar-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .btn {
    width: 100%;
  }
  
  .time-column {
    width: 3.5rem;
  }
  
  .time-label {
    font-size: 0.7rem;
    padding: 0.25rem 0.25rem;
  }
}

/* Ícones (placeholder - você precisará adicionar os ícones reais) */
.icon-calendar, 
.icon-agenda, 
.icon-chevron-left, 
.icon-chevron-right {
  display: inline-block;
  width: 1rem;
  height: 1rem;
}

/* Você pode substituir isso por um sistema de ícones como FontAwesome ou ícones SVG */
.icon-chevron-left:before {
  content: "←";
}

.icon-chevron-right:before {
  content: "→";
}

.icon-calendar:before {
  content: "📅";
}

.icon-agenda:before {
  content: "📋";
}