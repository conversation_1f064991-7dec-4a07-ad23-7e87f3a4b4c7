import { Injectable } from "@angular/core";
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UsuarioLogadoService } from '../usuarioLogado.service';


@Injectable({ providedIn: 'root' })
export class MenuGuard {

    constructor(
        private router: Router,
        private usuarioLogadoService: UsuarioLogadoService

    ) { }

    canActivate(): boolean | Observable<boolean> | Promise<boolean> {
            
        if (!this.usuarioLogadoService.isLogged()) {
            this.router.navigate(['login']);
            return false;
        }
        return true
    }
}


