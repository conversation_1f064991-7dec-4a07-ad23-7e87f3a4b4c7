import { Injectable, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
//import { Observable } from 'rxjs/Observable';

@Injectable({
    providedIn: 'root'
})
export class FileuploadArquivoService implements OnDestroy {

	public lastFileUpload?: File;

	constructor(private http: HttpClient) { }

	//postFile(fileToUpload: File): Observable<boolean> {
	//  const endpoint = 'your-destination-url';
	//  const formData: FormData = new FormData();
	//  formData.append('fileKey', fileToUpload, fileToUpload.name);
	//  return this.http
	//    .post(endpoint, formData)
	//    .map(() => { return true; })
	//    .catch((e) => this.handleError(e));
	//}

	ngOnDestroy(): void {



	}

	public saveImportacao(msgImportacao: any): Promise<any> {
		return this.http
			.post(environment.apiEndpoint + '/Importacao', msgImportacao)
			.toPromise();
	}

	public postFile(fileToUpload: File): Promise<any> {
		;

		const formData: FormData = new FormData();
		formData.append('fileKey', fileToUpload, fileToUpload.name);


		return new Promise<any>((resolve, reject) => {
			this.http
			  .post(environment.apiEndpoint + '/Importacao/UploadArquivoMenuEsquerdo', formData)
			  .toPromise()
			  .then(resp => { resolve(resp); })
			  .catch(err => { reject(err); });
		  });
	}

}
