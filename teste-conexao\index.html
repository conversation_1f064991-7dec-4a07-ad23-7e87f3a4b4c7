<!DOCTYPE html>
<html ng-app="app" lang="pt-br">

<head>
  <meta charset='utf-8'>
  <meta http-equiv='X-UA-Compatible' content='IE=edge'>
  <title>Page Title</title>
  <meta name='viewport' content='width=device-width, initial-scale=1'>
  <link href="testeMicrofone.css" rel="stylesheet">


  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
  <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.7.2/angular.min.js"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/d3js/5.9.7/d3.min.js"></script>
  <script src="jquery.js"></script>


  <link href="index.css" rel="stylesheet">

</head>

<body>



  <div class="container-fluid">
    <div class="row">

      <div class="col-md-12 col-sm-12 col-xs-12 text-center">

        <img src="img/icone-chamada-teste-conexao.png" class="logo" style="width: 10%;">

        <img src="img/Logo-sotext.png" class="logo img-responsive" style="margin-top: -50px;">

      </div>


      <div class="col-md-6 " style="overflow-x: hidden;overflow-y: scroll;height: 60vh; margin-top: 10vh;">
        <div class="col-md-12" style="margin-bottom: 4%;">
          <h5 class="text-primary"> Iniciar sessão de consulta de vídeo </h5>
          <span class="text-muted">

            Configure sua webcam, microfone e rede antes de iniciar a sessão de consulta de vídeo
          </span>

        </div>
        <div class="row">
          <div class="col-md-2 col-sm-2 col-lg-2 col-2" style="padding: 0!Important;">

            <b>1 -</b>

          </div>
          <div class="col-md-10 col-sm-10 col-lg-10 col-10">
            <span>Certifique-se de que seu navegador Web suporta chamadas de vídeo.
            </span><br>
            <span class="text-muted">Conceda permissão ao seu navegador para acessar sua câmera e microfone</span>
          </div>
        </div>
        <div class="row" style="padding: unset;padding-left: 30px;">
          <div class="col-md-2"></div>
          <div class="col-md-10 row"
            style="background-color: white; text-align: center; border: 1px solid; border-color: #1265b9;;  margin-top: 2%; margin-bottom: 4%;">

            <div class="col-md-2">
              <img src=" img/desktop.png" class="logo img-responsive" style="width: 70px;">
            </div>

            <div class="col-md-10" style="padding-top: 10px;">
              No computador, a chamada por vídeo apresenta o melhor desempenho com a versão mais recente do <a
                href="https://www.google.com/chrome/">Google Chrome
              </a>
              ou do <a href="https://www.mozilla.org/en-US/firefox/new/"> Mozilla Firefox</a>
            </div>

          </div>
        </div>
        <div class="row">
          <div class="col-md-2">

            <b></b>

          </div>
          <div class="col-md-10">
            <h6 class="text-primary " style="margin-left: -15px">
              Usando outros dispositivos?</h6>
            <ul>
              <li><small class="text-muted">Para navegadores web do android ou tablets, atualize para a versão mais
                  recente do Chrome ou Firefox. <br>
                  <b>A video chamada no navegador Web do tablet com iOS não é suportada</b></small></li>

              <li><small class="text-muted"> e você está usando um smartphone, baixe the Virtual Practice app,
                  disponivel na <a class="googleplaylink"
                    href="https://play.google.com/store/apps/details?id=com.needstreet.health.hp"> Google Play</a>
                  (Android 4.1 ou superior) ou na
                  <a class="ituneslink"
                    href="https://itunes.apple.com/in/app/mobile-doctor-your-virtual/id648841439?mt=8">App Store </a>
                  (iOS 6 ou superior)
                </small></li>

            </ul>
          </div>
        </div>
        <div class="row">
          <div class="col-md-2 col-sm-2 col-lg-2 col-2" style="padding: 0!Important;">

            <b>2 -</b>

          </div>
          <div class="col-md-10 col-sm-10 col-lg-10 col-10">
            <span class=""><b> Verifique sua webcam e compatibilidade com microfone</b></span><br>
            <span class="text-muted">Verifique se a webcam e os dispositivos de microfone estão conectados corretamente
            </span>
          </div>
          <br>

          <div class="col-md-12 m-t-10" style="padding-left: 30px;">
            <div class="row">
              <div class="col-md-8 col-sm-12">
                <webcam></webcam>
              </div>
              <div class=" col-md-4 col-sm-12">
                <div class="pids-wrapper"
                  style="text-align: center; margin-left: -55px; margin-top: 40px;  display: block;;">

                  <div class="grid_pid colun">
                    <div class="pid pid-0"></div>  
                  </div>

                  <div class="grid_pid colun-0">
                    <div class="pid pid-1"></div>
                    <div class="pid pid-1"></div>
                  </div>

                  <div class="grid_pid colun-1">
                    <div class="pid pid-2"></div>
                    <div class="pid pid-2"></div>
                    <div class="pid pid-2"></div>
                  </div>
                  <div class="grid_pid colun-2">
                    <div class="pid pid-3"></div>
                    <div class="pid pid-3"></div>
                    <div class="pid pid-3"></div>
                    <div class="pid pid-3"></div>
                  </div>
                  <div class="grid_pid colun-3">
                    <div class="pid pid-4"></div>
                    <div class="pid pid-4"></div>
                    <div class="pid pid-4"></div>
                    <div class="pid pid-4"></div>
                    <div class="pid pid-4"></div>
                  </div>

                  <div class="grid_pid colun-4">
                    <div class="pid pid-5"></div>
                    <div class="pid pid-5"></div>
                    <div class="pid pid-5"></div>
                    <div class="pid pid-5"></div>
                    <div class="pid pid-5"></div>
                    <div class="pid pid-5"></div>
                  </div>



                </div>
              </div>
            </div>
          </div>

          <div class="col-md-12" style="margin-top: 20px;">
            <span style="font-size: 11px;"> Nota: Esta página apenas verifica a acessibilidade da sua webcam e microfone
              deste navegador.</span>
          </div>
        </div>
      </div>
      <div class="col-md-6 menor" style="overflow: auto; height: 60vh; margin-top: 10vh;">
        <div class="row">
          <div class="col-md-12 col-sm-12 text-center">
            <h5 class="text-primary">Verifique a qualidade de sua conexão com a internet </h5>
            <span class="text-muted">Descubra sua velocidade de conexão com a Internet e compatibilidade de rede</span>
          </div>



          <div class="col-md-2 col-sm-2 col-lg-2 col-2" style="padding: 0!Important;">

            <b>3 -</b>

          </div>
          <div class="col-md-10 col-sm-10 col-lg-10 text-left">
            Clique em iniciar para ver qual é a sua velocidade e se é abito a usar o mesmo.
          </div>
        </div>

        <!-- início teste conexão -->
        <div class="row" style="padding: unset;padding-left: 30px;">
          <div class="col-md-2"></div>
          <div class="col-md-10 row"
            style="background-color: white; text-align: left; border: 1px solid; border-color: #1265b9;;  margin-top: 2%; margin-bottom: 4%;">
            <div class="col-md-12" style="padding-top: 10px; padding-bottom: 10px; height: 120px;" id="test">
              <span><b>A velocidade da sua intenet atualmente é de
                </b></span><br>
              <div class="row">
                <div class="col-md-8 col-sm-12 col-xs-12">
                  <div class="testGroup">
                    <div class="testArea">
                      <div class="testName">Download</div>
                      <div id="dlText" class="meterText"></div>
                      <div class="unit">Mbps</div>
                    </div>
                    <div class="testArea">
                      <div class="testName">Upload</div>
                      <div id="ulText" class="meterText"></div>
                      <div class="unit">Mbps</div>
                    </div>
                  </div>
                  <div style="display: none;">
                    <div class="testGroup">
                      <div class="testArea">
                        <div class="testName">Ping</div>
                        <div id="pingText" class="meterText"></div>
                        <div class="unit">ms</div>
                      </div>
                      <div class="testArea">
                        <div class="testName">Jitter</div>
                        <div id="jitText" class="meterText"></div>
                        <div class="unit">ms</div>
                      </div>
                    </div>
                    <div id="ipArea">
                      IP Address: <span id="ip"></span>
                    </div>
                  </div>
                </div>
                <div class="col-md-4 col-sm-12 col-xs-12 text-center" style=" margin-top: 10px;">
                  <div id="startStopBtn" onclick="startStop()"></div>
                  <div id="serverId" style="display: none;">Selecting server...</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- fim teste conexão -->

        <div class="row">
          <div class="col-md-2 col-lg-2 "></div>
          <div class="col-md-10 col-lg-10 ">
            <ul>
              <li>
                <small class="text-muted"> Recomendamos no mínimo 500 kbps de velocidade de conexão</small>
              </li>

            </ul>
          </div>
        </div>
        <div class="row">
          <div class="col-md-2">

            <b></b>

          </div>
          <div class="col-md-10">
            <ul>
              <li><small class="text-muted">A perfomance é melhor em uma rede Wi-Fi ou 4G</small>
              </li>



            </ul>
          </div>

        </div>
      </div>
    </div>
  </div>

  <script src="app.js"></script>
  <script src="webcam.js"></script>
  <script src="testeMicrofone.js"></script>

  <!-- teste de conexão -->
  <!-- teste de conexão -->
  <script type="text/javascript" src="speedtest.js"></script>
  <script type="text/javascript" src="index.js"></script>
  <script type="text/javascript">
    initUI();
    selectServer();
  </script>

</body>

</html>