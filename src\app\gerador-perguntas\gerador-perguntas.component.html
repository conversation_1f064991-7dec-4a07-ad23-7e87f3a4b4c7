<mat-card appearance="outlined" class="mother-div">
    <mat-card-title class="spacer-card" style="padding: unset">

        <div class="col-md-12" style="padding: unset">
            <mat-icon class="icon-title">group</mat-icon><a class="title-content fonte-tamanho">{{ 'TELAGERADORDEPERGUNTAS.TITULO' | translate }} </a>
        </div>
    </mat-card-title>

    <mat-divider class="p-t-20"></mat-divider>

    <mat-card-content>

        <div class="row p-t-10  justify-content-center">
            <mat-form-field class="input-spacing col-md-5 col-sm-10 col-lg-5">
                <input matInput 
                    placeholder="{{ 'TELAGERADORDEPERGUNTAS.PERGUNTASPADRAO' | translate }}" 
                    name="perguntas" 
                    maxlength="50" 
                    id="perguntas" 
                    required 
                    [(ngModel)]="perguntas" 
                    (keyup.enter)="salvarPergunta()">
                <mat-error *ngIf="campoPerguntaInvalid == true">{{getErrorMessagecampoPergunta() | translate }}</mat-error>
            </mat-form-field>

            <span class="input-space col-md-2 col-sm-2 col-lg-1  text-center">
                <button mat-mini-fab class=" btn-primary " (click)="salvarPergunta()">
                    <mat-icon style="color:white;">add</mat-icon>
                </button>
            </span>

            <mat-form-field class="col-md-5 col-sm-12 col-lg-5 input-spacing mt-1">
                <input matInput placeholder="{{ 'TELAGERADORDEPERGUNTAS.BUSCAR' | translate }}" id="inputBusca" value="" name="pesquisa" style="cursor:pointer;" [(ngModel)]="pesquisa" (keyup)=CarregaPerguntas()>
            </mat-form-field>

        </div>


        <!-- <div class="input-group col-md-12  mt-1">
        <mat-form-field class="input spacing col-md-5 col-sm-5 col-10" maxlength="30">
            <input matInput placeholder="{{ 'TELAGERADORDEPERGUNTAS.PERGUNTASPADRAO' | translate }}" name="perguntas"
                id="perguntas" required [(ngModel)]="perguntas" (keyup.enter)="salvarPergunta()">
        </mat-form-field>
        <span class="input-group-btn input spacing col-md-1 col-sm-1 col-1">
            <button mat-mini-fab class=" btn-primary " (click)="salvarPergunta()">
                <mat-icon style="color:white;">add</mat-icon>
            </button>
        </span>
        <mat-form-field class="col-md-6 col-sm-6 col-12 input-spacing mt-1">
            <input matInput placeholder="{{ 'TELAGERADORDEPERGUNTAS.BUSCAR' | translate }}" id="inputBusca" value=""
                name="pesquisa" style="cursor:pointer;" [(ngModel)]="pesquisa" (keyup)=CarregaPerguntas()>
        </mat-form-field>
    </div> -->
        <mat-card-content>
            <div class="row">
                <div class="col-md-9 col-sm-8">
                    <!-- <mat-form-field>
                        <input matInput value="Disco party!" placeholder="Message" #message>
                      </mat-form-field>
                      
                      <mat-form-field>
                        <input matInput value="Dance" placeholder="Action" #action>
                      </mat-form-field>
                      
                      <button mat-button (click)="AlgumAcerto(message.value, action.value) ">Show snack-bar</button> -->

                </div>
                <div class="col-md-3 col-sm-4  coluna">
                    <table style="position: absolute; z-index: 2; width: 88%; " class="tabela">
                        <thead>
                            <tr class="cor" style="border-bottom: 1px solid #ddd;">
                                <td class="text-center bold value-color" style="padding: 8px!important; cursor: pointer;" (click)="legenda = !legenda">
                                    {{ 'TELAGERADORDEPERGUNTAS.LEGENDAS' | translate }}
                                    <mat-icon style="float: left;" *ngIf="legenda">expand_less</mat-icon>
                                    <mat-icon style="float: left;" *ngIf="!legenda">expand_more</mat-icon>
                                </td>
                            </tr>
                        </thead>
                        <tbody *ngIf="legenda" style="  pointer-events: none;">
                            <tr>
                                <td class="" style=" font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                    <mat-icon style="vertical-align: sub;">edit</mat-icon><span style="vertical-align: super;margin-left: 5px;">
                                        {{ 'TELAGERADORDEPERGUNTAS.EDITAR' | translate }}</span>
                                </td>
                            </tr>
                        </tbody>

                        <tbody *ngIf="legenda" style="  pointer-events: none;">
                            <tr>
                                <td class="" style="font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                    <mat-icon style="vertical-align: sub;">delete</mat-icon><span style="vertical-align: super;margin-left: 8px;">{{ 'TELAGERADORDEPERGUNTAS.EXCLUIR' | translate }}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="col-md-12 col-sm-12 col-xs-12 p-t-20" style="padding: unset; margin-top: 30px;">
                <div class="col-md-12 col-sm-12 col-xs-12 no-mobile-card">
                    <table *ngFor="let item of DadosPerguntas" class="table" id="DatatableCliente" style="margin-top: 10px; margin-bottom: 10px;">
                        <thead style="display: none;">

                            <tr>
                                <th class="">{{ 'TELAGERADORDEPERGUNTAS.PERGUNTAS' | translate }}</th>
                                <th class="">{{ 'TELAGERADORDEPERGUNTAS.ACOES' | translate }}</th>
                            </tr>
                        </thead>
                        <tbody>

                            <tr class="card_table no-mobile-card ">

                                <td class="" id="nome" style="width:100%">
                                    <div class="row">
                                        <div class="div_paciente quebra">
                                            <label class=" quebra" style="margin-top: 10px;padding-right: 10px;"><b>{{item.desPergunta}}</b></label>
                                        </div>
                                    </div>
                                </td>
                                <td style="min-width:120px;">
                                    {{item.dtaCadastro | date: 'dd/MM/yyyy HH:mm' }}
                                    <!-- {{ item.dtaCadastro }} -->
                                </td>
                                <td class="text-center no-mobile-card" id="acoes" style="min-width:100px;">
                                    <button mat-icon-button class="panel_button" (click)="editPergunta(item.idPergunta)" title="{{'TELAGERADORDEPERGUNTAS.EDITAR' | translate}}">
                                        <mat-icon aria-label="Editar linha selecionada">edit
                                        </mat-icon>
                                    </button>
                                    <button mat-icon-button class="panel_button" (click)="ValorPergunta(item.idPergunta)" title="{{'TELAGERADORDEPERGUNTAS.EXCLUIR' | translate}}">
                                        <mat-icon aria-label="Deletar Linha selecionada">
                                            delete
                                        </mat-icon>
                                    </button>
                                </td>

                            </tr>
                        </tbody>
                    </table>
                </div>





                <mat-card appearance="outlined" *ngFor="let item of DadosPerguntas; let i = index" class="header-card no-desktop" id="DatatableCliente">
                    <div class="text-left quebra">
                        <p>{{item.desPergunta}}</p>
                    </div>
                    <mat-divider></mat-divider><br>
                    <div class=" lateral">
                        <div [@openClose]="toggle[i] ? 'open': 'closed'">
                            <button class="btn-primary buttons-lateral" id="opcao1" mat-mini-fab (click)="editPergunta(item.idPergunta)" title="{{'TELAGERADORDEPERGUNTAS.EDITAR' | translate}}">
                            <mat-icon aria-label="Editar linha selecionada" class="">edit</mat-icon>
                        </button>
                            <button class="btn-primary buttons-lateral" id="opcao2" mat-mini-fab (click)="ValorPergunta(item.idPergunta)" title="{{'TELAGERADORDEPERGUNTAS.EXCLUIR' | translate}}">
                            <mat-icon aria-label="Deletar Linha selecionada" class="">delete
                            </mat-icon>
                        </button>
                        </div>
                        <button class="btn-primary buttons-lateral" mat-mini-fab (click)="toggle[i] = !toggle[i]"><mat-icon>keyboard_arrow_up</mat-icon></button>
                    </div>

                    <!-- <div class="text-left quebra">
                        <p>{{item.desPergunta}}</p>
                    </div>
                    <mat-divider></mat-divider><br>
                    <div class="text-center fab" ontouchstart="">

                        <button class="mainb material-icons">


                        </button>
                        <ul>
                            <li (click)="editPergunta(item.idPergunta)">
                                <label for="opcao1">
                                    <p style="margin-top: -9px;">
                                        Editar</p>
                                </label>
                                <button id="opcao1">
                                    <mat-icon aria-label="Editar linha selecionada" class="">edit</mat-icon>
                                </button>
                            </li>

                            <li (click)="ValorPergunta(item.idPergunta)" *ngIf="inativos != true ">
                                <label for="opcao2">
                                    <p style="margin-top: -9px;">
                                        Excluir</p>
                                </label>
                                <button id="opcao2">
                                    <mat-icon aria-label="Deletar Linha selecionada" class="">delete
                                    </mat-icon>
                                </button>
                            </li>

                        </ul>
                    </div> -->
                </mat-card>
            </div>

            <div class="col-sm-12 text-center">
                <button mat-flat-button class="btn-primary" *ngIf="(DadosPerguntas != undefined &&  DadosPerguntas.length > 0) && bOcultaCarregaMais == false" (click)="CarregarMais()">{{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}</button>
            </div>
            <!-- <div class="p-t-20"></div> -->
        </mat-card-content>





        <!-- </mat-card> -->

        <!-- 

<iframe src="http://prd3.wfat.com.br/tabelaiss.aspx" width="100%" height="500" scrolling="yes" class="iframe-class" frameborder="0"></iframe> -->


        <!-- Modal excluir -  -->

        <ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal emailmodal">
            <div class="modal-header p-t-20 p-b-20">
                <h1 class="little-title fw-700">
                    <mat-icon style="color:red">warning</mat-icon>
                    {{ 'TELAGERADORDEPERGUNTAS.EXCLUIRPERGUNTA' | translate }}
                </h1>
            </div>



            <mat-divider></mat-divider>
            <div class="row-button text-right" style="padding: 10px 0px;">
                <button mat-flat-button class="input-align btn btn-danger" (click)="ngxSmartModalService.getModal('excluirItem').close()">
                    {{ 'TELAGERADORDEPERGUNTAS.NAO' | translate }} </button>
                <button mat-flat-button class="input-align btn btn-success" (click)="inativarPerguntas()">
                    {{ 'TELAGERADORDEPERGUNTAS.SIM' | translate }} </button>
            </div>
        </ngx-smart-modal>
        <!----------------------------------------------------------------------------------------------------------------------------------------------------------------------->