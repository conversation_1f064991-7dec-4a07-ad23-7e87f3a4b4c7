// :host{
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     position: fixed;
//     z-index: 100;
//     left: 0;
//     top: 0;
//     width: 100%;
//     height: 100%;
//     overflow: auto;
//     background-color: rgba(0, 0, 0, 0.4);
// }

.ConteudoModal{
    // padding: 20px;
    // width: 100%;
    // height: 100%;
    // max-width: 700px;
    // max-height: 90vh;
    // background-color: #fefefe;
    // border-radius: 5px;
    // box-shadow: 0 7px 25px rgba(0, 0, 0, 0.25);
    // flex-direction: column;
    // display: flex;
    // overflow: hidden;
}

.CaixaFechar{
    width: 17px;
    height: 17px;
    color: #aaa;
    float: right;
    align-self: end;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    margin-top: -10px;
}

.CaixaFechar:hover{
    color: black;
    cursor: pointer;
}

.CaixaCabecalho{
    padding: 30px 0px 0px 0px;
    span{
        font-weight: 700; 
        color:  #5260ff; 
        font-size: 20px;
    }
    hr{
        margin: 5px 0px 15px 0px;
    }
}


