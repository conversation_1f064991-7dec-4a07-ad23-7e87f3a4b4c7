import { Component, OnInit } from "@angular/core";
import { EnumMeses } from "src/app/Util/EnumMeses";
import {
  
  MatSnackBar as MatSnackBar,
} from "@angular/material/snack-bar";
import { EnumTipoUsuario } from "src/app/Util/tipoUsuario";
import { UsuarioLogadoService } from "src/app/auth/usuarioLogado.service";
import { LifeLineService } from "src/app/lifeLine/lifeline.service";
import { DocumentosService } from "src/app/service/documentos.service";
import { NgxSmartModalService } from "ngx-smart-modal";
import { ConsultaService } from "src/app/service/consulta.service";
import { Router } from "@angular/router";
import { AppComponent } from "src/app/app.component";
import {
  subMonths,
  addMonths,
  addDays,
  addWeeks,
  subDays,
  subWeeks,  
} from "date-fns";
import { ObjGrafico } from "src/app/model/consulta";
import { SpinnerService } from "src/app/service/spinner.service";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatCardModule } from "@angular/material/card";
import { MatDivider } from "@angular/material/divider";
import { MatInputModule } from "@angular/material/input";
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { GoogleChartsModule } from 'angular-google-charts';
import { CalendarModule } from "angular-calendar";

type CalendarPeriod = "day" | "week" | "month";

function addPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
  return {
    day: addDays,
    week: addWeeks,
    month: addMonths,
  }[period](date, amount);
}

function subPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
  return {
    day: subDays,
    week: subWeeks,
    month: subMonths,
  }[period](date, amount);
}

@Component({
    selector: "app-indicadores",
    templateUrl: "./indicadores.component.html",
    styleUrls: ["./indicadores.component.scss"],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatDivider,
      NgxChartsModule,
      MatButtonToggleModule,
      GoogleChartsModule,
      CalendarModule
    ]
})
export class IndicadoresComponent implements OnInit {
  constructor(
    private spinner: SpinnerService,
    private appc: AppComponent,
    private router: Router,
    private consultaService: ConsultaService,
    public ngxSmartModalService: NgxSmartModalService,
    public snackBar: MatSnackBar,
    public documentosService: DocumentosService,
    private lifeLineService: LifeLineService,
    private usuarioLogadoService: UsuarioLogadoService
  ) {
    this.screenWidthstream = window.innerWidth;
    if (this.screenWidthstream > 1150) this.view = [700, 300];
    else if (this.screenWidthstream < 1150 && this.screenWidthstream > 500)
      this.view = [500, 400];
    else this.view = [300, 700];
    // Object.assign(this, { this.single })
  }

  viewMes: CalendarPeriod = "month";
  DataGrafico: Date = new Date();
  consultasMeses: boolean = false;
  // consultasMeses:boolean = false;
  screenWidthstream: number;
  totalConsultas?: number;
  single!: any[];
  singleMeses?: any[];
  dados = [];
  consultas: number = 0;
  // usuario: Usuario;
  alerta: boolean = false;
  DadosConsultaLifeLine: any = [];
  dadosLife = false;
  dadosAnonimo?: string;
  MedicoAnonimo?: string;
  Dataanonimo?: string;
  dadosLifeLine: any = [];
  nomePaciente: string = "";
  tipoUsuario?: string;
  AnexosQnt: number = 0;
  QntLifeLine: number = 0;

  graficoValoresVazio: boolean = true;
  graficoConsultaMesesVazio: boolean = true;
  nomeMesAnterior:any;

  AdmPermissao = false;
  MedicoPermissao = false;
  AtendentePermissao = false;
  PacientePermissao = false;
  showPanel = false;
  showLife = true;
  video: boolean = false;
  dadosAvaliacao: any = [];
  dadosSilver: number = 0;
  dadosGold: number = 0;
  dadosBronze: number = 0;

  // message: string = "Sua Life-Line esta vazia!.  ✔ ";
  // nconcordomessage: string = "Excluido com Sucesso. ✔";
  // actionButtonLabel: string = "Fechar";
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 60000;
  // horizontalPosition: MatSnackBarHorizontalPosition = "right";
  // verticalPosition: MatSnackBarVerticalPosition = "bottom";
  // concordo: boolean;
  // concordomsg: boolean;
  // sendemail: string = "Perfil salvo com Sucesso.  ✔";
  flgGraficoZero: boolean = false;
  btnConsultas: boolean = false;
  btnFinancas: boolean = false;

  // dadosPagamentoGrafico = [
  //   ['Mês', 'R$'],
  //   ['1', 1250.23],
  //   ['7', 850.54],
  //   ['15', 780.76],
  //   ['22', 1435.88],
  //   ['30', 200.89],
  //   ['*2', 1220.00],
  //   ['*3', 3001.54],
  //   ['*4', 590.76],
  //   ['*5', 2320.88],
  //   ['*6', 605.89],
  // ]

  dadosPagamentoGrafico: any = [["Mês", "R$"]];

  // public rawChartData: google.visualization.ChartSpecs = {
  //   chartType: "AreaChart",
  //   dataTable: this.dadosPagamentoGrafico,
  // };

  // view: any[];

  view: any[];
  // = [700, 300];
  // view: mobile[] = [400, 200];
  // options
  showXAxis = true;
  showYAxis = true;
  gradient = false;
  showLegend = true;
  showXAxisLabel = true;
  xAxisLabel = "Country";
  showYAxisLabel = true;
  yAxisLabel = "Population";

  colorScheme = {
    domain: ["#5AA454", "#C7B42C", "#A10A28", "#7aa3e5"],
  };

  // line, area
  autoScale = true;

  public rawFormatter: any;

  dadosConsultasUltimosMeses: any = [];

  public chart = {
    title: "Consultas 2019",
    type: "BarChart",
    data: this.dadosConsultasUltimosMeses,
    columnNames: ["Element", "Consulta"],
    options: {
      animation: {
        duration: 250,
        easing: "ease-in-out",
        startup: true,
      },
    },
  };

  async ngOnInit() {
    this.GetAvaliacoesGrafico();
    this.GetValoresGrafico();
    await this.GetConsultasUltimosMesesGrafico();
    this.CarregaConsultas();
    // this.Carregagraficos();
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM) {
      this.tipoUsuario = "ADM Sistema";
      this.AdmPermissao = true;
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      // this.Carregagraficos();
      this.dadosGraficoPizza();
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente
    ) {
      this.tipoUsuario = "Atendente";
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      this.dadosGraficoPizza();
      // this.Carregagraficos();
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico
    ) {
      this.tipoUsuario = "Médico";
      this.MedicoPermissao = true;
      this.dadosGraficoPizza();
      // this.Carregagraficos();
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente
    ) {
      this.tipoUsuario = "Paciente";
      this.PacientePermissao = true;
    }
  }

  Agenda() {
    sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(["/calendario"]);
    this.appc.ngOnInit();
  }

  Perfil() {
    this.router.navigate(["/perfil"]);
    this.appc.ngOnInit();
  }
  dadosGraficoPizza() {
    this.consultaService
      .GetDadosPizza(this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe((retorno) => {
        if (retorno) {
          this.single = [];
          retorno.forEach((element:any) => {
            if (element.Status == "Concluido")
              this.single?.push({
                name: "Consulta Concluidas",
                value: element.qtd,
              });
            if (element.Status == "Agendada")
              this.single?.push({
                name: "Consulta Agendada",
                value: element.qtd,
              });
            if (element.Status == "Cancelada")
              this.single?.push({
                name: "Consulta Cancelada",
                value: element.qtd,
              });
            if (element.Status == "Total") {
              this.single?.push({
                name: "Total De Consultas",
                value: element.qtd,
              });
              this.totalConsultas = element.qtd;
            }
          });

          var dados = [];
          dados = this.single.filter((c) => c.value > 0);
          if (dados.length == 0) this.flgGraficoZero = true;

          var soma = 0;
          var teste:any = [];
          for (var i = 0; i < this.single.length; i++) {
            teste[i] = parseInt(this.single[i].value);
            soma += parseInt(teste[i]);
          }

          ;
        }

        ;
      });
  }

  lifeline() {
    var usuarioLifeline = {
      tipoUsuario: this.usuarioLogadoService.getIdTipoUsuario(),
      idUsuario: this.usuarioLogadoService.getIdUsuarioAcesso(),
    };
    this.lifeLineService.setModalLifeline(usuarioLifeline);
  }

  // LifeLineVazia(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["success-snack"];
  //     this.snackBar.open(
  //       this.message,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }

  // MensagemSnack(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["success-snack"];
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }

  DadosPaciente(id:any) {
    this.dadosLife = false;
    this.DadosConsultaLifeLine = [];
    this.consultaService.GetDadosLifeLine(id).subscribe(
      (retorno) => {
        this.DadosConsultaLifeLine = retorno.convercas;
        if (this.DadosConsultaLifeLine.anonimo != null) {
          this.dadosAnonimo = this.DadosConsultaLifeLine.anonimo;
          this.MedicoAnonimo = this.DadosConsultaLifeLine.medico;
          this.Dataanonimo = this.DadosConsultaLifeLine.dtaConsulta;
        }
        this.dadosLife = true;
        ;
      }
    );
  }

  abreDate() {
    this.showPanel = !this.showPanel;
    this.showLife = false;
  }

  abreLife() {
    this.showLife = !this.showLife;
    this.showPanel = false;
  }
  Irconsulta() {
    // localStorage.setItem("dash", 'Agendada');
    this.router.navigate(["/consulta"]);
  }

  CarregaConsultas() {
    try {
      this.consultaService
        .GetDashBoard(this.usuarioLogadoService.getIdUsuarioAcesso())
        .subscribe(
          (retorno) => {
            ;
            if (
              this.tipoUsuario == "Paciente" ||
              this.tipoUsuario == "Médico"
            ) {
              this.dados = retorno.consultas;

              this.QntLifeLine = retorno.lifeLine;
              this.AnexosQnt = retorno.anexo;
              this.consultas = retorno.consultasTotal;
            } else this.consultas = retorno.totalconsultas;

            var minutos = 20;

            if (this.consultas > 0) {
              this.dados.forEach((element:any) => {
                var teste = new Date(element.dtaConsulta);
                teste.setMinutes(teste.getMinutes() - minutos);
                var data = new Date();
                data.setMinutes(data.getMinutes() - minutos);
                if (teste <= new Date() && teste >= data) {
                  this.alerta = true;
                  return;
                } else this.alerta = false;
              });
            }
          }
        );
    } catch (error) {
    }
  }

  download(arq:any, nome:any, contentType:any) {
    if (!contentType) {
      contentType = "application/octet-stream";
    }
    var a = document.createElement("a");
    var blob = new Blob([arq], { type: contentType });
    a.href = window.URL.createObjectURL(blob);
    a.download = nome;
    a.click();
  }

  BaixarArquivo(chave:any, nome:any) {
    this.consultaService.CarregaCaminhoArquivo(String(chave), nome).subscribe(
      (response) => {
        var application;
        this.download(response, nome, application);
        this.spinner.hide();
      },
      () => {
        this.spinner.hide();
      }
    );
  }

  GetAvaliacoesGrafico() {
    this.consultaService
      .GetAvaliacoesGrafico(this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe((retorno) => {
        this.dadosAvaliacao = retorno;

        this.dadosSilver = this.dadosAvaliacao.filter(
          (c:any) => c.avaliacao == "Silver"
        ).length;
        this.dadosGold = this.dadosAvaliacao.filter(
          (c:any) => c.avaliacao == "Gold"
        ).length;
        this.dadosBronze = this.dadosAvaliacao.filter(
          (c:any) => c.avaliacao == "Bronze"
        ).length;
        ;
        this.spinner.hide();
      });
  }

  GetValoresGrafico() {
    var obj = new ObjGrafico();

    obj.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
    obj.Mes = this.DataGrafico;

    this.consultaService.GetValoresGrafico(obj).subscribe((retorno: any) => {
      ;

      if (retorno.length > 0) {
        this.graficoVasil = false;
        this.graficoValoresVazio = false;

        switch (retorno[0].Mês) {
          case EnumMeses.Janeiro:
            this.nomeMesAnterior = "Janeiro";
            break;
          case EnumMeses.Fevereiro:
            this.nomeMesAnterior = "Fevereiro";
            break;
          case EnumMeses.Março:
            this.nomeMesAnterior = "Março";
            break;
          case EnumMeses.Abril:
            this.nomeMesAnterior = "Abril";
            break;
          case EnumMeses.Maio:
            this.nomeMesAnterior = "Maio";
            break;
          case EnumMeses.Junho:
            this.nomeMesAnterior = "Junho";
            break;
          case EnumMeses.Julho:
            this.nomeMesAnterior = "Julho";
            break;
          case EnumMeses.Agosto:
            this.nomeMesAnterior = "Agosto";
            break;
          case EnumMeses.Setembro:
            this.nomeMesAnterior = "Setembro";
            break;
          case EnumMeses.Outubro:
            this.nomeMesAnterior = "Outubro";
            break;
          case EnumMeses.Novembro:
            this.nomeMesAnterior = "Novembro";
            break;
          case EnumMeses.Dezembro:
            this.nomeMesAnterior = "Dezembro";
            break;
          default:
            this.nomeMesAnterior = "Invalido";
            break;
        }
        var totalDias:any;

        if (
          retorno[0].Mês == 1 ||
          retorno[0].Mês == 3 ||
          retorno[0].Mês == 5 ||
          retorno[0].Mês == 7 ||
          retorno[0].Mês == 8 ||
          retorno[0].Mês == 10 ||
          retorno[0].Mês == 12
        ) {
          totalDias = 31;
        } else if (retorno[0].Mês == 2) {
          var data = new Date();
          if (data.getFullYear() % 4 == 0) totalDias = 29;
          else totalDias = 28;
        } else if (
          retorno[0].Mês == 4 ||
          retorno[0].Mês == 6 ||
          retorno[0].Mês == 9 ||
          retorno[0].Mês == 11
        ) {
          totalDias = 30;
        } else {
          ;
        }

        for (let index = 0; index < totalDias; index++) {
          this.dadosPagamentoGrafico.push([(1 + index).toString(), 0]);
        }

        retorno.forEach((element:any) => {
          this.dadosPagamentoGrafico.forEach((elemento:any) => {

            if (element.Dia == elemento[0]) {
              elemento[1] = element.ValorConsulta;
            }
          });
        });
        //atualizar grafico
        // this.rawChartData = null;
        // this.rawChartData = {
        //   chartType: "AreaChart",
        //   dataTable: this.dadosPagamentoGrafico,
        // };
        // this.rawChartData.dataTable = this.dadosPagamentoGrafico
      } else {
        this.graficoVasil = true;
        this.dadosPagamentoGrafico.forEach((elemento:any) => {
          elemento[1] = 0;
        });
      }
    });
    this.spinner.hide();
  }
  locale: string = 'pt';
  increment(): void {
    this.changeDateMEs(addPeriod(this.viewMes, this.DataGrafico, 1));
  }
  graficoVasil = false;
  decrement(): void {
    this.changeDateMEs(subPeriod(this.viewMes, this.DataGrafico, 1));
  }

  changeDateMEs(date: Date): void {
    this.DataGrafico = date;
    this.dadosPagamentoGrafico = [["Mês", "R$"]];
    this.GetValoresGrafico();

    // this.rawChartData = {
    //   chartType: "AreaChart",
    //   dataTable: this.dadosPagamentoGrafico,
    // };
  }

  GetConsultasUltimosMesesGrafico() {
    this.consultaService
      .GetConsultasUltimosMesesGrafico(
        this.usuarioLogadoService.getIdUltimaClinica()
      )
      .subscribe((retorno: any) => {
        ;

        if (retorno.length > 0) {
          this.graficoConsultaMesesVazio = false;

          retorno.forEach((element:any) => {
            var nomeMes = "";
            var quant = null;
            quant = element.Qtd;
            switch (element.Mês) {
              case 1:
                nomeMes = "Janeiro";
                break;
              case 2:
                nomeMes = "Fevereiro";
                break;
              case 3:
                nomeMes = "Março";
                break;
              case 4:
                nomeMes = "Abril";
                break;
              case 5:
                nomeMes = "Maio";
                break;
              case 6:
                nomeMes = "Junho";
                break;
              case 7:
                nomeMes = "Julho";
                break;
              case 8:
                nomeMes = "Agosto";
                break;
              case 9:
                nomeMes = "Setembro";
                break;
              case 10:
                nomeMes = "Outubro";
                break;
              case 11:
                nomeMes = "Novembro";
                break;
              case 12:
                nomeMes = "Dezembro";
                break;
              default:
                nomeMes = "Invalido";
                break;
            }
            this.dadosConsultasUltimosMeses.push([
              nomeMes.valueOf(),
              quant.valueOf(),
            ]);
          });
        } else this.graficoConsultaMesesVazio = true;

        ;
      });
  }

  Carregagraficos() {
    try {
      this.documentosService
        .Carregagraficos(this.usuarioLogadoService.getIdUltimaClinica())
        .subscribe((retorno) => {
          ;

          var meses = retorno;

          this.singleMeses = [];
          meses.forEach((element:any) => {
            if (element.mes == "1") {
              this.singleMeses!.push({
                name: "Janeiro",
                value: element.total,
              });
            } else if (element.mes == "2") {
              this.singleMeses!.push({
                name: "Fevereiro",
                value: element.total,
              });
            } else if (element.mes == "3") {
              this.singleMeses!.push({
                name: "Março",
                value: element.total,
              });
            } else if (element.mes == "4") {
              this.singleMeses!.push({
                name: "Abril",
                value: element.total,
              });
            } else if (element.mes == "5") {
              this.singleMeses!.push({
                name: "Maio",
                value: element.total,
              });
            } else if (element.mes == "6") {
              this.singleMeses!.push({
                name: "Junho",
                value: element.total,
              });
            } else if (element.mes == "7") {
              this.singleMeses!.push({
                name: "Julho",
                value: element.total,
              });
            } else if (element.mes == "8") {
              this.singleMeses!.push({
                name: "Agosto",
                value: element.total,
              });
            } else if (element.mes == "9") {
              this.singleMeses!.push({
                name: "Setembro",
                value: element.total,
              });
            } else if (element.mes == "10") {
              this.singleMeses!.push({
                name: "Outubro",
                value: element.total,
              });
            } else if (element.mes == "11") {
              this.singleMeses!.push({
                name: "Novembro",
                value: element.total,
              });
            } else if (element.mes == "12") {
              this.singleMeses!.push({
                name: "Dezembro",
                value: element.total,
              });
            }
          });

          ;
        });
      this.spinner.hide();
    } catch (error) {
      this.spinner.hide();
    }
  }

  selecConsultas() {
    this.btnConsultas = true;
    this.btnFinancas = false;
  }

  selecFinancas() {
    this.btnConsultas = false;
    this.btnFinancas = true;
    this.GetValoresGrafico();
  }
}
