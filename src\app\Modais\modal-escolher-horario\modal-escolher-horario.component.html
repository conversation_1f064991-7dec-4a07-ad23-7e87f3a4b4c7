<div style="width: 100%; height: 100%; position: relative; justify-content: space-between;">

    <div style="display: flex; justify-content: space-between; max-height: 5%;">
        <h2 mat-dialog-title style="font-weight: 700; color: #279EFF; margin-bottom: -2px;">Escolha um horário para o
            agendamento
        </h2>
        <button mat-icon-button (click)="fecharModal()"> <mat-icon>close</mat-icon> </button>
    </div>
    <br>
    <div style="display: flex; width: 100%; flex-direction: column;">
        <span style="font-size: 1.6ch; font: small;">Escolha o horário</span>
        <mat-form-field appearance="outline">
            <input matInput type="time" [(ngModel)]="varData" (ngModelChange)="validarHorario()">
        </mat-form-field>
    </div>
    <br><br>

    <div style="position: absolute; bottom: 0; width: 100%; display: flex; justify-content: center; gap: 1em;">
        <button mat-flat-button class="btCancelar" (click)="cancelar()">cancelar</button>
        <button mat-flat-button class="btEnvio" [disabled]="!horarioValido" (click)="salvar()">salvar</button>
    </div>
</div>