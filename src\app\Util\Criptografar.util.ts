/**
 * Utilitário para criptografia de duas camadas e gerenciamento de localStorage criptografado
 *
 * Implementa:
 * - Criptografia Base64 + XOR com chave
 * - Métodos para salvar/recuperar dados criptografados no localStorage
 *
 * <AUTHOR> Bonecare
 * @version 1.0
 */

export class CriptografarUtil {

    private static readonly CHAVE_PRIMARIA = 'BoneCare2024@#$';
    private static readonly CHAVE_SECUNDARIA = 'SecureKey!@#456';

    /**
     * Primeira camada: Criptografia XOR com chave
     * @param texto Texto a ser criptografado
     * @param chave Chave para criptografia
     * @returns Texto criptografado
     */
    private static criptografiaXOR(texto: string, chave: string): string {
        let resultado = '';
        for (let i = 0; i < texto.length; i++) {
            const charCode = texto.charCodeAt(i) ^ chave.charCodeAt(i % chave.length);
            resultado += String.fromCharCode(charCode);
        }
        return resultado;
    }

    /**
     * Segunda camada: Codificação Base64 com embaralhamento
     * @param texto Texto a ser codificado
     * @returns Texto codificado
     */
    private static codificarBase64Embaralhado(texto: string): string {
        // Converte para Base64
        const base64 = btoa(unescape(encodeURIComponent(texto)));

        // Embaralha os caracteres (inverte e adiciona prefixo/sufixo)
        const embaralhado = 'BC_' + base64.split('').reverse().join('') + '_END';

        return embaralhado;
    }

    /**
     * Decodifica Base64 embaralhado
     * @param textoEmbaralhado Texto embaralhado a ser decodificado
     * @returns Texto decodificado
     */
    private static decodificarBase64Embaralhado(textoEmbaralhado: string): string {
        try {
            if (!textoEmbaralhado.startsWith('BC_') || !textoEmbaralhado.endsWith('_END')) {
                throw new Error('Formato inválido');
            }

            const semPrefixoSufixo = textoEmbaralhado.slice(3, -4);

            const base64Original = semPrefixoSufixo.split('').reverse().join('');

            return decodeURIComponent(escape(atob(base64Original)));
        } catch (error) {
            console.error('Erro ao decodificar Base64 embaralhado:', error);
            return '';
        }
    }

    public static gerarHashToken(cpf: string, data: string): string {
        const timestamp = Date.now().toString();
        const randomPart = Math.random().toString(36).substring(2, 8);
        const cpfPart = cpf.replace(/\D/g, '').substring(0, 6);
        const dataPart = data.replace(/\D/g, '').substring(0, 8);
        const token = `${cpfPart}-${dataPart}-${timestamp}-${randomPart}`;
        return token;
    }

    /**
     * Criptografa um texto usando duas camadas de segurança
     * @param texto Texto a ser criptografado
     * @returns Texto criptografado com duas camadas
     */
    public static criptografar(texto: string): string {
        if (!texto) return '';

        try {
            const primeiraLayer = this.criptografiaXOR(texto, this.CHAVE_PRIMARIA);
            const segundaLayer = this.criptografiaXOR(primeiraLayer, this.CHAVE_SECUNDARIA);
            const resultado = this.codificarBase64Embaralhado(segundaLayer);

            return resultado;
        } catch (error) {
            console.error('Erro ao criptografar:', error);
            return texto;
        }
    }

    /**
     * Descriptografa um texto que foi criptografado com duas camadas
     * @param textoCriptografado Texto criptografado
     * @returns Texto original descriptografado
     */
    public static descriptografar(textoCriptografado: string): string {
        if (!textoCriptografado) return '';

        try {
            const primeiraDecodificacao = this.decodificarBase64Embaralhado(textoCriptografado);
            if (!primeiraDecodificacao) return '';
            const segundaDecodificacao = this.criptografiaXOR(primeiraDecodificacao, this.CHAVE_SECUNDARIA);
            const resultado = this.criptografiaXOR(segundaDecodificacao, this.CHAVE_PRIMARIA);
            return resultado;
        } catch (error) {
            console.error('Erro ao descriptografar:', error);
            return '';
        }
    }

    /**
     * Salva um valor criptografado no localStorage
     * @param chave Nome da chave (também será criptografada)
     * @param valor Valor a ser criptografado e salvo
     */
    public static localStorageCriptografado(chave: string, valor: string): void {
        if (!chave || valor === null || valor === undefined) {
            console.warn('Chave ou valor inválido para localStorage criptografado');
            return;
        }

        try {
            const chaveCriptografada = this.criptografar(chave);
            const valorCriptografado = this.criptografar(valor.toString());
            localStorage.setItem(chaveCriptografada, valorCriptografado);
        } catch (error) {
            console.error('Erro ao salvar no localStorage criptografado:', error);
            localStorage.setItem(chave, valor.toString());
        }
    }

    /**
     * Recupera e descriptografa um valor do localStorage
     * @param chave Nome da chave (será criptografada para busca)
     * @returns Valor descriptografado ou null se não encontrado
     */
    public static obterLocalStorageCriptografado(chave: string): string | null {
        if (!chave) {
            console.warn('Chave inválida para obter localStorage criptografado');
            return null;
        }

        try {
            const chaveCriptografada = this.criptografar(chave);
            const valorCriptografado = localStorage.getItem(chaveCriptografada);
            if (!valorCriptografado) 
                return null;
            const valorDescriptografado = this.descriptografar(valorCriptografado);
            return valorDescriptografado;
        } catch (error) {
            console.error('Erro ao obter do localStorage criptografado:', error);
            return localStorage.getItem(chave);
        }
    }

    /**
     * Remove um item criptografado do localStorage
     * @param chave Nome da chave (será criptografada para busca)
     */
    public static removerLocalStorageCriptografado(chave: string): void {
        if (!chave) {
            console.warn('Chave inválida para remover localStorage criptografado');
            return;
        }

        try {
            const chaveCriptografada = this.criptografar(chave);
            localStorage.removeItem(chaveCriptografada);
        } catch (error) {
            console.error('Erro ao remover do localStorage criptografado:', error);
            // Fallback: tenta remover sem criptografia
            localStorage.removeItem(chave);
        }
    }

    /**
     * Verifica se uma chave existe no localStorage criptografado
     * @param chave Nome da chave (será criptografada para busca)
     * @returns true se a chave existe, false caso contrário
     */
    public static existeLocalStorageCriptografado(chave: string): boolean {
        if (!chave) return false;

        try {
            const chaveCriptografada = this.criptografar(chave);
            return localStorage.getItem(chaveCriptografada) !== null;
        } catch (error) {
            console.error('Erro ao verificar localStorage criptografado:', error);
            return localStorage.getItem(chave) !== null;
        }
    }

    /**
     * Limpa todos os itens criptografados do localStorage
     * (Remove apenas itens que seguem o padrão de criptografia)
     */
    public static limparLocalStorageCriptografado(): void {
        try {
            const chaves = Object.keys(localStorage);
            let removidos = 0;

            chaves.forEach(chave => {
                if (chave.startsWith('BC_') && chave.endsWith('_END')) {
                    localStorage.removeItem(chave);
                    removidos++;
                }
            });

        } catch (error) {
            console.error('Erro ao limpar localStorage criptografado:', error);
        }
    }
}