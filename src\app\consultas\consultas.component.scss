@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

// Variáveis do tema
$primary-color: #2E8B57; // Verde esmeralda
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

// Estilos Globais
body, html {
  font-family: 'Inter', sans-serif;
  background-color: $bg-color;
  color: $text-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Resets e Normalizações
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

button {
  cursor: pointer;
  font-family: 'Inter', sans-serif;
  &:focus {
    outline: none;
  }
}

.w-100 {
  width: 100%;
}

// Cartão principal
.dashboard-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  max-height: 81vh;
  @media (max-width:1000px) {
    width: 98%;
    overflow: auto;
    height: 100%;
  }
  mat-card-content {
    padding: 0;
  }
}

// Cabeçalho do cartão
.card-header {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// Barra de pesquisa e filtros
.search-filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  @media (min-width: 1200px) {
    flex-direction: row;
    align-items: flex-start;
  }
}

.search-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.filter-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  @media (min-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

.filter-item {
  flex: 1;
  min-width: 200px;
  
  @media (max-width: 767px) {
    width: 100%;
  }
}

// Calendários
.calendar-container {
  display: flex;
  gap: 16px;
  width: 100%;
  max-width: 600px;
  
  @media (min-width: 992px) {
    flex-direction: row;
  }
}

.calendar-wrapper {
  flex: 1;
  background-color: $card-bg;
  border-radius: $border-radius;
  overflow: hidden;
  box-shadow: $box-shadow;
}

// Botões de ação
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.button-container {
  display: flex;
  gap: 12px;
  align-items: center;
  .modalConfirmacao{
    background-color: transparent;
  }
}

// Botões
.btn-primary {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  transition: $transition;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background-color: $primary-dark;
    box-shadow: $box-shadow;
  }
}
.search{
  background-color: transparent;
  color: $primary-color;
}

.btn-secondary {
  background-color: $secondary-color;
  color: $text-primary;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 500;
  transition: $transition;
  
  &:hover {
    background-color: darken($secondary-color, 5%);
    box-shadow: $box-shadow;
  }
}

.btn-accent {
  background-color: $accent-color;
  color: #fff;
  padding: 5px;
  border-radius: 8px;
  background-color: $secondary-color;
  color: $text-primary;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  transition: $transition;
  mat-icon{
    margin-bottom: -6px;
  }
  &:hover {
    background-color: darken($secondary-color, 5%);
    box-shadow: $box-shadow;
  }
}

// Mensagem sem dados
.no-data-message {
  padding: 48px 24px;
  text-align: center;
  
  h3 {
    color: $text-secondary;
    font-weight: 500;
  }
}

// Lista de consultas - Mobile
.consultation-mobile-list {
  padding: 16px;
  display: none;
  
  @media (max-width: 991px) {
    display: block;
  }
}

.consultation-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  margin-bottom: 16px;
  overflow: hidden;
  position: relative;
  
  .card-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $border-color;
    @media (max-width:1000px) {
      flex-direction: row !important;
    }
    
    .status-indicators {
      position: relative;
      
      .indicator {
        width: 10px;
        height: 10px;
        background-color: $error-color;
        border-radius: 50%;
        position: absolute;
        top: 0;
        right: 0;
      }
    }
    
    .title {
      flex: 1;
      margin-left: 8px;
      
      h3 {
        font-size: 16px;
        margin: 0;
        color: $primary-color;
        font-weight: 600;
      }
    }
    
    .status-stamp {
      position: absolute;
      top: 8px;
      right: 8px;
      
      img {
        width: 50px;
        height: auto;
      }
    }
  }
  
  .card-body {
    padding: 16px;
    
    .info-item {
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      @media (max-width:1000px) {
        flex-direction: column;
      }
      .label {
        color: $text-secondary;
        font-weight: 500;
        background-color: transparent !important;
        box-shadow: none !important;
        border: none !important;
        background: transparent !important;
      }
      
      .value {
        font-weight: 500;
      }
    }
    @media (max-width:1000px) {
      height: auto !important;
    }
  }
  
  .mobile-action-buttons {
    position: relative;
    
    .fab-toggle {
      position: absolute;
      bottom: 16px;
      right: 16px;
      background-color: $primary-color;
      color: white;
      z-index: 2;
      
      &:hover {
        background-color: $primary-dark;
      }
    }
    
    .fab-menu {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      position: absolute;
      bottom: 80px;
      right: 16px;
      opacity: 0;
      transform: translateY(20px);
      transition: all $transition ease;
      pointer-events: none;
      
      &.open {
        opacity: 1;
        transform: translateY(0);
        pointer-events: all;
      }
      
      button {
        background-color: #ffff;
        color: $text-primary;
        
        &.highlight-button {
          background-color: $primary-color;
          color: white;
        }
      }
    }
  }
}

// Lista de consultas - Desktop (MELHORADA)
.consultation-desktop-list {
  padding: 16px;
  max-height: 230px; // Altura máxima definida
  overflow-y: auto; // Adicionando scroll vertical
  scrollbar-width: thin; // Para Firefox
  scrollbar-color: $primary-color $secondary-light; // Para Firefox
  
  // Estilizando a scrollbar para navegadores WebKit (Chrome, Safari, etc.)
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: $secondary-light;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba($primary-color, 0.6);
    border-radius: 4px;
    
    &:hover {
      background-color: $primary-color;
    }
  }
  
  // Adicionando transição suave de rolagem
  scroll-behavior: smooth;
  
  // Container para garantir que os painéis sejam exibidos corretamente
  .consultation-list-container {
    min-width: 100%;
    padding-right: 8px; // Espaço para a scrollbar
  }
  
  @media (max-width: 991px) {
    display: none;
  }
}

// Painel expansível (MELHORADO)
mat-expansion-panel {
  margin-bottom: 12px !important; // Reduzido para tornar a lista mais compacta
  border-radius: $border-radius !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important; // Sombra mais sutil
  border: 1px solid rgba($border-color, 0.7); // Borda suave
  transition: all 0.3s ease !important;
  background-color: #efefef;
  
  &:hover {
    background-color: $primary-light;
    transform: translateY(-2px); // Leve efeito de elevação ao passar o mouse
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important; // Sombra mais pronunciada no hover
  }
  
  &.mat-expanded {
    background-color: $primary-light;
    margin-top: 16px !important;
    margin-bottom: 16px !important;
    
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background-color: $primary-color;
      border-radius: $border-radius 0 0 $border-radius;
    }
  }
}
mat-expansion-panel-header{
  padding: 35px 0 !important;
}

.panel-header-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 0; // Padding vertical adicional para melhor clicabilidade
  
  @media (max-width: 1200px) {
    flex-wrap: wrap;
  }
}

.panel-column {
  display: flex;
  align-items: center;
  padding: 0 12px;
  
  &.patient-doctor {
    flex: 2;
    min-width: 200px;
    
    .status-icon {
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: $secondary-light;
      
      .active {
        color: $error-color;
      }
      
      .ready {
        color: $primary-color;
      }
    }
    
    .name {
      font-weight: 500;
      
      div {
        line-height: 1.4;
        
        &:first-child {
          color: $primary-color;
          font-weight: 600;
        }
      }
    }
  }
  
  &.date {
    flex: 1;
    min-width: 180px;
    font-weight: 500;
    color: $text-secondary;
  }
  
  &.payment-type {
    flex: 1;
    min-width: 150px;
    position: relative;
    
    span {
      font-weight: 500;
      padding: 4px 8px;
      border-radius: 4px;
      background-color: rgba($secondary-color, 0.6);
      font-size: 14px;
    }
    
    .retorno-icon {
      width: 18px;
      height: 18px;
      margin-left: 8px;
    }
    
    .video-call-indicator {
      position: relative;
      margin-left: 8px;
      
      mat-icon {
        color: $accent-color;
      }
      
      .indicator {
        width: 8px;
        height: 8px;
        background-color: $error-color;
        border-radius: 50%;
        position: absolute;
        top: 0;
        right: 0;
        animation: pulse 1.5s infinite;
      }
    }
    
    .whatsapp-icon {
      width: 20px;
      height: 20px;
      transition: transform 0.2s ease;
      
      &:hover {
        transform: scale(1.2);
      }
    }
  }
  
  &.actions {
    flex: 2;
    min-width: 200px;
    display: flex;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: 4px;
    
    button {
      transition: all 0.2s ease;
      background-color: transparent;
      
      &:hover {
        background-color: $secondary-color;
        transform: translateY(-2px);
      }
    }
    
    .status-emoji {
      font-size: 18px;
      background-color: transparent;
    }
    
    .success-icon {
      color: $primary-color;
    }
    
    .start-button {
      color: white;
      background-color: $primary-color;
      
      &:hover {
        background-color: $primary-dark;
      }
      
      mat-icon {
        font-weight: bold;
      }
    }
    
    .status-stamp {
      .stamp-icon {
        width: 30px;
        height: 30px;
        transition: transform 0.2s ease;
        
        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

// Conteúdo expandido do painel (MELHORADO)
.panel-content {
  padding: 16px;
  border-top: 1px solid $border-color;
  background-color: rgba($primary-light, 0.4);
  border-radius: 0 0 $border-radius $border-radius;
}

.expanded-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.rating-container {
  display: flex;
  justify-content: center;
  padding: 8px 0;
  background-color: rgba(white, 0.6);
  border-radius: $border-radius;
  
  .star-rating {
    .stars {
      display: flex;
      
      label {
        margin: 0 2px;
        font-size: 20px;
        
        .fa:before {
          content: "\f005";
          color: #ffc107;
        }
      }
      
      input[type="radio"] {
        display: none;
        
        &:checked ~ label .fa:before {
          color: #ccc;
        }
      }
    }
  }
}

.info-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  background-color: rgba(white, 0.6);
  padding: 16px;
  border-radius: $border-radius;
}

.info-column {
  flex: 1;
  min-width: 250px;
  
  .info-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: $primary-dark;
    border-bottom: 2px solid rgba($primary-color, 0.3);
    padding-bottom: 4px;
  }
  
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    mat-icon {
      margin-right: 8px;
      font-size: 18px;
      color: $primary-color;
    }
    
    .label {
      color: $text-secondary;
      margin-right: 8px;
      font-weight: 500;
      box-shadow: none !important;
      background: transparent !important;
      border: none !important;
    }
    
    .highlight {
      font-weight: 600;
      color: $primary-color;
    }
    
    .quick-access {
      color: $primary-color;
      text-decoration: underline;
      cursor: pointer;
      
      &:hover {
        color: $primary-dark;
      }
    }
  }
  
  .observation {
    margin-top: 8px;
    color: $text-secondary;
    font-style: italic;
    padding: 8px;
    background-color: rgba($secondary-light, 0.6);
    border-radius: 4px;
    border-left: 3px solid rgba($primary-color, 0.6);
  }
  
  &.logo-column {
    flex: 0 0 150px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
  }
}

.logo-container {
  position: relative;
  
  .convenio-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    border: 3px solid rgba($primary-light, 0.8);
    transition: transform 0.2s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  .retorno-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 28px;
    height: 28px;
    background-color: white;
    border-radius: 50%;
    padding: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// Botão de carregar mais (MELHORADO)
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 8px;
  
  button {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

// Indicador de consultas vazias (MELHORADO)
.empty-list-indicator {
  padding: 32px 16px;
  text-align: center;
  color: $text-secondary;
  background-color: rgba($secondary-light, 0.6);
  border-radius: $border-radius;
  margin: 16px 0;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: rgba($primary-color, 0.6);
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    font-weight: 500;
  }
}

// Legenda
.legend-container {
  position: relative;
}

.legend-toggle {
  background-color: $secondary-color;
  
  &:hover {
    background-color: darken($secondary-color, 5%);
  }
}

.legend-panel {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 10;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 16px;
  min-width: 250px;
  
  .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    mat-icon {
      margin-right: 8px;
      color: $primary-color;
    }
    
    .icon-with-indicator {
      position: relative;
      margin-right: 8px;
      
      .indicator {
        width: 8px;
        height: 8px;
        background-color: $error-color;
        border-radius: 50%;
        position: absolute;
        top: 0;
        right: 0;
      }
    }
    
    span {
      color: $text-secondary;
    }
  }
}

// Animação de pulso para botões de ação
.pulse-button {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba($primary-color, 0.7);
  }
  
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba($primary-color, 0);
  }
  
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba($primary-color, 0);
  }
}

// Modais
.modal-container {
  padding: 0;
  border-radius: $border-radius;
  overflow: hidden;
}

.modal-header {
  background-color: $primary-color;
  color: white;
  padding: 16px 24px;
  
  .modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.modal-body {
  padding: 24px;
  
  .form-group {
    margin-bottom: 16px;
  }
  
  .validation-error {
    color: $error-color;
    font-size: 12px;
    margin-top: 4px;
  }
  
  .alert {
    padding: 12px;
    border-radius: 8px;
    margin: 16px 0;
    
    &.alert-danger {
      background-color: rgba($error-color, 0.1);
      color: $error-color;
      border: 1px solid rgba($error-color, 0.2);
    }
  }
  
  .payment-toggle, 
  .consultation-type {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 16px;
    
    span {
      color: $text-secondary;
    }
  }
  
  // Estilo para ng-select
  ::ng-deep {
    .ng-select {
      .ng-select-container {
        border-radius: 8px;
        border-color: $border-color;
        min-height: 48px;
        
        &:hover {
          border-color: $primary-color;
        }
        
        .ng-value-container {
          padding: 8px 12px;
        }
      }
      
      &.ng-select-focused {
        .ng-select-container {
          border-color: $primary-color;
          box-shadow: 0 0 0 1px $primary-color;
        }
      }
      
      .ng-dropdown-panel {
        border-radius: 8px;
        box-shadow: $box-shadow;
        
        .ng-dropdown-panel-items {
          .ng-option {
            padding: 12px;
            
            &.ng-option-selected,
            &.ng-option-selected.ng-option-marked {
              background-color: $primary-light;
              color: $primary-dark;
              font-weight: 600;
            }
            
            &.ng-option-marked {
              background-color: $secondary-light;
              color: $text-primary;
            }
          }
        }
      }
    }
  }
  
  // Estilo para mat-form-field
  ::ng-deep {
    .mat-form-field {
      width: 100%;
      
      &.mat-focused {
        .mat-form-field-outline {
          color: $primary-color;
        }
        
        .mat-form-field-label {
          color: $primary-color;
        }
      }
      
      .mat-form-field-wrapper {
        padding-bottom: 0;
      }
      
      .mat-form-field-outline {
        color: $border-color;
      }
      
      .mat-form-field-label {
        color: $text-secondary;
      }
      
      .mat-input-element {
        color: $text-primary;
      }
      
      .mat-form-field-required-marker {
        color: $error-color;
      }
      
      .mat-error {
        color: $error-color;
      }
      
      .mat-hint {
        color: $text-secondary;
      }
    }
    
    // Estilo para checkbox e radio button
    .mat-checkbox-checked .mat-checkbox-background,
    .mat-radio-button.mat-accent .mat-radio-inner-circle {
      background-color: $primary-color;
    }
    
    .mat-checkbox-frame {
      border-color: $border-color;
    }
    
    .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
      background-color: $primary-color;
    }
    
    .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
      background-color: rgba($primary-color, 0.5);
    }
    
    // Estilo para calendário
    .mat-calendar-header {
      background-color: $primary-color;
      padding: 8px;
      border-radius: 8px 8px 0 0;
      
      .mat-calendar-controls {
        .mat-calendar-period-button,
        .mat-icon-button {
          color: white;
        }
      }
    }
    
    .mat-calendar-table-header {
      color: $text-secondary !important;
    }
    
    .mat-calendar-body-selected {
      background-color: $primary-color;
      color: white;
    }
    
    .mat-calendar-body-today:not(.mat-calendar-body-selected) {
      border-color: $primary-color;
      color: $primary-color;
    }
  }
}

.modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid $border-color;
}
.actions-container{
  display: flex;
  justify-content: space-between;
}

// Responsividade
@media (max-width: 767px) {
  .modal-body {
    .row {
      margin: 0;
    }
    
    [class*="col-"] {
      padding: 0;
    }
  }
  
  .card-header {
    padding: 16px;
  }
  
  .panel-header-container {
    .panel-column {
      padding: 8px 0;
    }
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: flex-start;
  }
}

// Compatibilidade com material design
::ng-deep {
  // Estiliza componentes que podem ter sido migrados para MDC
  .mdc-text-field--outlined {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: $border-color;
    }
    
    &:hover .mdc-notched-outline__leading,
    &:hover .mdc-notched-outline__notch,
    &:hover .mdc-notched-outline__trailing {
      border-color: $primary-color;
    }
  }
  
  .mdc-text-field--focused {
    .mdc-notched-outline__leading,
    .mdc-notched-outline__notch,
    .mdc-notched-outline__trailing {
      border-color: $primary-color !important;
      border-width: 2px;
    }
  }
  
  // Correções de cor para botões mat
  .mat-mdc-raised-button.mat-primary,
  .mat-mdc-unelevated-button.mat-primary {
    background-color: $primary-color;
  }
  
  .mat-mdc-outlined-button.mat-primary {
    color: $primary-color;
    border-color: $primary-color;
  }
}

// Animações para transições suaves
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// Utility classes
.text-primary { color: $primary-color; }
.text-error { color: $error-color; }
.bg-primary { background-color: $primary-color; }
.bg-primary-light { background-color: $primary-light; }

// Animação de expansão do painel
::ng-deep {
  .mat-expansion-panel-body {
    padding: 0 !important;
  }
  
  .mat-expansion-indicator::after {
    color: $primary-color;
  }
}

// Melhorias adicionais para a interface
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -12px;
  
  [class*="col-"] {
    padding: 0 12px;
  }
}

// Estilos específicos para avaliação de estrelas
.estrelas {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  input[type="radio"] {
    display: none;
  }
  
  label i.fa:before {
    content: "\f005";
    color: #ffb800;
    font-size: 20px;
    transition: color 0.2s ease;
  }
  
  input[type="radio"]:checked ~ label i.fa:before {
    color: #ccc;
  }
  
  .usuario-principal {
    margin-left: 12px;
    color: $text-secondary;
    font-weight: 500;
  }
}

// Status e selos
.status-stamp {
  img {
    opacity: 0.8;
    transition: opacity 0.2s ease;
    
    &:hover {
      opacity: 1;
    }
  }
}

// Indicadores de status
.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: $error-color;
  position: absolute;
  top: 0;
  right: 0;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

// Tooltips aprimorados
::ng-deep {
  .mat-tooltip {
    background-color: rgba($text-primary, 0.9);
    border-radius: 4px;
    font-size: 12px;
    padding: 8px 12px;
    max-width: 250px;
  }
}


/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Estilização para o componente do modal */
:host {
  display: block;
  width: 100%;
  height: 100%;
}

/* Configurações para o modal usando ngx-smart-modal */
::ng-deep .modern-modal {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

::ng-deep .health-modal .nsm-content {
  border-radius: $border-radius !important;
  overflow: hidden !important;
  max-width: 600px !important;
  width: 95% !important;
  padding: 0 !important;
}

/* Container principal do modal */
.modal-container {
  display: flex;
  flex-direction: column;
  background-color: $bg-color;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
  background-color: $primary-color;
  padding: 16px 20px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.modal-title-container {
  flex: 1;
  text-align: center;
  max-width: 80%;
}

.modal-title {
  color: white;
  margin: 0;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  font-size: 20px;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color $transition ease;
  z-index: 11;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo principal */
.modal-content {
  flex: 1;
  padding: 24px;
  background-color: $secondary-light;
  display: flex;
  flex-direction: column;
}

/* Formulário */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  width: 100%;
}

.form-field {
  flex: 1;
  min-width: 200px;
}

.form-field.full-width {
  width: 100%;
  flex-basis: 100%;
}

/* Estilo dos inputs */
mat-form-field {
  width: 100%;
}

/* Customização dos campos do Angular Material */
::ng-deep .mat-mdc-form-field-flex {
  background-color: $secondary-light;
  height: 40px;
}

::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

::ng-deep .mat-mdc-text-field-wrapper {
  background-color: $secondary-light;
  height: 40px;
}

::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: transparent;
}

::ng-deep .mat-mdc-form-field-infix {
  min-height: 40px;
}

::ng-deep textarea.mat-mdc-input-element {
  resize: none;
}

::ng-deep .mat-mdc-form-field {
  display: inline-flex;
  flex-direction: column;
  min-width: 0;
  text-align: left;
}

::ng-deep .mat-mdc-form-field-appearance-outline .mat-mdc-form-field-outline {
  color: $border-color;
}

::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
  color: $primary-color;
}

::ng-deep .mat-mdc-input-element:disabled {
  color: $text-primary;
  font-weight: 500;
}

/* Rodapé do modal */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: $secondary-color;
  border-top: 1px solid $border-color;
}

/* Logo no rodapé */
.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.logo-image {
  max-height: 40px;
  max-width: 180px;
  object-fit: contain;
}

/* Botão de ação no rodapé */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: $border-radius;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition ease;
  min-width: 110px;
}

.close-button-footer {
  background-color: $primary-color;
  color: white;
}

.close-button-footer:hover {
  background-color: $primary-dark;
  transform: translateY(-2px);
  box-shadow: $box-shadow;
}

/* Responsividade */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
  
  .form-field {
    min-width: 100%;
  }
  
  .modal-footer {
    flex-direction: column;
    gap: 16px;
  }
  
  .logo-container {
    justify-content: center;
  }
  
  .action-button {
    width: 100%;
  }
}
/* Usaremos as mesmas variáveis definidas no arquivo anterior */

/* Estilos específicos para o modal de agendamento */
.split-content {
  display: flex;
  flex-direction: row;
  padding: 0;
  height: 100%;
}

/* Coluna da imagem ilustrativa */
.image-column {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba($primary-light, 0.2);
  overflow: hidden;
}

.appointment-image {
  width: 100%;
  height: 100%;
  background-image: url('/assets/images/appointment.jpg'); /* Substituir pelo caminho real da imagem */
  background-size: cover;
  background-position: center;
  /* Caso não tenha a imagem, usar um gradiente como fallback */
  background: linear-gradient(45deg, $primary-light, $primary-color);
  opacity: 0.8;
}

/* Coluna do formulário */
.form-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24px;
  background-color: $secondary-light;
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

/* Estilos para o ng-select */
.select-label {
  display: block;
  font-size: 14px;
  color: $text-secondary;
  margin-bottom: 8px;
  font-weight: 500;
}

.custom-select {
  width: 100%;
}

/* Botão de agendamento */
.schedule-button {
  background-color: $primary-color;
  color: white;
}

.schedule-button:hover {
  background-color: $primary-dark;
}

/* Responsividade */
@media (max-width: 768px) {
  .split-content {
    flex-direction: column;
  }
  
  .image-column {
    display: none; /* Esconde a imagem em telas pequenas */
  }
  
  .form-column {
    padding: 16px;
  }
}
/* Este arquivo contém apenas os estilos adicionais específicos para o modal de editar horário */
/* Os estilos base são reutilizados do modal anterior */

/* Estilos para o campo de informações do paciente com botão */
.form-field.with-icon {
  position: relative;
  padding-right: 40px;
}

.info-button {
  position: absolute;
  right: 0;
  top: 28px;
  background-color: transparent;
  border: none;
  color: $primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color $transition ease;
}

.info-button:hover:not([disabled]) {
  background-color: rgba($primary-color, 0.1);
}

.info-button[disabled] {
  color: $secondary-dark;
  cursor: not-allowed;
}

.info-button mat-icon {
  font-size: 20px;
}

/* Toggle de pagamento com cartão */
.payment-toggle {
  padding: 12px 0;
  border-top: 1px solid $border-color;
  border-bottom: 1px solid $border-color;
  margin: 16px 0;
}

.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.toggle-container span {
  font-size: 14px;
  color: $text-primary;
  font-weight: 500;
}

/* Checkbox field */
.checkbox-field {
  display: flex;
  align-items: center;
  height: 48px;
}

/* Estilização do grupo de radio buttons */
.radio-group-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 8px 0;
}

.radio-group-label {
  font-size: 14px;
  color: $text-primary;
  font-weight: 500;
}

.radio-group {
  display: flex;
  gap: 24px;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-inner-circle {
  background-color: $primary-color !important;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-outer-circle {
  border-color: $primary-color !important;
}

::ng-deep .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
  border-color: $primary-color !important;
}

::ng-deep .mat-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
  background-color: rgba($primary-color, 0.2) !important;
}

/* Customização do slide toggle */
::ng-deep .mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb {
  background-color: $primary-color !important;
}

::ng-deep .mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar {
  background-color: rgba($primary-color, 0.5) !important;
}

/* Responsividade específica para este modal */
@media (max-width: 768px) {
  .radio-group {
    flex-direction: column;
    gap: 12px;
  }
  
  .toggle-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

