import { <PERSON><PERSON><PERSON> } from './endereco';
import { Contato } from './contato';
import { Usuario, UsuarioClinica } from './usuario';
import { Clinica } from './clinica';

    export class Pessoa {

        idPessoa?: number;
        nomePessoa?: string;
        idContato?: number;
        idEndereco?: number;
        foto?: string;
        sexo?: string;
        cpf?: string;
        profissao?: string;
        naturalidade?: string;
        nascionalidade?: string;
        dtaNascimento?: Date | null;
        flgInativo?: boolean;
        idUsuarioGerador?: number;
        idUsuarioAcesso?:number;
        dtaCadastro?: Date;
        email?:string;

        endereco?: Endereco;
        contato?: Contato;
        usuario?: Usuario;

        imagem64?: string;
        removerFoto?: boolean;
        usuarioClinica?: UsuarioClinica[];
        clinicas?: Clinica[];


    }
