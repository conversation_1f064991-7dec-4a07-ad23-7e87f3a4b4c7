import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { AlertComponent } from 'src/app/alert/alert.component';
import { ObjetoPadrao } from 'src/app/model/RetornoPadraoApi';
import { SpinnerService } from 'src/app/service/spinner.service';
import { WhatsService } from 'src/app/service/whats.service';

@Component({
    selector: 'app-modal-confirma-chegada-paciente',
    templateUrl: './modal-confirma-chegada-paciente.component.html',
    styleUrls: ['./modal-confirma-chegada-paciente.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon
    ]
})

export class ModalConfirmaChegadaPacienteComponent implements OnInit {

  constructor(
    private dialogRef: MatDialogRef<ModalConfirmaChegadaPacienteComponent>,
    private whatsService: WhatsService,
    @Inject(MAT_DIALOG_DATA) public idConsulta: number,
    private spinner: SpinnerService,
    private snackBar: AlertComponent
  ) {}

  ngOnInit(): void {}

  validaSalvar(){
    if(!this.idConsulta){
      this.snackBar.falhaSnackbar("Falha ao salvar, consulta não selecionada corretamente");
      return false;
    }

    return true;
  }

  confirmarChegada(opcao: boolean) {
    if(!this.validaSalvar())
      return;

    this.spinner.show();
    let obj: ObjetoPadrao = new ObjetoPadrao();

    obj.num = this.idConsulta;
    obj.flg = opcao
    this.whatsService.ConfirmarChegadaPaciente(obj).subscribe(() => {
      this.spinner.hide();
      this.fecharModal(true);
    },
      (error) => {
        this.snackBar.falhaSnackbar("Falha ao confirmar o status do paciente",error);
        this.spinner.hide();
      })

  }

  fecharModal(flgEditado: boolean) {
    this.dialogRef.close(flgEditado);
  }
}
