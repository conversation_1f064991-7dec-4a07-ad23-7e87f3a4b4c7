import { Injectable } from "@angular/core";
import { Subject } from "rxjs";
import { documentosModal } from '../model/DocumentosModal';

@Injectable({
    providedIn: 'root'
})
export class documentosModalService {

    abrirModal: Subject<any> = new Subject<any>();

    setAbrirModal(abrirModal: documentosModal) {
        
        this.abrirModal.next(abrirModal);
    }

    getAbrirModal() {
        return this.abrirModal.asObservable();
    }


}