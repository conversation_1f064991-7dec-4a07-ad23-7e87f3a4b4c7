<div class="container">
  <mat-card class="card-principal">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">group</span>
      </div>
      <h2 class="header-title">{{ 'TELAPESQUISAUSUARIO.TITULO' | translate }}</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">

      <div class="busca-container">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>{{ 'TELAPESQUISAUSUARIO.BUSCAR' | translate }}</mat-label>
          <input matInput (keyup.enter)="CarregaTable()" [(ngModel)]="pesquisa">
          <button mat-icon-button matSuffix (click)="CarregaTable()" class="btn-busca">
            <mat-icon>search</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <div class="adicionar-container">
        <button mat-raised-button class="btn-adicionar" [routerLink]="['/CadastroAtendente']">
          <mat-icon>add</mat-icon>
          <span>{{ 'TELAPESQUISAUSUARIO.ADICIONARATENDENTE' | translate }}</span>
        </button>
      </div>

      <div class="toggles-container">
        <mat-slide-toggle class="toggle-item" [(ngModel)]='Foto'>
          {{ 'TELAPESQUISAUSUARIO.MOSTRARFOTO' | translate }}
        </mat-slide-toggle>
        <mat-slide-toggle class="toggle-item" [(ngModel)]='inativos' (change)="CarregaTable()">
          {{ 'TELAPESQUISAUSUARIO.INATIVOS' | translate }}
        </mat-slide-toggle>
      </div>
    </div>

    <!-- LISTA DE ATENDENTES - DESKTOP VIEW -->
    <div class="lista-container desktop-view">
      <div class="lista-scroll">
        <div class="atendente-card" *ngFor="let item of DadosTab">
          <!-- INFO DO ATENDENTE COM FOTO -->
          <div class="atendente-info" *ngIf="Foto">
            <div class="atendente-avatar">
              <img *ngIf="item.sexo == 'Feminino'" src="{{item.imagenUsuario == null ? 'assets/build/img/userdefault.png' : item.imagenUsuario}}" class="img-circle" alt="Foto do atendente">
              <img *ngIf="item.sexo != 'Feminino'" src="{{item.imagenUsuario == null ? 'assets/build/img/userdefault.png' : item.imagenUsuario}}" class="img-circle" alt="Foto do atendente">
            </div>
            <div class="atendente-detalhes">
              <div class="info-item">
                <mat-icon>face</mat-icon>
                <span class="nome">{{item.nome | truncate : 15 : "…"}}</span>
              </div>
              <div class="info-item">
                <mat-icon>cake</mat-icon>
                <span>{{item.dtaNascimento | date: 'dd/MM/yyyy'}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span title="{{item.email}}">{{item.email }}</span>
              </div>
            </div>
          </div>

          <!-- INFO DO ATENDENTE SEM FOTO -->
          <div class="atendente-info" *ngIf="!Foto">
            <div class="atendente-detalhes sem-foto">
              <div class="info-item">
                <mat-icon>face</mat-icon>
                <span class="nome">{{item.nome | truncate : 17 : "…" }}</span>
              </div>
              <div class="info-item">
                <mat-icon>cake</mat-icon>
                <span>{{item.dtaNascimento | date: 'dd/MM/yyyy'}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span title="{{item.email}}">{{item.email }}</span>
              </div>
            </div>
          </div>

          <!-- DADOS DE CADASTRO -->
          <div class="atendente-dados" [ngClass]="{'com-foto': Foto, 'sem-foto': !Foto}">
            <div class="dados-item">
              <label class="dados-label">{{ 'TELAPESQUISAUSUARIO.DATADECADASTRO' | translate }}</label>
              <span class="dados-valor">{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
            </div>
          </div>

          <!-- AÇÕES -->
          <div class="atendente-acoes">
            <button mat-icon-button matTooltip="Perfil" (click)="PerfilAtendente(item.idUsuarioacesso)">
              <mat-icon>account_circle</mat-icon>
            </button>
            <button mat-icon-button [matTooltip]="item.flgEmail ? 'TELAPESQUISAUSUARIO.EMAILENVIADO' : 'TELAPESQUISAUSUARIO.EMAILDEBOASVINDAS' | translate" (click)="ModalEmail(item.idUsuarioacesso,item.idAtendente)">
              <mat-icon *ngIf="!item.flgEmail">mail_outline</mat-icon>
              <mat-icon *ngIf="item.flgEmail">mark_email_read</mat-icon>
            </button>
            <button mat-icon-button matTooltip="{{ 'TELAPESQUISAUSUARIO.EDITAR' | translate }}" (click)="editUsuario(item.idAtendente)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button class="remove" matTooltip="Inativar" *ngIf="!inativos" (click)="ModalInativarAtendente(item.idAtendente)">
              <mat-icon>delete</mat-icon>
            </button>
            <button mat-icon-button matTooltip="{{ 'TELAPESQUISAUSUARIO.ATIVAR' | translate }}" *ngIf="inativos" (click)="ModalAtivarAtendente(item.idAtendente)">
              <mat-icon>check</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="DadosTab?.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhum atendente encontrado</p>
        </div>
        
        <!-- BOTÃO CARREGAR MAIS -->
        <div class="carregar-mais" *ngIf="(DadosTab != undefined && DadosTab.length > 0) && bOcultaCarregaMais == false">
          <button mat-flat-button class="btn-carregar" (click)="CarregarMais()">
            {{ 'TELAPESQUISAUSUARIO.CARREGARMAIS' | translate }}
          </button>
        </div>
      </div>
    </div>
  </mat-card>
</div>

<!-- MODAL EMAIL -->
<ngx-smart-modal #emailUsuario identifier="emailUsuario" customClass="modal-container emailmodal" [dismissable]="false">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Email de Acesso</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAUSUARIO.DESEJAENVIARESSEEMAIL' | translate }}</p>
      <p class="modal-subtexto">{{ 'TELAPESQUISAUSUARIO.RECEBERAOEMAILCOMASENHA' | translate }}</p>

      <div class="alerta-inativo" *ngIf="inativos">
        <mat-icon>warning</mat-icon>
        <p>{{ 'TELAPESQUISAUSUARIO.CADASTROESTAINATIVO' | translate }}</p>
        <p class="alerta-subtexto">{{ 'TELAPESQUISAUSUARIO.ATIVANOVAMENTE' | translate }}</p>
      </div>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="emailUsuario.close()">
        {{ 'TELAPESQUISAMEDICO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" *ngIf="!inativos" (click)="mandaEmail()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" *ngIf="inativos" (click)="mandaEmailAtivarUser()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL INATIVAR -->
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Confirmação</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">Você tem certeza que deseja inativar este usuário?</p>
      <p class="modal-subtexto">O usuário ficará inativo!</p>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="excluirItem.close()">
        {{ 'TELAPESQUISAUSUARIO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-excluir" (click)="InativarUsuario()">
        {{ 'TELAPESQUISAUSUARIO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL ATIVAR -->
<ngx-smart-modal #ativarItem identifier="ativarItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Ativar Cadastro</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAUSUARIO.DESEJAATIVARESTEUSUARIO' | translate }}</p>
      <p class="modal-subtexto">{{ 'TELAPESQUISAUSUARIO.OUSUARIOTERAACESSOAOSISTEMA' | translate }}</p>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="ativarItem.close()">
        {{ 'TELAPESQUISAUSUARIO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" (click)="AtivarUsuario()">
        {{ 'TELAPESQUISAUSUARIO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>