import { UsuarioLogadoService } from './../../../auth/usuarioLogado.service';
import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { AlertComponent } from 'src/app/alert/alert.component';
import { AnaliseMensagemModelView } from 'src/app/model/analise';
import { AnaliseService } from 'src/app/service/analise.service';
import { SpinnerService } from 'src/app/service/spinner.service';

// Interface para os dados passados para o dialog
interface ChatDialogData {
  idAnalise: number;
  flgApenasVisualizacao?: boolean;
}

@Component({
  selector: 'app-chat-interno-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatInputModule,
    MatButtonModule,
    MatCheckboxModule,
    MatIconModule,
    MatBadgeModule
  ],
  templateUrl: './chat-interno-modal.component.html',
  styleUrl: './chat-interno-modal.component.scss'
})
export class ChatInternoModalComponent implements OnInit {

  messages: AnaliseMensagemModelView[] = [];
  sortedMessages: AnaliseMensagemModelView[] = [];
  objetoMensagem = new AnaliseMensagemModelView();
  flgApenasVisualizacao: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<ChatInternoModalComponent>,
    private analiseService: AnaliseService,
    @Inject(MAT_DIALOG_DATA) public data: ChatDialogData,
    private spinner: SpinnerService,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBarAlert: AlertComponent,
  ) {
  }

  idAnalise: number | null = 0;
  idPessoaLogado: number = 0;

  ngOnInit() {
    this.idAnalise = this.data.idAnalise;
    this.flgApenasVisualizacao = this.data.flgApenasVisualizacao || false;
    this.idPessoaLogado = this.usuarioLogadoService.getIdPessoa()!;
    this.recuperaMensagens();
  }

  sortMessages() {
    let messagesToSort = [...this.messages];

    if (this.flgApenasVisualizacao) {
      messagesToSort = messagesToSort.filter(msg => msg.flgMensagemImportante);
    }

    this.sortedMessages = messagesToSort.sort((a, b) => {
      const dateA = this.ensureDate(a.dtHoraEnvio);
      const dateB = this.ensureDate(b.dtHoraEnvio);

      return dateA.getTime() - dateB.getTime();
    });
  }

  private ensureDate(date: Date | string | undefined): Date {
    if (date instanceof Date) {
      return date;
    }
    if (typeof date === 'string') {
      return new Date(date);
    }
    return new Date();
  }

  recuperaMensagens() {
    this.spinner.show();
    this.analiseService.getMensaems(this.idAnalise!).subscribe({
      next: (ret) => {
        this.messages = ret;
        this.messages.forEach(msg => {
          if (msg.dtHoraEnvio && typeof msg.dtHoraEnvio === 'string') {
            msg.dtHoraEnvio = new Date(msg.dtHoraEnvio);
          }
        });

        this.sortMessages();
        this.spinner.hide();
      },
      error: (error) => {
        console.error('Erro ao recuperar mensagens:', error);
        this.spinner.hide();
      }
    });
  }

  sendMessage() {
    if (!this.objetoMensagem.mensagem.trim()) {
      this.snackBarAlert.falhaSnackbar("Erro ao enviar mensagem, escreva primeiramente o conteudo da mensagem");
      return;
    }

    this.objetoMensagem.idAnalise = this.idAnalise!;
    this.objetoMensagem.idPessoa = this.usuarioLogadoService.getIdPessoa();

    if (this.objetoMensagem.idAnalise == null || this.objetoMensagem.idPessoa == null) {
      this.snackBarAlert.falhaSnackbar("Erro ao enviar mensagem, não foi possivel completar os dados");
      return;
    }

    this.spinner.show();
    this.analiseService.salvaMensagemInterna(this.objetoMensagem).subscribe({
      next: (ret) => {
        if (ret) {
          this.objetoMensagem = new AnaliseMensagemModelView();
          this.snackBarAlert.sucessoSnackbar("mensagem enviada");
          this.recuperaMensagens();
        } else {
          this.snackBarAlert.falhaSnackbar("Erro ao salvar");
          this.spinner.hide();
        }
      },
      error: (error) => {
        console.error('Erro ao salvar mensagem:', error);
        this.snackBarAlert.falhaSnackbar("Erro ao salvar");
        this.spinner.hide();
      }
    });
  }

  close(): void {
    this.dialogRef.close();
  }
}