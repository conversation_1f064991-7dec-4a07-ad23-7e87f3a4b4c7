<div class="medico-container">
    <div class="card main-card">
      <div class="card-header">
        <div class="header-left">
          <div class="icon-container">
            <span class="material-icons">medical_services</span>
          </div>
          <h1 class="page-title">{{ 'TELACADASTROMEDICO.TITULO' | translate }}</h1>
        </div>
        <button class="btn btn-link" onclick='history.go(-1)'>
          <span class="material-icons">arrow_back</span>
        </button>
      </div>
  
      <div class="card-body">
        <!-- Seção de Informações Pessoais -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACADASTROMEDICO.ACCOUNT' | translate }}</h2>
          </div>
          <div class="section-content">
            <!-- Foto de Perfil -->
            <div class="profile-upload">
              <label for="imageperfilusuario" class="photo-container">
                <img src="{{ ImagemPessoa }}" class="profile-photo" alt="Foto de Perfil" 
                     title="{{ 'TELACADASTROMEDICO.FOTODEPERFIL' | translate }}">
                <div class="photo-overlay">
                  <span class="material-icons">photo_camera</span>
                </div>
              </label>
              <input type="file" id="imageperfilusuario" (change)="AlterarImagemPessoa($event)" 
                    (click)="LimpaCampoFile()" />
              <input type="text" style="display: none;" id="Foto" name="Foto" [(ngModel)]="Dados.foto">
            </div>
            
            <!-- Linhas de formulário -->
            <div class="form-row">
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.NOME' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.NOME' | translate }}" 
                        name="Nome" required [(ngModel)]="Dados.nome" #Nome="ngModel"
                        (keyup)="mascaraText($event, 'Nome')" (change)="mascaraText($any($event), 'Nome')" 
                        maxlength="100" type="text">
                  <mat-error *ngIf="Nome.invalid">{{getErrorMessageNome() | translate }}</mat-error>
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.EMAIL' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.EMAIL' | translate }}" 
                         type="email" name="Email" required [(ngModel)]="Dados.email" 
                         (change)='validarEmailMedico($any($event.target).value)' 
                         id="email" maxlength="50">
                </mat-form-field>
                <div class="field-error" *ngIf="campoEmailInvalido">Email inválido</div>
                <div class="field-error" *ngIf="campoEmailVazil && !Dtanasc">Esse campo precisa ser preenchido</div>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.DATADENASCIMENTO' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.DATADENASCIMENTO' | translate }}" 
                        mask="00/00/0000" name="Data de Nascimento" id="DtaNasc" 
                        (keyup)="mascaraData($event)" [(ngModel)]="Dados.dtaNascimento" 
                        maxlength="10" (blur)="ValidaDta($any($event.target).value)">
                </mat-form-field>
                <div class="field-error" *ngIf="Dtanasc">{{ 'TELACADASTROMEDICO.ERRODATA' | translate }}</div>
              </div>
              
              <div class="form-group col-md-4">
                <div class="select-container">
                  <ng-select class=""
                            [items]="DadosSexo" 
                            placeholder="{{ 'TELACADASTROMEDICO.SEXO' | translate }}" 
                            name="Sexo" bindValue="DadosSexo.id" bindLabel="" 
                            (focus)="carregaDadosSexo()" 
                            notFoundText="Sexo não encontrado" 
                            (keyup)="mascaraText($event, 'Sexo')" 
                            [selectOnTab]="true" 
                            [(ngModel)]="campoSexo">
                  </ng-select>
                </div>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.CPF' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.CPF' | translate }}" 
                        name="CPF" id="CPF" 
                        (change)='validarCpf($any($event.target).value)' 
                        (keypress)="mascaraCpf('###.###.###-##', $any($event))" 
                        minlength="14" mask="000.000.000-00" maxlength="14" 
                        required [(ngModel)]="Dados.cpf">
                </mat-form-field>
                <div class="field-error" *ngIf="campoCPFInvalido">CPF inválido</div>
                <div class="field-error" *ngIf="campoCPFVazil && !campoCPFInvalido">Esse campo precisa ser preenchido</div>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.TELEFONE' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.TELEFONE' | translate }}" 
                        name="Telefone" (keyup)="mascaraTelefone($any($event))" 
                        (keypress)="mascaraTelefone($any($event))" 
                        (change)="ValidaTelefone($any($event.target).value)" 
                        maxlength="15" minlength="14" [(ngModel)]="Dados.telefone">
                </mat-form-field>
                <div class="field-error" *ngIf="TelVal">{{ 'TELACADASTROMEDICO.ERROTELEFONE' | translate }}</div>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.CELUAR' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.CELUAR' | translate }}" 
                        (change)='validarTelMovel($any($event.target).value)' 
                        required name="Telefone Movel" 
                        (keyup)="mascaraTelefone($event)" 
                        (keypress)="mascaraTelefone($event)" 
                        (change)="ValidaTelefoneMovel($any($event.target).value)" 
                        maxlength="15" minlength="14" [(ngModel)]="Dados.telefoneMovel" 
                        id="telcelular">
                </mat-form-field>
                <div class="field-error" *ngIf="TelMovVal">{{ 'TELACADASTROMEDICO.ERROTELEFONE' | translate }}</div>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.TELEFONECOMERCIAL' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.TELEFONECOMERCIAL' | translate }}" 
                        name="Telefone Comercial" (keyup)="mascaraTelefone($event)" 
                        (keypress)="mascaraTelefone($event)" 
                        (change)="ValidaTelefoneComercial($any($event.target).value)" 
                        maxlength="15" minlength="14" [(ngModel)]="Dados.telefoneComercial">
                </mat-form-field>
                <div class="field-error" *ngIf="TelComVal">{{ 'TELACADASTROMEDICO.ERROTELEFONE' | translate }}</div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Seção de Informações Profissionais -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACADASTROMEDICO.WORK' | translate }}</h2>
          </div>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.VALORCONSULTA' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.VALORCONSULTA' | translate }}" 
                        name="valor" id="valorConsulta" [(ngModel)]="Dados.valorConsulta" 
                        (keypress)="mascaraValor($event)" 
                        (change)="mascaraValor($any($event))" 
                        (keyup)="mascaraValor($event)" maxlength="15">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.CRM' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.CRM' | translate }}" 
                        name="CRM" required [(ngModel)]="Dados.crm" #crm="ngModel" 
                        maxlength="15">
                  <mat-error *ngIf="crm.invalid">{{geterrorMessageCRM() | translate }}</mat-error>
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.ESPECIALIDADES' | translate }}</mat-label>
                  <mat-select placeholder="{{ 'TELACADASTROMEDICO.ESPECIALIDADES' | translate }}" 
                             multiple name="especialidade" 
                             (keyup)="mascaraText($event, 'especialidade')" 
                             (blur)="ValidaEspecialidades($any($event.target).value)">
                    <mat-option *ngFor="let item of DadosEspecialidade;let i = index" [value]="item">
                      {{item.desEspecialidade | truncate : 40 : "…"}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.CLINICAS' | translate }}</mat-label>
                  <mat-select placeholder="{{ 'TELACADASTROMEDICO.CLINICAS' | translate }}" 
                             required multiple name="clinicas" 
                             (keyup)="mascaraText($event, 'clinicas')">
                    <mat-option *ngFor="let item of DadosClinicas;let i = index" [value]="item">
                      {{item.desClinica | truncate : 40 : "…"}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
                <div class="field-error" *ngIf="clinicaVal">{{ 'TELACADASTROMEDICO.ERROCLINICAS' | translate }}</div>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>% {{ 'TELACADASTROMEDICO.RETIDO' | translate }}</mat-label>
                  <input matInput placeholder="% {{ 'TELACADASTROMEDICO.RETIDO' | translate }}" 
                        name="retidoClinica" id="retidoClinica" 
                        [(ngModel)]="Dados.retidoClinica" type="number" 
                        maxlength="15">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.CODIGOCONVENIO' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.CODIGOCONVENIO' | translate }}" 
                        name="codigoConvenio" id="codigoConvenio" 
                        [(ngModel)]="Dados.codigoConvenio" maxlength="15">
                </mat-form-field>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Tipo de Orientação</mat-label>
                  <mat-select placeholder="Tipo de orientação" name="tipoOrientacao" 
                             (blur)="ValidaEspecialidades($any($event.target).value)" 
                             [(ngModel)]="idTipoOrientacao">
                    <mat-option *ngFor="let item of objTipoOrientacao;let i = index" 
                               [value]="item.idTipoOrientacao">
                      {{item.tipoOrientacao! | truncate : 40 : "…"}}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
            
            <!-- Certificados Digitais -->
            <div class="certificates-section" *ngIf="flgMostraCertificados">
              <div class="certificates-header">
                <div class="certificate-icon">
                  <img src="{{imagemCertificado}}" alt="Certificado Digital">
                </div>
                <h3>Certificados Digitais</h3>
              </div>
              
              <div class="certificates-content">
                <div class="certificate-upload">
                  <label for="certificado" class="upload-label">
                    <span class="material-icons">cloud_upload</span>
                    <span>Cadastrar Certificado</span>
                    <input type="file" id="certificado" #certificadoinp
                           (change)="CadastrarCertificadoMedico(certificadoinp)"
                           accept=".xls,.xlsx,.pdf,.jpg" />
                    <input type="text" style="display: none;" id="Certifi" name="Certifi"
                           [(ngModel)]="Dados.certificado">
                  </label>
                </div>
                
                <div class="certificates-list">
                  <div class="certificates-table-container" *ngIf="retornoCertificados.length > 0">
                    <h4>Certificados Cadastrados</h4>
                    <table class="certificates-table">
                      <tbody>
                        <tr *ngFor="let item of retornoCertificados">
                          <td class="cert-id">Identificador: {{item.identificadorCertificado}}</td>
                          <td class="cert-date">Data de Criação: {{item.dtaCadastro | date:'dd/MM/yyyy HH:mm'}}</td>
                          <td class="cert-date">Data Validade: {{item.dtaValidade | date:'dd/MM/yyyy HH:mm'}}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <div class="no-certificates" *ngIf="retornoCertificados.length <= 0">
                    <p>Não há certificado cadastrado</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Seção de Endereço -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACADASTROMEDICO.ENDERECO' | translate }}</h2>
          </div>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.RUA' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.RUA' | translate }}" 
                        name="Rua" [(ngModel)]="Dados.rua" maxlength="150">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-2">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.NUMERO' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.NUMERO' | translate }}" 
                        name="n°" [(ngModel)]="Dados.numero" maxlength="10" 
                        (keyup)="mascaraNumeros($event)">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.COMPLEMENTO' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.COMPLEMENTO' | translate }}" 
                        name="Complemento" [(ngModel)]="Dados.complemento">
                </mat-form-field>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-3">
                <div class="select-container">
                  <ng-select class=""
                            [items]="dadosUF" 
                            placeholder="{{ 'TELACADASTROMEDICO.UF' | translate }}" 
                            bindLabel="siglasUf" bindValue="siglasUf" 
                            name="UF" maxlength="2" 
                            (keyup)="mascaraText($event, 'siglasUf')" 
                            (change)="CidadePorUF()" 
                            [selectOnTab]="true" 
                            notFoundText="{{'TELACADASTROMEDICO.UFNAOENCONTRADA' | translate}}" 
                            [(ngModel)]="Dados.uf">
                  </ng-select>
                </div>
              </div>
              
              <div class="form-group col-md-3">
                <div class="select-container">
                  <ng-select class=""
                            [items]="dadosCidadeUf" 
                            placeholder="{{ 'TELACADASTROMEDICO.MUNICIPIO' | translate }}" 
                            bindLabel="nmeCidade" bindValue="idCidade" 
                            (keyup)="mascaraText($event,'nmeCidade')" 
                            name="Municipio" 
                            [selectOnTab]="true" 
                            notFoundText="{{'TELACADASTROMEDICO.UFNAOENCONTRADA' | translate}}" 
                            [(ngModel)]="Dados.idCidade">
                  </ng-select>
                </div>
              </div>
              
              <div class="form-group col-md-3">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.BAIRRO' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.BAIRRO' | translate }}" 
                        name="Bairro" [(ngModel)]="Dados.bairro">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-3">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROMEDICO.CEP' | translate }}</mat-label>
                  <input matInput placeholder="{{ 'TELACADASTROMEDICO.CEP' | translate }}" 
                        name="CEP" mask="00000-000" maxlength="9" 
                        [(ngModel)]="Dados.cep">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Seção de Horários -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACADASTROMEDICO.DIASPROGRAMACAO' | translate }}</h2>
          </div>
          <div class="section-content schedule-content">
            <div class="schedule-table-container">
              <table class="schedule-table">
                <thead>
                  <tr>
                    <th class="schedule-header-cell"></th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.SEGUNDA' | translate }}</th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.TERCA' | translate }}</th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.QUARTA' | translate }}</th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.QUINTA' | translate }}</th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.SEXTA' | translate }}</th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.SABADO' | translate }}</th>
                    <th class="schedule-header-cell">{{ 'TELACADASTROMEDICO.DOMINGO' | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Linha de Habilitar/Desabilitar -->
                  <tr>
                    <td class="schedule-label">{{ 'TELACADASTROMEDICO.HABILITARDESABILITAR' | translate }}</td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Segundacheck','Segunda')" 
                              id="Segundacheck" name="Segundacheck" [(ngModel)]="Segunda">
                      </div>
                    </td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Tercacheck','Terça')" 
                              id="Tercacheck" name="Tercacheck" [(ngModel)]="Terca">
                      </div>
                    </td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Quartacheck','Quarta')" 
                              id="Quartacheck" name="Quartacheck" [(ngModel)]="Quarta">
                      </div>
                    </td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Quintacheck','Quinta')" 
                              id="Quintacheck" name="Quintacheck" checked [(ngModel)]="Quinta">
                      </div>
                    </td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Sextacheck','Sexta')" 
                              id="Sextacheck" name="Sextacheck" checked [(ngModel)]="Sexta">
                      </div>
                    </td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Sabadocheck','Sabado')" 
                              id="Sabadocheck" name="Sabadocheck" [(ngModel)]="Sabado">
                      </div>
                    </td>
                    <td class="schedule-cell">
                      <div class="checkbox-container">
                        <input type="checkbox" (click)="BlockDiaSemana('Domingocheck','Domingo')" 
                              id="Domingocheck" name="Domingocheck" [(ngModel)]="Domingo">
                      </div>
                    </td>
                  </tr>
                  
                  <!-- Linha de Hora Início -->
                  <tr>
                    <td class="schedule-label">{{ 'TELACADASTROMEDICO.INICIO' | translate }}</td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #SegundaInicio4 id="SegundaInicio" 
                             name="Segunda Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(SegundaInicio4.value,$event.target)" 
                             [disabled]='Segunda == false' [(ngModel)]="segundaHorario.SegundaInicio">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #TercaInicio4 id="TercaInicio" 
                             name="Terça Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(TercaInicio4.value,$event.target)" 
                             [disabled]='Terca == false' [(ngModel)]="tercaHorario.TercaInicio">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #QuartaInicio4 id="QuartaInicio" 
                             name="Quarta Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(QuartaInicio4.value,$event.target)" 
                             [disabled]='Quarta == false' [(ngModel)]="quartaHorario.QuartaInicio">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #QuintaInicio4 id="QuintaInicio" 
                             name="Quinta Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(QuintaInicio4.value,$event.target)" 
                             [disabled]='Quinta == false' [(ngModel)]="quintaHorario.QuintaInicio">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #SextaInicio4 id="SextaInicio" 
                             name="Sexta Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(SextaInicio4.value,$event.target)" 
                             [disabled]='Sexta == false' [(ngModel)]="sextaHorario.SextaInicio">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #SabadoInicio4 id="SabadoInicio" 
                             name="Sabado Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(SabadoInicio4.value,$event.target)" 
                             [disabled]='Sabado == false' [(ngModel)]="sabadoHorario.SabadoInicio">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" #DomingoInicio4 id="DomingoInicio" 
                             name="Domingo Inicio" pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(DomingoInicio4.value,$event.target)" 
                             [disabled]='Domingo == false' [(ngModel)]="domingoHorario.DomingoInicio">
                    </td>
                  </tr>
                  
                  <!-- Linha de Início Intervalo -->
                  <tr>
                    <td class="schedule-label">{{ 'TELACADASTROMEDICO.INICIOINTERVALO' | translate }}</td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SegundaInicioInt" 
                             name="Segunda inicio intervalo" [disabled]='Segunda == false' 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [(ngModel)]="segundaHorario.SegundaInicioInt">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="TercaInicioInt" 
                             name="Terça inicio intervalo" [disabled]='Terca == false' 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [(ngModel)]="tercaHorario.TercaInicioInt">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuartaInicioInt" 
                             name="Quarta inicio intervalo" pattern="[0-9]{2}:[0-9]{2}" 
                             min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [disabled]='Quarta == false' [(ngModel)]="quartaHorario.QuartaInicioInt">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuintaInicioInt" 
                             name="Quinta inicio intervalo" pattern="[0-9]{2}:[0-9]{2}" 
                             min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [disabled]='Quinta == false' [(ngModel)]="quintaHorario.QuintaInicioInt">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SextaInicioInt" 
                             name="Sexta inicio intervalo" pattern="[0-9]{2}:[0-9]{2}" 
                             min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [disabled]='Sexta == false' [(ngModel)]="sextaHorario.SextaInicioInt">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SabadoInicioInt" 
                             name="Sabado inicio intervalo" pattern="[0-9]{2}:[0-9]{2}" 
                             min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [disabled]='Sabado == false' [(ngModel)]="sabadoHorario.SabadoInicioInt">
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="DomingoInicioInt" 
                             name="Domingo inicio intervalo" pattern="[0-9]{2}:[0-9]{2}" 
                             min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter($any($event.target).value,$event.target)" 
                             [disabled]='Domingo == false' [(ngModel)]="domingoHorario.DomingoInicioInt">
                    </td>
                  </tr>
                  
                  <!-- Linha de Fim Intervalo -->
                  <tr>
                    <td class="schedule-label">{{ 'TELACADASTROMEDICO.FIMINTERVALO' | translate }}</td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SegundaFimInt" 
                             name="Segunda fim do intervalo" #SegundaFimIntInput 
                             (blur)="ValidaTimerInter(SegundaFimIntInput.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="segundaHorario.SegundaFimInt" [disabled]='Segunda == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="TercaFimInt" 
                             name="Terça fim do intervalo" #TercaFimIntInput 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter(TercaFimIntInput.value,$event.target)" 
                             [(ngModel)]="tercaHorario.TercaFimInt" [disabled]='Terca == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuartaFimInt" 
                             name="Quarta fim do intervalo" #QuartaFimIntInput 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter(QuartaFimIntInput.value,$event.target)" 
                             [(ngModel)]="quartaHorario.QuartaFimInt" [disabled]='Quarta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuintaFimInt" 
                             name="Quinta fim do intervalo" #QuintaFimInt 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter(QuintaFimInt.value,$event.target)" 
                             [(ngModel)]="quintaHorario.QuintaFimInt" [disabled]='Quinta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SextaFimInt" 
                             name="Sexta fim do intervalo" #SextaFimIntInput 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter(SextaFimIntInput.value,$event.target)" 
                             [(ngModel)]="sextaHorario.SextaFimInt" [disabled]='Sexta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SabadoFimInt" 
                             name="Sabado fim do intervalo" #SabadoFimIntInput 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter(SabadoFimIntInput.value,$event.target)" 
                             [(ngModel)]="sabadoHorario.SabadoFimInt" [disabled]='Sabado == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="DomingoFimInt" 
                             name="Domingo fim do intervalo" #DomingoFimIntInput 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerInter(DomingoFimIntInput.value,$event.target)" 
                             [(ngModel)]="domingoHorario.DomingoFimInt" [disabled]='Domingo == false'>
                    </td>
                  </tr>
                  
                  <!-- Linha de Hora Fim -->
                  <tr>
                    <td class="schedule-label">{{ 'TELACADASTROMEDICO.Fim' | translate }}</td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SegundaFim" 
                             name="Segunda Fim período" #SegundaFim2 
                             (blur)="ValidaTimer(SegundaFim2.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="segundaHorario.SegundaFim" [disabled]='Segunda == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="TercaFim" 
                             name="Terça Fim período" #TercaFim2 
                             (blur)="ValidaTimer(TercaFim2.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="tercaHorario.TercaFim" [disabled]='Terca == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuartaFim" 
                             name="Quarta Fim período" #QuartaFim2 
                             (blur)="ValidaTimer(QuartaFim2.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="quartaHorario.QuartaFim" [disabled]='Quarta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuintaFim" 
                             name="Quinta Fim período" #QuintaFim2 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(QuintaFim2.value,$event.target)" 
                             [(ngModel)]="quintaHorario.QuintaFim" [disabled]='Quinta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SextaFim" 
                             name="Sexta Fim período" #SextaFim2 
                             (blur)="ValidaTimer(SextaFim2.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="sextaHorario.SextaFim" [disabled]='Sexta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SabadoFim" 
                             name="Sabado Fim período" #SabadoFim2 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(SabadoFim2.value,$event.target)" 
                             [(ngModel)]="sabadoHorario.SabadoFim" [disabled]='Sabado == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="DomingoFim" 
                             name="Domingo Fim período" #DomingoFim2 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimer(DomingoFim2.value,$event.target)" 
                             [(ngModel)]="domingoHorario.DomingoFim" [disabled]='Domingo == false'>
                    </td>
                  </tr>
                  
                  <!-- Linha de Tempo por Consulta -->
                  <tr>
                    <td class="schedule-label">{{ 'TELACADASTROMEDICO.TEMPOPORCONSULTA' | translate }}</td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SegundaTempo" 
                             name="Segunda tempo por consulta" #SegundaTempo3 
                             (blur)="ValidaTimerTempo(SegundaTempo3.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="segundaHorario.SegundaTempo" [disabled]='Segunda == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="TercaTempo" 
                             name="Terça tempo por consulta" #TercaTempo3 
                             (blur)="ValidaTimerTempo(TercaTempo3.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="tercaHorario.TercaTempo" [disabled]='Terca == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuartaTempo" 
                             name="Quarta tempo por consulta" #QuartaTempo3 
                             (blur)="ValidaTimerTempo(QuartaTempo3.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="quartaHorario.QuartaTempo" [disabled]='Quarta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="QuintaTempo" 
                             name="Quinta tempo por consulta" #QuintaTempo3 
                             (blur)="ValidaTimerTempo(QuintaTempo3.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="quintaHorario.QuintaTempo" [disabled]='Quinta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SextaTempo" 
                             name="Sexta tempo por consulta" #SextaTempo3 
                             (blur)="ValidaTimerTempo(SextaTempo3.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="sextaHorario.SextaTempo" [disabled]='Sexta == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="SabadoTempo" 
                             name="Sabado tempo por consulta" #SabadoTempo3 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             (blur)="ValidaTimerTempo(SabadoTempo3.value,$event.target)" 
                             [(ngModel)]="sabadoHorario.SabadoTempo" [disabled]='Sabado == false'>
                    </td>
                    <td class="schedule-cell">
                      <input type="time" class="time-input" id="DomingoTempo" 
                             name="Domingo tempo por consulta" #DomingoTempo3 
                             (blur)="ValidaTimerTempo(DomingoTempo3.value,$event.target)" 
                             pattern="[0-9]{2}:[0-9]{2}" min="00:00" max="23:59" 
                             [(ngModel)]="domingoHorario.DomingoTempo" [disabled]='Domingo == false'>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
  
      <div class="card-footer">
        <button class="btn btn-outline" (click)="LimparCampos()">
          <span class="material-icons">clear</span>
          {{ 'TELACADASTROMEDICO.LIMPAR' | translate }}
        </button>
        <button class="btn btn-success" (click)="Submit()">
          <span class="material-icons">save</span>
          {{ 'TELACADASTROMEDICO.SALVAR' | translate }}
        </button>
      </div>
    </div>
  </div>
  
  <!-- Modais -->
  <!-- Modal de exclusão -->
  <ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title warning-title">
          <span class="material-icons warning-icon">warning</span> 
          Você tem certeza que deseja excluir este Usuário?
        </h2>
      </div>
      <div class="modal-body">
        <p class="modal-message">O Usuário será excluído permanentemente</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline" (click)="excluirItem.close()">
          NÃO
        </button>
        <button class="btn btn-danger" (click)="InativarMedico()">
          SIM
        </button>
      </div>
    </div>
  </ngx-smart-modal>
  
  <!-- Modal de edição de foto -->
  <ngx-smart-modal #ModalFoto identifier="ModalFoto" customClass="nsm-centered medium-modal">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">
          {{ 'TELACADASTROMEDICO.FOTODEPERFIL' | translate }}
        </h2>
      </div>
      <div class="modal-body">
        <div class="image-editor-controls">
          <button class="btn btn-control" (click)="rotateLeft()">
            <span class="material-icons">rotate_left</span>
            {{ 'TELACADASTROMEDICO.GIRARAESQUERDA' | translate }}
          </button>
          <button class="btn btn-control" (click)="rotateRight()">
            <span class="material-icons">rotate_right</span>
            {{ 'TELACADASTROMEDICO.GIRARADIREITA' | translate }}
          </button>
          <button class="btn btn-control" (click)="flipHorizontal()">
            <span class="material-icons">flip</span>
            {{ 'TELACADASTROMEDICO.VIRARHORIZONTALMENTE' | translate }}
          </button>
          <button class="btn btn-control" (click)="flipVertical()">
            <span class="material-icons">flip</span>
            {{ 'TELACADASTROMEDICO.VIRARVERTICALMENTE' | translate }}
          </button>
        </div>
        
        <div class="image-cropper-container">
          <image-cropper
            [imageChangedEvent]="imageChangedEvent"
            [maintainAspectRatio]="true"
            [aspectRatio]="3/3"
            [onlyScaleDown]="true"
            [roundCropper]="false"
            outputType="base64"
            (imageCropped)="imageCropped($event)"
            (imageLoaded)="imageLoaded()"
            (cropperReady)="cropperReady()"
            (loadImageFailed)="loadImageFailed()"
            [style.display]="showCropper ? null : 'none'"
            [alignImage]="'left'">
          </image-cropper>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-success" (click)="CortarImagem()">
          <span class="material-icons">content_cut</span>
          {{ 'TELACADASTROMEDICO.CORTAR' | translate }}
        </button>
      </div>
    </div>
  </ngx-smart-modal>
  
  <!-- Modal de usuário existente -->
  <ngx-smart-modal #UsuarioExistente identifier="UsuarioExistente" customClass="nsm-centered medium-modal" [closable]="false" [dismissable]="false" [escapable]="false">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">{{mensagemPaciente}}</h2>
      </div>
      <div class="modal-footer">
        <button class="btn btn-success" (click)="AceitarUsuarioExistente()">Aceitar</button>
        <button class="btn btn-outline" (click)="NaoAceitarUsuarioExistente()">Cancelar</button>
      </div>
    </div>
  </ngx-smart-modal>
  
  <!-- Modal de certificado após cadastro -->
  <ngx-smart-modal #CertificadoAposCadastro identifier="CertificadoAposCadastro" customClass="nsm-centered medium-modal" [closable]="false" [dismissable]="false" [escapable]="false">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">Deseja cadastrar certificados digitais?</h2>
      </div>
      <div class="modal-footer">
        <button class="btn btn-success" (click)="CadastrarCertificadoNovoMedico()">Aceitar</button>
        <button class="btn btn-outline" (click)="CancelarCadastroCertificado()">Cancelar</button>
      </div>
    </div>
  </ngx-smart-modal>