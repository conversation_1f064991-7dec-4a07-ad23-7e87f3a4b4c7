import { PacienteModelview } from "./cliente";

export class Whatssap {
  phone?: string;
  message?: string;
}

export class ObjMensagemWhatsappPadrao{
  idMensagemWhatsappPadrao?: number | null;
  tituloMensagem: string = "";
  mensagem: string = "";
  flgAguardaResposta?: boolean;
}

export class ObjMensagemEnvio{
  idsConsulta?: number[];
  mensagem?: string;
  idTipoMensagem?: number;
  flgAguardaResposta?: boolean;
}

export class ObjConsultaChat{
  idConsulta?: number;
  mensagem?: string;
  dtConsulta?: Date;
}

export class ModelConsultaWhats {
  idConsulta?: number;
  dtConsulta?: string;
  idMedico?: number;
  idPaciente?: number;
  nomeMedico?: string;
  nomePaciente?: string;
  lsMensagens?: ObjMensagemConsulta[];


  flgSelecionado?: boolean = false;
  flgVisualizarMsgs?: boolean = false;
}

export class ObjMensagemConsulta {
  idMensagem?: number;
  mensagem?: string;
  telefone?: string;
  flgAguardaResposta?: boolean;
  flgRespondido?: boolean | null;
  idConsulta?: number;
  dtEnviada?: string;
  resposta?: string;
  dtaResposta?: string | null;
  idTipoMensagem?: number | null;
  descTipoMensagem?: string;
}

export class ObjMensagemListaPaciente {
  mensagem?: string;
  listaPaciente?: PacienteModelview[];
}