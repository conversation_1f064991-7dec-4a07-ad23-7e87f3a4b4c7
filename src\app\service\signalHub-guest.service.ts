import {
  HubConnectionBuilder,
  LogLevel,
  HubConnection,
} from '@microsoft/signalr';

import { Injectable, EventEmitter } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class SignalHubGuestService {
  hubConnection: HubConnection;
  private isConnected: boolean = false;
  private tokenConexao: string | null = null;

  // Event Emitters para usuários deslogados
  public OnAtualizaChamaPacienteFila: EventEmitter<string> = new EventEmitter<string>();
  public OnChamaPacienteFila: EventEmitter<string> = new EventEmitter<string>();
  public OnDisparaAlertFilaClinica: EventEmitter<string> = new EventEmitter<string>();
  public OnFinalizaConsulta: EventEmitter<string> = new EventEmitter<string>();
  public OnConviteReuniao: EventEmitter<any> = new EventEmitter<any>();
  public OnConvidarPacienteComToken: EventEmitter<any> = new EventEmitter<any>();

  // Event Emitters para comunicação médico-paciente
  public OnSolicitacaoDadosPaciente: EventEmitter<any> = new EventEmitter<any>();
  public OnDadosPacienteRecebidos: EventEmitter<any> = new EventEmitter<any>();

  public changeConsulta$: EventEmitter<any>;

  constructor() {
    const buildHubConn = new HubConnectionBuilder();
    buildHubConn.configureLogging(LogLevel.Information);
    buildHubConn.withUrl(environment.apiEndpoint + '/notify');
    // buildHubConn.withAutomaticReconnect();
    this.hubConnection = buildHubConn.build();
    // this.hubConnection.serverTimeoutInMilliseconds = 10000;

    this.changeConsulta$ = new EventEmitter();
    this.initializeSignalIR();
  }

  // Método para definir o token de conexão do usuário guest
  public setTokenConexao(token: string) {
    this.tokenConexao = token;
  }

  // Método para enviar dados do usuário guest para o SignalR
  private sendGuestUserSignalR(token: string) {
    if (token && this.isConnected) {
      const guestUser = {
        tokenConexao: token,
        tipoUsuario: 'Guest',
        timestamp: new Date().toISOString()
      };
      this.hubConnection.send('GuestUserConnected', guestUser);
    }
  }

  // Método público para conectar usuário guest
  public connectGuestUser(token: string) {
    this.setTokenConexao(token);
    if (this.isConnected) {
      this.sendGuestUserSignalR(token);
    } else {
      // Se não estiver conectado, aguarda a conexão
      this.changeConsulta$.subscribe(() => {
        if (this.isConnected && this.tokenConexao) {
          this.sendGuestUserSignalR(this.tokenConexao);
        }
      });
    }
  }

  // Método para desconectar usuário guest
  public disconnectGuestUser() {
    if (this.tokenConexao && this.isConnected) {
      this.hubConnection.send('GuestUserDisconnected', this.tokenConexao);
      this.tokenConexao = null;
    }
  }

  private qtdTentativaConectarApi = 0; // Contador - a cada 3 tentativas o tempo aumenta
  private tempoBaseConectarApi = 1000; // tempo inicial para conectar a api

  private async startSignalR() {
    return this.hubConnection
      .start()
      .then(async () => {
        this.isConnected = true;
        this.tempoBaseConectarApi = 1000;
        this.qtdTentativaConectarApi = 0; // A tentativa de conexão foi um sucesso, reseto as tentativas.
        
        // Se já temos um token, conecta o usuário guest
        if (this.tokenConexao) {
          this.sendGuestUserSignalR(this.tokenConexao);
        }
        
        this.changeConsulta$.emit(true);
        console.log('SignalR Guest conectado com sucesso');
      })
      .catch((err) => {
        this.isConnected = false;
        console.error("SignalR Guest Start Error!", err);

        // Depois de 3 tentativas o tempo base vira 5 minutos
        if (this.qtdTentativaConectarApi == 3 && this.tempoBaseConectarApi < 300000) {
          this.qtdTentativaConectarApi = 1;
          this.tempoBaseConectarApi = 300000;
        }

        // Aumenta o tempo de espera a cada 3 falhas
        this.qtdTentativaConectarApi++;
        const delay = this.tempoBaseConectarApi * Math.pow(2, Math.floor(this.qtdTentativaConectarApi / 3));
        
        setTimeout(() => this.startSignalR(), delay);
      });
  }

  private async initializeSignalIR() {
    const self = this;

    this.startSignalR();

    this.hubConnection.onclose(() => {
      this.isConnected = false;
      console.log('SignalR Guest desconectado');
      setTimeout(() => self.startSignalR(), 1000);
    });

    // Configurar listeners para eventos relevantes para usuários deslogados
    this.setupEventListeners();
  }

  private setupEventListeners() {
    // Evento principal para atualização da fila
    this.hubConnection.on('AtualizaChamaPacienteFila', (res: string) => {
      this.OnAtualizaChamaPacienteFila.emit(res);
    });

    // Evento para chamar paciente específico
    this.hubConnection.on('ChamaPacienteFila', (res: string) => {
      this.OnChamaPacienteFila.emit(res);
    });

    // Evento para alertas da fila da clínica
    this.hubConnection.on('DisparaAlertFilaClinica', (res: string) => {
      this.OnDisparaAlertFilaClinica.emit(res);
    });

    // Evento para finalização de consulta
    this.hubConnection.on('FinalizaConsulta', (res: string) => {
      this.OnFinalizaConsulta.emit(res);
    });

    // Evento para convite para reunião (específico para guests)
    this.hubConnection.on('ConviteReuniao', (res: any) => {
      this.OnConviteReuniao.emit(res);
    });

    // Evento específico para guests - convite com token
    this.hubConnection.on('ConviteReuniaoToken', (token: string, nomePaciente?: string) => {
      if (token === this.tokenConexao) {
        this.OnConviteReuniao.emit({ token, nomePaciente });
      }
    });

    // Evento para convidar paciente com token (método específico)
    this.hubConnection.on('convidarPacienteComToken', (token: string, nomePaciente?: string) => {
      console.log('Evento convidarPacienteComToken recebido:', { token, nomePaciente });
      if (token === this.tokenConexao) {
        console.log('Token corresponde ao paciente atual - emitindo evento');
        this.OnConvidarPacienteComToken.emit({ token, nomePaciente });
      }
    });

    // Evento para solicitação de dados do paciente pelo médico
    this.hubConnection.on('SolicitarDadosPaciente', (medicoId: string, tokenPaciente: string) => {
      console.log('Solicitação de dados recebida do médico:', { medicoId, tokenPaciente });
      if (tokenPaciente === this.tokenConexao) {
        console.log('Solicitação é para este paciente - emitindo evento');
        this.OnSolicitacaoDadosPaciente.emit({ medicoId, tokenPaciente });
      }
    });

    // Evento para recebimento de dados do paciente pelo médico
    this.hubConnection.on('DadosPacienteRecebidos', (dadosPaciente: any) => {
      console.log('Dados do paciente recebidos:', dadosPaciente);
      this.OnDadosPacienteRecebidos.emit(dadosPaciente);
    });
  }

  // Método para enviar dados para o servidor
  public enviaServer(metodo: string, ...args: any[]) {
    if (this.isConnected) {
      this.hubConnection.send(metodo, args);
    } else {
      console.warn('SignalR Guest não está conectado. Não foi possível enviar:', metodo);
    }
  }

  // Método para verificar se está conectado
  public isHubConnected(): boolean {
    return this.isConnected;
  }

  // Método para obter o token atual
  public getTokenConexao(): string | null {
    return this.tokenConexao;
  }

  // Método público para verificar se está conectado
  public getIsConnected(): boolean {
    return this.isConnected;
  }

  // Método para debug do estado da conexão
  public getEstadoConexao() {
    return {
      isConnected: this.isConnected,
      tokenConexao: this.tokenConexao,
      connectionId: this.hubConnection?.connectionId || null,
      state: this.hubConnection?.state || 'Unknown'
    };
  }

  // Método para solicitar dados do paciente (usado pelo médico)
  public solicitarDadosPaciente(tokenPaciente: string, medicoId: string) {
    if (this.isConnected) {
      console.log('Solicitando dados do paciente:', { tokenPaciente, medicoId });
      this.hubConnection.send('SolicitarDadosPaciente', medicoId, tokenPaciente);
    } else {
      console.warn('SignalR Guest não está conectado. Não foi possível solicitar dados do paciente.');
    }
  }

  // Método para enviar dados do paciente para o médico (usado pelo paciente)
  public enviarDadosParaMedico(medicoId: string, dadosPaciente: any) {
    if (this.isConnected) {
      console.log('Enviando dados para o médico:', { medicoId, dadosPaciente });
      this.hubConnection.send('EnviarDadosPaciente', medicoId, dadosPaciente);
    } else {
      console.warn('SignalR Guest não está conectado. Não foi possível enviar dados para o médico.');
    }
  }

  // Método para limpar conexão
  public dispose() {
    this.disconnectGuestUser();
    if (this.hubConnection) {
      this.hubConnection.stop();
      this.isConnected = false;
    }
  }
}
