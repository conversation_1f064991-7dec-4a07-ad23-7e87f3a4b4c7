<ngx-smart-modal #PesquisaCid identifier="PesquisaCid" customClass="modern-modal health-modal cid-modal emailmodal" [dismissable]="false">
  <div class="modal-container">
      <header class="modal-header">
          <div class="modal-title-container">
              <mat-icon>assignment</mat-icon>
              <h3 class="modal-title">Listagem - CID</h3>
          </div>
      </header>

      <main class="modal-container">
          <div class="search-section">
              <div class="search-row">
                  <div class="search-field code-field">
                      <mat-form-field appearance="outline">
                          <mat-label>Buscar por Código</mat-label>
                          <input matInput id="campoCodCID" [(ngModel)]="pesquisaPorCod" 
                              (change)="ValidaPesquisa()" (keyup.enter)="FiltrarPorCod()" 
                              style="text-transform: uppercase;">
                          <button mat-icon-button matSuffix (click)="FiltrarPorCod()">
                              <mat-icon>search</mat-icon>
                          </button>
                      </mat-form-field>
                  </div>
                  
                  <div class="search-field description-field">
                      <mat-form-field appearance="outline">
                          <mat-label>Buscar pela Descrição</mat-label>
                          <input matInput id="campoDesCID" [(ngModel)]="pesquisaPorDes" 
                              (keyup.enter)="FiltrarPorDes()">
                          <button mat-icon-button matSuffix (click)="FiltrarPorDes()">
                              <mat-icon>search</mat-icon>
                          </button>
                      </mat-form-field>
                  </div>
              </div>
          </div>
          
          <div class="table-container">
              <table class="cid-table">
                  <thead>
                      <tr>
                          <th class="code-column">Código CID</th>
                          <th class="description-column">Descrição CID</th>
                      </tr>
                  </thead>
                  <tbody>
                      <tr *ngFor="let item of listaCid" (click)="SelecionarCIV(item.codCid)">
                          <td class="code-column"><strong>{{item.codCid}}</strong></td>
                          <td class="description-column">{{item.desCid}}</td>
                      </tr>
                      <tr *ngIf="listaCid.length === 0">
                          <td colspan="2" class="empty-message">Nenhum registro encontrado</td>
                      </tr>
                  </tbody>
              </table>
              
              <div class="pagination-container" *ngIf="(listaCid != undefined && listaCid.length > 0) && bOcultaCarregaMais == false">
                  <button class="load-more-button" (click)="CarregarMais()">
                      {{ 'TELAPESQUISAUSUARIO.CARREGARMAIS' | translate }}
                  </button>
              </div>
          </div>
      </main>
  </div>
</ngx-smart-modal>

<!-- Modal de Cadastro/Edição de CID -->
<ngx-smart-modal #CadastraCid identifier="CadastraCid" customClass="modern-modal health-modal">
  <div class="modal-container">
      <header class="modal-header">
          <div class="modal-title-container">
              <h3 class="modal-title" *ngIf="idCidEdit==null">Cadastro de CID</h3>
              <h3 class="modal-title" *ngIf="idCidEdit! > 0">Edição de CID</h3>
          </div>
          <button class="close-button" (click)="ngxSmartModalService.getModal('CadastraCid').close()">
              <mat-icon>close</mat-icon>
          </button>
      </header>

      <main class="modal-container">
          <div class="form-container">
              <div class="form-field">
                  <mat-form-field appearance="outline">
                      <mat-label>Código CID</mat-label>
                      <input matInput required [(ngModel)]="codCidEdit">
                  </mat-form-field>
              </div>
              
              <div class="form-field">
                  <mat-form-field appearance="outline">
                      <mat-label>Descrição CID</mat-label>
                      <input matInput required [(ngModel)]="desCidEdit">
                  </mat-form-field>
              </div>
          </div>
      </main>
      
      <footer class="modal-footer">
          <button class="action-button save-button" (click)="SalvarCid()">
              <mat-icon>save</mat-icon>
              <span>Salvar</span>
          </button>
      </footer>
  </div>
</ngx-smart-modal>

<!-- Modal de Exclusão de CID -->
<ngx-smart-modal #ExcluirCid identifier="ExcluirCid" customClass="modern-modal health-modal confirm-modal">
  <div class="modal-container">
      <header class="modal-header">
          <div class="modal-title-container">
              <h3 class="modal-title">Confirmação</h3>
          </div>
          <button class="close-button" (click)="ngxSmartModalService.getModal('ExcluirCid').close()">
              <mat-icon>close</mat-icon>
          </button>
      </header>

      <main class="modal-content confirm-content">
          <div class="confirm-message">
              <mat-icon class="warning-icon">warning</mat-icon>
              <p>Deseja excluir esse CID?</p>
          </div>
      </main>
      
      <footer class="modal-footer">
          <button class="action-button cancel-button" (click)="ngxSmartModalService.getModal('ExcluirCid').close()">
              <mat-icon>cancel</mat-icon>
              <span>Cancelar</span>
          </button>
          
          <button class="action-button delete-button" (click)="ExclusaoCid()">
              <mat-icon>delete</mat-icon>
              <span>Excluir</span>
          </button>
      </footer>
  </div>
</ngx-smart-modal>