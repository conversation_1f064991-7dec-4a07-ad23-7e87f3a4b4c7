import { MatDialog as MatDialog, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { Injectable, TemplateRef } from '@angular/core';
import { ModalGerarPdfLifeLineComponent } from '../Modais/modal-gerar-pdf-life-line/modal-gerar-pdf-life-line.component';
import { ModalHistoricoRespostaMensagemWhatsComponent } from '../Modais/modal-historico-resposta-mensagem-whats/modal-historico-resposta-mensagem-whats.component';
import { ModalConfirmacaoComponent } from '../Modais/modal-confirmacao/modal-confirmacao.component';
import { ModalConfiguraFaturamentoComponent } from '../Modais/modal-configura-faturamento/modal-configura-faturamento.component';
import { ComponentType } from '@angular/cdk/portal';
import { ObjDownloadFileModal } from '../model/arquivo';
import { ModalRelatorioAgendamentosComponent } from '../Modais/modal-relatorio-agendamentos/modal-relatorio-agendamentos.component';
import { ModalDownloadDocumentosComponent } from '../Modais/modal-download-documentos/modal-download-documentos.component';
import { ModalUploadFotosComponent } from '../Modais/modal-upload-fotos/modal-upload-fotos.component';

@Injectable({
  providedIn: 'root'
})
export class ControleModaisService {

  constructor(
    private matDialog: MatDialog
  ) { }

  widthMap: Record<'small' | 'medium' | 'large', string> = {
    small: '30vmax',
    medium: '40vmax',
    large: '75vmax',
  };

  //#region ModalConfiguraFaturamento
  idConsulta: number = 0;
  lsConsultas: any[] = [];
  flgCarreagemntoGeral: boolean = false;
  ModalConfiguraFaturamentoGeral(consultas:any): Promise<boolean> {
    this.lsConsultas = consultas;
    this.flgCarreagemntoGeral = true;
    const dialogRef = this.matDialog.open(ModalConfiguraFaturamentoComponent, {
      width: '50vmax',
    });

    return dialogRef.afterClosed().toPromise();
  }

  ModalConfiguraFaturamentoPorId(idConsulta:any): Promise<boolean> {
    this.flgCarreagemntoGeral = false;
    this.idConsulta = idConsulta;
    const dialogRef = this.matDialog.open(ModalConfiguraFaturamentoComponent, {
      width: '50vmax',
    });

    return dialogRef.afterClosed().toPromise();
  }
  //#endregion

  //#region  ModalLifeLine
  //#region Modal - Resposta Historico Mensagem
  listaIdsConsulta: number[] = [];
  AbreModalRespostaWhats(lsConsultas: any, lsIds: number[]) {
    let dialogRef
    this.listaIdsConsulta = lsIds;
    dialogRef = this.matDialog.open(ModalHistoricoRespostaMensagemWhatsComponent, {
      data: lsConsultas
    });

    dialogRef.afterClosed().subscribe((ret) => {
      this.listaIdsConsulta = [];
      return ret;
    })
  }
  //#endregion

  //#region Modal - Life Line
  IdPaciente_LifeLine?: number | null;
  Nome_LifeLine?: string | null;

  AbreModalLifeLine(idPaciente: number, nome: string) {
    this.IdPaciente_LifeLine = idPaciente;
    this.Nome_LifeLine = nome;
    let dialogRef;
    dialogRef = this.matDialog.open(ModalGerarPdfLifeLineComponent,
      {
        disableClose: true,
      }
    )

    dialogRef.afterClosed().subscribe((ret) => {
      this.IdPaciente_LifeLine = null;
      this.Nome_LifeLine = null;
      return ret;
    })
  }
  //#endregion

  //#region ModalConfirmação
  ModalConfirmacao(msg:any): Promise<boolean> {
    const dialogRef = this.matDialog.open(ModalConfirmacaoComponent, {
      width: '50vmax',
      data: msg,
    });

    return dialogRef.afterClosed().toPromise();
  }
  //#endregion

  ModalRelatorioAgendamento() {
    this.matDialog.open(ModalRelatorioAgendamentosComponent, {
      width: '30vmax'
    });

    // return dialogRef.afterClosed().toPromise();
  }

  //#region ModalDownload
  objDownloadFileModal?: ObjDownloadFileModal;
  ModalDownload(objFile:any) {
    this.objDownloadFileModal = objFile;
    this.matDialog.open(ModalDownloadDocumentosComponent, {
      width: '30vmax'
    });
  }
  //#endregion

  //#region AbrirModalComponente
  AbrirModalComponente<T>(
    component: ComponentType<T>,
    data?: { titulo?: string;[key: string]: any },
    size: 'small' | 'medium' | 'large' | string = '75vmax'
  ): MatDialogRef<T> {

    return this.matDialog.open(component, {
      data,
      width: this.widthMap[size as keyof typeof this.widthMap] || size,
      height: '80vh',
      panelClass: 'custom-modal',
    });
  }

  //#endregion

  AbrirModalTemplate<T>(
    template: TemplateRef<T>,
    data?: { titulo?: string;[key: string]: any }
  ): MatDialogRef<T> {
    const dialogRef = this.matDialog.open(template, {
      data,
      width: '40vmax',
      height: '80vh',
      panelClass: 'custom-modal',
    });
    return dialogRef;
  }

  FecharModal(dialogRef: MatDialogRef<any>) {
    dialogRef.close();
  }


  ModalUploadFoto(imagem: string | null): Promise<string | null> {
    const dialogRef = this.matDialog.open(ModalUploadFotosComponent, 
      { 
        data: imagem,
        
      }
    );
    return dialogRef.afterClosed().toPromise();
  }
}
