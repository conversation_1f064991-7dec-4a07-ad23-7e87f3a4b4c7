<div class="modal-container">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">Mensagens Confirmadas</h3>
        </div>
        <button class="close-button" (click)="fecharModal()">
            <mat-icon>close</mat-icon>
        </button>
    </header>

    <main class="modal-container">
        <div class="filters-section">
            <div class="filter-field">
                <label for="message-type">Tipo de mensagem:</label>
                <select id="message-type" class="custom-select" [(ngModel)]="IdFiltroMensagem"
                    (ngModelChange)="filtroTipoMensagem()">
                    <option *ngFor="let option of lsTipoMensagem" [value]="option.id">
                        {{ option.desc }}
                    </option>
                </select>
            </div>

            <div class="filter-field">
                <label for="message-status">Status da mensagem:</label>
                <select id="message-status" class="custom-select" [(ngModel)]="IdFiltroStatus"
                    (ngModelChange)="filtroStatusMensagem()">
                    <option *ngFor="let option of lsFiltroStatusMensagem" [value]="option.id">
                        {{ option.desc }}
                    </option>
                </select>
            </div>
        </div>

        <div class="appointments-list">
            <div class="empty-state" *ngIf="lsConsultas.length === 0">
                Nenhuma consulta encontrada com os filtros selecionados
            </div>
            
            <div class="appointment-card" *ngFor="let consulta of lsConsultas">
                <div class="appointment-header">
                    <button class="toggle-button" [disabled]="consulta.lsMensagens?.length === 0" 
                        (click)="consulta.flgVisualizarMsgs = !consulta.flgVisualizarMsgs">
                        <mat-icon class="toggle-icon">
                            {{consulta.flgVisualizarMsgs ? 'expand_less' : 'expand_more'}}
                        </mat-icon>
                    </button>
                    
                    <div class="appointment-info">
                        <div class="appointment-title">
                            Consulta do Doutor <span class="highlight">{{consulta.nomeMedico}}</span>. 
                            Paciente: <span class="highlight">{{consulta.nomePaciente}}</span>
                        </div>
                        <div class="appointment-date">
                            Data consulta: {{formatDate(consulta.dtConsulta!)}}
                        </div>
                    </div>
                </div>

                <div class="messages-container" *ngIf="consulta.flgVisualizarMsgs">
                    <div class="empty-state" *ngIf="consulta.lsMensagens?.length === 0">
                        Não há mensagens para esta consulta
                    </div>
                    
                    <div class="message-item" *ngFor="let msg of consulta.lsMensagens">
                        <div class="message-content">
                            <div class="message-text">{{removerExcedente(msg.mensagem!)}}</div>
                            <div class="message-type">{{msg.descTipoMensagem}}</div>
                        </div>
                        
                        <div class="message-status" [ngClass]="{
                            'status-waiting': msg.flgAguardaResposta && !msg.flgRespondido,
                            'status-answered': msg.flgAguardaResposta && msg.flgRespondido,
                            'status-no-response': !msg.flgAguardaResposta
                        }">
                            Status: {{msg.flgAguardaResposta ? 
                                (msg.flgRespondido ? 'Respondido' : 'Aguardando resposta') : 
                                'Não aguarda mensagem'}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="modal-footer">
        <button class="action-button cancel-button" (click)="fecharModal()">
            <mat-icon>close</mat-icon>
            <span>Fechar</span>
        </button>
    </footer>
</div>