// Variables - Green color scheme
$primary-color: #2E8B57; // Main green
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

// Configurações gerais
* {
  box-sizing: border-box;
}

.dashboard-card {
  margin: 0 auto;
  border: none !important;
  box-shadow: none !important;
  padding: 20px;
  border-radius: $border-radius;
  overflow-y: auto;
  height: auto;
  background-color: transparent;
}
.dashboard-card-paciente {
  margin: 0 auto;
  border: none !important;
  box-shadow: none !important;
  padding: 20px;
  border-radius: $border-radius;
  overflow-y: auto;
  height: auto;
  background-color: transparent;
  @media (max-width:100px) {
    height: 100vh;
  }
}

.dashboard-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  @media (max-width:1000px) {
    height: 90vh !important;
  }
}

// Cabeçalho de boas-vindas
.welcome-header {
  background: #219679;
  background: linear-gradient(90deg, rgba(33, 150, 121, 1) 0%, rgba(2, 156, 107, 1) 9%, rgba(31, 95, 61, 1) 97%);
  border-radius: $border-radius;
  margin-bottom: 24px;
  overflow: hidden;
  box-shadow:none;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
}

.welcome-text {
  color: $secondary-light;
  
  h2 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #fff;
  }
  
  p {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
  }
}

.welcome-image {
  img {
    max-width: 180px;
    height: auto;
  }
}

// Dashboard do médico
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: $box-shadow;
  transition: transform $transition;
  
  &:hover {
    transform: translateY(-2px);
  }
}

.stat-icon {
  margin-right: 16px;
  
  img {
    width: 40px;
    height: 40px;
  }
}

.stat-info {
  h3 {
    font-size: 14px;
    color: $text-secondary;
    margin: 0 0 8px 0;
    font-weight: 500;
  }
  
  .stat-number {
    font-size: 28px;
    font-weight: 600;
    color: $primary-color;
  }
}

.consultations-summary {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 16px 20px;
  min-width: 200px;
  flex-grow: 1;
  box-shadow: none;
  display: flex;
  gap: 24px;
  
  .summary-label {
    color: $text-secondary;
    font-size: 14px;
    margin: 0 0 2px 0;
  }
  
  .summary-number {
    font-size: 24px;
    font-weight: 600;
    color: #538468;
    
    span{
      font-weight: 500;
      font-size: 16px;
    }
  }
  .div-matIcon{
    border-radius: 50%;
    background-color: #f0faf5;
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #2E8B57;
    mat-icon{
      font-size: 30px;
      width: 30px;
      height: 30px;
    }
  }
}

.chart-container {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 20px;
  box-shadow: $box-shadow;
  height: 300px;
  margin-bottom: 24px;
}

// Dashboard do paciente
.patient-dashboard {
  margin-top: 20px;
  height: 65vh;
}

.dashboard-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
}
.dashboard-layout-paciente {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  @media (max-width:1000px) {
    display: flex;
    flex-direction: column;
  }
}

.body-illustration {
  background-color: rgba($primary-color, 0.05);
  border-radius: $border-radius;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  
  img {
    max-width: 100%;
    height: auto;
  }
}

.dashboard-main {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.quick-links {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.quick-link-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: $box-shadow;
  cursor: pointer;
  transition: transform $transition, box-shadow $transition;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.link-content {
  display: flex;
  align-items: center;
}

.link-icon {
  margin-right: 12px;
  
  img {
    width: 32px;
    height: 32px;
  }
}

.link-text {
  h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: $primary-color;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    color: $text-secondary;
  }
}

.link-action {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: $primary-color;
  color: $secondary-light;
}

.pending-tasks {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.pending-card {
  background-color: $primary-color;
  border-radius: $border-radius;
  padding: 16px;
  box-shadow: $box-shadow;
}

.pending-info {
  display: flex;
  align-items: center;
}

.pending-count {
  font-size: 36px;
  font-weight: 700;
  color: $accent-color;
  margin-right: 16px;
}

.pending-info p {
  color: $secondary-light;
  font-size: 16px;
  margin: 0;
  line-height: 1.3;
}

.health-progress {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 20px;
  box-shadow: $box-shadow;
  
  h3 {
    color: $primary-color;
    font-size: 18px;
    margin: 0 0 16px 0;
    font-weight: 500;
  }
  
  ::ng-deep .mat-progress-bar {
    height: 12px;
    border-radius: 6px;
  }
  
  ::ng-deep .mat-progress-bar-fill::after {
    background-color: $primary-color;
  }
  
  ::ng-deep .mat-progress-bar-buffer {
    background-color: rgba($primary-color, 0.2);
  }
}

// Dashboard do atendente
.menu-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.menu-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  text-align: center;
  box-shadow: $box-shadow;
  cursor: pointer;
  transition: transform $transition;
  height: 180px;
  
  &:hover {
    transform: scale(1.05);
  }
  
  .card-header {
    margin-bottom: 16px;
    
    h3 {
      color: $primary-color;
      font-size: 17px;
      font-weight: 600;
      margin: 0;
    }
  }
  
  .card-icon {
    margin: 16px 0;
    
    mat-icon {
      font-size: 30px;    
      height: 45px;    
      width: 45px;    
      border-radius: 50%;    
      color: #FFFFFF;
      background-color:#2E8B57;
      display:flex;
      justify-content:center;
      align-items:center;
      padding:28px;
      margin:auto;
    }
  }
  
  .card-description {
    color: $text-secondary;
    font-size: 14px;
    margin: 0;
  }
}

// Modal de orientação
.orientation-modal {
  ::ng-deep .nsm-content {
    border-radius: $border-radius;
    overflow: hidden;
    width: 500px;
    margin: 10% auto !important;
  }
}

.modal-content {
  padding: 0;
}

.modal-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background-color: #fff !important;
  color: $primary-color;
  
  .header-icon {
    margin-right: 12px;
    
    mat-icon {
      border: 2px solid $secondary-light;
      border-radius: 50%;
      padding: 6px;
      height: 36px;
      width: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  
  .header-title {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
  }
}

.modal-body {
  padding: 20px;
}

.orientation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.orientation-item {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 16px;
  border: 1px solid $border-color;
}

.item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  
  h4 {
    color: $primary-color;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }
  
  p {
    color: $text-secondary;
    font-size: 14px;
    margin: 0 0 4px 0;
  }
}

.orientation-content {
  h4 {
    color: $primary-color;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 8px 0;
  }
  
  .content-text {
    color: $text-secondary;
    font-size: 14px;
    line-height: 1.5;
  }
}

// Responsividade
@media (max-width: 992px) {
  .dashboard-layout {
    grid-template-columns: 1fr;
  }
  
  .body-illustration {
    max-width: 400px;
    margin: 0 auto;
  }
  
  .quick-links, .pending-tasks {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    padding: 16px;
  }
  
  .welcome-image {
    display: none;
  }
  
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .consultations-summary {
    flex-direction: column;
  }
  
  .menu-cards {
    grid-template-columns: 1fr;
  }
  
  .item-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .professional-info, .date-info {
    width: 100%;
  }
}
// Variáveis de cores
$primary-green: #27ae60;
$light-green: #e8f5e9;
$white: #ffffff;
$off-white: #f9fafb;
$text-dark: #2c3e50;
$text-light: #7f8c8d;
$border-color: #e0e0e0;
$shadow-color: rgba(0, 0, 0, 0.06);

// Container principal
.health-dashboard-container {
  display: flex;
  width: 50%;
}

// Card do gráfico
.health-chart-card {
  background-color: $white;
  border-radius: 8px;
  box-shadow: none;
  width: 100%;
  max-width: 700px;
  overflow: hidden;
  height: 100%; 
}

// Cabeçalho do gráfico
.chart-header {
  padding: 24px 28px 12px;
  border-bottom: 1px solid $light-green;
  display: flex;
  justify-content: space-between;
  
  h2 {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
    letter-spacing: -0.3px;
    color: $primary-dark;
  }
  
  .chart-description {
    color: $text-light;
    font-size: 12px;
    margin: 0;
    line-height: 1.4;
  }
  select{
    border: 1px solid $border-color;
    border-radius: 5px;
    padding: 5px 10px;
  }
}

// Conteúdo do gráfico
.chart-content {
  background-color: #fff;
  padding: 24px 0 8px 0;
  height: 100%;
}
.chart-wrapper {
  position: relative;
  height: 400px; 
  display: flex;
  flex-direction: column;
  justify-content: flex-start; 
  width: 90%;
  padding: 20px 0; 
}
 @media (max-width: 1600px){
   .chart-wrapper{
     height: 280px;
   }
 }

// Media queries para responsividade
@media (max-width: 768px) {
  .health-chart-card {
    border-radius: 12px;
  }
  
  .chart-header {
    padding: 18px 20px 10px;
    
    h2 {
      font-size: 18px;
    }
  }
  
  .chart-content {
    padding: 16px;
    
    .chart-wrapper {
      height: 300px;
    }
  }
  
  .chart-footer {
    padding: 10px 20px;
  }
}
// Upcoming Appointments Card
.upcoming-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: none;
  overflow: hidden;
  transition: all $transition ease;
  margin-bottom: 0px;
  height: 100%;
}

.upcoming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-bottom: 1px solid rgba($primary-color, 0.1);
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: $accent-color;
  }
  
  .view-all-button {
    color: $accent-color;
    background-color: transparent;
    padding: 5px;
    border-radius: 5px;
    &:hover {
      background-color: rgba($accent-color, 0.1);
    }
  }
}

.upcoming-list {
  padding: 10px 15px;
  max-height: 390px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 5px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 3px;
  }
  @media(max-height: 860px){
    max-height: 20px;
  }
  @media(max-height: 800px ){
    max-height: 260px;
  }
}

.upcoming-day {
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .day-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 5px;
    
    .day-name {
      font-size: 14px;
      font-weight: 600;
      color: $primary-dark;
    }
    
    .day-date {
      font-size: 12px;
      color: $text-secondary;
    }
  }
}

.upcoming-item {
  display: flex;
  align-items: center;
  padding: 10px 5px;
  position: relative;
  
  .time-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 10px;
    
    .time-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: $secondary-light;
      border: 2px solid $primary-color;
      margin-bottom: 5px;
      
      &.active {
        background-color: $primary-color;
        box-shadow: 0 0 0 3px rgba($primary-color, 0.2);
      }
    }
    
    .time-line {
      width: 2px;
      height: 40px;
      background-color: $primary-color;
      opacity: 0.3;
    }
  }
  
  &:last-child {
    .time-indicator .time-line {
      display: none;
    }
  }
  
  .appointment-time {
    width: 50px;
    font-size: 14px;
    font-weight: 600;
    color: $primary-color;
    margin-right: 10px;
  }
  
  .appointment-details {
    flex: 1;
    
    h4 {
      margin: 0 0 3px;
      font-size: 14px;
      font-weight: 500;
      color: $text-primary;
    }
    
    p {
      margin: 0;
      font-size: 12px;
      color: $text-secondary;
    }
  }
  
  .appointment-status {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-left: 10px;
    
    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
    
    &.in-person {
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
    
    &.virtual {
      background-color: rgba(#4F46E5, 0.1);
      color: #4F46E5;
    }
  }
  
  &:hover {
    background-color: $secondary-light;
    border-radius: 8px;
    cursor: pointer;
    
    .appointment-time {
      font-weight: 700;
    }
  }
}

.upcoming-footer {
  padding: 10px 15px;
  border-top: 1px solid $border-color;
  
  .schedule-button {
    width: 100%;
    background-color: #fff;
    color: $primary-color;
    border: 1px solid $primary-color;
    font-weight: 500;
    transition: all $transition ease;
    padding: 5px;
    border-radius: 5px;
    
    &:hover {
      background-color: $primary-color;
      color: white;
    }
  }
}
.exames-dados{
  display: flex;
  width: 100%;
  gap: 24px;
}
@media (max-width:1100px) {
  .exames-dados{
    flex-direction: column;
  }
  .proximas-consultas{
    width: 100% !important;
  }
  .health-dashboard-container{
    width: 100%;
  }
  .dashboard-container{
    height: 80vh;
    overflow: auto;
  }
}
.proximas-consultas{
  width: 50%;
}