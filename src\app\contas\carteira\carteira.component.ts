import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { EnumMeses } from '../../Util/EnumMeses';
import { PagamentoService } from 'src/app/service/pagamento.service';
import { Pagamento } from 'src/app/model/efetuar-pagamento';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';

@Component({
    templateUrl: 'carteira.component.html',
    styleUrls: ['carteira.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CarteiraComponent implements OnInit {

    graficoValoresVazio: boolean = true;
    nomeMesAnterior:any;
    public rawFormatter: any;
    listaPagamentos?: Pagamento[];

    dadosPagamentoGrafico: any = [['Mês', 'R$']];
    // public rawChartData: google.visualization.ChartSpecs = {
    //     chartType: 'AreaChart',
    //     dataTable: this.dadosPagamentoGrafico
    // };

    constructor(
        private spinner: SpinnerService,        
        private pagamentoService: PagamentoService
    ) { }

    ngOnInit(): void {
        this.GetValoresGrafico();
        this.carregarTransacoes();
    }

    carregarTransacoes() {
        this.listaPagamentos = [];
        this.pagamentoService
            .carregarPagamentos()
            .subscribe(ret => {
                ;
                this.listaPagamentos = ret;
                ;
                this.spinner.hide();
            })
    }

    GetValoresGrafico() {
        this.pagamentoService.carregarPagamentosGrafico().subscribe((retorno: any) => {
            

            if (retorno.length > 0) {
                this.graficoValoresVazio = false

                switch (retorno[0].Mês) {
                    case EnumMeses.Janeiro:
                        this.nomeMesAnterior = "Janeiro"
                        break;
                    case EnumMeses.Fevereiro:
                        this.nomeMesAnterior = "Fevereiro"
                        break;
                    case EnumMeses.Março:
                        this.nomeMesAnterior = "Março"
                        break;
                    case EnumMeses.Abril:
                        this.nomeMesAnterior = "Abril"
                        break;
                    case EnumMeses.Maio:
                        this.nomeMesAnterior = "Maio"
                        break;
                    case EnumMeses.Junho:
                        this.nomeMesAnterior = "Junho"
                        break;
                    case EnumMeses.Julho:
                        this.nomeMesAnterior = "Julho"
                        break;
                    case EnumMeses.Agosto:
                        this.nomeMesAnterior = "Agosto"
                        break;
                    case EnumMeses.Setembro:
                        this.nomeMesAnterior = "Setembro"
                        break;
                    case EnumMeses.Outubro:
                        this.nomeMesAnterior = "Outubro"
                        break;
                    case EnumMeses.Novembro:
                        this.nomeMesAnterior = "Novembro"
                        break;
                    case EnumMeses.Dezembro:
                        this.nomeMesAnterior = "Dezembro"
                        break;
                    default:
                        this.nomeMesAnterior = "Invalido"
                        break;
                }
                var totalDias;

                if (retorno[0].Mês == 1 || retorno[0].Mês == 3 || retorno[0].Mês == 5 || retorno[0].Mês == 7 || retorno[0].Mês == 8 || retorno[0].Mês == 10 || retorno[0].Mês == 12) {
                    totalDias = 31
                }
                else if (retorno[0].Mês == 2) {
                    var data = new Date;
                    if ((data.getFullYear() % 4) == 0)
                        totalDias = 29
                    else
                        totalDias = 28
                } else if (retorno[0].Mês == 4 || retorno[0].Mês == 6 || retorno[0].Mês == 9 || retorno[0].Mês == 11) {
                    totalDias = 30
                } else {
                    
                }

                for (let index = 0; index < totalDias!; index++) {
                    this.dadosPagamentoGrafico.push([(1 + index).toString(), 0])
                }

                retorno.forEach((element:any) => {
                    this.dadosPagamentoGrafico.forEach((elemento:any) => {
                        if (element.Dia == elemento[0]) {
                            elemento[1] = element.ValorConsulta
                        }
                    });
                });
            }
            else
                this.graficoValoresVazio = true

            
            this.spinner.hide();

        });
    }
}