import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { ClinicaService } from '../service/clinica.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { ImageCroppedEvent, ImageCropperComponent, ImageCropperModule, ImageTransform } from 'ngx-image-cropper';
import { Clinica } from '../model/clinica';
import { Contato } from '../model/contato';
import { ValidadoreseMascaras } from '../Util/validadores';
import { TranslateModule } from '@ngx-translate/core';
import { CidadeService } from '../service/cidade.service';
import { Endereco } from '../model/endereco';
import { UfClass } from '../Util/UFClass';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-perfil-clinica',
    templateUrl: './perfil-clinica.component.html',
    styleUrls: ['./perfil-clinica.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      ImageCropperModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      MatIcon,
      MatFormFieldModule,
      NgSelectModule,
      NgxSmartModalModule,
      MatDividerModule
    ]
})
export class PerfilClinicaComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private clinicaService: ClinicaService,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private snackBarAlert: AlertComponent,
    private cidadeService: CidadeService,

  ) { }

  @ViewChild(ImageCropperComponent) imageCropper?: ImageCropperComponent;

  showCropper = false;
  imagemCorte: any;

  imageChangedEvent: any = '';
  imagemClinica: any = "assets/build/img/logo-clinica.png";

  dados: any = [];
  clinica: any;
  contato: any;
  endereco: any;
  dadosCidade: any;
  dadosCidadeUf: any;
  flgProntuario: boolean = true;

  showMessageError = false;
  FlgProntuario: boolean = false;
  Dtalice?: boolean;
  TelVal?: boolean;
  TelMovVal?: boolean;
  TelComVal?: boolean;
  TelComValVasil = false;
  TelComLimpa?: boolean;

  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // actionButtonLabel: string = 'Fechar';

  campoEmailInvalido = false;
  mensagemErroEmail = "";
  dadosUF = UfClass;
  dadosUFCarregaBanco: any;

  campoCNPLInvalido: boolean = false;
  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;
  ngOnInit() {
    this.CarregaClinica(this.usuarioLogadoService.getIdUltimaClinica());
  }

  public CarregaClinica(id:any) {

    this.clinicaService.getClinicaEdit(id).subscribe((retorno) => {
      
      
      if (retorno) {
        this.clinica = retorno.clinica
        this.contato = retorno.contato
        this.endereco = retorno.endereco

        this.dados.nomeClinica = this.clinica.desClinica;
        this.FlgProntuario = this.clinica.flgSomenteProntuario;

        this.dados.cnpj = this.mascaravindoBanco(this.clinica.cnpj);
        this.dados.dtaLicenca = this.clinica.dtaLicenca ? new Date(this.clinica.dtaLicenca).toLocaleDateString() : '';

        this.flgProntuario = this.clinica.flgSomenteProntuario
        this.dados.caracterizacao = this.clinica.caracterizacao
        this.dados.logo = this.clinica.logo

        if (retorno.imagem64 != null && retorno.imagem64 != "")
          this.imagemClinica = retorno.imagem64;

        this.dados.emailClinica = this.contato.email
        this.dados.website = this.contato.site

        if (this.contato.telefone != null && this.contato.telefone != '' && this.contato.telefone != undefined) {
          this.contato.telefone = this.contato.telefone.replace(/\D/g, "");
          this.contato.telefone = this.contato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.contato.telefone = this.contato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.dados.telefone = this.contato.telefone;

        if (this.contato.telefoneMovel != null && this.contato.telefoneMovel != '' && this.contato.telefoneMovel != undefined) {
          this.contato.telefoneMovel = this.contato.telefoneMovel.replace(/\D/g, "");
          this.contato.telefoneMovel = this.contato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.contato.telefoneMovel = this.contato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.dados.telefoneMovel = this.contato.telefoneMovel;

        if (this.contato.telefoneComercial != null && this.contato.telefoneComercial != '' && this.contato.telefoneComercial != undefined) {
          this.contato.telefoneComercial = this.contato.telefoneComercial.replace(/\D/g, "");
          this.contato.telefoneComercial = this.contato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.contato.telefoneComercial = this.contato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.dados.telefoneComercial = this.contato.telefoneComercial;

        // if (retorno.valorConsulta)
        //   this.dados.valorConsulta = retorno.valorConsulta.replace(/,/g, ".");

        if (retorno.clinica.valorConsulta) {

          retorno.clinica.valorConsulta = this.verificaCasaDecimal(retorno.clinica.valorConsulta)
          retorno.clinica.valorConsulta = this.aplicarMascaraValor(retorno.clinica.valorConsulta)
          this.dados.valorConsulta = retorno.clinica.valorConsulta
        }


        this.dados.crm = this.clinica.crm
        this.dados.cnes = this.clinica.cnes
        this.dados.iss = this.clinica.issCcm


        this.dados.rua = this.endereco.rua;
        this.dados.numero = this.endereco.numero;
        this.dados.complemento = this.endereco.complemento;
        this.dados.bairro = this.endereco.bairro;
        this.dados.cep = this.endereco.cep;

        if (this.endereco.idCidade != null) {
          this.cidadeService.getCidades().then((retornaCidade) => {

            this.dadosCidade = retornaCidade
            var sigle = this.dadosCidade.filter((c:any) => c.idCidade == this.endereco.idCidade)
            this.dados.uf = sigle[0].siglasUf

            this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == sigle[0].siglasUf);
            this.dados.idCidade = this.endereco.idCidade
            this.spinner.hide();
          }, () => {
            this.spinner.hide();
          })
        }
        this.spinner.hide();
      }
      else
        this.snackBarAlert.falhaSnackbar( "Erro ao carregar dados!")
        this.spinner.hide();
    })
  }
  public ValidaCPFCNPJ(value:any) {
    this.campoCPFInvalido = false;
    this.campoCNPLInvalido = false;
    if (value != "") {
      this.campoCPFVazil = false;
      if (value.length < 14) {
        this.campoCPFInvalido = true;
        return
      }
      if (value.length > 14 && value.length < 18) {
        this.campoCNPLInvalido = true;
        return
      }

      if (value.length < 18) {
        if (!this.validacao.cpf(value)) {
          this.campoCPFInvalido = true;
          return;
        }
      }
      else {
        if (!this.validacao.cnpj(value)) {
          this.campoCNPLInvalido = true;
          return
        }
      }
    }
    else
      this.campoCPFVazil = true;
  }


  mascaravindoBanco(item:any) {


    if (item.length > 14) {


      var ao_cnpj = item;

      ao_cnpj = ao_cnpj.replace(/\D/g, "");
      ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
      ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
      ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");

      return ao_cnpj;
    }
    else {

      var ao_cpf = item;

      ao_cpf = ao_cpf.replace(/\D/g, "");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

      return ao_cpf;
    }
  }

  public Submit() {

    this.validarCampos();
    if (this.showMessageError) {
      return;
    }
    var clinica = new Clinica();
    var contato = new Contato();
    var endereco = new Endereco();

    clinica.Logo = this.dados.logo;
    clinica.DesClinica = this.dados.nomeClinica;
    clinica.DtaLicenca = (document.getElementById('dataLicenca') as HTMLInputElement) ['value'] != '' ? this.validacao.Convertdata((document.getElementById('dataLicenca') as HTMLInputElement)['value']) : null;
    clinica.Caracterizacao = this.dados.caracterizacao;
    clinica.Cnpj = this.dados.cnpj;
    clinica.Cnes = this.dados.cnes;
    clinica.IssCcm = this.dados.iss;
    clinica.Crm = this.dados.crm;
    clinica.FlgSomenteProntuario = this.flgProntuario;
    // clinica.valorConsulta = document.getElementById('valorConsulta')['value'];
    if (this.dados.valorConsulta) {
      this.dados.valorConsulta = this.validacao.removeMascara(this.dados.valorConsulta);
      this.dados.valorConsulta = this.dados.valorConsulta.replace(/(\d{1})(\d{1,2})$/, "$1.$2");
      clinica.valorConsulta = this.dados.valorConsulta;
    }

    contato.site = this.dados.website;
    contato.email = this.dados.emailClinica;
    contato.telefone = this.dados.telefone;
    contato.telefoneMovel = this.dados.telefoneMovel;
    contato.telefoneComercial = this.dados.telefoneComercial;
    clinica.FlgSomenteProntuario = this.FlgProntuario;

    if (this.imagemClinica != "assets/build/img/logo-clinica.png")
      clinica.Imagem64 = this.imagemClinica;

    ;

    clinica.idClinica = this.clinica.idClinica;
    clinica.IdContato = this.clinica.idContato;
    contato.idContato = this.clinica.idContato;
    clinica.IdEndereco = this.clinica.idEndereco;
    clinica.DtaCadastro = this.clinica.dtaCadastro;
    contato.dtaCadastro = this.contato.dtaCadastro;
    endereco.idEndereco = this.clinica.idEndereco;
    endereco.dtaCadastro = this.endereco.dtaCadastro;

    contato.idUsuarioGerador = this.contato.idUsuarioGerador;

    clinica.DtaCadastro = this.clinica.dtaCadastro;
    clinica.FlgInativo = this.clinica.flgInativo;
    clinica.IdUsuarioGerador = this.clinica.idUsuarioGerador;


    endereco.rua = this.dados.rua;
    endereco.numero = this.dados.numero;
    endereco.complemento = this.dados.complemento;
    endereco.idCidade = this.dados.idCidade;
    endereco.bairro = this.dados.bairro;
    endereco.cep = this.dados.cep;



    clinica.contato = contato;
    clinica.endereco = endereco;
    this.clinicaService.SalvarPerfilClinica(clinica).subscribe((retorno) => {
      

      if (retorno != true) {
        this.snackBarAlert.falhaSnackbar("Erro ao validar")
        
      }
      else {
        this.snackBarAlert.sucessoSnackbar("Clinica Atualizada com Sucesso")
      }
      this.spinner.hide();
    }, () => {
      this.snackBarAlert.falhaSnackbar("Erro ao salvar Clinica");
      this.spinner.hide();
    })
  }

  public validarCampos() {
    this.showMessageError = false;
    this.Nome.markAsTouched();
    this.cnpj.markAsTouched();
    this.email.markAsTouched();
    this.ValidaDtaChange();
    this.ValidarEmail(this.dados.emailClinica)
    this.ValidaTelefoneComercial((document.getElementById('TelefoneComercial') as HTMLInputElement)['value']);
    // this.ValidaTelefoneMovel(document.getElementById('TelefoneMovel')['value']);
    this.ValidaCPFCNPJ(this.dados.cnpj)

    if (this.dados.cnpj == undefined || !this.dados.cnpj.trim())
      this.campoCPFVazil = true;



    if (this.dados.nomeClinica == undefined || !this.dados.nomeClinica.trim()
      || this.dados.cnpj == undefined || !this.dados.cnpj.trim()
      || this.Dtalice == true || this.TelMovVal == true
      || this.TelVal == true || this.TelComVal == true
      || this.campoCNPLInvalido == true || this.campoCPFInvalido == true
      || this.campoEmailInvalido == true) {

      this.showMessageError = true;

      if (this.TelComVal != true && this.TelComVal != false || this.TelComLimpa == false) {
        this.TelComValVasil = true;
        this.TelComLimpa = true;
      }
      document.documentElement.scrollTop = 0;
    }

  }

  ValidarEmail(value:any) {
    if (value == undefined || !value.trim()) {
      this.mensagemErroEmail = "Esse campo precisa ser preenchido"
      this.campoEmailInvalido = true
      return
    } else {
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        this.mensagemErroEmail = "Email inválido"
      }
      else
        this.campoEmailInvalido = false;

      return

    }
  }

  public CidadePorUF() {
    try {

      this.cidadeService.getCidades().then((retornaCidade) => {
        this.dadosCidade = retornaCidade;
        this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == this.dados.uf);
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar( 'Erro ao carregar Cidade')
        this.spinner.hide();
      })

    } catch (error) {
      this.snackBarAlert.falhaSnackbar('Erro ao carregar Cidade')
    }
  }

  AlterarImagemClinica(event: any): void {
    this.ngxSmartModalService.getModal('ModalFoto').open();
    this.imageChangedEvent = event
  }
  LimpaCampoFile() {
    (document.getElementById('imageperfilusuario') as HTMLInputElement)['value'] = '';
  }
  CortarImagem() {
    this.ngxSmartModalService.getModal('ModalFoto').close()
    this.imagemClinica = this.imagemCorte
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCorte = event.base64;
    ;
  }
  imageLoaded() {
    this.showCropper = true;
    
  }
  cropperReady() {
    
  }
  loadImageFailed() {
    ;
  }
  transform: ImageTransform = {
    rotate: 0,
    flipH: false,
    flipV: false
  };

  rotateLeft() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! - 90 };
  }

  rotateRight() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! + 90 };
  }

  flipHorizontal() {
    this.transform = { ...this.transform, flipH: !this.transform.flipH };
  }

  flipVertical() {
    this.transform = { ...this.transform, flipV: !this.transform.flipV };
  }

  public mascaraText(evento: KeyboardEvent, campo: string) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");

    if (campo == 'Caract')
      this.dados.caracterizacao = v;

    if (campo == 'NomeCli')
      this.dados.nomeClinica = v;

    // (<HTMLInputElement>evento.target).value = v
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v
  }

  public MascaraDinheiro(evento: KeyboardEvent) {

    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }
    this.dados.valorConsulta = v
  }
  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }
  ValidaDta(dta:any):any {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '')
      this.Dtalice = false;


    else if (patternValidaData.test(dta)) {
      this.Dtalice = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtalice = true;
    }
    else
      this.Dtalice = false
  }
  ValidaDtaChange() {
    const dta = (document.getElementById('dataLicenca') as HTMLInputElement)['value'];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.Dtalice = false;
    }
    else if (patternValidaData.test(dta)) {
      this.Dtalice = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtalice = true;
    }
    else
      this.Dtalice = false
  }
  public mascaraCnpj(mascaraCNPJ:any, mascaraCPF:any, evento: KeyboardEvent) {

    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 18) {

      if (i > 14) {
        var saida = mascaraCNPJ.substring(0, 1);
        var texto = mascaraCNPJ.substring(i);

        if (texto.substring(0, 1) != saida) {
          return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
        }

        var ao_cnpj = valorEvento;

        ao_cnpj = ao_cnpj.replace(/\D/g, "");
        ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
        ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
        ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cnpj;
      }
      else {
        var saida = mascaraCPF.substring(0, 1);
        var texto = mascaraCPF.substring(i);

        if (texto.substring(0, 1) != saida) {
          return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
        }
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }
  public mascaraIss(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 11) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 8) {
        var ao_iss = valorEvento;

        ao_iss = ao_iss.replace(/\D/g, "");
        ao_iss = ao_iss.replace(/(\d{1})(\d)/, "$1.$2");
        ao_iss = ao_iss.replace(/(\d{3})(\d)/, "$1.$2");
        ao_iss = ao_iss.replace(/(\d{3})(\d{1})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_iss;
      }
    }
  }

  public mascaraCrm(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 7) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 7) {
        var ao_crm = valorEvento;

        ao_crm = ao_crm.replace(/\D/g, "");

        return (<HTMLInputElement>evento.target).value = ao_crm;
      }
    }
  }
  ValidaTelefone(tle:any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle:any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelMovVal = false;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovVal = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneComercial(tle:any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComValVasil = true;
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComValVasil = false;
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
      this.TelComValVasil = false;
    }
    else
      this.TelComVal = false
  }


  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }

  // ErroSalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROCLINICA.ERROAOSALVAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }
  // SalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELACADASTROCLINICA.CADASTROSLAVOCOMSUCESSO').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }


  Nome = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  cnpj = new FormControl('', [Validators.required, Validators.maxLength(18)])

  getErrorMessageNome() {
    return this.Nome.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.Nome.hasError('Nome') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.email.hasError('Email') ? 'TELACADASTROCLINICA.ERROEMAILNAOEVALIDO' :
        '';
  }
  getErrorMessageCNPJ() {
    return this.cnpj.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.cnpj.hasError('CNPJ') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
        '';
  }


  aplicarMascaraValor(v:any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor:any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

}
