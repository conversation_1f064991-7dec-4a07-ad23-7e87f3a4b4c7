import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit, Output } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { MedicamentosProgramados } from '../model/documentos';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { DocumentosService } from '../service/documentos.service';
import { ClinicaService } from '../service/clinica.service';
import { FadeIn } from 'src/app/Util/Fadein.animation';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDivider } from '@angular/material/divider';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';

@Component({
    selector: 'app-medicamentos',
    templateUrl: './medicamentos.component.html',
    styleUrls: ['./medicamentos.component.scss'],
    animations: [FadeIn],
    host: { '[@FadeIn]': '' },
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      MatDivider,
      NgxSmartModalModule,
      MatSelectModule,
      TruncatePipe
    ]
})
export class MedicamentosComponent implements OnInit {

  constructor(    
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private documentosService: DocumentosService,
    private clinicaService: ClinicaService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private usuarioLogadoService: UsuarioLogadoService

  ) { }


  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;

  @Output() FadeIn?: string;
  // isOpen = false;
  showMessageError: boolean = false;
  Dados: any = [];
  posologia = '';
  // usuario: Usuario;
  DesItem: boolean = false;
  DadosClinicas: any;
  clinicas: any = new FormControl([]);
  clinicaVasil?: boolean;
  clinicaVal?: boolean;
  DadosTable: any = [];
  pesquisa = ''
  remedioDelet = 0;
  legenda = false;

  qtdRegistros = 10;
  bOcultaCarregaMais = false;



  ngOnInit() {
    this.CarregaClinicas();
    this.CarregaMedicamentos();
  }

  toggle: any = {}
  CarregaClinicas() {
    try {
      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {
        this.DadosClinicas = retornaClinicas
        
        
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar("erro no retorno especialidade");
        this.spinner.hide();
      })
    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Erro ao carregar!");
      this.spinner.hide();
    }
  }


  CarregaMedicamentos() {
    try {
      this.bOcultaCarregaMais = false

      this.documentosService.CarregaMedicamentosGrid(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        this.DadosTable = retorno

        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;
        
        
        this.spinner.hide();
      }, () => {        
        this.snackBarAlert.falhaSnackbar("Erro ao carregar!");
        this.spinner.hide();
      })
    } catch (error) {
      
      this.snackBarAlert.falhaSnackbar("Erro ao carregar!");

      this.spinner.hide();
      // this.ErroCarregar(true)
    }
  }

  CarregarMais() {
    try {

      this.bOcultaCarregaMais = false
      this.documentosService.CarregaMedicamentosGrid(this.DadosTable.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        var dados = retorno;
        for (let index = 0; index < dados.length; index++) {
          this.DadosTable.push(dados[index]);
        }
        if (retorno.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;
        
        
        this.spinner.hide();
      }, err => {
        // this.ErroCarregar(true)
        this.snackBarAlert.falhaSnackbar("Erro ao carregar!",err)
        
        this.spinner.hide();
      })


    } catch (error) {
      
      this.spinner.hide();

    }
  }


  LimparCampos() {
    this.clinicas = new FormControl([])
    this.Dados = [];
    this.posologia = ''
    this.DesItem = false;
    this.Descricao.markAsUntouched();
    this.Poso.markAsUntouched();
    this.clinicaVal = false

  }




  public Submit() {
    try {
      this.validarCampos();
      if (this.showMessageError) {
        return;
      }


      var medicamentos = new MedicamentosProgramados();
      medicamentos.FlgDestacarItem = this.DesItem;
      medicamentos.MedicamentosProgramados = this.Dados.des;
      medicamentos.Programacao = this.posologia;
      if (this.Dados.idMedicamento)
        medicamentos.IdMedicamentosProgramados = this.Dados.idMedicamento
      else
        medicamentos.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();


      var Clinic:any = []
      this.clinicas.value.forEach((element:any) => {

        Clinic.push({
          idClinica: element.idClinica,
        })
      });

      if (Clinic.length > 0)
        medicamentos.clinicas = Clinic;

      this.documentosService.SalvarMedicamentos(medicamentos).subscribe(() => {
        this.CarregaMedicamentos();
        this.LimparCampos();

        this.snackBarAlert.sucessoSnackbar("Salvo com sucesso!")
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar!")
        this.spinner.hide();
      })

    } catch (error) {
      this.snackBarAlert.falhaSnackbar("falha na conexão!")
      
    }
  }
  Descricao = new FormControl('', [Validators.required, Validators.minLength(4)])
  Poso = new FormControl('', [Validators.required, Validators.minLength(4)])


  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  getErrorMessageDesc() {
    return this.Descricao.hasError('required') ? 'TELAMEDICAMENTOS.ERROCAMPO' :
      this.Descricao.hasError('Nome') ? 'TELAMEDICAMENTOS.ERRONAOEVALIDO' :
        '';

  }
  getErrorMessagePoso() {
    return this.Poso.hasError('required') ? 'TELAMEDICAMENTOS.ERROCAMPO' :
      this.Poso.hasError('Nome') ? 'TELAMEDICAMENTOS.ERRONAOEVALIDO' :
        '';

  }

  ValidaClinicas(cli: any = []) {
    this.clinicas.value.forEach((element:any) => {

      cli.push({
        idClinica: element.idClinica,
      })
    });
    if (cli.length > 0) {
      this.clinicaVasil = false;
      this.clinicaVal = false;
    }
    else {
      this.clinicaVasil = false;
      this.clinicaVal = true;
    }
  }


  public validarCampos() {
    this.showMessageError = false;
    this.Descricao.markAsTouched();
    this.Poso.markAsTouched();
    this.ValidaClinicas()

    if (this.Dados.des == '' || this.Dados.des == undefined
      || this.clinicaVal == true || this.clinicaVasil == true
      || this.posologia == '' || this.posologia == undefined) {

      this.showMessageError = true;
      document.documentElement.scrollTop = 0;
    }

    if (this.clinicaVasil == true) {
      this.clinicaVal = true;
    }

  }



  editMedicamento(id:any) {

    var remedio = this.DadosTable.filter((c:any) => c.medicamento.idMedicamentosProgramados == id)
    this.Dados.des = remedio[0].medicamento.medicamentosProgramados;
    this.posologia = remedio[0].medicamento.programacao;
    this.DesItem = remedio[0].medicamento.flgDestacarItem;
    this.Dados.idMedicamento = remedio[0].medicamento.idMedicamentosProgramados;

    if (remedio[0].clinicas && remedio[0].clinicas.length > 0) {

      this.clinicas = [];
      remedio[0].clinicas.forEach((element:any) => {
        this.DadosClinicas.forEach((elemento:any) => {
          if (elemento.idClinica == element.idClinica)
            this.clinicas.push(elemento)
        })
      })
      this.clinicas = new FormControl(this.clinicas);
    }
    document.documentElement.scrollTop = 0;
  }

  valueRemedio(id:any) {
    this.remedioDelet = id;
    if (this.remedioDelet > 0)
      this.ngxSmartModalService.getModal('excluirItem').open();

  }

  InativarMedicamento() {
    try {
      if (this.remedioDelet > 0) {
        this.documentosService.inativarMedicamento(this.remedioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
          var Delete = retorno;
          if (Delete == true) {
            this.remedioDelet = 0
            this.CarregaMedicamentos();
            this.ngxSmartModalService.getModal('excluirItem').close();
            this.snackBarAlert.sucessoSnackbar('Item inativado com sucesso!')
            this.spinner.hide();
          }
          else {
            this.snackBarAlert.falhaSnackbar('Falha ao inativar item!')
            this.spinner.hide();
          }
        }, () => {
          this.snackBarAlert.falhaSnackbar('Falha ao inativar item!')
          this.spinner.hide();
        })
      }

    } catch (error) {
      this.snackBarAlert.falhaSnackbar('Falha na conexão!')
      
      this.spinner.hide();
    }



  }

  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTable[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.DadosTable[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index:any) {
    
    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.DadosTable[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTable[this.indexGlobal]['toggle'] = !this.DadosTable[this.indexGlobal]['toggle'];
  }

}
