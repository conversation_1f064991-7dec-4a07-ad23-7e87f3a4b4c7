<div class="sala-container">
    <div class="card main-card">
      <div class="card-header">
        <div class="header-left">
          <div class="icon-container">
            <span class="material-icons">meeting_room</span>
          </div>
          <h1 class="page-title">Sala</h1>
        </div>
        <button class="btn btn-link" onclick='history.go(-1)'>
          <span class="material-icons">arrow_back</span>
          
        </button>
      </div>
  
      <div class="card-body">
        <!-- Seção de dados da sala -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">Dados da Sala</h2>
          </div>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group col-md-2">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Número</mat-label>
                  <input matInput placeholder="Número" id="Numero" type="number" [(ngModel)]="objSala.numero">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-10">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Nome</mat-label>
                  <input matInput placeholder="Nome" type="text" [(ngModel)]="objSala.nome">
                </mat-form-field>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group full-width">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Descrição</mat-label>
                  <input matInput placeholder="Descrição" type="text" [(ngModel)]="objSala.descSala">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <div class="card-footer">
        <button class="btn btn-success" (click)="SalvarSala()">
          <span class="material-icons">save</span>
          Salvar
        </button>
      </div>
    </div>
  </div>