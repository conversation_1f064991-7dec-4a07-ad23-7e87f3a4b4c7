import { DadosConsultaHojeModelView, ObjGrafico, GuiaTissModelview, LoteGuia, ConsultaModelView, DadosLifeLine } from './../model/consulta';
import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { catchError, finalize, Observable, of, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ChatConsulta, objPesquisaconsulta } from '../model/consulta';
import { AnexosArquivos } from '../model/anexosConsulta';
import { Avaliacao } from '../model/avaliacao';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { SpinnerService } from './spinner.service';
import { ObjFaturamento, ObjFaturamentoLote } from '../model/fatura';
import { CriptografarUtil } from '../Util/Criptografar.util';

@Injectable({
    providedIn: 'root'
})
export class ConsultaService {

    public atualizaLegenda$: EventEmitter<string>;
    public atualizaComparativo$: EventEmitter<string>;

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
        this.atualizaLegenda$ = new EventEmitter();
        this.atualizaComparativo$ = new EventEmitter();
    }

    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getQuesitosAvaliacao() {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Quesitos')
            .toPromise();
    }
    public GetDashBoard(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/CarregaDadosDashBoard/' + id);
    }


    // Métodos removidos: filaEsperaSolicitante e filaEsperaPaciente
    // Agora usamos apenas o sistema de fila baseado em token para usuários não logados
    public GetConsultaGuid(guid: any, codAcesso: any): Observable<any> {
        this.spinner.show();
        var reqHeader = new HttpHeaders({ 'No-Auth': 'True' });
        return this.http.get(environment.apiEndpoint + '/Agenda/GetConsultaGuid/' + guid + '/' + codAcesso, { headers: reqHeader });
    }

    public FinalizarConsulta(id: any, idUsuario: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/FinalizarConsulta/' + id + '/' + idUsuario);
    }

    public FinalizarConsultaProntuario(id: any, idCid: any, idUsuario: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/FinalizarConsultaProntuario/' + id + '/' + idCid + '/' + idUsuario);
    }

    public GetDadosMesDashBoard(idPessoa: any, idClinica: any, idTipoPessoa: any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('idPessoa', idPessoa);
        params = params.append('idClinica', idClinica);
        params = params.append('idTipoPessoa', idTipoPessoa);

        return this.http.get(environment.apiEndpoint + '/Consulta/CarrecarTotalDash', { params });


    }
    public CheckinConsulta(idconsulta: any, idusuario: any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idUsuario', String(idusuario));
        params = params.append('idConsulta', String(idconsulta));
        return this.http.get(environment.apiEndpoint + '/Consulta/CheckinConsulta', { params });
    }



    public GetAvaliacaoConsulta(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/GetAvaliacaoConsulta/' + id);
    }
    public GetConsulta(id: any): Observable<any> {
        return this.http.get(environment.apiEndpoint + '/Consulta/getDadosConsulta/' + id);
    }
    public ConsultaAndamento(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/ConsultaAndamento/' + id);
    }
    public GetchatConsulta(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/GetchatConsulta/' + id);
    }

    public carregaConsultaFilaEspera(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/carregaConsultaFilaEspera/' + id);
    }

    public GetTelaConsulta(objConsulta: objPesquisaconsulta): Observable<any> {
        return this.http.post<ConsultaModelView[]>(environment.apiEndpoint + '/Consulta/carregaConsulta', objConsulta);
    }

    public GetAtualizacao(objConsulta: objPesquisaconsulta): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Consulta/carregaConsultaAtualizacao', objConsulta);
    }
    public GetRetornoConvenio(idPaciente: any, idConvenio: any): Observable<any> {
        let params = new HttpParams();
        params = params.append('idPaciente', String(idPaciente));
        params = params.append('idConvenio', String(idConvenio));
        return this.http.get(environment.apiEndpoint + '/Consulta/RetornoConvenio', { params });
    }

    public GetConsultaAlerta(idusuario: any, tipoUsuario: any, idclinica: any): Observable<any> {
        let params = new HttpParams();
        params = params.append('idusuario', String(idusuario));
        params = params.append('tipoUsuario', String(tipoUsuario));
        params = params.append('idclinica', String(idclinica));
        return this.http.get(environment.apiEndpoint + '/Consulta/carregaConsultaAlerta', { params });
    }

    public SalvarIdPagamentoconsulta(idPagamento: any, idConsulta: any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('idPagamento', String(idPagamento));
        params = params.append('idConsulta', String(idConsulta));
        return this.http.get(environment.apiEndpoint + '/TransacaoFinanceira/SalvarPagamentoConsulta', { params });
    }

    public UploadArquivo(arquivos: AnexosArquivos, files: FileList,): Observable<any> {
        this.spinner.show();

        const formData: FormData = new FormData();
        for (let index = 0; index < files.length; index++) {
            formData.append('fileKey' + index, files[index], files[index].name);

        }

        return this.http.post(environment.apiEndpoint + '/Consulta/UploadArquivo/' + arquivos.IdConsulta + '/' + arquivos.IdUsuarioGerador + '/' + arquivos.FlgVisualizacaoUsuario, formData);
    }


    public GetDadoshistoricoPaciente(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/DadosHistorico/' + id);
    }

    public PutOnlinenaConsulta(idconsulta: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/PutOnlinenaConsulta/' + idconsulta);
    }

    public MedicoOnlinenaConsulta(idconsulta: any, idUsuario: any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('idUsuario', String(idUsuario));
        params = params.append('idconsulta', String(idconsulta));
        return this.http.get(environment.apiEndpoint + '/Consulta/MedicoOnlinenaConsulta', { params });
    }
    public GetDadosLifeLine(id: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/DadosConsultaLifeLine/' + id);
    }
    public GetDadosPizza(idClinica: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/CarregaDadosGrafico/' + idClinica);
    }


    public GetLifeLine(idTipoUsuario: any, idUsuario: any): Observable<any> {
        this.spinner.show();

        var idMedico = 0;
        var idPaciente = 0;
        if (idTipoUsuario == EnumTipoUsuario.Médico)
            idMedico = idUsuario
        else if (idTipoUsuario == EnumTipoUsuario.Paciente)
            idPaciente = idUsuario


        let params = new HttpParams();
        params = params.append('idMedico', String(idMedico));
        params = params.append('idUsuario', String(idPaciente));
        return this.http.get(environment.apiEndpoint + '/Consulta/GetLifeLine', { params });
    }



    public AnexosConsulta(idConsulta: any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/CarregarDocumentos/' + idConsulta);
    }

    public CarregaSolicitacoesOrientacoes(idPessoa: any, idClinica: any, inicio: any, fim: any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('idPessoa', idPessoa);
        params = params.append('idClinica', idClinica);
        params = params.append('inicio', inicio);
        params = params.append('fim', fim);

        return this.http.get(environment.apiEndpoint + '/Consulta/CarregaSolicitacoesOrientacoes', { params });
    }


    public DadosRelatorio(idPessoa: any, idClinica: any, idTipoPessoa: any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('idPessoa', idPessoa);
        params = params.append('idClinica', idClinica);
        params = params.append('idTipoPessoa', idTipoPessoa);

        return this.http.get(environment.apiEndpoint + '/Consulta/getDadosrelatorico', { params });
    }


    public SalvarAnotacaoAnonima(ObsAnonima: any, idConsulta: any, idUsuario: any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('ObsAnonima', String(ObsAnonima));
        params = params.append('idConsulta', String(idConsulta));
        params = params.append('idUsuario', String(idUsuario));

        return this.http.get(environment.apiEndpoint + '/Consulta/SalvarAnotacaoAnonima', { params });
    }
    public SalvarObservacaoConsulta(Obs: any, idConsulta: any, idUsuario: any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('Obs', String(Obs));
        params = params.append('idConsulta', String(idConsulta));
        params = params.append('idUsuario', String(idUsuario));

        return this.http.get(environment.apiEndpoint + '/Consulta/SalvarObservacaoConsulta', { params });
    }


    public SalvaAvaliacao(avaliacao: Avaliacao): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Consulta/SalvarAvaliacao', avaliacao);
    }

    public SalvaChat(chat: ChatConsulta): Observable<any> {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Consulta/PostChat', chat);
    }


    public RemoveArquivo(guid: any, idDocumento: any): Observable<any> {
        this.spinner.show();
        try {


            let params = new HttpParams();
            params = params.append('idDocumento', String(idDocumento));
            params = params.append('guid', String(guid));
            return this.http.get(environment.apiEndpoint + '/Consulta/RemoverDocumento', { params });

        } catch (error) {
            ;
            return of(null);
        }
    }
    public CarregaCaminhoArquivo(guid: any, nomeArquivo: any): Observable<any> {
        this.spinner.show();
        try {


            let params = new HttpParams();
            params = params.append('guid', String(guid));
            params = params.append('nomeArquivo', String(nomeArquivo));
            return this.http.get(environment.apiEndpoint + '/Consulta/CarregarURLDocumento', { params, responseType: 'arraybuffer' });

        } catch (error) {
            ;
            return of(null);
        }
    }

    public GetAvaliacoesGrafico(idClinica: any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/GetAvaliacoesGrafico/' + idClinica);
    }

    public GetValoresGrafico(Obj: ObjGrafico) {
        this.spinner.show();

        return this.http.post(environment.apiEndpoint + '/Consulta/GetValoresGrafico', Obj);

    }

    public GetConsultasUltimosMesesGrafico(idClinica: any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Consulta/GetConsultasUltimosMesesGrafico/' + idClinica);

    }

    public carregaconsulta_Paciente_ColunaDireita(IDUsuario: number) {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Consulta/carregaconsulta_Paciente_ColunaDireita', IDUsuario)
    }

    public SalvarGuiaTiss(guiaTiss: GuiaTissModelview) {
        return this.http.post(environment.apiEndpoint + '/Consulta/SalvarGuiaTiss', guiaTiss)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public ConverterLoteGuiaZip(loteGuia: LoteGuia): Observable<any> {
        return this.http.post(environment.apiEndpoint + '/Consulta/ConverterLoteGuiaZip', loteGuia)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public GetConsultasHoje(idMedico: number) {
        return this.http.post<DadosConsultaHojeModelView[]>(environment.apiEndpoint + '/Consulta/GetConsultasHoje', idMedico)
    }



    public CarregaFaturamento(idConsulta: number) {
        let params = new HttpParams();
        params = params.append('idConsulta', idConsulta);
        return this.http.get<ObjFaturamento>(environment.apiEndpoint + '/Consulta/CarregaFaturamento', { params })
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public SalvarFaturamento(obj: ObjFaturamento) {
        return this.http.post<boolean>(environment.apiEndpoint + '/Consulta/SalvarFaturamento', obj)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }
    public SalvarFaturamentoLote(obj: ObjFaturamentoLote) {
        return this.http.post<boolean>(environment.apiEndpoint + '/Consulta/SalvarFaturamentoLote', obj)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public GetListaConsulta(objPesquisa: objPesquisaconsulta) {
        return this.http.post<ConsultaModelView[]>(environment.apiEndpoint + '/Consulta/GetListaConsulta', objPesquisa)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public GetListaConsultaFatura(idFatura: number) {
        let params = new HttpParams();
        params = params.append('idFatura', idFatura);
        return this.http.get<ConsultaModelView[]>(environment.apiEndpoint + '/Consulta/GetListaConsultaFatura', { params })
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public BaixarRelatorioListaConsulta(listaIdConsulta: number[]) {
        return this.http.post<string>(environment.apiEndpoint + '/Consulta/BaixarRelatorioListaConsulta', listaIdConsulta)
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public GetDadosLifeLinePaciente(idPaciente: number) {
        let params = new HttpParams();
        params = params.append('idPaciente', idPaciente);
        return this.http.get<DadosLifeLine[]>(environment.apiEndpoint + '/Consulta/GetDadosLifeLinePaciente', { params })
            .pipe(
                catchError((error) => {
                    ;
                    return throwError(() => error);
                })
            );
    }

    public cadastrarFilaEsperaDeslogado(dadosQuestionario: any): Observable<any> {
        this.spinner.show();


        if (!dadosQuestionario) {
            console.error('dadosQuestionario está null ou undefined');
            this.spinner.hide();
            throw new Error('Dados do questionário são obrigatórios');
        }

        const nome = dadosQuestionario.nome || dadosQuestionario.nomePaciente || '';
        const dataNascimento = dadosQuestionario.dataNascimento || dadosQuestionario.dataNascimento;

        if (!nome.trim()) {
            console.error('Nome do paciente não informado');
            this.spinner.hide();
            throw new Error('Nome do paciente é obrigatório');
        }

        if (!dataNascimento) {
            console.error('Data de nascimento não informada');
            this.spinner.hide();
            throw new Error('Data de nascimento é obrigatória');
        }

        const idade = calcularIdade(dataNascimento);

        if (idade <= 0) {
            console.error('Idade inválida calculada:', idade);
            this.spinner.hide();
            throw new Error('Data de nascimento inválida');
        }

        const payload = {
            NomePaciente: nome.trim(),
            IdadePaciente: idade,
            Token: CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera')
        };


        return this.http.post(environment.apiEndpoint + '/fila-espera/adicionar-paciente', payload)
            .pipe(
                catchError(error => {
                    console.error('Erro na requisição:', error);
                    this.spinner.hide();
                    throw error;
                }),
                finalize(() => this.spinner.hide())
            );
    }

    public filaEsperaParaMedico(): Observable<any[]> {
        this.spinner.show();
        return this.http.get<any[]>(environment.apiEndpoint + '/fila-espera/listar-pacientes');
    }

    public removerDaFilaEspera(token: string): Observable<any> {
        this.spinner.show();

        return this.http.delete(`${environment.apiEndpoint}/fila-espera/remover-paciente/${token}`)
            .pipe(
                catchError(error => {
                    console.error('Erro ao remover da fila:', error);
                    this.spinner.hide();
                    throw error;
                }),
                finalize(() => this.spinner.hide())
            );
    }

    public consultarPosicaoFila(token: string): Observable<any> {
        return this.http.get(environment.apiEndpoint + `/fila-espera/consultar-posicao/${token}`);
    }

    public obterPacienteFila(token: string): Observable<any> {
        return this.http.get(environment.apiEndpoint + `/fila-espera/paciente/${token}`);
    }

}

export function calcularIdade(dataNascimento: string | Date): number {
    if (!dataNascimento) return 0;

    let nascimento: Date;

    if (typeof dataNascimento === 'string') {
        if (dataNascimento.includes('/')) {
            const [dia, mes, ano] = dataNascimento.split('/');
            nascimento = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
        } else {
            nascimento = new Date(dataNascimento);
        }
    } else {
        nascimento = dataNascimento;
    }

    if (isNaN(nascimento.getTime())) {
        console.error('Data de nascimento inválida:', dataNascimento);
        return 0;
    }

    const hoje = new Date();
    let idade = hoje.getFullYear() - nascimento.getFullYear();
    const mesAtual = hoje.getMonth();
    const mesNascimento = nascimento.getMonth();

    if (mesAtual < mesNascimento || (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {
        idade--;
    }

    return idade;
}