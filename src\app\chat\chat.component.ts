import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { FiltroCarregaMensagem, MensagemModel, UsuarioChat } from '../model/chat';
import { ChatService } from '../service/chat.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SignalHubService } from '../service/signalHub.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatIcon } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    MatIcon,
    MatMenuModule,
    TruncatePipe
  ]
})
export class ChatComponent implements OnInit {

  // private spinner: SpinnerService,
  constructor(
    private dialogRef: MatDialogRef<ChatComponent>,
    private chatService: ChatService,
    private localStorageService: LocalStorageService,
    private usuarioLogadoService: UsuarioLogadoService,
    private signalService: SignalHubService,
    private spinner: SpinnerService,
  ) {
  }
  nomeBusca = "";
  objFiltro = new FiltroCarregaMensagem();

  lsContatos: UsuarioChat[] = [];
  lsContatosBkp: UsuarioChat[] = [];

  lsMensagens: MensagemModel[] = [];

  objUsuarioSelecionado: UsuarioChat = new UsuarioChat();
  idChatSelecionado?: number;

  msg: string = "";

  flgBtCarregarMais: boolean = false
  flgExibeLoader: boolean = false

  qtdTotalMensagem: number = 20;

  idUsuarioLogado: number = 0;
  nomeUsuarioLogado: string = "";

  flgNotificacaoSonora: boolean = false;
  flgNotificacaoWindows: boolean = false;

  ngOnInit(): void {
    this.flgNotificacaoSonora = this.localStorageService.flgNotificacaoSonora;
    this.flgNotificacaoWindows = this.localStorageService.flgNotificacaoWindows;

    this.signalService
      .OnAtualizaMensagem
      .subscribe(() => {
        
        this.CarregarContatos();
        this.carregarMensagens();
      });

    this.idUsuarioLogado = this.usuarioLogadoService.getIdUsuarioAcesso()!;
    this.nomeUsuarioLogado = this.usuarioLogadoService.getNomeUsuario()!;

    this.CarregarContatos();
  }

  CarregarContatos() {
    this.chatService.CarregaContatos().subscribe((ret) => {
      if (ret.ok) {
        this.lsContatos = ret.lsUsuarios!;
        this.lsContatosBkp = ret.lsUsuarios!;
      }
    })
  }

  onEnter(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault(); // Impede o comportamento padrão do Enter
      this.EnviarMensagem();
    }
  }

  @ViewChild('textareaElement') textareaElement?: ElementRef;

  EnviarMensagem() {
    if (!this.msg?.trim()) {
      return;
    }

    const msg = new MensagemModel();
    msg.mensagem = this.msg.trim();
    msg.idDestinatario = this.idChatSelecionado!;
    msg.dtaEnvio = new Date();
    msg.idRemetente = this.idUsuarioLogado;

    this.lsMensagens.unshift(msg);
    this.msg = "";

    this.chatService.EnviarMensagem(msg).subscribe(() => {
      this.textareaElement!.nativeElement.focus();
    });
  }


  validaBtEnviarMsg(): boolean {
    let flg = true;
    if (!this.idChatSelecionado)
      flg = false;

    if (!this.msg)
      flg = false;

    const cleanedInput = this.msg.replace(/\s+/g, '');
    if (cleanedInput.length < 1)
      flg = false;

    const cleanedInput2 = this.msg.trim();
    if (cleanedInput2 == "")
      flg = false;

    return flg;

  }

  onScroll(event: any) {
    const element = event.target;
    const valorMax = element.scrollHeight - 5;
    const isAtTop = Math.abs(element.scrollTop) + element.clientHeight >= valorMax;

    if (isAtTop && this.flgBtCarregarMais) {
      this.flgExibeLoader = true;
      this.qtdTotalMensagem = this.qtdTotalMensagem + 20;
      this.carregarMensagens();
    }
  }

  selecionarUsuario(user: UsuarioChat) {
    this.idChatSelecionado = user.idUsuario;
    this.objUsuarioSelecionado = user;
    this.lsMensagens = [];
    this.spinner.show();
    this.carregarMensagens();
  }

  carregarMensagens() {

    let filtro = new FiltroCarregaMensagem();

    filtro.idChat = this.idChatSelecionado!;
    filtro.qtdInicio = 0;
    filtro.qtdFim = this.qtdTotalMensagem;

    this.chatService.CarregaMensagemChat(filtro).subscribe((ret) => {
      this.spinner.hide();
      if (ret.ok && ret.listaMensagem) {
        this.lsMensagens = ret.listaMensagem;

        ;

        if (this.lsMensagens.length == this.qtdTotalMensagem)
          this.flgBtCarregarMais = true;

        else
          this.flgBtCarregarMais = false;

        this.flgExibeLoader = false;


        this.signalService.OnAtualizaQtdMensagem.emit();
      }
    })
  }

  fecharModal(): void {
    this.dialogRef.close();
  }

  BuscarUsuario() {
    this.lsContatos = []
    if (this.nomeBusca.trim() !== '') {
      let filtro = this.nomeBusca.toLowerCase();
      this.lsContatos = this.lsContatosBkp.filter(pe => {
        return (
          pe.nomeUsuario!.toLowerCase().includes(filtro)
        );
      });
    } else {
      this.lsContatosBkp.forEach(usu => {
        this.lsContatos.push(usu)
      });
    }
  }

  formatDate(input: string | Date | null): string | void {
    if (!input) return;

    const date = new Date(input);

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
  }







  atualizaNotificacao_Sonora() {
    let statusAtual = this.localStorageService.flgNotificacaoSonora;
    this.flgNotificacaoSonora = this.localStorageService.flgNotificacaoSonora = !statusAtual;

  }
  atualizaNotificacao_Windows() {
    let statusAtual = this.localStorageService.flgNotificacaoWindows;
    this.flgNotificacaoWindows = this.localStorageService.flgNotificacaoWindows = !statusAtual;
  }
}
