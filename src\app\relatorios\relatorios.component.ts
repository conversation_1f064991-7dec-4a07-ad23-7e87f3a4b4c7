import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { DocumentosService } from '../service/documentos.service';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { ClinicaService } from '../service/clinica.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { flgConsultasRelatorio, flgMedicoRelatorio, flgPacientesRelatorio } from '../model/flgRelatorio';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input'
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DateValidationResult, parseDateString, validateDates } from '../Util/ValidadorDataPorPeriodo';


@Component({
    selector: 'app-relatorios',
    templateUrl: './relatorios.component.html',
    styleUrls: ['./relatorios.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      TruncatePipe,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIconModule,
      MatFormFieldModule,
      MatDivider,
      NgxSmartModalModule,
      TranslateModule,
      MatCheckboxModule,
      MatSlideToggleModule,
    ]
})
export class RelatoriosComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private documentosService: DocumentosService,
    private clinicaService: ClinicaService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private tradutor: TranslateService,
    public ngxSmartModalService: NgxSmartModalService,
    private usuarioLogadoService: UsuarioLogadoService

  ) { }
  elementosAba: any;
  tituloAba: any;
  tituloAba1: any;
  tituloAba2: any;
  tituloAba3: any;
  abas1: any;
  abas2: any;
  abas3: any;
  DtaFimErrado: boolean = false
  DtaInicioErrado: boolean = false
  abas: any;
  dadosExcelFor: any;
  dadosExcel: any;
  // usuario: Usuario;
  DadosClinicasUser: any = [];
  nomeClinica: string = ""
  ImagemClinica: string = ""
  DadosClinicas: any = [];

  Consultas: boolean = true;
  CadastrosMedicos: boolean = true;
  CadastrosPacientes: boolean = true;


  relatorioConsulta = new flgConsultasRelatorio();
  relatorioCadastrosMedico = new flgMedicoRelatorio();
  relatorioCadastrosPaciente = new flgPacientesRelatorio();

  listaTodosPadroes: any = []
  idPadrao?: number | null;
  nomePadrao?: string | null;
  idPadraoExclusao?: number;

  idClinicaUsuLogado: any;

  ngOnInit() {
    this.CarregaClinicas();
    this.idClinicaUsuLogado = this.usuarioLogadoService.getIdUltimaClinica();
    // this.ChangeConsulta();
    this.relatorioConsulta = new flgConsultasRelatorio;
    this.relatorioCadastrosMedico = new flgMedicoRelatorio;
    this.relatorioCadastrosPaciente = new flgPacientesRelatorio;
    this.CarregarTodosPadroes();
    this.checkItens();

  }



  CarregaClinicas() {
    try {
      this.clinicaService.getGridClinica(0, 100, null).subscribe((retorno: any) => {

        retorno.forEach((element: any) => {
          if (element.idclinica == this.usuarioLogadoService.getIdUltimaClinica()) {
            if (element.logo && element.logo != null)
              this.ImagemClinica = element.logo
            this.nomeClinica = element.nomeClinica
            this.DadosClinicas.push(element)
          }
        });

        this.spinner.hide();

      }, () => {
        this.spinner.hide();
      })
    } catch (error) {

    }
  }

  ChangeConsulta() {
    this.clinicaService.getGridClinica(0, 100, null).subscribe((retorno: any) => {

      this.DadosClinicasUser = []
      var clinic: any = []
      retorno.forEach((element: any) => {
        this.usuarioLogadoService.getClinicas()!.forEach(elemento => {
          if (element.idclinica == elemento.idClinica) {
            if (element.cnpj != null && element.cnpj != "")
              element.cnpj = element.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5");
            if (element.tel != null && element.tel != "") {
              element.tel = element.tel.replace(/\D/g, "");
              element.tel = element.tel.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.tel = element.tel.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            clinic.push(element)
          }
          this.spinner.hide();
        });

      });
      this.DadosClinicasUser = clinic;




      this.ngxSmartModalService.getModal('trocaClinica').open();
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })

  }
  CarregaPadrao(id: any) {
    this.documentosService.GetPadraoRelatorio(id, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      if (retorno) {
        var Dados = JSON.parse(retorno.desPadraoJson)
        this.relatorioConsulta = Dados
        if (Dados.Paciente == null)
          this.relatorioCadastrosPaciente = new flgPacientesRelatorio;
        else
          this.relatorioCadastrosPaciente = Dados.Paciente;

        if (Dados.Medico == null)
          this.relatorioCadastrosMedico = new flgMedicoRelatorio
        else
          this.relatorioCadastrosMedico = Dados.Medico;

        this.idPadrao = retorno.idPadraoRelatorioUsuario;
        this.nomePadrao = retorno.nomePadraoRelatorio;

        this.Consultas = Dados.FlgCheckConsultas;
        this.CadastrosMedicos = Dados.FlgCheckMedico;
        this.CadastrosPacientes = Dados.FlgCheckPaciente;

        this.checkItens();
      }
      else { this.checkItens() }
      this.spinner.hide();
    })
  }

  tocarClinic(idclinica: any) {
    this.clinicaService.TrocarClinica(this.usuarioLogadoService.getIdUsuarioAcesso()!, idclinica).subscribe((retorno) => {
      this.usuarioLogadoService.getUsuarioLogado().subscribe(async (ret) => {
        var usuLogado = ret;
        ;
        usuLogado!.idUltimaClinica = retorno.idClinica;

        this.usuarioLogadoService.setUsuarioLogado(JSON.stringify(usuLogado));

        this.ngxSmartModalService.getModal('trocaClinica').close();
        window.location.reload();
        this.spinner.hide();
      });

    }, () => {
      this.spinner.hide();
    })

  }

  ValidaDta(dta: any) {
    this.DtaInicioErrado = false;

    var min = new Date('01/01/1753 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaInicioErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaInicioErrado = true;
        return;
      }

    }
    else
      return;

  }

  public mascaraNum(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
  }


  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");
    v = v.replace(/(\d{2})(\d)/, "$1/$2");
    v = v.replace(/(\d{2})(\d)/, "$1/$2");

    (<HTMLInputElement>evento.target).value = v;
  }

  modalOpcoes : any

  ValidaFimDta(dta: any) {
    this.DtaFimErrado = false;

    var min = new Date('01/01/1753 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaFimErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaFimErrado = true;
        return;
      }

    }
    else
      return;

  }




  public GerarRelatorio() {
    try {
      this.validaDatas()


      if (!this.hasSelectedCheckbox()) {
        this.tradutor.get('TELARELATORIOS.NENHUMDADOFOIMARCADOPARAGERARORELATORIO').subscribe((res: string) => {
          this.snackBarAlert.falhaSnackbar(res);
        });
        return;
      }

      if (this.flgDtInicioNaoPreenchida || this.flgDtFimNaoPreenchida || this.flgDtFimMaiorQueInicio) {
        this.snackBarAlert.falhaSnackbar("Erro na data preenchida.");
        return;
      }

      this.relatorioConsulta!.Medico = this.relatorioCadastrosMedico;
      this.relatorioConsulta!.Paciente = this.relatorioCadastrosPaciente;
      this.relatorioConsulta!.FlgCheckConsultas = true;
      this.relatorioConsulta!.FlgCheckMedico = true;
      this.relatorioConsulta!.FlgCheckPaciente = true;
      this.relatorioConsulta!.idClinica = this.usuarioLogadoService.getIdUltimaClinica()
      this.relatorioConsulta!.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso()

      this.relatorioConsulta!.DtaInicio = this.dtInicio;
      this.relatorioConsulta!.DtaFim = this.dtFim;

      this.relatorioConsulta!.nomePadraoRelatorio = this.nomePadrao;
      if (this.idPadrao == null) {
        this.relatorioConsulta!.idPadraoRelatorio = 0
      }
      else
        this.relatorioConsulta!.idPadraoRelatorio = this.idPadrao


      this.documentosService.RelatorioClinica(this.relatorioConsulta!).subscribe(result => {
        if (!result) {
          document.body.scrollTop = document.documentElement.scrollTop = 0;
          this.tradutor.get('TELARELATORIOS.PREENCHACAMPOSDATA').subscribe((res: string) => {
            ;
            this.snackBarAlert.falhaSnackbar(res)
            this.ngxSmartModalService.getModal('GeracaoEnviada').close()
          });
          return;
        }
        this.GerarExcel(result);
        this.ngxSmartModalService.getModal('GeracaoEnviada').close();
        this.spinner.hide();
      }, err => {
        this.ngxSmartModalService.getModal('GeracaoEnviada').close();
        this.snackBarAlert.falhaSnackbar(err)

        this.spinner.hide();
      })
      this.ngxSmartModalService.getModal('GeracaoEnviada').open();
      this.spinner.hide();



    } catch (error) {

    }
  }

  GerarExcel(dadosExcel: any) {
    this.tituloAba = [];
    this.elementosAba = [];

    this.tituloAba1 = [];
    this.tituloAba2 = [];
    this.tituloAba3 = [];

    this.abas = [this.tituloAba, this.elementosAba];
    this.abas1 = [this.tituloAba1];
    this.abas2 = [this.tituloAba2];
    this.abas3 = [this.tituloAba3];
    this.dadosExcelFor = []
    this.dadosExcel = dadosExcel;


    if (this.dadosExcel.nomeclinica != null && this.dadosExcel.nomeclinica != "") {
      this.tituloAba.push("CLÍNICA")
      this.elementosAba.push(this.dadosExcel.nomeclinica)
    }
    if (this.dadosExcel.email != null && this.dadosExcel.email != "") {
      this.tituloAba.push("EMAIL")
      this.elementosAba.push(this.dadosExcel.email)
    }
    if (this.dadosExcel.cnpj != null && this.dadosExcel.cnpj != "") {
      this.tituloAba.push("CNJP")
      this.dadosExcel.cnpj = this.dadosExcel.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5")
      this.elementosAba.push(this.dadosExcel.cnpj)
    }
    if (this.dadosExcel.uf != null && this.dadosExcel.uf != "") {
      this.tituloAba.push("UF")
      this.elementosAba.push(this.dadosExcel.uf)
    }
    if (this.dadosExcel.cidade != null && this.dadosExcel.cidade != "") {
      this.tituloAba.push("CIDADE")
      this.elementosAba.push(this.dadosExcel.cidade)
    }
    if (this.dadosExcel.bairro != null && this.dadosExcel.bairro != "") {
      this.tituloAba.push("BAIRRO")
      this.elementosAba.push(this.dadosExcel.bairro)
    }
    if (this.dadosExcel.dtalicenca != null && this.dadosExcel.dtalicenca != "") {
      this.tituloAba.push("DATA DA LICENÇA")
      this.elementosAba.push(new Date(this.dadosExcel.dtalicenca).toLocaleDateString())
    }
    if (this.dadosExcel.telcomercial != null && this.dadosExcel.telcomercial != "") {
      this.tituloAba.push("TELEFONE")

      this.dadosExcel.telcomercial = this.dadosExcel.telcomercial.replace(/\D/g, "");
      this.dadosExcel.telcomercial = this.dadosExcel.telcomercial.replace(/^(\d{2})(\d)/g, "($1) $2");
      this.dadosExcel.telcomercial = this.dadosExcel.telcomercial.replace(/(\d)(\d{4})$/, "$1-$2");

      this.elementosAba.push(this.dadosExcel.telcomercial)
    }
    if (this.dadosExcel.caracterizacao != null && this.dadosExcel.caracterizacao != "") {
      this.tituloAba.push("CARACTERIZAÇÃO")
      this.elementosAba.push(this.dadosExcel.caracterizacao)
    }

    //aba consulta
    if (this.validaConsultaSelecionada()) {
      if (this.dadosExcel.consultas.length > 0) {
        this.dadosExcel.consultas.forEach((element: any) => {
          var elemento: any = [];
          //valida os checkboxmarcados
          if (this.relatorioConsulta!.flgDtaConsultas) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("DATA DA CONSULTA")
            // elemento.push(element.dtaConsultas)
            elemento.push(new Date(element.dtaConsultas).toLocaleString())
          }
          if (this.relatorioConsulta!.flgPaciente) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("PACIENTE")
            elemento.push(element.paciente)
          }
          if (this.relatorioConsulta!.flgMedico) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("MÉDICO")
            elemento.push(element.medico)
          }
          if (this.relatorioConsulta!.flgTipoAgendamento) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("TIPO DE AGENDAMENTO")
            elemento.push(element.tipoAgendamento)
          }
          if (this.relatorioConsulta!.flgTempo) {

            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("TEMPO DA CONSULTA")
            //
            if (element.tempoConsulta != null && element.tempoConsulta != "") {
              elemento.push(element.tempoConsulta.substring(0, 8))
            }
            else
              elemento.push(element.tempoConsulta)
          }

          if (this.relatorioConsulta!.flgTipoPagamento) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("MODO DE PAGAMENTO")
            elemento.push(element.tipoPagamento)
          }
          if (this.relatorioConsulta!.flgConvenio) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("CONVÊNIO")
            elemento.push(element.convenio)
          }
          if (this.relatorioConsulta!.flgCodConvenio) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("CÓDIGO DO CONVÊNIO")
            elemento.push(element.codConvenio)
          }
          if (this.relatorioConsulta!.flgValorConsulta) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("VALOR DA CONSULTA")
            if (element.valorConsulta != null && element.valorConsulta != "") {
              if (element.valorConsulta) {
                element.valorConsulta = this.verificaCasaDecimal(element.valorConsulta)
                element.valorConsulta = this.aplicarMascaraValor(element.valorConsulta)
              }
              elemento.push("R$" + element.valorConsulta)
            }
            else
              elemento.push(element.valorConsulta)
          }
          if (this.relatorioConsulta!.flgRetorno) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("RETORNO")
            if (element.retorno) {
              elemento.push("Sim")
            } else { elemento.push("Não") }
          }

          if (this.relatorioConsulta!.flgCid) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("CID")
            // if (element.codCid == "")
            elemento.push(element.codCid)
            // else
            //   elemento.push(element.codCid + ':' + element.desCid)
          }
          if (this.relatorioConsulta!.flgStatus) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("STATUS DA CONSULTA")
            elemento.push(element.status)
          }
          if (this.relatorioConsulta!.flgObservacaoCancelamento) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("OBSERVAÇÕES DO CANCELAMENTO")
            elemento.push(element.obsCancelamento)
          }
          if (this.relatorioConsulta!.flgObservacaoAvaliacao) {
            if (dadosExcel.consultas[0] == element)
              this.tituloAba1.push("OBSERVAÇÕES DE AVALIAÇÃO")
            elemento.push(element.objAvaliacao)
          }

          this.abas1.push(elemento);
        })

      }
    }

    if (this.validaCadastrosMedicoSelecionado()) {
      if (this.dadosExcel.usuariosMedicos.length > 0) {
        this.dadosExcel.usuariosMedicos.forEach((element: any) => {
          var elemento: any = [];
          if (this.relatorioCadastrosMedico!.flgNome) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("NOME")
            elemento.push(element.nome)
          }
          if (this.relatorioCadastrosMedico!.flgEmail) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("EMAIL")
            elemento.push(element.email)
          }
          if (this.relatorioCadastrosMedico!.flgDtanascimento) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("DATA DE NASCIMENTO")
            elemento.push(new Date(element.dtanascimento).toLocaleDateString())
          }
          if (this.relatorioCadastrosMedico!.flgDtaCadastro) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("DATA DE CADASTRO")
            elemento.push(new Date(element.dtaCadastro).toLocaleString())
          }
          if (this.relatorioCadastrosMedico!.flgSexo) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("SEXO")
            elemento.push(element.sexo)
          }
          if (this.relatorioCadastrosMedico!.flgCpf) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("CPF")
            if (element.cpf != null && element.cpf != "") {
              element.cpf = element.cpf.replace(/\D/g, "");
              element.cpf = element.cpf.replace(/(\d{3})(\d)/, "$1.$2");
              element.cpf = element.cpf.replace(/(\d{3})(\d)/, "$1.$2");
              element.cpf = element.cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
            }
            elemento.push(element.cpf)
          }
          if (this.relatorioCadastrosMedico!.flgTel) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("TELEFONE")

            if (element.tel != null && element.tel != "") {
              element.tel = element.tel.replace(/\D/g, "");
              element.tel = element.tel.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.tel = element.tel.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            elemento.push(element.tel)
          }
          if (this.relatorioCadastrosMedico!.flgTelmovel) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("TELEFONE MOVEL")
            if (element.telmovel != null && element.telmovel != "") {
              element.telmovel = element.telmovel.replace(/\D/g, "");
              element.telmovel = element.telmovel.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.telmovel = element.telmovel.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            elemento.push(element.telmovel)
          }
          if (this.relatorioCadastrosMedico!.flgTelComercial) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("TELEFONE COMERCIAL")
            if (element.telComercial != null && element.telComercial != "") {
              element.telComercial = element.telComercial.replace(/\D/g, "");
              element.telComercial = element.telComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.telComercial = element.telComercial.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            elemento.push(element.telComercial)
          }
          if (this.relatorioCadastrosMedico!.flgCRM) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("CRM")
            elemento.push(element.crm)
          }
          if (this.relatorioCadastrosMedico!.flgRua) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("RUA")
            elemento.push(element.rua)
          }
          if (this.relatorioCadastrosMedico!.flgNumero) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("N°")
            elemento.push(element.numero)
          }
          if (this.relatorioCadastrosMedico!.flgComplement) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("COMPLEMENTO")
            elemento.push(element.complement)
          }
          if (this.relatorioCadastrosMedico!.flgUf) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("UF")
            elemento.push(element.uf)
          }
          if (this.relatorioCadastrosMedico!.flgCidade) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("CIDADE")
            elemento.push(element.cidade)
          }
          if (this.relatorioCadastrosMedico!.flgBairro) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("BAIRRO")
            elemento.push(element.bairro)
          }
          if (this.relatorioCadastrosMedico!.flgCep) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("CEP")
            if (element.cep != null && element.cep != "") {
              element.cep = element.cep.replace(/\D/g, "");
              element.cep = element.cep.replace(/(\d)(\d{3})$/, "$1-$2");
            }
            elemento.push(element.cep)
          }
          if (this.relatorioCadastrosMedico!.flgValorConsulta) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("VALOR DA CONSULTA")
            if (element.valorConsulta != null && element.valorConsulta != "") {

              if (element.valorConsulta) {
                element.valorConsulta = this.verificaCasaDecimal(element.valorConsulta)
                element.valorConsulta = this.aplicarMascaraValor(element.valorConsulta)
              }

              elemento.push("R$" + element.valorConsulta)
            }
            else
              elemento.push(element.valorConsulta)
          }
          if (this.relatorioCadastrosMedico!.flgCodConvenio) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("CÓDIGO DO CONVÊNIO")

            elemento.push(element.codConvenio)
          }
          if (this.relatorioCadastrosMedico!.flgvlRetidoClinica) {
            if (dadosExcel.usuariosMedicos[0] == element)
              this.tituloAba2.push("VALOR RETIDO")
            if (element.vlRetidoClinica != null && element.vlRetidoClinica != "") {
              elemento.push("R$" + element.vlRetidoClinica)
            }
            else
              elemento.push(element.vlRetidoClinica)
          }

          this.abas2.push(elemento)
        })
      }
    }

    if (this.validaCadastrosPacienteSelecionado()) {
      if (this.dadosExcel.usuariosPacientes.length > 0) {
        this.dadosExcel.usuariosPacientes.forEach((element: any) => {
          var elemento: any = [];
          if (this.relatorioCadastrosPaciente!.flgNome) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("NOME")
            elemento.push(element.nome)
          }
          if (this.relatorioCadastrosPaciente!.flgEmail) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("EMAIL")
            elemento.push(element.email)
          }
          if (this.relatorioCadastrosPaciente!.flgDtanascimento) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("DATA DE NASCIMENTO")
            elemento.push(new Date(element.dtanascimento).toLocaleDateString())
          }
          if (this.relatorioCadastrosPaciente!.flgDtaCadastro) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("DATA DE CADASTRO")
            elemento.push(new Date(element.dtaCadastro).toLocaleString())
          }
          if (this.relatorioCadastrosPaciente!.flgSexo) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("SEXO")
            elemento.push(element.sexo)
          }
          if (this.relatorioCadastrosPaciente!.flgCpf) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("CPF")
            if (element.cpf != null && element.cpf != "") {
              element.cpf = element.cpf.replace(/\D/g, "");
              element.cpf = element.cpf.replace(/(\d{3})(\d)/, "$1.$2");
              element.cpf = element.cpf.replace(/(\d{3})(\d)/, "$1.$2");
              element.cpf = element.cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
            }
            elemento.push(element.cpf)
          }
          if (this.relatorioCadastrosPaciente!.flgTel) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("TELEFONE")
            if (element.tel != null && element.tel != "") {
              element.tel = element.tel.replace(/\D/g, "");
              element.tel = element.tel.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.tel = element.tel.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            elemento.push(element.tel)
          }
          if (this.relatorioCadastrosPaciente!.flgTelmovel) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("TELEFONE MOVEL")
            if (element.telmovel != null && element.telmovel != "") {
              element.telmovel = element.telmovel.replace(/\D/g, "");
              element.telmovel = element.telmovel.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.telmovel = element.telmovel.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            elemento.push(element.telmovel)
          }
          if (this.relatorioCadastrosPaciente!.flgTelComercial) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("TELEFONE COMERCIAL")
            if (element.telComercial != null && element.telComercial != "") {
              element.telComercial = element.telComercial.replace(/\D/g, "");
              element.telComercial = element.telComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
              element.telComercial = element.telComercial.replace(/(\d)(\d{4})$/, "$1-$2");
            }
            elemento.push(element.telComercial)
          }
          if (this.relatorioCadastrosPaciente!.flgRua) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("RUA")
            elemento.push(element.rua)
          }
          if (this.relatorioCadastrosPaciente!.flgNumero) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("N°")
            elemento.push(element.numero)
          }
          if (this.relatorioCadastrosPaciente!.flgComplement) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("COMPLEMENTO")
            elemento.push(element.complement)
          }
          if (this.relatorioCadastrosPaciente!.flgUf) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("UF")
            elemento.push(element.uf)
          }
          if (this.relatorioCadastrosPaciente!.flgCidade) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("CIDADE")
            elemento.push(element.cidade)
          }
          if (this.relatorioCadastrosPaciente!.flgBairro) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("BAIRRO")
            elemento.push(element.bairro)
          }
          if (this.relatorioCadastrosPaciente!.flgCep) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("CEP")
            if (element.cep != null && element.cep != "") {
              element.cep = element.cep.replace(/\D/g, "");
              element.cep = element.cep.replace(/(\d)(\d{3})$/, "$1-$2");
            }
            elemento.push(element.cep)
          }
          if (this.relatorioCadastrosPaciente!.flgContaAtiva) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("CONTA ATIVA")
            elemento.push(element.contaAtiva)
          }
          if (this.relatorioCadastrosPaciente!.flgObsInativacao) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("MOTIVO DA INATIVAÇÃO")
            elemento.push(element.obsInativacao)
          }
          if (this.relatorioCadastrosPaciente!.flgNaturalidade) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("NATURALIDADE")
            elemento.push(element.naturalidade)
          }
          if (this.relatorioCadastrosPaciente!.flgNascionalidade) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("NASCIONALIDADE")
            elemento.push(element.nascionalidade)
          }
          if (this.relatorioCadastrosPaciente!.flgProcedencia) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("PROCEDÊNCIA")
            elemento.push(element.procedencia)
          }
          if (this.relatorioCadastrosPaciente!.flgProfissao) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("PROFISSÃO")
            elemento.push(element.profissao)
          }
          if (this.relatorioCadastrosPaciente!.flgPlanoSaude) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("PLANO DE SAÚDE")
            elemento.push(element.planoSaude)
          }
          if (this.relatorioCadastrosPaciente!.flgConvenio) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("CONVÊNIO")
            elemento.push(element.convenio)
          }
          if (this.relatorioCadastrosPaciente!.flgMatricula) {
            if (dadosExcel.usuariosPacientes[0] == element)
              this.tituloAba3.push("MATRÍCULA")
            elemento.push(element.matricula)
          }
          this.abas3.push(elemento)
        })
      }
    }

    if (this.abas.length == 0) return;
    var ws = XLSX.utils.aoa_to_sheet(this.abas);
    var wb = XLSX.utils.book_new();

    XLSX.utils.book_append_sheet(wb, ws, "CLINICA");

    if (this.abas1[0].length > 0) {
      var ws2 = XLSX.utils.aoa_to_sheet(this.abas1);

      XLSX.utils.book_append_sheet(wb, ws2, "CONSULTAS");

    }

    if (this.abas2[0].length > 0) {
      var ws2 = XLSX.utils.aoa_to_sheet(this.abas2);
      XLSX.utils.book_append_sheet(wb, ws2, "MÉDICOS");
    }

    if (this.abas3[0].length > 0) {
      var wsConsulta = XLSX.utils.aoa_to_sheet(this.abas3);
      XLSX.utils.book_append_sheet(wb, wsConsulta, "PACIENTES");
    }

    var wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

    function s2ab(s: any) {
      var buf = new ArrayBuffer(s.length);
      var view = new Uint8Array(buf);
      for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
      return buf;
    }

    saveAs(new Blob([s2ab(wbout)], { type: "application/octet-stream" }), this.dadosExcel.nomeclinica + ".xlsx");
  }

  CarregarTodosPadroes() {
    this.documentosService.GetTodosPadroesRelatorios(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      this.listaTodosPadroes = retorno
      this.CarregaPadrao(this.listaTodosPadroes[0].idPadraoRelatorioUsuario);
      this.spinner.hide();
    })
  }

  BotaoSalvarPadrao() {
    if (this.idPadrao! > 0)
      this.ngxSmartModalService.getModal('EdicaoRelatorio').open()
    else {
      this.SalvarPadrao()
      this.ngxSmartModalService.getModal('EdicaoRelatorio').close()
    }
  }


  ModalSalvarPadrao() {
    this.idPadrao = null
    this.nomePadrao = null
    this.snackBarAlert.falhaSnackbar("Informe o nome para o novo padrão")
    this.ngxSmartModalService.getModal('EdicaoRelatorio').close()

  }

  SalvarPadrao() {
    if (this.nomePadrao == undefined || !this.nomePadrao.trim()) {
      this.snackBarAlert.falhaSnackbar("Para salvar um padrão, informe um nome!")
      return;
    }


    this.LimpaCheckboxDesmarcada();

    // if (this.CadastrosMedicos)
    this.relatorioConsulta!.Medico = this.relatorioCadastrosMedico;
    // if (this.CadastrosP!acientes)
    this.relatorioConsulta!.Paciente = this.relatorioCadastrosPaciente;
    this.relatorioConsulta!.FlgCheckConsultas = this.Consultas;
    this.relatorioConsulta!.FlgCheckMedico = this.CadastrosMedicos;
    this.relatorioConsulta!.FlgCheckPaciente = this.CadastrosPacientes;

    this.relatorioConsulta!.idClinica = this.usuarioLogadoService.getIdUltimaClinica()
    this.relatorioConsulta!.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso()


    this.relatorioConsulta!.flgStatus = this.relatorioConsulta!.flgStatus;
    this.relatorioConsulta!.flgObservacaoCancelamento = this.relatorioConsulta!.flgObservacaoCancelamento;
    this.relatorioConsulta!.flgObservacaoAvaliacao = this.relatorioConsulta!.flgObservacaoAvaliacao;
    this.relatorioConsulta!.nomePadraoRelatorio = this.nomePadrao

    if (this.idPadrao == null) {
      this.relatorioConsulta!.idPadraoRelatorio = 0
    }
    else
      this.relatorioConsulta!.idPadraoRelatorio = this.idPadrao


    this.documentosService.SalvarPadraoRelatorio(this.relatorioConsulta!).subscribe((retorno) => {

      if (retorno == true) {
        this.snackBarAlert.sucessoSnackbar("Padrão salvo!")
        this.CarregarTodosPadroes();

        if (this.idPadrao == null) {
          var padrao = this.listaTodosPadroes.filter((c: any) => c.nomePadraoRelatorio == this.nomePadrao)
            ;

          this.CarregaPadrao(padrao.idPadraoRelatorioUsuario);
        }

      }

      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  EditarPadrao(idPadrao: any) {

    this.CarregaPadrao(idPadrao);

    document.body.scrollTop = document.documentElement.scrollTop = 0;
    (document.getElementById("matCardCheckbox") as HTMLInputElement).scrollTop = 0

  }

  AbrirModalExcluir(idPadrao: any) {
    this.idPadraoExclusao = idPadrao
    this.ngxSmartModalService.getModal('ExcluirPadraoRelatorio').open()

  }

  ExcluirPadrao() {
    this.documentosService.InativarPadraoRelatorio(this.idPadraoExclusao).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar("Padrão Excluido!")
      this.ngxSmartModalService.getModal('ExcluirPadraoRelatorio').close()
      this.CarregarTodosPadroes();
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  NovoPadrao() {
    this.idPadrao = null
    this.nomePadrao = ""
    this.Consultas = false
    this.CadastrosMedicos = false
    this.CadastrosPacientes = false
    this.LimpaCheckboxDesmarcada()
    this.Consultas = true
    this.CadastrosMedicos = true
    this.CadastrosPacientes = true

    document.body.scrollTop = document.documentElement.scrollTop = 0;
    (document.getElementById("matCardCheckbox") as HTMLInputElement).scrollTop = 0
  }

  LimpaCheckboxDesmarcada() {
    const resetarPropriedades = (obj: any) => {
      if (!obj) return;

      Object.keys(obj)
        .filter(key => key.startsWith('flg'))
        .forEach(key => obj[key] = false);
    };

    if (!this.Consultas)
      resetarPropriedades(this.relatorioConsulta);

    if (!this.CadastrosMedicos)
      resetarPropriedades(this.relatorioCadastrosMedico);

    if (!this.CadastrosPacientes)
      resetarPropriedades(this.relatorioCadastrosPaciente);
  }

  aplicarMascaraValor(v: any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  verificaCasaDecimal(valor: any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }
  abrirModalOpcoes() {
    this.ngxSmartModalService.getModal('modalOpcoes').open();
  }

  hasSelectedCheckbox(): boolean {
    // Verificar se algum checkbox em relatorioConsulta está marcado
    const hasConsultaSelected = this.validaConsultaSelecionada();

    // Verificar se algum checkbox em relatorioCadastrosMedico está marcado
    const hasMedicoSelected = this.validaCadastrosMedicoSelecionado();

    // Verificar se algum checkbox em relatorioCadastrosPaciente está marcado
    const hasPacienteSelected = this.validaCadastrosPacienteSelecionado();

    // Retorna true se pelo menos uma das seções tiver algum checkbox marcado
    return hasConsultaSelected || hasMedicoSelected || hasPacienteSelected;
  }

  validaConsultaSelecionada(): boolean {
    return Object.keys(this.relatorioConsulta as object)
      .filter(key => key.startsWith('flg'))
      .some(key => (this.relatorioConsulta as any)[key] === true);
  }
  validaCadastrosMedicoSelecionado(): boolean {
    return Object.keys(this.relatorioCadastrosMedico as object)
      .filter(key => key.startsWith('flg'))
      .some(key => (this.relatorioCadastrosMedico as any)[key] === true);
  }
  validaCadastrosPacienteSelecionado(): boolean {
    return Object.keys(this.relatorioCadastrosPaciente as object)
      .filter(key => key.startsWith('flg'))
      .some(key => (this.relatorioCadastrosPaciente as any)[key] === true);
  }

  private setAllFlags(obj: any, value: boolean): void {
    Object.keys(obj).forEach(key => {
      if (key.startsWith('flg')) {
        obj[key] = value;
      }
    });
  }

  private areAllFlagsTrue(obj: any): boolean {
    return Object.keys(obj)
      .filter(key => key.startsWith('flg'))
      .every(key => obj[key] === true);
  }

  checkItens() {
    this.checkItemConsultasChange();
    this.checkItemCadastrosMedicos();
    this.checkItemCadastroPaciente();
  }

  checkConsultasChange(): void {
    this.setAllFlags(this.relatorioConsulta, this.Consultas);
  }

  checkItemConsultasChange(): void {
    this.Consultas = this.areAllFlagsTrue(this.relatorioConsulta);
  }

  checkCadastrosMedicos(): void {
    this.setAllFlags(this.relatorioCadastrosMedico, this.CadastrosMedicos);
  }

  checkItemCadastrosMedicos(): void {
    this.CadastrosMedicos = this.areAllFlagsTrue(this.relatorioCadastrosMedico);
  }

  checkCadastrosPacientes(): void {
    this.setAllFlags(this.relatorioCadastrosPaciente, this.CadastrosPacientes);
  }

  checkItemCadastroPaciente(): void {
    this.CadastrosPacientes = this.areAllFlagsTrue(this.relatorioCadastrosPaciente);
  }

  strDtInicio = "";
  strDtFim = "";

  dtInicio? = new Date();
  dtFim = new Date();

  flgDiferencaDatas: boolean = false;
  flgDtInicioNaoPreenchida: boolean = false;
  flgDtFimNaoPreenchida: boolean = false;
  flgDtFimMaiorQueInicio: boolean = false;

  dtChangeInicio() {
    this.dtInicio = parseDateString(this.strDtInicio)!;
    this.flgDtInicioNaoPreenchida = !this.dtInicio;
    this.validaDatas();
  }

  dtChangeFim() {
    this.dtFim = parseDateString(this.strDtFim)!;
    this.flgDtFimNaoPreenchida = !this.dtFim;
    this.validaDatas();
  }


  validaDatas() {
    const validationResult = validateDates(this.dtInicio!, this.dtFim, 48);

    switch (validationResult) {
      case DateValidationResult.DtInicio_Nao_Preenchida:
        console.warn('Data Inicial não preenchida.');
        this.flgDtInicioNaoPreenchida = true;
        break;
      case DateValidationResult.DtFim_Nao_Preenchida:
        console.warn('Data Final não preenchida.');
        this.flgDtFimNaoPreenchida = true;
        break;
      case DateValidationResult.DtInicio_Maior_DtFim:
        console.error('Data Inicial não pode ser maior que a Data Final.');
        this.flgDtFimMaiorQueInicio = true;
        break;
      case DateValidationResult.OK:
        this.flgDiferencaDatas = false;
        this.flgDtFimMaiorQueInicio = false;
        break;
    }
  }
}
