// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$success-color: #4CAF50;        // Verde para sucesso
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}

.sala-container {
  min-height: 100vh;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Cards
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: none;
  margin-bottom: 24px;
  overflow: hidden;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
  height: 26vh !important;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// Headers
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

// Sections
.section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
}

// Form
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-group {
  padding: 0 8px;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.col-md-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-10 {
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.full-width {
  width: 100%;
}

// Material overrides
:host ::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-label {
    color: $text-secondary;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
    color: $primary-color;
  }
  
  .mat-input-element {
    color: $text-primary;
  }
  
  .mat-form-field-subscript-wrapper {
    overflow: visible;
  }
  
  .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.8em 0;
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary;
  
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background-color: rgba($primary-color, 0.05);
  }
}

.btn-success {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-link {
  background: none;
  color: $primary-color;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .form-row {
    margin: 0 -4px 12px;
  }
  
  .form-group {
    padding: 0 4px;
    margin-bottom: 12px;
  }
  
  .card-footer {
    flex-wrap: wrap;
    
    .btn {
      flex: 1;
    }
  }
}