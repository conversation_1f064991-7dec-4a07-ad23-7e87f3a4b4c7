import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Clinica } from '../model/clinica';
import { Observable } from 'rxjs';
import { Atendente } from '../model/atendente';
import { SpinnerService } from './spinner.service';
// import { Endereco } from '../model/endereco';

@Injectable({
    providedIn: 'root'
})
export class ClinicaService {

    public changeImage$: EventEmitter<any>;

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {

        this.changeImage$ = new EventEmitter();
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getTabelaClinica(inicio:any, fim:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));


        return this.http.get(environment.apiEndpoint + '/Clinica/GetTabClinica', { params });
        // return this.http.get(environment.apiEndpoint + '/Clinica')
        //     .toPromise();
    }



    public CarregarClinica(id:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/' + id)
            .toPromise();
    }

    public EnviarArrayEmailImobiliaria(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/EmailClinicasImobiliaria/' + id);
    }

    public EnviarEmaiClinica(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/EnviarEmaiClinica/' + id);
    }


    public CarregarPerfil(id:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/CarregaPerfilClinicaLogado/' + id)
            .toPromise();
    }

    public salvarClinica(clinica: Clinica): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Clinica/', clinica);
    }


    public salvarAtendente(atendente: Atendente): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Clinica/', atendente);
    }

    public salvarClinicaImobiliaria(clinica: Clinica): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Clinica/postClinicaImobiliaria', clinica);
    }




    // public AtualizarPerfil(clinica: Clinica): Observable<any> {

    //     return this.http.post(environment.apiEndpoint + '/Clinica/AtualizaSenha/' ,clinica);     
    //     }

    public ValidarCNPJ(value: string, idClinica: number): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('value', String(value));
        params = params.append('idClinica', String(idClinica));

        return this.http.get(environment.apiEndpoint + '/Clinica/ValidarCNPJ', { params });
    }




    public TrocarClinica(id: number, idClinica: number): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('id', String(id));
        params = params.append('idClinica', String(idClinica));

        return this.http.get(environment.apiEndpoint + '/Clinica/TrocarClinica', { params });
    }



    public CarregaTipoClinica(): Observable<any> {
        return this.http.get(environment.apiEndpoint + '/Clinica/TipoClinica');
    }



    public getGridClinicaInativos(inicio:any, fim:any, pesquisa:any): Observable<any> {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        return this.http.get(environment.apiEndpoint + '/Clinica/getGridClinicaInativos', { params });
    }

    public getGridClinica(inicio:any, fim:any, pesquisa:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        return this.http.get(environment.apiEndpoint + '/Clinica/getGridClinica', { params });
    }
    public getClinicas(idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/getClinicas/' + idUsuario);
    }

    public getClinicaEdit(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/GetClinicaEdcao/' + id);
    }
    public inativarClinica(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Clinica/InativarClinica/' + id + '/' + idUsuario);
    }

    public AtivarClinica(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Clinica/AtivarClinica/' + id + '/' + idUsuario);
    }

    public getPagamentoClinica(idClinica:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Clinica/getPagamentoClinica/' + idClinica);
    }


    
    public SalvarPerfilClinica(clinica: Clinica): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Clinica/SalvarPerfilClinica', clinica);
    }
}


