import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TabLocal } from '../model/local';
import { LocalService } from '../service/local.service';
import { msgResposta } from '../model/retorno-resposta';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { ModalEnviarLocalPacienteComponent } from '../Modais/modal-enviar-local-paciente/modal-enviar-local-paciente.component';
import { ControleModaisService } from '../service/controle-modais.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';


@Component({
    selector: 'app-cadastro-locais',
    templateUrl: './cadastro-locais.component.html',
    styleUrls: ['./cadastro-locais.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCard,
      MatFormFieldModule,
      MatIcon,
      TranslateModule
    ]
})
export class CadastroLocaisComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private LocalService: LocalService,
    private localStorageService: LocalStorageService,
    private router: Router,
    private modalService: ControleModaisService
  ) { }

  idLocal!: number;
  retorno!: msgResposta;
  listaLocais: TabLocal[] = [];
  flgmodal: boolean = false;
  flginativos: boolean = false;
  flgFoto: boolean = false;
  ImagemPessoa: any = "assets/build/img/userdefault.png";


  ngOnInit() {
    this.GetLocais();
  }
  GetLocais() {
    this.LocalService.GetLocais().subscribe((retorno) => {
      this.listaLocais = retorno;
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  excluirLocal(idLocal: number) {
    this.idLocal = idLocal;
    this.flgmodal = true;
  }

  Excluir(selecao: boolean) {
    if (selecao) {
      this.LocalService.deleteLocal(this.idLocal).subscribe((retorno) => {
        this.retorno = retorno;
        this.GetLocais();
        this.spinner.hide();

      }, err => {
        console.error(err)
        this.spinner.hide();
      })
      this.flgmodal = false
    }
    else {
      this.flgmodal = false
      this.spinner.hide();
    }

  }

  Editar(idLocal: number) {
    this.localStorageService.idLocal = idLocal;
    this.adicionarlocais()
  }

  adicionarlocais() {
    this.router.navigate(['/adicionarlocais']);
  }


  filtroBusca: string = '';

  filtrarLocais() {
    if (this.filtroBusca.trim() !== '') {
      this.listaLocais = this.listaLocais.filter(local => {
        return (
          local.nome!.toLowerCase().includes(this.filtroBusca.toLowerCase()) ||
          local.telefone!.toLowerCase().includes(this.filtroBusca.toLowerCase()) ||
          local.email!.toLowerCase().includes(this.filtroBusca.toLowerCase())
        );
      });
    } else {
      this.GetLocais();
    }
  }

  AbrirModalListaPacientes(idLocal: number){
    this.modalService.AbrirModalComponente(ModalEnviarLocalPacienteComponent, {titulo: 'Enviar mensagem', idLocal: idLocal})
  }
}
