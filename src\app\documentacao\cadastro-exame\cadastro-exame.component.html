
<ngx-smart-modal #examesModal identifier="examesModal" [dismissable]="false"
    customClass="nsm-centered receit medium-modal form-modal emailmodal" style="padding: 0 !important;">
    <!-- <div class="col-md-6 background-Iniciar" style="display: flex; height: 100px; width: 515px !important"> -->
    <!-- <div class="modal-info" style="margin-top: 48px;">
            <b> {{ 'TELADOCUMENTACAO.DADOS' | translate }} {{DadosInformUsuario.tipoUsuario}}</b><br>
        </div> -->
    <!-- <mat-icon style="color: white;
    font-size: 88px;
    margin-left: 35%;"> perm_identity</mat-icon>

    </div> -->
  <div class="col-md-12">
    <div class="col-md-12 col-sm-12" style="margin: 0 auto; justify-content: center; text-align: center;  margin-top: 5%; display: block;">
        <h4 class="little-title fw-700" style="text-align: center;">
          GUIA DE SERVIÇO </h4>
          <p class="sub-title-exames">Para salvar os resultados de Exame!</p>
    </div>

    <hr class="sep-1" />

    <div class="modal-info">

      <div style="max-height:300px ">
        <div class="col-md-12 col-sm-12 row" style="margin: 0px;">

          <div class="col-md-6 col-sm-6 col-xs-6 ">
            <ng-select [items]="DadosPacientes" style="font-size: 12px;" placeholder="Paciente"
              bindLabel="nome" bindValue="idCliente" name="medicos" [selectOnTab]="true" [(ngModel)]="objExames.IdPaciente"
              aria-required="true">
            </ng-select>
            <mat-error *ngIf="NomePaciente == true" style="font-size: 65%;color: #f44336;font-weight: 600;top: 37px;
              position: absolute; left: 3%;">{{getErrorMessageNomeProSolicitante() | translate }}
            </mat-error>
          </div>

          <div class="col-md-6 col-sm-6 col-xs-6 ">
            <ng-select [items]="DadosExame" style="font-size: 12px;" placeholder="Exame" bindLabel="desExame"
              bindValue="idExameClinica" name="medicos" [selectOnTab]="true" [(ngModel)]="objExames.IdExameClinica"
              aria-required="true">
            </ng-select>
            <mat-error *ngIf="NomePaciente == true" style="font-size: 65%;color: #f44336;font-weight: 600;top: 37px;
              position: absolute; left: 3%;">{{getErrorMessageNomeProSolicitante() | translate }}
            </mat-error>
          </div>


          <mat-form-field class="col-md-12 col-sm-12">
            <input matInput maxlength="35" name="CodigoCnes15" autocomplete="off" placeholder="Resultado"
              [(ngModel)]="objExames.DesResultado">
              <!-- <mat-error *ngIf="CodCNES.invalid">{{getErrorMessageCodCNES() | translate }}</mat-error> -->
          </mat-form-field>




          <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right" style="justify-content: center; text-align: center; padding: 0;">
            <button class="btn-primary " mat-raised-button style="color:white;" (click)="LimparCampos()"
              style="margin-right: 2%;">
              <mat-icon>clear</mat-icon> <span class="legenda">{{ 'TELACADASTROMEDICO.LIMPAR' | translate }}</span>
            </button>
            <button class="btn-primary " mat-raised-button
              style="color:white;margin-top: 10px;margin-bottom: 10px;" (click)="Submit()">
              <mat-icon>save</mat-icon><span class="legenda">{{ 'TELACADASTROMEDICO.SALVAR' | translate }}</span>
            </button>
          </div>

        </div>
      </div>
    </div>

    <div class="modal-exames-logo">
    </div>

  </div>  
</ngx-smart-modal>
