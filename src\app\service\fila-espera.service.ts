import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FilaEsperaService {
  
  constructor(private http: HttpClient) { }

  adicionarPaciente(nomePaciente: string, idadePaciente: number, token?: string): Observable<any> {
    const request = { NomePaciente: nomePaciente, IdadePaciente: idadePaciente, Token: token };
    return this.http.post(environment.apiEndpoint + '/fila-espera/adicionar-paciente', request);
  }

  consultarPosicao(token: string): Observable<any> {
    return this.http.get(environment.apiEndpoint + `/fila-espera/consultar-posicao/${token}`);
  }

  removerPaciente(token: string): Observable<any> {
    return this.http.delete(environment.apiEndpoint + `/fila-espera/remover-paciente/${token}`);
  }

  listarPacientes(): Observable<any> {
    return this.http.get(environment.apiEndpoint + '/fila-espera/listar-pacientes');
  }

  obterPaciente(token: string): Observable<any> {
    return this.http.get(environment.apiEndpoint + `/fila-espera/paciente/${token}`);
  }

  chamarProximoPaciente(): Observable<any> {
    return this.http.post(environment.apiEndpoint + '/fila-espera/chamar-proximo', {});
  }

  limparFila(): Observable<any> {
    return this.http.delete(environment.apiEndpoint + '/fila-espera/limpar-fila');
  }
} 
