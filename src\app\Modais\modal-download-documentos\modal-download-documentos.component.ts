import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { ObjDownloadFileModal } from 'src/app/model/arquivo';
import { ControleModaisService } from 'src/app/service/controle-modais.service';

@Component({
    selector: 'app-modal-download-documentos',
    templateUrl: './modal-download-documentos.component.html',
    styleUrls: ['./modal-download-documentos.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      MatIcon,
      MatDialogModule,
      CommonModule,
      FormsModule
    ]
})
export class ModalDownloadDocumentosComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ModalDownloadDocumentosComponent>,
    public controleModas: ControleModaisService
  ) { }

  objArquivos: ObjDownloadFileModal = new ObjDownloadFileModal() ;
  pdfArquivo: string = "";

  ngOnInit(): void {
    this.objArquivos = this.controleModas.objDownloadFileModal!;
  }

  downloadPdf() {
    const source = `data:application/${this.objArquivos.FileExtension};base64,${this.objArquivos.Base64File}`;
    const link = document.createElement('a');

    link.href = source;
    link.download = this.objArquivos.FileName + "." + this.objArquivos.FileExtension;
    link.click();
  }


  FecharModal(){
      this.dialogRef.close();
  }
}
