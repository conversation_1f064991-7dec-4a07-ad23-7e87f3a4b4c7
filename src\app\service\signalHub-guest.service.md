# SignalHubGuestService - Documentação

## Visão Geral

O `SignalHubGuestService` é um serviço alternativo ao `SignalHubService` projetado especificamente para usuários **não autenticados** (guests) que precisam se conectar ao SignalR da API.

## Principais Diferenças

### SignalHubService (Usuários Logados)
- <PERSON>quer autenticação via `UsuarioLogadoService`
- Envia dados completos do usuário (ID, clínica, tipo, token)
- Verifica se o usuário está logado antes de conectar

### SignalHubGuestService (Usuários Deslogados)
- **Não requer autenticação**
- Usa token de conexão temporário
- Conecta automaticamente sem verificação de login
- Focado em eventos de fila de espera

## Como Usar

### 1. Injeção de Dependência

```typescript
import { SignalHubGuestService } from 'src/app/service/signalHub-guest.service';

constructor(
  private signalHubGuestService: SignalHubGuestService
) { }
```

### 2. Conectar Usuário Guest

```typescript
// Após obter o token de conexão temporário
const tokenConexao = 'token_temporario_do_usuario';
this.signalHubGuestService.connectGuestUser(tokenConexao);
```

### 3. Escutar Eventos

```typescript
// Escutar atualizações da fila
this.signalHubGuestService.OnAtualizaChamaPacienteFila
  .subscribe(() => {
    // Recalcular posição na fila
    this.buscarPosicaoAtual();
  });

// Escutar convites para reunião
this.signalHubGuestService.OnConviteReuniao
  .subscribe((convite: any) => {
    if (convite.token === this.tokenConexao) {
      // Usuário foi convidado para reunião
      this.iniciarConsulta();
    }
  });
```

### 4. Desconectar

```typescript
ngOnDestroy() {
  // Desconectar usuário guest
  this.signalHubGuestService.disconnectGuestUser();
  
  // Limpar subscriptions
  this.subscriptions.forEach(sub => sub.unsubscribe());
}
```

## Eventos Disponíveis

### Para Usuários Deslogados

| Evento | Descrição | Uso |
|--------|-----------|-----|
| `OnAtualizaChamaPacienteFila` | Fila foi atualizada | Recalcular posição |
| `OnChamaPacienteFila` | Paciente foi chamado | Verificar se é o usuário atual |
| `OnConviteReuniao` | Convite para reunião | Iniciar consulta |
| `OnDisparaAlertFilaClinica` | Alerta da clínica | Notificações gerais |
| `OnFinalizaConsulta` | Consulta finalizada | Limpeza/redirecionamento |

## Métodos Principais

### `connectGuestUser(token: string)`
Conecta um usuário guest usando token temporário.

### `disconnectGuestUser()`
Desconecta o usuário guest atual.

### `setTokenConexao(token: string)`
Define o token de conexão (usado internamente).

### `isHubConnected(): boolean`
Verifica se a conexão SignalR está ativa.

### `enviaServer(metodo: string, ...args: any[])`
Envia dados para o servidor via SignalR.

## Exemplo Completo

```typescript
import { Component, OnInit, OnDestroy } from '@angular/core';
import { SignalHubGuestService } from 'src/app/service/signalHub-guest.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-exemplo-guest',
  templateUrl: './exemplo-guest.component.html'
})
export class ExemploGuestComponent implements OnInit, OnDestroy {
  private subscriptions: Subscription[] = [];
  private tokenConexao: string = 'meu_token_temporario';

  constructor(
    private signalHubGuestService: SignalHubGuestService
  ) { }

  ngOnInit() {
    this.configurarSignalR();
    this.signalHubGuestService.connectGuestUser(this.tokenConexao);
  }

  private configurarSignalR() {
    const sub1 = this.signalHubGuestService.OnAtualizaChamaPacienteFila
      .subscribe(() => {
        console.log('Fila atualizada!');
        this.atualizarPosicao();
      });

    const sub2 = this.signalHubGuestService.OnConviteReuniao
      .subscribe((convite: any) => {
        if (convite.token === this.tokenConexao) {
          console.log('Convite recebido!');
          this.iniciarReuniao();
        }
      });

    this.subscriptions.push(sub1, sub2);
  }

  ngOnDestroy() {
    this.signalHubGuestService.disconnectGuestUser();
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private atualizarPosicao() {
    // Implementar lógica de atualização
  }

  private iniciarReuniao() {
    // Implementar lógica de início da reunião
  }
}
```

## Vantagens

1. **Independente de autenticação** - Funciona para usuários não logados
2. **Conexão automática** - Não verifica status de login
3. **Focado em guests** - Eventos específicos para usuários temporários
4. **Gerenciamento de token** - Controle do token de conexão temporário
5. **Reconexão automática** - Mantém conexão estável

## Quando Usar

- Telas de fila de espera para usuários não logados
- Consultas rápidas sem cadastro
- Notificações em tempo real para guests
- Qualquer funcionalidade que precise de SignalR sem autenticação
