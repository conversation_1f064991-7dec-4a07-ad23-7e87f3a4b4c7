{"name": "tele-medicina", "version": "0.0.0", "scripts": {"ng": "ng", "compress": "gulp gzip", "buildAzureProd": "ng build --configuration production && gulp gzip-PRD", "buildAzureHOM": "ng build --configuration=teste && gulp gzip-dev", "buildDev": "node --max_old_space_size=6000 node_modules/@angular/cli/bin/ng build  --configuration=development", "buildDev3": "node --max_old_space_size=6000 node_modules/@angular/cli/bin/ng build  --configuration=dev3", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "aot": "node  --max_old_space_size=6000 node_modules/@angular/cli/bin/ng serve --open", "precache": "sw-precache --verbose —config=sw-precache-config.js"}, "main": "src/src/index.ts", "repository": {"type": "git", "url": "git+https://github.com/zurfyx/angular-contents.git"}, "bugs": {"url": "https://github.com/zurfyx/angular-contents/issues"}, "homepage": "https://github.com/zurfyx/angular-contents#readme", "keywords": ["angular", "contents", "table", "follow", "index", "scroll", "angular 5"], "private": true, "dependencies": {"@angular-devkit/architect": "^0.1901.8", "@angular-devkit/core": "^17.3.12", "@angular/animations": "^17.3.12", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.12", "@angular/compiler": "^17.3.12", "@angular/core": "^17.3.12", "@angular/forms": "^17.3.12", "@angular/material": "^17.3.10", "@angular/material-moment-adapter": "^17.3.10", "@angular/platform-browser": "^17.3.12", "@angular/platform-browser-dynamic": "^17.3.12", "@angular/router": "^17.3.12", "@ckeditor/ckeditor5-angular": "^5.2.0", "@ckeditor/ckeditor5-build-classic": "^36.0.1", "@microsoft/signalr": "^3.1.31", "@ng-select/ng-select": "^12.0.7", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@swimlane/ngx-charts": "^20.1.0", "@syncfusion/ej2-angular-buttons": "^18.4.41", "@syncfusion/ej2-angular-splitbuttons": "^18.4.41", "angular-calendar": "^0.31.1", "angular-google-charts": "^16.0.2", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "ckeditor-build-b64imageupload": "^3.1.0", "core-js": "^2.6.12", "crypto-js": "^4.1.1", "crypto-service-client": "^1.1.1", "date-fns": "^1.30.1", "dotenv": "^17.2.1", "file-saver": "^2.0.5", "hammerjs": "^2.0.8", "jspdf": "^2.5.1", "moment": "^2.30.1", "ng-chat": "^3.1.0", "ng-click-outside": "^9.0.0", "ng2-charts": "^4.1.1", "ng2-currency-mask": "^13.0.3", "ng2-pdf-viewer": "^9.1.2", "ng2-truncate": "^1.3.17", "ngx-bootstrap": "^4.3.0", "ngx-currency": "^2.5.3", "ngx-image-cropper": "^6.3.4", "ngx-mask": "^17.1.8", "ngx-smart-modal": "^14.0.3", "ngx-swiper-wrapper": "^7.2.1", "ngx-webcam": "^0.4.1", "npm": "^6.14.18", "rxjs": "^7.8.0", "rxjs-compat": "^6.6.7", "truncate": "^2.1.0", "tslib": "^1.14.1", "web-animations-js": "^2.3.2", "xlsx": "^0.15.6", "zone.js": "0.14.10"}, "devDependencies": {"@angular-builders/custom-webpack": "^17.0.2", "@angular-devkit/build-angular": "^17.3.12", "@angular/cli": "^17.3.12", "@angular/compiler-cli": "^17.3.7", "@angular/language-service": "^17.3.7", "@types/crypto-js": "^3.1.47", "@types/file-saver": "^2.0.7", "@types/google.visualization": "^0.0.74", "@types/jasmine": "^2.8.23", "@types/jasminewd2": "^2.0.13", "@types/lodash": "^4.17.15", "@types/node": "~8.9.4", "@types/npm": "^7.19.3", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "eslint": "^9.21.0", "gulp": "^4.0.2", "gulp-azure-storage": "^0.12.1", "gulp-gzip": "^1.4.2", "https-browserify": "^1.0.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~6.4.4", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^2.0.6", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "protractor": "^7.0.0", "querystring-es3": "^0.2.1", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "sw-precache-webpack-plugin": "^0.11.5", "ts-node": "~7.0.0", "tslint": "~5.11.0", "typescript": "^5.4.5", "url": "^0.11.4", "util": "^0.12.5"}, "browser": {"crypto": false}}