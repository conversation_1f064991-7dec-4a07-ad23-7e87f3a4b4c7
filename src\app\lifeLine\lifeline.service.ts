import { MatDialog as MatDialog } from '@angular/material/dialog';
import { Injectable } from "@angular/core";
import { Subject } from "rxjs";
import { LifelineComponent } from './lifeline.component';

@Injectable({
    providedIn: 'root'
})
export class LifeLineService {
    constructor(
        private matDialog: MatDialog
    ) { }
    modalLifeline: Subject<any> = new Subject<any>();
    varModalLifeLine: any;
    setModalLifeline(modalLifeline:any) {
        this.varModalLifeLine = modalLifeline;
        // this.modalLifeline.next(modalLifeline);
        this.matDialog.open(LifelineComponent);
    }

    getModalLifeline() {
        return this.modalLifeline.asObservable();
    }


}