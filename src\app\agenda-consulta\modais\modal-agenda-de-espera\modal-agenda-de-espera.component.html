<div class="modal-container">
    <!-- Modal Header -->
    <div class="modal-header">
        <h3 >Agenda de espera</h3>
    </div>

    <!-- Modal Body -->
    <div class="modal-body">
        <div class="form-row" *ngIf="IdTipoUsuario != 2">
            <div class="form-group col-md-5">
                <ng-select [items]="lsMedicos" placeholder="Escolha o médico" bindLabel="nome" bindValue="idMedico"
                    [selectOnTab]="true" [(ngModel)]="IdMedico" (change)="onSelectionChange()"
                    class="custom-theme">
                </ng-select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group col-md-5">
                <ng-select [items]="lsPacientes" placeholder="Paciente" bindLabel="nome" bindValue="idCliente"
                    [selectOnTab]="true" [(ngModel)]="objAgendaEspera.IdPaciente"
                    (change)="ValidaPaciAgenda(objAgendaEspera.IdPaciente)" (blur)="validaPaciente()"
                    class="custom-theme">
                </ng-select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="w-100">
                    <mat-label>Telefone de contato</mat-label>
                    <input matInput placeholder="(00) 00000-0000" mask="(00) 00000-0000"
                        [(ngModel)]="objAgendaEspera.TelContato">
                </mat-form-field>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group col-md-12">
                <mat-form-field appearance="outline" class="w-100">
                    <mat-label>Observação</mat-label>
                    <textarea matInput #input maxlength="150" [(ngModel)]="objAgendaEspera.DesAnotacao" rows="3">
            </textarea>
                    <mat-hint align="end">{{ 0 + (objAgendaEspera?.DesAnotacao?.length || 0) }}/150</mat-hint>
                </mat-form-field>
            </div>
        </div>
        <!-- Waiting List Table -->
        <div class="waiting-list-container" *ngIf="DadosAgendaEspera.length > 0">
            <h4 class="section-title">Pacientes na espera</h4>
            <div class="table-responsive">
                <table class="table table-hover waiting-list-table">
                    <tbody>
                        <tr *ngFor="let item of DadosAgendaEspera" class="patient-row">
                            <td class="patient-info">
                                <div class="info-grid">
                                    <div class="info-cell">
                                        <span class="info-label">Nome:</span>
                                        <span class="info-value" title="{{item.nomePaciente}}">{{item.nomePaciente |
                                            truncate : 22 : "…"}}</span>
                                    </div>
                                    <div class="info-cell">
                                        <span class="info-label">Tel:</span>
                                        <span class="info-value">{{item.tel}}</span>
                                    </div>
                                    <div class="info-cell">
                                        <span class="info-label">Obs:</span>
                                        <span class="info-value" title="{{item.desAgendamento}}">{{item.desAgendamento |
                                            truncate : 22 : "…"}}</span>
                                    </div>
                                    <div class="info-cell">
                                        <span class="info-label">Data de cadastro:</span>
                                        <span class="info-value">{{item.dtaCadastro | date:'dd/MM/yyyy HH:mm' }}</span>
                                    </div>
                                </div>
                            </td>
                            <td class="action-buttons">
                                <button mat-icon-button class="action-btn" (click)="AdicionarAgendamento(item)"
                                    title="Adicionar Agendamento">
                                    <mat-icon>person_add</mat-icon>
                                </button>
                                <button mat-icon-button class="action-btn" (click)="AbrirModalExclusao(item.idAgenda)"
                                    title="Excluir Agendamento">
                                    <mat-icon>delete</mat-icon>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button  class=" action-button cancel-button" (click)="fecharModal()">
            <mat-icon>close</mat-icon>
            Cancear
        </button>
        <button class="action-button confirm-button" (click)="SalvarAgendaEspera()">
            <mat-icon>save</mat-icon>
             Salvar
        </button>
    </div>
</div>
