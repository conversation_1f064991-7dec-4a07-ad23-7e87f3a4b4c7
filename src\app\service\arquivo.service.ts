
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { objDadosModeloPdf, objGerarRelatorioAssinatura } from '../model/arquivo';
import { MatDialog } from '@angular/material/dialog';
import { Observable, map } from 'rxjs';
import { FileModalData, FileModalResult, ModalArquivoComponent } from '../Modais/modal-arquivo/modal-arquivo.component';


export interface ArquivoRetorno {
  arquivo?: string;         // base64 do arquivo
  nomeArquivo?: string;
  tipoArquivo?: string;
  tamanhoArquivo?: number;
  excluido: boolean;        // indica se o arquivo foi excluído
}

@Injectable({
  providedIn: 'root'
})
export class ArquivoService {

  constructor(
    private http: HttpClient,
    private dialog: MatDialog
  ) { }


  public GetPdfGenerico(modeloPdf: objDadosModeloPdf) {
    return this.http.post(environment.apiEndpoint + '/dadosArquivos/GetPdfGenerico', modeloPdf)
  }

  public GerarRelatorioFaturas(listaIdGuiaTiss: number[]) {
    return this.http.post(environment.apiEndpoint + '/dadosArquivos/GerarRelatorioFaturas', listaIdGuiaTiss, { responseType: 'arraybuffer' })
  }

  public GerarRelatorioAgendamentos(obj: objGerarRelatorioAssinatura) {
    return this.http.post<string>(environment.apiEndpoint + '/dadosArquivos/GerarRelatorioAgendamentos', obj);
  }




  //#region Opera modal arquivo
  public abrirModalArquivo(
    arquivo?: string,
    nomeArquivo?: string,
    tipoArquivo?: string,
    flgEditaArquivo: boolean = false,
    flgRemocaoArquivo: boolean = false
  ): Observable<ArquivoRetorno> {
    const data: FileModalData = {
      arquivo,
      nomeArquivo,
      tipoArquivo,
      flgEditaArquivo,
      flgRemocaoArquivo
    };

    const dialogRef = this.dialog.open(ModalArquivoComponent, {
      width: '600px',
      maxWidth: '100vw',
      maxHeight: '90vh',
      disableClose: true,
      data
    });

    return dialogRef.afterClosed().pipe(
      map((result: FileModalResult) => {
        if (!result) {
          return { excluido: false }; // Operação cancelada
        }

        switch (result.acao) {
          case 'salvar':
            return {
              arquivo: result.arquivo,
              nomeArquivo: result.nomeArquivo,
              tipoArquivo: result.tipoArquivo,
              tamanhoArquivo: result.tamanhoArquivo,
              excluido: false
            };
          case 'excluir':
            return { excluido: true };
          default:
            return { excluido: false }; // Cancelamento
        }
      })
    );
  }
  //#endregion
}
