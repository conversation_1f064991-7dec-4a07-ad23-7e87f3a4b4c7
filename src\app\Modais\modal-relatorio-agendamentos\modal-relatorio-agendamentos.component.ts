import { Component, OnInit } from '@angular/core';
import {
  FormsModule,
  ReactiveFormsModule
} from '@angular/forms';
import { MatDialogModule, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { ObjDownloadFileModal, objGerarRelatorioAssinatura } from 'src/app/model/arquivo';
import { AgendaService } from 'src/app/service/agenda.service';
import { ArquivoService } from 'src/app/service/arquivo.service';
import { ControleModaisService } from 'src/app/service/controle-modais.service';
import { MedicoService } from 'src/app/service/medico.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { ConvertDataObj } from 'src/app/Util/ConvertDataObj';
import { parseDateString, validateDates, DateValidationResult } from 'src/app/Util/ValidadorDataPorPeriodo';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-modal-relatorio-agendamentos',
  templateUrl: './modal-relatorio-agendamentos.component.html',
  styleUrls: ['./modal-relatorio-agendamentos.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatDialogModule,
    MatDividerModule
  ]
})
export class ModalRelatorioAgendamentosComponent implements OnInit {

  constructor(
    public dialogRef: MatDialogRef<ModalRelatorioAgendamentosComponent>,
    private controleModas: ControleModaisService,
    private usuarioLogadoService: UsuarioLogadoService,
    private medicoService: MedicoService,
    private spinner: SpinnerService,
    private snackBar: AlertComponent,
    private agendaService: AgendaService,
    private arquivoService: ArquivoService
  ) { }

  ngOnInit(): void {
    this.CarregaMedicos();
    this.tipoAgendamento();

    this.dtChangeInicio();
    this.dtChangeFim();
  }

  FileName = "Arquivo Agendamentos";
  ListaMedicos: any;
  dadosTipoAgendamento: any
  idMedicoSelecionado?: number;
  idTipoAtendimento?: number;

  strDtInicio = "";
  strDtFim = "";

  dtInicio? = new Date();
  dtFim = new Date();


  GerarRelatorio() {

    if (!this.validaGerarRelatorio())
      return;

    let obj = new objGerarRelatorioAssinatura();
    obj.dtInicio = ConvertDataObj(this.dtInicio, true);
    obj.dtFinal = ConvertDataObj(this.dtFim, true);

    if (this.idMedicoSelecionado != null && this.idMedicoSelecionado != undefined && this.idMedicoSelecionado > 0)
      obj.idMedico = this.idMedicoSelecionado
    else
      obj.idMedico = null;

    this.spinner.show();

    this.arquivoService.GerarRelatorioAgendamentos(obj).subscribe((ret) => {
      this.spinner.hide();

      if (ret == "Sem registros") {
        this.snackBar.alertaSnackbar("Sem registros para o filtro escolhido.")
        return;
      }

      this.descargarPdf(ret);
    }, () => {
      this.snackBar.alertaSnackbar("Erro carregar relatorio")
      this.spinner.hide();
    })

  }

  flgDiferencaDatas: boolean = false;
  flgDtInicioNaoPreenchida: boolean = false;
  flgDtFimNaoPreenchida: boolean = false;
  flgDtFimMaiorQueInicio: boolean = false;

  dtChangeInicio() {
    this.dtInicio = parseDateString(this.strDtInicio)!;
    this.flgDtInicioNaoPreenchida = !this.dtInicio;
    this.validateDates();
  }

  dtChangeFim() {
    this.dtFim = parseDateString(this.strDtFim)!;
    this.flgDtFimNaoPreenchida = !this.dtFim;
    this.validateDates();
  }

  validateDates() {
    const validationResult = validateDates(this.dtInicio!, this.dtFim, 48);

    switch (validationResult) {
      case DateValidationResult.DtInicio_Nao_Preenchida:
        console.warn('Data Inicial não preenchida.');
        this.flgDtInicioNaoPreenchida = true;
        break;
      case DateValidationResult.DtFim_Nao_Preenchida:
        console.warn('Data Final não preenchida.');
        this.flgDtFimNaoPreenchida = true;
        break;
      case DateValidationResult.DtInicio_Maior_DtFim:
        console.error('Data Inicial não pode ser maior que a Data Final.');
        this.flgDtFimMaiorQueInicio = true;
        break;
      case DateValidationResult.Diferenca_Entre_Datas_Supera_Limite_Fornecido:
        console.error('A diferença entre as datas não pode ser maior que ' + 6 + ' meses.');
        this.flgDiferencaDatas = true;
        break;
      case DateValidationResult.OK:
        this.flgDiferencaDatas = false;
        this.flgDtFimMaiorQueInicio = false;
        break;
    }
  }

  validaGerarRelatorio(): boolean {
    if (this.FileName.length < 1 || this.FileName == null || this.FileName == "" || this.FileName == " ")
      return false

    if (this.flgDtFimMaiorQueInicio, this.flgDtFimNaoPreenchida, this.flgDtFimNaoPreenchida, this.flgDiferencaDatas)
      return false;

    else
      return true;
  }

  AbrirDownloadArquivo(file: any) {
    let obj = new ObjDownloadFileModal();
    obj.FileExtension = "PDF";
    obj.FileName = this.FileName;
    obj.Base64File = file;


    this.controleModas.ModalDownload(obj);
  }

  tipoAgendamento() {
    this.spinner.show();
    this.agendaService.getTipoAgendamento(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.dadosTipoAgendamento = retorno;
      this.spinner.hide();
    }, err => {
      this.snackBar.alertaSnackbar("Erro ao carregar agendamentos.");
      this.spinner.hide();
      console.error(err)
    })
  }

  CarregaMedicos() {
    this.spinner.show();
    this.medicoService.getMedicos(null, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.ListaMedicos = retorno
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.snackBar.alertaSnackbar("Erro ao carregar médico.");
      this.spinner.hide();
    })
  }

  FecharModal() {
    this.dialogRef.close();
  }

  descargarPdf(base64Html: string) {
    const htmlString = decodeURIComponent(escape(atob(base64Html)));

    const div = document.createElement('div');
    div.innerHTML = htmlString;
    document.body.appendChild(div);

    html2canvas(div).then(canvas => {
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({ orientation: 'landscape', unit: 'mm', format: 'a4' });
      const pageWidth = pdf.internal.pageSize.getWidth();
      const imgWidth = pageWidth;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save('documento.pdf');

      document.body.removeChild(div);
    });
  }
}