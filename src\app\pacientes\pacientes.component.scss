// Variables - Health Green Theme
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro
$error-color: #FF6B6B;          // Vermelho Pastel
$success-color: #4CAF50;        // Verde para sucesso
$pending-color: #FFC107;        // Amarelo para pendente
$text-primary: #333333;         // Cinza escuro
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s ease-in-out;

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', 'Segoe UI', sans-serif;
  color: $text-primary;
  background-color: $bg-color;
}

.analysis-container {
  height: 87vh;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
  
  @media (max-width: 480px) {
    padding: 12px;
  }
}

// Card Styling
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  margin-bottom: 24px;
  overflow: hidden;
  animation: fadeIn 0.3s $transition;
  border: none;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  
  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.header-icon-title {
  display: flex;
  align-items: center;
  gap: 16px;
  
  h1 {
    font-size: 1.4rem;
    font-weight: 500;
    color: $primary-color;
    margin: 0;
    text-transform: uppercase;
  }
}

.icon-box {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.btn-back {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: $primary-color;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: $transition;
  padding: 8px 12px;
  border-radius: 6px;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
  
  .material-icons {
    font-size: 18px;
  }
}

.card-content {
  padding: 24px;
  height: 71vh;
  overflow: auto;
  @media (max-width: 768px) {
    padding: 16px;
  }
}

.card-actions {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  gap: 12px;
  
  @media (max-width: 576px) {
    justify-content: space-between;
  }
}

// Success Message
.success-message {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: rgba($success-color, 0.1);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  
  .success-icon {
    color: $success-color;
    font-size: 24px;
  }
  
  p {
    color: $text-primary;
    font-weight: 500;
    margin: 0;
  }
}

// Form Sections
.form-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  
  h2 {
    font-size: 1.1rem;
    font-weight: 500;
    color: $primary-color;
    margin: 0;
  }
}

.section-marker {
  display: block;
  width: 4px;
  height: 20px;
  background-color: $primary-color;
  border-radius: 2px;
}

.form-container {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
  
  @media (max-width: 576px) {
    padding: 16px;
  }
}

// Profile Photo
.profile-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  
  @media (max-width: 576px) {
    margin-bottom: 16px;
  }
}

.profile-photo-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.profile-photo-container {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid $primary-light;
  position: relative;
  margin-bottom: 8px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.profile-photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: $transition;
  
  .material-icons {
    color: white;
    font-size: 32px;
  }
}

.profile-photo-container:hover .profile-photo-overlay {
  opacity: 1;
}

.photo-hint {
  font-size: 0.9rem;
  color: $text-secondary;
}

input[type="file"],
input[type="hidden"] {
  display: none;
}

// Form Grid
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  
  @media (max-width: 576px) {
    grid-template-columns: 1fr;
  }
}

.form-field {
  position: relative;
  margin-bottom: 4px;
  
  &.field-large {
    grid-column: span 2;
    
    @media (max-width: 768px) {
      grid-column: span 1;
    }
  }
  
  &.field-medium {
    grid-column: span 1;
  }
  
  &.field-small {
    min-width: 100px;
    
    @media (max-width: 576px) {
      min-width: initial;
    }
  }
}

// Material Form Field Styling
mat-form-field {
  width: 100%;
  
  ::ng-deep {
    .mat-form-field-wrapper {
      margin-bottom: 0;
      padding-bottom: 0;
    }
    
    .mat-form-field-outline {
      color: lighten($border-color, 5%);
    }
    
    .mat-form-field-label {
      color: $text-secondary;
    }
    
    &.mat-focused {
      .mat-form-field-outline-thick {
        color: $primary-color;
      }
      
      .mat-form-field-label {
        color: $primary-color;
      }
    }
  }
}

// NG-Select Styling
.select-label {
  font-size: 0.8rem;
  color: $text-secondary;
  margin-bottom: 4px;
  display: block;
}

.custom-select {
  width: 100%;
  
  ::ng-deep {
    .ng-select-container {
      border-color: lighten($border-color, 5%);
      min-height: 52px;
      border-radius: 4px;
      
      &:hover {
        border-color: $primary-light;
      }
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
    }
    
    .ng-option {
      padding: 8px 12px;
      
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}

// Error messages
.error-text {
  color: $error-color;
  font-size: 0.8rem;
  margin-top: 4px;
  display: block;
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: $transition;
  font-size: 0.95rem;
  min-width: 120px;
  
  .material-icons {
    font-size: 20px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary;
  
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
  }
}

// Modal Styling
.modal-header {
  padding: 16px;
  background-color: $primary-color;
  position: relative;
  
  .modal-title {
    color: white;
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
  }
  
  .title-icon {
    position: absolute;
    top: 16px;
    right: 16px;
    color: rgba(white, 0.8);
  }
}

.modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
  
  @media (max-width: 576px) {
    padding: 16px;
    max-height: 60vh;
  }
}

.modal-footer {
  padding: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid $border-color;
  
  &.terms-footer {
    flex-direction: column;
    
    .terms-question {
      text-align: center;
      font-weight: 500;
      margin-bottom: 16px;
    }
    
    .terms-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
  }
}

// Terms content
.terms-content {
  max-height: 60vh;
  overflow-y: auto;
  
  p {
    margin-bottom: 16px;
    line-height: 1.5;
    
    strong {
      color: $primary-dark;
    }
  }
}

// Image cropper
.image-edit-controls {
  margin-bottom: 20px;
  
  .edit-row {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 12px;
  }
}

.btn-control {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba($primary-color, 0.1);
  color: $primary-color;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: $transition;
  
  &:hover {
    background-color: rgba($primary-color, 0.2);
  }
  
  .material-icons {
    font-size: 18px;
  }
  
  @media (max-width: 576px) {
    padding: 6px 10px;
    
    .control-text {
      display: none;
    }
  }
}

.cropper-wrapper {
  display: flex;
  justify-content: center;
  margin: 0 auto;
  max-width: 300px;
  max-height: 300px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid $border-color;
}

// Warning message
.message-container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  background-color: rgba($pending-color, 0.1);
  border-radius: 8px;
  
  .icon-warning {
    color: $pending-color;
    font-size: 24px;
  }
  
  p {
    margin: 0;
    color: $text-primary;
    flex: 1;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }
  
  .card-actions {
    flex-wrap: wrap;
  }
  
  .form-container {
    padding: 16px;
  }
  
  .form-grid {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .btn-text {
    display: none;
  }
  
  .btn .material-icons {
    margin-right: 0;
  }
  
  .profile-photo-container {
    width: 100px;
    height: 100px;
  }
  
  .modal-body {
    padding: 12px;
  }
}