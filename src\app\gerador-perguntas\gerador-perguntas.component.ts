import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit, Output } from '@angular/core';
import { Perguntas } from '../model/perguntas';
import { GeradorPerguntasService } from '../service/geradorPerguntas.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';

// import { MatDialogRef, MAT_DIALOG_DATA, MatDialog, MAT_DIALOG_DEFAULT_OPTIONS, MatDialogModule } from '@angular/material';
// import { MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition, MatSnackBarConfig, MatSnackBar } from '@angular/material/snack-bar';
import { TranslateModule } from '@ngx-translate/core';
import { FadeIn } from 'src/app/Util/Fadein.animation';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatDivider } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-gerador-perguntas',
    templateUrl: './gerador-perguntas.component.html',
    styleUrls: ['./gerador-perguntas.component.scss'],
    animations: [FadeIn],
    host: { '[@FadeIn]': '' },
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCardModule,
      MatDivider,
      MatIcon,
      MatFormFieldModule,
      TranslateModule,
      NgxSmartModalModule
    ]
})
export class GeradorPerguntasComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private perguntasService: GeradorPerguntasService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private usuarioLogadoService: UsuarioLogadoService

  ) {
  }

  @Output() FadeIn?: string;

  toggle:any = {}
  perguntas?: string;
  bOcultaCarregaMais = false;
  qtdRegistros = 10;
  pesquisa = "";
  idPergunta = 0
  DadosPerguntas: any;
  // usuario: Usuario;
  legenda = false;
  ObjPergunta: any;
  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // perguntaSalva: string = 'Pergunta salva com sucesso. ✔';
  // editarPergunta: string = 'Editar pergunta';
  // editPerguntaSalva: string = 'Pergunta editada com sucesso ✔';
  // deletarPergunta: string = 'Pergunta apagada com sucesso ✔';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  campoPerguntaInvalid: boolean = false;


  ngOnInit() {
    this.CarregaPerguntas();
  }



  CarregaPerguntas() {
    this.spinner.hide();
    this.bOcultaCarregaMais = false;

    this.perguntasService.GetPerguntas(0, this.qtdRegistros, this.pesquisa).subscribe((retorno) => {
      ;
      ;
      this.DadosPerguntas = retorno;

      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
      // this.DadosPerguntas.forEach(element => {
      //   element.dtaCadastro = new Date(element.dtaCadastro).toLocaleString();

      // });
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }


  CarregarMais() {
    this.bOcultaCarregaMais = false;
    this.perguntasService.GetPerguntas(this.DadosPerguntas.length, this.qtdRegistros, this.pesquisa).subscribe((retorno) => {
      var rtnCarregaMais = retorno;

      for (let index = 0; index < rtnCarregaMais.length; index++) {
        this.DadosPerguntas.push(rtnCarregaMais[index]);
      }
      // this.DadosPerguntas.forEach(element => {
      //   element.dtaCadastro = new Date(element.dtaCadastro).toLocaleString();
      // });

      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  PerguntasTotal() {
    this.perguntasService.getPergunta().subscribe(() => {
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }




  salvarPergunta() {

    
    if (this.campoPergunta.invalid) {
      this.campoPerguntaInvalid = true;
      this.campoPergunta.markAsTouched()
      this.snackBarAlert.falhaSnackbar('Preencha o campo obrigatório');
      return
    }
    this.campoPerguntaInvalid = false;
    if (this.perguntas != undefined && this.perguntas != "") {
      var perguntas = new Perguntas();
      perguntas.FlgInativo = false;
      perguntas.DesPergunta = this.perguntas;
      perguntas.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()
      if (this.idPergunta != 0)
        perguntas.idPergunta = this.idPergunta;

      perguntas.DtaCadastro = new Date();
      if (this.ObjPergunta) {


        if (this.ObjPergunta[0].idPergunta == this.idPergunta) {
          this.snackBarAlert.alertaSnackbar("Pergunta editada com sucesso");
        } else 
          this.snackBarAlert.sucessoSnackbar("Cadastro salvo com Sucesso.");

      }
      this.perguntasService.salvarPergunta(perguntas).subscribe((retorno) => {
        if (retorno) {
          this.CarregaPerguntas()
          this.perguntas = "";
          this.idPergunta = 0;
          this.snackBarAlert.sucessoSnackbar("Pergunta salva com sucesso.");
          (document.getElementById('perguntas')as HTMLInputElement).focus();
          this.campoPergunta.markAsUntouched()
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }

  }
  inativarPerguntas() {
    this.perguntasService.inativarPergunta(this.idPergunta, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      retorno;
      this.idPergunta = 0
      this.ngxSmartModalService.getModal('excluirItem').close();
      this.snackBarAlert.sucessoSnackbar("Pergunta apagada com sucesso");
      this.CarregaPerguntas();
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  ValorPergunta(id:any) {
    this.idPergunta = 0
    this.idPergunta = id;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }
  editPergunta(id:any) {

    this.ObjPergunta = this.DadosPerguntas.filter((c:any) => c.idPergunta == id)
    this.perguntas = this.ObjPergunta[0].desPergunta;
    this.idPergunta = this.ObjPergunta[0].idPergunta;
    document.documentElement.scrollTop = 0;
    this.snackBarAlert.alertaSnackbar("Editar pergunta");
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  // AlgumAcerto(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }
  // PerguntaSalva(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAGERADORDEPERGUNTAS.PERGUNTASALVA').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }
  // EditarPergunta(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAGERADORDEPERGUNTAS.EDITARPERGUNTA').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }
  // EditPerguntaSalva(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAGERADORDEPERGUNTAS.EDITARPERGUNTASALVA').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }
  // DeletarPergunta(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAGERADORDEPERGUNTAS.DELETARPERGUNTA').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }

  campoPergunta = new FormControl('', [Validators.required, Validators.required])


  getErrorMessagecampoPergunta() {
    return this.campoPergunta.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.campoPergunta.hasError('campoPergunta') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDO' :
        '';

  }
}
