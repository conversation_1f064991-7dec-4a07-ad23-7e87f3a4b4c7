import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from './auth.service';
import { Login } from '../model/login'
import { firstValueFrom, Observable } from 'rxjs';
import { UsuarioService } from '../service/usuario.service';
import { LoginService } from '../service/login.service';
import { ValidacaoService } from '../service/validacao.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { UsuarioLogado } from '../auth/UsuarioLogado';
import { LocalStorageService } from '../service/LocalStorageService';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
  ]
})


export class LoginComponent implements OnInit {
  
 

  constructor(
    private spinner: SpinnerService,
    private route: ActivatedRoute,
    private loginservice: LoginService,
    private authService: AuthService,
    private router: Router,
    private usuarioService: UsuarioService,
    public validador: ValidacaoService,
    public translate: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService

  )
  
  {
    // spinner.hide();

    this.usuarioLogadoService.clear();
    translate.setDefaultLang('pt');
    this.Linguagem = this.localStorageService.Linguagem;

    // const username = document.getElementById('username')
    // const password = document.getElementById('password')
    // const check = document.getElementById('check')
    // const spinner = document.getElementById('spinner')
    // const connect = document.getElementById('connect')

    // var username_valid = false
    // var password_valid = false


    // username.oninput = function () {
    //   if (this.value.length > 4) {
    //     username_valid = true
    //   }
    //   else {
    //     username_valid = false
    //   }
    //   form_valid (username_valid, password_valid)
    // }
    // password.oninput = function () {
    //   if (this.value.length > 8) {
    //      password_valid = true 
    //   }
    //   else {
    //     password_valid = false
    //   }
    //   form_valid (username_valid, password_valid)
    // }

    // function form_valid (username_valid, password_valid) {
    //   if (username_valid && password_valid) {
    //     spinner.style.display = 'none'
    //     check.style.display = 'block'
    //     button.classList.add('valid')
    //     check.classList.add('up')
    //   }
    //   else {
    //     spinner.style.display = 'block'
    //     check.style.display = 'none'
    //     button.classList.remove('valid')
    //     check.classList.remove('up')
    //   }
    // }

  }

  public login: Login = new Login();
  timer?: Observable<any>;
  logininvalido: boolean = false;
  isMobile: boolean = false;
  Linguagem: string = "";
  ModoLogin: string = 'Cpf';
  testeInicial = 'Selecione a forma de identificação';
  showPassword: boolean = false;
  
  ngOnInit() {

    if (!this.Linguagem || this.Linguagem == "null")
      this.Linguagem = "pt";
  }


  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }

  changeType() {
    this.login.cpf = "";
    this.login.senha = "";
  }
  Logar() {
    ;
    this.loginservice.logado$.emit(true);
    sessionStorage.setItem('Logado', "true");
    window.location.href = "/pesquisapacientes";
  }

  async realizarLogin() {
    const fncError = () => {
      this.logininvalido = true;
    };

    this.spinner.show();

    var sGuid = "";
    this.route.queryParams.subscribe(params => {
      sGuid = params['guid'];
    });
    if (sGuid == undefined)
      sGuid = "";
    try {
      if (this.login.senha == null || this.login.senha == "" || this.login.cpf == null || this.login.cpf == "") {
        this.logininvalido = true;
        this.spinner.hide();
      }
      else {
        
        const data: any = await this.authService.AutenticaUsuario(this.login.cpf, this.login.senha, sGuid, this.ModoLogin);
        if (data.access_token != null) {
          this.localStorageService.token = data.access_token;

          let dataUsuario: UsuarioLogado;
          dataUsuario = await firstValueFrom(this.usuarioService.getPerfilUsuarioLogado());

          if (!dataUsuario) {
            this.spinner.hide();
            return fncError();
          }

          this.localStorageService.TelaPrivacidadeLogin = 'Login'

          this.usuarioService.AtualizaDadosUsuarioLogado(dataUsuario);
          this.localStorageService.Logado = true;
          this.localStorageService.Linguagem = this.Linguagem;
          this.router.navigate(['']);
          this.spinner.hide();
        } else {
          this.spinner.hide();
          return fncError();
        }
      }

    } catch (res) {
      this.spinner.hide();
      console.error(res);
      return fncError();
    }
  }

  recuperarSenha() {
    this.loginservice.recuperarSenha$.emit(true);
    this.localStorageService.Linguagem = this.Linguagem;
    this.router.navigate(['/recuperarSenha']);
  }

  public mascaraCpf(mascara: any, evento: KeyboardEvent) {
    mascara;
    var valorEvento = (<HTMLInputElement>evento.target).value;
    // var i = valorEvento.length;
    var ao_cpf = valorEvento;
    ao_cpf = ao_cpf.replace(/\D/g, "");
    ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
    return (<HTMLInputElement>evento.target).value = ao_cpf;

  }


}
