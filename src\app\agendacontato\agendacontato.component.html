<div class="container">
  <mat-card class="card-principal mother-div">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">group</span>
      </div>
      <h2 class="header-title">{{ 'TELAAGENDACONTATO.CONTATOS' | translate }}</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">
      <div class="busca-container">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>{{ 'TELAAGENDACONTATO.BUSCAR' | translate }}</mat-label>
          <input matInput 
                 id="inputBusca" 
                 [maxlength]="Tipopesquisa == 'Tel' ? 15 : 50"
                 (keyup)="mascaraPesquisa($event)"
                 (keyup.enter)="CarregaTable()"
                 [(ngModel)]="pesquisa">
          <button mat-icon-button matSuffix (click)="CarregaTable()" class="btn-busca">
            <mat-icon>search</mat-icon>
          </button>
        </mat-form-field>
      </div>

      <div class="adicionar-container desktop-only">
        <button mat-raised-button class="btn-adicionar" (click)="NovoCont()">
          <span>{{ 'TELAAGENDACONTATO.ADICIONARCONTATO' | translate }}</span>
        </button>
      </div>
      
      <div class="adicionar-container mobile-only">
        <button mat-raised-button class="btn-adicionar-mobile" (click)="NovoCont()">
          <mat-icon>person_add</mat-icon>
          <span>{{ 'TELAAGENDACONTATO.ADICIONARCONTATO' | translate }}</span>
        </button>
      </div>
    </div>

    <!-- LISTA DE CONTATOS - DESKTOP VIEW -->
    <div class="lista-container desktop-only">
      <div class="lista-scroll">
        <div class="contato-card" *ngFor="let item of DadosTab">
          <div class="contato-info">
            <div class="contato-principal">
              <div class="info-item nome-item">
                <mat-icon>face</mat-icon>
                <span class="nome" [title]="item.nome">{{item.nome | truncate : 20 : "…"}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span [title]="item.email">{{item.email | truncate : 25 : "…"}}</span>
              </div>
              <div class="info-item">
                <mat-icon>folder_shared</mat-icon>
                <span [title]="item.tipoUser">{{item.tipoUser != null ? item.tipoUser : 'Contato' }}</span>
              </div>
            </div>
          </div>
          
          <div class="contato-dados">
            <div class="dados-item">
              <span class="dados-label">{{ 'TELAAGENDACONTATO.TELEFONE:' | translate }}</span>
              <span class="dados-valor" [title]="item.tel">{{item.tel}}</span>
            </div>
            <div class="dados-item">
              <span class="dados-label">{{ 'TELAAGENDACONTATO.CELULAR:' | translate }}</span>
              <span class="dados-valor" [title]="item.telMovel">{{item.telMovel}}</span>
            </div>
          </div>
          
          <div class="contato-dados secundarios">
            <div class="dados-item">
              <span class="dados-label">{{ 'TELAAGENDACONTATO.TELCOMERCIAL:' | translate }}</span>
              <span class="dados-valor" [title]="item.telCom">{{item.telCom}}</span>
            </div>
            <div class="dados-item">
              <span class="dados-label">{{ 'TELAAGENDACONTATO.SITE:' | translate }}</span>
              <span class="dados-valor" [title]="item.site">{{item.site | truncate : 25 : "…"}}</span>
            </div>
          </div>
          
          <div class="contato-acoes">
            <button  class="action-button edit-button"
                    (click)="EdicaoCont(item.idcontato)"
                    [title]="'TELAAGENDACONTATO.EDITARCONTATO' | translate">
              <mat-icon>edit</mat-icon>
            </button>
            <button class="action-button delete-button"
                    (click)="valorDelet(item.idcontato)"
                    [title]="'TELAAGENDACONTATO.EXCLUIRCONTATO' | translate">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="DadosTab?.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhum contato encontrado</p>
        </div>
      </div>
    </div>    
    <!-- BOTÃO CARREGAR MAIS -->
    <div class="carregar-mais" *ngIf="(DadosTab != undefined && DadosTab.length > 0) && bOcultaCarregaMais == false">
      <button mat-flat-button class="btn-carregar" (click)="CarregarMais()">
        {{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}
      </button>
    </div>
  </mat-card>
</div>

<!-- MODAL NOVO/EDITAR CONTATO -->
<ngx-smart-modal #ModalNovoContato identifier="ModalNovoContato" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <div class="modal-title">
        <mat-icon>contact_phone</mat-icon>
        <h3>{{ Edcao ? ('TELAAGENDACONTATO.EDITARCONTATO' | translate) : ('TELAAGENDACONTATO.NOVOCONTATO' | translate) }}</h3>
      </div>
    </div>
    
    <div class="modal-body">
      <div class="form-row">
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>{{ 'TELAAGENDACONTATO.NOME' | translate }}</mat-label>
          <input matInput name="Nome" required
                 [(ngModel)]="dadosNovoContato.nome" 
                 (keyup)="mascaraText($any($event.target).value)" 
                 (change)="mascaraText($any($event.target).value)">
          <mat-error *ngIf="nome.invalid">{{getErrorMessagenome() | translate }}</mat-error>
        </mat-form-field>
      </div>
      
      <div class="form-row">
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>{{ 'TELAAGENDACONTATO.EMAIL' | translate }}</mat-label>
          <input matInput type="email" name="email" required [(ngModel)]="dadosNovoContato.email">
          <mat-error *ngIf="email.invalid">{{getErrorMessageEmail() | translate }}</mat-error>
        </mat-form-field>
      </div>
      
      <div class="form-row">
        <div class="field-container">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'TELAAGENDACONTATO.TELEFONE' | translate }}</mat-label>
            <input matInput name="Telefone"
                   (keyup)="mascaraTelefone($any($event.target).value)" 
                   (keypress)="mascaraTelefone($any($event.target).value)"
                   (blur)="ValidaTelefone($any($event.target).value)" 
                   maxlength="15" minlength="14"
                   [(ngModel)]="dadosNovoContato.telefone">
          </mat-form-field>
          <div class="field-error" *ngIf="TelVal">
            {{ 'TELAAGENDACONTATO.TELEFONEINVALIDO' | translate }}
          </div>
        </div>
      </div>
      
      <div class="form-row">
        <div class="field-container">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'TELAAGENDACONTATO.CELULAR' | translate }}</mat-label>
            <input matInput name="Telefone Movel" 
                   (keyup)="mascaraTelefone($event)" 
                   (keypress)="mascaraTelefone($event)"
                   (blur)="ValidaTelefoneMovel($any($event.target).value)" 
                   maxlength="15" minlength="14" required
                   [(ngModel)]="dadosNovoContato.telefoneMovel">
          </mat-form-field>
          <div class="field-error" *ngIf="TelMovVal">
            {{ 'TELAAGENDACONTATO.TELEFONEINVALIDO' | translate }}
          </div>
          <div class="field-error" *ngIf="TelMovValVasil && TelMovVal != true">
            {{ 'TELAAGENDACONTATO.ESSECAMPOPRECISASERPREECHIDO' | translate }}
          </div>
        </div>
      </div>
      
      <div class="form-row">
        <div class="field-container">
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>{{ 'TELAAGENDACONTATO.TELEFONECOMERCIAL' | translate }}</mat-label>
            <input matInput name="Telefone Comercial" 
                   (keyup)="mascaraTelefone($event)"
                   (keypress)="mascaraTelefone($event)" 
                   (blur)="ValidaTelefoneComercial($any($event.target).value)"
                   maxlength="15" minlength="14" 
                   [(ngModel)]="dadosNovoContato.telefoneCom">
          </mat-form-field>
          <div class="field-error" *ngIf="TelComVal">
            {{ 'TELAAGENDACONTATO.TELEFONEINVALIDO' | translate }}
          </div>
        </div>
      </div>
      
      <div class="form-row">
        <mat-form-field appearance="outline" class="form-field">
          <mat-label>{{ 'TELAAGENDACONTATO.SITE' | translate }}</mat-label>
          <input matInput name="Site" [(ngModel)]="dadosNovoContato.Site">
        </mat-form-field>
      </div>
    </div>
    
    <div class="modal-footer">
      <button mat-flat-button class="btn-danger" *ngIf="Edcao" (click)="valorDelet(dadosNovoContato.idcontato)">
        <mat-icon>delete</mat-icon>
        <span>{{ 'TELAAGENDACONTATO.EXCLUIR' | translate }}</span>
      </button>
      <button mat-flat-button class="btn-neutral" (click)="LimparCampos()">
        <mat-icon>clear</mat-icon>
        <span>{{ 'TELAAGENDACONTATO.LIMPAR' | translate }}</span>
      </button>
      <button mat-flat-button class="btn-primary" (click)="salvarCont()">
        <mat-icon>save</mat-icon>
        <span>{{ 'TELAAGENDACONTATO.SALVAR' | translate }}</span>
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL EXCLUIR -->
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <div class="modal-title">
        <mat-icon class="warning-icon">warning</mat-icon>
        <h3>{{ 'TELAAGENDACONTATO.EXCLUIRESTECONTATO' | translate }}</h3>
      </div>
    </div>
    
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAAGENDACONTATO.EXCLUIDOPERMANENTEMENTE' | translate }}</p>
    </div>
    
    <div class="modal-footer">
      <button mat-flat-button class="btn-neutral" (click)="excluirItem.close()">
        {{ 'TELAAGENDACONTATO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-danger" (click)="ExcluirContato()">
        {{ 'TELAAGENDACONTATO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>