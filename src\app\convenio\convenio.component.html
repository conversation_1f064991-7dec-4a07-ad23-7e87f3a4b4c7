<div class="convenio-container">
    <div class="card main-card">
      <!-- Mensagens de alerta -->
      <div *ngIf="showMessageSuccess" class="alert alert-success">
        <span class="material-icons">check_circle</span>
        <p>{{ 'TELACONVENIO.REGISTROSALVO' | translate }}</p>
      </div>
  
      <div class="card-header">
        <div class="header-left">
          <div class="icon-container">
            <span class="material-icons">credit_card</span>
          </div>
          <h1 class="page-title">{{ 'TELACONVENIO.CONVENIO' | translate }}</h1>
        </div>
        <button class="btn btn-link" onclick='history.go(-1)'>
          <span class="material-icons">arrow_back</span>
        </button>
      </div>
  
      <div class="card-body">
        <!-- Seção de dados do convênio -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACONVENIO.DADOSDOVONVENIO' | translate }}</h2>
          </div>
          <div class="section-content">
            <div class="convenio-info">
              <div class="convenio-logo">
                <label for="imageperfilClinica" class="image-upload-container">
                  <img src="{{ ImagemConvenio }}" alt="Logo do Convênio" class="convenio-img" />
                  <div class="image-upload-overlay">
                    <span class="material-icons">photo_camera</span>
                  </div>
                </label>
                <p class="upload-label">{{ 'TELACADASTROCLINICA.LOGO' | translate }}</p>
                <input type="file" id="imageperfilClinica" (change)="AlterarImagemClinica($event)" />
                <input type="text" style="display: none;" id="Logo" name="Logo" [(ngModel)]="DadosConvenio.Logo">
              </div>
              
              <div class="convenio-details">
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>{{ 'TELACONVENIO.CNPJ' | translate }}</mat-label>
                      <input matInput name="CNPJ" 
                        (change)="ValidaCNPJ($any($event.target).value)"
                        (keypress)="mascaraCnpj('##.###.###/####-##', $event)"
                        (keyup)="mascaraCnpj('##.###.###/####-##', $event)" 
                        maxlength="18" required
                        [(ngModel)]="DadosConvenio.cNPJ">
                    </mat-form-field>
                    <div class="field-error" *ngIf="campoCNPJInvalido">CNPJ inválido</div>
                    <div class="field-error" *ngIf="campoCNPJVazil && !campoCNPJInvalido">Esse campo precisa ser preenchido</div>
                  </div>
                  
                  <div class="form-group col-md-6">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>{{ 'TELACONVENIO.REGCLINICA' | translate }}</mat-label>
                      <input matInput name="Reg. Clinica" [(ngModel)]="DadosConvenio.regClinica">
                    </mat-form-field>
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group col-md-4">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>{{ 'TELACONVENIO.DESCRICAO' | translate }}</mat-label>
                      <input matInput name="Descricao" required [(ngModel)]="DadosConvenio.descricao" #Descricao="ngModel">
                      <mat-error *ngIf="Descricao.invalid">{{getErrorMessageNome() | translate }}</mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="form-group col-md-4">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>{{ 'TELACONVENIO.VALORCONSULTA' | translate }}</mat-label>
                      <input matInput name="valor" id="valorConsulta" required
                        [(ngModel)]="DadosConvenio.valorConsulta" #vlr="ngModel"
                        (keypress)="mascaraValor($event)"
                        (change)="mascaraValor($any($event))" 
                        (keyup)="mascaraValor($event)"
                        maxlength="15">
                      <mat-error *ngIf="vlr.invalid">{{geterrorMessageVALOR() | translate }}</mat-error>
                    </mat-form-field>
                  </div>
                  
                  <div class="form-group col-md-4">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>{{ 'TELACONVENIO.PERIODORETORNO' | translate }}</mat-label>
                      <input matInput name="retorno" maxlength="3" 
                        (keyup)="mascaraNum($event)"
                        (keypress)="mascaraNum($event)" 
                        [(ngModel)]="DadosConvenio.retorno">
                    </mat-form-field>
                  </div>
                </div>
                
                <div class="form-row">
                  <div class="form-group full-width">
                    <mat-form-field appearance="outline" class="full-width">
                      <mat-label>{{ 'TELACONVENIO.OBSERVACAO' | translate }}</mat-label>
                      <input matInput name="Observação" maxlength="100" [(ngModel)]="DadosConvenio.observacao">
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Seção de contato -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACONVENIO.CONTATO' | translate }}</h2>
          </div>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.EMAIL' | translate }}</mat-label>
                  <input matInput type="email" name="Email" required [(ngModel)]="DadosConvenio.email" #email="ngModel">
                  <mat-error *ngIf="email.invalid">{{getErrorMessageEmail() | translate }}</mat-error>
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROCLINICA.WEBSITE' | translate }}</mat-label>
                  <input matInput name="website" maxlength="100" (keyup)="mascaraText($event)" [(ngModel)]="DadosConvenio.website">
                </mat-form-field>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.TELEFONE' | translate }}</mat-label>
                  <input matInput name="Telefone" (keyup)="mascaraTelefone($event)" maxlength="15" [(ngModel)]="DadosConvenio.telefone">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.TELEFONEMOVEL' | translate }}</mat-label>
                  <input matInput name="Telefone Movel" (keyup)="mascaraTelefone($event)" maxlength="15" [(ngModel)]="DadosConvenio.telefoneMovel">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACADASTROCLINICA.TELEFONECOMERCIAL' | translate }} *</mat-label>
                  <input matInput name="Telefone Comercial" 
                    (keyup)="mascaraTelefone($event)"
                    (keypress)="mascaraTelefone($event)" 
                    (blur)="ValidaTelefoneComercial($any($event.target).value)" 
                    maxlength="15" minlength="14"
                    [(ngModel)]="DadosConvenio.telefoneComercial">
                </mat-form-field>
                <div class="field-error" *ngIf="TelComVal">{{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}</div>
                <div class="field-error" *ngIf="TelComValVasil && !TelComVal">{{ 'TELACADASTROCLINICA.ERROCAMPO' | translate }}</div>
              </div>
            </div>
          </div>
        </div>
  
        <!-- Seção de endereço -->
        <div class="section">
          <div class="section-header">
            <h2 class="section-title">{{ 'TELACONVENIO.ENDERECO' | translate }}</h2>
          </div>
          <div class="section-content">
            <div class="form-row">
              <div class="form-group col-md-6">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.RUA' | translate }}</mat-label>
                  <input matInput name="Rua" [(ngModel)]="DadosConvenio.rua">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-2">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.NUMERO' | translate }}</mat-label>
                  <input matInput name="Numero" [(ngModel)]="DadosConvenio.numero">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.COMPLEMENTO' | translate }}</mat-label>
                  <input matInput name="Complemento" [(ngModel)]="DadosConvenio.complemento">
                </mat-form-field>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-2">
                <ng-select 
                  class="modern-select"
                  [items]="dadosUF"
                  placeholder="{{ 'TELACONVENIO.UF' | translate }}" 
                  bindLabel="siglasUf"
                  bindValue="siglasUf" 
                  name="UF" 
                  (change)="CidadePorUF()" 
                  [selectOnTab]="true"
                  notFoundText="{{'TELACONVENIO.UFNAOENCONTRADA' | translate}}"
                  [(ngModel)]="DadosConvenio.uf">
                </ng-select>
              </div>
              
              <div class="form-group col-md-4">
                <ng-select 
                  class="modern-select"
                  [items]="dadosCidadeUf"
                  placeholder="{{ 'TELACONVENIO.MUNICIPIO' | translate }}" 
                  bindLabel="nmeCidade"
                  bindValue="idCidade" 
                  name="Municipio" 
                  [selectOnTab]="true"
                  notFoundText="{{'TELACONVENIO.UFNAOENCONTRADA' | translate}}"
                  [(ngModel)]="DadosConvenio.idCidade">
                </ng-select>
              </div>
              
              <div class="form-group col-md-4">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.BAIRRO' | translate }}</mat-label>
                  <input matInput name="Bairro" [(ngModel)]="DadosConvenio.bairro">
                </mat-form-field>
              </div>
              
              <div class="form-group col-md-2">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>{{ 'TELACONVENIO.CEP' | translate }}</mat-label>
                  <input matInput name="CEP" maxlength="10" mask="00000-000" [(ngModel)]="DadosConvenio.cep">
                </mat-form-field>
              </div>
            </div>
          </div>
        </div>
      </div>
  
      <div class="card-footer">
        <button class="btn btn-outline" (click)="LimparCampos()">
          <span class="material-icons">clear</span>
          {{ 'TELACONVENIO.LIMPAR' | translate }}
        </button>
        <button class="btn btn-success" (click)="Submit()">
          <span class="material-icons">save</span>
          {{ 'TELACONVENIO.SALVAR' | translate }}
        </button>
      </div>
    </div>
  </div>
  
  <!-- Modal Termos -->
  <ngx-smart-modal #Termos identifier="Termos" customClass="nsm-centered medium-modal">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">
          {{ 'TELACONVENIO.NOSSOSTERMOSDEUSO' | translate }}
          <span class="material-icons">copyright</span>
        </h2>
      </div>
      <div class="modal-body">
        <div class="terms-content">
          <p><b>1 Introdução</b> O Serviço xxx inclui recursos sociais e interativos. O uso do Serviço xxx se baseia
            em vários requisitos técnicos. Seu acordo conosco inclui esses Termos e Condições de Uso ("Termos") e
            nossa Política de Privacidade.</p>
          <p>(Os Termos, a Política de Privacidade e quaisquer termos adicionais com os quais você concorda, conforme
            discutido na seção Acordo integral, são referidos, juntos, como "Acordos".) Se você quiser analisar os
            termos dos Acordos, a versão
            efetiva dos Acordos poderá ser encontrada no site do xxx. Você confirma que leu e compreendeu os
            Acordos, que aceita esses Acordos e concorda em cumpri-los.</p>
          <!-- Texto dos termos continua... -->
        </div>
      </div>
      <div class="modal-footer">
        <p class="terms-question">{{ 'TELACONVENIO.CONCORDACOMTERMOS' | translate }}</p>
        <div>
          <button class="btn btn-outline" (click)="Termos.close()">
            {{ 'TELACONVENIO.NAO' | translate }}
          </button>
          <button class="btn btn-success" (click)="Termos.close()">
            {{ 'TELACONVENIO.SIM' | translate }}
          </button>
        </div>
      </div>
    </div>
  </ngx-smart-modal>
  
  <!-- Modal Excluir Item -->
  <ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title warning-title">
          <span class="material-icons warning-icon">warning</span> 
          {{ 'TELACONVENIO.EXCLUIRESTEUSUARIO' | translate }}
        </h2>
      </div>
      <div class="modal-body">
        <p class="warning-message">{{ 'TELACONVENIO.SERAEXCLUIDOPERMANENTEMENTE' | translate }}</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline" (click)="excluirItem.close()">
          {{ 'TELACONVENIO.NAO' | translate }}
        </button>
        <button class="btn btn-danger" (click)="InativarUsuario()">
          {{ 'TELACONVENIO.SIM' | translate }}
        </button>
      </div>
    </div>
  </ngx-smart-modal>
  
  <!-- Modal Editar Foto -->
  <ngx-smart-modal #ModalFoto identifier="ModalFoto" customClass="nsm-centered medium-modal">
    <div class="modal-container">
      <div class="modal-header">
        <h2 class="modal-title">{{ 'TELACADASTROCLINICA.FOTODEPERFIL' | translate }}</h2>
      </div>
      <div class="modal-body">
        <div class="image-controls">
          <button class="btn btn-outline" (click)="rotateLeft()">
            <span class="material-icons">rotate_left</span>
            {{ 'TELACADASTROCLINICA.GIRARAESQUERDA' | translate }}
          </button>
          <button class="btn btn-outline" (click)="rotateRight()">
            <span class="material-icons">rotate_right</span>
            {{ 'TELACADASTROCLINICA.GIRARADIREITA' | translate }}
          </button>
        </div>
        <div class="image-controls">
          <button class="btn btn-outline" (click)="flipHorizontal()">
            <span class="material-icons">flip</span>
            {{ 'TELACADASTROCLINICA.VIRARHORIZONTALMENTE' | translate }}
          </button>
          <button class="btn btn-outline" (click)="flipVertical()">
            <span class="material-icons">flip</span>
            {{ 'TELACADASTROCLINICA.VIRARVERTICALMENTE' | translate }}
          </button>
        </div>
        <div class="cropper-container">
          <image-cropper 
            [imageChangedEvent]="imageChangedEvent" 
            [maintainAspectRatio]="true" 
            [aspectRatio]="3/3"
            [onlyScaleDown]="true" 
            [roundCropper]="false" 
            outputType="base64"
            (imageCropped)="imageCropped($event)" 
            (imageLoaded)="imageLoaded()" 
            (cropperReady)="cropperReady()"
            (loadImageFailed)="loadImageFailed()" 
            [style.display]="showCropper ? null : 'none'"
            [alignImage]="'left'">
          </image-cropper>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-success" (click)="CortarImagem()">
          <span class="material-icons">content_cut</span>
          {{ 'TELACADASTROCLINICA.CORTAR' | translate }}
        </button>
      </div>
    </div>
  </ngx-smart-modal>