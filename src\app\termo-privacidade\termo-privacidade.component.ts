import { Component, OnInit } from "@angular/core";
import { UsuarioService } from "../service/usuario.service";
import { UsuarioLogadoService } from "../auth/usuarioLogado.service";
import { LocalStorageService } from "../service/LocalStorageService";
import { UsuarioLogado } from "../auth/UsuarioLogado";
import { AgendaService } from "../service/agenda.service";
import { ConsultaService } from "../service/consulta.service";

import { SpinnerService } from "../service/spinner.service";
import { Router } from "@angular/router";
import { CommonModule } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatCardModule } from "@angular/material/card";
import { MatIconModule } from "@angular/material/icon";
import { TranslateModule } from "@ngx-translate/core";
import { MatInputModule } from "@angular/material/input";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatButtonModule } from "@angular/material/button";

@Component({
  selector: "app-termo-privacidade",
  templateUrl: "./termo-privacidade.component.html",
  styleUrls: ["./termo-privacidade.component.scss"],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    TranslateModule,
    MatCheckboxModule,
    MatButtonModule,
  ]
})
export class TermoPrivacidadeComponent implements OnInit {
  constructor(
    private spinner: SpinnerService,
    private router: Router,
    private usuarioService: UsuarioService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,

    private agendaService: AgendaService,
    public consultaService: ConsultaService
  ) { }
  checkAceito?: boolean;
  // usuario: Usuario;

  CPF = "";
  Nome = "";
  ngOnInit() {
    // this.carregaFila();
    this.Nome = this.usuarioLogadoService.getNomeUsuario()!;
    this.CPF = this.usuarioLogadoService.getCPFUsuario()!;
    if (this.CPF != "") {
      this.CPF = this.CPF.replace(/\D/g, "");
      this.CPF = this.CPF.replace(/(\d{3})(\d)/, "$1.$2");
      this.CPF = this.CPF.replace(/(\d{3})(\d)/, "$1.$2");
      this.CPF = this.CPF.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
    }
  }

  primeiroAcesso() {

    this.usuarioService
      .AceitaPrivacidade(this.usuarioLogadoService.getIdUsuarioAcesso())
      .then(
        async (retorno) => {
          if (retorno == true) {
            this.localStorageService.Logado = true;
            const dataUsuario: UsuarioLogado = await this.usuarioService
              .getPerfilUsuarioLogado()
              .toPromise();
            this.spinner.hide();
            this.usuarioService.AtualizaDadosUsuarioLogado(dataUsuario);
          }
          if (this.localStorageService.TelaPrivacidadeLogin != "") {
            if (this.localStorageService.TelaPrivacidadeLogin == "Login")
              this.router.navigate(["/perfil"]);
            else if (
              this.localStorageService.TelaPrivacidadeLogin == "AcessoAgenda"
            )
              this.router.navigate(["filaespera"]);
            else this.router.navigate(["/streaming"]);
          } else this.router.navigate([""]);
          this.spinner.hide();
        },
        () => {
          this.spinner.hide();
          console.error("erro salvar Usuario ");
        }
      );
  }

  idConsulta?: number;
  carregaFila() {
    // MÉTODO DESABILITADO: Sistema de fila agora é baseado apenas em token para usuários não logados
    // Para usuários logados, usar o novo sistema de fila baseado em token
    console.warn('carregaFila() está obsoleto. Use o sistema de fila baseado em token.');
    this.spinner.hide();
  }

  CancelarConsulta() {


    this.agendaService
      .InativarAgendamento(
        this.idConsulta,
        this.usuarioLogadoService.getIdUsuarioAcesso(),
        "Paciente desistiu da Fila de Espera"
      )
      .subscribe(
        () => {
          this.Logoff();
          this.spinner.hide();
          ;
        },
        (err) => {
          this.spinner.hide();
          console.error(err);

        }
      );
  }

  Logoff() {
    var telaPrivacidadeLogin = this.localStorageService.TelaPrivacidadeLogin;

    if (telaPrivacidadeLogin != "") {
      this.CancelarConsulta();

      this.usuarioLogadoService.logout();
      localStorage.clear();
      sessionStorage.clear();
      if (telaPrivacidadeLogin == "Login") this.router.navigate(["login"]);
      else if (telaPrivacidadeLogin == "AcessoAgenda")
        this.router.navigate([""]);
      else this.router.navigate(["atendimento/" + telaPrivacidadeLogin]);
    } else {
      this.usuarioLogadoService.logout();
      localStorage.clear();
      sessionStorage.clear();
      this.router.navigate([""]);
    }
  }
}
