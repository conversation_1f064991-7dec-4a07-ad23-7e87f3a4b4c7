import { ModelChangeBase } from "../Util/modelChangeBase";
import { FormsModule,
ReactiveFormsModule, NG_VALUE_ACCESSOR } from "@angular/forms";
import { Component, forwardRef, Input } from "@angular/core";
import { DateInputFormatPipe } from "../Util/date-format.pipe";
import { CommonModule } from "@angular/common";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";

@Component({
    selector: "input-date",
    templateUrl: "./input-date.component.html",
    styleUrls: ["./input-date.component.sass"],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => InputDateComponent),
            multi: true
        }
    ],
      standalone: true,
    imports: [
    MatInputModule,
    DateInputFormatPipe,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule
]
})
export class InputDateComponent extends ModelChangeBase {
    @Input() label: string = '';
    @Input() name: string  = '';
    @Input() placeholder: string = '';
    @Input() required: boolean = false;
    @Input() disabled: boolean = false;

    constructor() {
        super();
    }

    onInput(event: Event): void {
        const input = event.target as HTMLInputElement;
        this.value = input.value; // Isso invoca o setter e chama updateChanges()
        this.onTouched(); // Opcional: para notificar que o campo foi tocado
      }
      
}
