/*Material Content  -- My Custom*/

/*TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
.mat-mdc-form-field .mat-mdc-select.mat-select-invalid .mat-select-arrow {
    color: #666 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-mdc-form-field.mat-form-field-invalid .mat-form-field-label {
    color: #666 !important;
}

.mat-mdc-form-field.mat-form-field-invalid .mat-form-field-label .mat-form-field-required-marker,
.mat-form-field.mat-form-field-invalid .mat-form-field-label.mat-accent {
    color: #666 !important;
}

.mat-form-field-invalid .mat-mdc-input-element,
.mat-warn .mat-input-element {
    color: #666;
}

.mat-mdc-form-field.mat-form-field-invalid .mat-form-field-ripple,
.mat-form-field.mat-form-field-invalid .mat-form-field-ripple.mat-accen {
    color: rgba(0, 0, 0, 0.42);
}

.mat-mdc-form-field.mat-form-field-invalid .mat-form-field-ripple,
.mat-form-field.mat-form-field-invalid .mat-form-field-ripple.mat-accent {
    background-color: rgba(0, 0, 0, 0.42) !important;
}

.mat-step-header .mat-step-icon {
    background-color: #3f5fb5 !important;
    color: #fff !important;
}

/* .mat-badge-content {
    color: #fff;
    background: #d04b4b;
} */

/*TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
.mat-radio-label-content {
    display: inline-block;
    order: 0;
    line-height: inherit;
    padding-left: 5px !important;
    padding-right: 10px !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-appearance-outline .mat-form-field-outline-thick {
    color: rgb(23, 138, 255) !important;
}

.mat-step-label-selected {
    padding: 10px !important;
    font-size: 17px !important;
    font-weight: 500;
    border-radius: 2px;
    text-align: center;
    border-bottom: 2px solid #2b4f75;
}

.mat-step-label {
    font-size: 17px !important;
}

.mat-drawer-backdrop {
    position: fixed !important;
}

.cdk-drag-animating {
    transition: transform 450ms cubic-bezier(0, 0, 0.2, 1);
}

/*TODO(mdc-migration): The following rule targets internal classes of menu that may no longer apply for the MDC version.*/
.mat-menu-adjust {
    left: 10px !important;
    margin-top: -35px !important;
}
.cadastroparceiro {
    margin-top: -65px !important;
    margin-left: 78px !important;
    display: list-item;
    position: absolute;
}

.mat-mdc-menu-content:not(:empty) {
    padding: 0!important;
}

/*TODO(mdc-migration): The following rule targets internal classes of menu that may no longer apply for the MDC version.*/
.mat-menu-custom {
    /* background: linear-gradient(45deg, #2573c2 0%, #2573c2 43%, #1265b9 71%, #1265b9 100%); */
    color: white;
    padding: 5px;
    background-color: #052b3b;
}

/* .mat-menu-custom:hover {
    background: #0f559c!important;
} */

.mat-stepper-horizontal-line {
    display: none !important;
}

.mat-expansion-panel-body {
    padding: 0!important;
}

.mat-expansion-panel:not([class*="mat-elevation-z"]) {
    box-shadow: unset!important;
}

.mat-expansion-panel {
    position: relative!important;
}

.mat-expansion-panel {
    background: unset!important;
    color: white;
}

.mat-horizontal-stepper-header-container {
    white-space: nowrap;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ngx-editor .ngx-wrapper .ngx-editor-textarea {
    max-height: 224px;
    height: 224px !important;
}

.mat-mdc-chip.mat-mdc-standard-chip {
    background-color: transparent;
    color: rgb(189, 193, 198);
    border-radius: 32px;
    padding: 10px;
}

.mat-mdc-tab-group.mat-primary .mat-ink-bar,
.mat-mdc-tab-nav-bar.mat-primary .mat-ink-bar {
    background-color: #1265b9 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
.mat-checkbox-layout {
    cursor: inherit;
    align-items: baseline;
    vertical-align: middle;
    display: inline-flex;
    white-space: initial !important;
}

.mat-badge-large .mat-badge-content {
    font-size: 17px !important;
}

.mat-badge-large.mat-badge-overlap.mat-badge-after .mat-badge-content {
    right: 15px !important;
}

.mat-mdc-input-element {
    caret-color: #2b4f75 !important;
}

.p-none {
    padding: 0 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-mdc-form-field.mat-focused .mat-form-field-label {
    color: #2b4f75 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-mdc-form-field.mat-focused .mat-form-field-ripple {
    background-color: #2b4f75 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of option that may no longer apply for the MDC version.*/
.mat-primary .mat-mdc-option.mat-selected:not(.mat-option-disabled) {
    color: #2b4f75;
}

.mat-accordion .mat-expansion-panel:not(.mat-expanded),
.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing) {
    border-radius: 0;
    border: 1px solid #e4e4e4;
}

.mat-badge-accent .mat-badge-content {
    color: white !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin: 0em 0 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
.mat-tab-body-content {
    height: 100%;
    overflow: hidden !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
.mat-tab-label .mat-tab-label-content {
    display: inline-flex;
    justify-content: center;
    font-size: 12px;
    align-items: center;
    white-space: nowrap;
}

/*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
.mat-tab-labels {
    display: flex;
    justify-content: center;
}

/*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
.mat-tab-label {
    height: 48px;
    padding: 0 24px;
    cursor: pointer;
    box-sizing: border-box;
    opacity: 0.6;
    min-width: 60px !important;
    padding: 5px !important;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    position: relative;
    margin-right: 10px;
    margin-left: 10px;
}

.mat-drawer-inner-container {
    width: 100%;
    height: 100%;
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch;
}

/*TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
.mat-mdc-radio-button.mat-accent .mat-radio-inner-circle {
    background-color: #4b6fd0 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
.mat-mdc-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle {
    border-color: #272727 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of slide-toggle that may no longer apply for the MDC version.*/
.mat-mdc-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar {
    background-color: rgb(75, 111, 208) !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of slide-toggle that may no longer apply for the MDC version.*/
.mat-mdc-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb {
    background-color: #0983ff !important;
    border: 1px solid #0983ff;
}


/*      
.input-align{ margin-right: 20px;} */

/*TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
.mat-checkbox-checked.mat-accent .mat-checkbox-background,
.mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: #3f51b5 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of radio that may no longer apply for the MDC version.*/
.mat-mdc-radio-button.mat-accent .mat-radio-ripple .mat-ripple-element {
    background-color: rgba(64, 93, 255, 0.26) !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-appearance-legacy .mat-form-field-wrapper {
    padding-bottom: 1.25em;
    font-size: 12px;
    font-weight: 600;
    /* max-width: 250px;
    display: inline-block; */
}


.ng-select .ng-select-container .ng-value-container .ng-placeholder {
    font-size: 12px;
    font-weight: 500;
    color: rgb(193, 193, 193) !important;
}

.ng-select .ng-select-container .ng-value-containe r {
    padding: 0px !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
.mat-form-field-appearance-legacy .mat-form-field-underline {
    background-color: rgba(0, 0, 0, 0.42) !important;
}

.ng-select.ng-select-single .ng-select-container .ng-value-container,
.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px !important;
    font-weight: 500;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked {
    background: rgba(6, 24, 41, 0.15)!important;
    color: rgba(0, 0, 0, 0.87);
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked:hover {
    background: rgba(6, 24, 41, 0.15);
    color: rgba(0, 0, 0, 0.87);
}

.ng-select .ng-select-container {
    align-items: baseline;
    min-height: 33.5px !important;
    /* min-height: 45.5px!important; */
}

/*TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version.*/
.mat-mdc-progress-bar[mode="buffer"] .mat-progress-bar-background {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    animation: mat-progress-bar-background-scroll 250ms infinite cubic-bezier(0.67, 0.04, 0, 0.88);
    display: block;
    background: #348bc100;
}

/*TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version.*/
.mat-progress-bar-background {
    fill: #348bc114;
}

.mat-mdc-card {
    border: 1px solid #ddd;
}

/*TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version.*/
.mat-progress-bar-fill::after {
    background-color: #0080c7 !important;
}

/*TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version.*/
.mat-progress-bar-background {
    fill: #348bc138;
}


/* .mat-progress-bar-buffer {
  background-color: #0080c7!important;
  } */

  .mat-drawer-content {
  	position: relative;
  	z-index: 1;
  	display: block;
  	height: 100%;
  	overflow: unset !important;
  }

  .mat-body{
  	margin-left: 15px;
  	margin-top: 10px;
  }

  /*TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
  .mat-button-toggle-checked {
  	background-color: #02376d!important;
  	color: white!important;
  }
 
  .panel-selector{
    width: 65px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .panel-selector small{
    font-size: 0.7rem;
    font-weight: 700;
    display: inline-flex;
}

 

   
  @media(max-width:470px){
  	/*TODO(mdc-migration): The following rule targets internal classes of menu that may no longer apply for the MDC version.*/
  	.mat-menu-adjust{
  		margin-top: -72px!important;
  		margin-left: -14px!important;
  	}

  	

  }
