
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Medico } from '../model/medico';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class AtendenteService {

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });



    public getAtendente(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Atendente/GetCarregaAtendente/' + id);
        
    }
    public getAtendenteChat(id:any): Observable<any> {
        return this.http.get(environment.apiEndpoint + '/Atendente/GetCarregaAtendenteChat/' + id);
    }
    public getGridAtendenteInativos(inicio:any, fim:any, pesquisa:any,idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Atendente/getGridAtendenteInativos', { params });
    }
    
    public getGridAtendente(inicio:any, fim:any, pesquisa:any,idClinica:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        params = params.append('idClinica', String(idClinica));
        return this.http.get(environment.apiEndpoint + '/Atendente/getGridAtendente', { params });
    }
    
    public InativarAtendente(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Atendente/InativarAtendente/' + id + '/' + idUsuario);
    }
    public AtivarAtendente(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Atendente/AtivarAtendente/' + id + '/' + idUsuario);
    }
    public salvarMedico(cliente: Medico): Observable<any> {
        this.spinner.show();       
        return this.http.post(environment.apiEndpoint + '/Medico/', cliente);
    }
}