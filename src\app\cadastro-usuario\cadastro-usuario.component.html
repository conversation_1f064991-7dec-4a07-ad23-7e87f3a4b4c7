<div class="analysis-container">  
    <!-- <PERSON><PERSON><PERSON><PERSON> principal -->
    <div class="card">
        <!-- <PERSON><PERSON><PERSON><PERSON>ho -->
        <div class="card-header">
          <div class="header-icon-title">
            <div class="icon-box">
              <span class="material-icons">group</span>
            </div>
            <h1>{{ 'TELACADASTROUSUARIO.CADASTROATENDENTE' | translate }}</h1>
          </div>
          <button class="btn-back" onclick='history.go(-1)'>
            <span class="material-icons">arrow_back</span>
          </button>
        </div>
    
        <div class="card-content">
          <!-- Foto de perfil -->
          <div class="profile-section">
            <label for="imageperfilusuario" class="profile-photo-label">
              <div class="profile-photo-container">
                <img src="{{ ImagemPessoa }}" alt="Foto de Perfil">
                <div class="profile-photo-overlay">
                  <span class="material-icons">photo_camera</span>
                </div>
              </div>
              <span class="photo-hint">{{ 'TELACADASTROUSUARIO.FOTODEPERFIL' | translate }}</span>
            </label>
            <input type="file" id="imageperfilusuario" (change)="AlterarImagemClinica($event)" (click)="LimpaCampoFile()">
            <input type="hidden" id="Foto" name="Foto" [(ngModel)]="Dados.foto">
          </div>
    
          <div class="form-container">
            <!-- Dados Pessoais -->
            <div class="form-section">
              <div class="section-header">
                <span class="section-marker"></span>
                <h2>{{ 'TELACADASTROUSUARIO.DADOSPESSOAIS' | translate }}</h2>
              </div>
    
              <div class="form-grid">
                <!-- Nome -->
                <div class="form-field field-large">
                  <mat-form-field appearance="outline">
                    <mat-label>Nome*</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.NOME' | translate }}" 
                      id="Nome" name="Nome" required maxlength="100"
                      (keyup)="mascaraText($any($event.target).value, 'Nome')" 
                      (change)="mascaraText($any($event.target).value, 'Nome')"
                      [(ngModel)]="Dados.nome">
                    <mat-error *ngIf="Nome.invalid">{{getErrorMessageNome() | translate }}</mat-error>
                  </mat-form-field>
                </div>
    
                <!-- Data Nascimento -->
                <div class="form-field">
                  <mat-form-field appearance="outline">
                    <mat-label>Data de Nascimento</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.DATADENASCIMENTO' | translate }}"
                      name="Data de Nascimento" id="DtaNasc" 
                      (keypress)="mascaraData($event)"
                      [(ngModel)]="Dados.dtaNascimento" maxlength="10"
                      (blur)="ValidaDta($any($event.target).value)">
                  </mat-form-field>
                  <span class="error-text" *ngIf="Dtanasc">
                    {{ 'TELACADASTROUSUARIO.DATAINVALIDA' | translate }}
                  </span>
                </div>
    
                <!-- Sexo -->
                <div class="form-field">
                  <ng-select 
                    [items]="DadosSexo"
                    placeholder="{{ 'TELACADASTROUSUARIO.SEXO' | translate }}" 
                    (focus)="carregaDadosSexo()"
                    name="Sexo" 
                    notFoundText="Sexo não encontrado" 
                    [selectOnTab]="true"
                    [(ngModel)]="campoSexo"
                    class="custom-select">
                  </ng-select>
                </div>
    
                <!-- CPF -->
                <div class="form-field">
                  <mat-form-field appearance="outline">
                    <mat-label>CPF*</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.CPF' | translate }}"
                      (blur)='validarCpf($any($event.target).value)' 
                      name="CPF"
                      (keypress)="mascaraCpf('###.###.###-##', $event)" 
                      mask="000.000.000-00"
                      maxlength="14" minlength="14" required 
                      [(ngModel)]="Dados.cpf" id="CPF">
                  </mat-form-field>
                  <span class="error-text" *ngIf="campoCPFInvalido">CPF inválido</span>
                  <span class="error-text" *ngIf="campoCPFVazil && !campoCPFInvalido">
                    Esse campo precisa ser preenchido
                  </span>
                </div>
    
                <!-- Email -->
                <div class="form-field field-large">
                  <mat-form-field appearance="outline">
                    <mat-label>Email*</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.EMAIL' | translate }}" 
                      type="email" name="Email" 
                      (change)='validarEmail($any($event.target).value)'
                      id="email" required 
                      [(ngModel)]="Dados.email">
                  </mat-form-field>
                  <span class="error-text" *ngIf="campoEmailInvalido">Email inválido</span>
                  <span class="error-text" *ngIf="campoEmailVazil && !campoEmailInvalido">
                    Esse campo precisa ser preenchido
                  </span>
                </div>
    
                <!-- Telefone -->
                <div class="form-field">
                  <mat-form-field appearance="outline">
                    <mat-label>Telefone</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.TELEFONE' | translate }}"
                      name="Telefone" 
                      (keyup)="mascaraTelefone($event)"
                      (keypress)="mascaraTelefone($event)" 
                      (change)="ValidaTelefone($any($event.target).value)"
                      maxlength="15" minlength="14" 
                      [(ngModel)]="Dados.telefone">
                  </mat-form-field>
                  <span class="error-text" *ngIf="TelVal">
                    {{ 'TELACADASTROUSUARIO.TELEFONEINVALIDO' | translate }}
                  </span>
                </div>
    
                <!-- Celular -->
                <div class="form-field">
                  <mat-form-field appearance="outline">
                    <mat-label>Celular*</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.CELULAR' | translate }}"
                      name="Telefone Movel" 
                      (change)="validarTelMovel($any($event.target).value)"
                      (keyup)="mascaraTelefone($event)" 
                      (keypress)="mascaraTelefone($event)"
                      maxlength="15" minlength="14" id="telcelular" required
                      [(ngModel)]="Dados.telefoneMovel">
                  </mat-form-field>
                  <span class="error-text" *ngIf="TelMovVal">
                    {{ 'TELACADASTROUSUARIO.TELEFONEINVALIDO' | translate }}
                  </span>
                  <span class="error-text" *ngIf="TelMovValVasil && !TelMovVal">
                    {{ 'TELACADASTROUSUARIO.ESSECAMPOPRECISASERPREENCHIDO' | translate }}
                  </span>
                </div>
    
                <!-- Telefone Comercial -->
                <div class="form-field">
                  <mat-form-field appearance="outline">
                    <mat-label>Telefone Comercial</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.TELEFONECOMERCIAL' | translate }}"
                      name="Telefone Comercial" 
                      (keyup)="mascaraTelefone($event)"
                      (keypress)="mascaraTelefone($event)"
                      (change)="ValidaTelefoneComercial($any($event.target).value)" 
                      maxlength="15" minlength="14" 
                      [(ngModel)]="Dados.telefoneComercial">
                  </mat-form-field>
                  <span class="error-text" *ngIf="TelComVal">
                    {{ 'TELACADASTROUSUARIO.TELEFONEINVALIDO' | translate }}
                  </span>
                </div>
    
                <!-- Clínicas -->
                <div class="form-field field-large">
                  <mat-form-field appearance="outline">
                    <mat-label>Clínicas Correspondentes*</mat-label>
                    <mat-select
                      placeholder="{{ 'TELACADASTROPACIENTE.CLINICASCORRESPONDENTE' | translate }}"
                      required multiple name="clinicas" 
                      (keyup)="mascaraText($event, 'clinicas')"
                      (blur)="ValidaClinicas($any($event.target).value)" 
                      [formControl]="clinicas">
                      <mat-option *ngFor="let item of DadosClinicas; let i = index" [value]="item">
                        {{item.desClinica | truncate : 40 : "…"}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                  <span class="error-text" *ngIf="clinicaVal">
                    {{ 'TELACADASTROUSUARIO.UMACLINICAPRECISASERSELECIONADA' | translate }}
                  </span>
                </div>
              </div>
            </div>
    
            <!-- Endereço -->
            <div class="form-section">
              <div class="section-header">
                <span class="section-marker"></span>
                <h2>{{ 'TELACADASTROUSUARIO.ENDEREÇO' | translate }}</h2>
              </div>
    
              <div class="form-grid">
                <!-- Rua -->
                <div class="form-field field-large">
                  <mat-form-field appearance="outline">
                    <mat-label>Rua</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.RUA' | translate }}" 
                      name="Rua" [(ngModel)]="Dados.rua">
                  </mat-form-field>
                </div>
    
                <!-- Número -->
                <div class="form-field field-small">
                  <mat-form-field appearance="outline">
                    <mat-label>Número</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.NUMERO' | translate }}" 
                      name="n°" maxlength="10" 
                      (keyup)="mascaraNumeros($event)" [(ngModel)]="Dados.numero">
                  </mat-form-field>
                </div>
    
                <!-- Complemento -->
                <div class="form-field field-medium">
                  <mat-form-field appearance="outline">
                    <mat-label>Complemento</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.COMPLEMENTO' | translate }}"
                      name="Complemento" [(ngModel)]="Dados.complemento">
                  </mat-form-field>
                </div>
    
                <!-- UF -->
                <div class="form-field">
                  <ng-select 
                    [items]="dadosUF"
                    placeholder="{{ 'TELACADASTROUSUARIO.UF' | translate }}" 
                    bindLabel="siglasUf"
                    bindValue="siglasUf" 
                    name="UF" 
                    (change)="CidadePorUF()" 
                    [selectOnTab]="true"
                    notFoundText="{{ 'TELACADASTROUSUARIO.UFNAOENCONTRADA' | translate }}" 
                    [(ngModel)]="Dados.uf"
                    class="custom-select">
                  </ng-select>
                </div>
    
                <!-- Município -->
                <div class="form-field field-medium">
                  <ng-select 
                    [items]="dadosCidadeUf"
                    placeholder="{{ 'TELACADASTROUSUARIO.MUNICIPIO' | translate }}" 
                    bindLabel="nmeCidade"
                    bindValue="idCidade" 
                    name="Municipio" 
                    [selectOnTab]="true"
                    notFoundText="{{ 'TELACADASTROUSUARIO.UFNAOENCONTRADA' | translate }}"
                    [(ngModel)]="Dados.idCidade"
                    class="custom-select">
                  </ng-select>
                </div>
    
                <!-- Bairro -->
                <div class="form-field field-medium">
                  <mat-form-field appearance="outline">
                    <mat-label>Bairro</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.BAIRRO' | translate }}" 
                      name="Bairro" [(ngModel)]="Dados.bairro">
                  </mat-form-field>
                </div>
    
                <!-- CEP -->
                <div class="form-field">
                  <mat-form-field appearance="outline">
                    <mat-label>CEP</mat-label>
                    <input matInput placeholder="{{ 'TELACADASTROUSUARIO.CEP' | translate }}" 
                      name="CEP" mask="00000-000" maxlength="9" [(ngModel)]="Dados.cep">
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
        </div>
    
        <div class="card-actions">
          <button class="btn btn-outline" (click)="LimparCampos()">
            <span class="material-icons">refresh</span>
            <span>{{ 'TELACADASTROUSUARIO.LIMPAR' | translate }}</span>
          </button>
          <button class="btn btn-primary" (click)="OnSubmit()">
            <span class="material-icons">save</span>
            <span>{{ 'TELACADASTROUSUARIO.SALVAR' | translate }}</span>
          </button>
        </div>
      </div>
    </div>