<div class="consultation-container yes-desktop">
    <div class="row col-12" style="height: 85vh; overflow: hidden; flex-direction: column;">
        <!-- Coluna do Histórico e Info do Paciente -->
        <div class="col-lg-2 sidebar-column">
            <!-- Informações do Paciente -->
            <div class="patient-info-card">
                <div class="patient-avatar">
                    <img src={{ImagemPessoa}} alt="Foto do Paciente">
                </div>
                <div class="patient-details">
                    <mat-form-field appearance="outline" class="patient-name-field">
                        <input class="patient-name" matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled
                            name="Nome" [(ngModel)]="DadosInformUsuario.nome">
                    </mat-form-field>

                    <button mat-icon-button class="info-button" (click)="informacao()" 
                           title="{{ 'TELAAGENDA.INFORMACOESPACIENTE' | translate }}">
                        <mat-icon>info</mat-icon>
                    </button>
                </div>
            </div>

            <!-- Histórico do Paciente -->
            <div class="history-container">
                <h4 class="history-title">{{ 'TELAINICIO.HISTORICO' | translate }}</h4>
                <hr class="section-divider"/>
                
                <!-- Loading Spinner -->
                <div class="upload-container text-center" *ngIf="spinnerHistorico == true">
                    <img src="assets/build/img/spinner.gif" class="loading-spinner">
                </div>

                <!-- Histórico de Consultas -->
                <div class="history-scroll" *ngIf="spinnerHistorico != true" #scrollMe>
                    <div class="history-item" *ngFor="let item of dadosHistorico;let i = index;" 
                         (click)="flgConsultaAnonimo()">
                        <div class="history-date" (click)="historico = true">
                            {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                        </div>
                        <div class="history-indicators">
                            <mat-icon *ngIf="item.anonimo != null" matTooltip="Anotação anônima">visibility_off</mat-icon>
                            <mat-icon *ngIf="item.anexo.length > 0" matTooltip="Contém anexos">archive</mat-icon>
                            <mat-icon *ngIf="item.dadosCorpo != null" matTooltip="Contém dados físicos">accessibility_new</mat-icon>
                        </div>
                        <hr class="item-divider"/>
                    </div>
                </div>
            </div>
        </div>

        <!-- Coluna Principal da Consulta -->
        <div class="col-lg-10 main-column">
            <div class="row" style="width: 950px;">
                <!-- Área principal (consulta, anotações ou anexos) -->
                <div [className]="!corpo && !anexo ? 'col-lg-12' : 'col-lg-9'">
                    <div class="consultation-content">
                        <!-- Timer da Consulta -->
                        <div class="consultation-timer">
                            <label>{{hora}}:{{minutos | number:'2.0'}}:{{segundos | number:'2.0'}}</label>
                        </div>

                        <!-- Histórico de Consulta Selecionado -->
                        <div class="consultation-history" #scrollMe 
                             [ngClass]="{'hidden': !consulta && !anonimo && !resultadosExame }">
                            <div *ngFor="let item of dadosHistorico;let i = index">
                                <div class="history-consultation" *ngIf="historico">
                                    <div class="history-card" id="{{ item.posicao }}" #destinationRef>
                                        <div class="history-header">
                                            <span>
                                                {{ 'TELASTREAMING.CONSULTADIA' | translate }}
                                                {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                                                {{ 'TELASTREAMING.MEDICO' | translate }} {{item.medico}}
                                            </span>
                                        </div>

                                        <div class="history-content">
                                            <div *ngIf="item.obsConsulta != null && item.obsConsulta != ''">
                                                <hr class="dotted-divider" *ngIf="item.obsConsulta != null && item.obsConsulta != ''">
                                                <span class="history-observation">
                                                    {{ 'TELASTREAMING.PRONTUARIO' | translate }} {{item.obsConsulta}}
                                                </span>
                                            </div>

                                            <div *ngIf="item.receita != null" class="prescription-data">
                                                <span>Receituário:<br>{{ item.receita }}</span>
                                            </div>
                                        </div>

                                        <!-- Dados do Paciente -->
                                        <div class="patient-data-panel" *ngIf="item.dadosCorpo!=null">
                                            <mat-accordion>
                                                <mat-expansion-panel hideToggle (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                                                    <mat-expansion-panel-header>
                                                        <mat-panel-title>
                                                            <mat-icon>accessibility_new</mat-icon>
                                                            <span>Dados do Paciente</span>
                                                        </mat-panel-title>
                                                    </mat-expansion-panel-header>
                                                    <div class="patient-metrics">
                                                        <div class="metric-item">
                                                            <span>Peso: {{item.dadosCorpo.peso}}</span>
                                                        </div>
                                                        <div class="metric-item">
                                                            <span>Altura: {{item.dadosCorpo.altura}}</span>
                                                        </div>
                                                        <div class="metric-item">
                                                            <span>IMC: {{item.dadosCorpo.imc}}</span>
                                                        </div>
                                                        <div class="metric-item">
                                                            <span>Pressão: {{item.dadosCorpo.pressao}}</span>
                                                        </div>
                                                        <div class="metric-item">
                                                            <span>Batimento: {{item.dadosCorpo.batimento}}</span>
                                                        </div>
                                                        <div class="metric-item">
                                                            <span>Temperatura: {{item.dadosCorpo.temperatura}}</span>
                                                        </div>
                                                    </div>
                                                </mat-expansion-panel>
                                            </mat-accordion>
                                        </div>

                                        <!-- Anotações Anônimas -->
                                        <div class="anonymous-panel" *ngIf="item.anonimo != '' && item.anonimo != null && historico">
                                            <mat-accordion>
                                                <mat-expansion-panel hideToggle (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                                                    <mat-expansion-panel-header>
                                                        <mat-panel-title>
                                                            <img src="assets/build/img/incognito.svg" class="anonymous-icon">
                                                            <span>{{ 'TELASTREAMING.ANONIMO' | translate }}</span>
                                                        </mat-panel-title>
                                                    </mat-expansion-panel-header>
                                                    <div class="anonymous-content">
                                                        <span>{{item.anonimo}}</span>
                                                    </div>
                                                </mat-expansion-panel>
                                            </mat-accordion>
                                        </div>

                                        <!-- Anexos -->
                                        <div class="attachments-panel" *ngIf="dadosHistorico[i].anexo.length > 0 && historico">
                                            <mat-accordion>
                                                <mat-expansion-panel hideToggle (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                                                    <mat-expansion-panel-header>
                                                        <mat-panel-title>
                                                            <i class="fas fa-paperclip"></i>
                                                            <span>{{ 'TELASTREAMING.ANEXOS' | translate }}</span>
                                                        </mat-panel-title>
                                                    </mat-expansion-panel-header>
                                                    <div class="attachments-list">
                                                        <div class="attachment-item" *ngFor="let anex of dadosHistorico[i].anexo">
                                                            <div class="attachment-bubble" id="colapsoAnexo+{{i}}"
                                                                *ngIf="tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& anex.flgVisualizacaoUsuario==true">
                                                                <div class="attachment-content">
                                                                    <i (click)="BaixarArquivo(anex.chave,anex.anexo)" class="fa fa-download" title="Download arquivo"></i>
                                                                    <span class="attachment-name" title="{{anex.anexo}}">{{anex.anexo}}</span>
                                                                </div>

                                                                <div class="attachment-meta">
                                                                    <small class="attachment-date">{{anex.dtaCadastro | date: 'dd/MM/yyyy HH:mm'}}</small>
                                                                    <small class="attachment-user" title="{{anex.usuario}}">{{anex.usuario}}</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </mat-expansion-panel>
                                            </mat-accordion>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Visualização de Resultados de Exames -->
                        <div class="exams-results" *ngIf="resultadosExame">
                            <h3 class="section-title">Resultados</h3>
                            <table class="exams-table">
                                <tbody *ngFor="let item of dadosexame">
                                    <tr>
                                        <th class="exam-description">
                                            <div class="exam-label">Descrição Exame</div>
                                            <div class="exam-value">{{item.des}}</div>
                                        </th>
                                        <th class="exam-code">
                                            <div class="exam-label">Cod.Exame</div>
                                            <div class="exam-value">{{item.cod | truncate : 30 : "…"}}</div>
                                        </th>
                                        <th class="exam-result">
                                            <div class="exam-label">Resultado</div>
                                            <div class="exam-value">{{item.result | truncate : 30 : "…"}}</div>
                                        </th>
                                        <th class="exam-date">
                                            <div class="exam-label">Data</div>
                                            <div class="exam-value">{{item.data | date: 'dd/MM/yyyy'}}</div>
                                        </th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Anotações Anônimas -->
                        <div class="anonymous-notes" *ngIf="anonimo">
                            <h3 class="section-title">{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</h3>
                            <mat-form-field appearance="outline" class="notes-field">
                                <textarea matInput id="txtAnonimo" name="Observação"
                                    (keyup.enter)="addParagrafoAnonimo()" [(ngModel)]="DesAnonimo"
                                    class="notes-textarea"></textarea>
                            </mat-form-field>
                        </div>

                        <!-- Consulta -->
                        <div class="consultation-notes" *ngIf="consulta">
                            <h3 class="section-title">{{ 'TELASTREAMING.CONSULTAS' | translate }}</h3>
                            <mat-form-field appearance="outline" class="notes-field">
                                <textarea matInput id="txtConsulta" name="Observação"
                                    (keyup.enter)="addParagrafo()" [(ngModel)]="DesConsulta"
                                    class="notes-textarea"></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <!-- Coluna de Anexos ou Dados do Paciente -->
                <div class="col-lg-3 sidebar-right" *ngIf="corpo || anexo">
                    <!-- Botão Fechar -->
                    <button *ngIf="anexo || corpo" (click)="anexo ? (anexo = false) : (corpo = false)" 
                            aria-label="Close" class="close-button" type="button">
                        <mat-icon>close</mat-icon>
                    </button>
                    
                    <!-- Área de Anexos -->
                    <div *ngIf="anexo" class="attachments-area">
                        <!-- Loading Spinner -->
                        <div class="spinner-container" *ngIf="spinnerAnexo == true">
                            <img src="assets/build/img/spinner.gif" class="loading-spinner">
                        </div>
                        
                        <!-- Área para Upload de Arquivos -->
                        <div *ngIf="spinnerAnexo == false" id="divArquivo" class="dropzone-container"
                            (dragover)="destacaBorda($event)" (dragleave)="tiraBorada($event)"
                            (drop)="fileuploadesquerdo($event)">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <p class="dropzone-text">
                                {{ 'TELASTREAMING.AREAPARAIMPORTARARQUIVOS' | translate }}
                                <br><br>
                                {{ 'TELASTREAMING.OU' | translate }}
                            </p>

                            <label for="file" class="upload-button">
                                {{ 'TELASTREAMING.CARREGARARQUIVO' | translate }}
                            </label>
                            <input type="file" id="file" (change)="SubirArquivoConsulta($event)" accept=".xls,.xlsx,.pdf" />
                        </div>
                        
                        <!-- Lista de Anexos -->
                        <div class="attachments-list-container">
                            <div class="attachment-group" *ngFor="let item of DadosanexConsulta">
                                <!-- Anexos do Usuário Atual -->
                                <div class="attachment-wrapper" *ngIf="item.idUsuario == usuario">
                                    <div class="user-attachment">
                                        <div class="attachment-content">
                                            <i (click)="BaixarArquivo(item.chave,item.nomeArquivo)" class="fa fa-download" title="Download arquivo"></i>
                                            <span class="attachment-name" title="{{item.nomeArquivo}}">
                                                {{item.nomeArquivo | truncate : 14 : "…"}}
                                            </span>
                                        </div>
                                        <div class="attachment-meta">
                                            <span class="attachment-date">{{item.dtacadastro | date: 'dd/MM/yyyy HH:mm'}}</span>
                                            <button mat-icon-button class="delete-button" (click)="DeleteAnexo(item.chave, item.idAnexo)">
                                                <mat-icon>delete</mat-icon>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Anexos de Outros Usuários -->
                                <div class="attachment-wrapper" 
                                     *ngIf="item.idUsuario != usuario && (tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& item.flgVisualizacaoUsuario==true)">
                                    <div class="other-attachment">
                                        <div class="attachment-content">
                                            <i (click)="BaixarArquivo(item.chave,item.nomeArquivo)" class="fa fa-download" title="Download arquivo"></i>
                                            <span class="attachment-name" title="{{item.nomeArquivo}}">
                                                {{item.nomeArquivo}}
                                            </span>
                                        </div>
                                        <div class="attachment-meta">
                                            <span class="attachment-date">{{item.dtacadastro | date: 'dd/MM/yyyy HH:mm'}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Área de Dados do Paciente -->
                    <div *ngIf="corpo" class="patient-data-area">
                        <div class="data-container">
                            <div class="data-header">
                                <h4>{{ 'TELASTREAMING.DADOSPACIENTE' | translate }}</h4>
                            </div>

                            <div class="patient-illustration">
                                <img src="assets/build/img/corpoPaciente.png" class="body-image">
                            </div>

                            <div class="patient-metrics-form">
                                <mat-form-field appearance="outline">
                                    <mat-label>{{ 'TELASTREAMING.PESO' | translate }}</mat-label>
                                    <input matInput id="CampoPeso" name="Peso" maxlength="6" 
                                           (keypress)="mascaraPeso($event)" (keyup)="mascaraPeso($event)" 
                                           [(ngModel)]="Dados.peso" (blur)="CalculoIMC()">
                                </mat-form-field>

                                <mat-form-field appearance="outline">
                                    <mat-label>{{ 'TELASTREAMING.ALTURA' | translate }}</mat-label>
                                    <input matInput id="CampoAltura" name="Altura" maxlength="4"
                                           (keypress)="mascaraAltura($event)" (keyup)="mascaraAltura($event)"
                                           [(ngModel)]="Dados.altura" (blur)="CalculoIMC()">
                                </mat-form-field>

                                <mat-form-field appearance="outline">
                                    <mat-label>{{ 'TELASTREAMING.IMC' | translate }}</mat-label>
                                    <input matInput name="IMC" [(ngModel)]="Dados.iMC" maxlength="5" readonly="true">
                                </mat-form-field>

                                <mat-form-field appearance="outline">
                                    <mat-label>{{ 'TELASTREAMING.PRESSAO' | translate }}</mat-label>
                                    <input matInput id="mascaraPressao" name="Pressão" [(ngModel)]="Dados.pressao" 
                                           maxlength="5" (keypress)="mascaraPressao($event)" (keyup)="mascaraPressao($event)">
                                </mat-form-field>

                                <mat-form-field appearance="outline">
                                    <mat-label>{{ 'TELASTREAMING.BATIMENTO' | translate }}</mat-label>
                                    <input matInput name="Batimento" [(ngModel)]="Dados.batimento" maxlength="3"
                                           (keypress)="mascaraNum($event)" (keyup)="mascaraNum($event)">
                                </mat-form-field>

                                <mat-form-field appearance="outline">
                                    <mat-label>{{ 'TELASTREAMING.TEMPERATURA' | translate }}</mat-label>
                                    <input matInput name="Temperatura" maxlength="5" 
                                           (keypress)="mascaraAltura($event)" [(ngModel)]="Dados.temperatura">
                                </mat-form-field>
                            </div>

                            <div class="data-actions">
                                <button mat-mini-fab class="data-action-button clear-button" (click)="LimparDados()">
                                    <p>Limpar</p>
                                </button>

                                <button mat-mini-fab class="data-action-button save-button" (click)="SalvarDadosCorpo()">
                                    <p>Salvar</p>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Barra de Botões de Ação -->
            <div class="action-buttons-row">
                <div class="action-buttons-container">
                    <!-- Botão Consultas -->
                    <div class="action-button-item">
                        <button mat-mini-fab [class]="consulta ? 'active-button' : 'inactive-button'" (click)="ConsultMenu()">
                            <mat-icon>assignment</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.CONSULTAS' | translate }}</small>
                    </div>

                    <!-- Botão Anotações Anônimas -->
                    <div class="action-button-item">
                        <button mat-mini-fab [class]="anonimo ? 'active-button' : 'inactive-button'" (click)="AnotMenu()">
                            <mat-icon>visibility_off</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</small>
                    </div>

                    <!-- Botão Anexos -->
                    <div class="action-button-item">
                        <button mat-mini-fab [class]="anexo ? 'active-button' : 'inactive-button'" (click)="AnexoMenu()">
                            <mat-icon>archive</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.ANEXOS' | translate }}</small>
                    </div>

                    <!-- Botão Dados Pessoais -->
                    <div class="action-button-item">
                        <button mat-mini-fab [class]="corpo ? 'active-button' : 'inactive-button'" (click)="DadosMenu()">
                            <mat-icon>account_circle</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.DADOSPESSOAIS' | translate }}</small>
                    </div>

                    <!-- Botão Atestado -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="action-button" (click)="Atestado()">
                            <mat-icon>assignment</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.ATESTADO' | translate }}</small>
                    </div>

                    <!-- Botão Receituário -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="action-button" (click)="Receituario()">
                            <mat-icon>file_copy</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.RECEITUARIO' | translate }}</small>
                    </div>

                    <!-- Botão Declaração -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="action-button" (click)="Declaracao()">
                            <mat-icon>assignment_ind</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.DECALARACAO' | translate }}</small>
                    </div>

                    <!-- Botão Exames -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="action-button" (click)="ExamesPaciente()">
                            <mat-icon>assignment</mat-icon>
                        </button>
                        <small>Exames</small>
                    </div>

                    <!-- Botão Rechamar -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="action-button" (click)="RechamarPaciente()">
                            <mat-icon>campaign</mat-icon>
                        </button>
                        <small>Rechamar paciente</small>
                    </div>

                    <!-- Botão Análise -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="action-button" (click)="AbreModalAnalise()">
                            <mat-icon>medical_services</mat-icon>
                        </button>
                        <small>Análise</small>
                    </div>

                    <!-- Botão Finalizar -->
                    <div class="action-button-item">
                        <button mat-mini-fab class="finish-button" (click)="ngxSmartModalService.getModal('FinalizarChamada').open()">
                            <mat-icon>check</mat-icon>
                        </button>
                        <small>{{ 'TELASTREAMING.CONCLUIRCONSULTA' | translate }}</small>
                    </div>

                    <!-- Botão Sair -->
                    <div class="action-button-item" *ngIf="Sairsala == true">
                        <button mat-mini-fab class="exit-button" (click)="BtnSair()">
                            <mat-icon>exit_to_app</mat-icon>
                        </button>
                        <small>Sair da Sala</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- VERSÃO MOBILE -->
<div class="consultation-container-mobile yes-mobile" (click)="fecharBotoesMobile()">
    <div class="mobile-header">
        <!-- Tempo da Consulta -->
        <div class="mobile-timer">
            <label>{{hora}}:{{minutos | number:'2.0'}}:{{segundos | number:'2.0'}}</label>
        </div>

        <!-- Info do Paciente -->
        <div class="mobile-patient-info">
            <mat-form-field appearance="outline" class="patient-name-field">
                <input class="patient-name" matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled
                    name="Nome" [(ngModel)]="DadosInformUsuario.nome">
            </mat-form-field>

            <button mat-icon-button class="info-button" (click)="informacao()" 
                   title="{{ 'TELAAGENDA.INFORMACOESPACIENTE' | translate }}">
                <mat-icon>info</mat-icon>
            </button>
        </div>
    </div>

    <!-- Menu de Botões Mobile -->
    <div class="mobile-action-menu">
        <button class="menu-toggle-button" mat-mini-fab (click)="openToggle()">
            <mat-icon><i class="fas fa-angle-right"></i></mat-icon>
        </button>

        <div class="mobile-action-buttons" [@openClose2]="toogleBotoesMobile ? 'open2': 'closed2'">
            <!-- Botão Consultas -->
            <button mat-mini-fab [class]="consulta ? 'active-button' : 'inactive-button'" (click)="ConsultMenu()">
                <mat-icon>assignment</mat-icon>
                <small>{{ 'TELASTREAMING.CONSULTAS' | translate }}</small>
            </button>

            <!-- Botão Anotações Anônimas -->
            <button mat-mini-fab [class]="anonimo ? 'active-button' : 'inactive-button'" (click)="AnotMenu()">
                <mat-icon>visibility_off</mat-icon>
                <small>{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</small>
            </button>

            <!-- Botão Anexos -->
            <button mat-mini-fab [class]="anexo ? 'active-button' : 'inactive-button'" (click)="AnexoMenu()">
                <mat-icon>archive</mat-icon>
                <small>{{ 'TELASTREAMING.ANEXOS' | translate }}</small>
            </button>

            <!-- Botão Dados Pessoais -->
            <button mat-mini-fab [class]="corpo ? 'active-button' : 'inactive-button'" (click)="DadosMenu()">
                <mat-icon>account_circle</mat-icon>
                <small>{{ 'TELASTREAMING.DADOSPESSOAIS' | translate }}</small>
            </button>

            <!-- Botão Atestado -->
            <button mat-mini-fab class="action-button" (click)="Atestado()">
                <mat-icon>assignment</mat-icon>
                <small>{{ 'TELASTREAMING.ATESTADO' | translate }}</small>
            </button>

            <!-- Botão Receituário -->
            <button mat-mini-fab class="action-button" (click)="Receituario()">
                <mat-icon>file_copy</mat-icon>
                <small>{{ 'TELASTREAMING.RECEITUARIO' | translate }}</small>
            </button>

            <!-- Botão Declaração -->
            <button mat-mini-fab class="action-button" (click)="Declaracao()">
                <mat-icon>assignment_ind</mat-icon>
                <small>{{ 'TELASTREAMING.DECALARACAO' | translate }}</small>
            </button>

            <!-- Botão Exames -->
            <button mat-mini-fab class="action-button" (click)="ExamesPaciente()">
                <mat-icon>assignment</mat-icon>
                <small>Exames</small>
            </button>

            <!-- Botão Finalizar -->
            <button mat-mini-fab class="finish-button" (click)="ngxSmartModalService.getModal('FinalizarChamada').open()">
                <mat-icon>check</mat-icon>
                <small>{{ 'TELASTREAMING.CONCLUIRCONSULTA' | translate }}</small>
            </button>

            <!-- Botão Sair -->
            <button mat-mini-fab class="exit-button" *ngIf="Sairsala == true" (click)="BtnSair()">
                <mat-icon>exit_to_app</mat-icon>
                <small>Sair da Sala</small>
            </button>
        </div>
    </div>

    <!-- Histórico Mobile -->
    <div class="mobile-history">
        <h4 class="mobile-history-title">{{ 'TELAINICIO.HISTORICO' | translate }}</h4>
        <hr class="section-divider"/>
        
        <!-- Loading Spinner -->
        <div class="upload-container text-center" *ngIf="spinnerHistorico == true">
            <img src="assets/build/img/spinner.gif" class="loading-spinner">
        </div>

        <!-- Histórico de Consultas -->
        <div class="mobile-history-scroll" *ngIf="spinnerHistorico != true" #scrollMe>
            <div class="mobile-history-item" *ngFor="let item of dadosHistorico;let i = index;" 
                 (click)="flgConsultaAnonimo()">
                <div class="mobile-history-date" (click)="historico = true">
                    {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                </div>
                <div class="mobile-history-indicators">
                    <mat-icon *ngIf="item.anonimo != null" class="mobile-history-icon">visibility_off</mat-icon>
                    <mat-icon *ngIf="item.anexo.length > 0" class="mobile-history-icon">archive</mat-icon>
                    <mat-icon *ngIf="item.dadosCorpo != null" class="mobile-history-icon">accessibility_new</mat-icon>
                </div>
                <hr class="item-divider"/>
            </div>
        </div>
    </div>

    <!-- Conteúdo Principal Mobile -->
    <div class="mobile-content">
        <!-- Histórico de Consulta Selecionado -->
        <div class="mobile-consultation-history" #scrollMe [ngClass]="{'hidden': !consulta && !anonimo}">
            <div *ngFor="let item of dadosHistorico;let i = index">
                <div class="mobile-history-consultation" *ngIf="historico">
                    <div class="mobile-history-card" id="{{ item.posicao }}" #destinationRef>
                        <div class="mobile-history-header">
                            <span>
                                {{ 'TELASTREAMING.CONSULTADIA' | translate }}
                                {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                                {{ 'TELASTREAMING.MEDICO' | translate }} {{item.medico}}
                            </span>
                        </div>

                        <div class="mobile-history-content">
                            <div *ngIf="item.obsConsulta != null && item.obsConsulta != ''">
                                <hr class="dotted-divider" *ngIf="item.obsConsulta != null && item.obsConsulta != ''">
                                <span class="mobile-history-observation">
                                    {{ 'TELASTREAMING.PRONTUARIO' | translate }} {{item.obsConsulta}}
                                </span>
                            </div>

                            <div *ngIf="item.receita != null" class="mobile-prescription-data">
                                <span>Receituário:<br>{{ item.receita }}</span>
                            </div>
                        </div>

                        <!-- Expansões Mobile -->
                        <div class="mobile-expansion-panels">
                            <!-- Dados do Paciente -->
                            <div class="mobile-patient-data-panel" *ngIf="item.dadosCorpo!=null">
                                <mat-accordion>
                                    <mat-expansion-panel hideToggle>
                                        <mat-expansion-panel-header>
                                            <mat-panel-title>
                                                <mat-icon>accessibility_new</mat-icon>
                                                <span>Dados do Paciente</span>
                                            </mat-panel-title>
                                        </mat-expansion-panel-header>
                                        <div class="mobile-patient-metrics">
                                            <div class="mobile-metric-item">Peso: {{item.dadosCorpo.peso}}</div>
                                            <div class="mobile-metric-item">Altura: {{item.dadosCorpo.altura}}</div>
                                            <div class="mobile-metric-item">IMC: {{item.dadosCorpo.imc}}</div>
                                            <div class="mobile-metric-item">Pressão: {{item.dadosCorpo.pressao}}</div>
                                            <div class="mobile-metric-item">Batimento: {{item.dadosCorpo.batimento}}</div>
                                            <div class="mobile-metric-item">Temperatura: {{item.dadosCorpo.temperatura}}</div>
                                        </div>
                                    </mat-expansion-panel>
                                </mat-accordion>
                            </div>

                            <!-- Anotações Anônimas -->
                            <div class="mobile-anonymous-panel" *ngIf="item.anonimo != '' && item.anonimo != null && historico">
                                <mat-accordion>
                                    <mat-expansion-panel hideToggle>
                                        <mat-expansion-panel-header>
                                            <mat-panel-title>
                                                <img src="assets/build/img/incognito.svg" class="anonymous-icon">
                                                <span>{{ 'TELASTREAMING.ANONIMO' | translate }}</span>
                                            </mat-panel-title>
                                        </mat-expansion-panel-header>
                                        <div class="mobile-anonymous-content">
                                            {{item.anonimo}}
                                        </div>
                                    </mat-expansion-panel>
                                </mat-accordion>
                            </div>

                            <!-- Anexos -->
                            <div class="mobile-attachments-panel" *ngIf="dadosHistorico[i].anexo.length > 0 && historico">
                                <mat-accordion>
                                    <mat-expansion-panel hideToggle>
                                        <mat-expansion-panel-header>
                                            <mat-panel-title>
                                                <i class="fas fa-paperclip"></i>
                                                <span>{{ 'TELASTREAMING.ANEXOS' | translate }}</span>
                                            </mat-panel-title>
                                        </mat-expansion-panel-header>
                                        <div class="mobile-attachments-list">
                                            <div class="mobile-attachment-item" *ngFor="let anex of dadosHistorico[i].anexo">
                                                <div class="mobile-attachment-bubble" id="colapsoAnexo+{{i}}"
                                                    *ngIf="tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& anex.flgVisualizacaoUsuario==true">
                                                    <div class="mobile-attachment-content">
                                                        <i (click)="BaixarArquivo(anex.chave,anex.anexo)" class="fa fa-download"></i>
                                                        <span class="mobile-attachment-name">{{anex.anexo}}</span>
                                                    </div>
                                                    <div class="mobile-attachment-meta">
                                                        <small class="mobile-attachment-date">{{anex.dtaCadastro | date: 'dd/MM/yyyy HH:mm'}}</small>
                                                        <small class="mobile-attachment-user">{{anex.usuario}}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </mat-expansion-panel>
                                </mat-accordion>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resultados de Exames Mobile -->
        <div class="mobile-exams-results" *ngIf="resultadosExame">
            <h3 class="mobile-section-title">Resultados</h3>
            <div class="mobile-exams-list">
                <div class="mobile-exam-item" *ngFor="let item of dadosexame">
                    <div class="mobile-exam-header">Descrição Exame</div>
                    <div class="mobile-exam-value">{{item.des}}</div>
                    
                    <div class="mobile-exam-header">Cod.Exame</div>
                    <div class="mobile-exam-value">{{item.cod | truncate : 30 : "…"}}</div>
                    
                    <div class="mobile-exam-header">Resultado</div>
                    <div class="mobile-exam-value">{{item.result | truncate : 30 : "…"}}</div>
                    
                    <div class="mobile-exam-header">Data</div>
                    <div class="mobile-exam-value">{{item.data | date: 'dd/MM/yyyy'}}</div>
                </div>
            </div>
        </div>

        <!-- Anotações Anônimas Mobile -->
        <div class="mobile-anonymous-notes" *ngIf="anonimo">
            <h3 class="mobile-section-title">{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</h3>
            <mat-form-field appearance="outline" class="mobile-notes-field">
                <textarea matInput id="txtAnonimo" name="Observação"
                    (keyup.enter)="addParagrafoAnonimo()" [(ngModel)]="DesAnonimo"
                    class="mobile-notes-textarea"></textarea>
            </mat-form-field>
        </div>

        <!-- Consulta Mobile -->
        <div class="mobile-consultation-notes" *ngIf="consulta">
            <h3 class="mobile-section-title">{{ 'TELASTREAMING.CONSULTAS' | translate }}</h3>
            <mat-form-field appearance="outline" class="mobile-notes-field">
                <textarea matInput id="txtConsulta" name="Observação"
                    (keyup.enter)="addParagrafo()" [(ngModel)]="DesConsulta"
                    class="mobile-notes-textarea"></textarea>
            </mat-form-field>
        </div>

        <!-- Anexos Mobile -->
        <div class="mobile-attachments-area" *ngIf="anexo">
            <!-- Loading Spinner -->
            <div class="mobile-spinner-container" *ngIf="spinnerAnexo == true">
                <img src="assets/build/img/spinner.gif" class="loading-spinner">
            </div>
            
            <!-- Área para Upload de Arquivos -->
            <div *ngIf="spinnerAnexo == false" id="divArquivo" class="mobile-dropzone-container"
                (dragover)="destacaBorda($event)" (dragleave)="tiraBorada($event)"
                (drop)="fileuploadesquerdo($event)">
                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                <p class="mobile-dropzone-text">
                    {{ 'TELASTREAMING.AREAPARAIMPORTARARQUIVOS' | translate }}
                    <br><br>
                    {{ 'TELASTREAMING.OU' | translate }}
                </p>

                <label for="file-mobile" class="mobile-upload-button">
                    {{ 'TELASTREAMING.CARREGARARQUIVO' | translate }}
                </label>
                <input type="file" id="file-mobile" (change)="SubirArquivoConsulta($event)" accept=".xls,.xlsx,.pdf" />
            </div>
            
            <!-- Lista de Anexos Mobile -->
            <div class="mobile-attachments-list-container">
                <div class="mobile-attachment-group" *ngFor="let item of DadosanexConsulta">
                    <div class="mobile-attachment-wrapper" *ngIf="item.idUsuario == usuario">
                        <div class="mobile-user-attachment">
                            <div class="mobile-attachment-content">
                                <i (click)="BaixarArquivo(item.chave,item.nomeArquivo)" class="fa fa-download"></i>
                                <span class="mobile-attachment-name">{{item.nomeArquivo | truncate : 14 : "…"}}</span>
                            </div>
                            <div class="mobile-attachment-meta">
                                <span class="mobile-attachment-date">{{item.dtacadastro | date: 'dd/MM/yyyy HH:mm'}}</span>
                                <button mat-icon-button class="mobile-delete-button" (click)="DeleteAnexo(item.chave, item.idAnexo)">
                                    <mat-icon>delete</mat-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mobile-attachment-wrapper" 
                         *ngIf="item.idUsuario != usuario && (tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& item.flgVisualizacaoUsuario==true)">
                        <div class="mobile-other-attachment">
                            <div class="mobile-attachment-content">
                                <i (click)="BaixarArquivo(item.chave,item.nomeArquivo)" class="fa fa-download"></i>
                                <span class="mobile-attachment-name">{{item.nomeArquivo}}</span>
                            </div>
                            <div class="mobile-attachment-meta">
                                <span class="mobile-attachment-date">{{item.dtacadastro | date: 'dd/MM/yyyy HH:mm'}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <button (click)="anexo = false" aria-label="Close" class="mobile-close-button" type="button">
                <mat-icon>close</mat-icon>
            </button>
        </div>

        <!-- Dados do Paciente Mobile -->
        <div class="mobile-patient-data-area" *ngIf="corpo">
            <div class="mobile-data-container">
                <div class="mobile-data-header">
                    <h4>{{ 'TELASTREAMING.DADOSPACIENTE' | translate }}</h4>
                </div>

                <div class="mobile-patient-illustration">
                    <img src="assets/build/img/corpoPaciente.png" class="mobile-body-image">
                </div>

                <div class="mobile-patient-metrics-form">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELASTREAMING.PESO' | translate }}</mat-label>
                        <input matInput name="Peso" maxlength="6" 
                               (keypress)="mascaraPeso($event)" (keyup)="mascaraPeso($event)" 
                               [(ngModel)]="Dados.peso" (blur)="CalculoIMC()">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELASTREAMING.ALTURA' | translate }}</mat-label>
                        <input matInput name="Altura" maxlength="4"
                               (keypress)="mascaraAltura($event)" (keyup)="mascaraAltura($event)"
                               [(ngModel)]="Dados.altura" (blur)="CalculoIMC()">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELASTREAMING.IMC' | translate }}</mat-label>
                        <input matInput name="IMC" [(ngModel)]="Dados.iMC" maxlength="5" readonly="true">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELASTREAMING.PRESSAO' | translate }}</mat-label>
                        <input matInput name="Pressão" [(ngModel)]="Dados.pressao" 
                               maxlength="5" (keypress)="mascaraPressao($event)" (keyup)="mascaraPressao($event)">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELASTREAMING.BATIMENTO' | translate }}</mat-label>
                        <input matInput name="Batimento" [(ngModel)]="Dados.batimento" maxlength="3"
                               (keypress)="mascaraNum($event)" (keyup)="mascaraNum($event)">
                    </mat-form-field>

                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELASTREAMING.TEMPERATURA' | translate }}</mat-label>
                        <input matInput name="Temperatura" maxlength="5" 
                               (keypress)="mascaraAltura($event)" [(ngModel)]="Dados.temperatura">
                    </mat-form-field>
                </div>

                <div class="mobile-data-actions">
                    <button mat-mini-fab class="mobile-data-action-button mobile-clear-button" (click)="LimparDados()">
                        <mat-icon>clear</mat-icon>
                    </button>

                    <button mat-mini-fab class="mobile-data-action-button mobile-save-button" (click)="SalvarDadosCorpo()">
                        <mat-icon>save</mat-icon>
                    </button>
                </div>
            </div>
            
            <button (click)="corpo = false" aria-label="Close" class="mobile-close-button" type="button">
                <mat-icon>close</mat-icon>
            </button>
        </div>
    </div>
</div>