<div class="clinica-container">
  <div class="card main-card">
    <div class="card-header">
      <div class="header-left">
        <div class="icon-container">
          <span class="material-icons">business</span>
        </div>
        <h1 class="page-title">{{ 'TELACADASTROCLINICA.CLINICA' | translate }}</h1>
      </div>
      <button class="btn btn-link" onclick='history.go(-1)'>
        <span class="material-icons">arrow_back</span>
      </button>
    </div>

    <div class="card-body">
      <!-- Seção de Dados da Clínica -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">{{ 'TELACADASTROCLINICA.DADOSDACLINICA' | translate }}</h2>
        </div>
        <div class="section-content">
          <!-- Foto de Perfil -->
          <div class="profile-upload">
            <label for="imageperfilClinica" class="photo-container">
              <img src="{{ ImagemClinica }}" class="profile-photo" alt="Logo de Perfil" 
                   title="{{'TELACADASTROCLINICA.LOGODOPERFIL' | translate}}">
              <div class="photo-overlay">
                <span class="material-icons">photo_camera</span>
              </div>
            </label>
            <h5>{{ 'TELACADASTROCLINICA.LOGO' | translate }}</h5>
            <input type="file" id="imageperfilClinica" (change)="AlterarImagemClinica($event)" (click)="LimpaCampoFile()" />
            <input type="text" style="display: none;" id="Logo" name="Logo" [(ngModel)]="Dados.Logo">
          </div>
          <!-- Linhas de formulário -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nome</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.NOME' | translate }}"
                  id="nome" name="Nome" required maxlength="100"
                  (keyup)="mascaraText($event, 'nome')" (change)="mascaraText($any($event), 'nome')"
                  [(ngModel)]="Dados.nome">
                <mat-error *ngIf="Nome.invalid">
                  {{ getErrorMessageNome() | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Data da Licença</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.DATADALICENCA' | translate }}"
                  name="dataLicenca" id="dataLicenca" (keyup)="mascaraData($any($event))"
                  [(ngModel)]="Dados.dataLicenca" maxlength="10"
                  (blur)="ValidaDta($any($event.target).value)">
              </mat-form-field>
              <div class="field-error" *ngIf="Dtalice">
                {{ 'TELACADASTROCLINICA.DATAINVALIDA' | translate }}
              </div>
            </div>

            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Valor Consulta</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROMEDICO.VALORCONSULTA' | translate }}"
                  name="valor" id="valorConsulta" [(ngModel)]="Dados.valorConsulta"
                  (keypress)="mascaraValor($any($event.target).value)" (change)="mascaraValor($any($event.target).value)"
                  (keyup)="mascaraValor($any($event.target).value)" maxlength="15">
              </mat-form-field>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Caracterização</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CARACTERIZACAO' | translate }}"
                  id="caracterizacao" name="Caracterizacao" maxlength="100"
                  (keyup)="mascaraText($event, 'caracterizacao')"
                  [(ngModel)]="Dados.caracterizacao">
              </mat-form-field>
            </div>

            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CNPJ</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CNPJ' | translate }}"
                  id="cpfcnpj" type="text" name="cnpj"
                  (change)="ValidaCPFCNPJ($any($event.target).value)"
                  (keypress)="mascaraCnpj('##.###.###/####-##','###.###.###-##', $event)"
                  (keyup)="mascaraCnpj('##.###.###/####-##','###.###.###-##', $event)"
                  maxlength="18" required [(ngModel)]="Dados.cnpj">
              </mat-form-field>
              <div class="field-error" *ngIf="campoCPFInvalido">CPF inválido</div>
              <div class="field-error" *ngIf="campoCNPLInvalido">CNPJ inválido</div>
              <div class="field-error" *ngIf="campoCPFVazil && !campoCPFInvalido && !campoCNPLInvalido">
                Esse campo precisa ser preenchido
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>ISS/CCM</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.ISS/CCM' | translate }}"
                  name="iss" (keypress)="mascaraIss('#.###.###-#', $event)" mask="0.000.000-0"
                  maxlength="11" [(ngModel)]="Dados.iss">
              </mat-form-field>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CRM</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CRM' | translate }}"
                  name="crm" (keypress)="mascaraCrm('#######', $event)" mask="0000000"
                  maxlength="7" [(ngModel)]="Dados.crm">
              </mat-form-field>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CNES</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CNES' | translate }}"
                  name="cnes" mask="00000000" maxlength="8" [(ngModel)]="Dados.cnes">
              </mat-form-field>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Website</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.WEBSITE' | translate }}"
                  name="website" maxlength="100" [(ngModel)]="Dados.website">
              </mat-form-field>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.EMAIL' | translate }}" type="email"
                  name="Email" (blur)="ValidarEmail($any($event.target).value)" required
                  [(ngModel)]="Dados.email">
              </mat-form-field>
              <div class="field-error" *ngIf="campoEmailInvalido">
                {{ mensagemErroEmail }}
              </div>
            </div>
            
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Telefone</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.TELEFONE' | translate }}"
                  name="Telefone" (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)"
                  (blur)="ValidaTelefone($any($event.target).value)" maxlength="15" minlength="14"
                  [(ngModel)]="Dados.telefone">
              </mat-form-field>
              <div class="field-error" *ngIf="TelVal">
                {{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}
              </div>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Celular</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CELULAR' | translate }}"
                  name="telefoneMovel" id="TelefoneMovel" (keyup)="mascaraTelefone($event)"
                  (keypress)="mascaraTelefone($event)" (blur)="ValidaTelefoneMovel($any($event.target).value)"
                  maxlength="15" minlength="14" [(ngModel)]="Dados.telefoneMovel">
              </mat-form-field>
              <div class="field-error" *ngIf="TelMovVal">
                {{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}
              </div>
            </div>

            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Telefone Comercial</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.TELEFONECOMERCIAL' | translate }}"
                  name="telefoneComercial" (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)"
                  id="TelefoneComercial" (blur)="ValidaTelefoneComercial($any($event.target).value)" maxlength="15"
                  minlength="14" [(ngModel)]="Dados.telefoneComercial">
              </mat-form-field>
              <div class="field-error" *ngIf="TelComInvalido">
                {{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}
              </div>
              <div class="field-error" *ngIf="TelComVasil && !TelComInvalido">
                {{ 'TELACADASTROCLINICA.ERROCAMPO' | translate }}
              </div>
            </div>
          </div>
          <!-- Toggles de opções -->
          <div class="toggles-container">
            <div class="toggle-option">
              <mat-slide-toggle [(ngModel)]="FlgProntuario">
                {{'TELACADASTROCLINICA.SOMENTEPRONTUARIOS' | translate}}
              </mat-slide-toggle>
            </div>
            <div class="toggle-option">
              <mat-slide-toggle [(ngModel)]="FlgFilaEspera">
                Fila de Espera
              </mat-slide-toggle>
            </div>
            <div class="toggle-option">
              <mat-slide-toggle [(ngModel)]="FlgSolicitarOrientacao">
                Solicitar Orientação
              </mat-slide-toggle>
            </div>
            <div class="toggle-option">
              <mat-slide-toggle [(ngModel)]="FlgHabilitaChat">
                Chat interno
              </mat-slide-toggle>
            </div>
            <div class="toggle-option">
              <mat-slide-toggle [(ngModel)]="FlgHabilitaPagamento">
                Habilitar Pagamento
              </mat-slide-toggle>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção de Endereço -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">{{ 'TELACADASTROCLINICA.ENDERECO' | translate }}</h2>
        </div>
        <div class="section-content">
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Rua</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.RUA' | translate }}"
                  name="Rua" [(ngModel)]="Dados.rua">
              </mat-form-field>
            </div>
            <div class="form-group col-md-2">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Número</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.NUMERO' | translate }}"
                  name="numero" maxlength="10" (keyup)="mascaraNumeros($event)"
                  [(ngModel)]="Dados.numero">
              </mat-form-field>
            </div>
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Complemento</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.COMPLEMENTO' | translate }}"
                  name="complemento" [(ngModel)]="Dados.complemento">
              </mat-form-field>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group col-md-3">
              <div class="select-container">
                <ng-select class="modern-select"
                  [items]="dadosUF" 
                  placeholder="{{ 'TELACADASTROCLINICA.UF' | translate }}" bindLabel="siglasUf"
                  bindValue="siglasUf" name="UF" (change)="CidadePorUF()" [selectOnTab]="true"
                  notFoundText="{{ 'TELACADASTROCLINICA.UFNAOENCONTRADA' | translate }}"
                  [(ngModel)]="Dados.uf">
                </ng-select>
              </div>
            </div>
            <div class="form-group col-md-3">
              <div class="select-container">
                <ng-select class="modern-select"
                  [items]="dadosCidadeUf" 
                  placeholder="{{ 'TELACADASTROCLINICA.MUNICIPIO' | translate }}" bindLabel="nmeCidade"
                  bindValue="idCidade" name="municipio" [selectOnTab]="true"
                  notFoundText="{{ 'TELACADASTROCLINICA.UFNAOENCONTRADA' | translate }}"
                  [(ngModel)]="Dados.idCidade">
                </ng-select>
              </div>
            </div>
            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Bairro</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.BAIRRO' | translate }}"
                  name="bairro" [(ngModel)]="Dados.bairro">
              </mat-form-field>
            </div>
            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CEP</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CEP' | translate }}" name="cep"
                  mask="00000-000" maxlength="9" [(ngModel)]="Dados.cep">
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção de Responsável -->
      <div class="section">
        <div class="section-header">
          <div class="section-header-with-actions">
            <h2 class="section-title">Responsável da Clínica</h2>
            <div class="header-actions">
              <button mat-button class="btn-add" 
                (click)="AdicionarResponsavelClinica()">
                <span class="material-icons">person_add</span>
                <p>Adicionar Clínica</p>
              </button>
              <button mat-button class="btn-outline" *ngIf="camposResponsaveisAtivos"
              (click)="LimparCamposResponsavel()">
              <span class="material-icons">clear_all</span>
              Limpar Campos
            </button>
            </div>
          </div>
        </div>
        <div class="section-content">
          <!-- Campos de Responsável -->
          <div class="form-row" *ngIf="camposResponsaveisAtivos">
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nome</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROUSUARIO.NOME' | translate }}" id="NomeResponsavel"
                  name="nomeResponsavel" required maxlength="100"
                  (keyup)="mascaraText($any($event.target).value, 'NomeResponsavel')"
                  (change)="mascaraText($any($event.target).value, 'NomeResponsavel')"
                  [(ngModel)]="pessoaResponsavel!.nome!">
                <mat-error *ngIf="nomeResponsavel.invalid">
                  {{ getErrorMessageNomeResponsavel() | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CPF</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROUSUARIO.CPF' | translate }}"
                  (change)="validarCpfResponsavel($any($event.target).value)" name="cpfResponsavel"
                  (keypress)="mascaraCpf('###.###.###-##', $event)" mask="000.000.000-00" maxlength="14"
                  minlength="14" required [(ngModel)]="pessoaResponsavel!.cpf!" id="CPF">
              </mat-form-field>
              <div class="field-error" *ngIf="campoCpfResponsavelInvalido">CPF inválido</div>
              <div class="field-error" *ngIf="campoCpfResponsavelVazil && !campoCpfResponsavelInvalido">
                Esse campo precisa ser preenchido
              </div>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROUSUARIO.EMAIL' | translate }}" type="email"
                  name="emailResponsavel" (change)="validarEmailResponsavel($any($event.target).value)"
                  required [(ngModel)]="pessoaResponsavel!.email!" id="emailResponsavel">
              </mat-form-field>
              <div class="field-error" *ngIf="campoEmailResponsavelInvalido">Email inválido</div>
              <div class="field-error" *ngIf="campoEmailResponsavelVazil && !campoEmailResponsavelInvalido">
                Esse campo precisa ser preenchido
              </div>
            </div>
          </div>

          <div class="form-row" *ngIf="camposResponsaveisAtivos">
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Celular</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROUSUARIO.CELULAR' | translate }}"
                  name="telefoneMovelResponsavel" (change)="validarTelMovel($any($event.target).value); ValidaTelefoneMovelResponsavel($any($event.target).value)"
                  (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)" maxlength="15" minlength="14"
                  required [(ngModel)]="pessoaResponsavel!.celular!" id="celularResp">
              </mat-form-field>
              <div class="field-error" *ngIf="TelMovValResponsavel">
                {{ 'TELACADASTROUSUARIO.TELEFONEINVALIDO' | translate }}
              </div>
              <div class="field-error" *ngIf="TelMovValResponsavelVasil && !TelMovValResponsavel">
                {{ 'TELACADASTROUSUARIO.ESSECAMPOPRECISASERPREENCHIDO' | translate }}
              </div>
            </div>
          </div>

          <!-- Lista de Responsáveis -->
          <div class="responsibles-table" *ngIf="gridResponsavelClinica.length > 0">
            <table class="data-table">
              <tbody>
                <tr *ngFor="let item of gridResponsavelClinica; let i = index;">
                  <td class="name-col"><strong>{{ item.nome }}</strong></td>
                  <td class="cpf-col"><strong>{{ item.cpf }}</strong></td>
                  <td class="email-col"><strong>{{ item.email }}</strong></td>
                  <td class="phone-col"><strong>{{ item.celular }}</strong></td>
                  <td class="actions-col">
                    <button mat-icon-button class="btn-action" (click)="EditarResponsavel(i, item.cpf)"
                      title="{{ 'TELAPESQUISAUSUARIO.EDITAR' | translate }}">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button class="btn-action btn-delete" (click)="ApagarResponsavel(i, item.idPessoa)"
                      title="Inativar">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <button class="btn btn-outline" (click)="LimparCampos()">
        <span class="material-icons">clear</span>
        {{ 'TELACADASTROCLINICA.LIMPAR' | translate }}
      </button>
      <button class="btn btn-success" (click)="OnSubmit()">
        <span class="material-icons">save</span>
        {{ 'TELACADASTROCLINICA.SALVAR' | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Modal de Foto -->
<ngx-smart-modal #ModalFoto identifier="ModalFoto" customClass="nsm-centered medium-modal">
  <div class="modal-container">
    <div class="modal-header">
      <h2 class="modal-title">{{ 'TELACADASTROCLINICA.FOTODEPERFIL' | translate }}</h2>
    </div>
    <div class="modal-body">
      <div class="image-editor-controls">
        <button class="btn btn-control" (click)="rotateLeft()">
          <span class="material-icons">rotate_left</span>
          {{ 'TELACADASTROCLINICA.GIRARAESQUERDA' | translate }}
        </button>
        <button class="btn btn-control" (click)="rotateRight()">
          <span class="material-icons">rotate_right</span>
          {{ 'TELACADASTROCLINICA.GIRARADIREITA' | translate }}
        </button>
        <button class="btn btn-control" (click)="flipHorizontal()">
          <span class="material-icons">flip</span>
          {{ 'TELACADASTROCLINICA.VIRARHORIZONTALMENTE' | translate }}
        </button>
        <button class="btn btn-control" (click)="flipVertical()">
          <span class="material-icons">flip</span>
          {{ 'TELACADASTROCLINICA.VIRARVERTICALMENTE' | translate }}
        </button>
      </div>
      
      <div class="image-cropper-container">
        <image-cropper 
          [imageChangedEvent]="imageChangedEvent" [maintainAspectRatio]="true" [aspectRatio]="3/3"
          [onlyScaleDown]="true" [roundCropper]="false" outputType="base64"
          (imageCropped)="imageCropped($event)" (imageLoaded)="imageLoaded()"
          (cropperReady)="cropperReady()" (loadImageFailed)="loadImageFailed()"
          [style.display]="showCropper ? null : 'none'" [alignImage]="'left'">
        </image-cropper>
      </div>
    </div>
    <div class="modal-footer">
      <button class="btn btn-success" (click)="CortarImagem()">
        <span class="material-icons">content_cut</span>
        {{ 'TELACADASTROCLINICA.CORTAR' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- Modal Usuário Existente -->
<ngx-smart-modal #UsuarioExistente identifier="UsuarioExistente" customClass="nsm-centered medium-modal" 
  [closable]="false" [dismissable]="false" [escapable]="false">
  <div class="modal-container">
    <div class="modal-header">
      <h2 class="modal-title">{{ mensagemPessoa }}</h2>
    </div>
    <div class="modal-footer">
      <button class="btn btn-success" (click)="AceitarUsuarioExistente()">Aceitar</button>
      <button class="btn btn-outline" (click)="NaoAceitarUsuarioExistente()">Cancelar</button>
    </div>
  </div>
</ngx-smart-modal>