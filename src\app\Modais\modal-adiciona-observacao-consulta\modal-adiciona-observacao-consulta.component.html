<div class="modal-container">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">Atualizando a observação do paciente</h3>
        </div>
    </header>

    <main class="modal-content">
        <div class="form-container">
            <div class="form-field">
                <mat-form-field appearance="outline" class="observation-input">
                    <mat-label>Observação</mat-label>
                    <textarea matInput [(ngModel)]="strObservacaoChegada" 
                        placeholder="Informe a observação sobre o paciente" rows="4"></textarea>
                </mat-form-field>
            </div>
        </div>
    </main>

    <footer class="modal-footer">
        <button class="action-button cancel-button" (click)="fecharModal(false)">
            <mat-icon>cancel</mat-icon>
            <span>Cancelar</span>
        </button>
        
        <button class="action-button save-button" (click)="SalvarObservacaoChegadaPaciente()">
            <mat-icon>save</mat-icon>
            <span>Salvar</span>
        </button>
    </footer>
</div>