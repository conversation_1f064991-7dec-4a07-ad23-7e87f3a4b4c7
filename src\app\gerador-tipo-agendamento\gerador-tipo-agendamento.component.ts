import { Component, OnInit, Output } from '@angular/core';
import { AgendaService } from '../service/agenda.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { TipoAgendamento } from '../model/agenda';

// import { FadeIn } from 'src/app/Util/Fadein.animation';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-gerador-tipo-agendamento',
    templateUrl: './gerador-tipo-agendamento.component.html',
    styleUrls: ['./gerador-tipo-agendamento.component.scss'],
    animations: [trigger('openClose', [
            state('open', style({
                opacity: '1',
                display: 'block'
            })),
            state('closed', style({
                opacity: '0',
                display: 'none'
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ]),
        ])
    ],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCardModule,
      TranslateModule,
      MatIcon,
      MatDivider,
      MatFormFieldModule,
      NgxSmartModalModule
    ]
})
export class GeradorTipoAgendamentoComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    public agendaService: AgendaService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBarAlert: AlertComponent



  ) { }
  @Output() FadeIn?: string;
  // isOpen = false;
  tipoAgendamento?: string;
  bOcultaCarregaMais = false;
  qtdRegistros = 10;
  pesquisa = "";
  idTipoAgendamento = 0
  DadosTipoAgendamento: any;
  // usuario: Usuario;
  legenda = false

  actionButtonLabel: string = 'Fechar';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';

  ngOnInit() {
    this.CarregaTipoAgendamento();
  }


  toggle:any = {}

  // CarregaTipoAgendamento() {
  //   this.agendaService.getTipoAgendamento().then((retorno) => {
  //     
  //     this.DadosTipoAgendamento = retorno;
  //   }, err =>{
  //   })
  // }

  CarregaTipoAgendamento() {
    this.campoTipoAgendamento.markAsUntouched();
    this.bOcultaCarregaMais = false
    this.agendaService.GetTipoAgendamentos(0, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      
      this.DadosTipoAgendamento = retorno;
      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
      else
        this.bOcultaCarregaMais = false;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  CarregarMais() {
    this.bOcultaCarregaMais = false;
    this.agendaService.GetTipoAgendamentos(this.DadosTipoAgendamento.length, this.qtdRegistros, this.pesquisa, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      var dados = retorno;
      for (let index = 0; index < dados.length; index++) {
        this.DadosTipoAgendamento.push(dados[index]);
      }
      if (retorno.length < this.qtdRegistros)
        this.bOcultaCarregaMais = true;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  PerguntasTotal() {
    this.agendaService.getTipoAgendamento(this.usuarioLogadoService.getIdUltimaClinica()).subscribe(() => {
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  public buscaDadosTable() {
    // var input, filter, table, tr, td, i;
    // input = document.getElementById("inputBusca");
    // filter = input!.toUpperCase();
    // table = document.getElementById("DatatableCliente");
    // tr = table!.getElementsByTagName("tr");

    // for (i = 0; i < tr.length; i++) {
    //   td = tr[i].getElementsByTagName("td")[0];
    //   if (td) {
    //     if (td.innerHTML.toUpperCase().indexOf(filter) > -1) {
    //       tr[i].style.display = "";
    //     } else {
    //       tr[i].style.display = "none";
    //     }
    //   }
    // }
  }


  salvarTipoAgendamento() {
    
    if (this.campoTipoAgendamento.invalid) {
      this.campoTipoAgendamento.markAsTouched()
      this.snackBarAlert.falhaSnackbar("Preencha o campo obrigatório")
      return
    }
    if (this.tipoAgendamento != undefined && this.tipoAgendamento != "") {
      var tipoAgendamento = new TipoAgendamento();
      tipoAgendamento.FlgInativo = false;
      tipoAgendamento.idClinica = this.usuarioLogadoService.getIdUltimaClinica()!;
      tipoAgendamento.DesTipoAgendamento = this.tipoAgendamento;
      tipoAgendamento.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!
      if (this.idTipoAgendamento != 0)
        tipoAgendamento.IdTipoAgendamento = this.idTipoAgendamento;

      this.agendaService.salvarTipoAgendamento(tipoAgendamento).subscribe((retorno) => {
        if (retorno) {
          this.CarregaTipoAgendamento()
          this.tipoAgendamento = "";
          this.idTipoAgendamento = 0;
          this.snackBarAlert.sucessoSnackbar("Tipo de Agendamento Salvo.");
          this.campoTipoAgendamento.markAsUntouched()
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }

  }
  inativarTipoAgendamento() {

    this.agendaService.InativarTipoAgendamento(this.idTipoAgendamento, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      
      if (retorno) {
        this.idTipoAgendamento = 0
        this.ngxSmartModalService.getModal('excluirItem').close();
        this.snackBarAlert.sucessoSnackbar("Excluido com sucesso.")
        this.CarregaTipoAgendamento();
      }
      else
        this.snackBarAlert.falhaSnackbar("Erro ao Excluir")
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  ValorTipoAgendamento(id:any) {
    this.campoTipoAgendamento.markAsUntouched();
    this.idTipoAgendamento = 0
    this.idTipoAgendamento = id;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }
  editTipoAgendamento(id:any) {
    var ObjtipoAgendamento = this.DadosTipoAgendamento.filter((c:any) => c.idTipoAgendamento == id)
    this.tipoAgendamento = ObjtipoAgendamento[0].desTipoAgendamento;
    this.idTipoAgendamento = ObjtipoAgendamento[0].idTipoAgendamento;
    document.documentElement.scrollTop = 0;
  }

  // SnackMensagem(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }


  campoTipoAgendamento = new FormControl('', [Validators.required, Validators.required])


  getErrorMessagecampoTipoAgendamento() {
    return this.campoTipoAgendamento.hasError('required') ? 'TELACADASTROUSUARIO.ERRODATA' :
      this.campoTipoAgendamento.hasError('campoTipoAgendamento') ? 'TELACADASTROUSUARIO.ERRONAOEVALIDO' :
        '';

  }
}