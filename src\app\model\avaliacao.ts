export class Avaliacao {

    IdAvaliacao?: number;
    IdConsulta?: number;
    Observacao?: string;
    IdCliente?: number;
    DtaCadastro?: Date;
    FlgInativo?: boolean

    quesitosAvaliacao?: QuesitosAvaliacao[];

}

export class QuesitosAvaliacao {

    IdQuesitoAvaliacao?: number;
    IdQuesito?: number;
    IdAvaliacao?: number;
    voto?:number;
    DtaCadastro?: Date;
    FlgInativo?: boolean

}