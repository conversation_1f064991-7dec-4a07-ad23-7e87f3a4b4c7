import { EventEmitter, Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SpinnerService {
  public flgExibeSpinner$: EventEmitter<boolean>;

  constructor() {
    // Inicializa o EventEmitter
    this.flgExibeSpinner$ = new EventEmitter<boolean>();
  }

  public show() {
    console.log("spinner show")
    this.flgExibeSpinner$.emit(true);
  }

  public hide() {
    console.log("spinner hide")
    this.flgExibeSpinner$.emit(false);
  }
}
