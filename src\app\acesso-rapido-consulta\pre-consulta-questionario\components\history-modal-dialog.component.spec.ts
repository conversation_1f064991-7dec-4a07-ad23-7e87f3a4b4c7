import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HistoryModalDialogComponent } from './history-modal-dialog.component';
import { QuestionarioPreConsultaDados } from '../MapPalavrasModel';

describe('HistoryModalDialogComponent', () => {
  let component: HistoryModalDialogComponent;
  let fixture: ComponentFixture<HistoryModalDialogComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<HistoryModalDialogComponent>>;

  const mockData: QuestionarioPreConsultaDados = {
    nome: '<PERSON>',
    cpf: '12345678901',
    email: '<EMAIL>',
    telefone: '11999999999',
    dataNascimento: '1990-01-01',
    alergias: 'Nenhuma',
    sintomas: 'Dor de cabeça',
    intensidadeDor: '7',
    tempoSintomas: '2 dias',
    doencasPrevias: 'Hipert<PERSON>ão',
    observacoes: 'Paciente relata stress'
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        HistoryModalDialogComponent,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { 
          provide: MAT_DIALOG_DATA, 
          useValue: { 
            dadosColetados: mockData,
            snackBar: { sucessoSnackbar: jasmine.createSpy(), falhaSnackbar: jasmine.createSpy() }
          } 
        }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(HistoryModalDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display filled data correctly', () => {
    const dadosPreenchidos = component.getDadosPreenchidos();
    expect(dadosPreenchidos.length).toBeGreaterThan(0);
    
    const nomeItem = dadosPreenchidos.find(item => item.label === 'Nome');
    expect(nomeItem).toBeTruthy();
    expect(nomeItem?.value).toBe('João Silva');
  });

  it('should calculate progress percentage correctly', () => {
    const percentage = component.getProgressPercentage();
    expect(percentage).toBeGreaterThan(0);
    expect(percentage).toBeLessThanOrEqual(100);
  });

  it('should filter data based on search term', () => {
    component.searchTerm = 'João';
    const filteredData = component.getFilteredData();
    expect(filteredData.length).toBeGreaterThan(0);
    
    const hasJoao = filteredData.some(item => 
      item.label.toLowerCase().includes('joão') || 
      item.value.toLowerCase().includes('joão')
    );
    expect(hasJoao).toBeTruthy();
  });

  it('should validate email field correctly', () => {
    const validStatus = component.getValidationStatusForField('email');
    expect(validStatus).toBe('valid');
  });

  it('should close dialog when onClose is called', () => {
    component.onClose();
    expect(mockDialogRef.close).toHaveBeenCalled();
  });
});
