import { Component, OnInit } from '@angular/core';
import { AlertComponent } from '../alert/alert.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog as MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ConvenioService } from '../service/convenio.service';
import { FaturaService } from '../service/fatura.service';
import { SpinnerService } from '../service/spinner.service';
import { ValidadoreseMascaras } from '../Util/validadores';
import { FaturaModelview } from '../model/fatura';
import { ModalDetalhesFaturaComponent } from '../Modais/modal-detalhes-fatura/modal-detalhes-fatura.component';
import { ArquivoService } from '../service/arquivo.service';
import { Uteis } from '../Util/uteis';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';

@Component({
    selector: 'app-lista-faturas',
    templateUrl: './lista-faturas.component.html',
    styleUrls: ['./lista-faturas.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      MatCheckboxModule 
    ]
})
export class ListaFaturasComponent implements OnInit {

  constructor(
    public validadores: ValidadoreseMascaras,
    public convenioService: ConvenioService,
    public usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private snackBarAlert: AlertComponent,
    private router: Router,
    private faturaService: FaturaService,
    private matDialog: MatDialog,
    private arquivoService: ArquivoService,
    private snackbar: AlertComponent,
  ) { }

  listaFatura: FaturaModelview[] = [];
  listaFaturaSelecionados: number[] = [];

  flgSelecionaTodos: boolean = false;

  ngOnInit() {
    this.GetListaFatura();
    this.listaFaturaSelecionados = [];
  }

  async GetListaFatura() {
    this.spinner.show();
    await this.faturaService.GetListaFatura(false).subscribe((ret) => {
      this.listaFatura = ret;
      
      this.spinner.hide();
    }, () => {
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar as faturas.')
      this.spinner.hide();
    })
    
  }

  AbrirModalDetalhesFatura(objFatura: FaturaModelview){
    this.matDialog.open(ModalDetalhesFaturaComponent, {
      width: '710px',
      height: '90vh',
      data:{
        idFatura: objFatura.idFatura,
        flgFaturaProcedimento: objFatura.flgFaturaProcedimento,
        desFatura: objFatura.descricao
      } 
    });
  }

  AdicionarNovaFatura() {
    this.router.navigate(['/fatura']);
  }

  pdfArquivo:any;

  GerarRelatorio() {

    if (this.listaFaturaSelecionados.length == 0){
      this.snackBarAlert.falhaSnackbar("Por favor selecione pelo menos uma fatura para gera o relatorio.")
      return; 
    }

    this.spinner.show();
    this.arquivoService.GerarRelatorioFaturas(this.listaFaturaSelecionados).subscribe((ret) => {

      this.pdfArquivo = ret;
      this.snackbar.sucessoSnackbar("Gerado com sucesso");
      this.downloadPdf();
      
      this.spinner.hide();

    })
  }

  downloadPdf() {
    Uteis.BaixarFileEmPDF(this.pdfArquivo, "Relatorio Faturas");
  }

  AdicionarFatura(event: any, id: number) {
    this.flgSelecionaTodos = false;
    if (event.checked && !this.ValidaFoiSelecionado(id)) {
      this.listaFaturaSelecionados!.push(id);
    } else {
      this.listaFaturaSelecionados = this.listaFaturaSelecionados!.filter(x => x != id);
    }
  }

  AdicionarTodos() {
    this.listaFaturaSelecionados = [];
    if (this.flgSelecionaTodos) {
      this.listaFaturaSelecionados = this.listaFatura.map(x => x.idFatura!);
    }
  }

  ValidaFoiSelecionado(id: number){
      return this.listaFaturaSelecionados!.find(x => x == id);
  }

}
