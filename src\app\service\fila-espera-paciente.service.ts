import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { CriptografarUtil } from '../Util/Criptografar.util';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class FilaEsperaPacienteService {

  constructor(
    private http: HttpClient,
  ) { }

  removerDaFilaEspera(tokenConexao: string) {
    return this.http.delete(`${environment.apiEndpoint}/fila-espera/remover-paciente/${tokenConexao}`);
  }

  salvarTokenFila(token: string): void {
    CriptografarUtil.localStorageCriptografado('tokenFilaEspera', token);
  }

  obterTokenFila(): string | null {
    return CriptografarUtil.obterLocalStorageCriptografado('tokenFilaEspera');
  }

  limparDadosFilaEspera(): void {
    CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');
    CriptografarUtil.removerLocalStorageCriptografado('tokenFilaEspera');
    CriptografarUtil.removerLocalStorageCriptografado('posicaoFila');
  }
}