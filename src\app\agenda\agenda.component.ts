import { ChatService } from './../service/chat.service';
import { Component, OnInit, ElementRef, ViewChild, TemplateRef } from '@angular/core';
import { CalendarEvent, CalendarMonthViewDay, DAYS_OF_WEEK, CalendarView, CalendarEventAction, CalendarEventTimesChangedEvent, DateFormatterParams, CalendarModule } from 'angular-calendar';
import {
  subMonths, addMonths, addDays, addWeeks, subDays, subWeeks, isSameMonth,
  isSameDay
} from 'date-fns';

import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { MedicoService } from '../service/medico.service';
import { AgendaService } from '../service/agenda.service';
import { AgendaEspera } from '../model/agenda';
import { PacienteService } from '../service/pacientes.service';
import { CommonModule, DatePipe, formatDate } from '@angular/common';
import {
  FormControl, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { Cliente } from '../model/cliente';
import { Contato } from '../model/contato';
import { ClinicaService } from '../service/clinica.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/es';
import { ValidadoreseMascaras } from '../Util/validadores';
import { ConvenioService } from '../service/convenio.service';
import { RecadoDia } from '../model/medico';
import { ConsultaService } from '../service/consulta.service';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { Pessoa } from '../model/pessoa';
import { UsuarioService } from '../service/usuario.service';
import { ICustomCalendarEvent } from '../Util/ICustomCalendar';
import { AcessoRapidoEmail } from '../model/acesso-rapido-consulta';
import { EnvioEmailService } from '../service/envioEmail.service';
import { irParaConsulta, Consulta } from '../model/consulta';
import { Router } from '@angular/router';
import { LocalStorageService } from '../service/LocalStorageService';
import { SignalHubService } from '../service/signalHub.service';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { MatDialog as MatDialog } from '@angular/material/dialog';
import { ModalEscolherHorarioComponent } from '../Modais/modal-escolher-horario/modal-escolher-horario.component';
import { ModalInfoSobreUsuarioComponent } from '../Modais/modal-info-sobre-usuario/modal-info-sobre-usuario.component';
import { FaturaService } from '../service/fatura.service';
import { FaturaModelview } from '../model/fatura';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatButtonToggleModule } from '@angular/material/button-toggle';



@Component({
  selector: 'app-agenda',
  templateUrl: './agenda.component.html',
  styleUrls: ['./agenda.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CalendarModule,
    NgxSmartModalModule,
    MatIcon,
    TranslateModule,
    MatFormFieldModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgSelectModule,
    MatDivider,
    TruncatePipe,
    MatTooltipModule,
    MatSelectModule,
    MatRadioModule,
    MatButtonToggleModule,
  ]
})


export class AgendaComponent implements OnInit {


  @ViewChild('agendaEspera') mdAgendaEspera!: TemplateRef<any>;
  @ViewChild('excluirAgendamentoEspera') mdExcluirAgendamentoEspera!: TemplateRef<any>;
  @ViewChild('marcarHorario') mdMarcarHorario!: TemplateRef<any>;
  @ViewChild('ModalNovoPaciente') ModalNovoPaciente!: TemplateRef<any>;

  constructor(
    private usuarioService: UsuarioService,
    private medicoService: MedicoService,
    private agendaService: AgendaService,
    public ngxSmartModalService: NgxSmartModalService,
    private clinicaService: ClinicaService,
    private pacientesService: PacienteService,
    private spinner: SpinnerService,
    // public snackBar: MatSnackBar,  
    private tradutor: TranslateService,
    private validador: ValidadoreseMascaras,
    private convenioService: ConvenioService,
    private consultaService: ConsultaService,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private emailService: EnvioEmailService,
    private router: Router,
    private localStorageService: LocalStorageService,
    private signalHubService: SignalHubService,
    private snackBarAlert: AlertComponent,
    private matDialog: MatDialog,
    private faturaService: FaturaService,
    private chatService: ChatService
  ) {
    registerLocaleData(localeFr);

    this.signalHubService.OnDisparaAlertNovaConsultaConsulta
      .subscribe(async () => {
        await this.Carregasalva('dia', true);
      });

    this.signalHubService.AtualizaPainelAtendimento
      .subscribe(() => {
        this.chatService.DisparaNotificacaoSonora();
        if (this.view == "day") {
          this.changeView('day');
          this.CarregaAgenda('dia');
        }
      });
  }

  DadosRecados: any = [];
  telCont: string = '';
  DiaRecado: string = '';
  DadosAgendaEspera: any = [];
  DesRecadoDia: string = '';
  NomeMedico: string = '';



  FlgRetorno: boolean = false;
  FlgdtaContador: boolean = false;
  paciAgendaVasil?: boolean;
  paciAgendaVal?: boolean;
  PagaAgendaVal?: boolean;
  pacienteValido: boolean = true;
  convenioValido: boolean = true;
  flgConvenioParticular: boolean = true;


  novoHorario: boolean = false;
  dadosNovoPaciente: any = [];
  DadosInformCancelament: any = [];
  DadosInformUsuario: any = [];
  vimTela = true;
  horaInicio: number = 9;
  MinutoInicio: number = 0;
  horaFim: number = 20;
  MinutoFim: number = 0;
  Tempo: number = 6;


  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;

  //flgProntuario
  // true -> Consulta básica
  // false -> Consulta em video
  flgProntuario: boolean = true;

  loginComMedico: boolean = false
  // Exclusao = 0;
  DadosEspecialidadeMedico: any = []
  cancelamento = false;
  Motivocancelameto = ''
  Exclusao: any;
  Edicao: boolean = false;
  public selectedTime?: string | null;
  dadosAgendaEdcao: any;
  DesAgendamento: string = "";
  Especialidade?: number;
  VindoPaciente: any;
  ListaMedicos: any;
  DadosEspecialidade: any;
  // usuario: Usuario;
  Dados: any;
  IdMedico?: number | null;
  IdPaciente?: number | null;
  DadosPacientes: any = [];
  TempoConsulta: any;
  dataMarcada?: Date;
  view: CalendarPeriod = 'month';
  viewDate: Date = new Date();
  activeDayIsOpen: boolean = false;
  prevBtnDisabled: boolean = false;
  nextBtnDisabled: boolean = false;
  clickedDate?: Date;
  clickedColumn?: number;
  // locale: string = this.tradutor.store.currentLang;
  locale: string = 'pt';
  weekStartsOn: number = DAYS_OF_WEEK.MONDAY;
  weekendDays: number[] = [DAYS_OF_WEEK.FRIDAY, DAYS_OF_WEEK.SATURDAY];
  selectedMonthViewDay?: CalendarMonthViewDay;
  selectedDayViewDate?: Date;
  selectedDays: any = [];
  dadosTipoAgendamento: any;
  tipoagendamento?: number | null;
  idConsulta?: number | null;
  CPF = ""
  cssClass: string = RED_CELL;
  cssBlock: string = BLUE_CELL;
  showMessageError = false;
  cliente: any
  Dtanasc?: boolean;
  DtanascLimpa = false;
  DtanascVasil = false;
  hraEdit?: boolean;
  hraEditVasil = true;
  @ViewChild('input1') inputEl!: ElementRef;
  legenda: boolean = false;
  clinicaVasil?: boolean;
  clinicaVal?: boolean;
  clinicas: any = new FormControl([]);

  valorConsultaParticular?: string;
  valorConsulta?: string | null;
  flgExigePagamento: boolean = false;
  idConvenio?: number | null;
  codConvenio?: string | null;
  DadosClinicas: any = [];
  dadosConvenio: any = [];
  diasTrabalhados: any = [];
  Agenda = false;
  objAgenda: any = [];
  flgHabilitaPagamento: boolean = false;


  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;
  actionsprimeira: CalendarEventAction[] = [
    {
      label: '<i class="fa fa-fw fa-times item" style="margin-top: -5px; font-size: 20px; float: right; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.handleEvent('Deleted', event);
      }
    },
    {
      label: '<i class="fa fa-fw fa-pencil item" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('Edited', event);
      }
    },

  ];

  actionsprimeiraComVideo: CalendarEventAction[] = [
    {
      label: '<i class="fa fa-fw fa-times item" style="margin-top: -5px; font-size: 20px; float: right; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.handleEvent('Deleted', event);
      }
    },
    {
      label: '<i class="fa fa-fw fa-pencil item" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('Edited', event);
      }
    },
    {
      label: '<i class="fa fa-info-circle item" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('info', event);
      }
    },
    {
      label: '<i class="fa fa-fw  fa-video item" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('video', event);
      }
    },
  ];


  actions: CalendarEventAction[] = [
    {
      label: '<i class="fa fa-fw fa-times" style="margin-top: -5px; font-size: 20px; float: right; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.handleEvent('Deleted', event);
      }
    },
    {
      label: '<i class="fa fa-fw fa-pencil" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('Edited', event);
      }
    },
    // {
    //   label: '<i class="fas fa-info-circle" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
    //   onClick: ({ event }: { event: CalendarEvent }): void => {
    //     this.Edicao = true;
    //     this.handleEvent('info', event);
    //   }
    // }
  ];
  actionsVideo: CalendarEventAction[] = [
    {
      label: '<i class="fa fa-fw fa-times" style="margin-top: -5px; font-size: 20px; float: right; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.handleEvent('Deleted', event);
      }
    },
    {
      label: '<i class="fa fa-fw fa-pencil" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('Edited', event);
      }
    },
    {
      label: '<i class="fas fa-info-circle" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('info', event);
      }
    },
    {
      label: '<i class="fa fa-fw fas fa-video" style="margin-top: -5px; font-size: 20px; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.Edicao = true;
        this.handleEvent('video', event);
      }
    },
  ];
  actionsFinalizada: CalendarEventAction[] = [
    {
      label: '<i class="fa fa-fw fa-check" style="margin-top: -5px; font-size: 20px; float: right; cursor: pointer;"></i>',
      onClick: ({ }: { event: CalendarEvent }): void => {
      }
    },
  ];


  actionsCancelada: CalendarEventAction[] = [
    {
      label: '<i class="fa fa-fw fa-eye item" style="margin-top: -5px; font-size: 20px; float: right; color:white !important; cursor: pointer;"></i>',
      onClick: ({ event }: { event: CalendarEvent }): void => {
        this.handleEvent('Cancelada', event);
      }
    },
  ];

  // name = 'SnackBarConfirmação';
  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔'
  // emailenviado: string = 'Email Enviado. ✔';


  idExclusaoAgendaEspera: any;
  idExclusaoMensagemDia: any;
  modalInfo: any = [];
  url_atual?: string;

  campoDesRecadoVazio: boolean = false;

  flgSomenteProntuario?: boolean;
  campoEmailInvalido = false;
  mensagemErroEmail = ""

  agenda: any = [];

  listaFatura: FaturaModelview[] = [];
  idFatura?: number;

  ngOnInit() {

    this.flgSomenteProntuario = this.usuarioLogadoService.getFlgProntuario();
    this.flgHabilitaPagamento = this.usuarioLogadoService.getFlgHabilitaPagamento()!;
    this.url_atual = window.location.origin;

    this.tipoAgendamento()
    this.IdMedico = null

    this.VindoPaciente = this.localStorageService.ConsultaAgenda;

    this.CarregaMedicos()

    this.varificaTipoUsuario()


    this.getPagamentoClinica()
    this.CarregaPacientes()

    this.Dados = []
    this.CarregaConvenio();
    if (this.VindoPaciente) {
      this.IdMedico = this.VindoPaciente.IdMedico
      this.IdPaciente = this.VindoPaciente.IdPaciente
      this.Especialidade = this.VindoPaciente.IdEspecialidade


      this.vimTela = false;
      this.CarregaAgenda('dia')
      this.localStorageService.clearByName("ConsultaAgenda");

    }
  }

  EnviarEmailAcesso(idagenda: any) {
    const objConsulta = this.objAgenda.filter((c: any) => c.idConsulta == idagenda);
    const objacesso = new AcessoRapidoEmail;
    objacesso.Email = objConsulta[0].emailPaci;
    objacesso.LinkAcesso = this.modalInfo.guid;
    objacesso.CodAcesso = this.modalInfo.CodAcesso;
    objacesso.NomeMedico = objConsulta[0].medico;
    objacesso.Paciente = objConsulta[0].nome;
    objacesso.DtaConsulta = new Date(objConsulta[0].dtaConsulta).toLocaleString();

    if (!objacesso.Email) {

      this.snackBarAlert.falhaSnackbar('Email do paciente não existente, Verifique!')
      return;
    }
    this.emailService.MandaEmailAcesso(objacesso).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar('Email com Acesso enviado!')

    }, () => {
      this.snackBarAlert.falhaSnackbar('Erro ao enviar Email, Verifique!')

    })

  }


  getPagamentoClinica() {
    this.clinicaService.getPagamentoClinica(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      // this.valorConsultaParticular = retorno

      if (retorno) {
        retorno = this.verificaCasaDecimal(retorno)
        retorno = this.aplicarMascaraValor(retorno)
        this.valorConsultaParticular = retorno
      }
      this.spinner.hide();

    });
  }



  validaPaciente() {
    if (this.IdPaciente == null) {
      this.pacienteValido = false
    } else
      this.pacienteValido = true
  }



  // ValidarEmail(value) {
  //
  //   if (value == undefined || !value.trim()) {
  //     this.mensagemErroEmail = "Esse campo precisa ser preenchido"
  //     this.campoEmailInvalido = true
  //     return
  //   } else {
  //     if (!this.validacao.validarEmail(value)) {
  //       this.campoEmailInvalido = true;
  //       this.mensagemErroEmail = "Email inválido"
  //     }
  //     else
  //       this.campoEmailInvalido = false;

  //     return

  //   }
  // }



  public monthViewColumnHeader({ date, locale }: DateFormatterParams): string | null {
    return new DatePipe(locale!).transform(date, 'EEE', locale);
  }

  public monthViewTitle({ date, locale }: DateFormatterParams): string | null {
    return new DatePipe(locale!).transform(date, 'MMM y', locale);
  }

  public weekViewColumnHeader({ date, locale }: DateFormatterParams): string | null {
    return new DatePipe(locale!).transform(date, 'EEE', locale);
  }

  public dayViewHour({ date, locale }: DateFormatterParams): string | null {
    return new DatePipe(locale!).transform(date, 'HH:mm', locale);
  }
  CarregaPacientes() {
    this.pacientesService.GetPacienteAgenda(this.CPF, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      this.DadosPacientes = []
      this.DadosPacientes = retorno.filter((c: any) => c.flgInativo != true);


      if (this.CPF != '' && this.CPF != undefined && this.CPF != null) {
        if (this.DadosPacientes.length == 1)
          this.IdPaciente = this.DadosPacientes[0].idCliente

      }

      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }


  handleEvent(action: string, event: CalendarEvent): void {
    if (action == 'Edited') {
      this.CarregaAgendaEdit(event.id);
    } else if (action == 'Deleted') {

      this.Exclusao = event;
      this.Motivocancelameto = '';
      this.ngxSmartModalService.getModal('cancelarHorario').open();

    } else if (action == 'info') {
      this.modalInformativo(event.id);
    } else if (action == 'video') {

      if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) { this.modalTelaConsultaVideo(event.id) }
    }
    else {
      this.MotivoCancelamento(event.id)

    }
  }

  modalTelaConsultaVideo(id: any) {
    this.usuarioConsultaAgenda = []

    var info = this.objAgenda.filter((c: any) => c.idConsulta == id);


    this.usuarioConsultaAgenda.push({
      nome: info[0].nomePaciente,
      idconsulta: info[0].idConsulta,
      dtaconsulta: info[0].dtaAgenda,
      tipoAgendamento: info[0].tipoconsulta,
      primeiraConsulta: info[0].flgConsulta,
      Clinica: info[0].clinica,
      flgSomenteProntuario: false,
      ImagemPessoa: info[0].imagemPaciente,
      idMedicoPessoa: info[0].idMedicoPessoa,
      idPacientePessoa: info[0].idPacientePessoa,
      IdPagamento: info[0].idPagamento,
      IdConvenio: info[0].idConvenio,
      valorConsulta: info[0].valorConsulta,
      flgExigePagamento: info[0].flgExigePagamento,

    })
    this.ngxSmartModalService.getModal('consultaAgenda').open();



  }

  IrConsulta(id: any, flg: any) {
    if (id != "" && id != 0) {
      const consulta = new irParaConsulta();
      consulta.idConsulta = id;
      consulta.flgSomenteProntuario = flg;
      this.localStorageService.Consulta = consulta;
      // sessionStorage.setItem("TelaStream", "true");
      if (flg) {
        this.router.navigate(['/prontuario']);

      }
      else {
        this.router.navigate(['/streaming']);

      }
    }


  }

  usuarioConsultaAgenda: any = []
  modalInformativo(id: any) {

    var info = this.objAgenda.filter((c: any) => c.idConsulta == id);
    this.modalInfo.guid = this.url_atual + "/atendimento/" + info[0].acessoGuid;
    this.modalInfo.CodAcesso = info[0].codAcesso;
    this.modalInfo.idagenda = id;
    this.ngxSmartModalService.getModal('infoConsulta').open();

  }

  async CarregaespecialidadeMedico() {
    if (this.IdMedico == null || this.IdMedico == 0) {
      this.Especialidade = 0;
    }
    this.DadosEspecialidadeMedico = [];
    await this.medicoService.getEspecialidadeMedicos(this.IdMedico).subscribe((retorno) => {

      // this.DadosEspecialidade.forEach(element => {
      //   retorno.forEach(elemento => {
      //     if (element.idEspecialidade == elemento.idEspecialidade)
      //       teste.push(element)
      //   });
      // });

      this.DadosEspecialidadeMedico = retorno;
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  CarregaAgendaEdit(id: any) {
    this.flgConvenioParticular = true;
    this.novoHorario = false;
    this.dadosAgendaEdcao = [];
    this.CPF = '';
    this.idConsulta = 0;
    this.Dados.dtaConsulta = ''
    this.Dados.dtanova = ''
    this.selectedTime = ''
    this.IdPaciente = null;
    this.DesAgendamento = ''
    this.Dtanasc = false;
    this.DtanascVasil = false;
    this.hraEditVasil = false;
    this.tipoAgendamento()
    this.idConvenio = null
    this.codConvenio = ''
    this.flgExigePagamento = false;
    this.flgProntuario = true;
    if (this.DadosPacientes.length == 0)
      this.CarregaPacientes();

    this.agendaService.getAgendaEdit(id).subscribe((retorno) => {

      if (retorno != null) {
        // this.ngxSmartModalService.getModal('marcarHorario').open();
        this.AbrirModalMarcarHorario();

        this.dadosAgendaEdcao = retorno.agenda;
        this.idConsulta = this.dadosAgendaEdcao.agenda.idConsulta;
        this.Dados.dtahora = new Date(this.dadosAgendaEdcao.dtaConsulta)
        this.FormataData(this.Dados.dtahora);

        this.Dados.dtanova = this.Dados.dtahora.substring(0, 10);
        this.selectedTime = this.Dados.dtahora.substring(11, 20);
        this.Dados.dtaConsulta = this.dadosAgendaEdcao.dtaConsulta;
        this.IdPaciente = this.dadosAgendaEdcao.agenda.idPaciente;
        this.DesAgendamento = this.dadosAgendaEdcao.agenda.desAnotacao;
        this.Especialidade = this.dadosAgendaEdcao.agenda.idEspecialidade;
        this.tipoagendamento = this.dadosAgendaEdcao.agenda.idTipoAgendamento;
        this.IdMedico = this.dadosAgendaEdcao.agenda.idMedico;


        this.idConvenio = this.dadosAgendaEdcao.consulta.idConvenio;

        if (this.idConvenio) {
          var convenio = this.dadosConvenio.filter((c: any) => c.idConvenio == this.idConvenio)
          if (convenio.length > 0) {
            if (convenio[0].desConvenio == "Particular")
              this.flgConvenioParticular = true
            else
              this.flgConvenioParticular = false
          }

        } else
          this.flgConvenioParticular = true

        this.codConvenio = this.dadosAgendaEdcao.consulta.codigoConvenio;

        // this.valorConsulta = this.dadosAgendaEdcao.consulta.valorConsulta ? this.verificaCasaDecimal(this.dadosAgendaEdcao.consulta.valorConsulta) : this.dadosAgendaEdcao.consulta.valorConsulta;

        if (this.dadosAgendaEdcao.consulta.valorConsulta) {
          this.dadosAgendaEdcao.consulta.valorConsulta = this.verificaCasaDecimal(this.dadosAgendaEdcao.consulta.valorConsulta)
          this.dadosAgendaEdcao.consulta.valorConsulta = this.aplicarMascaraValor(this.dadosAgendaEdcao.consulta.valorConsulta)
          this.valorConsulta = this.dadosAgendaEdcao.consulta.valorConsulta
        }


        this.flgProntuario = this.dadosAgendaEdcao.agenda.flgProntuario;
        this.flgExigePagamento = this.dadosAgendaEdcao.consulta.flgExigePagamento;

      }
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })

  }


  MotivoCancelamento(id: any) {
    this.DadosInformCancelament = [];
    this.agendaService.GetCancelamento(id).subscribe((retorno) => {


      this.DadosInformCancelament.nome = retorno.nome
      this.DadosInformCancelament.Motivo = retorno.motivo
      this.DadosInformCancelament.Dta = new Date(retorno.dtaCancelamento).toLocaleDateString()

      this.ngxSmartModalService.getModal('InforCancelamento').open();
      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })

  }

  events?: ICustomCalendarEvent[];

  CarregaMedicos() {
    this.medicoService.getMedicos(this.Especialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {



      this.ListaMedicos = retorno

      if (this.ListaMedicos.length == 1) {
        this.IdMedico = this.ListaMedicos[0].idMedico;

        this.CarregaAgenda('dia');

      }

      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  MotivoCampo() {
    if (this.cancelamento == true)
      this.cancelamento = false
  }
  CancelarConsulta() {

    if (this.Motivocancelameto == '' || this.Motivocancelameto == undefined) {
      this.cancelamento = true;
      return;
    }

    this.agendaService.InativarAgendamento(this.Exclusao.id, this.usuarioLogadoService.getIdUsuarioAcesso(), this.Motivocancelameto).subscribe(() => {
      this.Carregasalva('dia', true);
      // this.deleteEvent(this.Exclusao)
      this.ngxSmartModalService.getModal('cancelarHorario').close();
      this.spinner.hide();

    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }

  beforeMonthViewRender({ body }: { body: CalendarMonthViewDay[] }): void {

    body.forEach(day => {
      var hj = new Date();
      hj.setDate(hj.getDate() - 1)

      if (day.date < hj) {
        day.cssClass = this.cssBlock;
      }
      else if (this.diasTrabalhados.length > 0 && this.diasTrabalhados != undefined) {
        var dia = day.date.toString()
        this.diasTrabalhados.forEach((element: any) => {
          if (dia.substring(0, 3) == 'Mon' && element.diaSemana == 'Segunda') {
            day.cssClass = this.cssClass;
          }
          else if (dia.substring(0, 3) == 'Tue' && element.diaSemana == 'Terça') {
            day.cssClass = this.cssClass;
          }
          else if (dia.substring(0, 3) == 'Wed' && element.diaSemana == 'Quarta') {
            day.cssClass = this.cssClass;
          }
          else if (dia.substring(0, 3) == 'Thu' && element.diaSemana == 'Quinta') {
            day.cssClass = this.cssClass;
          }
          else if (dia.substring(0, 3) == 'Fri' && element.diaSemana == 'Sexta') {
            day.cssClass = this.cssClass;
          }
          else if (dia.substring(0, 3) == 'Sat' && element.diaSemana == 'Sabado') {
            day.cssClass = this.cssClass;
          }
          else if (dia.substring(0, 3) == 'Sun' && element.diaSemana == 'Domingo') {
            day.cssClass = this.cssClass;
          }
        });

      }


    });

  }

  ViewState = 'month';
  contadorDias(value: any) {
    if (value != '') {
      var teste = new Date()
      teste.setDate(teste.getDate() + parseInt(value));
      this.FlgdtaContador = true;
      this.viewDate = teste
      this.changeView('day')
      this.CarregaAgenda('dia')
    }
    else {
      this.viewDate = new Date();
      this.changeView('day')
      this.CarregaAgenda('mes')
    }
  }


  public MascaraDinheiro(evento: KeyboardEvent) {

    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }

    (<HTMLInputElement>evento.target).value = v
  }


  async CarregaAgenda(obj: any) {

    await this.CarregaespecialidadeMedico();
    this.Agenda = false;

    this.diasTrabalhados = []
    if (this.IdMedico && this.IdMedico != null) {
      this.Agenda = true;

    }
    else {
      this.Agenda = false;
      return
    }

    this.events = []
    await this.agendaService.GetDiasAgenda(this.IdMedico).subscribe(async (retornoDias: any) => {

      this.diasTrabalhados = retornoDias;
      const data = new Date();

      await this.agendaService.GetAgenda(this.IdMedico, 0, data.toDateString(), obj).subscribe(async (retornaEspecialidade) => {




        if (retornaEspecialidade.length) {
          this.agenda = []
          this.agenda = retornaEspecialidade;
          this.objAgenda = retornaEspecialidade;

          await this.agenda.forEach((element: any) => {

            let tipoAgen = '';
            if (element.tipoAgendamento != null) {
              tipoAgen = element.tipoAgendamento;
            }

            let cor = '';
            if (element.flgPrimeira) {
              cor = 'primeiraConsulta';
              if (element.flgFinalizada) {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: 'EscritaCss',
                    color: {
                      primary: '#008000',
                      secondary: '#228B22'
                    },

                    actions: this.actionsFinalizada,
                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
              else if (!element.flgProntuario) {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: 'EscritaCss',
                    color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                      primary: '#008000', // the primary event color (should be darker than secondary)
                      secondary: '#228B22' // the secondary event color (should be lighter than primary)
                    },
                    actions: this.actionsprimeiraComVideo,

                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
              else if (element.flgInicioConsulta) {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' Consulta em andamento entre ' + element.nomePaciente + ' e o  Dr. ' + element.nomeMedico + '.',
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: 'EscritaCss',
                    color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                      primary: '#b39660', // the primary event color (should be darker than secondary)
                      secondary: '#ffc861' // the secondary event color (should be lighter than primary)
                    },

                    actions: this.actionsFinalizada,
                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
              else {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: 'EscritaCss',
                    color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                      primary: '#008000', // the primary event color (should be darker than secondary)
                      secondary: '#228B22' // the secondary event color (should be lighter than primary)
                    },
                    actions: this.actionsprimeira,

                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }

            }

            else if (element.flgInativa) {

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: 'EscritaCss',
                  actions: this.actionsCancelada,

                  color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                    primary: '#FF0000', // the primary event color (should be darker than secondary)
                    secondary: ' #CC3333' // the secondary event color (should be lighter than primary)
                  },
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }

            else {


              if (element.flgFinalizada) {
                // background-color: rgb(250, 227, 227);
                // border-color: rgb(173, 33, 33);

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: cor,
                    color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                      primary: '#FF0000', // the primary event color (should be darker than secondary)
                      secondary: ' #CC3333' // the secondary event color (should be lighter than primary)
                    },
                    actions: this.actionsFinalizada,
                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
              else if (!element.flgProntuario) {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: cor,
                    actions: this.actionsVideo,
                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
              else if (element.flgInicioConsulta) {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' Consulta em andamento entre ' + element.nomePaciente + ' e o  Dr. ' + element.nomeMedico + '.',
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: 'EscritaCss',
                    color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                      primary: '#b39660', // the primary event color (should be darker than secondary)
                      secondary: '#ffc861' // the secondary event color (should be lighter than primary)
                    },

                    actions: this.actionsFinalizada,
                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
              else {

                this.events!.push(
                  {
                    id: element.idConsulta,
                    start: new Date(element.dtaAgenda),
                    title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '. ' + (element.especialidade ?? ''),
                    idmedico: element.idMedico,
                    idpaciente: element.idPaciente,
                    cssClass: cor,
                    actions: this.actions,
                    resizable: {
                      beforeStart: true,
                    },
                    draggable: false
                  })
              }
            }
          });
        }
        this.changeView('day')
        if (!this.FlgdtaContador)
          var date = new Date;
        else {
          var date = this.viewDate
          this.FlgdtaContador = false;
        }
        if (this.diasTrabalhados.length > 0 && this.diasTrabalhados != undefined) {
          var dia = date.toString()

          var teste = false;
          var diaClicado = this.diasTrabalhados.filter((c: any) => (c.diaSemana == 'Segunda' && dia.substring(0, 3) == 'Mon')
            || (dia.substring(0, 3) == 'Tue' && c.diaSemana == 'Terça')
            || (dia.substring(0, 3) == 'Wed' && c.diaSemana == 'Quarta')
            || (dia.substring(0, 3) == 'Thu' && c.diaSemana == 'Quinta')
            || (dia.substring(0, 3) == 'Fri' && c.diaSemana == 'Sexta')
            || (dia.substring(0, 3) == 'Sat' && c.diaSemana == 'Sabado')
            || (dia.substring(0, 3) == 'Sun' && c.diaSemana == 'Domingo'))
          if (diaClicado.length > 0) {
            if (isSameMonth(date, this.viewDate)) {

              teste = true;
              if (
                (isSameDay(this.viewDate, date) && this.activeDayIsOpen === true)
              ) {
                this.activeDayIsOpen = false;
              } else {
                this.activeDayIsOpen = true;
              }
              this.changeView('day')
            }
            teste = true;
            if (diaClicado[0].tempoConsulta == null) {
              this.TempoConsulta = '00:20:00';
              this.Tempo = 60 / parseInt('00:20:00'.substring(3, 5))
            }
            else {
              this.TempoConsulta = diaClicado[0].tempoConsulta;
              this.Tempo = 60 / parseInt(diaClicado[0].tempoConsulta.substring(3, 5))
            }
            this.horaInicio = parseInt(diaClicado[0].horaEntrada.substring(0, 2))
            this.MinutoInicio = parseInt(diaClicado[0].horaEntrada.substring(3, 5))
            this.MinutoFim = parseInt(diaClicado[0].horaSaida.substring(3, 5))
            this.horaFim = parseInt(diaClicado[0].horaSaida.substring(0, 2))

            this.Tempo = 3
          }
          if (teste == false) {
            this.ngxSmartModalService.getModal('excecao').open();
            this.TempoConsulta = '00:20:00';
            this.viewDate = date;
            this.horaInicio = 8
            this.horaFim = 20

            this.Tempo = 2
          }
        }
        else {
          this.ngxSmartModalService.getModal('excecao').open();
          this.TempoConsulta = '00:30:00';
          this.viewDate = date;
          this.horaInicio = 8
          this.horaFim = 20
        }

        this.ngxSmartModalService.getModal('Inicializando').close();
        this.spinner.hide();
      }, () => {
        console.error("erro no retorno especialidade")
        this.spinner.hide();
      })
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }



  beforeDayViewRender(renderEvent: any, date: any) {
    ;
    var hj = new Date()
    hj.setDate(hj.getDate())
    var click = date;
    click.setDate(click.getDate())
    if (hj.getDate() <= click.getDate() && hj.getMonth() <= click.getMonth()) {
      var clickHora = date
      clickHora.setTime(clickHora.getTime())
      var horario = new Date()
      horario.setTime(horario.getTime())
      if (clickHora <= horario) {
        renderEvent.body.hourGrid.forEach((hour: any) => {
          hour.segments.forEach((segment: any) => {
            if (segment.date.getTime() <= horario.getTime()) {
              segment.cssClass = 'bg-block';
            }
          });
        });

      }
    }
    else if (hj.getMonth() <= click.getMonth()) {

    }
    else {
      renderEvent.body.hourGrid.forEach((hour: any) => {
        hour.segments.forEach((segment: any) => {
          segment.cssClass = 'bg-block';
        });
      });
    }

  }





  dayClicked({ date }: { date: Date }): void {

    if (this.diasTrabalhados.length > 0 && this.diasTrabalhados != undefined) {
      var dia = date.toString()

      var teste = false;
      var diaClicado = this.diasTrabalhados.filter((c: any) => (c.diaSemana == 'Segunda' && dia.substring(0, 3) == 'Mon')
        || (dia.substring(0, 3) == 'Tue' && c.diaSemana == 'Terça')
        || (dia.substring(0, 3) == 'Wed' && c.diaSemana == 'Quarta')
        || (dia.substring(0, 3) == 'Thu' && c.diaSemana == 'Quinta')
        || (dia.substring(0, 3) == 'Fri' && c.diaSemana == 'Sexta')
        || (dia.substring(0, 3) == 'Sat' && c.diaSemana == 'Sabado')
        || (dia.substring(0, 3) == 'Sun' && c.diaSemana == 'Domingo'))
      if (diaClicado.length > 0) {
        if (isSameMonth(date, this.viewDate)) {
          this.viewDate = date;
          teste = true;
          if (
            (isSameDay(this.viewDate, date) && this.activeDayIsOpen === true)
          ) {
            this.activeDayIsOpen = false;
          } else {
            this.activeDayIsOpen = true;
          }
          this.changeView('day')
        }
        teste = true;
        if (diaClicado[0].tempoConsulta == null) {
          this.TempoConsulta = '00:20:00';
          this.Tempo = 60 / parseInt('00:20:00'.substring(3, 5))

        }
        else {
          this.TempoConsulta = diaClicado[0].tempoConsulta;
          this.Tempo = 60 / parseInt(diaClicado[0].tempoConsulta.substring(3, 5))

        }

        this.horaInicio = parseInt(diaClicado[0].horaEntrada.substring(0, 2))
        this.MinutoInicio = parseInt(diaClicado[0].horaEntrada.substring(3, 5))
        this.MinutoFim = parseInt(diaClicado[0].horaSaida.substring(3, 5))
        this.horaFim = parseInt(diaClicado[0].horaSaida.substring(0, 2))

      }

      if (teste == false) {
        this.ngxSmartModalService.getModal('excecao').open();
        this.viewDate = date;
        this.horaInicio = 8
        this.horaFim = 20
        this.Tempo = 3
      }

    }
    else {
      this.ngxSmartModalService.getModal('excecao').open();
      this.viewDate = date;
      this.horaInicio = 8
      this.horaFim = 20
      this.Tempo = 2
    }
  }

  ValidaTimerInter(hr: any, camp: any) {
    this.novoHorario = false;
    if (hr != '') {
      var isValid = /^([0-1]?[0-9]|2[0-4]):([0-5][0-9])(:[0-5][0-9])?$/.test(hr);
      if (!isValid) {
        this.novoHorario = true;
        (document.getElementById(camp.id) as HTMLInputElement).focus();
        this.tradutor.get('TELAAGENDA.NOVOHORARIOERRO').subscribe(() => {

          // this.AlgumErro(true, camp.name + ' ' + res);
          this.snackBarAlert.falhaSnackbar('Falha ao validar horário');
        });
      }
    }
  }

  exececaoDia() {
    this.changeView('day')
    this.ngxSmartModalService.getModal('excecao').close();
  }


  deleteEvent(eventToDelete: CalendarEvent) {
    this.events = this.events!.filter(event => event !== eventToDelete);
    this.Exclusao = 0
  }

  setView(view: CalendarView) {
    this.view = view;
  }

  CriarNovoAgendamento() {
    let DtAgendamento = new Date();
    DtAgendamento.setMinutes(DtAgendamento.getMinutes() + 2); // Adiciona 2 minutos à data atual
    this.EventoAbrirModalCriarAgendamento(DtAgendamento);
  }

  EventoAbrirModalCriarAgendamento(dtHorario: Date) {
    
    if (this.IdMedico == null || this.IdMedico == 0) {
      this.Especialidade = 0;
    }

    this.Dados.dtaConsulta = ''
    var hj = new Date()
    hj.setDate(hj.getDate())

    var click = dtHorario;
    if (hj >= click) {

      var clickHora = dtHorario
      clickHora.setTime(clickHora.getTime())
      var horario = new Date()
      horario.setTime(horario.getTime())
      if (clickHora > horario) {
        this.flgConvenioParticular = true

        this.CPF = ''
        this.dataMarcada = dtHorario
        this.Dados.dtahora = new Date(dtHorario);
        this.FormataData(this.Dados.dtahora);
        // new Date(dtHorario).toString();
        this.Dados.nome = this.usuarioLogadoService.getNomeUsuario()
        this.dadosNovoPaciente.cpf = ''
        if (!this.VindoPaciente) {
          this.IdPaciente = null
        }
        this.tipoagendamento = null
        // this.Especialidade = null
        this.paciAgendaVal = false;
        this.DesAgendamento = ''
        this.idConsulta = 0;
        this.Edicao = false;
        this.idConvenio = null
        this.codConvenio = ''
        this.FlgRetorno = false;
        this.paci.markAsUntouched()
        this.medi.markAsUntouched()
        this.Dados.dtanova = null
        this.selectedTime = null;
        this.pacienteValido = true;
        this.convenioValido = true;
        this.valorConsulta = null;
        this.flgProntuario = true;
        this.flgExigePagamento = false;

        if (this.DadosPacientes.length == 0)
          this.CarregaPacientes();
        // this.ngxSmartModalService.getModal('marcarHorario').open();
        this.AbrirModalMarcarHorario()
      }

    }
    else {

      this.flgConvenioParticular = true

      this.CPF = ''
      this.dataMarcada = dtHorario

      this.Dados.dtanova = null
      // this.Dados.dtahora = new Date(dtHorario).toLocaleString();
      this.Dados.dtahora = new Date(dtHorario);
      this.FormataData(this.Dados.dtahora);
      this.Dados.nome = this.usuarioLogadoService.getNomeUsuario();
      this.tipoagendamento = null
      // this.Especialidade = null
      this.paciAgendaVal = false;
      this.PagaAgendaVal = false;
      this.dadosNovoPaciente.cpf = ''
      this.idConsulta = 0;
      if (!this.VindoPaciente) {
        this.IdPaciente = null
      }
      this.DesAgendamento = ''
      this.Edicao = false;
      this.idConvenio = null
      this.codConvenio = ''
      this.FlgRetorno = false;
      this.paci.markAsUntouched();
      this.medi.markAsUntouched()
      if (this.DadosPacientes.length == 0)
        this.CarregaPacientes();
      this.pacienteValido = true;
      this.convenioValido = true;
      this.valorConsulta = null;
      this.selectedTime = null;
      this.flgProntuario = true;
      this.flgExigePagamento = false;
      // this.ngxSmartModalService.getModal('marcarHorario').open();

      this.AbrirModalMarcarHorario()
    }


  }


  hourSegmentClicked(date: Date) {
    if (this.IdMedico == null || this.IdMedico == 0) {
      this.Especialidade = 0;
    }

    this.Dados.dtaConsulta = ''
    var hj = new Date()
    hj.setDate(hj.getDate())

    var click = date;

    click.setDate(click.getDate())
    if (hj >= click) {

      var clickHora = date
      clickHora.setTime(clickHora.getTime())
      var horario = new Date()
      horario.setTime(horario.getTime())
      if (clickHora > horario) {
        this.flgConvenioParticular = true
        this.CPF = ''
        this.dataMarcada = date
        this.Dados.dtahora = new Date(date);
        this.FormataData(this.Dados.dtahora);
        this.Dados.nome = this.usuarioLogadoService.getNomeUsuario()
        this.dadosNovoPaciente.cpf = ''
        if (!this.VindoPaciente) {
          this.IdPaciente = null
        }
        this.tipoagendamento = null
        // this.Especialidade = null
        this.paciAgendaVal = false;
        this.DesAgendamento = ''
        this.idConsulta = 0;
        this.Edicao = false;
        this.idConvenio = null
        this.codConvenio = ''
        this.FlgRetorno = false;
        this.paci.markAsUntouched()
        this.medi.markAsUntouched()
        this.Dados.dtanova = null
        this.selectedTime = null;
        this.pacienteValido = true;
        this.convenioValido = true;
        this.valorConsulta = null;
        this.flgProntuario = true;
        this.flgExigePagamento = false;

        if (this.DadosPacientes.length == 0)
          this.CarregaPacientes();
        // this.ngxSmartModalService.getModal('marcarHorario').open();
        if (this.listaFatura.length == 0)
          this.GetListaFatura();
        this.ngxSmartModalService.getModal('marcarHorario').open();

        this.AbrirModalMarcarHorario()
      }

    }
    else {

      this.flgConvenioParticular = true

      this.CPF = ''
      this.dataMarcada = date

      this.Dados.dtanova = null
      this.Dados.dtahora = new Date(date);
      this.FormataData(this.Dados.dtahora);
      this.Dados.nome = this.usuarioLogadoService.getNomeUsuario();
      this.tipoagendamento = null
      // this.Especialidade = null
      this.paciAgendaVal = false;
      this.PagaAgendaVal = false;
      this.dadosNovoPaciente.cpf = ''
      this.idConsulta = 0;
      if (!this.VindoPaciente) {
        this.IdPaciente = null
      }
      this.DesAgendamento = ''
      this.Edicao = false;
      this.idConvenio = null
      this.codConvenio = ''
      this.FlgRetorno = false;
      this.paci.markAsUntouched();
      this.medi.markAsUntouched()
      if (this.DadosPacientes.length == 0)
        this.CarregaPacientes();
      if (this.listaFatura.length == 0)
        this.GetListaFatura();
      this.pacienteValido = true;
      this.convenioValido = true;
      this.valorConsulta = null;
      this.selectedTime = null;
      this.flgProntuario = true;
      this.flgExigePagamento = false;
      // this.ngxSmartModalService.getModal('marcarHorario').open();

      this.AbrirModalMarcarHorario()
    }


  }

  CarregaEspecialidade() {

    this.medicoService.getEspecialidade().then((retornaEspecialidade) => {
      this.DadosEspecialidade = retornaEspecialidade


      this.spinner.hide();
    }, () => {
      console.error("erro no retorno especialidade")
      this.spinner.hide();
    })
  }

  ValidaDta(dta: any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '')
      this.Dtanasc = false

    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }

  ValidaPaciAgenda(pac: any) {

    pac = this.IdPaciente;

    if (pac > 0) {
      this.paciAgendaVasil = false;
      this.paciAgendaVal = false;
    }
    else {
      this.paciAgendaVasil = false;
      this.paciAgendaVal = true;
    }
  }
  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraDataNovoPaciente(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }

  async Carregasalva(date: any, FlgSignal: any) {
    try {

      var TipoPesquisa = 'dia'
      await this.agendaService.GetAgenda(this.IdMedico, 0, date, TipoPesquisa).subscribe(async (retornaEspecialidade) => {

        this.agenda = []
        this.agenda = retornaEspecialidade
        this.objAgenda = retornaEspecialidade
        this.events = [];
        await this.agenda.forEach((element: any) => {
          var tipoAgen = ''
          if (element.tipoAgendamento != null)
            tipoAgen = element.tipoAgendamento

          let cor = '';

          if (element.flgPrimeira) {
            cor = 'primeiraConsulta';
            if (element.flgFinalizada) {

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: 'EscritaCss',
                  color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                    primary: '#008000', // the primary event color (should be darker than secondary)
                    secondary: '#228B22' // the secondary event color (should be lighter than primary)
                  },

                  actions: this.actionsFinalizada,
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
            else if (!element.flgProntuario) {
              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: 'EscritaCss',
                  color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                    primary: '#008000', // the primary event color (should be darker than secondary)
                    secondary: '#228B22' // the secondary event color (should be lighter than primary)
                  },
                  actions: this.actionsprimeiraComVideo,

                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
            else if (element.flgInicioConsulta) {

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' Consulta em andamento entre ' + element.nomePaciente + ' e o  Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: 'EscritaCss',
                  color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                    primary: '#b39660', // the primary event color (should be darker than secondary)
                    secondary: '#ffc861' // the secondary event color (should be lighter than primary)
                  },

                  actions: this.actionsFinalizada,
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
            else {

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: 'EscritaCss',
                  color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                    primary: '#008000', // the primary event color (should be darker than secondary)
                    secondary: '#228B22' // the secondary event color (should be lighter than primary)
                  },
                  actions: this.actionsprimeira,

                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }

          }

          else if (element.flgInativa) {

            this.events!.push(
              {
                id: element.idConsulta,
                start: new Date(element.dtaAgenda),
                title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                idmedico: element.idMedico,
                idpaciente: element.idPaciente,
                cssClass: 'EscritaCss',
                actions: this.actionsCancelada,

                color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                  primary: '#FF0000', // the primary event color (should be darker than secondary)
                  secondary: ' #CC3333' // the secondary event color (should be lighter than primary)
                },
                resizable: {
                  beforeStart: true,
                },
                draggable: false
              })
          }

          else {


            if (element.flgFinalizada) {
              // background-color: rgb(250, 227, 227);
              // border-color: rgb(173, 33, 33);

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: cor,
                  actions: this.actionsFinalizada,
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
            else if (!element.flgProntuario) {

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: cor,
                  actions: this.actionsVideo,
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
            else if (element.flgInicioConsulta) {

              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' Consulta em andamento entre ' + element.nomePaciente + ' e o  Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: 'EscritaCss',
                  color: { // can also be calendarConfig.colorTypes.warning for shortcuts to the deprecated event types
                    primary: '#b39660', // the primary event color (should be darker than secondary)
                    secondary: '#ffc861' // the secondary event color (should be lighter than primary)
                  },

                  actions: this.actionsFinalizada,
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
            else {


              this.events!.push(
                {
                  id: element.idConsulta,
                  start: new Date(element.dtaAgenda),
                  title: new Date(element.dtaAgenda).toLocaleString() + ' ' + tipoAgen + ' ' + element.nomePaciente + ' ' + 'Consulta com ' + ' Dr. ' + element.nomeMedico + '.',
                  idmedico: element.idMedico,
                  idpaciente: element.idPaciente,
                  cssClass: cor,
                  actions: this.actions,
                  resizable: {
                    beforeStart: true,
                  },
                  draggable: false
                })
            }
          }

        });
        if (FlgSignal == false) {
          this.fechaModal_MarcarHorario();
          this.snackBarAlert.sucessoSnackbar("Horário agendado com sucesso!")

        }
        this.spinner.hide();
      }, () => {
        console.error("erro no retorno especialidade")
        this.spinner.hide();
      })
    } catch (error) {
      console.error(error)
      this.spinner.hide();
    }
  }


  excecaoModal() {
    this.changeView('month')
    this.ngxSmartModalService.getModal('excecao').close();
  }

  tipoAgendamento() {
    this.agendaService.getTipoAgendamento(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {


      this.dadosTipoAgendamento = retorno;
      this.spinner.hide();
    })
  }

  ValidaDtaEd(dta: any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.DtanascVasil = true;
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.DtanascVasil = false;
      this.Dtanasc = false;
      this.DtanascLimpa = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
      this.DtanascVasil = false;
    }
    else
      this.Dtanasc = false
  }

  VerificaHora(hora: any) {
    var hra = /^[0-9]{2}:[0-9]{2}$/;
    if (hora == '') {
      this.hraEditVasil = true;
      this.hraEdit = false;
    }
    else if (hra.test(hora)) {
      this.hraEditVasil = false;
      this.hraEdit = false;
    }
  }

  VerificaHoraChange() {
    const hora = (document.getElementById('horaEdcao') as HTMLInputElement)['value'];
    var hra = /^[0-9]{2}:[0-9]{2}$/;
    if (hora == '' || hora == undefined) {
      this.hraEditVasil = true;
      this.hraEdit = false;
    }
    else if (hra.test(hora)) {
      this.hraEditVasil = false;
      this.hraEdit = false;
    }
  }

  ValidaDtaChange() {
    const dta = (document.getElementById('Dtanova') as HTMLInputElement)['value'];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '' || dta == undefined) {
      this.DtanascVasil = true;
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.DtanascVasil = false;
      this.Dtanasc = false;
      this.DtanascLimpa = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
      this.DtanascVasil = false;
    }
    else
      this.Dtanasc = false;
  }

  public validarConsulta() {

    this.showMessageError = false;
    this.VerificaHoraChange();
    this.ValidaDtaChange();
    if (this.Dtanasc == true || this.DtanascVasil == true
      || this.hraEdit == true || this.hraEditVasil == true) {

      this.showMessageError = true;
    }
  }

  SalvarConsulta() {
    if (this.Edicao) {
      this.validarConsulta();
      if (this.showMessageError) {
        return;
      }
    }
    try {
      if (!this.IdPaciente || !this.IdMedico || this.Dtanasc) {

        this.medi.markAsTouched();
        this.proce.markAsTouched();
        this.valor.markAsTouched();
        this.paci.markAsTouched();
        this.validaPaciente();
        return;
      }
      if (this.flgExigePagamento && !this.valorConsulta) {

        this.valor.markAsTouched();
        return;
      }
      if (this.novoHorario) {
        return;
      }

      const agenda = new Consulta();


      agenda.flgexigePagamento = !this.flgProntuario ? this.flgExigePagamento : false;
      if (this.idConsulta! > 0 && this.idConsulta != undefined) {
        agenda.IdConsulta = this.idConsulta;
      }
      if (this.Dados.dtanova) {

        if (this.selectedTime) {

          const datenova = this.validador.MontaData(this.Dados.dtanova.toString(), this.selectedTime);
          agenda.DtaConsulta = datenova;
        }
        else {

          const hora = new Date(this.Dados.dtaConsulta)
          const data = this.validador.Convertdata(this.Dados.dtanova.toString())


          const datenova = this.validador.SetHorasData(data!, hora.toTimeString())
          agenda.DtaConsulta = datenova;
        }
      }
      else if (this.selectedTime) {

        const datenova = this.validador.SetHorasData(this.Dados.dtanova, this.selectedTime);
        agenda.DtaConsulta = datenova;
      }
      else
        agenda.DtaConsulta = this.dataMarcada;




      agenda.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
      agenda.idTipoAgendamento = this.tipoagendamento!;
      agenda.IdMedico = this.IdMedico;
      if (this.IdPaciente > 0) {
        agenda.IdPaciente = this.IdPaciente;

      }
      agenda.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
      agenda.DtaCadastro = new Date;
      if (this.Especialidade! > 0)
        agenda.IdEspecialidade = this.Especialidade;
      agenda.FlgInativo = false;
      agenda.DesAnotacao = this.DesAgendamento;
      agenda.TempoConsulta = this.TempoConsulta
      agenda.flgProntuario = this.flgProntuario;

      // if (this.valorConsulta)
      //   agenda.ValorConsulta = document.getElementById('valorConsulta')['value'];


      if (this.valorConsulta) {
        this.valorConsulta = this.validacao.removeMascara(this.valorConsulta);
        this.valorConsulta = this.valorConsulta!.replace(/(\d{1})(\d{1,2})$/, "$1.$2");
        agenda.ValorConsulta = this.valorConsulta;
      }

      agenda.CodigoConvenio = this.codConvenio!;
      agenda.idConvenio = this.idConvenio!;
      agenda.idFatura = this.idFatura;
      agenda.FlgRetorno = this.FlgRetorno



      // return;

      if (agenda.DtaConsulta) {
        this.agendaService.salvarAgendamento(agenda).subscribe((retorno) => {


          if (!agenda.IdConsulta)
            this.consultaService.atualizaComparativo$.emit();



          if (!retorno) {
            this.snackBarAlert.falhaSnackbar("Falha ao salvar consulta!")

          } else {
            this.agendaService.atualizaDadosMes$.emit(retorno)


            this.Carregasalva(new Date(agenda.DtaConsulta!), false)
            this.CarregaMedicos();
          }

          this.idConsulta = 0
          this.IdPaciente = null
          this.DesAgendamento = ""
          this.Especialidade = 0
          this.tipoagendamento = 0
          this.idConvenio = null;
          this.codConvenio = '';
          this.CPF = ''
          this.paci.markAsUntouched()
          this.medi.markAsUntouched()
          this.DtanascVasil = false;
          this.hraEditVasil = false;
          this.spinner.hide();

          if (this.flgExcluiAgenda) {
            this.idExclusaoAgendaEspera = this.objPacienteAtendimento.idAgenda
            this.InativarAgendaEspera()
          }
        }, err => {
          this.snackBarAlert.falhaSnackbar("Falha ao salvar a consulta")
          console.error(err)
          this.spinner.hide();
        })
      }

    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão")
      console.error(error)
      this.spinner.hide();
    }
  }

  // beforeDayViewRender(dayView: DayViewHour[]) {


  //   this.dayView = dayView;
  //   this.addSelectedDayViewClass();
  // }
  eventTimesChanged({

    event,
    newStart,
    newEnd
  }: CalendarEventTimesChangedEvent): void {

    this.events = this.events!.map(iEvent => {
      if (iEvent === event) {
        return {
          ...event,
          start: newStart,
          end: newEnd
        };
      }
      return iEvent;
    });
    this.handleEvent('Dropped or resized', event);
  }



  increment(): void {

    if (this.view == 'month')
      this.changeDateMEs(addPeriod(this.view, this.viewDate, 1));

    else
      this.changeDate(addPeriod(this.view, this.viewDate, 1));


  }

  today(): void {
    (document.getElementById('CalculaDia') as HTMLInputElement)['value'] = ""
    this.changeDate(new Date());

  }

  decrement(): void {

    if (this.view == 'month')
      this.changeDateMEs(subPeriod(this.view, this.viewDate, 1));
    else
      this.changeDate(subPeriod(this.view, this.viewDate, 1))

  }

  changeDate(date: Date): void {

    this.viewDate = date;
    this.dayClicked({ date })

  }

  changeDateMEs(date: Date): void {
    this.viewDate = date;

  }

  changeView(view: CalendarPeriod): void {
    this.view = view;
  }



  // CancelarHorarioSnack(value) {
  //   if (value == true) {
  //     // let config = new MatSnackBarConfig();
  //     // config.verticalPosition = this.verticalPosition;
  //     // config.horizontalPosition = this.horizontalPosition;
  //     // config.duration = this.setAutoHide ? this.autoHide : 0;
  //     // config.panelClass = ['success-snack'];

  //     // this.tradutor.get('TELAAGENDA.EMAILENVIADO').subscribe((res: string) => {

  //     //   this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     // });
  //     this.snackBarAlert.sucessoSnackbar("E-mail enviado com sucesso")
  //   }
  //   this.cancelamento = false;
  // }
  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }

  AddnovoPaciente() {
    this.dadosNovoPaciente = []

    this.campoCPFVazil = false;
    this.campoCPFInvalido = false;

    this.CarregaClinicas();
    // this.ngxSmartModalService.getModal('ModalNovoPaciente').open();
    this.abrirModalAddPaciente();

  }
  ref_ModalAddPaciente: any;
  abrirModalAddPaciente() {
    if (this.ref_ModalAddPaciente != null) {
      this.ref_ModalAddPaciente.close();
      this.ref_ModalAddPaciente = null;
    }

    this.ref_ModalAddPaciente = this.matDialog.open(this.ModalNovoPaciente);
  }

  fecharModalAddPaciente() {
    this.ref_ModalAddPaciente.close();
    this.ref_ModalAddPaciente = null;
  }

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }
  ValidaClinicas(cli: any = []) {
    this.clinicas.value.forEach((element: any) => {

      cli.push({
        idClinica: element.idClinica,
      })
    });
    if (cli.length > 0) {
      this.clinicaVasil = false;
      this.clinicaVal = false;
    }
    else {
      this.clinicaVasil = false;
      this.clinicaVal = true;
    }
  }

  SubmitNovoPaciente() {
    try {
      this.validarCampos();

      if (this.showMessageError == true) {
        this.snackBarAlert.falhaSnackbar("Erro ao Salvar paciente")
        return;
      }

      var pessoa = new Pessoa();
      var cliente = new Cliente();
      var contato = new Contato()


      if (this.cliente != null) {

        if (this.cliente.paciente) {
          cliente.idCliente = this.cliente.paciente.idCliente
        }
        else
          cliente.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()

        pessoa.idPessoa = this.cliente.pessoa.idPessoa
        pessoa.idContato = this.cliente.pessoa.idContato
        contato.idContato = this.cliente.contato.idContato
      }
      else
        pessoa.idUsuarioGerador = contato.idUsuarioGerador = cliente.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

      pessoa.cpf = this.dadosNovoPaciente.cpf;
      pessoa.nomePessoa = this.dadosNovoPaciente.nome;
      pessoa.dtaNascimento = this.validador.Convertdata(this.dadosNovoPaciente.dtaNascimento)
      pessoa.email = this.dadosNovoPaciente.email;

      contato.telefoneMovel = this.dadosNovoPaciente.telefoneMovel;

      cliente.procedencia = this.dadosNovoPaciente.procedencia;
      cliente.flgBoasVindas = this.dadosNovoPaciente.flgBoasVindas;


      var Clinic: any = []
      this.clinicas.value.forEach((element: any) => {
        Clinic.push({
          idClinica: element.idClinica,
        })
      });
      if (Clinic.length > 0)
        cliente.clinicas = Clinic;

      pessoa.contato = contato;
      cliente.pessoa = pessoa


      this.pacientesService.salvarPacienteAgenda(cliente, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornoId) => {

        if (!retornoId) {
          this.snackBarAlert.falhaSnackbar("Falha ao salvar")
        }
        else {
          this.LimparCamposNovoPaciente();
          // this.CarregaPacientes();
          this.pacientesService.GetPacienteAgenda(this.CPF, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {


            this.DadosPacientes = []

            this.DadosPacientes = retorno.filter((c: any) => c.flgInativo != true);

            this.IdPaciente = retornoId;


            this.campoCPFVazil = false;
            this.campoCPFInvalido = false;

            this.ngxSmartModalService.getModal('ModalNovoPaciente').close();
            this.snackBarAlert.sucessoSnackbar("Salvo com sucesso!");

          }, err => {
            console.error(err)
          })
        }

        this.spinner.hide();
      }, err => {
        console.error(err)
        this.spinner.hide();
      })


    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão");
      console.error(error);
      this.spinner.hide();
    }
  }

  LimparCamposNovoPaciente() {

    this.dadosNovoPaciente = []
    this.clinicas = new FormControl([]);
    this.cpf.markAsUntouched();
    this.Nome.markAsUntouched();
    this.email.markAsUntouched();
    this.DtaNasc.markAsUntouched();
    this.tel.markAsUntouched();
    this.proce.markAsUntouched();
    this.dtaNascNovoPaciente.markAsUntouched();

    this.campoCPFInvalido = false;
    this.campoCPFVazil = false;
  }
  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }



  public mascaraText(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    this.dadosNovoPaciente.nome = v;
    // (<HTMLInputElement>evento.target).value = v
  }



  public mascaraCpf(mascara: any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 14) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 11) {
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }

  CarregaClinicas() {
    try {
      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {

        this.DadosClinicas = retornaClinicas
        this.spinner.hide()


      }, () => {
        console.error("erro no retorno especialidade")
        this.spinner.hide()
      })
    } catch (error) {
      console.error(error)
      this.spinner.hide()
    }
  }


  public validarCampos() {

    this.showMessageError = false;
    this.cpf.markAsTouched();
    this.Nome.markAsTouched();
    this.email.markAsTouched();
    this.DtaNasc.markAsTouched();
    this.dtaNascNovoPaciente.markAsTouched();
    this.tel.markAsTouched();
    this.proce.markAsTouched();


    if (!this.validacao.cpf(this.dadosNovoPaciente.cpf)) {
      this.campoCPFInvalido = true;
      return;
    }


    if (this.dadosNovoPaciente.cpf == undefined || !this.dadosNovoPaciente.cpf.trim())
      this.campoCPFVazil = true;


    if (this.dadosNovoPaciente.nome == undefined || !this.dadosNovoPaciente.nome.trim()
      || this.dadosNovoPaciente.cpf == undefined || !this.dadosNovoPaciente.cpf.trim()
      || this.dadosNovoPaciente.telefoneMovel == undefined || !this.dadosNovoPaciente.telefoneMovel.trim()
      || this.Dtanasc == true || this.clinicaVal == true || this.clinicaVasil == true
      || this.campoEmailVazil == true || this.campoEmailInvalido == true
      || this.campoCPFVazil == true || this.campoCPFInvalido == true) {

      this.showMessageError = true;
      if (this.clinicaVal != true && this.clinicaVal != false) {
        this.clinicaVasil = true;
      }
      if (this.clinicaVasil == true) {
        this.clinicaVal = true;
      }
    }
  }
  Nome = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  cpf = new FormControl('', [Validators.minLength(10), Validators.maxLength(11), Validators.required])
  DtaNasc = new FormControl('', [Validators.required, Validators.maxLength(11)])
  tel = new FormControl('', [Validators.required, Validators.maxLength(11)])
  proce = new FormControl('', [Validators.required, Validators.maxLength(11)])
  paci = new FormControl('', [Validators.required, Validators.maxLength(11)])
  medi = new FormControl('', [Validators.required, Validators.maxLength(11)])
  conv = new FormControl('', [Validators.required, Validators.maxLength(11)])
  codConv = new FormControl('', [Validators.required, Validators.maxLength(11)])
  valor = new FormControl('', [Validators.required, Validators.maxLength(11)])
  dtaNascNovoPaciente = new FormControl('', [Validators.required, Validators.required])


  getErrorMessageDtaNascNovoPaciente() {
    return this.dtaNascNovoPaciente.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.dtaNascNovoPaciente.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessageConv() {
    return this.conv.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.conv.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessageCodConv() {
    return this.codConv.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.codConv.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessagevalorConsulta() {
    return this.valor.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.valor.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessagepaci() {
    return this.paci.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.paci.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';

  }
  getErrorMessagemedi() {
    return this.medi.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.medi.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }

  getErrorMessageNome() {
    return this.Nome.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.Nome.hasError('Nome') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';

  }


  getErrorMessagePROCE() {
    return this.proce.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.proce.hasError('Procedência') ? 'TELAAGENDA.ERRONAOEVALIDA' :
        '';
  }


  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.email.hasError('email') ? 'TELAAGENDA.ERROEMAIL' :
        '';
  }

  getErrorMessageCPF() {
    return this.cpf.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.cpf.hasError('cpf') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }

  geterrorMessageDate() {
    return this.DtaNasc.hasError('required') ? 'TELAAGENDA.ERRODATA' :
      this.DtaNasc.hasError('Data de Nascimento') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }

  getErrorMessagetel() {
    return this.tel.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.tel.hasError('Plano de Saúde') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }

  LimpaMensErroPaci() {
    this.paciAgendaVal = false;
    this.flgExcluiAgenda = false;
    this.IdPaciente = null
  }



  informacao(user: any, id: any) {
    this.DadosInformUsuario = {}
    if (user == 'Paciente') {

      var user = this.DadosPacientes.filter((c: any) => c.idCliente == id);
      this.DadosInformUsuario = user[0]
      this.matDialog.open(ModalInfoSobreUsuarioComponent, {
        data: this.DadosInformUsuario
      })

      // this.ngxSmartModalService.getModal('InforUsuario').open();
    }
    else {
      this.DadosInformUsuario = this.ListaMedicos.filter((c: any) => c.idMedico == id);
      this.matDialog.open(ModalInfoSobreUsuarioComponent, {
        data: this.DadosInformUsuario
      })

      // this.ngxSmartModalService.getModal('InforUsuario').open();
    }
  }



  CarregaConvenio() {

    this.convenioService.getConvenios(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      this.dadosConvenio = retorno;
      this.spinner.hide();
    },
      () => {
        this.spinner.hide();
      })
  }


  MensagemModal() {
    this.DesRecadoDia = '';
    var medico = this.ListaMedicos.filter((c: any) => c.idMedico == this.IdMedico)
    this.NomeMedico = medico[0].nomeMedico
    this.DiaRecado = this.viewDate.toLocaleDateString();
    this.carregarMensagemDia();
    // var idMedicoAcesso = this.ListaMedicos.filter((c:any) => c.idMedico == this.IdMedico)
    // var idAcesso = idMedicoAcesso[0].idUsuarioacesso;


  }



  agendaesperaModal() {
    this.DesAgendamento = '';
    this.IdPaciente = null;
    this.CarregaAgendaEspera();
    this.abrirModalAgenda();
    this.pacienteValido = true
    this.DiaRecado = this.viewDate.toLocaleDateString();
  }

  CarregaAgendaEspera() {
    this.agendaService.GetAgendaEspera(this.viewDate.toDateString(), this.IdMedico).subscribe((retorno) => {
      if (retorno) {
        var telefones: any = []
        retorno.forEach((elemento: any) => {

          elemento.tel = elemento.tel.replace(/\D/g, "");
          elemento.tel = elemento.tel.replace(/^(\d{2})(\d)/g, "($1) $2");
          elemento.tel = elemento.tel.replace(/(\d)(\d{4})$/, "$1-$2");

          telefones.push(elemento)
        });

        this.DadosAgendaEspera = telefones;
      }
      this.spinner.hide();
    }, err => {
      this.snackBarAlert.falhaSnackbar('Erro ao Carregar Agenda de Espera')
      console.error(err)
      this.spinner.hide();
    })

  }

  dialogRef_Agenda: any;
  dialogRef_Excluir: any;
  dialogRef_MarcarHorario: any;

  abrirModalAgenda() {

    this.dialogRef_Agenda = this.matDialog.open(this.mdAgendaEspera, {
      disableClose: true
    });

    this.dialogRef_Agenda.afterClosed().subscribe(() => {

    });
  }

  AbrirModalExcluir() {

    this.dialogRef_Excluir = null;
    this.dialogRef_Excluir = this.matDialog.open(this.mdExcluirAgendamentoEspera, {
      disableClose: true
    });

    this.dialogRef_Excluir.afterClosed().subscribe(() => {

    });
  }

  AbrirModalMarcarHorario() {

    this.dialogRef_MarcarHorario = this.matDialog.open(this.mdMarcarHorario, {
      disableClose: true
    });

    this.dialogRef_MarcarHorario.afterClosed().subscribe(() => {

    });
  }

  fechaModal_Agenda() {
    this.flgExcluiAgenda = false;
    this.IdPaciente = null;
    this.dialogRef_Agenda.close();
  }

  fechaModal_Excluir() {
    this.dialogRef_Excluir.close();
  }

  fechaModal_MarcarHorario() {
    this.dialogRef_MarcarHorario.close();
  }



  objPacienteAtendimento: any;
  flgExcluiAgenda: boolean = false
  AdicionarAgendamento(objItem: any) {

    this.objPacienteAtendimento = objItem;
    this.IdPaciente = objItem.idPaciente;
    let dialog = this.matDialog.open(ModalEscolherHorarioComponent, {
      data: this.viewDate
    });

    dialog.afterClosed().subscribe((ret) => {
      if (ret) {

        this.processarHorarioSelecionado(ret);
      } else {

      }
    });
  }

  // Exemplo de método para processar o horário selecionado
  processarHorarioSelecionado(horario: Date) {
    this.IdPaciente = this.IdPaciente;
    this.IdMedico = this.objPacienteAtendimento.idMedico;
    this.Dados.dtahora = horario;
    this.VindoPaciente = true;

    this.FormataData(this.Dados.dtahora);
    this.flgExcluiAgenda = true;
    this.EventoAbrirModalCriarAgendamento(horario);


  }
  AbrirModalExclusao(id: any) {
    this.idExclusaoAgendaEspera = id
    // this.ngxSmartModalService.getModal('excluirAgendamentoEspera').open();
    this.AbrirModalExcluir();

  }
  InativarAgendaEspera() {
    //retirar id
    this.agendaService.InativarAgendaEspera(this.idExclusaoAgendaEspera, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe(() => {

      this.CarregaAgendaEspera();
      this.snackBarAlert.sucessoSnackbar("Agendamento em espera excluido")
      this.fechaModal_Excluir();
      this.spinner.hide();

    }, err => {
      this.snackBarAlert.falhaSnackbar('Erro ao Carregar Agenda de Espera')
      console.error(err)
      this.spinner.hide();
    })
  }

  ValidaCampoNovoRecado() {

    if (this.DesRecadoDia == null || !this.DesRecadoDia.trim())
      this.campoDesRecadoVazio = true
    else
      this.campoDesRecadoVazio = false
  }
  SalvarMensagem() {
    try {
      if (this.DesRecadoDia == null || !this.DesRecadoDia.trim()) {
        this.snackBarAlert.falhaSnackbar('Preencha o campo da mensagem')
        this.campoDesRecadoVazio = true;
        this.DesRecadoDia = "";
        return
      }

      var mens = new RecadoDia
      mens.DesRecado = this.DesRecadoDia;
      mens.IdMedico = this.IdMedico!;
      mens.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
      mens.DtaDiaRecado = this.viewDate;
      mens.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
      mens.FlgVisualizado = false;
      this.medicoService.salvarRecadoDia(mens).subscribe((retorno) => {

        if (retorno) {
          this.snackBarAlert.sucessoSnackbar('Recado criado com sucesso.');
          this.DesRecadoDia = "";
          this.campoDesRecadoVazio = false;
          this.carregarMensagemDia();
        }
        this.spinner.hide();

      }, err => {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar")
        console.error(err)
        this.spinner.hide();
      })


    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão")
      console.error(error)
      this.spinner.hide();
    }
  }

  DeletarMensagemDia() {
    this.medicoService.DeletarRecadoDia(this.idExclusaoMensagemDia, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe(() => {
      this.snackBarAlert.sucessoSnackbar('Mensagem excluida com Sucesso.')
      this.carregarMensagemDia();
      this.ngxSmartModalService.getModal('excluirMensagemDia').close();
      this.spinner.hide();
    })
  }



  AbrirModalDeletarMensagemDia(id: any) {
    this.idExclusaoMensagemDia = id
    this.ngxSmartModalService.getModal('excluirMensagemDia').open();
  }

  carregarMensagemDia() {
    var idMedicoAcesso = this.ListaMedicos.filter((c: any) => c.idMedico == this.IdMedico)
    var idAcesso = idMedicoAcesso[0].idUsuarioacesso;
    // var dtaHj = new Date(this.viewDate).toLocaleDateString();
    this.medicoService.getRecadoDia(idAcesso, this.usuarioLogadoService.getIdUltimaClinica(), 'Todos', new Date(this.viewDate).toDateString()).subscribe((retorno) => {
      if (retorno) {


        this.DadosRecados = retorno;
        this.ngxSmartModalService.getModal('mensagemDiaAgenda').open();
      }
      this.spinner.hide();
    }, err => {

      this.snackBarAlert.falhaSnackbar('Erro ao Carregar Agenda de Espera')
      console.error(err)
      this.spinner.hide();
    })

  }

  SalvarAgendaEspera() {
    try {
      if (!this.IdPaciente) {
        this.paci.markAsTouched();
        this.validaPaciente();
        return
      }

      var espera = new AgendaEspera();
      espera.DtaConsulta = new Date(this.viewDate);
      espera.IdPaciente = this.IdPaciente;
      espera.IdMedico = this.IdMedico!;
      espera.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;
      espera.FlgInativo = false;
      espera.DtaCadastro = new Date();
      espera.DesAnotacao = this.DesAgendamento;
      espera.idClinica = this.usuarioLogadoService.getIdUltimaClinica()!;
      espera.TelContato = this.telCont;
      this.agendaService.salvarAgendaEspera(espera).subscribe(() => {
        this.CarregaAgendaEspera();
        this.DesAgendamento = '';
        this.telCont = '';
        this.IdPaciente = null;
        this.snackBarAlert.sucessoSnackbar("Agendamento Salvo com Sucesso")
        this.spinner.hide();
      })

    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão")
      console.error(error)
      this.spinner.hide();
    }
  }


  RetornoConvenio(idPaciente: any, idConvenio: any) {

    if (!idPaciente || !idConvenio)
      return
    this.spinner.show();

    this.consultaService.GetRetornoConvenio(idPaciente, idConvenio).subscribe((retorno) => {
      this.spinner.hide();
      this.FlgRetorno = retorno;
    }, (erro) => {
      this.spinner.hide();
      console.error(erro);
    })

  }

  PreencheValorPagamento() {

    this.codConvenio = null
    this.valorConsulta = null

    if (this.idConvenio) {

      var convenio = this.dadosConvenio.filter((c: any) => c.idConvenio == this.idConvenio)

      if (convenio[0].desConvenio == "Particular") {
        // this.valorConsulta = this.valorConsultaParticular
        this.flgConvenioParticular = true
      }
      else {
        this.flgConvenioParticular = false
      }

      // this.valorConsulta = this.verificaCasaDecimal(convenio[0].valorConsulta)


      if (convenio[0].valorConsulta) {
        this.valorConsulta = convenio[0].valorConsulta;
        this.valorConsulta = this.verificaCasaDecimal(this.valorConsulta)
        this.valorConsulta = this.aplicarMascaraValor(this.valorConsulta)
      }



    } else
      this.flgConvenioParticular = true

  }

  verificaConvenio() {
    if (this.codConv == null || this.codConv == undefined) {
      this.valorConsulta = null
    }
  }



  varificaTipoUsuario() {
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.loginComMedico = true

      this.medicoService.getMedicos(this.Especialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        this.ListaMedicos = retorno

        var medico = this.ListaMedicos.filter((c: any) => c.idUsuarioacesso == this.usuarioLogadoService.getIdUsuarioAcesso())
        this.IdMedico = medico[0].idMedico
        this.CarregaAgenda('dia')
        this.spinner.hide();


      }, err => {
        console.error(err)
        this.spinner.hide();
      })
    } else
      this.loginComMedico = false
    this.spinner.hide();

  }

  copyMessage(val: string) {
    const selBox = document.createElement('textarea');
    selBox.style.position = 'fixed';
    selBox.style.left = '0';
    selBox.style.top = '0';
    selBox.style.opacity = '0';
    selBox.value = val;
    document.body.appendChild(selBox);
    selBox.focus();
    selBox.select();
    document.execCommand('copy');
    document.body.removeChild(selBox);
    this.snackBarAlert.sucessoSnackbar("Link copiado!")
  }

  FiltroEspecialidade() {
    this.IdMedico = null;
    this.CarregaMedicos()
  }


  mensagemPaciente!: string;
  campoExitente!: string;
  campoEmailVazil = false;


  public validarCpf(value: any) {
    this.campoCPFInvalido = false
    if (value != "") {
      this.campoCPFVazil = false;


      if (!this.validacao.cpf(value)) {
        this.campoCPFInvalido = true;
        return;
      }

      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, null, 'CPF').subscribe((retorno) => {


        this.mensagemPaciente = '';
        this.cliente = null;
        if (retorno != null) {
          this.campoExitente = "CPF"
          this.mensagemPaciente = 'CPF já registrado no sistema. Deseja utilizar o paciente desse cadastro?';
          this.cliente = retorno;
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      }, err => {
        console.error(err)
      })
    }
    else
      this.campoCPFVazil = true;
  }

  public ValidarTelMovel(value: any) {
    if (value != "") {
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, 0, 'TelMovel').subscribe((retorno) => {


        this.mensagemPaciente = '';
        this.cliente = null;
        if (retorno != null) {
          this.campoExitente = "Tel"
          this.mensagemPaciente = 'Telefone já registrado no sistema. Deseja utilizar o paciente desse cadastro?';
          this.cliente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      }, err => {
        console.error(err)
      })
    }
  }

  public ValidarEmail(value: any) {

    if (value != "") {
      this.campoEmailVazil = false;
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        return;
      }
      this.campoEmailInvalido = false
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, 0, 'EMAIL').subscribe((retorno) => {


        this.mensagemPaciente = '';
        this.cliente = null;
        if (retorno != null) {
          this.campoExitente = "EMAIL"
          this.mensagemPaciente = 'Email já registrado no sistema. Deseja utilizar o paciente desse cadastro?';
          this.cliente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      }, err => {
        console.error(err)
      })
    }
    else
      this.campoEmailVazil = true;
    this.campoEmailInvalido = false;
  }


  public AceitarUsuarioExistente() {

    if (this.cliente != null) {

      this.dadosNovoPaciente.cpf = this.cliente.pessoa.cpf;
      this.dadosNovoPaciente.nome = this.cliente.pessoa.nomePessoa;
      this.dadosNovoPaciente.dtaNascimento = this.cliente.pessoa.dtaNascimento != null ? new Date(this.cliente.pessoa.dtaNascimento).toLocaleDateString() : "";
      this.dadosNovoPaciente.email = this.cliente.pessoa.email;

      if (this.cliente.paciente != null) {
        this.dadosNovoPaciente.procedencia = this.cliente.paciente.procedencia;
      }
      if (this.cliente.contato.telefoneMovel != null && this.cliente.contato.telefoneMovel != '' && this.cliente.contato.telefoneMovel != undefined) {
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(/\D/g, "");
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.cliente.contato.telefoneMovel = this.cliente.contato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.dadosNovoPaciente.telefoneMovel = this.cliente.contato.telefoneMovel;

      if (this.cliente.clinicas && this.cliente.clinicas.length > 0) {

        this.clinicas = [];
        this.cliente.clinicas.forEach((element: any) => {
          this.DadosClinicas.forEach((elemento: any) => {
            if (elemento.idClinica == element.idClinica)
              this.clinicas.push(elemento)
          })
        })
        this.clinicas = new FormControl(this.clinicas);
      }

    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();
  }

  public NaoAceitarUsuarioExistente() {
    if (this.campoExitente == 'CPF') {
      this.dadosNovoPaciente.cpf = ""
      this.campoExitente = ""
    }
    else if (this.campoExitente == 'EMAIL') {
      this.dadosNovoPaciente.email = ""
      this.campoExitente = ""
    }
    else {
      this.dadosNovoPaciente.telefoneMovel = ''
      this.campoExitente = ""
    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();
  }

  EnviarWhatsAcesso(idagenda: any) {

    const objConsulta = this.objAgenda.filter((c: any) => c.idConsulta == idagenda);
    const objacesso = new AcessoRapidoEmail;
    objacesso.LinkAcesso = this.modalInfo.guid;
    objacesso.CodAcesso = this.modalInfo.CodAcesso;


    if (objConsulta[0].telPaciente != null) {
      var celular = objConsulta[0].telPaciente != null ? objConsulta[0].telPaciente.replace(/[^0-9]/g, '') : "";

      var texto = "Link de Acesso: " + objacesso.LinkAcesso + "  \n Cod.Acesso: " + objacesso.CodAcesso + "  \n ";
      // texto = window.encodeURIComponent(texto.toString());

      window.open("https://api.whatsapp.com/send?phone=55" + celular + "&text=" + texto, "_blank");

    }
    else {

      this.snackBarAlert.falhaSnackbar('Erro ao enviar Whatsapp, Verifique o numero do paciente!')
    }
  }


  aplicarMascaraValor(v: any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor: any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

  dtExibicao!: string;

  escolherHorarioConsulta() {
    let dialog = this.matDialog.open(ModalEscolherHorarioComponent, {
      data: this.Dados.dtahora
    });

    dialog.afterClosed().subscribe((ret) => {
      if (ret) {

        this.Dados.dtahora = new Date(ret);
        this.dataMarcada = new Date(ret);
        this.FormataData(this.Dados.dtahora);
      } else {

      }
    });
  }


  FormataData(data: Date) {
    this.dtExibicao = formatDate(data, 'dd/MM/yyyy - HH:mm', this.locale);
  }

  async GetListaFatura() {
    this.spinner.show();
    await this.faturaService.GetListaFatura(false).subscribe((ret) => {
      this.listaFatura = ret;
    }, () => {
      this.snackBarAlert.sucessoSnackbar('Ocurreu um erro tentando carregar as faturas.')
    })
    this.spinner.hide();
  }
}

// import { colors } from '../demo-utils/colors';
type CalendarPeriod = 'day' | 'week' | 'month';

const RED_CELL: 'red-cell' = 'red-cell';
const BLUE_CELL: 'blue-cell' = 'blue-cell';

// const Prim_Cons: 'primeiraConsulta' = 'primeiraConsulta';
// const Cancel_Cons: 'Cancelada' = 'Cancelada';

function addPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
  return {
    day: addDays,
    week: addWeeks,
    month: addMonths
  }[period](date, amount);
}

function subPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
  return {
    day: subDays,
    week: subWeeks,
    month: subMonths
  }[period](date, amount);
}

// function startOfPeriod(period: CalendarPeriod, date: Date): Date {
//   return {
//     day: startOfDay,
//     week: startOfWeek,
//     month: startOfMonth
//   }[period](date);
// }

// function endOfPeriod(period: CalendarPeriod, date: Date): Date {
//   return {
//     day: endOfDay,
//     week: endOfWeek,
//     month: endOfMonth
//   }[period](date);


// }


