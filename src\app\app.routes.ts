import { CadastrarSalaComponent } from './listar-salas/cadastrar-sala/cadastrar-sala.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginComponent } from './login/login.component';
// import { ModuleWithProviders } from '@angular/core/src/metadata/ng_module';
import { PerfilComponent } from './perfil/perfil.component';
import { RecuperarSenhaComponent } from './login/recuperar-senha/recuperar-senha.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { MedicosComponent } from './medicos/medicos.component';
import { PesquisaMedicosComponent } from './medicos/pesquisa-medicos/pesquisa-medicos.component';
import { PacientesComponent } from './pacientes/pacientes.component';
import { PesquisaPacientesComponent } from './pacientes/pesquisa-pacientes/pesquisa-pacientes.component';
import { StreamingComponent } from './streaming/streaming.component';
import { GeradorPerguntasComponent } from './gerador-perguntas/gerador-perguntas.component';
import { ConvenioComponent } from './convenio/convenio.component';
import { ConsultasComponent } from './consultas/consultas.component';
import { CadastroUsuarioComponent } from './cadastro-usuario/cadastro-usuario.component';
import { PesquisaUsuarioComponent } from './cadastro-usuario/pesquisa-usuario/pesquisa-usuario.component';
import { GeradorTipoAgendamentoComponent } from './gerador-tipo-agendamento/gerador-tipo-agendamento.component';
import { CadastroClinicaComponent } from './cadastro-clinica/cadastro-clinica.component';
import { PesquisaClinicaComponent } from './cadastro-clinica/pesquisa-clinica/pesquisa-clinica.component';
import { TesteConexaoComponent } from './teste-conexao/teste-conexao.component';
import { TermoPrivacidadeComponent } from './termo-privacidade/termo-privacidade.component';
import { AgendacontatoComponent } from './agendacontato/agendacontato.component';
import { DocumentacaoComponent } from './documentacao/documentacao.component';
import { Streaming1Component } from './streaming1/streaming1.component';
import { StreamingPacienteComponent } from './streaming-paciente/streaming-paciente.component';
import { StreamingGuestComponent } from './streaming-guest/streaming-guest.component';

// import { MedicamentosComponent } from './medicamentos/medicamentos.component';
import { RelatoriosComponent } from './relatorios/relatorios.component';
import { PesquisaConvenioComponent } from './convenio/pesquisa-convenio/pesquisa-convenio.component';
import { HistoricoRecadosComponent } from './medicos/historico-recados/historico-recados.component';
import { HistoricoUsuariosComponent } from './relatorios/historico-usuarios/historico-usuarios.component';
import { PermissoesComponent } from './permissoes/permissoes.component';
import { PesquisaPermissoesComponent } from './permissoes/pesquisa-permissoes/pesquisa-permissoes.component';
import { HistoricoRecadosDiaComponent } from './relatorios/historico-recados-dia/historico-recados-dia.component';
import { LoginGuard } from './auth/guards/login.guard';
import { MenuGuard } from './auth/guards/menu.guard';
import { MenuComponent } from './menu/menu.component';
import { ContasPagarComponent } from './contas/contas-pagar/contas-pagar.component';
import { ContasReceberComponent } from './contas/contas-receber/contas-receber.component';
import { AcessoRapidoConsultaComponent } from './acesso-rapido-consulta/acesso-rapido-consulta.component';
import { CarteiraComponent } from './contas/carteira/carteira.component';
import { PerfilClinicaComponent } from './perfil-clinica/perfil-clinica.component';
import { AcessoRapidoAgendamentoComponent } from './acesso-rapido-consulta/acesso-rapido-agendamento/acesso-rapido-agendamento.component';
import { FilaEsperaComponent } from './acesso-rapido-consulta/fila-espera/fila-espera.component';
import { OrientacoesMedicasComponent } from './consultas/orientacoes-medicas/orientacoes-medicas.component';
import { PrivacidadeGuard } from './auth/guards/privacidade.guard';
import { SalaReuniaoComponent } from './streaming/Reuniao/sala-reuniao/sala-reuniao.component';
import { AcessosalaReuniaoComponent } from './streaming/Reuniao/acessosala-reuniao/acessosala-reuniao.component';
import { AgendarReuniaoComponent } from './streaming/Reuniao/agendar-reuniao/agendar-reuniao.component';
import { IndicadoresComponent } from './dashboard/indicadores/indicadores.component';
import { CadastroExamePacienteComponent } from './documentacao/cadastro-exame-paciente/cadastro-exame-paciente.component';
import { AcessoRapidoCovidComponent } from './acesso-rapido-consulta/covid/acesso-rapido-covid/acesso-rapido-covid.component';
// import { CadastroLocaisComponent } from './cadastro-locais/cadastro-locais.component';
import { CadastroItensComponent } from './cadastro-itens/cadastro-itens.component';
import { CadastroReceitasComponent } from './cadastro-receitas/cadastro-receitas.component';
import { AdicionarItensComponent } from './cadastro-itens/adicionar-itens/adicionar-itens.component';
// import { AdicionarLocaisComponent } from './cadastro-locais/adicionar-locais/adicionar-locais.component';
import { AdicionarReceitasComponent } from './cadastro-receitas/adicionar-receitas/adicionar-receitas.component';
import { AnaliseComponent } from './analise/analise.component';
import { AdicionarAnaliseComponent } from './analise/adicionar-analise/adicionar-analise.component';
import { AnalisePacienteComponent } from './analise/analise-paciente/analise-paciente.component';
import { FormularioComponent } from './formulario/formulario.component';
import { AdicionarFormularioComponent } from './formulario/adicionar-formulario/adicionar-formulario.component';
import { DashboardInternoComponent } from './dashboard-interno/dashboard-interno.component'
import { PainelAtendimentoComponent } from './painel-atendimento/painel-atendimento.component';
import { ListarSalasComponent } from './listar-salas/listar-salas.component';
import { FaturaComponent } from './fatura/fatura.component';
import { ListaFaturasComponent } from './lista-faturas/lista-faturas.component';
import { ModalListaGuiaTissComponent } from './Modais/modal-lista-guia-tiss/modal-lista-guia-tiss.component';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { AgendaConsultaComponent } from './agenda-consulta/agenda-consulta.component';
import { AgendaComponent } from './agenda/agenda.component';
import { PreConsultaQuestionarioComponent } from './acesso-rapido-consulta/pre-consulta-questionario/pre-consulta-questionario.component';
import { FilaEsperaMedicoComponent } from './medicos/fila-espera-medico.component';

import { LogoutGuard } from './auth/guards/logout.guard';


// export const AppRoutes: Routes = [
//     {
//       path: 'login',
//       component: LoginComponent,
//       canActivate: [LoginGuard],
//     },
//     {
//       path: '',
//       component: MenuComponent,
//       canActivate: [PrivacidadeGuard, MenuGuard],
//       children:
//         [
//           {
//             path: '',
//             pathMatch: 'full',
//             component: DashboardComponent
//           },
//         ]
//     }

// ]

export const AppRoutes: Routes = [
  {
    path: 'Painel',
    component: PainelAtendimentoComponent,
    canActivate: [LogoutGuard]
  },

  {
    path: 'filaespera',
    component: FilaEsperaComponent,
    // Sem guards - acessível para usuários logados e não logados
  },

  {
    path: 'pre-consulta-questionario',
    component: PreConsultaQuestionarioComponent,
  },

  {
    path: 'streaming-paciente',
    component: StreamingPacienteComponent,
    // Sem guards - acessível para usuários logados e não logados
  },

  {
    path: 'streaming-guest',
    component: StreamingGuestComponent,
    canActivate: [LogoutGuard] // Apenas médicos logados
  },

  {
    path: 'login',
    component: LoginComponent,
    canActivate: [LoginGuard],
  },
  {
    path: 'recuperarSenha',
    component: RecuperarSenhaComponent,
    canActivate: [LoginGuard],
  },

  {
    path: 'acessoagenda',
    component: AcessoRapidoAgendamentoComponent,
    canActivate: [LogoutGuard]
  },


  {
    path: 'privacidade',
    component: TermoPrivacidadeComponent,
    canActivate: [LogoutGuard]

  },
  {
    path: 'reuniao',
    component: SalaReuniaoComponent,
    canActivate: [LogoutGuard]
  },

  {
    path: 'atendimento/:idGuid',
    component: AcessoRapidoConsultaComponent,
    canActivate: [LogoutGuard]
  },
  {
    path: 'acessoreuniao/:idGuid',
    component: AcessosalaReuniaoComponent,
    canActivate: [LogoutGuard]
  },
  {
    path: 'covid',
    component: AcessoRapidoCovidComponent,
    canActivate: [LogoutGuard]
  }, {
    path: 'arauz',
    component: AcessoRapidoCovidComponent,
    canActivate: [LogoutGuard]
  },
  {
    path: 'apresentacao',
    component: AcessoRapidoCovidComponent,
    canActivate: [LogoutGuard]
  },
  {
    path: 'prisma',
    component: AcessoRapidoCovidComponent,
    canActivate: [LogoutGuard]
  },
  {
    path: 'gobox',
    component: AcessoRapidoCovidComponent,
    canActivate: [LogoutGuard]
  },
  {
    path: 'gobox/:cpf',
    component: AcessoRapidoCovidComponent,
    canActivate: [LogoutGuard]
  },

  {
    path: '',
    component: MenuComponent,
    canActivate: [PrivacidadeGuard, MenuGuard, LogoutGuard],
    children:
      [
        {
          path: '',
          pathMatch: 'full',
          component: DashboardComponent
        },
        { path: '', component: DashboardComponent },
        { path: 'indicadores', component: IndicadoresComponent },
        { path: 'perfil', component: PerfilComponent },
        { path: 'medico', component: MedicosComponent },
        { path: 'pesquisamedicos', component: PesquisaMedicosComponent },
        { path: 'paciente', component: PacientesComponent },
        { path: 'pesquisapacientes', component: PesquisaPacientesComponent },
        { path: 'calendario2', component: AgendaComponent },
        { path: 'calendario', component: AgendaConsultaComponent },
        { path: 'streaming', component: StreamingComponent },
        { path: 'perguntas', component: GeradorPerguntasComponent },
        { path: 'convenio', component: ConvenioComponent },
        { path: 'consulta', component: ConsultasComponent },
        { path: 'orientacoes', component: OrientacoesMedicasComponent },
        { path: 'CadastroAtendente', component: CadastroUsuarioComponent },
        { path: 'cadastroitens', component: CadastroItensComponent },
        { path: 'cadastroreceitas', component: CadastroReceitasComponent },
        { path: 'Atendente', component: PesquisaUsuarioComponent },
        { path: 'tipoAgendamento', component: GeradorTipoAgendamentoComponent },
        { path: 'cadastroclinica', component: CadastroClinicaComponent },
        { path: 'clinicas', component: PesquisaClinicaComponent },
        { path: 'testeConexao', component: TesteConexaoComponent },
        { path: 'contatos', component: AgendacontatoComponent },
        { path: 'doc', component: DocumentacaoComponent },
        { path: 'relatorios', component: RelatoriosComponent },
        { path: 'pesquisaconvenio', component: PesquisaConvenioComponent },
        { path: 'historicorecados', component: HistoricoRecadosComponent },
        { path: 'historicoUsuarios', component: HistoricoUsuariosComponent },
        { path: 'permissoes', component: PermissoesComponent },
        { path: 'pesquisapermissoes', component: PesquisaPermissoesComponent },
        { path: 'historicoRecados', component: HistoricoRecadosDiaComponent },
        { path: 'contasPagar', component: ContasPagarComponent },
        { path: 'contasReceber', component: ContasReceberComponent },
        { path: 'carteira', component: CarteiraComponent },
        { path: 'examesClinica', component: CadastroExamePacienteComponent },
        { path: 'agendareuniao', component: AgendarReuniaoComponent },
        { path: 'adicionaritens', component: AdicionarItensComponent },
        { path: 'adicionarreceitas', component: AdicionarReceitasComponent },
        { path: 'analise', component: AnaliseComponent },
        { path: 'adicionaranalise', component: AdicionarAnaliseComponent },
        { path: 'analisepaciente', component: AnalisePacienteComponent },
        { path: 'formularios', component: FormularioComponent },
        { path: 'adicionarformularios', component: AdicionarFormularioComponent },
        { path: 'dashboard-interno', component: DashboardInternoComponent },
        { path: 'listagemsalas', component: ListarSalasComponent },
        { path: 'cadastrosala', component: CadastrarSalaComponent },
        { path: 'fila-espera-medico', component: FilaEsperaMedicoComponent },

        {
          path: 'perfilClinica',
          component: PerfilClinicaComponent
        },
        {
          path: 'prontuario',
          component: Streaming1Component
        },
        {
          path: 'streaming',
          component: StreamingComponent
        },
        {
          path: 'fatura',
          component: FaturaComponent
        },
        {
          path: 'lista-faturas',
          component: ListaFaturasComponent
        },
        { path: 'lista-guia-tiss', component: ModalListaGuiaTissComponent },
        {
          path: '**',
          component: DashboardComponent,
          canActivate: [MenuGuard],
        },
      ]
  },

  {
    path: '**',
    component: LoginComponent,
    canActivate: [LoginGuard],
  },

];

@NgModule({
  imports: [RouterModule.forRoot(AppRoutes), TranslateModule, MatFormFieldModule, MatInputModule],
  exports: [RouterModule]
})
export class AppRoutingModule { }



