import { Component } from '@angular/core';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { TelemedicinaComponentBase } from 'src/app/Util/component.base';
import { trigger, state, style, animate, transition } from '@angular/animations';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { SafeResourceUrl, DomSanitizer } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDivider } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';

declare var JitsiMeetExternalAPI: any;
@Component({
    selector: 'app-sala-reuniao',
    templateUrl: './sala-reuniao.component.html',
    styleUrls: ['./sala-reuniao.component.scss'],
    animations: [
        trigger('openClose', [
            state('open', style({
                height: '300px',
            })),
            state('closed', style({
                height: '36px',
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ])
        ]),
        // Fim transition
    ],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      NgxSmartModalModule,
      MatProgressBarModule,
      MatDivider,
      TranslateModule,
    ]
})
export class SalaReuniaoComponent extends TelemedicinaComponentBase {

  constructor(    
    public usuarioLogadoService: UsuarioLogadoService, 
    private router: Router,
    public ngxSmartModalService: NgxSmartModalService,
    public sanitizer: DomSanitizer,
  ) {

    super();
    this.screenWidthstream = window.innerWidth;
    if (this.screenWidthstream < 600) {
     this.showFillerStream = true;
    }
    else {
      this.showFillerStream = false;
    }
    // this.streamService.telastream$.emit(true);


  }


  color = 'primary';
  mode = 'indeterminate';
  value = 0;
  bufferValue = 0;
  showAba: boolean = false;
  historico: boolean = false;
  showFillerStream: boolean = false;
  showPanel: boolean = false;
  screenWidthstream: number;
  isOpen = false;

  // domain: string = "jitsi.medicinaparavoce.com.br";
  domain: string = "meet.jit.si";
  options: any;
  api: any;

  ngOnInit() {

  }


  recusarSair() {
    this.ngxSmartModalService.getModal('Inicializando').close();
    sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(['/']);

  }
  FlgVideo: boolean = false;
  LinkVideo?: string
  sala?: string;
  urlSafe?: SafeResourceUrl;
  // AbrirwebCan(): void {
  //   try {
  //     this.sala = 'medicinaparavoce';
  //     this.FlgVideo = true;
  //     if (window.location.hostname == "camposverdes.medicinaparavoce.com.br") { this.LinkVideo = `${environment.endPointTokboxCamposVerdes}&room=` + this.sala + '&iframe=true'; }
  //     else { this.LinkVideo = `${environment.endPointTokbox}&room=` + this.sala + '&iframe=true'; }
  //     this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.LinkVideo);

  //     // || this.usuarioLogadoService.getIdPessoa == this.dadosConsulta.

  //     this.ngxSmartModalService.getModal('Inicializando').close();
  //   } catch (error) {
  //   }
  // }


  AbrirwebCan(): void {
    try {
      if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
        this.options = {
          roomName: 'alcidesbranco',
          interfaceConfigOverwrite: {
            SHOW_JITSI_WATERMARK: false, SHOW_WATERMARK_FOR_GUESTS: false, INVITATION_POWERED_BY: false,
            AUTHENTICATION_ENABLE: false, DISPLAY_WELCOME_PAGE_CONTENT: false, GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false, VIDEO_QUALITY_LABEL_DISABLED: true,
            MOBILE_APP_PROMO: false, TOOLBAR_ALWAYS_VISIBLE: false,
            settings: false, videoquality: false, fullscreen: false, info: false,
            TOOLBAR_BUTTONS: ['microphone', 'camera', 'fullscreen',
              'tileview', 'hangup', 'profile', 'chat',
              'recording', 'livestreaming',
              'videoquality', 'settings',
              'shortcuts',
              'help'],
          },
          parentNode: document.querySelector('#react')
        }
        this.api = new JitsiMeetExternalAPI(this.domain, this.options);
        this.api.executeCommand('muteEveryone');
      }
      else if (this.usuarioLogadoService.getIdTipoUsuario() != EnumTipoUsuario.Médico) {
        this.options = {
          roomName: 'alcidesbranco',
          interfaceConfigOverwrite: {
            SHOW_JITSI_WATERMARK: false, SHOW_WATERMARK_FOR_GUESTS: false, INVITATION_POWERED_BY: false,
            AUTHENTICATION_ENABLE: false, DISPLAY_WELCOME_PAGE_CONTENT: false, GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false, VIDEO_QUALITY_LABEL_DISABLED: false,
            MOBILE_APP_PROMO: false,
            TOOLBAR_BUTTONS: ['fullscreen', 'tileview','microphone', 'camera',]

          },

          parentNode: document.querySelector('#react')

        }
        this.api = new JitsiMeetExternalAPI(this.domain, this.options);
      }



      this.ngxSmartModalService.getModal('Inicializando').close();

      
      

      
      ;
      ;
    } catch (error) {
      
      ;
      // this.AlgumErro(true, error)

    }
  }


}
