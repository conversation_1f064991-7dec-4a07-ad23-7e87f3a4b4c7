// date-utils.ts

export enum DateValidationResult {
    OK,
    DtInicio_Nao_Preenchida,
    DtFim_Nao_Preenchida,
    DtInicio_Maior_DtFim,
    Diferenca_Entre_Datas_Supera_Limite_Fornecido
}

export function validateDates(startDate: Date | null, endDate: Date | null, QtdMesesDiferenca:any): DateValidationResult {
    if (!startDate) {
        return DateValidationResult.DtInicio_Nao_Preenchida;
    }

    if (!endDate) {
        return DateValidationResult.DtFim_Nao_Preenchida;
    }

    if (startDate > endDate) {
        return DateValidationResult.DtInicio_Maior_DtFim;
    }

    const diffInMonths = monthDifference(startDate, endDate);
    if (diffInMonths > QtdMesesDiferenca) {
        return DateValidationResult.Diferenca_Entre_Datas_Supera_Limite_Fornecido;
    }

    return DateValidationResult.OK;
}

export function monthDifference(date1: Date, date2: Date): number {
    const yearsDiff = date2.getFullYear() - date1.getFullYear();
    const monthsDiff = date2.getMonth() - date1.getMonth();
    return yearsDiff * 12 + monthsDiff;
}

export function parseDateString(dateString: string | null): Date | null {
    if (!dateString) {
        return null;
    }

    const [year, month, day] = dateString.split('-');
    return new Date(`${month}-${day}-${year}`);
}
