<div class="container">
  <mat-card class="card-principal mother-div">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <mat-icon class="header-icon-material">group</mat-icon>
      </div>
      <div class="header-content">
        <h2 class="header-title">{{'TELAPESQUISAMEDICO.TITULO' | translate }}</h2>
        <p class="header-subtitle"><PERSON><PERSON><PERSON><PERSON> e visualize todos os médicos cadastrados</p>
      </div>
    </div>

    <!-- FILTROS E AÇÕES -->
    <div class="filtros-section">
      <div class="filtros-row">
        <div class="busca-container">
          <mat-form-field appearance="outline" class="busca-field">
            <mat-label>{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}</mat-label>
            <input matInput name="pesquisa" (keyup.enter)="CarregaTable()" [(ngModel)]="pesquisa"
                   placeholder="Digite o nome, CRM ou especialidade...">
            <button mat-icon-button matSuffix (click)="CarregaTable()" class="btn-busca">
              <mat-icon>search</mat-icon>
            </button>
          </mat-form-field>
        </div>

        <div class="acoes-container">
          <button mat-raised-button class="btn-adicionar" [routerLink]="['/medico']">
            <mat-icon>add</mat-icon>
            <span>{{ 'TELAPESQUISAMEDICO.ADICIONAR' | translate }}</span>
          </button>
          <button mat-stroked-button class="btn-filtros" (click)="toggleFiltros()">
            <mat-icon>tune</mat-icon>
            <span>Filtros</span>
          </button>
        </div>
      </div>

      <!-- FILTROS AVANÇADOS -->
      <div class="filtros-avancados" [class.expanded]="mostrarFiltros">
        <div class="toggles-container">
          <mat-slide-toggle [(ngModel)]='Foto' class="toggle-item">
            <mat-icon>photo_camera</mat-icon>
            {{ 'TELAPESQUISAMEDICO.MOSTRARFOTO' | translate }}
          </mat-slide-toggle>
          <mat-slide-toggle [(ngModel)]='inativos' (change)="CarregaTable()" class="toggle-item">
            <mat-icon>visibility_off</mat-icon>
            Mostrar {{ 'TELAPESQUISAMEDICO.INATIVOS' | translate }}
          </mat-slide-toggle>
        </div>
      </div>
    </div>

    <!-- ESTATÍSTICAS -->
    <div class="estatisticas-container">
      <div class="estatistica-item" *ngIf="DadosTab && DadosTab.length > 0">
        <mat-icon>group</mat-icon>
        <div class="estatistica-info">
          <span class="estatistica-numero">{{DadosTab.length}}</span>
          <span class="estatistica-label">Médicos encontrados</span>
        </div>
      </div>
      <div class="estatistica-item" *ngIf="!inativos && DadosTab && DadosTab.length > 0">
        <mat-icon>check_circle</mat-icon>
        <div class="estatistica-info">
          <span class="estatistica-numero">{{medicosAtivos}}</span>
          <span class="estatistica-label">Médicos ativos</span>
        </div>
      </div>
      <div class="estatistica-item">
        <mat-icon>queue</mat-icon>
        <div class="estatistica-info">
          <span class="estatistica-numero">{{pacientesFila.length}}</span>
          <span class="estatistica-label">Pacientes na fila</span>
        </div>
      </div>
      <div class="estatistica-item" *ngIf="carregandoPacientes">
        <mat-icon>sync</mat-icon>
        <div class="estatistica-info">
          <span class="estatistica-numero">...</span>
          <span class="estatistica-label">Carregando...</span>
        </div>
      </div>
    </div>

    <!-- SEÇÃO DE PACIENTES NA FILA -->
    <div class="pacientes-fila-container" *ngIf="pacientesFila.length > 0">
      <div class="fila-header">
        <h3 class="fila-titulo">
          <mat-icon>queue</mat-icon>
          Pacientes na Fila de Espera
        </h3>
        <button mat-stroked-button class="btn-atualizar" (click)="carregarPacientesFila()">
          <mat-icon>refresh</mat-icon>
          Atualizar
        </button>
      </div>

      <div class="fila-scroll">
        <div class="paciente-fila-card" *ngFor="let paciente of pacientesFila; let i = index">
          <div class="paciente-posicao">
            <span class="posicao-numero">{{paciente.PosicaoFila || i + 1}}</span>
          </div>
          <div class="paciente-info-fila">
            <div class="paciente-nome">{{paciente.NomePaciente}}</div>
            <div class="paciente-detalhes-fila">
              <span class="paciente-idade">{{paciente.IdadePaciente}} anos</span>
              <span class="paciente-tempo">Aguardando: {{paciente.TempoEspera || 'N/A'}}</span>
            </div>
          </div>
          <div class="paciente-acoes-fila">
            <button mat-mini-fab class="btn-chamar" matTooltip="Chamar paciente">
              <mat-icon>call</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- LISTA DE MÉDICOS - DESKTOP VIEW -->
    <div class="lista-container desktop-view">
      <div class="lista-header">
        <h3 class="lista-titulo">
          <mat-icon>list</mat-icon>
          Lista de Médicos
        </h3>
        <div class="lista-info">
          <span class="total-registros">{{DadosTab?.length || 0}} registros</span>
        </div>
      </div>

      <div class="lista-scroll">
        <div class="medico-card" *ngFor="let item of DadosTab; let i = index"
             [class.card-inativo]="item.flgInativo">
          <!-- STATUS INDICATOR -->
          <div class="status-indicator" [class.ativo]="!item.flgInativo" [class.inativo]="item.flgInativo"></div>

          <!-- INFO DO MÉDICO COM FOTO -->
          <div class="medico-info" *ngIf="Foto">
            <div class="medico-avatar">
              <img src="{{item.imagenUsuario == null ? 'assets/build/img/userdefault.png' : item.imagenUsuario}}"
                   class="img-circle" alt="Foto do médico">
              <div class="avatar-badge" *ngIf="!item.flgInativo">
                <mat-icon>verified</mat-icon>
              </div>
            </div>
            <div class="medico-detalhes">
              <div class="info-item nome-principal">
                <mat-icon>person</mat-icon>
                <span class="nome" [title]="item.nome">{{item.nome | truncate : 20 : "…"}}</span>
                <span class="status-badge" [class.ativo]="!item.flgInativo" [class.inativo]="item.flgInativo">
                  {{item.flgInativo ? 'Inativo' : 'Ativo'}}
                </span>
              </div>
              <div class="info-item">
                <mat-icon>cake</mat-icon>
                <span>{{item.dtaNascimento | date: 'dd/MM/yyyy'}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span [title]="item.email">{{item.email | truncate : 20 : "…"}}</span>
              </div>
            </div>
          </div>

          <!-- INFO DO MÉDICO SEM FOTO -->
          <div class="medico-info" *ngIf="!Foto">
            <div class="medico-icon-avatar">
              <mat-icon class="avatar-icon">person</mat-icon>
            </div>
            <div class="medico-detalhes sem-foto">
              <div class="info-item nome-principal">
                <mat-icon>person</mat-icon>
                <span class="nome" [title]="item.nome">{{item.nome | truncate : 25 : "…" }}</span>
                <span class="status-badge" [class.ativo]="!item.flgInativo" [class.inativo]="item.flgInativo">
                  {{item.flgInativo ? 'Inativo' : 'Ativo'}}
                </span>
              </div>
              <div class="info-item">
                <mat-icon>cake</mat-icon>
                <span>{{item.dtaNascimento | date: 'dd/MM/yyyy'}}</span>
              </div>
              <div class="info-item">
                <mat-icon>mail</mat-icon>
                <span [title]="item.email">{{item.email | truncate : 20 : "…"}}</span>
              </div>
            </div>
          </div>

          <!-- DADOS PROFISSIONAIS -->
          <div class="medico-dados" [ngClass]="{'com-foto': Foto, 'sem-foto': !Foto}">
            <div class="dados-grid">
              <div class="dados-item">
                <mat-icon class="dados-icon">badge</mat-icon>
                <div class="dados-content">
                  <label class="dados-label">{{ 'TELAPESQUISAMEDICO.CRM' | translate }}</label>
                  <span class="dados-valor" [title]="item.crm">{{item.crm | truncate : 8 : "…"}}</span>
                </div>
              </div>
              <div class="dados-item">
                <mat-icon class="dados-icon">calendar_today</mat-icon>
                <div class="dados-content">
                  <label class="dados-label">{{ 'TELAPESQUISAMEDICO.DATADECADASTRO' | translate }}</label>
                  <span class="dados-valor">{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
                </div>
              </div>
              <div class="dados-item" *ngIf="item.Spec != null">
                <mat-icon class="dados-icon">local_hospital</mat-icon>
                <div class="dados-content">
                  <label class="dados-label">Especialidade</label>
                  <span class="dados-valor" [title]="item.Spec">{{item.Spec | truncate : 15 : "…"}}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- AÇÕES -->
          <div class="medico-acoes">
            <div class="acoes-principais">
              <button mat-mini-fab matTooltip="Ver Perfil" class="btn-acao perfil"
                      (click)="PerfilMedico(item.idUsuarioacesso)">
                <mat-icon>account_circle</mat-icon>
              </button>
              <button mat-mini-fab matTooltip="{{ 'TELAPESQUISAMEDICO.CRIARAGENDAMENTO' | translate }}"
                      class="btn-acao agenda" (click)="ValorMedico(item.idMedico,item.nome)">
                <mat-icon>event</mat-icon>
              </button>
              <button mat-mini-fab matTooltip="{{ 'TELAPESQUISAMEDICO.EDITAR' | translate }}"
                      class="btn-acao editar" (click)="editUsuario(item.idMedico)">
                <mat-icon>edit</mat-icon>
              </button>
            </div>

            <div class="acoes-secundarias">
              <button mat-icon-button [matTooltip]="item.flgEmail ? 'Email já enviado' : 'Enviar email de boas-vindas'"
                      class="btn-email" [class.enviado]="item.flgEmail"
                      (click)="ModalEmail(item.idUsuarioacesso,item.idMedico)">
                <mat-icon *ngIf="!item.flgEmail">mail_outline</mat-icon>
                <mat-icon *ngIf="item.flgEmail">mark_email_read</mat-icon>
              </button>

              <button mat-icon-button *ngIf="!inativos" matTooltip="Inativar médico"
                      class="btn-inativar" (click)="ValorUsuario(item.idMedico)">
                <mat-icon>block</mat-icon>
              </button>
              <button mat-icon-button *ngIf="inativos" matTooltip="{{ 'TELAPESQUISAMEDICO.ATIVAR' | translate }}"
                      class="btn-ativar" (click)="ValorUsuarioAtivar(item.idMedico)">
                <mat-icon>check_circle</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="DadosTab?.length === 0">
          <div class="vazia-icon">
            <mat-icon>search_off</mat-icon>
          </div>
          <h3>Nenhum médico encontrado</h3>
          <p>Tente ajustar os filtros de pesquisa ou adicione um novo médico</p>
          <button mat-raised-button class="btn-adicionar-vazio" [routerLink]="['/medico']">
            <mat-icon>add</mat-icon>
            Adicionar Médico
          </button>
        </div>

        <!-- BOTÃO CARREGAR MAIS -->
        <div class="carregar-mais" *ngIf="(DadosTab != undefined && DadosTab.length > 0) && bOcultaCarregaMais == false">
          <button mat-stroked-button class="btn-carregar" (click)="CarregarMais()">
            <mat-icon>expand_more</mat-icon>
            {{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}
          </button>
        </div>
      </div>
    </div>
  </mat-card>
</div>

<!-- MODAL EXCLUIR -->
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Confirmação</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">Você tem certeza que deseja inativar este usuário?</p>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="excluirItem.close()">
        {{ 'TELAPESQUISAMEDICO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-excluir" (click)="InativarUsuario()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL EMAIL -->
<ngx-smart-modal #emailUsuario identifier="emailUsuario" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Email de Acesso</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAMEDICO.ENVIARESTEEMAIL' | translate }}</p>
      <p class="modal-subtexto">{{ 'TELAPESQUISAMEDICO.EMAILCOMASENHA' | translate }}</p>
      
      <div class="alerta-inativo" *ngIf="inativos">
        <mat-icon>warning</mat-icon>
        <p>{{'TELAPESQUISAMEDICO.CADASTROESTAINATIVO' | translate}}</p>
        <p class="alerta-subtexto">{{'TELAPESQUISAMEDICO.ATIVANOVAMENTE' | translate}}</p>
      </div>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="emailUsuario.close()">
        {{ 'TELAPESQUISAMEDICO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" *ngIf="!inativos" (click)="mandaEmail(); emailUsuario.close()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" *ngIf="inativos" (click)="mandaEmailAtivarUser(); emailUsuario.close()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL AGENDAMENTO -->
<ngx-smart-modal #CriaAgendamento identifier="CriaAgendamento" customClass="modal-container emailmodal ">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Agendamento</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAMEDICO.DESEJAIRPARAAAGENDADESTEMEDICO' | translate }}</p>
      <p class="medico-nome-modal">{{nomeMedico | truncate : 25 : "…"}}</p>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-confirmar" (click)="AgendarConsulta()">
        {{ 'TELAPESQUISAMEDICO.AGENDA' | translate }}
      </button>
      <button mat-flat-button class="btn-cancelar" (click)="ngxSmartModalService.getModal('CriaAgendamento').close()">
        {{ 'TELAPESQUISAMEDICO.SAIR' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>

<!-- MODAL ATIVAR -->
<ngx-smart-modal #ativarItem identifier="ativarItem" customClass="modal-container emailmodal">
  <div class="modal-container">
    <div class="modal-header">
      <h3>Ativar Cadastro</h3>
    </div>
    <div class="modal-body">
      <p class="modal-text">{{ 'TELAPESQUISAMEDICO.ATIVARUSUARIO' | translate }}</p>
      <p class="modal-subtexto">{{ 'TELAPESQUISAMEDICO.OUSUARIOTERAACESSOAOSISTEMA' | translate }}</p>
    </div>
    <div class="modal-footer">
      <button mat-flat-button class="btn-cancelar" (click)="ativarItem.close()">
        {{ 'TELAPESQUISAMEDICO.NAO' | translate }}
      </button>
      <button mat-flat-button class="btn-confirmar" (click)="AtivarUsuario()">
        {{ 'TELAPESQUISAMEDICO.SIM' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>