/* You can add global styles to this file, and also import other style files */

$primary: #1265b9;
$secondary: #178aff;
$primary-color: #2E8B57 ; // Índigo
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9 ; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; // Tom médio entre azul e roxo
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s ;

@import "~@angular/material/prebuilt-themes/deeppurple-amber.css";
// @import "~ngx-smart-modal/ngx-smart-modal.css";
@import "~@ng-select/ng-select/themes/default.theme.css";
@import url("https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css");
@import "../node_modules/angular-calendar/css/angular-calendar.css";
@import "~swiper/dist/css/swiper.min.css";
@import "assets/build/css/efect.scss";
@import "../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "../node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "../node_modules/@syncfusion/ej2-splitbuttons/styles/material.css";
// Tratamento Primario
body.dialog-open {
    overflow: hidden;
    position: fixed;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.div-input-resp .mat-form-field-infix .mat-form-field-label-wrapper{
    top: -22px !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.celular-resp .mat-form-field-infix .mat-form-field-label-wrapper {
    top: -22px !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.sus-input .mat-form-field-infix .mat-form-field-label-wrapper {
    top: -22px !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.div-nome-covid .mat-form-field-infix .mat-form-field-label-wrapper {
    top: -22px !important;
}
.mat-expansion-indicator::after {
    color: #5260ff !important;
  }

.ng-select-container {
    border-bottom: none !important;
}
.ng-dropdown-panel {
    border-bottom-right-radius: 5px;
    border: 1px solid #ddd;
    border-bottom-left-radius: 5px;
}

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#buscar-locais .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #fff;
    border-radius: 5px;
}  

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#buscar-locais2 .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #fff !important;
    border-radius: 5px !important;
} 

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#buscar-locais2 .mat-form-field-appearance-outline .mat-form-field-wrapper {
    margin: 0 !important;
    padding: 0 !important;
}

  
.btn-primary {
    background-color: #0ad07c !important;
    color: #fff !important;
    box-shadow: 0 3px 1px -2px #0003,0 2px 2px #00000024,0 1px 5px #0000001f;
    padding: 5px 16px;
    font-size: 14px;
    border-radius: 5px;
}
// .ng-select .ng-select-container:after {
//     border-bottom: none !important;
// }
// .ng-select {
//     padding-bottom: 0 !important;
//     align-self: center;
//     border: 1px solid #ddd;
//     border-radius: 5px;
// }

.mat-mdc-form-field {
    font-size: 12px !important;
}

.ng-dropdown-panel {
    opacity: 1 !important;
}
.emailmodal .nsm-content {
   z-index: 1048;    
   position: relative;    
   margin: 0 auto;    
   margin-top: 50px;    
   justify-content: center;    
   display: flex;    
   align-items: center;    
}
ngx-smart-modal{
    .emailmodal{
        min-height: calc(100% - 3.5rem);
    }
}

#modal-orientacao .emailmodal {
    min-height: auto !important;
    max-width: 75% !important;
}

@media (max-width: 1115px) {
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .div-input-resp .mat-form-field-infix .mat-form-field-label-wrapper{
        top: -15px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .celular-resp .mat-form-field-infix .mat-form-field-label-wrapper {
        top: -15px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .sus-input .mat-form-field-infix .mat-form-field-label-wrapper {
        top: -15px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .div-nome-covid .mat-form-field-infix .mat-form-field-label-wrapper {
        top: -15px !important;
    }
}

@media (max-width: 820px) {
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .div-input-resp .mat-form-field-infix .mat-form-field-label-wrapper{
        top: -19px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .celular-resp .mat-form-field-infix .mat-form-field-label-wrapper {
        top: -19px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .sus-input .mat-form-field-infix .mat-form-field-label-wrapper {
        top: -19px !important;
    }
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .div-nome-covid .mat-form-field-infix .mat-form-field-label-wrapper {
        top: -19px !important;
    }
}


    .swiper > .swiper.s-wrapper {
        width: 100%;
        height: 146%;
        overflow: scroll;
      }

.custom-search {
    color: #1265b9;
    float: right;
    cursor: pointer;
    position: absolute;
    margin-left: -6%;
  }


.mat-toolbar.mat-primary {
    background: #f5f5f5 !important;
    color: #0b2637;
    border-bottom: 1px solid #c5c3c3;
}

// .mat-badge-content {
//     color: #fff;
//     background: #0f86ff !important;
// }

.title-content {
    font-size: 19px;
}

.sucessoSnackbar {
    background-color: #29a22d;
    color: #ffffff;
}

.falhaSnackbar {
    background-color: #b40906;
    color: #ffffff;
}

.infoSnackbar{
    background-color:#000000;
    color: #ffffff;
}

.mat-mdc-tab-group.mat-background-primary .mat-mdc-tab-header,
.mat-tab-group.mat-background-primary .mat-mdc-tab-links,
.mat-mdc-tab-nav-bar.mat-background-primary .mat-tab-header,
.mat-tab-nav-bar.mat-background-primary .mat-tab-links {
    background-color: $primary;
}

.filtro-bg {
    // height: inherit;
    background: $primary !important;
    //background-image: url("./assets/build/img/bg-dna3.png") !important;
    color: white;
    height: auto;
    min-height: 100%;
    max-height: max-content;
}

.img-cardio {
    background-image: url(/assets/build/img/Eletrocardio.png) !important;
    background-attachment: fixed;
    background-position: 113px 210px;
    background-repeat: no-repeat;
    height: 200px !important;
    background-size: 245px;
}

.swiper-container {
    position: relative;
}

.swiper-slide {
    background-color: #fff;
    padding: 20px;
}

.content-slider {
    // width: 335px!important;
    width: 235px !important;
}
#calendar-dash .mat-calendar-controls {
    display: flex;
    margin: 0 !important;
}
#calendar-dash .mat-calendar-table {
    border: none !important;
}
#calendar-dash .mat-calendar-table-header-divider::after {
    display: none !important;
}
#calendar-dash .mat-calendar-body-label {
    visibility: hidden !important;
}
#calendar-dash .mat-calendar-table-header {
    background-color: transparent;
}
#calendar-dash .mat-calendar-table-header th {
    font-size: 12px;
    font-weight: 600;
    color: #00a6e8;
}#calendar-dash .mat-calendar-header {
    background-color: #052b3b;
    color: #fff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 0 !important;
}

#calendar-dash .mat-calendar-table-header th {
    font-size: 12px;
    font-weight: 600;
    color: #5260ff;
}
#calendar-dash .mat-calendar-header {
    background-color: #f3f4ff;
    color: #5260ff;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 0 !important;
}
.mat-calendar-body-cell-content {
    color: #939393;
}
.mat-drawer-backdrop {
    z-index: 1 !important;
}
.mat-drawer-backdrop.mat-drawer-shown {
    background-color: #f6f9fe !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version. */
.mat-progress-bar-buffer {
    background-color: #daddff !important;
}

/* TODO(mdc-migration): The following rule targets internal classes of progress-bar that may no longer apply for the MDC version. */
.mat-progress-bar-fill::after {
    background-color: #4cd9ff !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#perfil-forms .mat-form-field-appearance-outline .mat-form-field-outline {
    background: #fff;
    border-radius: 5px;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#perfil-forms .mat-form-field-appearance-outline .mat-form-field-suffix {
    top: 2px !important;
    align-self: center !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of menu that may no longer apply for the MDC version. */
.mat-menu-custom {
    background-color: #fff !important;
    text-align: left;
}

.modal-infor-pac {
    .nsm-body {
        background: #f5f5f5;
        border-radius: 10px;
        height: 55px;
        
        
    }
}

.ng-select {
    border: 1px solid #ddd !important;
    font-size: 12px;
    padding: 0 5px !important;
    background-color: #fff !important;
    border-radius: 4px !important;
    height: 42px !important;
}
.ng-select .ng-select-container:after {
    border-bottom: none !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#buscar-locais3 .mat-form-field-wrapper {
    padding-bottom: 0 !important;
}
/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
#buscar-locais3 .mat-form-field-appearance-outline .mat-form-field-outline {
    background-color: #fff;
    border-radius: 5px;
} 

@media (max-width: 767px) {
    .modal-infor-pac {
        overflow: unset !important;
      }
}

@media (min-width: 1540px) {
    .filtro-bg {
        // height: 100% !important;
        background: $primary !important;
        color: white;
        background-attachment: fixed;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
    }
}

.mat-accent .mat-pseudo-checkbox-checked,
.mat-accent .mat-pseudo-checkbox-indeterminate,
.mat-pseudo-checkbox-checked,
.mat-pseudo-checkbox-indeterminate {
    background: $accent-color !important;
}
.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after, 
.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{
    color: #fff !important;
}
.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{
    color: $accent-color !important;
}
.mat-mdc-select-arrow svg{
    fill: $accent-color !important;
}

@media (max-width: 780px) {
    body {
        -webkit-overflow-scrolling: touch;
    }
    .title-content {
        font-size: 1.7rem;
    }
}

@media (max-width: 450px) {
    .title-content {
        font-size: 1.2rem;
        margin-right: 10px;
        margin-left: 0px;
        align-self: flex-end;
    }
    .form-modal .nsm-dialog-btn-close {
        top: -30px;
    }
}

@media (max-width: 600px) {
    .cal-month-view {
        height: unset !important;
        overflow-y: scroll;
        overflow-x: hidden;
    }
    .cal-day-view .cal-hour-rows {
        height: unset !important;
        overflow-y: scroll;
    }
    .modal-consulta-play {
        .nsm-dialog-btn-close {
           margin-top: 50px;
        }
    }
}

@media (max-width: 900px){
    .nsm-body {
        background: #f5f5f5;
        border-radius: 10px;
        max-height: 55vh;
        
        
    }
}

.cal-day-view mwl-calendar-day-view-hour-segment,
.cal-day-view .cal-hour-segment {
    touch-action: auto !important;
}

@import "~@angular/material/prebuilt-themes/deeppurple-amber.css";

body {
    font-family: Roboto, sans-serif;
    margin: 0;
}

.basic-container {
    padding: 30px;
    display: inline-grid;
}

.version-info {
    font-size: 8pt;
    float: right;
}

.example-container {
    display: flex;
    flex-direction: column;
    max-height: 500px;
    min-width: 300px;
}

.mat-mdc-table {
    overflow: auto;
    max-height: 500px;
}
// .coluna-maior .mat-form-field-flex {
//     height: -webkit-fill-available !important;
// }

/* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
.coluna-maior .mat-form-field-wrapper {
    padding-bottom: unset !important;
}

.prontuario-Perfil{
    /* TODO(mdc-migration): The following rule targets internal classes of slide-toggle that may no longer apply for the MDC version. */
    .mat-slide-toggle-content {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: unset;
        white-space: normal;
    }
}

.agenda-component {

  .cal-day-view .cal-hour-segment.cal-after-hour-start .cal-time {
    display: flex !important;
  }

  .cal-month-view .cal-day-number {
    opacity: unset;
  }

  .cal-month-view .cal-cell-row:hover {
    background-color: unset;
  }

  .cal-month-view {
    background-color: #808080;
  }

  .cal-month-view .cal-day-badge {
    background-color: #b40906;
  }

  .cal-day-view .cal-event-container {
    width: 100% !important;
  }

  .cal-day-view .cal-hour:last-child :not(:last-child) .cal-hour-segment {
    cursor: pointer;
  }

}

@media (max-height: 450px){

    .footer-faso-novo {
        display: none !important;
    }
}

.div-mother-padd {
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .mat-form-field-appearance-outline .mat-form-field-infix {
        padding: 6px;
    }
}

@media (max-width: 1190px) {
    .complete {
        .mat-calendar-controls {
            display: block;
            text-align: center;
        }
        .mat-calendar-content {
            min-width: 175px;
            margin-left: -15px;
        }
    }

}
@media (max-width: 780px) {
    .complete {
        .mat-calendar-table-header-divider {
            display: none;
        }
    }
}

@media (max-width: 767px) {
    .modal-espera-agenda {
        /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
        .mat-form-field-flex {
            width: -webkit-fill-available;
        }
    }
}

@media (max-width: 680px) {
    .complete .mat-calendar-content {
        min-width: 175px;
        margin-left: 4px;
    }
}



.leftwatermark{
    display:none;
    left:50px;
    margin-left:10px;
    
}

.mother-total {
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version. */
    .mat-form-field-appearance-outline .mat-form-field-flex {
        position: unset;
    }
}

@media (max-width: 425px) {
    .modal-escolhamedico {
        .ng-dropdown-panel .scroll-host {
            height: 23vh;
        }
    }
 
}

.custom-modal .mat-mdc-dialog-container {
    border-radius: 10px;
}

* {
    text-decoration: none;
    list-style: none;
    outline: none;
    border: none;
    box-shadow: none;
}
::ng-deep{
    .mat-mdc-dialog-container .mdc-dialog__surface {
        height: auto !important;
    }
}
mat-form-field , .small {
    --mat-form-field-container-height: 40px !important;
    --mat-form-field-container-vertical-padding: 8px !important;
    --mdc-outlined-text-field-outline-color: rgb(230, 230, 230) !important;
    --mdc-outlined-text-field-focus-outline-color: #2E8B57 !important;
    --mdc-outlined-text-field-hover-outline-color: #2E8B57 !important;
    --mdc-outlined-text-field-focus-label-text-color: #2E8B57 !important;
    --mat-mdc-form-field-font-size: 5px !important;
    background-color: #fff !important;
    --mdc-filled-text-field-container-color: #ffffff00;
    height: 40px !important;
    padding: 0px !important;
    span{
        font-size: 12px;
        color: rgb(193, 193, 193) !important;
        font-weight: 500 !important;

    }
    input.mat-input-element {
        color: white;
    }
}
ng-select{
    font-weight: 500 !important;
    color: rgb(193, 193, 193) !important;
}
mat-slide-toggle {
    //Ativo
    --mdc-switch-selected-handle-color:  #2E8B57 !important;
    --mdc-switch-selected-pressed-handle-color: #2E8B57 !important;
    --mdc-switch-selected-pressed-state-layer-color: #175834 !important;
    --mdc-switch-selected-hover-state-layer-color: #2E8B57 !important;
    --mdc-switch-selected-hover-handle-color: #2E8B57 !important;
    --mdc-switch-selected-focus-state-layer-color: #2E8B57 !important;
    --mdc-switch-selected-focus-handle-color: #2E8B57 !important;
    --mdc-switch-selected-track-color: #175834 !important;
    --mdc-switch-selected-pressed-track-color: #175834 !important;
    --mdc-switch-selected-hover-track-color: #175834 !important;
    --mdc-switch-selected-focus-track-color: #175834 !important;
    --mdc-switch-selected-icon-color:#2E8B57 !important;
    //Desativo 
    --mdc-switch-unselected-handle-color: #fff !important;
    --mdc-switch-unselected-pressed-handle-color: #fff !important;
    --mdc-switch-unselected-pressed-state-layer-color: #97979E !important;
    --mdc-switch-unselected-hover-state-layer-color: #fff !important;
    --mdc-switch-unselected-hover-handle-color: #fff !important;
    --mdc-switch-unselected-focus-state-layer-color: #fff !important;
    --mdc-switch-unselected-focus-handle-color: #fff !important ;
    --mdc-switch-unselected-track-color: #97979E!important ;
    --mdc-switch-unselected-pressed-track-color: #fff !important;
    --mdc-switch-unselected-hover-track-color: #97979E !important;
    --mdc-switch-unselected-focus-track-color: #97979E !important;
    
  }
  .modal-email-atend, .modal-email-pac, .modal-delete-pac{
    background-color: #fff !important;
    border-radius: 10px !important;
}   
mat-checkbox{
    --mdc-checkbox-selected-icon-color: #175834 !important;
    --mdc-checkbox-disabled-selected-icon-color: rgba(255, 255, 255, 0.38) !important;
     --mdc-checkbox-disabled-unselected-icon-color: rgba(0, 0, 0, 0.38) !important;
     --mdc-checkbox-selected-checkmark-color: #ffffff !important;
     --mdc-checkbox-selected-focus-icon-color: #175834 !important;
     --mdc-checkbox-selected-hover-icon-color: #175834 !important;
     --mdc-checkbox-selected-icon-color: #175834 !important; 
     --mdc-checkbox-selected-pressed-icon-color: #175834 !important;
     --mdc-checkbox-unselected-focus-icon-color: #212121 !important;
     --mdc-checkbox-unselected-hover-icon-color: #212121 !important;
     --mdc-checkbox-unselected-icon-color: rgba(0, 0, 0, 0.54) !important;
     --mdc-checkbox-unselected-pressed-icon-color: rgba(0, 0, 0, 0.54) !important;
     --mdc-checkbox-selected-focus-state-layer-color: #175834 !important;
     --mdc-checkbox-selected-hover-state-layer-color: #175834 !important;
     --mdc-checkbox-selected-pressed-state-layer-color: #175834 !important;
     --mdc-checkbox-unselected-focus-state-layer-color: black !important;
     --mdc-checkbox-unselected-hover-state-layer-color: black !important;
     --mdc-checkbox-unselected-pressed-state-layer-color: black !important;
     --mat-checkbox-disabled-label-color: rgba(0, 0, 0, 0.38) !important;
 }
 mat-radio-button{
        --mdc-radio-disabled-selected-icon-color: black;
        --mdc-radio-disabled-unselected-icon-color: black;
        --mdc-radio-unselected-hover-icon-color: #212121;
        --mdc-radio-unselected-icon-color: rgba(0, 0, 0, 0.54);
        --mdc-radio-unselected-pressed-icon-color: rgba(0, 0, 0, 0.54);
        --mdc-radio-selected-focus-icon-color: #2E8B57 !important;
        --mdc-radio-selected-hover-icon-color: #2E8B57 !important;
        --mdc-radio-selected-icon-color: #2E8B57 !important;
        --mdc-radio-selected-pressed-icon-color: #2E8B57 !important;
        --mat-radio-ripple-color: black;
        --mat-radio-checked-ripple-color: #2E8B57 !important;
        --mat-radio-disabled-label-color: rgba(0, 0, 0, 0.38);
 }
 .panel_button {
    color: #000000;
    background-color: #40404000;
    padding: 5px;
}
mat-dialog-container{
    border-radius: 10px !important;
}
.mat-mdc-dialog-container{
    height: auto;
}
.btn-padrao{
    border: none;    
    font-size: 14px;    
    border-radius: 8px;    
    padding: 5px 16px;    
    color: #fff;
    background-color: $primary-dark;
}
.ajuste-icon{
    margin-bottom: -6px;
}
.btn-adicionar {
    background-color: #2E8B57;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 16px !important;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 16px !important;
    height: 40px;
    min-width: 140px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    @media screen {
        margin-left: 0 !important;    
    }
  }
  
  .btn-adicionar:hover {
    background-color: $primary-dark;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
  .toggles-container {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 24px !important;
    gap: 16px;
    background-color: $bg-color;
    padding: 16px !important;
    border-radius:$border-radius;
  }
  
  .toggle-item {
    margin-right: 24px !important;
    background-color: white;
    padding: 8px 16px !important;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all$transition;
  }
  
  .toggle-item:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  
  .toggle-item ::ng-deep .mat-slide-toggle-bar {
    background-color: rgba(0, 0, 0, 0.1);
  }
  
  .toggle-item ::ng-deep .mat-slide-toggle-thumb {
    background-color: white;
  }
  
  .toggle-item ::ng-deep .mat-checked .mat-slide-toggle-bar {
    background-color: rgba(46, 139, 87, 0.5) !important;
  }
  
  .toggle-item ::ng-deep .mat-checked .mat-slide-toggle-thumb {
    background-color: $primary-color !important;
  }
  @media(max-width: 1600px){
    .lista-scroll{
        max-height:44vh !important ;
    }
    /*.card-principal*/ .mother-div{
        height: 84vh !important;
    }
  }
  .card-body {
    padding: 24px ;
    height: 67vh !important;
    overflow: auto !important;
  }
  .container{
    max-width: 1350px !important;
    padding: 0px !important;
  }
  .card, .card-principal, .dashboard-card, .profile-card, .principal {
    border-top:4px solid #2E8B57 !important ;
    box-shadow: none !important;
  }
  .modal-container, .modal-content{
    border-radius: 4px !important;
  }
  .modal-header{
    background-color: #2E8B57 !important;
    padding: 20px 20px !important;
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 60px !important;
    border-radius: 4px 4px 0 0 !important;
    border: none;
  }
  .modal-container ::ng-deep .nsm-content {
    border-radius: $border-radius;
    padding: 0;
    overflow: hidden;
    width: 90%;
    max-width: 500px;
  }
  
  .modal-content {
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    background-color: $primary-color;
    color: white;
    padding: 16px 24px;
    text-align: center;
  }
  
  .modal-header h3 {
    margin: 0;
    font-weight: 500;
    font-size: 18px;
  }
  
  .modal-body {
    padding: 24px;
    text-align: center;
    background-color: #fff;
  }
  .modal-footer {
    display: flex;
    justify-content: center;
    padding: 16px 20px;
    background-color: $secondary-color;
    gap: 12px;
  }
  .cancel-button {
    background-color: #DADDE5;
    color: $text-primary;
  }
  .cancel-button:hover {
    background-color: darken(#DADDE5, 5%);
  }
  
  .confirm-button {
    background-color: $primary-color;
    color: $secondary-light;
  }
  
  .confirm-button:hover {
    background-color: $primary-dark;
  }
  .action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 120px;
  }
  
::ng-deep .ng-select.custom-theme {
    .ng-select-container {
      background-color: #f0f7f4;
      border: 2px solid #4caf50;
      border-radius: 10px;
      padding: 6px;
      font-size: 14px;
      color: #2e7d32;
      transition: all 0.2s ease-in-out;
  
      &:hover {
        border-color: #388e3c;
      }
    }
  
    .ng-arrow-wrapper {
      color: #4caf50;
    }
  
    .ng-dropdown-panel {
      border-radius: 8px;
      border: 1px solid #c8e6c9;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      background-color: #ffffff;
    }
  
    .ng-option {
      padding: 10px;
  
      &.ng-option-marked {
        background-color: #e8f5e9;
      }
  
      &.ng-option-selected {
        background-color: #c8e6c9;
        color: #1b5e20;
      }
    }
  }
    @media (max-width: 1000px) {
    .nsm-content{
        z-index: 1048;
        position: relative;
        margin: 0 auto !important; 
        margin-top: 50px; 
        justify-content: center; 
        display: flex;
        align-items: center; 
        left: 19%;
    }

  }