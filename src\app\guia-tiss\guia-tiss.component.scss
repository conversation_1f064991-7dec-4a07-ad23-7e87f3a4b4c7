/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* ESTILO BASE */
:host {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  max-width: 710px;
  max-height: 90vh;
  background-color: $bg-color;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
  overflow: hidden;
}

.formulario-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 80px; /* Espaço para os botões de ação */
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $secondary-light;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 10px;
  }
}

/* SEÇÕES DO FORMULÁRIO */
.card-secao {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  margin-bottom: 16px;
  overflow: hidden;
}

.secao-header {
  background-color: $primary-color;
  color: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  
  mat-icon {
    margin-right: 8px;
    font-size: 20px;
  }
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.secao-content {
  padding: 16px;
}

/* CAMPOS DO FORMULÁRIO */
.campo-grupo {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  mat-form-field {
    flex: 1;
    min-width: 200px;
  }
  
  .campo-full {
    flex: 100%;
  }
  
  .campo-maior {
    flex: 2;
  }
  
  .campo-menor {
    flex: 1;
  }
}

/* Estilização dos campos Material */
::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: $border-color;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: $primary-color;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0;
}

::ng-deep .mat-form-field-label {
  color: $text-secondary;
}

::ng-deep .mat-form-field.mat-focused .mat-form-field-label {
  color: $primary-color;
}

::ng-deep .mat-select-value, 
::ng-deep .mat-select-arrow {
  color: $text-primary;
}

::ng-deep .mat-select-panel {
  background-color: $card-bg;
}

::ng-deep .mat-option.mat-selected:not(.mat-option-disabled) {
  color: $primary-color;
}

::ng-deep .mat-option:hover:not(.mat-option-disabled) {
  background-color: $primary-light;
}

/* BOTÕES DE AÇÃO */
.acoes-container {
  position: sticky;
  bottom: 0;
  display: flex;
  justify-content: flex-end;
  padding: 16px;
  background-color: $bg-color;
  border-top: 1px solid $border-color;
  z-index: 10;
}

.btn-salvar {
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all $transition;
  height: 40px;
  
  &:hover {
    background-color: $primary-dark;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  mat-icon {
    font-size: 18px;
  }
}

/* RESPONSIVIDADE */
@media (max-width: 600px) {
  .campo-grupo {
    flex-direction: column;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  .acoes-container {
    padding: 12px;
  }
  
  .btn-salvar {
    width: 100%;
    justify-content: center;
  }
}