import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { WebhookAiQuestionarioPayload, WebhookAiQuestionarioResponse } from '../MapPalavrasModel';

@Injectable({
  providedIn: 'root'
})
export class AiQuestionarioApiService {
  private readonly baseUrl = `${environment.apiEndpoint}/AiQuestionarioApi`;

  constructor(private http: HttpClient) { }

  enviarMensagens(payload: WebhookAiQuestionarioPayload): Observable<WebhookAiQuestionarioResponse> {
    return this.http.post<WebhookAiQuestionarioResponse>(this.baseUrl, payload, { headers: new HttpHeaders({ accept: '*/*' }) });
  }
}
