/* VARIÁVEIS DE CORES */
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* IMPORTS */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

/* ESTILOS GERAIS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'Cairo', sans-serif;
  color: $text-primary;
  background-color: $bg-color;
  line-height: 1.6;
}

.hidden {
  display: none;
}

/* Exibição Desktop/Mobile */
.yes-desktop {
  display: block;
  
  @media (max-width: 768px) {
    display: none;
  }
}

.yes-mobile {
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
}

/* CONTAINER PRINCIPAL */
.consultation-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  background-color: transparent;
  padding: 20px;
  height: 86vh;
}

/* COLUNA LATERAL - INFO DO PACIENTE E HISTÓRICO */
.sidebar-column {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 16px;
  margin-bottom: 20px;
  max-width: 300px;
}

/* CARD DE INFORMAÇÕES DO PACIENTE */
.patient-info-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.patient-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 2px solid $primary-light;
  overflow: hidden;
  margin-bottom: 15px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.patient-details {
  width: 100%;
  display: flex;
  align-items: center;
}

.patient-name-field {
  flex: 1;
  
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    height: 0;
  }
  
  ::ng-deep .mat-mdc-text-field-wrapper {
    background-color: $card-bg;
  }
  
  ::ng-deep .mat-mdc-form-field-flex {
    padding: 0 5px;
  }
  
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: $border-color;
  }
}

.info-button {
  color: $primary-color;
  background-color: transparent;
}

/* HISTÓRICO DO PACIENTE */
.history-container {
  padding-top: 10px;
}

.history-title {
  color: $primary-color;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  font-family: 'Cairo', sans-serif;
}

.section-divider {
  border: 0;
  height: 2px;
  background-image: linear-gradient(to right, transparent, $primary-color, transparent);
  margin-bottom: 15px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
}

.history-scroll {
  max-height: 55vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: $primary-color $secondary-light;
  padding-right: 5px;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: $secondary-light;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 10px;
  }
  @media(max-width: 1600px){
    max-height: 46vh !important;
  }
}

.history-item {
  cursor: pointer;
  padding: 8px 0;
  transition: $transition;
  
  &:hover {
    background-color: $primary-light;
    border-radius: 8px;
    padding-left: 5px;
  }
}

.history-date {
  color: $primary-color;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  padding: 5px;
}

.history-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 5px;
  
  mat-icon {
    font-size: 16px;
    color: $primary-color;
    height: 16px;
    width: 16px;
  }
}

.item-divider {
  border: 0;
  height: 1px;
  background-image: linear-gradient(to right, transparent, $border-color, transparent);
  margin-top: 8px;
}

/* COLUNA PRINCIPAL - CONTEÚDO DA CONSULTA */
.main-column {
  padding-left: 20px;
}

/* CONTEÚDO DA CONSULTA */
.consultation-content {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 20px;
  min-height: 68vh;
  width: 100%;
  position: relative;
  @media(max-width:1600px){
    min-height: 63.5vh !important;
    width: 74%;
    min-height: 70vh;

  }
}

/* TIMER DA CONSULTA */
.consultation-timer {
  background-color: $primary-color;
  color: white;
  border-radius: 8px;
  padding: 5px 15px;
  display: inline-block;
  font-family: 'Cairo', sans-serif;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 20px;
  
  label {
    margin: 0;
  }
}

/* HISTÓRICO DE CONSULTA */
.consultation-history {
  max-height: 56vh;
  overflow-y: auto;
  padding-right: 10px;
  scrollbar-width: thin;
  scrollbar-color: $primary-color $secondary-light;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $secondary-light;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 10px;
  }
}

.history-consultation {
  margin-bottom: 30px;
}

.history-card {
  background-color: $primary-light;
  border-radius: $border-radius;
  padding: 15px;
  border-left: 4px solid $primary-color;
}

.history-header {
  text-align: center;
  margin-bottom: 15px;
  color: $primary-dark;
  font-weight: 500;
}

.history-content {
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  margin-bottom: 15px;
}

.dotted-divider {
  border: 0;
  border-top: 2px dotted $primary-color;
  margin: 10px 0;
}

.history-observation {
  display: block;
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.5;
}

.prescription-data {
  margin-top: 15px;
  font-style: italic;
  color: $text-secondary;
}

/* PAINÉIS DE EXPANSÃO */
.patient-data-panel,
.anonymous-panel,
.attachments-panel {
  margin-top: 10px;
  
  mat-expansion-panel {
    background-color: white;
    
    mat-expansion-panel-header {
      padding: 0 15px;
      height: 40px !important;
      
      mat-panel-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        color: $primary-color;
        
        mat-icon, i {
          font-size: 18px;
          height: 18px;
          width: 18px;
        }
        
        img.anonymous-icon {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

.patient-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  padding: 15px;
  border: 1px solid $border-color;
  border-radius: 8px;
  
  .metric-item {
    font-size: 14px;
    white-space: pre-wrap;
    
    span {
      font-weight: 500;
    }
  }
}

.anonymous-content {
  padding: 15px;
  border: 1px solid $border-color;
  border-radius: 8px;
  white-space: pre-wrap;
  font-size: 14px;
}

.attachments-list {
  padding: 10px;
}

.attachment-item {
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.attachment-bubble {
  background-color: $primary-color;
  border-radius: 15px;
  padding: 12px;
  position: relative;
  max-width: 100%;
  color: white;
  
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    border-left: 1px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid $primary-color;
    bottom: 24px;
    left: -10px;
    transform: rotate(90deg);
  }
  
  .attachment-content {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    i {
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      color: white;
    }
    
    .attachment-name {
      font-size: 14px;
      word-break: break-word;
      color: white;
    }
  }
  
  .attachment-meta {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    
    .attachment-date {
      color: rgba(255, 255, 255, 0.7);
    }
    
    .attachment-user {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

/* RESULTADOS DE EXAMES */
.exams-results {
  margin-top: 20px;
}

.section-title {
  color: $primary-color;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  font-family: 'Cairo', sans-serif;
}

.exams-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  
  tbody tr {
    border-bottom: 1px solid $border-color;
    
    &:nth-child(even) {
      background-color: $secondary-light;
    }
    
    &:hover {
      background-color: $primary-light;
    }
    
    th {
      padding: 12px;
      text-align: left;
      vertical-align: top;
      font-weight: normal;
    }
  }
  
  .exam-label {
    font-weight: 600;
    color: $primary-color;
    margin-bottom: 5px;
    font-size: 14px;
  }
  
  .exam-value {
    font-size: 14px;
    color: $text-secondary;
  }
}

/* ANOTAÇÕES ANÔNIMAS */
.anonymous-notes {
  margin-top: 20px;
}

.notes-field {
  width: 100%;
  
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    height: 0;
  }
  
  ::ng-deep .mat-mdc-text-field-wrapper {
    padding: 0;
  }
  
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: $border-color;
  }
  
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
  ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
    border-color: $primary-color;
  }
}

.notes-textarea {
  min-height: 50vh !important;
  resize: none;
  padding: 10px;
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  line-height: 1.6;
  @media(max-width: 1600px){
    min-height: 40vh !important;
  }
}

/* CONSULTA */
.consultation-notes {
  margin-top: 20px;
}

/* COLUNA LATERAL DIREITA - ANEXOS OU DADOS DO PACIENTE */
.sidebar-right {
  position: relative;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $secondary-light;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 10px;
  }
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: transparent;
  border: none;
  color: $text-secondary;
  cursor: pointer;
  z-index: 10;
  
  mat-icon {
    font-size: 18px;
  }
  
  &:hover {
    color: $primary-color;
  }
}

/* ÁREA DE ANEXOS */
.attachments-area {
  padding-top: 10px;
}

.spinner-container {
  text-align: center;
  padding: 30px 0;
}

.dropzone-container {
  border: 2px dashed $border-color;
  border-radius: $border-radius;
  padding: 20px;
  text-align: center;
  background-color: $secondary-light;
  margin-bottom: 20px;
  transition: all $transition;
  
  &:hover {
    border-color: $primary-color;
  }
  
  .upload-icon {
    font-size: 40px;
    color: $primary-color;
    opacity: 0.5;
    margin-bottom: 10px;
  }
  
  .dropzone-text {
    font-size: 13px;
    color: $text-secondary;
    margin-bottom: 20px;
  }
  
  .upload-button {
    display: inline-block;
    background-color: $primary-color;
    color: white;
    padding: 8px 16px;
    border-radius: $border-radius;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all $transition;
    
    &:hover {
      background-color: $primary-dark;
      transform: translateY(-2px);
    }
  }
  
  input[type="file"] {
    display: none;
  }
}

.attachments-list-container {
  max-height: 50vh;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: $secondary-light;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: $primary-color;
    border-radius: 10px;
  }
}

.attachment-group {
  margin-bottom: 15px;
  width: 180px;
}

.attachment-wrapper {
  margin-bottom: 10px;
}

.user-attachment {
  background-color: $primary-color;
  border-radius: 15px;
  padding: 12px;
  position: relative;
  max-width: 100%;
  margin-left: auto;
  max-width: 90%;
  color: white;
  
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    border-left: 20px solid transparent;
    border-right: 1px solid transparent;
    border-top: 20px solid $primary-color;
    bottom: 24px;
    right: -10px;
    transform: rotate(-90deg);
  }
  
  .delete-button {
    color: $error-color;
    background-color: transparent;
    opacity: 0.7;
    
    &:hover {
      opacity: 1;
    }
  }
}

.other-attachment {
  background-color: $primary-color;
  border-radius: 15px;
  padding: 12px;
  position: relative;
  max-width: 90%;
  color: white;
  
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    border-left: 1px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid $primary-color;
    bottom: 24px;
    left: -10px;
    transform: rotate(90deg);
  }
}

/* ÁREA DE DADOS DO PACIENTE */
.patient-data-area {
  padding-top: 10px;
}

.data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.data-header {
  text-align: center;
  margin-bottom: 15px;
  
  h4 {
    color: $primary-color;
    font-size: 18px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
  }
}

.patient-illustration {
  text-align: center;
  margin-bottom: 20px;
  
  .body-image {
    max-width: 150px;
  }
}

.patient-metrics-form {
  width: 100%;
  
  mat-form-field {
    width: 100%;
    margin-bottom: 10px;
    
    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      height: 0;
    }
    
    ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
    ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
    ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
      border-color: $border-color;
    }
    
    ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,
    ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,
    ::ng-deep .mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing {
      border-color: $primary-color;
    }
  }
}

.data-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}

.data-action-button {
  &.clear-button {
    background-color: darken($secondary-color, 5%);
    color: white;
    padding: 5px;
    border-radius: 5px;
    &:hover {
      background-color: #b8b8b8;

    }
  }
  
  &.save-button {
    background-color: $primary-color;
    color: white;
    padding: 5px;
    border-radius: 5px;
    &:hover {
      background-color: $primary-dark;
    }
  }
}

/* BARRA DE BOTÕES DE AÇÃO */
.action-buttons-row {
  margin-top: 20px;
  width: 950px;
}

.action-buttons-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 20px;
  width: 100%;
  @media(max-width: 1600px){
    width: 74%;
  }
}

.action-button-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  button {
    margin-bottom: 5px;
  }
  
  small {
    font-size: 11px;
    color: $text-secondary;
    text-align: center;
    max-width: 80px;
  }
}

.active-button {
  background-color: transparent;
  color: $primary-color;
  padding: 5px;
  border-radius: 4px;
  &:hover{
    background-color: #b8b8b8;
  }
}

.inactive-button {
  background-color: transparent;
  color: $text-secondary;
  padding: 5px;
  border-radius: 4px;
  &:hover{
    background-color: #b8b8b8;
  }
}

.action-button {
  color: $primary-color;
  background-color: transparent;
  border: none;
  width: auto;
  padding: 5px;
  &:hover{
    background-color: #b8b8b8;
  }
}

.finish-button {
  background-color: #175834;
  color: white;
  width: 30px;
  height: 30px;
  display: flex;
  border-radius: 50%;
  align-items: center;
  justify-content: center;
  &:hover {
    background-color: darken($primary-dark, 5%);
  }
}

.exit-button {
  background-color: rgba($error-color, 0.1);
  color: $error-color;
  
  &:hover {
    background-color: $error-color;
    color: white;
  }
}

/* ESTILOS MOBILE */
.consultation-container-mobile {
  width: 100%;
  padding: 15px;
  background-color: $bg-color;
  font-family: 'Inter', 'Cairo', sans-serif;
}

.mobile-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.mobile-timer {
  background-color: $primary-color;
  color: white;
  border-radius: 8px;
  padding: 5px 10px;
  display: inline-block;
  font-family: 'Cairo', sans-serif;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 15px;
  align-self: flex-start;
  
  label {
    margin: 0;
  }
}

.mobile-patient-info {
  display: flex;
  align-items: center;
  background-color: $card-bg;
  padding: 10px;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  
  .patient-name-field {
    flex: 1;
    margin-bottom: 0;
    
    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }
    
    ::ng-deep .mat-mdc-text-field-wrapper {
      padding: 0;
    }
  }
}

.mobile-action-menu {
  position: relative;
  margin-bottom: 15px;
}

.menu-toggle-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  background-color: $primary-color;
  color: white;
}

.mobile-action-buttons {
  position: fixed;
  bottom: 80px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
  
  button {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 56px;
    height: 56px;
    padding: 0;
    
    small {
      font-size: 9px;
      line-height: 1;
      margin-top: 2px;
      max-width: 50px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.mobile-history {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 15px;
  margin-bottom: 15px;
}

.mobile-history-title {
  color: $primary-color;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
  font-family: 'Cairo', sans-serif;
}

.mobile-history-scroll {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 5px;
}

.mobile-history-item {
  cursor: pointer;
  padding: 8px 0;
  transition: $transition;
  
  &:hover {
    background-color: $primary-light;
    border-radius: 8px;
    padding-left: 5px;
  }
}

.mobile-history-date {
  color: $primary-color;
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  padding: 5px;
}

.mobile-history-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 5px;
  
  .mobile-history-icon {
    font-size: 16px;
    color: $primary-color;
    height: 16px;
    width: 16px;
  }
}

.mobile-content {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: 15px;
  margin-bottom: 15px;
  min-height: 56vh;
}

.mobile-consultation-history {
  max-height: 56vh;
  overflow-y: auto;
}

.mobile-history-consultation {
  margin-bottom: 20px;
}

.mobile-history-card {
  background-color: $primary-light;
  border-radius: $border-radius;
  padding: 15px;
  border-left: 4px solid $primary-color;
}

.mobile-history-header {
  text-align: center;
  margin-bottom: 15px;
  color: $primary-dark;
  font-weight: 500;
  font-size: 14px;
}

.mobile-history-content {
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 14px;
}

.mobile-history-observation {
  display: block;
  white-space: pre-wrap;
  line-height: 1.5;
}

.mobile-prescription-data {
  margin-top: 15px;
  font-style: italic;
  color: $text-secondary;
}

.mobile-expansion-panels {
  margin-top: 10px;
  
  mat-expansion-panel {
    background-color: white;
    margin-bottom: 10px;
    
    mat-expansion-panel-header {
      padding: 0 15px;
      height: 40px !important;
      
      mat-panel-title {
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        color: $primary-color;
        
        mat-icon, i {
          font-size: 18px;
          height: 18px;
          width: 18px;
        }
        
        img.anonymous-icon {
          width: 18px;
          height: 18px;
        }
      }
    }
  }
}

.mobile-patient-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  padding: 15px;
  border: 1px solid $border-color;
  border-radius: 8px;
  
  .mobile-metric-item {
    font-size: 14px;
    white-space: pre-wrap;
  }
}

.mobile-anonymous-content {
  padding: 15px;
  border: 1px solid $border-color;
  border-radius: 8px;
  white-space: pre-wrap;
  font-size: 14px;
}

.mobile-attachments-list {
  padding: 10px;
}

.mobile-attachment-item {
  margin-bottom: 15px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.mobile-attachment-bubble {
  background-color: $primary-color;
  border-radius: 15px;
  padding: 12px;
  position: relative;
  max-width: 100%;
  color: white;
  
  .mobile-attachment-content {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    
    i {
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      color: white;
    }
    
    .mobile-attachment-name {
      font-size: 14px;
      word-break: break-word;
      color: white;
    }
  }
  
  .mobile-attachment-meta {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    
    .mobile-attachment-date,
    .mobile-attachment-user {
      color: rgba(255, 255, 255, 0.7);
    }
  }
}

.mobile-exams-results {
  margin-top: 15px;
}

.mobile-section-title {
  color: $primary-color;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  font-family: 'Cairo', sans-serif;
}

.mobile-exams-list {
  .mobile-exam-item {
    background-color: $secondary-light;
    border-radius: $border-radius;
    padding: 15px;
    margin-bottom: 15px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .mobile-exam-header {
      font-weight: 600;
      color: $primary-color;
      margin-bottom: 5px;
      font-size: 14px;
    }
    
    .mobile-exam-value {
      font-size: 14px;
      color: $text-secondary;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.mobile-anonymous-notes,
.mobile-consultation-notes {
  margin-top: 15px;
}

.mobile-notes-field {
  width: 100%;
  
  ::ng-deep .mat-mdc-form-field-subscript-wrapper {
    height: 0;
  }
}

.mobile-notes-textarea {
  min-height: 40vh !important;
  resize: none;
  padding: 10px;
  font-family: 'Inter', sans-serif;
  font-size: 15px;
  line-height: 1.6;
}

.mobile-attachments-area,
.mobile-patient-data-area {
  position: relative;
  padding-top: 10px;
}

.mobile-spinner-container {
  text-align: center;
  padding: 30px 0;
}

.mobile-dropzone-container {
  border: 2px dashed $border-color;
  border-radius: $border-radius;
  padding: 15px;
  text-align: center;
  background-color: $secondary-light;
  margin-bottom: 15px;
  
  .upload-icon {
    font-size: 30px;
    color: $primary-color;
    opacity: 0.5;
    margin-bottom: 10px;
  }
  
  .mobile-dropzone-text {
    font-size: 12px;
    color: $text-secondary;
    margin-bottom: 15px;
  }
  
  .mobile-upload-button {
    display: inline-block;
    background-color: $primary-color;
    color: white;
    padding: 8px 16px;
    border-radius: $border-radius;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
  }
  
  input[type="file"] {
    display: none;
  }
}

.mobile-attachments-list-container {
  max-height: 40vh;
  overflow-y: auto;
}

.mobile-attachment-group {
  margin-bottom: 15px;
}

.mobile-attachment-wrapper {
  margin-bottom: 10px;
}

.mobile-user-attachment {
  background-color: $primary-color;
  border-radius: 15px;
  padding: 12px;
  position: relative;
  max-width: 100%;
  margin-left: auto;
  max-width: 90%;
  color: white;
  
  .mobile-delete-button {
    color: white;
    opacity: 0.7;
    
    &:hover {
      opacity: 1;
    }
  }
}

.mobile-other-attachment {
  background-color: $primary-color;
  border-radius: 15px;
  padding: 12px;
  position: relative;
  max-width: 90%;
  color: white;
}

.mobile-close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: transparent;
  border: none;
  color: $text-secondary;
  cursor: pointer;
  z-index: 10;
  
  mat-icon {
    font-size: 18px;
  }
}

.mobile-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mobile-data-header {
  text-align: center;
  margin-bottom: 15px;
  
  h4 {
    color: $primary-color;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
  }
}

.mobile-patient-illustration {
  text-align: center;
  margin-bottom: 15px;
  
  .mobile-body-image {
    max-width: 120px;
  }
}

.mobile-patient-metrics-form {
  width: 100%;
  
  mat-form-field {
    width: 100%;
    margin-bottom: 10px;
    
    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      height: 0;
    }
  }
}

.mobile-data-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.mobile-data-action-button {
  &.mobile-clear-button {
    background-color: $secondary-color;
    color: $text-secondary;
  }
  
  &.mobile-save-button {
    background-color: $primary-color;
    color: white;
  }
}