import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { trigger, style, transition, animate } from '@angular/animations';
import { ModalColetaDadosVittaltecComponent } from '../fila-espera/modal-coleta-dados-vittaltec/modal-coleta-dados-vittaltec.component';
import { QuestionarioPreConsultaDados, WebhookAiQuestionarioPayload, WebhookAiQuestionarioResponse } from './MapPalavrasModel';
import { Subject, takeUntil } from 'rxjs';
import { VoiceRecorderService } from './Service/voice-recorder.service';
import { SpeakerService } from './Service/speaker.service';
import { AiQuestionarioApiService } from './Service/ai-questionario-api.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CriptografarUtil } from 'src/app/Util/Criptografar.util';

interface FilterOption {
  type: string;
  label: string;
  icon: string;
  active: boolean;
}

interface EnhancedDataItem {
  label: string;
  value: string;
  category: string;
  categoryLabel: string;
  icon: string;
  timestamp?: Date;
  validationStatus: 'valid' | 'warning' | 'error';
}

@Component({
  selector: 'app-pre-consulta-questionario',
  templateUrl: './pre-consulta-questionario.component.html',
  styleUrls: ['./pre-consulta-questionario.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatIconModule,
    MatDialogModule,
    MatTooltipModule,
    MatChipsModule,
    MatSnackBarModule
  ],
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease-out', style({ opacity: 0 }))
      ])
    ]),
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateY(-50px)', opacity: 0 }),
        animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)',
          style({ transform: 'translateY(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-in',
          style({ transform: 'translateY(-30px)', opacity: 0 }))
      ])
    ]),
    trigger('cardAnimation', [
      transition(':enter', [
        style({ transform: 'scale(0.8)', opacity: 0 }),
        animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)',
          style({ transform: 'scale(1)', opacity: 1 }))
      ])
    ])
  ]
})
export class PreConsultaQuestionarioComponent implements OnInit, OnDestroy {

  isIdle = true;
  isProcessing = false;
  isTextMode = false;
  isRecording = false;
  isAguardandoResposta = false;
  campoAtual = '';
  isHistoryModalOpen = false;

  // Novos estados para controle de fala e UI aprimorada
  isSpeaking = false;
  displayedText = '';
  fullResponseText = '';
  textDisplayInterval: any;

  searchTerm = '';
  availableFilters: FilterOption[] = [
    { type: 'personal', label: 'Dados Pessoais', icon: 'person', active: true },
    { type: 'medical', label: 'Informações Médicas', icon: 'medical_services', active: true },
    { type: 'contact', label: 'Contato', icon: 'contact_phone', active: true },
    { type: 'optional', label: 'Opcionais', icon: 'info', active: true }
  ];

  conversationHistory: string[] = [];
  currentToken = '';
  aiResponse = '';
  userInput = '';

  dadosColetados: QuestionarioPreConsultaDados = {
    nome: '',
    cpf: '',
    email: '',
    telefone: '',
    dataNascimento: '',
    alergias: '',
    sintomas: '',
    intensidadeDor: '',
    tempoSintomas: '',
    doencasPrevias: '',
    observacoes: ''
  };

  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private dialog: MatDialog,
    private voiceRecorder: VoiceRecorderService,
    private speaker: SpeakerService,
    private aiService: AiQuestionarioApiService,
    private snackBar: AlertComponent,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    this.preloadVoices();
    this.startSpeakingMonitor();

    this.voiceRecorder.recording$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isRecording => {
        this.isRecording = isRecording;
        this.cdr.detectChanges();
      });

    this.voiceRecorder.result$
      .pipe(takeUntil(this.destroy$))
      .subscribe(result => {
        if (result.success && result.text) {
          this.adicionarRespostaUsuario(result.text);
        }
        this.cdr.detectChanges();
      });

    this.voiceRecorder.error$
      .pipe(takeUntil(this.destroy$))
      .subscribe(error => {
        console.error('Erro de reconhecimento de voz:', error);
        this.isRecording = false;
        this.cdr.detectChanges();

        if (!error.includes('aborted')) {
          this.snackBar.falhaSnackbar(error);

          if (!error.includes('not-allowed') && !this.isTextMode && !this.isProcessing) {
            this.iniciarGravacaoAutomatica();
          }
        }
      });

    this.voiceRecorder.recordingEvent$
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        if (event.type === 'ended_automatically' && this.isAguardandoResposta && !this.isTextMode) {
          if (this.speaker.isSpeaking()) {
            this.speaker.waitUntilFinished().then(() => {
              if (this.isAguardandoResposta && !this.isRecording) {
                this.iniciarGravacaoAutomatica();
              }
            });
          } else {
            setTimeout(() => {
              if (this.isAguardandoResposta && !this.isRecording) {
                this.iniciarGravacaoAutomatica();
              }
            }, 500);
          }
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.speaker.cancel();
    this.voiceRecorder.stopRecording();
    this.clearTextDisplayInterval();
  }

  iniciarAtendimento(): void {
    this.isIdle = false;
    this.cdr.detectChanges();
    this.currentToken = this.generateToken();
    this.conversationHistory = ['Iniciar atendimento'];
    this.campoAtual = 'inicio';

    if (!this.isTextMode) {
      this.solicitarPermissaoMicrofone();
    }

    this.enviarMensagemParaIA();
  }

  toggleTextMode(): void {
    this.cdr.detectChanges();
    this.userInput = '';
  }

  private enviarMensagemParaIA(): void {
    this.isProcessing = true;
    this.cdr.detectChanges();

    const ultimaMensagem = this.conversationHistory.length > 0
      ? this.conversationHistory[this.conversationHistory.length - 1]
      : '';

    const payload: WebhookAiQuestionarioPayload = {
      formaDeResposta: this.isTextMode ? 'Texto digitado' : 'Audio gravado por microfone',
      historicoConversa: [...this.conversationHistory],
      ultimaMensagem: ultimaMensagem,
      campoAtual: this.campoAtual,
      token: this.currentToken
    };

    if (this.conversationHistory.length > 1 && !this.isTextMode) {
      if (this.voiceRecorder.isCurrentlyRecording()) {
        this.voiceRecorder.stopRecording();
      }
    }

    this.aiService.enviarMensagens(payload)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: WebhookAiQuestionarioResponse) => {
          this.processarRespostaIA(response);
        },
        error: (error) => {
          this.isProcessing = false;
          this.cdr.detectChanges();
          this.snackBar.falhaSnackbar('Erro ao comunicar com a IA. Tente novamente.');
          console.error('Erro na comunicação com IA:', error);
        }
      });
  }

  private processarRespostaIA(response: WebhookAiQuestionarioResponse): void {
    this.isProcessing = false;
    this.fullResponseText = response.textoResposta;
    this.displayedText = '';

    this.adicionarRespostaIA(response.textoResposta);

    if (response.campoAtual) {
      this.campoAtual = response.campoAtual;
    }

    this.cdr.detectChanges();

    this.atualizarDadosColetados(response.dados);
    this.cdr.detectChanges();

    if (response.flgFinalizar && this.todosOsCamposPreenchidos()) {
      this.finalizarProcesso();
    } else {
      // Iniciar exibição sincronizada do texto com a fala
      this.startSynchronizedTextDisplay();

      this.speaker.speak(response.textoResposta).then(() => {
        this.iniciarProcessoAguardandoResposta();
      }).catch(error => {
        console.error('Erro na reprodução da IA:', error);
        this.displayedText = this.fullResponseText; // Mostrar texto completo em caso de erro
        this.cdr.detectChanges();
        this.iniciarProcessoAguardandoResposta();
      });
    }
  }

  private adicionarRespostaIA(resposta: string): void {
    this.conversationHistory.push(`(resposta da ia) ${resposta}`);
    this.cdr.detectChanges();
  }

  private atualizarDadosColetados(novosdados: QuestionarioPreConsultaDados): void {
    Object.keys(novosdados).forEach(key => {
      if (novosdados[key as keyof QuestionarioPreConsultaDados] &&
        novosdados[key as keyof QuestionarioPreConsultaDados].trim() !== '') {
        this.dadosColetados[key as keyof QuestionarioPreConsultaDados] =
          novosdados[key as keyof QuestionarioPreConsultaDados];
      }
    });
    this.cdr.detectChanges();
  }

  private todosOsCamposPreenchidos(): boolean {
    const camposObrigatorios = ['nome', 'cpf', 'email', 'telefone', 'dataNascimento', 'sintomas', 'intensidadeDor', 'tempoSintomas'];
    return camposObrigatorios.every(campo =>
      this.dadosColetados[campo as keyof QuestionarioPreConsultaDados] &&
      this.dadosColetados[campo as keyof QuestionarioPreConsultaDados].trim() !== ''
    );
  }

  private iniciarProcessoAguardandoResposta(): void {
    this.isAguardandoResposta = true;
    this.cdr.detectChanges();

    if (!this.isTextMode)
      setTimeout(() => {
        this.iniciarGravacaoContinua();
      }, 500);
  }

  private async iniciarGravacaoContinua(): Promise<void> {
    if (!this.voiceRecorder.isSupported()) {
      console.error('Reconhecimento de voz não suportado');
      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');
      return;
    }

    // Redução de latência: monitorar mais frequentemente o fim da fala
    await this.waitForSpeechEndWithReducedLatency();
    this.iniciarGravacaoAutomatica();
    this.monitorarEstadoGravacao();
  }

  private monitorarEstadoGravacao(): void {
    const intervalId = setInterval(() => {
      if (!this.isAguardandoResposta) {
        clearInterval(intervalId);
        return;
      }

      if (this.isTextMode) {
        return;
      }

      const serviceRecording = this.voiceRecorder.isCurrentlyRecording();
      const componentRecording = this.isRecording;

      if (serviceRecording !== componentRecording) {
        this.isRecording = serviceRecording;
        this.cdr.detectChanges();
      }

      if (this.isAguardandoResposta && !serviceRecording && !componentRecording && !this.speaker.isSpeaking()) {
        this.iniciarGravacaoAutomatica();
      }
    }, 2000);
  }

  private finalizarProcessoAguardandoResposta(): void {
    this.isAguardandoResposta = false;
    this.cdr.detectChanges();

    if (this.isRecording) {
      this.voiceRecorder.stopRecording();
    }
  }

  private iniciarGravacaoAutomatica(): void {
    if (!this.voiceRecorder.isSupported()) {
      console.error('Reconhecimento de voz não suportado');
      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');
      return;
    }

    // Não iniciar gravação se o sistema estiver falando
    if (this.speaker.isSpeaking()) {
      console.log('🔇 Aguardando término da fala para iniciar gravação');
      return;
    }

    const serviceRecording = this.voiceRecorder.isCurrentlyRecording();
    const componentRecording = this.isRecording;

    if (serviceRecording || componentRecording) {
      return;
    }

    const success = this.voiceRecorder.startRecording();

    if (!success)
      console.error('❌ Falha ao iniciar gravação automática');

    this.cdr.detectChanges();
  }

  iniciarGravacao(): void {
    if (!this.voiceRecorder.isSupported()) {
      this.snackBar.falhaSnackbar('Reconhecimento de voz não suportado neste navegador');
      return;
    }

    // Verificar se o sistema está falando
    if (this.speaker.isSpeaking()) {
      this.snackBar.falhaSnackbar('Aguarde o término da fala para gravar');
      return;
    }

    const success = this.voiceRecorder.startRecording();
    if (!success) {
      this.snackBar.falhaSnackbar('Erro ao iniciar gravação');
    }
    this.cdr.detectChanges();
  }

  pararGravacao(): void {
    this.voiceRecorder.stopRecording();
    this.cdr.detectChanges();
  }

  toggleRecording(): void {
    if (this.isRecording) {
      this.pararGravacao();
    } else {
      this.iniciarGravacao();
    }
  }

  private preloadVoices(): void {
    const synthesis = window.speechSynthesis;
    const voices = synthesis.getVoices();

    if (voices.length === 0) {
      synthesis.onvoiceschanged = () => {
        const newVoices = synthesis.getVoices();
        newVoices;
      };

      const silentUtterance = new SpeechSynthesisUtterance('');
      silentUtterance.volume = 0;
      synthesis.speak(silentUtterance);
      synthesis.cancel();
    }
  }

  testarFluxoCompleto(): void {
    this.speaker.speak('Esta é uma mensagem de teste. Após eu terminar de falar, o microfone deve abrir automaticamente.').then(() => {
      this.iniciarProcessoAguardandoResposta();
    });
  }

  enviarTexto(): void {
    // Não permitir envio se o sistema estiver falando
    if (this.speaker.isSpeaking()) {
      this.snackBar.falhaSnackbar('Aguarde o término da fala para enviar');
      return;
    }

    if (this.userInput.trim()) {
      this.adicionarRespostaUsuario(this.userInput);
      this.userInput = '';
      this.cdr.detectChanges();
    }
  }

  private adicionarRespostaUsuario(resposta: string): void {
    if (this.isAguardandoResposta) {
      this.finalizarProcessoAguardandoResposta();
    }

    this.conversationHistory.push(`(resposta do usuário) ${resposta}`);
    this.cdr.detectChanges();
    this.enviarMensagemParaIA();
  }

  private async solicitarPermissaoMicrofone(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
    } catch (error) {
      console.error('Erro ao solicitar permissão de microfone:', error);
      this.snackBar.falhaSnackbar('Permissão de microfone necessária para o modo de voz. Por favor, permita o acesso.');
    }
  }

  private generateToken(): string {
    const now = new Date();
    const timestamp = now.getTime();
    const random = Math.floor(Math.random() * 10000);
    return `${timestamp}_${random}`;
  }

  // Métodos para controle de fala e exibição sincronizada
  private startSpeakingMonitor(): void {
    setInterval(() => {
      const currentlySpeaking = this.speaker.isSpeaking();
      if (this.isSpeaking !== currentlySpeaking) {
        this.isSpeaking = currentlySpeaking;
        this.cdr.detectChanges();
      }
    }, 100); // Verificar a cada 100ms
  }

  private startSynchronizedTextDisplay(): void {
    this.clearTextDisplayInterval();
    this.displayedText = '';

    const words = this.fullResponseText.split(' ');
    const totalDuration = this.estimateSpeechDuration(this.fullResponseText);
    const intervalTime = totalDuration / words.length;

    let currentWordIndex = 0;

    this.textDisplayInterval = setInterval(() => {
      if (currentWordIndex < words.length) {
        if (currentWordIndex === 0) {
          this.displayedText = words[currentWordIndex];
        } else {
          this.displayedText += ' ' + words[currentWordIndex];
        }
        currentWordIndex++;
        this.cdr.detectChanges();
      } else {
        this.clearTextDisplayInterval();
      }
    }, intervalTime);
  }

  private estimateSpeechDuration(text: string): number {
    // Estimar duração baseada em ~150 palavras por minuto (velocidade média de fala)
    const words = text.split(' ').length;
    const wordsPerMinute = 150;
    const durationInMinutes = words / wordsPerMinute;
    return (durationInMinutes * 60 * 1000); // Converter para milissegundos
  }

  private clearTextDisplayInterval(): void {
    if (this.textDisplayInterval) {
      clearInterval(this.textDisplayInterval);
      this.textDisplayInterval = null;
    }
  }

  private finalizarProcesso(): void {
    this.cdr.detectChanges();
    this.salvarDadosQuestionario();
    this.abrirDialogColetaDadosVittalTec();
  }

  private salvarDadosQuestionario(): void {
    try {
      const dadosParaSalvar = {
        nome: this.dadosColetados.nome,
        idade: this.calcularIdade(this.dadosColetados.dataNascimento),
        cpf: this.dadosColetados.cpf,
        email: this.dadosColetados.email,
        telefone: this.dadosColetados.telefone,
        dataNascimento: this.dadosColetados.dataNascimento,
        sintomas: this.dadosColetados.sintomas ? this.dadosColetados.sintomas.split(',').map(s => s.trim()) : [],
        sintomasOutros: this.dadosColetados.observacoes || '',
        intensidadeDor: this.extrairNumeroIntensidade(this.dadosColetados.intensidadeDor),
        tempoSintomas: this.dadosColetados.tempoSintomas,
        alergias: this.dadosColetados.alergias || '',
        doencasPrevias: this.dadosColetados.doencasPrevias || '',
        observacoes: this.dadosColetados.observacoes || '',
        dataPreenchimento: new Date().toISOString()
      };

      CriptografarUtil.localStorageCriptografado('questionario-pre-consulta', JSON.stringify(dadosParaSalvar));
      console.log('Dados do questionário salvos no localStorage:', dadosParaSalvar);
    } catch (error) {
      console.error('Erro ao salvar dados do questionário:', error);
      this.snackBar.falhaSnackbar('Erro ao salvar dados do questionário');
    }
  }

  private calcularIdade(dataNascimento: string): number {
    if (!dataNascimento) return 0;

    try {
      const nascimento = new Date(dataNascimento);
      const hoje = new Date();
      let idade = hoje.getFullYear() - nascimento.getFullYear();
      const mesAtual = hoje.getMonth();
      const mesNascimento = nascimento.getMonth();

      if (mesAtual < mesNascimento || (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {
        idade--;
      }

      return idade;
    } catch {
      return 0;
    }
  }

  private extrairNumeroIntensidade(intensidade: string): number {
    if (!intensidade) return 0;

    const match = intensidade.match(/\d+/);
    return match ? parseInt(match[0], 10) : 0;
  }

  private redirecionarParaFilaEspera(): void {
    try {
      this.router.navigate(['/filaespera']);
    } catch (error) {
      console.error('Erro ao redirecionar para fila de espera:', error);
      this.snackBar.falhaSnackbar('Erro ao prosseguir. Redirecionando...');
      this.router.navigate(['/filaespera']);
    }
  }

  private abrirDialogColetaDadosVittalTec(): void {
    const dialogRef = this.dialog.open(ModalColetaDadosVittaltecComponent, {
      disableClose: true,
      width: '500px',
      maxWidth: '85vw',
      height: 'auto',
      maxHeight: '70vh',
      panelClass: 'vittaltec-modal-panel',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result?.action === 'continuar') {
        this.snackBar.sucessoSnackbar("Dados coletados com sucesso!");
        this.redirecionarParaFilaEspera();
      } else if (result?.action === 'cancelar') {
        this.snackBar.falhaSnackbar("Processo de coleta de dados cancelado.");
        CriptografarUtil.removerLocalStorageCriptografado('questionario-pre-consulta');
      }

      this.cdr.detectChanges();
    });
  }

  getDadosPreenchidos(): Array<{ label: string, value: string }> {
    const labels: { [key: string]: string } = {
      nome: 'Nome',
      cpf: 'CPF',
      email: 'Email',
      telefone: 'Telefone',
      dataNascimento: 'Data de Nascimento',
      alergias: 'Alergias',
      sintomas: 'Sintomas',
      intensidadeDor: 'Intensidade da Dor',
      tempoSintomas: 'Tempo dos Sintomas',
      doencasPrevias: 'Doenças Prévias',
      observacoes: 'Observações'
    };

    return Object.keys(this.dadosColetados)
      .filter(key => this.dadosColetados[key as keyof QuestionarioPreConsultaDados] &&
        this.dadosColetados[key as keyof QuestionarioPreConsultaDados].trim() !== '')
      .map(key => ({
        label: labels[key],
        value: this.dadosColetados[key as keyof QuestionarioPreConsultaDados]
      }));
  }

  getUltimaVariavelPreenchida(): { label: string, value: string } | null {
    const dados = this.getDadosPreenchidos();
    return dados.length > 0 ? dados[dados.length - 1] : null;
  }

  openHistoryModal(): void {
    import('./components/history-modal-dialog.component').then(({ HistoryModalDialogComponent }) => {
      const dadosClone = JSON.parse(JSON.stringify(this.dadosColetados));
      this.dialog.open(HistoryModalDialogComponent, {
        width: '900px',
        maxWidth: '98vw',
        data: {
          dadosColetados: dadosClone,
          snackBar: this.snackBar,
          cdr: this.cdr
        },
        panelClass: 'modern-modal-overlay',
        autoFocus: false
      });
    });
  }

  getTotalCampos(): number {
    return 10;
  }

  getProgressPercentage(): number {
    const total = this.getTotalCampos();
    const filled = this.getDadosPreenchidos().length;
    return Math.round((filled / total) * 100);
  }

  onSearchChange(event: any): void {
    this.searchTerm = event.target.value;
  }

  clearSearch(): void {
    this.searchTerm = '';
  }

  toggleFilter(filter: FilterOption): void {
    filter.active = !filter.active;
  }

  getFilteredData(): EnhancedDataItem[] {
    let data = this.getEnhancedDadosPreenchidos();

    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      data = data.filter(item =>
        item.label.toLowerCase().includes(searchLower) ||
        item.value.toLowerCase().includes(searchLower)
      );
    }

    const activeCategories = this.availableFilters
      .filter(f => f.active)
      .map(f => f.type);

    data = data.filter(item => activeCategories.includes(item.category));

    return data;
  }

  getEnhancedDadosPreenchidos(): EnhancedDataItem[] {
    const categoryMap: { [key: string]: { category: string, categoryLabel: string, icon: string } } = {
      nome: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'person' },
      cpf: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'badge' },
      dataNascimento: { category: 'personal', categoryLabel: 'Dados Pessoais', icon: 'cake' },
      email: { category: 'contact', categoryLabel: 'Contato', icon: 'email' },
      telefone: { category: 'contact', categoryLabel: 'Contato', icon: 'phone' },
      sintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'medical_services' },
      intensidadeDor: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'personal_injury' },
      tempoSintomas: { category: 'medical', categoryLabel: 'Informações Médicas', icon: 'schedule' },
      alergias: { category: 'optional', categoryLabel: 'Opcionais', icon: 'medical_information' },
      observacoes: { category: 'optional', categoryLabel: 'Opcionais', icon: 'description' }
    };

    const labels: { [key: string]: string } = {
      nome: 'Nome',
      cpf: 'CPF',
      email: 'Email',
      telefone: 'Telefone',
      dataNascimento: 'Data de Nascimento',
      alergias: 'Alergias',
      sintomas: 'Sintomas',
      intensidadeDor: 'Intensidade da Dor',
      tempoSintomas: 'Tempo dos Sintomas',
      doencasPrevias: 'Doenças Prévias',
      observacoes: 'Observações'
    };

    return Object.keys(this.dadosColetados)
      .filter(key => this.dadosColetados[key as keyof QuestionarioPreConsultaDados] &&
        this.dadosColetados[key as keyof QuestionarioPreConsultaDados].trim() !== '')
      .map(key => {
        const categoryInfo = categoryMap[key] || { category: 'optional', categoryLabel: 'Outros', icon: 'info' };
        return {
          label: labels[key] || key,
          value: this.dadosColetados[key as keyof QuestionarioPreConsultaDados],
          category: categoryInfo.category,
          categoryLabel: categoryInfo.categoryLabel,
          icon: categoryInfo.icon,
          timestamp: new Date(),
          validationStatus: this.getValidationStatusForField(key) as 'valid' | 'warning' | 'error'
        };
      });
  }

  getValidationStatusForField(field: string): string {
    const value = this.dadosColetados[field as keyof QuestionarioPreConsultaDados];
    if (!value || value.trim() === '') return 'error';

    if (field === 'email' && !value.includes('@')) return 'warning';
    if (field === 'cpf' && value.length < 11) return 'warning';

    return 'valid';
  }

  trackByFn(index: number, item: EnhancedDataItem): string {
    index;
    return item.label + item.value;
  }

  highlightSearchTerm(text: string): string {
    if (!this.searchTerm) return text;

    const regex = new RegExp(`(${this.searchTerm})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  formatTimestamp(timestamp: Date): string {
    return timestamp.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getValidationIcon(status: string): string {
    switch (status) {
      case 'valid': return 'check_circle';
      case 'warning': return 'warning';
      case 'error': return 'error';
      default: return 'info';
    }
  }

  getValidationLabel(status: string): string {
    switch (status) {
      case 'valid': return 'Válido';
      case 'warning': return 'Atenção';
      case 'error': return 'Erro';
      default: return 'Info';
    }
  }

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      this.snackBar.sucessoSnackbar('Texto copiado para a área de transferência');
    }).catch(() => {
      this.snackBar.falhaSnackbar('Erro ao copiar texto');
    });
  }

  editValue(item: EnhancedDataItem): void {
    const newValue = prompt(`Editar ${item.label}:`, item.value);
    if (newValue !== null && newValue !== item.value) {
      const field = Object.keys(this.dadosColetados).find(key => {
        const labels: { [key: string]: string } = {
          nome: 'Nome',
          cpf: 'CPF',
          email: 'Email',
          telefone: 'Telefone',
          dataNascimento: 'Data de Nascimento',
          alergias: 'Alergias',
          sintomas: 'Sintomas',
          intensidadeDor: 'Intensidade da Dor',
          tempoSintomas: 'Tempo dos Sintomas',
          doencasPrevias: 'Doenças Prévias',
          observacoes: 'Observações'
        };
        return labels[key] === item.label;
      });

      if (field) {
        this.dadosColetados[field as keyof QuestionarioPreConsultaDados] = newValue;
        this.snackBar.sucessoSnackbar('Valor atualizado com sucesso');
        this.cdr.detectChanges();
      }
    }
  }

  clearAllData(): void {
    if (confirm('Tem certeza que deseja limpar todos os dados coletados?')) {
      this.dadosColetados = {
        nome: '',
        cpf: '',
        email: '',
        telefone: '',
        dataNascimento: '',
        alergias: '',
        sintomas: '',
        intensidadeDor: '',
        tempoSintomas: '',
        doencasPrevias: '',
        observacoes: ''
      };
      this.snackBar.sucessoSnackbar('Todos os dados foram limpos');
      this.cdr.detectChanges();
    }
  }

  // Métodos auxiliares para controle de UI
  isInputDisabled(): boolean {
    return this.speaker.isSpeaking() || this.isProcessing;
  }

  isMicrophoneDisabled(): boolean {
    return this.speaker.isSpeaking() || this.isProcessing;
  }

  canSendText(): boolean {
    return !this.speaker.isSpeaking() && !this.isProcessing && !!this.userInput?.trim();
  }

  // Método para reduzir latência na transição de áudio
  private async waitForSpeechEndWithReducedLatency(): Promise<void> {
    return new Promise<void>((resolve) => {
      const checkInterval = 50; // Verificar a cada 50ms para reduzir latência
      const maxWaitTime = 30000; // Máximo 30 segundos de espera
      let elapsedTime = 0;

      const intervalId = setInterval(() => {
        elapsedTime += checkInterval;

        if (!this.speaker.isSpeaking()) {
          clearInterval(intervalId);
          // Pequena pausa adicional para garantir que o áudio terminou completamente
          setTimeout(() => {
            resolve();
          }, 100);
        } else if (elapsedTime >= maxWaitTime) {
          clearInterval(intervalId);
          console.warn('Timeout aguardando fim da fala');
          resolve();
        }
      }, checkInterval);
    });
  }
}