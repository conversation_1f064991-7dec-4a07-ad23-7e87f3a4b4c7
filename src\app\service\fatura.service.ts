
import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { catchError, of, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ProcedimentoModelview } from '../model/procedimento';
import { FaturaModelview } from '../model/fatura';
import { GuiaTissModelview } from '../model/consulta';

@Injectable({
    providedIn: 'root'
})
export class FaturaService {

  constructor(
    private http: HttpClient,
  ) { }


  public GetListaFatura(flgInativos: boolean) {
    let params = new HttpParams();
    params = params.append('flgInativos', flgInativos);
    return this.http.get<FaturaModelview[]>(environment.apiEndpoint + '/Fatura/GetListaFatura', { params })
      .pipe(
        catchError(() => {
          return of();
        })
      );
  }

  public SalvarFatura(fatura: FaturaModelview) {
    return this.http.post(environment.apiEndpoint + '/Fatura/SalvarFatura', fatura)
      .pipe(
        catchError((error) => {
          console.error('Erro SalvarFatura', error);
          return throwError(() => error);
        })
      );
  }

  public GetListaProcedimentosFatura(idFatura: number) {
    let params = new HttpParams();
    params = params.append('idFatura', idFatura);
    return this.http.get<ProcedimentoModelview[]>(environment.apiEndpoint + '/Fatura/GetListaProcedimentosFatura', { params })
      .pipe(
        catchError((error) => {
          ;
          return throwError(() => error);
        })
      );
  }

  public GetListaGuiaTiss() {

    return this.http.get<GuiaTissModelview[]>(environment.apiEndpoint + '/Fatura/GetListaGuiaTiss')
      .pipe(
        catchError((error) => {
          ;
          return throwError(() => error);
        })
      );
  }

  public BaixarRelatorioListaGuiaTiss(listaIdGuiaTiss: number[]) {
    return this.http.post(environment.apiEndpoint + '/Fatura/BaixarRelatorioListaGuiaTiss', listaIdGuiaTiss, { responseType: 'arraybuffer' })
        .pipe(
            catchError((error) => {
                ;
                return throwError(() => error);
            })
        );
}
}
