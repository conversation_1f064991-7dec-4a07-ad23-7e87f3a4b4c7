import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { TabTipoResposta } from '../model/tipoResposta';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class TipoRespostaService {
    constructor(
        private http: HttpClient,
        private spinner : SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public getTipoResposta(){
        this.spinner.show();
        return this.http.get<TabTipoResposta[]>(environment.apiEndpoint + '/formulario/getTipoResposta');
    }
}