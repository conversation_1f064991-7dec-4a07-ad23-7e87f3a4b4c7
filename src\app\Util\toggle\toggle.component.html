<div class="Toggle">
    <div class="Complementos">
        <span *ngIf="texto">
            {{texto}}
        </span>

    </div>
    <div [ngClass]="{'Disabled': desabilitado }">
        <label class="toggle" [ngClass]="{'Ativo': flg, 'Inativo': !flg}">
            <!-- <input [(ngModel)]="flg" type="checkbox" (change)="flg$.emit(flg)"> -->
            <span class="slider"></span>
        </label>
    </div>
</div>