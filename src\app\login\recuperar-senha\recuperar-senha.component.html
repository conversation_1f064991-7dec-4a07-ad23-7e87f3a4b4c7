<div class="background-login">
    <div class="div-login">
        <mat-card appearance="outlined" class="div-login">

                    <header>
                        <a href="http://faso.com.br" target="_blank"><img src="/assets/build/img/logo faso Final  - Copia.png"
                                class="logo-footer" style="margin-left: 2% !important;
                            cursor: pointer;"></a>


                    </header>
                    <mat-card-title class="text-center">
                                                  width: 75% !important;" alt="logo" class="logo-controler">
                    </mat-card-title>
                    <mat-card-subtitle>

                    </mat-card-subtitle>
                    <mat-divider class="p-t-20"></mat-divider>
                    <br>



                    <form class="padding-10">
                        <mat-form-field class="input-login" appearance="outline">
                            <mat-label><i class="space-icon far fa-id-card"></i><i> {{ 'TELALOGINRECUPERARSENHA.CPF' | translate }}</i></mat-label>
                            <input matInput placeholder="{{ 'TELALOGINRECUPERARSENHA.CPF' | translate }}" id="cpf" name="cpf" (keypress)="mascaraCpf('###.###.###-##', $event)" (keyup)="mascaraCpf('###.###.###-##', $event)" mask="000.000.000-00" maxlength="14" [(ngModel)]="dados.cpf">
                        </mat-form-field>
                        <mat-form-field class="input-login" appearance="outline">
                            <mat-label>
                                <mat-icon class="space-icon">email</mat-icon> <i> {{ 'TELALOGINRECUPERARSENHA.EMAIL' | translate }}</i></mat-label>
                            <input matInput placeholder="{{ 'TELALOGINRECUPERARSENHA.EMAIL' | translate }}" type="email" id="email" name="email" [(ngModel)]="dados.email">

                        </mat-form-field>

                    </form>

                    <div class="danger-baloon" id="logininvalido" *ngIf="dadosInvalidos == true">
                        <label style="color: red;" class="text-right">{{ 'TELALOGINRECUPERARSENHA.EMAILOUCPFINVALIDO' | translate }}</label></div>
                    <div class="row-button padding-10">

                        <button mat-flat-button class="btn-primary" (click)="esqueciSenha()">{{ 'TELALOGINRECUPERARSENHA.ENVIAR' | translate }}</button>
                        <span class="">  </span>
                        <button mat-button class="f-r" (click)="voltarLogin()">{{ 'TELALOGINRECUPERARSENHA.VOLTAR' | translate }}</button>

                    </div>

                </mat-card>

            </div>
</div>
<footer class="footer-faso-novo">
    <div class="telaGrande" 
            style="margin-right: 50px; font-weight:400;"> 
        <img src="/assets/build/img/microsoftLogo.png"
            class="logoRodapefooter">
    </div>
    <div class="telaGrande" 
            style="margin-right: 50px; font-weight:400; align-self: center;cursor: context-menu;"> 
        <a style="margin-right: 5px;">
            {{ 'TELALOGIN.SITESEGURO' | translate }}
        </a> 
    </div>
    <div>
        <a href="https://www.facebook.com/MedicinaParaVoce/posts/117102886621942" target="_blank">
            <i class="fa fa-facebook fa-fw fa-2x logoRodapefooter" 
                    style="color:#002e4b; cursor: pointer;"
                    click="abrirFacebook">
            </i>
        </a>
        <a href="https://www.youtube.com/watch?v=ePea_b-M85c&feature=emb_title" target="_blank">
            <i class="fa fa-youtube fa-fw fa-2x" 
                    style="color:rgb(255, 0, 0); cursor: pointer; margin-left: 20px;">
            </i>
        </a>
        <a href="https://www.instagram.com/medicinaparavoce/" target="_blank">
            <i class="fa fa-instagram fa-fw fa-2x" 
                    style="color:black;cursor: pointer; margin-left: 20px;">
            </i>
        </a>
        <a href="https://twitter.com/medicinaparavc/status/1249782319075995649" target="_blank">
            <i class="fa fa-twitter fa-fw fa-2x" 
                    style="cursor: pointer; margin-left: 20px;">
            </i>
        </a>
    </div>
    <div class="telaGrande"
            style="margin-right: 50px; font-weight:400; align-self: center; cursor: context-menu;">
        <a style="margin-right: 5px;">
            © 2020 - Faso Fábrica de Software
        </a>
    </div>
</footer>