import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Router } from '@angular/router';
import { AlertComponent } from 'src/app/alert/alert.component';
import { SalaModelview } from 'src/app/model/salas';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SalaService } from 'src/app/service/sala.service';
import { SpinnerService } from 'src/app/service/spinner.service';

@Component({
  selector: 'app-cadastrar-sala',
  templateUrl: './cadastrar-sala.component.html',
  styleUrls: ['./cadastrar-sala.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatLabel,
    MatFormFieldModule
  ]
})
export class CadastrarSalaComponent implements OnInit {

  constructor(
    private router: Router,
    private spinner: SpinnerService,
    private salaService: SalaService,
    private snackbar: AlertComponent,
    private localstorage: LocalStorageService
  ) { }

  objSala: SalaModelview = new SalaModelview;

  ngOnInit() {
    this.objSala.idSala = this.localstorage.idSala || null;
    if (this.objSala.idSala != null) {
      this.GetSala();
    }
  }

  async SalvarSala() {
    this.spinner.show();
    await this.salaService.SalvarSala(this.objSala).subscribe((ret) => {
      if (ret.ok) {
        this.snackbar.sucessoSnackbar('Sala salva com sucesso.')
        this.router.navigate(["/listagemsalas"])
      }

      else 
        this.snackbar.sucessoSnackbar(ret.mensagem);

      this.spinner.hide();
    }, () => {
      this.snackbar.sucessoSnackbar('Ocurreu um erro tentando salvar a sala.')
      this.spinner.hide();
    })
  }

  async GetSala() {
    this.spinner.show();
    await this.salaService.GetDadosSala(this.objSala.idSala!).subscribe((ret) => {
      this.objSala = ret;
      this.localstorage.idSala = null;
    }, () => {
      this.snackbar.sucessoSnackbar('Ocurreu um erro tentando carregar a sala.')
    })
    this.spinner.hide();
  }



}
