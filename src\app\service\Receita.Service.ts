import { tipoOrientacao } from './../model/itens';
import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from '@angular/common/http';
import { msgResposta } from "../model/retorno-resposta";
import { environment } from "src/environments/environment";
import { novaReceita } from "../model/receitas";
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class ReceitaService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) { }

    public SalvarNovaReceita(objReceita: novaReceita)  {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('receita', String(objReceita.receita));
        params = params.append('idPaciente', Number(objReceita.idPaciente));
        params = params.append('idMedico', Number(objReceita.idMedico));
        params = params.append('tipoOrientacao', Number(objReceita.tipoOrientacao));
        ;
        
        return this.http.post(environment.apiEndpoint + '/receita/AdicionarReceita', objReceita)
    }
    
    public GetReceitas(){
        this.spinner.show();
        return this.http.get<novaReceita[]>(environment.apiEndpoint +'/receita/GetReceitas')
    }
    
    public DeleteReceita(id:number){
        this.spinner.show();
        return this.http.post<msgResposta>(environment.apiEndpoint + '/receita/DeleteReceita', id)
    }
    
    public GetObjReceita(id: number){
        this.spinner.show();
        return this.http.post<novaReceita>(environment.apiEndpoint + '/receita/GetObjReceita', id)
    }
    
    public GetTipoOrientacao(){
        this.spinner.show();
        return this.http.get<tipoOrientacao[]>(environment.apiEndpoint + '/item/GetTipoOrientacao')
    }
}