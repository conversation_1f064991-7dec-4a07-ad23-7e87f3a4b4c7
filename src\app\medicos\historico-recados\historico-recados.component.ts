import { Component, OnInit } from '@angular/core';
import { MedicoService } from 'src/app/service/medico.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatDivider } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-historico-recados',
    templateUrl: './historico-recados.component.html',
    styleUrls: ['./historico-recados.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatDivider,
      MatIcon,
      MatFormField,
      MatLabel,
      NgxSmartModalModule,
      TranslateModule
    ]
})
export class HistoricoRecadosComponent implements OnInit {

  constructor(    
    private spinner: SpinnerService,
    private medicoService: MedicoService,
    public ngxSmartModalService: NgxSmartModalService,
    private usuarioLogadoService: UsuarioLogadoService

  ) { }
  DadosRecados:any = [];
  pesquisa: string = new Date().toLocaleDateString();
  dtaLinha: string= new Date().toLocaleDateString();
  // usuario: Usuario;
  DtaErrado: boolean = false;
  dta: string = ''
  DesRecadoDia: string = '';
  UsuarioRecado: string = '';
  Flgtable: boolean = false;
  DesDataDia?: string;
  FlgDtahoje?: boolean
  ngOnInit() {
    this.DesDataDia = this.pesquisa
    this.CarregaRecados()
  }
  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }

  CarregaRecados() {
    this.medicoService.getRecadoDia(this.usuarioLogadoService.getIdUsuarioAcesso(), this.usuarioLogadoService.getIdUltimaClinica(), 'Todos', this.pesquisa).subscribe((retorno) => {
      ;
      
      this.DadosRecados = retorno;
      if (this.DadosRecados.length > 0) {

        this.Flgtable = true;
      }
      else
        this.Flgtable = false;

      ;
      this.spinner.hide();
    })

  }

  MensagemdoDia(id:any) {
    var men = this.DadosRecados.filter((c:any) => c.idRecado == id)
    this.DesRecadoDia = men[0].recado;
    this.UsuarioRecado = men[0].usuarioGerador;


    this.medicoService.VisualizacaoRecadoDia(id, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      if (retorno) {
        this.CarregaRecados()
        this.ngxSmartModalService.getModal('mensagemDiaNova').open();
        this.spinner.hide();
      }
    })

  }

  ValidaDta(dta:any) {
    this.DtaErrado = false;

    var min = new Date('01/01/1753 12:00:00')
    var dtaAgora = new Date(dta)
    if (dta != "") {
      var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

      if (!patternValidaData.test(dta)) {
        this.DtaErrado = true;
      }
      if (dtaAgora < min) {
        this.DtaErrado = true;
        return;
      }
      else if (this.DtaErrado == false) {
        this.CarregaRecados()
        this.DesDataDia = this.pesquisa;
      }

    }
    else if (dta == '' || this.dta != '' || this.dta != null)
      this.CarregaRecados()
    else
      return;

  }
}
