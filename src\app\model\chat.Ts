import { RetornoPadraoApi } from './RetornoPadraoApi';

export class MensagemModel {
    idMensagem?: number | null;
    idDestinatario?: number | null;
    idRemetente?: number | null;
    mensagem?: string;
    dtaLido?: Date | null;
    dtaEnvio?: Date | null | string;
}

export class FiltroCarregaMensagem {
    idChat?: number;
    qtdInicio?: number;
    qtdFim?: number;
}

export class RetListaMensagem extends RetornoPadraoApi {
    listaMensagem?: MensagemModel[];
}

export class RetUsuarioChat extends RetornoPadraoApi {
    lsUsuarios?: UsuarioChat[];
}

export class UsuarioChat {
    idUsuario?: number;
    qtdMsgNLida?: number | null;
    nomeUsuario?: string;
    tipoUsuario?: string;
}