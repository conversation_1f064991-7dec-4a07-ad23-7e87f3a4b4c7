<div class="local-container">
  <div class="card main-card">
    <div class="card-header">
      <div class="header-left">
        <div class="icon-container">
          <span class="material-icons">near_me</span>
        </div>
        <h1 class="page-title">Adicionar Local</h1>
      </div>
      <button class="btn btn-link" onclick='history.go(-1)'>
        <span class="material-icons">arrow_back</span>
      </button>
    </div>

    <div class="col-md-12 col-sm-12 col-xs-12 dados">

      <div class="title-dados">
        <h5 style="padding-left: 5px; font-family: system-ui; font-weight: 500; font-size: 18px; color: #5260ff;">Dados
          do Local</h5>
      </div>

      <div class="col-md-12 col-sm-12 col-xs-12 principal-dados">

        <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0; display: flex; " id="perfil-forms">

          <div class="col-md-2 col-sm-12 col-xs-12 foto-locais" style="overflow-y: scroll;">
            <label for="imageperfilusuario" style="text-align: center;">
              <img src="{{ ImagemPessoa }}" class="img-local profile_img-upload" style="display: inline; cursor:pointer"
                alt="Foto de Perfil" title="{{'TELACADASTROUSUARIO.FOTODEPERFIL' | translate}}">
            </label>
          </div>

          <div class="col-md-10 col-sm-12 col-xs-12" style="align-self: flex-end; padding: 0;">
            <mat-form-field appearance="outline" style="width: 100%; font-size: 12px; padding: 0 5px;">
              <mat-label><span>Nome</span></mat-label>
              <input matInput placeholder="Digite o nome do local" [(ngModel)]="ObjLocal.nome">
            </mat-form-field>
          </div>

        </div>

        <div class="col-md-12 col-sm-12 col-xs-12 descricao-dados" id="perfil-forms">

          <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0; display: flex; flex-wrap: wrap;">

            <div class="col-md-6 col-sm-12 col-xs-12" style="padding: 0 5px;">
              <mat-form-field appearance="outline" style="font-size: 12px; width: 100%; padding: 0 5px;">
                <mat-label><span>Telefone</span></mat-label>
                <input matInput placeholder="Digite o telefone" type="tel" [(ngModel)]="ObjLocal.telefone"
                  mask="(00) 0000-0000">
              </mat-form-field>
            </div>
            
            <div class="form-group form-group-half">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput placeholder="Digite o email" type="email" [(ngModel)]="ObjLocal.email">
              </mat-form-field>
            </div>
          </div>

          <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0; display: flex; flex-wrap: wrap;">

            <div class="col-md-6 col-sm-12 col-xs-12" style="padding: 0; display: flex; flex-direction: row;">
              <div class="col-md-3 col-sm-12" style="padding: 5px;" id="buscar-locais">
                <ng-select [items]="dadosUF" style="font-size: 12px;"
                  placeholder="{{ 'TELACADASTROCLINICA.UF' | translate }}" bindLabel="siglasUf" bindValue="siglasUf"
                  name="UF" (change)="CidadePorUF()" [selectOnTab]="true"
                  notFoundText="{{'TELACADASTROCLINICA.UFNAOENCONTRADA' | translate}}" [(ngModel)]="Dados.uf">
                </ng-select>
              </div>
              <div class="col-md-3 col-sm-12 col-xs-12" style="padding: 5px; min-width: 6vmax; width: 80%;"
                id="buscar-locais">
                <ng-select [items]="dadosCidadeUf" style="font-size: 12px; width: 100%;"
                  placeholder="{{ 'TELACADASTROCLINICA.MUNICIPIO' | translate }}" bindLabel="nmeCidade"
                  bindValue="idCidade" name="Municipio" [selectOnTab]="true"
                  notFoundText="{{'TELACADASTROCLINICA.UFNAOENCONTRADA' | translate}}"
                  [(ngModel)]="ObjLocal.endereco.idCidade">
                </ng-select>
              </div>
            </div>
            
            <div class="form-group form-group-half">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Site</mat-label>
                <input matInput placeholder="URL" [(ngModel)]="ObjLocal.site">
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção de endereço -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Endereço</h2>
        </div>
        <div class="section-content">
          <div class="form-row">
            <div class="form-group form-group-large">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Rua</mat-label>
                <input matInput placeholder="Entre apenas com o nome da rua" [(ngModel)]="ObjLocal.endereco.rua">
              </mat-form-field>
            </div>

            <div class="col-md-6 col-sm-12 col-xs-12" style="padding: 0;">
              <mat-form-field appearance="outline" style="width: 100%; font-size: 12px; padding: 0 5px;">
                <mat-label><span>Número</span></mat-label>
                <input matInput [(ngModel)]="numeroResidencia" mask="00000a" maxlength="5" dir="ltr">
              </mat-form-field>
            </div>

          </div>

          <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0; display: flex; flex-wrap: wrap;">

            <div class="col-md-6 col-sm-12 col-xs-12" style="padding: 0 5px;">
              <mat-form-field appearance="outline" style="width: 100%; font-size: 12px; padding: 0 5px;">
                <mat-label><span>CEP</span></mat-label>
                <input matInput placeholder="Informe o CEP" mask="00000-0000a" maxlength="9" dir="ltr"
                  [(ngModel)]="ObjLocal.endereco.cep">
              </mat-form-field>
            </div>
            
            <div class="form-group form-group-half">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Bairro</mat-label>
                <input matInput placeholder="Informe o bairro" [(ngModel)]="ObjLocal.endereco.bairro">
              </mat-form-field>
            </div>
          </div>
          <div class="col-md-12 col-sm-12 col-xs-12" style="padding: 0 5px;">
            <!-- <ckeditor placeholder="Observações" [editor]="Editor" [(ngModel)]="observacao" #obsEditor></ckeditor> -->

            <!-- <ckeditor [editor]="Editor" [(ngModel)]="observacao" [config]="config"></ckeditor> -->

            <textarea [(ngModel)]="observacao" style="width: 100%;"></textarea>

          </div>

        </div>

      </div>

      <div class="col-xs-12 col-md-12 col-sm-12 row" style="justify-content: flex-end; padding: 0; margin: 20px 0;">
        <div class="div-add">
          <button *ngIf="flgUser" class="btn-add" (click)="salvar()">Salvar</button>
        </div>

        <div class="div-add">
          <button class="btn-add" (click)="limpar()">Limpar</button>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <button *ngIf="flgUser" class="btn btn-primary" (click)="salvar()">
        <span class="material-icons">save</span>
        Salvar
      </button>
      <button class="btn btn-outline" (click)="limpar()">
        <span class="material-icons">refresh</span>
        Limpar
      </button>
    </div>
  </div>
</div>