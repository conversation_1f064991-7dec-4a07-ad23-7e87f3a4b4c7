export class flgConsultasRelatorio {

    idPadraoRelatorio?: number;
    nomePadraoRelatorio?: string | null;
    flgDtaConsultas?: boolean | null;
    flgPaciente?: boolean | null;
    flgMedico?: boolean | null;
    flgTipoAgendamento?: boolean | null;
    flgTipoPagamento?: boolean | null;
    flgCodConvenio?: boolean | null;
    flgConvenio?: boolean | null;
    flgValorConsulta?: boolean | null;
    flgTempo?: boolean | null;
    flgRetorno?: boolean | null;
    flgCid?: boolean | null;

    flgStatus?: boolean | null;
    flgObservacaoCancelamento?: boolean | null;
    flgObservacaoAvaliacao?:boolean | null;

    Medico?: flgMedicoRelatorio | null;
    Paciente?: flgPacientesRelatorio | null;
    DtaInicio?: Date | null;
    DtaFim?: Date | null;
    idClinica?: number | null;
    idUsuario?: number | null;
    FlgCheckMedico?: boolean | null;
    FlgCheckPaciente?: boolean | null;
    FlgCheckConsultas?: boolean | null;
}
export class flgMedicoRelatorio {


    flgNome?: boolean | null;
    flgEmail!: boolean | null;
    flgDtanascimento?: boolean;
    flgDtaCadastro?: boolean | null;
    flgSexo?: boolean | null;
    flgCpf?: boolean | null;
    flgTel?: boolean | null;
    flgTelmovel?: boolean | null;
    flgTelComercial?: boolean | null;
    flgCRM?: boolean | null;
    flgRua?: boolean | null;
    flgNumero?: boolean | null;
    flgComplement?: boolean | null;
    flgUf?: boolean | null;
    flgCidade?: boolean | null;
    flgBairro?: boolean | null;
    flgCep?: boolean | null;
    flgValorConsulta?: boolean | null;
    flgCodConvenio?: boolean | null;
    flgvlRetidoClinica?: boolean | null;
}

export class flgPacientesRelatorio {

    flgNome?: boolean;
    flgEmail?: boolean;
    flgDtanascimento?: boolean;
    flgDtaCadastro?: boolean;
    flgSexo?: boolean;
    flgCpf?: boolean;
    flgTel?: boolean;
    flgTelmovel?: boolean;
    flgTelComercial?: boolean;
    flgRua?: boolean;
    flgNumero?: boolean;
    flgComplement?: boolean;
    flgUf?: boolean;
    flgCidade?: boolean;
    flgBairro?: boolean;
    flgCep?: boolean;
    flgContaAtiva?: boolean;
    flgObsInativacao?: boolean;
    flgNaturalidade?: boolean;
    flgNascionalidade?: boolean;
    flgProcedencia?: boolean;
    flgProfissao?: boolean;
    flgPlanoSaude?: boolean;
    flgConvenio?: boolean;
    flgMatricula?: boolean;

}


