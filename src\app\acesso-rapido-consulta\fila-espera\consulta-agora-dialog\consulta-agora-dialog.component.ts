// consulta-agora-dialog.component.ts
import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-consulta-agora-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatDividerModule
  ],
  template: `
    <div class="background-email" style="display: flex;">
      <div>
        <img src="assets/build/img/Telemedicina-15.jpg" class="img-responsive" style="max-width: 100%;">
      </div>
    </div>

    <div class="modal-info">
      <h1 class="little-title fw-700" style="padding-left: 3vh; text-align: center">
        O médico entrou na sala
      </h1>
    </div>
    
    <mat-divider></mat-divider>
    
    <div class="col-12 row-button text-center p-20" mat-dialog-actions>
      <button mat-flat-button (click)="iniciarConsulta()" class="btn-primary">
        Iniciar consulta
      </button>
      <button mat-flat-button (click)="cancelar()" class="btn-primary" style="margin-left: 10px;">
        Cancelar
      </button>
    </div>
  `,
  styleUrls: ['./consulta-agora-dialog.component.scss']
})
export class ConsultaAgoraDialogComponent {

  constructor(
    private dialogRef: MatDialogRef<ConsultaAgoraDialogComponent>
  ) { }

  iniciarConsulta(): void {
    this.dialogRef.close('iniciar');
  }

  cancelar(): void {
    this.dialogRef.close('cancelar');
  }
}