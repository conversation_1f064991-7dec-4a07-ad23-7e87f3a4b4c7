<app-modal-template [cabecalho]="'Lista Procedimentos'" (fechar)="FecharModal()">
</app-modal-template>
    <section>
        <header>
            <div class="ConteudoCabecalho">
                <!-- <div class="CaixaMatInput">
                    <mat-form-field appearance="outline" style="width: 100%;">
                        <mat-label>Buscar</mat-label>
                        <input matInput placeholder="Procedimento">
                    </mat-form-field>
                </div> -->
                <div class="CaixaBtn" (click)="AbrirModalGuiaTiss()">
                    <button style="width: 200px;">
                        Criar Guia-Tiss
                    </button>
                </div>
                <div class="CaixaBtn" (click)="AbrirProcedimento()">
                    <button>
                        Adicionar
                    </button>
                </div>
            </div>
        </header>
        <main>
            <div class="TabelaPadrao">
                <table class="w3-table-all">
                    <colgroup>
                        <col style="width:30%">
                        <col style="width:50%">
                        <col style="width:10%">
                        <col style="width:10%">
                    </colgroup>

                    <tr class="Cabecalho">
                        <th class="HeaderLeft">
                            <div class="CaixaHeader">
                                <span class="TextoHeader">
                                    Procedimento
                                </span>
                            </div>

                        <th class="Header">
                            <span>
                                Fatura
                            </span>

                        <th class="Header">
                            <span>
                                Total
                            </span>

                        <th class="HeaderRight Header">
                    </tr>

                    <tbody *ngIf="listaProcedimento">
                        <tr *ngFor="let item of listaProcedimento" class="Corpo">
                            <td>{{item.desProcedimento}}</td>

                            <td class="TextoCorpo" (click)="AbrirProcedimento(item.idProcedimento)">{{item.desFatura}}</td>

                            <td class="TextoCorpo">
                                {{item.vlrM2filme! 
                                + item.vlrCustoOp!
                                + item.vlrHonorarios!
                                + item.vlrParticular!
                                + item.vlrConvenio!}}</td>

                            <td class="CaixaAcoes">
                                <mat-checkbox 
                                (change)="AdicionarProcedimento($event, item.idProcedimento!)">
                                </mat-checkbox>
                        </tr>
                    </tbody>

                    <tbody *ngIf="listaProcedimento.length == 0">
                        <td class="TextoCorpo" colspan="4" style="cursor: not-allowed;">Nenhum procedimento encontrado.
                        </td>
                    </tbody>
                </table>
            </div>
        </main>
        <footer>

        </footer>
    </section>