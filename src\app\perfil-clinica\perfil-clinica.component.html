<div class="form-container mother-div">
  <!-- Card principal -->
  <div class="card main-card">
    <div class="card-header">
      <div class="header-left">
        <div class="icon-container">
          <span class="material-icons">business</span>
        </div>
        <h1 class="page-title">{{ 'TELACADASTROCLINICA.PERFILDACLINICA' | translate }}</h1>
      </div>
      <button class="btn btn-link" onclick='history.go(-1)'>
        <span class="material-icons">arrow_back</span>
      </button>
    </div>

    <div class="card-body">
      <!-- Seção do perfil e imagem -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Dados da Clínica</h2>
        </div>
        <div class="section-content">
          <!-- Perfil e Nome -->
          <div class="profile-section">
            <div class="profile-image-wrapper">
              <label for="imageperfilClinica" class="profile-image-label">
                <img [src]="imagemClinica" class="profile-img" alt="Logo de Perfil"
                  title="{{'TELACADASTROCLINICA.LOGODOPERFIL' | translate}}">
                <div class="profile-image-overlay">
                  <span class="material-icons">photo_camera</span>
                </div>
              </label>
              <input type="file" id="imageperfilClinica" (change)="AlterarImagemClinica($event)"
                (click)="LimpaCampoFile()">
              <input type="text" style="display: none;" id="Logo" name="Logo" [(ngModel)]="dados.logo">
            </div>
            <div class="form-group full-width">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nome da Clínica</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.NOME' | translate }}" id="nome"
                  name="Nome" required maxlength="100" (keyup)="mascaraText($event, 'NomeCli')"
                  (change)="mascaraText($any($event), 'NomeCli')" [(ngModel)]="dados.nomeClinica">
                <mat-error *ngIf="Nome.invalid">{{getErrorMessageNome() | translate }}</mat-error>
              </mat-form-field>
            </div>
          </div>

          <!-- Primeira linha -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Data da Licença</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.DATADALICENCA' | translate }}"
                  name="Data da Licença" id="dataLicenca" (keyup)="mascaraData($event)" [(ngModel)]="dados.dtaLicenca"
                  maxlength="10" (blur)="ValidaDta($any($event.target).value)">
              </mat-form-field>
              <div class="error-message" *ngIf="Dtalice">
                {{ 'TELACADASTROCLINICA.DATAINVALIDA' | translate }}
              </div>
            </div>

            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Valor da Consulta</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROMEDICO.VALORCONSULTA' | translate }}" name="valor"
                  id="valorConsulta" [(ngModel)]="dados.valorConsulta" (keypress)="mascaraValor($event)"
                  (change)="mascaraValor($any($event))" (keyup)="mascaraValor($event)" maxlength="15">
              </mat-form-field>
            </div>
          </div>

          <!-- Segunda linha -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CNPJ</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CNPJ' | translate }}" id="cpfcnpj" type="text"
                  name="cnpj" (change)="ValidaCPFCNPJ(CNPJInputtt.value)" #CNPJInputtt
                  (keypress)="mascaraCnpj('##.###.###/####-##','###.###.###-##', $event)"
                  (keyup)="mascaraCnpj('##.###.###/####-##','###.###.###-##', $event)" maxlength="18" required
                  [(ngModel)]="dados.cnpj">
              </mat-form-field>
              <div class="error-message" *ngIf="campoCPFInvalido">CPF inválido</div>
              <div class="error-message" *ngIf="campoCNPLInvalido">CNPJ inválido</div>
              <div class="error-message" *ngIf="campoCPFVazil && !campoCPFInvalido && !campoCNPLInvalido">
                Esse campo precisa ser preenchido
              </div>
            </div>

            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Caracterização</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CARACTERIZACAO' | translate }}"
                  id="caracterizacao" name="Caracterizacao" maxlength="100" (keyup)="mascaraText($event, 'Caract')"
                  [(ngModel)]="dados.caracterizacao">
              </mat-form-field>
            </div>
          </div>

          <!-- Terceira linha -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>ISS/CCM</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.ISS/CCM' | translate }}" name="iss"
                  (keypress)="mascaraIss('#.###.###-#', $event)" mask="0.000.000-0" maxlength="11"
                  [(ngModel)]="dados.iss">
              </mat-form-field>
            </div>

            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CRM</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CRM' | translate }}" name="crm"
                  (keypress)="mascaraCrm('#######', $event)" mask="0000000" maxlength="7" [(ngModel)]="dados.crm">
              </mat-form-field>
            </div>
          </div>

          <!-- Quarta linha -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CNES</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CNES' | translate }}" name="cnes" mask="00000000"
                  maxlength="8" [(ngModel)]="dados.cnes">
              </mat-form-field>
            </div>

            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Site</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.WEBSITE' | translate }}" name="website"
                  maxlength="100" [(ngModel)]="dados.website">
              </mat-form-field>
            </div>
          </div>

          <!-- Quinta linha -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.EMAIL' | translate }}" type="email" name="Email"
                  (blur)="ValidarEmail(emailClinicainput.value)" required #emailClinicainput
                  [(ngModel)]="dados.emailClinica">
              </mat-form-field>
              <div class="error-message" *ngIf="campoEmailInvalido">{{mensagemErroEmail}}</div>
            </div>
          </div>

          <!-- Sexta linha -->
          <div class="form-row">
            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Telefone</mat-label>
                <input matInput #telefoneInput placeholder="{{ 'TELACADASTROCLINICA.TELEFONE' | translate }}" name="Telefone"
                  (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)"
                  (blur)="ValidaTelefone(telefoneInput.value)" maxlength="15" minlength="14"
                  [(ngModel)]="dados.telefone">
              </mat-form-field>
              <div class="error-message" *ngIf="TelVal">
                {{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}
              </div>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Celular</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CELULAR' | translate }}" name="Telefone Movel"
                  id="TelefoneMovel" (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)" #telefoneMovelinputt
                  (blur)="ValidaTelefoneMovel(telefoneMovelinputt.value)" maxlength="15" minlength="14"
                  [(ngModel)]="dados.telefoneMovel">
              </mat-form-field>
              <div class="error-message" *ngIf="TelMovVal">
                {{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}
              </div>
            </div>

            <div class="form-group col-md-4">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Tel. Comercial</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.TELEFONECOMERCIAL' | translate }}"
                  #telefoneComercial
                  name="Telefone Comercial" (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)"
                  id="TelefoneComercial" (blur)="ValidaTelefoneComercial(telefoneComercial.value)" maxlength="15"
                  minlength="14" [(ngModel)]="dados.telefoneComercial">
              </mat-form-field>
              <div class="error-message" *ngIf="TelComVal">
                {{ 'TELACADASTROCLINICA.TELEFONEINVALIDA' | translate }}
              </div>
              <div class="error-message" *ngIf="TelComValVasil && !TelComVal">
                {{ 'TELACADASTROCLINICA.ERROCAMPO' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Seção de endereço -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">Endereço</h2>
        </div>
        <div class="section-content">
          <!-- Primeira linha de endereço -->
          <div class="form-row">
            <div class="form-group col-md-6">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Rua</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.RUA' | translate }}" name="Rua"
                  [(ngModel)]="dados.rua">
              </mat-form-field>
            </div>

            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Nº</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.NUMERO' | translate }}" name="n°" maxlength="10"
                  (keyup)="mascaraNumeros($event)" [(ngModel)]="dados.numero">
              </mat-form-field>
            </div>

            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Complemento</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.COMPLEMENTO' | translate }}" name="Complemento"
                  [(ngModel)]="dados.complemento">
              </mat-form-field>
            </div>
          </div>

          <!-- Segunda linha de endereço -->
          <div class="form-row">
            <div class="form-group col-md-3">
              <ng-select [items]="dadosUF" 
                placeholder="{{ 'TELACADASTROCLINICA.UF' | translate }}"
                bindLabel="siglasUf" 
                bindValue="siglasUf" 
                name="UF" 
                (change)="CidadePorUF()" 
                [selectOnTab]="true"
                notFoundText="{{'TELACADASTROCLINICA.UFNAOENCONTRADA' | translate}}" 
                [(ngModel)]="dados.uf"
                class="modern-select">
              </ng-select>
            </div>

            <div class="form-group col-md-3">
              <ng-select [items]="dadosCidadeUf" 
                placeholder="{{ 'TELACADASTROCLINICA.MUNICIPIO' | translate }}"
                bindLabel="nmeCidade" 
                bindValue="idCidade" 
                name="Municipio" 
                [selectOnTab]="true"
                notFoundText="{{'TELACADASTROCLINICA.UFNAOENCONTRADA' | translate}}" 
                [(ngModel)]="dados.idCidade"
                class="modern-select">
              </ng-select>
            </div>

            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Bairro</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.BAIRRO' | translate }}" name="Bairro"
                  [(ngModel)]="dados.bairro">
              </mat-form-field>
            </div>

            <div class="form-group col-md-3">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>CEP</mat-label>
                <input matInput placeholder="{{ 'TELACADASTROCLINICA.CEP' | translate }}" name="CEP" mask="00000-000"
                  maxlength="9" [(ngModel)]="dados.cep">
              </mat-form-field>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <button class="btn btn-success" (click)="Submit()">
        {{ 'TELACADASTROCLINICA.SALVAR' | translate }}
      </button>
    </div>
  </div>
</div>

<!-- Modal de corte de imagem -->
<ngx-smart-modal #ModalFoto identifier="ModalFoto" customClass="nsm-centered medium-modal">
  <div class="modal-container">
    <div class="modal-header">
      <h2 class="modal-title">{{ 'TELAPERFIL.FOTODEPERFIL' | translate }}</h2>
    </div>

    <div class="modal-body">
      <div class="image-controls">
        <div class="control-group">
          <button mat-flat-button (click)="rotateLeft()" class="control-btn">
            <span class="material-icons">rotate_left</span>
            {{ 'TELAPERFIL.GIRARAESQUERDA' | translate }}
          </button>
          <button mat-flat-button (click)="rotateRight()" class="control-btn">
            <span class="material-icons">rotate_right</span>
            {{ 'TELAPERFIL.GIRARADIREITA' | translate }}
          </button>
        </div>

        <div class="control-group">
          <button mat-flat-button (click)="flipHorizontal()" class="control-btn">
            <span class="material-icons">flip</span>
            {{ 'TELAPERFIL.VIRARHORIZONTALMENTE' | translate }}
          </button>
          <button mat-flat-button (click)="flipVertical()" class="control-btn">
            <span class="material-icons">flip</span>
            {{ 'TELAPERFIL.VIRARVERTIVALEMENTE' | translate }}
          </button>
        </div>
      </div>
      
      <div class="cropper-container">
        <image-cropper
          [imageChangedEvent]="imageChangedEvent"
          [maintainAspectRatio]="true"
          [aspectRatio]="1"
          [onlyScaleDown]="true"
          [roundCropper]="true"
          outputType="base64"
          (imageCropped)="imageCropped($event)"
          (imageLoaded)="imageLoaded()"
          (cropperReady)="cropperReady()"
          (loadImageFailed)="loadImageFailed()"
          [style.display]="showCropper ? null : 'none'"
          [alignImage]="'center'"
          [transform]="transform">
        </image-cropper>
      </div>
    </div>

    <div class="modal-footer">
      <button mat-flat-button (click)="CortarImagem()" class="btn-primary">
        <span class="material-icons">content_cut</span>
        {{ 'TELAPERFIL.CORTAR' | translate }}
      </button>
    </div>
  </div>
</ngx-smart-modal>