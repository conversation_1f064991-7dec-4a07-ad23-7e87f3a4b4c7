import { LocalService } from './../../service/local.service';
import { LocalModelView } from './../../model/local';
import { Component, OnInit } from '@angular/core';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
// import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';
// import ClassicEditor from 'ckeditor-build-b64imageupload';
import { UfClass } from 'src/app/Util/UFClass';
import { SelectClinica } from 'src/app/model/clinica';
import { selectEstado } from 'src/app/model/endereco';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { CidadeService } from 'src/app/service/cidade.service';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { msgResposta } from 'src/app/model/retorno-resposta';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-adicionar-locais',
    templateUrl: './adicionar-locais.component.html',
    styleUrls: ['./adicionar-locais.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCard,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      NgSelectModule,
      CKEditorModule
    ]
})
export class AdicionarLocaisComponent implements OnInit {
  
  constructor(    
    private spinner: SpinnerService,  
    private LocalService: LocalService,
    private usuarioLogadoService: UsuarioLogadoService,
    private cidadeService: CidadeService,
    private localStorageService: LocalStorageService,
    // public snackBar: MatSnackBar
    private snackBarAlert: AlertComponent,

  ) { }
  Dados: any;
  dadosCidadeUf: any;
  dadosCidade: any;
  dadosUF = UfClass;
  
  observacao?: string;
  
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  action: boolean = true;
  actionButtonLabel: string = 'Fechar';
  
  // Editor = ClassicEditor;
  config = {
    toolbar: ['heading', '|', 'undo', 'redo', '|', "outdent", "indent" ,'bold', 'italic', 'underline', 'strikethrough', 'subscript', 'superscript', 'link', 'blockQuote', 'alignment:left', 'alignment:center', 'alignment:right', 'alignment:justify', 'imageInsert' ] // , 'removeFormat'],
  };

  flgUser: boolean = false;
  ImagemPessoa: any = "assets/build/img/userdefault.png";

  retorno?: msgResposta;

  ObjLocal = new LocalModelView();
  numeroResidencia?: string;
  
  idObjLocal?: number;

  ListaEstado: selectEstado[] = [];
  ListaClinicas: SelectClinica[] = [];

  ngOnInit() {

    this.verificaUsuario();
    this.localStorageService.clearByName("idClinica")
    this.Dados = [];
    
    this.idObjLocal = this.localStorageService.idLocal;

    if(this.idObjLocal != null && this.idObjLocal > 0 ){
      
      this.LocalService.GetDadosLocal(this.idObjLocal).subscribe((retorno) => {
        this.ObjLocal = retorno;
        this.numeroResidencia = this.ObjLocal.endereco.numero;
        this.observacao = this.ObjLocal.observacaoLocal;
        this.Dados.uf = this.ObjLocal.endereco.siglasUf;
        this.Dados.idCidade = this.ObjLocal.endereco.idCidade;
        this.CidadePorUF();
        this.spinner.hide();
      }, err =>{
        console.error(err)
        this.spinner.hide();
      })
    }

    this.localStorageService.idLocal = null;

  }

  verificaUsuario(){
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM || this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.flgUser = true;
    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente || this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
      this.flgUser = false;
    }
  }

  public CidadePorUF() {
    try {
      this.cidadeService.getCidades().then((retornaCidade) => {
        this.dadosCidade = retornaCidade;
        this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == this.Dados.uf);
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Erro ao carregar Cidade')
        this.spinner.hide();
      })

    } catch (error) {
      this.snackBarAlert.falhaSnackbar('Erro ao carregar Cidade')
      this.spinner.hide();

    }
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  //   else if (value == false) {
  //      ''
  //   }
  // }

  verificaSalvamento() {
    // verifica se elementos estão vazios:
    if (this.numeroResidencia != null){
      this.ObjLocal.endereco.numero = this.numeroResidencia;
    }
    if (this.ObjLocal.idClinica == null) {
      this.ObjLocal.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
    }
    if (this.ObjLocal.nome == '') {
      this.snackBarAlert.falhaSnackbar('Preencha o nome do \b Local');
      return false;
    }
    if (this.ObjLocal.endereco.rua == '') {
      this.snackBarAlert.falhaSnackbar('Preencha o campo \b Rua');
      return false;
    }
    if (this.ObjLocal.endereco.numero == null) {
      this.snackBarAlert.falhaSnackbar('Preencha o campo \b número');
      return false;
    }
    if (this.ObjLocal.endereco.cep == '') {
      this.snackBarAlert.falhaSnackbar('Preencha o campo \b Observaçoes ');
      return false;
    }
    if (this.ObjLocal.endereco.idCidade == null) {
      this.snackBarAlert.falhaSnackbar('Preencha o campo \b Cidade  ');
      return false;
    }
    this.ObjLocal.observacaoLocal = this.observacao;
    return true;
  }

  public salvar() {
    if (this.verificaSalvamento()) {  
      this.LocalService.SalvarLocal(this.ObjLocal).subscribe(() => {
        this.snackBarAlert.sucessoSnackbar("Local salvo com sucesso.")
        this.spinner.hide();
      })
    this.limpar()
    }
  }

  public limpar(){
    this.ObjLocal = new LocalModelView();
    this.observacao = "";
    this.numeroResidencia = '';
    this.Dados.uf = ''; 
    this.localStorageService.idMedico = "";
  }
}
