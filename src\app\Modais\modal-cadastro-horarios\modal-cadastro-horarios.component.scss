.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
        color: #279EFF;
        font-weight: 700;
        margin: 0;
    }

    .close-button {
        color: rgba(0, 0, 0, 0.54);
    }
}
.modal-body{
    padding: 10px 024px;
    text-align: center;
    background-color: #fff;
    height: 56vh;
    overflow: scroll;
}
.formulario-agendamento {
    width: 100%;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    @media (max-width: 600px) {
        grid-template-columns: 1fr;
    }
}

.observacao-field {
    width: 100%;
    margin-top: 16px;
}

mat-dialog-content {
    min-width: 500px;
    max-width: 800px;

    @media (max-width: 600px) {
        min-width: 100%;
    }
}

mat-dialog-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 16px;
}
.modal-footer{
    margin-top: 35px ;
}