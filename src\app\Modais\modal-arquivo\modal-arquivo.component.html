<div class="modal-container">
    <!-- Cabeçalho -->
    <div class="modal-header">
        <h2 class="modal-title">
            <mat-icon>{{ temArquivo && !modoUpload ? 'description' : 'upload_file' }}</mat-icon>
            {{ temArquivo && !modoUpload ? 'Visualizar Documento' : 'Upload de Documento' }}
        </h2>
        <button class="btn-close" (click)="cancelar()">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <!-- Corpo -->
    <div class="modal-body">
        <!-- Modo visualização -->
        <div *ngIf="temArquivo && !modoUpload" class="document-preview">
            <div class="document-info">
                <mat-icon>{{ obterIconeArquivo() }}</mat-icon>
                <div class="info-text">
                    <h3>{{ data.nomeArquivo }}</h3>
                    <p>{{ formatarTipoArquivo(data.tipoArquivo) }}</p>
                </div>
            </div>

            <div class="preview-container">
                <!-- Imagem -->
                <img *ngIf="ehImagem()" [src]="'data:' + data.tipoArquivo + ';base64,' + data.arquivo"
                    class="img-preview" alt="Visualização da imagem">

                <!-- PDF -->
                <iframe *ngIf="data.tipoArquivo === 'application/pdf'" [src]="obterUrlSegura()" class="pdf-preview"
                    title="Visualização do PDF"></iframe>

                <!-- Outros arquivos -->
                <div *ngIf="!ehImagem() && data.tipoArquivo !== 'application/pdf'" class="generic-preview">
                    <mat-icon class="file-icon">{{ obterIconeArquivo() }}</mat-icon>
                    <p>Este tipo de arquivo não pode ser visualizado</p>
                </div>
            </div>
        </div>

        <!-- Modo upload -->
        <div *ngIf="modoUpload" class="upload-area" [class.drag-over]="dragOverActive"
            (dragover)="onDragOver($event)" (dragleave)="onDragLeave()" (drop)="onDrop($event)"
            (click)="selecionarArquivo()">
            <input type="file" #fileInput hidden (change)="onFileSelected($event)">
            <mat-icon class="upload-icon">cloud_upload</mat-icon>
            <h3>Selecione um arquivo ou arraste para esta área</h3>
            <p>Formatos suportados: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</p>
        </div>

        <!-- Progress bar e mensagens de erro -->
        <mat-progress-bar *ngIf="uploading" mode="indeterminate"></mat-progress-bar>
        <div *ngIf="erro" class="alert alert-danger">{{ mensagemErro }}</div>
    </div>

    <!-- Rodapé -->
    <div class="modal-footer">
        <!-- Modo visualização -->
        <ng-container *ngIf="temArquivo && !modoUpload">
            <button *ngIf="data.flgEditaArquivo" class="btn btn-secondary" (click)="iniciarEdicao()">
                <mat-icon>edit</mat-icon> Substituir
            </button>
            <button *ngIf="data.flgRemocaoArquivo" class="btn btn-danger" (click)="excluir()">
                <mat-icon>delete</mat-icon> Excluir
            </button>
            <button class="btn btn-primary" (click)="salvar()">
                <mat-icon>check</mat-icon> Concluir
            </button>
        </ng-container>

        <!-- Modo upload -->
        <ng-container *ngIf="modoUpload">
            <button class="btn btn-outline-secondary" (click)="cancelar()">
                <mat-icon>close</mat-icon> Cancelar
            </button>
            <button class="btn btn-primary" [disabled]="!temArquivo || uploading" (click)="salvar()">
                <mat-icon>save</mat-icon> Salvar
            </button>
        </ng-container>
    </div>
</div>