import { HttpInter<PERSON>, HttpRequest, HttpHandler, HttpEvent, HttpResponse } from '@angular/common/http';
import { Observable, tap } from "rxjs";
// import 'rxjs/add/operator/do';
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { environment } from 'src/environments/environment';
import { LocalStorageService } from './LocalStorageService';

@Injectable({
    providedIn: 'root'
})
export class AuthInterceptor implements HttpInterceptor {

    constructor(
        private router: Router,
        private localStorageService: LocalStorageService) { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {

        if (req.headers.get('No-Auth') == "True")
            return next.handle(req.clone());

        if (this.localStorageService.token || '' != '') {
            const clonedreq = req.clone({
                headers: req.headers.set("Authorization", "Bearer " + this.localStorageService.token)
            });

            // return next.handle(clonedreq)
            //     .do(
            //         succ => { },
            //         err => {
            //             if (err.status === 401 || err.status === 403)
            //             {
            // 				this.localStorageService.clear();
            // 				this.router.navigate(['/login']);
            // 			}

            // 			if (err.status == 0)
            // 				console.error(`falha na comunicação api: ${environment.apiEndpoint}`)
            //         }
            //     );

            return next.handle(clonedreq).pipe(
                tap(event => {
                    if (event instanceof HttpResponse) {



                        if (event.status === 401 || event.status === 403) {
                            this.localStorageService.clear();
                            this.router.navigate(['login']);
                        }

                        if (event.status == 0)
                            console.error(`falha na comunicação api: ${environment.apiEndpoint}`)
                    }
                }
                    , err => {
                        if (err.status === 401 || err.status === 403) {
                            this.localStorageService.clear();
                            localStorage.clear();
                            sessionStorage.clear();
                            this.router.navigate(['login']);
                        }
                        ;
                    }
                )

            );
        }
        else {
            // localStorage.clear();
            // sessionStorage.clear();
            // this.router.navigate(['login'])
            return next.handle(req.clone());
        }
    }
}
