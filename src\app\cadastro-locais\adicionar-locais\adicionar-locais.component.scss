.principal {
    background-color: #5260ff12; 
    border-radius: 10px;
    padding: 5px 10px;
    border: none;
    box-shadow: none;
}
.icon-voltar {
    color: #5260ff;
    padding-right: 5px;
    margin-bottom: -6px;
}
.voltar-text {
    color: #5260ff;
    font-weight: 500;
    font-size: 15px;
    font-family: system-ui;
    padding: 0 5px;
    text-transform: uppercase;
    background-color: #5261ff00;
}
.title-page {
    font-weight: 500;
    color: #5260ff;
    font-size: 20px;
    text-transform: uppercase;
    font-family: system-ui;
    padding: 0;
    align-self: center;
    margin: 0;
    padding-left: 5px;
}
.dados {
    border: none;
    padding: 0;
}
.principal-dados {
    padding: 0;
}
.foto-locais {
    padding: 0;
    align-self: center;
    padding-left: 5px;
}
.img-local {
    width: 6vw;
}

.descricao-dados {
    padding: 0;
    align-self: center;
}
.div-add {
    padding: 0 5px;
}
.btn-add {
    height: 35px;
    border: none;
    border-radius: 5px;
    padding: 0 20px;
    color: #fff;
    background-color: #5260ff;
}

//ckeditor
:host ::ng-deep .ck-editor__editable_inline {
    min-height: 25vh;
    height:     80%;
    max-height: 80vh;
}
mat-form-field{
    margin-bottom: 20px !important;
    margin-left: 0 5px ;
}/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57;        /* Verde Saúde */
$primary-light: #A3D9B1;        /* Verde claro suavizado */
$primary-dark: #1F5F3D;         /* Verde escuro */
$secondary-color: #F4F4F9;      /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF;      /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5;       /* Cinza médio para hover/active */
$accent-color: #4ECDC4;         /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B;          /* Vermelho Pastel */
$success-color: #4CAF50;        /* Verde para sucesso */
$pending-color: #FFC107;        /* Amarelo para pendente */
$text-primary: #333333;         /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280;       /* Cinza médio */
$border-color: #E5E7EB;         /* Bordas suaves */
$bg-color: #F9FAFB;             /* Fundo geral suave */
$card-bg: #FFFFFF;              /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}

/* Container principal */
.local-container {
  padding: 24px;
  background-color: $bg-color;
  height: 85vh;
  overflow-y: hidden;
  
  @media (max-width: 768px) {
    padding: 16px;
  }
}

/* Cards */
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: 1px solid $border-color;
  margin-bottom: 24px;
  overflow: hidden;
  height: 100%;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
  overflow-y: hidden;
  flex: 1;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* Headers */
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

/* Sections */
.section {
  height: 350px;
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 0px 20px;
}

/* Profile/Place Section */
.place-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.place-photo {
  flex: 0 0 80px;
}

.place-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid $primary-light;
  cursor: pointer;
}

.place-name {
  flex: 1;
  min-width: 250px;
}

/* Form Fields */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  gap: 16px;
}

.form-group {
  flex: 1;
  min-width: 250px;
}

.form-group-half {
  flex: 1;
  min-width: 250px;
}

.form-group-small {
  flex: 0 0 120px;
}

.form-group-large {
  flex: 2;
  min-width: 300px;
}

.full-width {
  width: 100%;
}

/* Custom Location Field */
.location-fields {
  display: flex;
  gap: 16px;
}

.uf-field {
  width: 30%;
}

.city-field {
  width: 70%;
}

/* Material Form Field Overrides */
:host ::ng-deep {
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-label {
    color: $text-secondary;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
    color: $primary-color;
  }
  
  .mat-select-value {
    color: $text-primary;
  }
  
  .mat-select-arrow {
    color: $primary-color;
  }
  
  .mat-input-element:disabled {
    color: $text-secondary;
  }
}

/* ng-select overrides */
.modern-select {
  ::ng-deep {
    .ng-select-container {
      border-radius: 4px;
      border-color: $border-color;
      min-height: 52px;
      
      &:hover {
        border-color: $primary-light;
      }
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
    }
    
    .ng-option {
      padding: 10px 16px;
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary;
  
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background-color: rgba($primary-color, 0.05);
  }
}

.btn-success {
  background-color: $success-color;
  color: white;
  
  &:hover {
    background-color: darken($success-color, 5%);
  }
}

.btn-link {
  background: none;
  color: $primary-color;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .form-group,
  .form-group-half,
  .form-group-small,
  .form-group-large {
    min-width: 100%;
    margin-bottom: 8px;
  }
  
  .place-info {
    flex-direction: column;
  }
  
  .place-photo {
    margin-bottom: 16px;
  }
  
  .place-name {
    width: 100%;
  }
  
  .location-fields {
    flex-direction: column;
    gap: 8px;
    
    .uf-field,
    .city-field {
      width: 100%;
    }
  }
  
  .card-footer {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}