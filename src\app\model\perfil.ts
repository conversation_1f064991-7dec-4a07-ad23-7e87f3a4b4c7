
export class Perfil {

    IdPerfil?: number;
    NomePerfil?: string;

    DtaCadastro?: Date;
    IdUsuarioGerador?: number;
    FlgInativo?: boolean;
    IdClinica?: number;

    DictValoresPermissao :any = [];

}
export interface PerfilPermissao {
    flgVisualizar: boolean;
    flgIncluir: boolean;
    flgEditar: boolean;
    flgInativar: boolean;
    DesCategoria: string;
    Permissao_Categoria: string;
    idPermissao: number;
    idPerfilPermissao: number;
  }

export class flgPermissoes{
    FlgInativar?:boolean;
    FlgEditar?: boolean;
    FlgIncluir?:boolean;
    FlgVisualizar?: boolean;

}

