<div class="mother">
    <div style="display: flex; justify-content: flex-end; margin-right: 1em; ">
        <button mat-icon-button style="background-color: #dddddd00;" (click)="fecharModal()">
            <mat-icon>close</mat-icon>
        </button>
    </div>
    
    <div style="width: 100%; height: 100%; display: flex;">
        <div contents class="container tamanho container-life">
            <div class="col-md-12">
                <div class="modal-info margem-info">
                    <b class="fonteb"> {{ 'TELAINICIO.LIFELINE' | translate }} </b><br>
                </div>
            </div>
    
            <div class="d-flex justify-content-center margem-conteudo">
    
                <hr class="sep-1" />
    
                <div class="pr-1 pl-2">
                    <button tmDarkenOnHover class="btn-primary buttons-mobilet" [ngClass]="{ 'btnAtivo': showPanel }"
                        mat-mini-fab style="width: 30px;
                    touch-action: none;
                    user-select: none;
                    -webkit-user-drag: none;
                    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                    height: 30px;
                    margin-right: 4%;" (click)="abreDate();drawer.toggle()">
                        <mat-icon style="
                        margin-left: -12px;    
                        margin-bottom: -10px;
                        font-size: 19px;" aria-label="Cancelar Consulta" class="svg-icon">calendar_today</mat-icon>
                    </button>
                </div>
    
                <div class="pr-2 pl-1">
                    <button tmDarkenOnHover class="btn-primary buttons-mobilet" mat-mini-fab
                        [ngClass]="{ 'btnAtivo': showLife }" style="width: 30px;
                    touch-action: none;
                    user-select: none;
                    -webkit-user-drag: none;
                    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
                    height: 30px;
                    margin-right: 4%;" (click)='abreLife();drawer.close()'>
                        <img style="
                        width: 23px !important;    
                        margin-top: -5px;    
                        font-size: 19px;    
                        margin-left: -10px;
                        font-size: 19px;" src="assets/build/img/pulse-line-white.svg" class="svg-icon"
                            aria-label="Life line">
                    </button>
                </div>
    
                <hr class="sep-11" />
            </div>
    
            <mat-drawer-container class="example-container mt-4" autosize>
                <mat-drawer #drawer mode="side" style="width: 150px; overflow: hidden;">
    
                    <div class="text-center h-84 p-1">
                        <h4 class="h4-perg" style="color: #1265b9 !important;">
                            {{ 'TELAINICIO.HISTORICO' | translate }} </h4>
    
                        <hr class="sep-3" />
    
                        <div class="p-1" style="overflow: auto !important;" #scrollMe>
    
                            <div tmDarkenOnHover *ngFor="let item of dadosLifeLine;let i = index;" contentsTable
                                style="cursor: pointer !important;">
                                <span tmDarkenOnHover contentsLink position
                                    style=" padding: 5px;  display: block; font-size: 12px; color: #1265b9 !important;">
                                    {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                                </span>
    
                                <hr class="sep-2" />
                            </div>
    
                        </div>
    
    
                        <!-- <mat-selection-list color="primary" style=" overflow: auto !important; padding: 0px;">
    
                            <div #scrollMe>
                                <div style="padding-top: 2px; padding-bottom:2px">
                                    <div *ngFor="let item of dadosLifeLine;let i = index;" contentsTable
                                        style="border: 1px solid #bbb8b8;cursor: pointer;margin-bottom: 5px ; width: 100%;">
    
                                        <span  contentsLink position
                                            style=" padding: 5px;  display: block; font-size: 12px;">
                                            {{item.dtaAgendaFormatada  | date: 'dd/MM/yyyy HH:mm' }}</span>
                                    </div>
                                </div>
                            </div>
                        </mat-selection-list> -->
    
                    </div>
    
                </mat-drawer>
    
    
                <mat-drawer-content style=" height: 350px; overflow: auto !important;" #scrollMe>
    
                    <mat-tab-group *ngFor="let item of dadosLifeLine;let i = index;" color="primary"
                        backgroundColor="primary">
    
                        <div id="{{ item.posicao }}" #destinationRef>
    
                            <mat-tab>
                                <ng-template mat-tab-label>
                                    <i class="fas fa-notes-medical" style="font-size: 22px; padding-right: 12px;"></i>
                                    {{ 'TELAINICIO.CONSULTA' | translate }}
                                </ng-template>
                                <div style="border:1px solid #ddd;">
                                    <div class="container" style="padding:15px;">
                                        <small *ngIf="idTipoUsu==4"> {{'TELAINICIO.MEDICO' | translate }}
                                            <b>{{ item.medico}}</b>
                                            <br>
                                        </small>
                                        <small *ngIf="idTipoUsu==2"> {{ 'TELAINICIO.PACIENTE' | translate }}
                                            <b>{{ item.nomePaciente}}</b>
                                            <br>
                                        </small>
                                        <small> {{ 'TELAINICIO.DIAAGENDAMENTO' | translate }}
                                            <b>{{ item.dtaCadastro | date: 'dd/MM/yyyy HH:mm'}}</b>
                                            <br>
                                        </small>
                                        <small> {{ 'TELAINICIO.OBSERVACAOPARAACONSULTA' | translate }}
                                            <b>{{ item.obsConsulta}}</b>
                                            <br>
                                        </small>
    
                                        <small> {{ 'TELAINICIO.DIA' | translate }} <b>
                                                {{ item.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}</b>
                                            <br>
                                        </small>
    
    
                                        <small *ngIf=" item.tipoAgendamento != null">
                                            {{ 'TELAINICIO.TIPOAGENDAMENTO' | translate }} <b>{{ item.tipoAgendamento}}</b>
                                            <br>
                                        </small>
                                        <small *ngIf=" item.especialidade != null && item.especialidade !=  ''">
                                            {{ 'TELAINICIO.ESPECIALIDADE' | translate }}
                                            <b>{{ item.especialidade}}</b>
                                            <br *ngIf=" item.especialidade != null && item.especialidade !=  ''">
                                        </small>
                                    </div>
                                </div>
                            </mat-tab>
    
                            <mat-tab>
                                <ng-template mat-tab-label>
                                    <mat-icon class="example-tab-icon">accessibility_new</mat-icon>
                                    Dados Paciente
                                </ng-template>
                                <div style="border:1px solid #ddd;" *ngIf="dadosCorpoPaciente[i]!=null">
                                    <div class="container row" style="padding: 10px; margin: 0px;">
    
    
                                        <small *ngIf="item!=null" class="col-md-2"> Peso:
                                            <b>{{dadosCorpoPaciente[i].peso}}</b></small>
                                        <small *ngIf="item!=null" class="col-md-2">Altura:
                                            <b>{{dadosCorpoPaciente[i].altura}}</b></small>
                                        <small *ngIf="item!=null" class="col-md-2"> IMC:
                                            <b>{{dadosCorpoPaciente[i].imc}}</b></small>
                                        <small *ngIf="item!=null" class="col-md-2">Pressão:
                                            <b>{{dadosCorpoPaciente[i].pressao}}</b></small>
                                        <small *ngIf="item!=null" class="col-md-2">Batimento:
                                            <b>{{dadosCorpoPaciente[i].batimento}}</b></small>
                                        <small *ngIf="item!=null" class="col-md-2">Temperatura C:
                                            <b>{{dadosCorpoPaciente[i].temperatura}}</b></small>
                                        <br>
    
                                    </div>
                                </div>
                                <div style="border:1px solid #ddd;" *ngIf="dadosCorpoPaciente[i]==null">
                                    <div style="padding: 15px;" class="text-center">
    
                                        <b>Não há dados do paciente nessa consulta</b>
                                    </div>
                                </div>
    
                            </mat-tab>
    
                            <!-- <mat-tab>
                                <ng-template mat-tab-label>
                                    <mat-icon class="example-tab-icon">forum</mat-icon>
                                    {{ 'TELAINICIO.CONVERSA' | translate }}
                                </ng-template>
                                <div style="border:1px solid #ddd;">
                                    <div class="container">
                                        <div class="hist_chat row" *ngFor="let item of dadosLifeLine[i].convercas">
                                            <div class="col-md-12 col-sm-12 col-xs-12  text-right"
                                                *ngIf="item.tipoUsuario == 'Paciente'">
    
                                                <div class="md-chip  align-chip ">
                                                    <small> {{item.chat}}</small>
                                                    <p style="font-size: 8px;"> {{item.usuario}}
                                                        {{item.dtaCadastro | date: 'dd/MM/yyyy HH:mm' }}</p>
                                                </div>
                                            </div>
                                            <div class="col-md-12 col-sm-12 col-xs-12"
                                                *ngIf="item.tipoUsuario != 'Paciente'">
                                                <div class="md-chip  align-chip f-l  text-left">
                                                    <small> {{item.chat}}</small>
                                                    <p style="font-size: 8px;"> {{item.usuario}}
                                                        {{item.dtaCadastro | date: 'dd/MM/yyyy HH:mm' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </mat-tab> -->
                            <mat-tab style="border:1px solid #ddd;" *ngIf="!PacientePermissao">
                                <ng-template mat-tab-label>
                                    <mat-icon class="example-tab-icon">dehaze</mat-icon>
                                    {{ 'TELAINICIO.ANOTACOESANONIMAS' | translate }}
                                </ng-template>
                                <div class="" style="border:1px solid #ddd;" *ngIf="item.anonimo!=null">
                                    <div class="container">
                                        <div class="anon_anot row">
                                            <div class="col-md-12">
                                                <div class="card-anot">
                                                    <p>
                                                        {{item.anonimo}}
                                                    </p>
                                                    <div class="text-right">
                                                        <p *ngIf="item.anonimo!=null" class="bold" style="font-size: 12px;">
                                                            {{item.medico}}
                                                            {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div style="border:1px solid #ddd;" *ngIf="item.anonimo==null">
                                    <div style="padding: 15px;" class="text-center">
    
                                        <b>Não há anotação anônima nessa consulta</b>
                                    </div>
                                </div>
                            </mat-tab>
                            <mat-tab style="border:1px solid #ddd;">
                                <ng-template mat-tab-label>
                                    <mat-icon class="example-tab-icon">attachment</mat-icon>
                                    {{ 'TELAINICIO.ANEXO' | translate }}
                                </ng-template>
                                <div class="anon_anot" style="border:1px solid #ddd;" *ngIf="item.anexos.length>0">
                                    <table *ngIf="item.anexos.length>0" class="table">
                                        <thead>
                                            <tr>
                                                <!-- <td style="width: 40%;     border: unset;">
                                                    {{ 'TELAINICIO.USUARIO' | translate }}
                                                </td> -->
                                                <td style="width: 40%;     border: unset;">
                                                    {{ 'TELAINICIO.ANEXO' | translate }}
                                                </td>
    
    
                                                <td class="text-center" style="width: 20%; border: unset;">
                                                    {{ 'TELAINICIO.DATA' | translate }}
                                                </td>
    
                                                <td class="text-center" style="width: 20%; border: unset;">
                                                    Ações
                                                </td>
    
    
                                            </tr>
                                        </thead>
                                        <tbody *ngFor="let item of dadosLifeLine[i].anexos">
    
                                            <div *ngIf="idTipoUsuarioLogado == 4; else teste">
                                                <tr *ngIf="item.flgVisualizacaoUsuario == true">
                                                    <!-- <td>
                                                        {{item.tipoUsuario}}: {{item.usuario}}
                                                    </td> -->
                                                    <td>
                                                        {{item.anexos}}
                                                    </td>
                                                    <td>
                                                        {{item.dtaCadastro | date: 'dd/MM/yyyy HH:mm'}}
                                                    </td>
                                                    <td class="text-center">
                                                        <i (click)="BaixarArquivo(item.chave,item.anexos, item.tipoArquivo)"
                                                            style="    vertical-align: -webkit-baseline-middle;cursor: pointer; font-size: 20px"
                                                            class="fa fa-download text-left icone"
                                                            title="Download arquivo"></i>
                                                    </td>
                                                </tr>
                                            </div>
    
    
                                            <ng-template #teste>
                                                <tr>
                                                    <!-- <td>
                                                        {{item.tipoUsuario}}: {{item.usuario}}
                                                    </td> -->
                                                    <td>
                                                        {{item.anexos}}
                                                    </td>
                                                    <td>
                                                        {{item.dtaCadastro | date: 'dd/MM/yyyy HH:mm'}}
                                                    </td>
                                                    <td class="text-center">
                                                        <i (click)="BaixarArquivo(item.chave,item.anexos, item.tipoArquivo)"
                                                            style="    vertical-align: -webkit-baseline-middle;cursor: pointer; font-size: 20px"
                                                            class="fa fa-download text-left icone"
                                                            title="Download arquivo"></i>
                                                    </td>
                                                </tr>
                                            </ng-template>
    
                                        </tbody>
                                    </table>
                                </div>
                                <div style="border:1px solid #ddd;" *ngIf="item.anexos.length==0">
                                    <div style="padding: 15px;" class="text-center">
    
                                        <b>Não há anexos nessa consulta</b>
                                    </div>
                                </div>
                            </mat-tab>
    
                            <mat-tab>
                                <ng-template mat-tab-label *ngIf="idTipoUsu==2">
                                    {{item.nomePaciente | truncate : 10 : "…"}} {{ 'TELAINICIO.DIA' | translate }}
                                    {{item.dtaConsulta | date: 'dd/MM/yyyy'}}
                                </ng-template>
                                <ng-template mat-tab-label *ngIf="idTipoUsu==4">
                                    {{item.medico | truncate : 10 : "…"}} {{ 'TELAINICIO.DIA' | translate }}
                                    {{item.dtaConsulta | date: 'dd/MM/yyyy'}}
                                </ng-template>
                                <div class="container">
                                    <div class="pacienteLifeLine" style="padding: 15px;">
                                        <small *ngIf="idTipoUsu==4"> {{ 'TELAINICIO.MEDICO' | translate }}
                                            <b>{{ item.medico }}</b>
                                            <br>
                                        </small>
                                        <small *ngIf="idTipoUsu==2"> {{ 'TELAINICIO.PACIENTE' | translate }}
                                            <b>{{ item.nomePaciente }}</b>
                                            <br>
                                        </small>
                                        <small> {{ 'TELAINICIO.DIA' | translate }}
                                            <b>{{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm'}}</b></small>
                                    </div>
                                </div>
                            </mat-tab>
                        </div>
    
                        <!-- </div> -->
                    </mat-tab-group>
    
    
                </mat-drawer-content>
            </mat-drawer-container>
    
    
        </div>
    </div>
</div>