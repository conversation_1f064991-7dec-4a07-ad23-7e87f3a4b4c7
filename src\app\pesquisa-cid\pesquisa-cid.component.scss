/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$warning-color: #FFC107; /* <PERSON>elo alerta */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Ajusta o modal para a tela inteira quando aberto */
.health-modal.cid-modal .nsm-content {
  max-width: 900px !important;
  width: 95% !important;
  max-height: 90vh !important;
  height: auto !important;
  border-radius: $border-radius !important;
  overflow: hidden !important;
  margin: 20px auto !important;
  padding: 0 !important;
}

/* Container principal do modal */
.modal-container {
  display: flex;
  flex-direction: column;
  background-color: $bg-color;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
  background-color: $primary-color;
  padding: 16px 20px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
}

.modal-title-container {
  flex: 1;
  text-align: center;
  max-width: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.modal-title-container mat-icon {
  color: white;
}

.modal-title {
  color: white;
  margin: 0;
  font-family: 'Cairo', sans-serif;
  font-weight: 500;
  font-size: 20px;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color $transition ease;
  z-index: 11;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo do modal */
.modal-content {
  flex: 1;
  padding: 20px;
  background-color: $secondary-light;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

/* Seção de pesquisa */
.search-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background-color: $secondary-color;
  border-radius: $border-radius;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 50px;
}

.search-field {
  flex: 1;
  min-width: 200px;
  button{
    background-color: transparent;
  }
}

.code-field {
  flex: 0 0 30%;
}

.description-field {
  flex: 1;
}

.actions-row {
  display: flex;
  justify-content: flex-end;
}

/* Botões de ação */
.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: $border-radius;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition ease;
  min-width: 100px;
}

.clear-button {
  background-color: $secondary-dark;
  color: $text-primary;
}

.save-button {
  background-color: $primary-color;
  color: white;
}

.cancel-button {
  background-color: $secondary-dark;
  color: $text-primary;
}

.delete-button {
  background-color: $error-color;
  color: white;
}

/* Hover de botões */
.action-button:hover {
  transform: translateY(-2px);
  box-shadow: $box-shadow;
}

.clear-button:hover {
  background-color: darken($secondary-dark, 5%);
}

.save-button:hover {
  background-color: $primary-dark;
}

.delete-button:hover {
  background-color: darken($error-color, 10%);
}

/* Container da tabela */
.table-container {
  width: 100%;
  overflow: auto;
  flex: 1;
  border-radius: $border-radius;
  border: 1px solid $border-color;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
  max-height: 50vh;
}

/* Estilo da tabela */
.cid-table {
  width: 100%;
  border-collapse: collapse;
}

.cid-table th {
  position: sticky;
  top: 0;
  background-color: $primary-color;
  color: white;
  padding: 12px;
  font-weight: 500;
  text-align: left;
  z-index: 10;
}

.cid-table td {
  padding: 12px;
  border-bottom: 1px solid $border-color;
  color: $text-primary;
}

.cid-table tbody tr {
  transition: background-color $transition ease;
  cursor: pointer;
}

.cid-table tbody tr:hover {
  background-color: $secondary-color;
}

.code-column {
  width: 150px;
}

.description-column {
  width: auto;
}

.empty-message {
  text-align: center;
  color: $text-secondary;
  font-style: italic;
  padding: 24px;
}

/* Container de paginação */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 16px;
  border-top: 1px solid $border-color;
}

.load-more-button {
  background-color: $primary-color;
  color: #ffff;
  border: none;
  border-radius: $border-radius;
  padding: 8px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition ease;
}


/* Modal de confirmação */
.confirm-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 16px;
}

.confirm-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.warning-icon {
  font-size: 48px;
  color: $warning-color;
}

.confirm-message p {
  font-size: 18px;
  color: $text-primary;
  margin: 0;
}

/* Formulário de Cadastro/Edição */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 16px 0;
}

.form-field {
  width: 100%;
}

/* Footer do modal */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  background-color: $secondary-color;
  border-top: 1px solid $border-color;
}

/* Responsividade */
@media (max-width: 768px) {
  .search-row {
    flex-direction: column;
  }
  
  .code-field,
  .description-field {
    flex: 1 0 100%;
  }
  
  .cid-table th,
  .cid-table td {
    padding: 10px 8px;
  }
  
  .modal-header {
    padding: 12px 16px;
  }
  
  .modal-title {
    font-size: 18px;
  }
  
  .action-button {
    padding: 6px 12px;
    min-width: 0;
  }
  
  .action-button span {
    display: none;
  }
  
  .action-button mat-icon {
    margin: 0;
  }
  
  .confirm-message p {
    font-size: 16px;
  }
  
  .code-column {
    width: 100px;
  }
}