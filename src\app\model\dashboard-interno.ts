import { RetornoPadraoApi } from "./RetornoPadraoApi";

export class DashboardInternoModelView {
    idLogErro: number| undefined;
    metodo: string| undefined;
    desErro: string| undefined;
    dtaCadastro: Date | undefined;
    idUsuarioGerador: number| undefined;
    nomeUsuario: string | undefined
    idUltimaClinica: number| undefined;
    nomeClinica: string | undefined;
}

export class DadosDashboardInt{
    totalClinicasAtivas?: number;
    totalErros?: number;
    totalPacientes?: number;
    totalMedicosAtivos?: number;
}

export class ListaErros extends RetornoPadraoApi{
    erros: DashboardInternoModelView [] = [];
}