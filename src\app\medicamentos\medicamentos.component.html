<mat-card class="medication-card mother-div">
    <mat-card-content>
        <div class="container">
            <!-- Header Section -->
            <div class="header-section">
                <h1 class="page-title">{{ 'TELAMEDICAMENTOS.CADASTROMEDICPRESCR' | translate }}</h1>
            </div>

            <!-- New Medication Form -->
            <div class="form-section">
                <div class="section-header">
                    <mat-icon class="section-icon">local_pharmacy</mat-icon>
                    <h2>{{ 'TELAMEDICAMENTOS.NOVOSMEDICAMENTOS' | translate }}</h2>
                </div>

                <div class="form-content">
                    <mat-form-field class="form-field" appearance="outline">
                        <mat-label>{{ 'TELAMEDICAMENTOS.DESCRICAO' | translate }}</mat-label>
                        <input matInput name="descrição" required [(ngModel)]="Dados.des" maxlength="500" type="text">
                        <mat-error *ngIf="Descricao.invalid">{{getErrorMessageDesc() | translate }}</mat-error>
                    </mat-form-field>

                    <mat-form-field class="form-field" appearance="outline">
                        <mat-label>{{ 'TELAMEDICAMENTOS.POSOLOGIAPADRAO' | translate }}</mat-label>
                        <textarea matInput [(ngModel)]="posologia" name="Observações" id="obsAmbiente" required
                            maxlength="500"></textarea>
                        <mat-hint align="end">{{posologia.length}}/500</mat-hint>
                        <mat-error *ngIf="Poso.invalid">{{getErrorMessagePoso() | translate }}</mat-error>
                    </mat-form-field>

                    <div class="form-controls">
                        <div class="toggle-container">
                            <mat-slide-toggle [(ngModel)]='DesItem'>
                                {{ 'TELAMEDICAMENTOS.DESCARTARITEMNALISTA' | translate }}
                            </mat-slide-toggle>
                        </div>

                        <mat-form-field class="clinics-field" appearance="outline">
                            <mat-label>{{ 'TELACADASTROMEDICO.CLINICAS' | translate }}</mat-label>
                            <mat-select multiple name="clinicas" required
                                (blur)="ValidaClinicas($any($event.target).value)">
                                <mat-option *ngFor="let item of DadosClinicas;let i = index" [value]="item">
                                    {{item.desClinica | truncate : 40 : "…"}}
                                </mat-option>
                            </mat-select>
                            <mat-error *ngIf="clinicaVal">{{ 'TELACADASTROMEDICO.ERROCLINICAS' | translate }}</mat-error>
                        </mat-form-field>
                    </div>

                    <div class="button-group">
                        <button mat-button class="action-button cancel-button" (click)="LimparCampos()">
                            <mat-icon>clear</mat-icon>
                            {{ 'TELACADASTROMEDICO.LIMPAR' | translate }}
                        </button>
                        <button mat-button class="action-button save-button" (click)="Submit()">
                            <mat-icon>save</mat-icon>
                            {{ 'TELACADASTROMEDICO.SALVAR' | translate }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Medication List -->
            <div class="list-section">
                <div class="section-header">
                    <mat-icon class="section-icon">account_box</mat-icon>
                    <h2>{{ 'TELAMEDICAMENTOS.MEDICAMENTO' | translate }}</h2>
                </div>

                <div class="search-container">
                    <div class="search-field-container">
                        <mat-form-field class="search-field" appearance="outline">
                            <mat-label>{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}</mat-label>
                            <input matInput name="pesquisa" [(ngModel)]="pesquisa" (keyup.enter)="CarregaMedicamentos()">
                            <button mat-icon-button matSuffix (click)="CarregaMedicamentos()">
                                <mat-icon>search</mat-icon>
                            </button>
                        </mat-form-field>
                    </div>

                    <div class="legend-container">
                        <button mat-button class="legend-toggle" (click)="legenda = !legenda">
                            {{ 'TELAAGENDACONTATO.LEGENDA' | translate }}
                            <mat-icon>{{ legenda ? 'expand_less' : 'expand_more' }}</mat-icon>
                        </button>
                        <div class="legend-content" *ngIf="legenda">
                            <div class="legend-item">
                                <mat-icon>edit</mat-icon>
                                <span>{{ 'TELAAGENDACONTATO.EDITAR' | translate }}</span>
                            </div>
                            <div class="legend-item">
                                <mat-icon>delete</mat-icon>
                                <span>{{ 'TELAAGENDACONTATO.EXCLUIR' | translate }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Desktop Medication List -->
                <div class="medication-list desktop-only">
                    <table class="medication-table">
                        <tbody>
                            <tr *ngFor="let item of DadosTable" [class.highlighted]="item.medicamento.flgDestacarItem">
                                <td class="medication-name">
                                    <strong>{{ 'TELAMEDICAMENTOS.MEDICAMENTO:' | translate }}</strong>
                                    {{item.medicamento.medicamentosProgramados}}
                                </td>
                                <td class="medication-posology">
                                    <strong>{{ 'TELAMEDICAMENTOS.POSOLOGIA:' | translate }}</strong>
                                    {{item.medicamento.programacao | truncate : 40 : "…"}}
                                </td>
                                <td class="medication-actions">
                                    <button mat-icon-button class="edit-button"
                                        (click)="editMedicamento(item.medicamento.idMedicamentosProgramados)"
                                        title="{{'TELAMEDICAMENTOS.EDITAR' | translate}}">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button class="delete-button"
                                        (click)="valueRemedio(item.medicamento.idMedicamentosProgramados)"
                                        title="{{'TELAMEDICAMENTOS.EXCLUIR' | translate}}">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Medication Cards -->
                <div class="medication-cards mobile-only">
                    <div class="medication-card-item" *ngFor="let item of DadosTable; let i = index"
                        [class.highlighted]="item.medicamento.flgDestacarItem">
                        <div class="medication-info">
                            <div class="medication-detail">
                                <h3>{{ 'TELAMEDICAMENTOS.MEDICAMENTO:' | translate }}</h3>
                                <p>{{item.medicamento.medicamentosProgramados}}</p>
                            </div>
                            <div class="medication-detail">
                                <h3>{{ 'TELAMEDICAMENTOS.POSOLOGIA:' | translate }}</h3>
                                <p>{{item.medicamento.programacao | truncate : 40 : "…"}}</p>
                            </div>
                        </div>
                        <div class="card-actions">
                            <button mat-icon-button class="action-fab" (click)="openToggle(i)">
                                <mat-icon>more_vert</mat-icon>
                            </button>
                            <div class="action-menu" [@openClose]="toggle[i] ? 'open': 'closed'" 
                                 (clickOutside)="toggle[i] ? fecharBotoesMobile() : null">
                                <button mat-mini-fab class="edit-button"
                                    (click)="editMedicamento(item.medicamento.idMedicamentosProgramados)">
                                    <mat-icon>edit</mat-icon>
                                </button>
                                <button mat-mini-fab class="delete-button"
                                    (click)="valueRemedio(item.medicamento.idMedicamentosProgramados)">
                                    <mat-icon>delete</mat-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="load-more" *ngIf="(DadosTable != undefined && DadosTable.length > 0) && bOcultaCarregaMais == false">
                    <button mat-button class="load-more-button" (click)="CarregarMais()">
                        {{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}
                    </button>
                </div>
            </div>
        </div>
    </mat-card-content>
</mat-card>

<!-- Delete Confirmation Modal -->
<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal">
    <div class="modal-container">
        <div class="modal-header">
            <div class="modal-title">
                <mat-icon class="warning-icon">warning</mat-icon>
                <h2>{{ 'TELAMEDICAMENTOS.EXCLUIRMEDICAMENTO' | translate }}</h2>
            </div>
        </div>
        <div class="modal-body">
            <p>{{ 'TELAMEDICAMENTOS.EXCLUIRPERMANENTEMENTE' | translate }}</p>
        </div>
        <div class="modal-footer">
            <button mat-button class="cancel-button" (click)="excluirItem.close()">
                {{ 'TELAMEDICAMENTOS.NAO' | translate }}
            </button>
            <button mat-button class="confirm-button" (click)="InativarMedicamento()">
                {{ 'TELAMEDICAMENTOS.SIM' | translate }}
            </button>
        </div>
    </div>
</ngx-smart-modal>