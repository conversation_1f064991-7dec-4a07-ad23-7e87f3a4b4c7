import { Component } from "@angular/core";
import { TelemedicinaComponentBase } from "src/app/Util/component.base";
import { ExamesModalService } from "./cadastro-exame.service";
import { NgxSmartModalModule, NgxSmartModalService } from "ngx-smart-modal";
import { MedicoService } from "src/app/service/medico.service";
import { PacienteService } from "src/app/service/pacientes.service";
import { UsuarioLogadoService } from "src/app/auth/usuarioLogado.service";
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from "@angular/forms";
import { ExamePaciente } from "src/app/model/exames";
import { ExamesService } from "src/app/service/exameService.service";
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
import { SpinnerService } from "src/app/service/spinner.service";
import { AlertComponent } from "src/app/alert/alert.component";
import { CommonModule } from "@angular/common";
import { NgSelectModule } from "@ng-select/ng-select";
import { MatIcon } from "@angular/material/icon";
import { TranslateModule } from "@ngx-translate/core";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";

@Component({
    selector: "app-cadastro-exame",
    templateUrl: "./cadastro-exame.component.html",
    styleUrls: ["./cadastro-exame.component.scss"],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      NgxSmartModalModule,
      NgSelectModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule
    ]
})
export class CadastroExameComponent extends TelemedicinaComponentBase {
  constructor(    
    private spinner: SpinnerService,
    private examesModalService: ExamesModalService,
    public ngxSmartModalService: NgxSmartModalService,
    public medicoService: MedicoService,
    private examesService: ExamesService,
    public pacientesService: PacienteService,
    public usuarioLogadoService: UsuarioLogadoService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent
  ) {
    super();
  }
  actionButtonLabel: string = "Fechar";
  salvoSucess: string = "Cadastro salvo com Sucesso. ✔";
  ErroSalvar: string = "Erro ao salvar!";
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = "right";
  verticalPosition: MatSnackBarVerticalPosition = "bottom";
  concordo?: boolean;
  concordomsg?: boolean;

  showMessageError = false;
  dtaInvalida: boolean = false;
  ufInvalido: boolean = false;
  objExames = new ExamePaciente();
  pacienteValido = false;
  ListaMedicos = [];
  DadosPacientes:any = [];
  DadosExame = [];
  Paciente = [];
  Medico = [];
  NomePaciente: boolean = false;
  Nomemedico: boolean = false;
  ngOnInit() {
    this.examesModalService
      .getModalExames()      
      .subscribe(() => {
        this.modalExames();
      });

    this.CarregaPacientes();
    // this.CarregarExamesClinica()
  }

  modalExames() {
    this.ngxSmartModalService.getModal("examesModal").open();
  }


  LimparCampos(){
    this.objExames = new ExamePaciente();
  }
  CarregaPacientes() {
    this.pacientesService
      .GetPacienteAgenda(null, this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe(
        (retorno) => {
          this.DadosPacientes = [];

          this.DadosPacientes = retorno.filter((c:any) => c.flgInativo != true);

          if (this.DadosPacientes.length == 1)
            this.Paciente = this.DadosPacientes[0].idCliente;


          
          
          this.spinner.hide();
        },
        () => {
          this.spinner.hide();
        }
      );
  }

  CarregarExamesClinica() {
    this.examesService
      .GetExamesClinica("", this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe((retorno) => {
        
        
        this.DadosExame = retorno;
        this.spinner.hide();
      });
  }

  Nome = new FormControl("", [Validators.required, Validators.required]);
  CodCNES = new FormControl("", [Validators.required, Validators.required]);
  NomeProSolicitante = new FormControl("", [
    Validators.required,
    Validators.required,
  ]);
  ConsProfi = new FormControl("", [Validators.required, Validators.required]);
  NumConse = new FormControl("", [Validators.required, Validators.required]);
  CodCBOS = new FormControl("", [Validators.required, Validators.required]);
  IndicacaoClin = new FormControl("", [
    Validators.required,
    Validators.required,
  ]);
  CodProced = new FormControl("", [Validators.required, Validators.required]);
  DtaAssinatura = new FormControl("", [
    Validators.required,
    Validators.required,
  ]);

  getErrorMessageNome() {
    return this.Nome.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.Nome.hasError("Nome")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageNomeProSolicitante() {
    return this.NomeProSolicitante.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.NomeProSolicitante.hasError("NomeProSolicitante")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageNumConse() {
    return this.NumConse.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.NumConse.hasError("NumConse")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageCodCBOS() {
    return this.CodCBOS.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.CodCBOS.hasError("CodCBOS")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageCodProced() {
    return this.CodProced.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.CodProced.hasError("CodProced")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageCodCNES() {
    return this.CodCNES.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.CodCNES.hasError("CodCNES")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageConsProfi() {
    return this.ConsProfi.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.ConsProfi.hasError("ConsProfi")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageIndicacaoClin() {
    return this.IndicacaoClin.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.IndicacaoClin.hasError("IndicacaoClin")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }
  getErrorMessageDtaAssinatura() {
    return this.DtaAssinatura.hasError("required")
      ? "TELAAGENDA.ERROCAMPO"
      : this.DtaAssinatura.hasError("DtaAssinatura")
      ? "TELAAGENDA.ERRONAOEVALIDO"
      : "";
  }


  public Submit() {
    try {
      // this.validarCampos();
      if (this.showMessageError) {
        return;
      }

      var exame = new ExamePaciente();
      exame.IdPaciente = this.objExames.IdPaciente;
      exame.IdExameClinica = this.objExames.IdExameClinica;
      exame.IdClinica = this.usuarioLogadoService.getIdUltimaClinica()!;
      exame.DesResultado = this.objExames.DesResultado;
      exame.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;

      this.examesService.SalvarResultadoExame(exame).subscribe(
        () => {
          // this.CarregaExames();
          this.LimparCampos();
          this.snackBarAlert.sucessoSnackbar("Salvo com Sucesso!");
          this.spinner.hide();
        },
        () => {
          this.snackBarAlert.falhaSnackbar("Erro ao salvar!");
          this.spinner.hide();
        }
      );
    } catch (error) {
      this.snackBarAlert.falhaSnackbar
      ;
      this.spinner.hide();
    }
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }
}
