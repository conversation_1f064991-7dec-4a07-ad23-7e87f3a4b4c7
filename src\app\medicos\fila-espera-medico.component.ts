import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FilaEsperaMedicoService } from 'src/app/service/fila-espera-medico.service';
import { SignalHubService } from 'src/app/service/signalHub.service';
import { Subscription } from 'rxjs';
import { MatInputModule } from '@angular/material/input';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { RouterModule, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatBadgeModule } from '@angular/material/badge';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { AlertComponent } from '../alert/alert.component';
import { ControleModaisService } from '../service/controle-modais.service';
import { MatDividerModule } from '@angular/material/divider';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TranslateModule } from '@ngx-translate/core';
import { NgxSmartModalModule } from 'ngx-smart-modal';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { RelatorioExameApiService, DoctorPatientDataResponse } from '../service/relatorio-exame-api.service';

@Component({
  selector: 'app-fila-espera-medico',
  templateUrl: './fila-espera-medico.component.html',
  styleUrls: ['./fila-espera-medico.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    MatFormFieldModule,
    MatDialogModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatBadgeModule,
    MatSnackBarModule,
    TranslateModule,
    MatIcon,
    NgxSmartModalModule,
    MatDividerModule,
    MatSlideToggleModule
  ]
})
export class FilaEsperaMedicoComponent implements OnInit, OnDestroy {
  pacientesFila: any[] = [];
  loading: boolean = false;
  loadingConvite: boolean = false; // Estado de loading para convites
  signalRConnected: boolean = false; // Status da conexão SignalR
  private subscriptions: Subscription[] = [];
  ultimaAtualizacao: Date = new Date();
  autoRefresh: boolean = true;
  intervalId: any;

  constructor(
    private filaEsperaMedicoService: FilaEsperaMedicoService,
    private signalHubService: SignalHubService,
    private snackBarAlert: AlertComponent,
    private controleModaisService: ControleModaisService,
    private router: Router,
    private usuarioLogadoService: UsuarioLogadoService,
    private relatorioExameApiService: RelatorioExameApiService
  ) { }

  ngOnInit() {
    this.carregarFilaEspera();
    this.configurarSignalR();
    this.iniciarAutoRefresh();
    this.verificarConexaoSignalR();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  iniciarAutoRefresh() {
    if (this.autoRefresh) {
      this.intervalId = setInterval(() => {
        this.carregarFilaEspera();
      }, 30000);
    }
  }

  toggleAutoRefresh() {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.iniciarAutoRefresh();
    } else if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  private configurarSignalR() {
    const sub = this.signalHubService.OnAtualizaChamaPacienteFila
      .subscribe(() => {
        this.carregarFilaEspera();
      });

    this.subscriptions.push(sub);
  }

  /**
   * Verifica e garante que a conexão SignalR esteja ativa
   */
  private async verificarConexaoSignalR() {
    try {
      if (!this.signalHubService.isConnected()) {
        this.signalRConnected = false;
        await this.signalHubService.ensureConnection();
        this.signalRConnected = true;
      } else {
        this.signalRConnected = true;
      }
    } catch (error) {
      this.signalRConnected = false;
      console.error('❌ Erro ao estabelecer conexão SignalR:', error);
      this.snackBarAlert.falhaSnackbar('Aviso: Problemas de conexão podem afetar convites para reunião');
    }
  }

  /**
   * Getter para verificar se SignalR está conectado
   */
  get isSignalRConnected(): boolean {
    return this.signalRConnected && this.signalHubService.isConnected();
  }

  carregarFilaEspera() {
    this.loading = true;
    this.filaEsperaMedicoService.listarPacientesNaFila().subscribe({
      next: (response) => {
        if (response.sucesso) {
          this.pacientesFila = response.pacientes || [];
          this.ultimaAtualizacao = new Date();
        } else {
          this.snackBarAlert.falhaSnackbar("Erro ao carregar fila de espera");
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Erro ao carregar fila:', error);
        this.snackBarAlert.falhaSnackbar("Erro ao conectar com o servidor");
        this.loading = false;
      }
    });
  }

  chamarProximoPaciente() {
    if (this.pacientesFila.length === 0) {
      this.snackBarAlert.falhaSnackbar("Não há pacientes na fila para chamar");
      return;
    }

    this.filaEsperaMedicoService.chamarProximoPaciente().subscribe({
      next: (response) => {
        if (response.Sucesso) {
          this.snackBarAlert.sucessoSnackbar(`Paciente ${response.pacienteChamado || 'próximo'} foi chamado!`);
        } else {
          this.snackBarAlert.falhaSnackbar("Erro ao chamar paciente");
        }
      },
      error: (error) => {
        console.error('Erro ao chamar paciente:', error);
        this.snackBarAlert.falhaSnackbar("Erro ao chamar paciente");
      }
    });
  }

  async removerPaciente(token: string, nomePaciente?: string) {
    this.filaEsperaMedicoService.removerPacienteDaFila(token).subscribe({
      next: (response) => {
        if (response.sucesso) {
          this.snackBarAlert.sucessoSnackbar(`${nomePaciente || 'Paciente'} removido da fila com sucesso`);
          // A fila será atualizada automaticamente via SignalR
        } else {
          this.snackBarAlert.falhaSnackbar("Erro ao remover paciente da fila");
        }
      },
      error: (error) => {
        console.error('Erro ao remover paciente:', error);
        this.snackBarAlert.falhaSnackbar("Erro ao remover paciente da fila");
      }
    });
  }

  async limparFila() {
    if (this.pacientesFila.length === 0) {
      this.snackBarAlert.falhaSnackbar("A fila já está vazia");
      return;
    }

    const confirmacao = await this.controleModaisService.ModalConfirmacao(
      `Tem certeza que deseja limpar toda a fila? Isso removerá ${this.pacientesFila.length} paciente(s).`
    );

    if (confirmacao) {
      this.filaEsperaMedicoService.limparFila().subscribe({
        next: (response) => {
          if (response.Sucesso) {
            this.snackBarAlert.sucessoSnackbar("Fila limpa com sucesso");
            // A fila será atualizada automaticamente via SignalR
          } else {
            this.snackBarAlert.falhaSnackbar("Erro ao limpar a fila");
          }
        },
        error: (error) => {
          console.error('Erro ao limpar fila:', error);
          this.snackBarAlert.falhaSnackbar("Erro ao limpar a fila");
        }
      });
    }
  }

  async convidarParaReuniao(paciente: any) {
    const nomePaciente = paciente.nomePaciente || paciente.nome || 'Paciente';
    const token = paciente.tokenConexao || paciente.token;

    const confirmacao = await this.controleModaisService.ModalConfirmacao(
      `Deseja convidar ${nomePaciente} para a reunião e iniciar a consulta?`
    );

    if (confirmacao) {
      if (token) {
        this.loadingConvite = true; // Ativar loading

        try {
          // Enviar convite para o paciente
          await this.signalHubService.convidarPacienteComToken(token, nomePaciente);
          this.snackBarAlert.sucessoSnackbar(`Convite enviado para ${nomePaciente}. Iniciando consulta...`);

          // Chamar próximo paciente e redirecionar
          this.chamarProximoERedireccionar(paciente);

          // Remover paciente da fila
        } catch (error) {
          console.error('Erro ao processar convite:', error);
          this.snackBarAlert.falhaSnackbar("Erro ao enviar convite para reunião. Verifique a conexão.");
          this.loadingConvite = false;
        }
      } else {
        this.snackBarAlert.falhaSnackbar("Token de conexão não encontrado para o paciente");
      }
    }
  }

  /**
   * Chama próximo paciente e redireciona para streaming
   */
  private chamarProximoERedireccionar(paciente: any) {
    const token = paciente.tokenConexao || paciente.token;

    if (!token) {
      this.snackBarAlert.falhaSnackbar("Token do paciente inválido");
      this.loadingConvite = false;
      return;
    }

    // Verificar se há dados do paciente disponíveis antes de redirecionar
    this.verificarDadosPacienteERedireccionar(token);
  }

  /**
   * Verifica se há dados do paciente e redireciona para streaming
   */
  private verificarDadosPacienteERedireccionar(token: string) {
    this.loadingConvite = true;

    this.relatorioExameApiService.getPatientDataByToken(token).subscribe({
      next: (response: DoctorPatientDataResponse) => {
        if (response.success && response.patient) {
          console.log('✅ Dados do paciente encontrados:', response.patient);
          this.snackBarAlert.sucessoSnackbar(`Iniciando consulta com ${response.patient.name}`);
        } else {
          console.log('⚠️ Dados do paciente não encontrados, mas prosseguindo...');
          this.snackBarAlert.infoSnackbar('Dados do paciente serão solicitados durante a consulta');
        }

        // Redirecionar para streaming independentemente
        this.router.navigate(['/streaming-guest'], {
          queryParams: { token: token }
        });

        this.loadingConvite = false;
      },
      error: (error) => {
        console.warn('⚠️ Erro ao buscar dados do paciente, mas prosseguindo:', error);
        this.snackBarAlert.infoSnackbar('Dados do paciente serão solicitados durante a consulta');

        // Redirecionar mesmo com erro na busca de dados
        this.router.navigate(['/streaming-guest'], {
          queryParams: { token: token }
        });

        this.loadingConvite = false;
      }
    });
  }

  /**
   * Visualiza dados do paciente (modal ou painel lateral)
   */
  visualizarDadosPaciente(paciente: any) {
    const token = paciente.tokenConexao || paciente.token;

    if (!token) {
      this.snackBarAlert.falhaSnackbar("Token do paciente inválido");
      return;
    }

    this.relatorioExameApiService.getPatientDataByToken(token).subscribe({
      next: (response: DoctorPatientDataResponse) => {
        if (response.success && response.patient) {
          this.exibirModalDadosPaciente(response.patient);
        } else {
          this.snackBarAlert.falhaSnackbar("Dados do paciente não encontrados");
        }
      },
      error: (error) => {
        console.error('Erro ao buscar dados do paciente:', error);
        this.snackBarAlert.falhaSnackbar("Erro ao carregar dados do paciente");
      }
    });
  }

  /**
   * Exibe modal com dados do paciente
   */
  private exibirModalDadosPaciente(patient: any) {
    // Use alert for now, can be replaced with a proper modal later
    this.snackBarAlert.infoSnackbar('Dados do paciente carregados. Verifique o console para detalhes.');
    console.log('📋 Dados do Paciente:', patient);

    // Optional: Display formatted data in console for debugging
    const dadosFormatados = this.formatarDadosPacienteParaExibicao(patient);
    console.log('📋 Dados Formatados:', dadosFormatados);
  }

  /**
   * Formata dados do paciente para exibição
   */
  private formatarDadosPacienteParaExibicao(patient: any): string {
    let dados = `<div class="patient-data-modal">`;

    // Dados pessoais
    dados += `<h4>📋 Dados Pessoais</h4>`;
    dados += `<p><strong>Nome:</strong> ${patient.name || 'Não informado'}</p>`;
    dados += `<p><strong>CPF:</strong> ${patient.cpf || 'Não informado'}</p>`;
    dados += `<p><strong>Email:</strong> ${patient.email || 'Não informado'}</p>`;
    dados += `<p><strong>Telefone:</strong> ${patient.phone || 'Não informado'}</p>`;

    if (patient.age) {
      dados += `<p><strong>Idade:</strong> ${patient.age} anos</p>`;
    }

    // Sinais vitais
    if (patient.vitalSigns) {
      dados += `<h4>🩺 Sinais Vitais</h4>`;
      if (patient.vitalSigns.bloodPressure) {
        dados += `<p><strong>Pressão Arterial:</strong> ${patient.vitalSigns.bloodPressure}</p>`;
      }
      if (patient.vitalSigns.heartRate) {
        dados += `<p><strong>Frequência Cardíaca:</strong> ${patient.vitalSigns.heartRate} bpm</p>`;
      }
      if (patient.vitalSigns.temperatureFormatted) {
        dados += `<p><strong>Temperatura:</strong> ${patient.vitalSigns.temperatureFormatted}</p>`;
      }
      if (patient.vitalSigns.oxygenation) {
        dados += `<p><strong>Saturação O2:</strong> ${patient.vitalSigns.oxygenation}%</p>`;
      }
    }

    // Sintomas
    if (patient.symptoms) {
      dados += `<h4>🤒 Sintomas</h4>`;
      if (patient.symptoms.description) {
        dados += `<p><strong>Descrição:</strong> ${patient.symptoms.description}</p>`;
      }
      if (patient.symptoms.intensityDescription) {
        dados += `<p><strong>Intensidade:</strong> ${patient.symptoms.intensityDescription}</p>`;
      }
      if (patient.symptoms.durationDescription) {
        dados += `<p><strong>Duração:</strong> ${patient.symptoms.durationDescription}</p>`;
      }
    }

    // Alergias e doenças prévias
    if (patient.allergies) {
      dados += `<h4>⚠️ Alergias</h4>`;
      dados += `<p>${patient.allergies}</p>`;
    }

    if (patient.previousDiseases) {
      dados += `<h4>📋 Doenças Prévias</h4>`;
      dados += `<p>${patient.previousDiseases}</p>`;
    }

    // Observações adicionais
    if (patient.additionalObservations) {
      dados += `<h4>📝 Observações Adicionais</h4>`;
      dados += `<p>${patient.additionalObservations}</p>`;
    }

    dados += `</div>`;

    return dados;
  }

  formatarTempoEspera(tempoEspera: any): string {
    if (!tempoEspera) return 'N/A';

    if (typeof tempoEspera === 'string') {
      return tempoEspera;
    }

    // Se for um objeto de tempo, formatar adequadamente
    const horas = Math.floor(tempoEspera.hours || 0);
    const minutos = Math.floor(tempoEspera.minutes || 0);

    if (horas > 0) {
      return `${horas}h ${minutos}m`;
    }
    return `${minutos}m`;
  }

  get totalPacientes(): number {
    return this.pacientesFila.length;
  }

  get proximoPaciente(): any {
    return this.pacientesFila.find(p => p.PosicaoFila === 1) || this.pacientesFila[0];
  }

  /**
   * Inicia consulta com paciente específico e redireciona para streaming
   */
  iniciarConsultaComPaciente(paciente: any) {
    if (!paciente || !paciente.TokenConexao) {
      this.snackBarAlert.falhaSnackbar("Dados do paciente inválidos");
      return;
    }

    // Verificar se o médico está logado
    if (!this.usuarioLogadoService.isLogged()) {
      this.snackBarAlert.falhaSnackbar("Erro: Médico não está logado");
      this.router.navigate(['/login']);
      return;
    }

    // Redirecionar diretamente para streaming sem chamar endpoint removido
    this.snackBarAlert.sucessoSnackbar(`Iniciando consulta com ${paciente.NomePaciente}. Redirecionando...`);

    this.router.navigate(['/streaming-guest'], {
      queryParams: { token: paciente.TokenConexao }
    });
  }
}
