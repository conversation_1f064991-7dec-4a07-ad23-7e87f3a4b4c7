/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$success-color: #81DC81; /* Verde sucesso */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo principal */
.modal-content {
    flex: 1;
    overflow: hidden;
    padding: 16px;
    background-color: $secondary-light;
    display: flex;
    flex-direction: column;
}

.content-columns {
    display: flex;
    gap: 16px;
    height: 100%;
}

/* Títulos de seções */
.section-title {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: $text-primary;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid $border-color;
}

/* Coluna de mensagens padrão */
.messages-column {
    width: 25%;
    background-color: $secondary-color;
    border-radius: $border-radius;
    padding: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.messages-list {
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.message-card {
    background-color: $secondary-light;
    border-radius: $border-radius;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all $transition ease;
}

.message-card:hover {
    background-color: $primary-light;
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

/* Coluna central (conteúdo da mensagem) */
.content-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 16px;
}

.message-input-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.message-input-field {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.message-textarea {
    flex: 1;
    resize: none;
    min-height: 300px;
}

.message-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    padding: 8px 0;
}

.response-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toggle-label {
    font-size: 14px;
    color: $text-secondary;
}

.info-button {
    background-color: $primary-color;
    color: white;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color $transition ease;
}

.info-button:hover {
    background-color: $primary-dark;
}

/* Coluna de consultas */
.appointments-column {
    width: 25%;
    background-color: $secondary-color;
    border-radius: $border-radius;
    padding: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.appointments-list {
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.appointment-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    background-color: $secondary-light;
    border-radius: $border-radius;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all $transition ease;
}

.appointment-item:hover {
    background-color: $secondary-color;
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

.checkbox-container {
    padding-top: 2px;
}

.checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: $primary-color;
    cursor: pointer;
}

.appointment-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.appointment-message {
    font-weight: 500;
    color: $text-primary;
}

.appointment-date {
    font-size: 13px;
    color: $text-secondary;
}

.selection-counter {
    text-align: center;
    margin-top: 8px;
    font-size: 14px;
    color: $text-secondary;
}

/* Estado vazio */
.empty-state {
    text-align: center;
    color: $text-secondary;
    padding: 20px;
    font-style: italic;
}

/* Rodapé do modal */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px;
    background-color: $secondary-color;
    gap: 12px;
    border-top: 1px solid $border-color;
}

/* Botões de ação */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 120px;
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: darken($secondary-dark, 5%);
}

.confirm-button {
    background-color: $primary-color;
    color: white;
}

.confirm-button:hover {
    background-color: $primary-dark;
}

/* Responsividade */
@media (max-width: 992px) {
    .content-columns {
        flex-direction: column;
    }
    
    .messages-column,
    .appointments-column {
        width: 100%;
        max-height: 200px;
    }
    
    .content-column {
        padding: 16px 0;
    }
    
    .message-textarea {
        min-height: 150px;
    }
}