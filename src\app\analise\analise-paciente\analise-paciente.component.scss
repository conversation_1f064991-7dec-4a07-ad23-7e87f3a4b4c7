.principal {
    background-color: #f3f4ff;
    box-shadow: none;
    border-radius: 10px;
    padding: 5px 10px;
    border: none;
    background: #ffff;
}
.icon-voltar {
    color: #1f5f3d;
    padding-right: 5px;
}
.voltar-text {
    color: #1f5f3d;
    font-weight: 500;
    font-size: 15px;
    font-family: system-ui;
    padding: 0 5px;
    text-transform: uppercase;
}
.title-page {
    font-weight: 500;
    color: #1f5f3d;
    font-size: 20px;
    text-transform: uppercase;
    font-family: system-ui;
    padding: 0;
    align-self: center;
    margin: 0;
    padding-left: 5px;
}

.principal-dados {
    padding: 0;
    display: flex;
    flex-wrap: wrap;
}
.foto-locais {
    padding: 0;
    align-self: center;
    padding-left: 5px;
}
.img-local {
    width: 7vw;
}
.title-dados h5 {
    font-family: system-ui;
    font-weight: 500;
    font-size: 18px;
    color: #1f5f3d;
    margin: 0;
    padding: 15px 0;
}

.descricao-dados {
    padding: 0;
    align-self: center;
}
.btn-add {
    height: 35px;
    border: none;
    border-radius: 8px;
    padding: 0 20px;
    color: #fff;
    background-color: #00a6e8;
}

// --------------------------------


.icon-page-title {
    border: 2px solid #1f5f3d;
    border-radius: 100%;
    padding: 5px;
    height: 45px;
    width: 45px;
}
.buscar-adicionar {
    display: flex; 
    padding: 15px 0;
}
.ativar-foto {
    display: flex; 
    align-self: center; 
    padding: 0; 
    margin-bottom: 15px;
}
.bottom-ativar {
    padding-right: 10px; 
    color: #5260ff; 
    font-family: system-ui; 
    font-weight: 400;
}
.bottom-inativos {
    color: #5260ff; 
    font-family: system-ui; 
    font-weight: 400;
}
.div-buscar {
    display: flex; 
    padding: 0 5px;
}
.form-busca {
    font-size: 12px; 
    width: 100%; 
    padding: 0 5px;
}
.div-add {
    display: flex; 
    padding: 0; 
}

.foto-locais {
    padding: 0;
    align-self: center;
}
.img-local {
    width: 80px;
}

.infos-itens p {
    font-size: 14px;
    margin: 0;
    font-family: system-ui;
}
.linha-tudo {
    display: flex; 
    flex-wrap: wrap;
    border: 1px solid #ddd;
    border-radius: 10px;
    padding: 10px;
    margin: 10px 0;
    background-color: #fff;
}
.dados-tabela {
    display: flex; 
    flex-wrap: wrap;
    padding: 0;
}
.data-tabela {
    align-self: center;
    padding: 0;
}
.data-tabela p {
    font-size: 14px;
    margin: 0;
    font-family: system-ui;
}
.infos-itens {
    align-self: center;
}
.infos-itens p {
    font-size: 14px;
    margin: 0;
    font-family: system-ui;
    color: #898989;
}
.data-tabela p {
    font-size: 14px;
    margin: 0;
    font-family: system-ui;
    color: #898989;
}
.btns-tabela {
    text-align: end; 
    align-self: center;
}
.panel_button {
    color: #5260ff;
}
.div-scroll-tabela {
    overflow-y: auto;
    height: 70vh;
    padding: 10px;
}
.div-scroll-tabela::-webkit-scrollbar {
    width: 5px;
}
  
.div-scroll-tabela::-webkit-scrollbar-thumb:vertical {
    background-color: #5260ff;
    border-radius: 8px;
}

input[type="file"] {
    display: none;
  }

  #formularioViewer {
    width: 45vmax;
    height: 100%;
    min-height: 50vh;
    max-height: 80vh;
    display: flex;
    justify-content: center;
}

.formulario-content {
    width: 90%;
    height: 90%;
    background: #eaebf8;
    border-radius: 19px;
    margin: 3%;
}

#tituloModal {
    margin-left: 9%;
    margin-top: 3%;
}

.chat-container {
    margin: 3%;
    width: 100%;
    height: 100%;
    max-height: 60vh;
    overflow-y: scroll;
}

.chat-message {
    box-sizing: border-box;
    width: 85%;
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    font-size: 14px;
    overflow-wrap: break-word;
}

.left {
    background-color: #FFFFFF;
    border: 0.5px solid #E2E2E5;
    border-radius: 7px 7px 0px 7px;
    margin-right: 10%;
}

.right {
    background-color: #F4F4F4;
    border: 0.5px solid #E2E2E5;
    border-radius: 7px 7px 7px 0px;
    margin-left: 10%;

}

.row-button {
    text-align: center;
    padding: 20px;
}

.btn-email {
    background-color: #DC3545;
    color: #FFFFFF;
}