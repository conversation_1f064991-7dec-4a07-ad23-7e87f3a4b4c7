@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57; // Verde Saúde
$primary-light: #A3D9B1; // Verde claro suavizado
$primary-dark: #1F5F3D; // Verde escuro
$secondary-color: #F4F4F9; // Cinza Claro / Off-White
$secondary-light: #FFFFFF; // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5; // Cinza médio para hover/active
$accent-color: #4ECDC4; // Turquesa Claro (toque moderno)
$error-color: #FF6B6B; // Vermelho Pastel
$text-primary: #333333; // Cinza escuro para boa legibilidade
$text-secondary: #6B7280; // Cinza médio
$border-color: #E5E7EB; // Bordas suaves
$bg-color: #F9FAFB; // Fundo geral suave
$card-bg: #FFFFFF; // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

@mixin center() {
  margin: left;
  margin-right: auto;
}

.dropdown-circle {
  width: 50px;
  padding: 4px;
  left: 15px;
  margin-left: auto;
  margin-right: auto;
}

.img-perfil {
  width: 70px;
  margin-right: auto;
}

.button-config {
  border-radius: $border-radius;
}

.seta {
  cursor: pointer;
  color: #fff !important;
  font-size: 21px;
  transition: $transition;
  
  &:hover {
    transform: scale(1.1);
  }
}

.space {
  background-color: $primary-dark !important;
  padding: 5px;
  cursor: pointer;
  box-shadow: none;
  max-width: 39px;
  mat-icon{
    color: #fff !important;
  }
  &:hover {
    background: $secondary-dark;
    color: #fff;
  }
}

// Menu lateral
.sidenav {
  background:#fff;
  z-index: 2 !important;
  box-shadow: none;
  transition: 1s;
  width: 55px;
  overflow: visible;
  height: 100vh;
  display: flex;
  justify-content: space-between;
  align-items: start;
  flex-direction: column;
  gap: 30px;
  position: fixed;
}
#sidenav.active {
  width: 190px;
  background: #fff !important;
  box-shadow: none !important;
}
.paginas{
  z-index: 10;
  background-color: transparent; 
  transition: .5;
  margin-top: 0;
  transition: .6s;
}
.contanier{
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 10;
}
.menuA{
  color: $primary-color;;
  text-decoration: none;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-wrap: none;
  padding: 4px 7px;
  margin: 16px 8px;
  cursor: pointer;
  border-radius: 8px;
  font-weight: 500;
  &:hover{
      background-color: $primary-dark;
      mat-icon{
        color: #fff ;
      }
      span{
        color: #fff;
      }
  }
  mat-icon{
    margin-left: 2px;
    font-size: 20px;
    color: #73a888;
  }
  span{
      position: absolute;
      margin-left: 60px;
      font-size: 15.5px;
      color: #73a888;
  }
}
.menuFechado{
  width: 100%;
}
.expanpadirFecahdo{
  position: absolute;
  margin-left: 65px;
  background-color: #fff;
  width: 250px;
  height: auto;
  margin-top: -520px;
  border-radius: 8px;
  pointer-events: none;
  display: none;
  padding: 10px;
  box-shadow: 33px 23px 35px 3px rgba(0, 0, 0, 0.1);
}
.expanpadirFecahdo.modal2{
  width: 290px;
  margin-top: -430px;
  &::after{
    bottom: 75px;    
    left: -16px;
  }
}
.expanpadirFecahdo.modal3{
  width: 290px;
  margin-top: -384px;
  &::after{
    bottom: 75px;    
    left: -16px;
  }
}
.expanpadirFecahdo.modal4{
  width: 290px;
  margin-top: -305px;
  &::after{
    bottom: 75px;    
    left: -16px;
  }
}
 .disabled {
  display: flex;
  pointer-events: all;

}
.menuExpande{
  z-index: 1;
  position: absolute;
  pointer-events: none;
  opacity: 0;
  transition: .6s;
  width: 100%;
  span{
      margin-left: 50px;
  }
  span, i{
      font-size: 17px;
  }a{
      padding: 10px 20px;
  }
  li{
      padding-right: 35px;
  }
}
.buttonExpandido{
  padding-left: 25px;
  font-size: 11px;
  padding: 0px 25px;
  mat-icon{
    font-size: 18px;
  }
}
.menuExpande.aparecer{
  transition: .6s;
  opacity: 1;
  pointer-events: all;
}
.paginas.active3{
  margin-top: 360px;
  transition: .6s;
}
.paginas.active4{
  margin-top: 190px;
  transition: .6s;
}
.paginas.active5{
  margin-top: 157px;
  transition: .6s;
}
.paginas.active6{
  margin-top: 105px;
  transition: .6s;
}
.item-menu mat-icon {
  color: $secondary-light !important;
  transition: $transition;
}

.item-menu:hover mat-icon {
  color: $primary-color !important;
  transform: scale(1.1);
}

.success-check {
  transition: all 1s;
  background: rgba($accent-color, 0.2);
}

.check:hover {
  background: rgba($accent-color, 0.2);
  transition: all 1s;
}

mat-panel-title {
  border-radius: 0px;
}

.menu-scroll::-webkit-scrollbar {
  width: 4px;
  background-color: rgba($secondary-light, 0.1);
}

.menu-scroll::-webkit-scrollbar-thumb:vertical {
  background-color: rgba($secondary-light, 0.3);
  border-radius: 8px;
  
  &:hover {
    background-color: rgba($secondary-light, 0.5);
  }
}

.mat-accordion .mat-expansion-panel:not(.mat-expanded),
.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing) {
  border: unset !important;
}

.conteudo-central {
  background-color: $bg-color;
}
.item-menu {
  cursor: pointer;
  width: 55px;
  font-size: 25px;
  border-radius: $border-radius;
  transition: $transition;
}

.item-menu:hover {
  background-color: rgba($secondary-light, 0.15);
  border-radius: $border-radius;
  transform: translateY(-2px);
}

.item-menu p {
  margin: 0px;
  padding: 0px;
}

.menu-expanded a {
  color: $secondary-light;
  vertical-align: text-bottom;
  transition: $transition;
}

.menu-expanded:hover a {
  transform: scale(1.05);
}

.IconeChat.Ocultar{
  display: none;
}

.IconeChat {
  position: absolute;
  width: 100%;

  .bolinhaBG {
    position: absolute;
    background-color: $error-color;
    color: transparent;
    z-index: 1;
    right: 5px;
    height: 1.4rem;
    display: flex;
    width: 1.4rem;
    align-items: center;
    justify-content: center;
    animation: pulse 1.5s infinite;
  }

  .textoChat {
    font-size: 0.5em;
    color: $secondary-light;
    font-weight: 600;
  }
}

.dropmenor {
  padding-top: 4px;
}

.dropmenor:hover {
  background: rgba($primary-dark, 0.3);
}

.menu-expanded {
  cursor: pointer;
  transition: $transition;
}

.menu-expanded:hover {
  cursor: pointer;
  color: rgba($secondary-light, 0.15);
  background-color: #ffff;
}

.menu-expanded:hover .white {
  color: $primary-color !important;
}

.menu-expandedop1 {
  padding-left: 10px;
  margin-top: -7px;
  cursor: pointer;
}

.menu-expandedop1:hover {
  cursor: pointer;
  color: $secondary-light;
}

.passar:hover {
  background: rgba($primary-dark, 0.3);
}

.logo {
  cursor: pointer;
}

.align-menu {
  margin-top: 0%;
}

.content-menu {
  padding-top: 13%;
}

.paciente {
  border-radius: 50%;
  width: 45px;
  height: 40px;
  font-size: 20px !important;
  padding-left: 13px;
  padding-right: 15px;
  padding-bottom: 15px;
  padding-top: 9px;
  margin-top: 10px;
  color: $secondary-light;
  cursor: pointer;
}

.menu-item {
  color: $secondary-light;
  cursor: pointer;
}
.list-item{
  padding: 5px;
  width: 100%;
  border-radius: 5px;
  text-align: left;
  background-color: #fff;
  cursor: pointer;
  transition: .2s;
  &:hover{
    background-color: #1F5F3D;
    color: #fff !important;
    mat-icon{
      color: #fff !important;
    }
    a{
      color: #fff !important;
    }
  }
}
.sub-menu1 .mat-icon {
  font-size: 20px;
  padding: 2px;
  margin-left: 18px;
}

.sub-menu .mat-icon {
  font-size: 15px;
  margin-left: 20px;
}

.sub-menu:hover {
  transition: all 1s;
}

.sub-menu a {
  vertical-align: text-bottom;
}

.example-container {
  width: 500px;
  height: 300px;
  border: 1px solid rgba(0, 0, 0, 0.5);
}

.example-sidenav-content {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.white {
  color: $secondary-light !important;
  font-size: 16px;
  transition: $transition;
}

.white .mat-icon a {
  color: $secondary-light;
  font-size: 13px;
}

.text-menu {
  margin-left: 10px;
  margin-right: 10px;
  font-size: 15px;
  font-family: 'Poppins', sans-serif;
}

.text-menu1 {
  margin-left: 10px;
  margin-right: 10px;
  font-family: 'Poppins', sans-serif;
}

.text-menu2 {
  margin-left: 9px;
  margin-right: 10px;
  font-family: 'Poppins', sans-serif;
}

.text-menu3 {
  margin-left: 6px;
  margin-right: 10px;
  font-family: 'Poppins', sans-serif;
}

.title-content {
  font-size: 19px;
  padding-left: 10px;
  font-family: 'Poppins', system-ui;
  font-weight: 500;
  color: $primary-color;
  margin: 0;
}

.color-text {
  color: $primary-color !important;
  font-size: 15px;
}

.lista2:before {
  bottom: auto;
  content: "";
  height: 8px;
  left: 23px;
  margin-top: 15px;
  position: absolute;
  right: auto;
  width: 8px;
  z-index: 0;
  background: $secondary-light;
  border-radius: 50%;
}

.lista2:after {
  bottom: 15px;
  content: "";
  left: 27px;
  position: absolute;
  top: 0px;
  border-left: 1px solid $secondary-light;
}

.mat-expansion-indicator:after {
  color: $secondary-light !important;
}

.panel_menu {
  display: flex;
  align-content: center;
  align-items: center;
}

.user {
  padding-left: 10px;
  padding-right: 10px;
  line-height: 20px;
}

.heart-pulses {
  width: 50px;
  margin-right: 5px;
  margin-left: 5px;
}

.txt1 {
  font-weight: 400;
  font-size: 18px;
}

.yu {
  font-size: 20px;
}

.pulse-line {
  width: 25px;
  margin-left: 10px;
  margin-right: 10px;
}

.nome {
  font-size: 14px;
  text-transform: capitalize;
  margin-bottom: 0;
  font-weight: bold;
  color: $primary-color;
  font-family: 'Poppins', sans-serif;
}

.nome1 {
  font-size: 14px;
  text-transform: capitalize;
  margin-bottom: 0;
  font-weight: bold;
  color: $primary-color;
  font-family: 'Poppins', sans-serif;
}

.container-perfil {
  margin-right: -25px;
  display: flex;
}

.align-img-perfil {
  margin-right: 15px;
  margin-left: 15px;
}

.notifica-carta {
  outline: none;
  padding: 8px;
  display: flex;
  background-color: #2e8b57;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: .2s;
  &:hover{
    background-color: #1F5F3D;
  }
}
.sair{
  outline: none;
  padding: 8px;
  display: flex;
  border: 1px solid #ff3c3c;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: .2s;
  background-color: #ffffff;
  mat-icon{
    color: #ff3c3c;
  }
  &:hover{
    background-color: #f32727;
    mat-icon{
      color: #fff;
    }
  }
}
::ng-deep {
  .mat-badge-content{
    background-color: #ff5151!important;
    border: 1px solid #fff;
  }
  .mat-badge-after .mat-badge-content {
      left: 115% !important;
  }
  .mat-badge-above .mat-badge-content {
      bottom: 100% !important;
  }
  
  
}

.consulta {
  font-weight: 300;
  font-size: 12px;
  font-weight: bold;
  text-transform: capitalize;
  color: $secondary-light;
  font-family: 'Poppins', sans-serif;
}

.consulta1 {
  font-weight: 300;
  font-size: 12px;
  font-weight: bold;
  text-transform: capitalize;
  color: $primary-color;
  font-family: 'Poppins', sans-serif;
}

.line-h {
  line-height: 14px;
}

.categoria {
  font-size: 15px;
}


.line-h {
  cursor: pointer;
}

.fast_panel {
  display: flex;
  align-items: center;
  place-content: flex-end;
}

.label_paciente {
  margin: 0 !important;
  font-size: 100%;
  font-family: 'Poppins', system-ui;
  color: $text-secondary;
}

.content-clinica {
  font-size: 14px;
  color: $text-secondary;
  text-transform: capitalize;
  vertical-align: top;
}

.icon-clinica {
  font-size: 17px;
  color: $accent-color;
  vertical-align: bottom;
  margin-right: 5px;
}

.card {
  max-height: 570px;
  overflow: auto;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: 1px solid $border-color;
  background-color: $card-bg;
  transition: $transition;
  
  &:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.07);
  }
}

.card::-webkit-scrollbar {
  width: 5px;
}

.card::-webkit-scrollbar-thumb:vertical {
  background-color: $primary-color;
  border-radius: 5px;
}

.card_table:hover {
  box-shadow: $box-shadow;
  transition: 0.3s;
  background: unset;
  transform: translateY(-2px);
}

.background-icon {
  width: 50px;
  height: 50px;
  padding: 13px;
  color: $secondary-light;
  background: $primary-color;
  border-radius: 50%;
}

.panel-button-dropdown {
  padding-top: 10px;
  border-top: 1px dashed rgba($primary-color, 0.3);
}

.logo {
  width: 70%;
}

.box-card:hover {
  box-shadow: $box-shadow;
}

.background-icon-alert {
  background: #f7c406;
  width: 50px;
  height: 50px;
  padding: 13px;
  color: $secondary-light;
  border-radius: 50%;
}

.background-icon-danger {
  background: $error-color;
  width: 50px;
  height: 50px;
  padding: 13px;
  color: $secondary-light;
  border-radius: 50%;
}

.inf-content {
  padding: 10px;
  padding-left: 30px;
  font-size: 15px;
  line-height: 20px;
}

.dropdown-menu2.show {
  transform: translate3d(5px, 0px, 0px);
  will-change: transform;
  display: block;
  position: fixed !important;
  background: $primary-color;
  border-radius: $border-radius;
  color: $secondary-light;
  top: 175px !important;
  left: 74px !important;
}

.dropright .dropdown-toggle::after {
  display: none;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}

.list-item2 {
  margin-top: auto;
  margin-bottom: auto;
}


.img-circle {
  width: 62px !important;
  height: 62px !important;
  border: 1px solid $border-color;
  background: $secondary-light;
  margin-left: auto;
  margin-right: auto;
  margin-top: 10px;
  border-radius: 50%;
  object-fit: cover;
}

.dropdown-circle {
  width: 100%;
  padding: 4px;
  left: 15px;
  border-radius: 50%;
  margin-left: auto;
  margin-right: auto;
}

.Title-config {
  color: $text-secondary;
  font-size: 15px;
  text-transform: capitalize;
}

.Title-b {
  color: $primary-color;
  font-family: 'Poppins', system-ui;
  font-weight: 500;
  margin: 0;
}

.menu-icon {
  padding: 0px;
  font-size: 10px;
}

.body-config {
  white-space: pre-wrap;
  padding: 5px;
  line-height: 20px;
}

.dropdown-config {
  padding: 0 !important;
  border: none;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
}

.dropdown-configComCora {
  width: 280px;
  left: -70px !important;
  padding-top: 10px !important;
  padding-left: 15px !important;
  padding-right: 15px !important;
  padding-bottom: 10px !important;
  border: none;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
}

.painel-config {
  padding: 10px;
  box-shadow: $box-shadow;
}

.lateral-menu {
  padding-top: 10px;
}

.menu-expanded {
  display: flex;
  padding: 5px;
  padding-left: 10px;
  cursor: pointer;
}

i.fa {
  font-size: 25px;
  padding: 9px;
  width: 30px;
  text-align: center;
  text-shadow: 1px 1px 1px #000;
}

.leftMenu {
  height: 100%;
  margin-top: 60px;
  background-color: $primary-color;
  position: fixed;
  left: 0;
  top: 0;
  width: 50px;
  transition: all ease 1s;
   
  transition: 0.5s cubic-bezier(0.8, 0.5, 0.2, 1.4);
}

.hamburger {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $primary-color;
  float: right;
  margin-right: 5px;
  margin-top: 5px;
  text-align: center;
  cursor: pointer;
  transform: rotate(0deg);
}

.hamburger.open {
  background-color: $error-color;
  transition: all ease 0.5s;
}

.hamburger:hover {
  box-shadow: none;
  transition: all ease 0.5s;
}

.hamburger span {
  width: 60%;
  height: 3px;
  background-color: $secondary-light;
  display: block;
  border-radius: 1px;
  float: left;
  margin-left: 20%;
  transition: all ease 0.3s;
}

.hamburger span:nth-child(1) {
  margin-top: 13px;
}

.hamburger span:nth-child(2) {
  margin-top: 3px;
}

.hamburger span:nth-child(3) {
  margin-top: 3px;
}

.open span:nth-child(1) {
  background-color: $secondary-light;
  transform: rotate(45deg);
  transition-timing-function: ease-in-out;
  position: absolute;
  top: 5px;
  right: 8px;
  width: 25px;
  transition: all ease 0.5s;
  border-radius: 10px;
}

.open span:nth-child(2) {
  background-color: $secondary-light;
  transition-timing-function: ease-in-out;
  margin-top: 18px;
  opacity: 0;
  transition: all ease 0.5s;
}

.open span:nth-child(3) {
  background-color: $secondary-light;
  transform: rotate(-45deg);
  transition-timing-function: ease-in-out;
  margin-top: 0px;
  position: absolute;
  top: 18px;
  right: 8px;
  width: 25px;
  transition: all ease 0.5s;
  border-radius: 10px;
}

.openMenu {
  width: 250px;
  transition: all ease 1s;
   
  transition: 0.5s cubic-bezier(0.8, 0.5, 0.2, 1.4);
}

.leftMenuList {
  margin-top: 70px;
  list-style: none;
  padding: 0;
}

.leftMenuList li {
  width: 250px;
  line-height: 40px;
  color: $secondary-light;
  border-bottom: 1px solid $accent-color;
  font-size: 13px;
}

.leftMenuList li:hover {
  background-color: rgba($primary-dark, 0.3);
  transition: all ease 0.5s;
  cursor: pointer;
}

.leftMenuList li a {
  text-decoration: none;
  color: $secondary-light;
}

.leftMenuList li a svg {
  width: 40px;
  height: 40px;
  float: left;
  margin-left: 0px;
}

.leftMenuList li a span {
  text-decoration: none;
  color: $secondary-light;
  margin-left: 25px;
  width: 100%;
}

.leftMenuList li ul li {
  padding: 0px;
  line-height: 30px;
  transition: all 0.4s;
  box-sizing: border-box;
}

.leftMenuList li ul li:hover {
  background: $accent-color;
  transition: all 0.4s;
}

.leftMenuList li ul li ul li {
  padding: 5px;
  background: $primary-light;
}

.leftMenuList li ul li ul li a span {
  color: $primary-dark;
}

.leftMenuList li ul li ul li:hover {
  background: $primary-light;
}

.dropdown {
  text-decoration: none;
  display: block;
  transition: 0.5s;
  cursor: pointer;
  align-self: center;
}

.active+.dropdownlist {
  width: 100%;
  height: auto;
  line-height: 30px;
  padding: 0px;
  margin-left: 0px;
  background: $accent-color;
  transition: 0.5s;
}

.active+.dropdownlist:hover {
  background: $primary-light;
}

.dropdownlist {
  width: 0;
  height: 0;
   
  transition: 0.5s;
}

.dropdown.active:before {
  font-family: FontAwesome;
  content: "\f107";
  transition: all 0.4s;
  padding-right: 5px;
}

.leftMenu:not(.openMenu) li>.dropdownlist {
  pointer-events: none;
  height: 0;
}

.text-shadow {
  text-shadow: 1px 1px 0px #000;
}

.text-color {
  color: $accent-color;
}

.close {
  opacity: 1 !important;
}

.modal-titulo-consulta {
  margin-top: -15px;
  font-size: 27px;
  color: $primary-dark;
  padding: 10px;
}

.info-consulta {
  font-family: 'Poppins', Cairo, sans-serif;
  margin-top: 10px;
  margin-left: -10px;
}

.info-consulta p {
  font-family: 'Poppins', Cairo, sans-serif;
  line-height: 1.2em;
  font-weight: 500;
}

.logo-final-modal {
  max-width: 250px;
  margin-top: 20px;
  height: 30px;
  margin-bottom: 10px;
  margin-left: -10px;
}

.titulo-consulta {
  font-family: 'Poppins', Dosis, sans-serif;
  font-weight: 400;
  color: $primary-dark;
  margin-left: -170px;
  margin-top: 5px;
  margin-bottom: -5px;
}

.card-consulta {
  margin-bottom: 1rem !important;
  border: 1px solid $primary-color;
  border-radius: $border-radius;
  max-width: 320px;
  margin: 5px;
  background-color: $secondary-light;
  margin-top: 10px !important;
  box-shadow: $box-shadow;
  transition: $transition;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  }
}

.div-suporte {
  align-self: center;
  vertical-align: middle;
  text-align: center;
  cursor: pointer;
}

.btn-dropdow {
  background-color: $primary-color;
  color: $secondary-light;
  font-size: 13px;
  font-family: 'Poppins', sans-serif;
  border-radius: $border-radius;
  transition: $transition;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-dropdow2 {
  background-color: $primary-dark;
  color: $secondary-light;
  font-size: 13px;
  font-family: 'Poppins', sans-serif;
  border-radius: $border-radius;
  transition: $transition;
  
  &:hover {
    background-color: darken($primary-dark, 5%);
  }
}

.div-principal-dash {
  background-color: #edf8f3b3;
  margin: 0 auto;
  padding: 0 0px 20px 10px;
  height: 100vh;
   
}

.div-principal-dash::-webkit-scrollbar-thumb {
  background-color: $primary-color;
  border-radius: 10px;
}

.div-principal-dash::-webkit-scrollbar {
  border-radius: 10px;
  width: 5px;
}

.notifica-sino {
  border: none;
  background-color: transparent;
  height: 35px;
  width: 35px;
  vertical-align: middle;
  outline: none;
}

.notifica-sino mat-icon {
  text-align: center;
  height: 35px;
  width: 35px;
  color: $primary-color;
  font-size: 30px;
  outline: none;
  transition: $transition;
  
  &:hover {
    transform: scale(1.05);
    color: darken($primary-color, 10%);
  }
}

.btn-sair {
  border: none;
  background-color: transparent;
  height: 35px;
  width: 35px;
  vertical-align: middle;
}

.btn-sair mat-icon, .notifica-carta mat-icon {
  transition: $transition;
  
  &:hover {
    transform: scale(1);
  }
}

.conteudo-meio {
  padding: 0px;
  margin-top: 50px;
  @media (max-width: 1000px) {
    margin-top: 0;
  }
}

.menu-header {
  padding-left: 11px;
  margin-top: 2px;
  cursor: pointer;
  transition: $transition;
  
  &:hover {
    background-color: #fff;
    color: rgba($secondary-light, 0.15);
  }
}

.menu-header:hover {
  color: $secondary-light !important;
}

.menu-header:hover .white {
  color: $primary-color !important;
}

// Botões estilizados
.btn-primary {
  background-color: $primary-color !important;
  color: $secondary-light !important;
  border-color: $primary-color !important;
  border-radius: $border-radius;
  transition: $transition;
  
  &:hover {
    background-color: darken($primary-color, 10%) !important;
  }
}

.btn-danger {
  background-color: $error-color !important;
  border-color: $error-color !important;
  color: $secondary-light !important;
  border-radius: $border-radius;
  transition: $transition;
  
  &:hover {
    background-color: darken($error-color, 10%) !important;
  }
}

.btn-success {
  background-color: $accent-color !important;
  border-color: $accent-color !important;
  color: $secondary-light !important;
  border-radius: $border-radius;
  transition: $transition;
  
  &:hover {
    background-color: darken($accent-color, 10%) !important;
  }
}

// Elemento de botão interativo
.button-interactive {
  background-color: $primary-color;
  color: $secondary-light;
  border: none;
  border-radius: $border-radius;
  padding: 8px 16px;
  font-weight: 500;
  transition: $transition;
  
  &:hover {
    background-color: darken($primary-color, 10%);
    transform: translateY(-2px);
    box-shadow: $box-shadow;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// Botões de ação móvel
.buttons-mobilet {
  transition: $transition;
  
  &:hover {
    transform: scale(1.1);
  }
}

hr.sep-1 {
  border: 0;
  margin-bottom: 0 !important;
  height: 4px;
  width: 100%;
  background-image: linear-gradient(to right, $secondary-light, $primary-color, $primary-color, $secondary-light);
}

hr.sep-2 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  border: 0;
  height: 2px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  background-image: linear-gradient(to right, $secondary-light, $accent-color, $accent-color, $secondary-light);
}

// Animações
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba($error-color, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba($error-color, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($error-color, 0);
  }
}

.basic-pulses-red {
  animation: pulse 1.5s infinite;
}

.mat-badge-content {
  background: $error-color !important;
  color: $secondary-light !important;
  font-weight: 600;
}

::ng-deep {
  body:has(.overlay.nsm-overlay-open) {
    mat-drawer {
      display: none !important;
    }
  }

  .nsm-dialog-btn-close {
    font-size: 3px;
  }
}

// Responsividade
@media (max-width: 1500px) {
  .mat-mdc-menu-panel {
    top: -33px;
  }
}
@media (max-width: 1880px) {
  .logo {
    width: 287px;
  }
}

@media (max-width: 1024px) {
  .logo {
    width: 250px;
  }

  .dropdown-notification {
    width: 350px;
    height: 244px;
  }
}

@media (max-width: 600px) {
  .conteudo-meio {
    padding: 0;
  }
  
  .img-circle {
    border-radius: 50%;
    width: 45px !important;
    height: 45px !important;
    border: 1px solid $border-color;
  }

  .margem {
    padding-right: 10px;
  }

  .table-scroller {
    padding: 10px;
  }
}

@media (max-width: 475px) {
  .logo {
    display: none;
  }

  .dropdown-notification {
    width: 340px;
    height: 244px;
  }
  /* TODO(mdc-migration): The following rule targets internal classes of menu that may no longer apply for the MDC version.*/
  .mat-menu-adjust {
    margin-top: -83px !important;
    display: list-item !important;
    margin-left: 0 !important;
    position: absolute !important;
    left: 64px !important;
  }

  .text-menu[_ngcontent-c2] {
    margin-left: 0px;
  }

  .menu-item {
    font-size: 10px;
  }

  .text-menu {
    font-size: 10px;
  }

  .text-menu1 {
    font-size: 10px;
  }

  .text-menu2 {
    font-size: 10px;
  }

  .t-user {
    margin-left: 10px !important;
  }

  .item-menu {
    margin: 0;
    width: 63px !important;
  }
}

//426 - 575
@media (min-width: 426px) {
  .dropdown-notification {
    width: 340px;
    margin-right: 60px !important;
  }
}

@media (max-width: 425px) {
  .logo {
    width: 200px;
  }

  .dropdown-configComCora {
    width: 260px;
    left: -95px !important;
    border-radius: $border-radius;
  }

  .dropdown-config {
    width: 260px;
    left: -60px !important;
    border-radius: $border-radius;
  }

  .mobile-card {
    width: 20% !important;
  }

  .inf-content {
    width: 75% !important;
    padding-left: 0px;
  }

  .dropdown-notification {
    width: 340px;
    margin-right: 60px !important;
  }

  .consulta {
    display: none;
  }

  .nome {
    display: none;
  }

  .text-menu1 {
    margin-left: -2px;
  }

  .text-menu2 {
    margin-left: 5px;
  }
}

.iconCoracao li {
  position: absolute;
  will-change: transform;
  top: 0px;
  left: 0px;
  transform: translate3d(-47px, 40px, 0px);
}

@media (max-width: 420px) {
  .logo {
    width: 180px;
  }

  .dropdown-notification {
    width: 340px;
    margin-right: 60px !important;
  }
}

@media (max-width: 400px) {
  .logo {
    display: none;
  }

  .dropdown-notification {
    width: 340px;
    margin-right: 60px !important;
  }

  .dropdown-config {
    width: 230px;
    border-radius: $border-radius;
    left: -50px !important;
  }

  .dropdown-configComCora {
    width: 230px;
    left: -100px !important;
    border-radius: $border-radius;
  }
}

@media (max-width: 360px) {
  .container-perfil {
    margin-right: -50px;
    display: flex;
    padding-right: 10px;
  }
}

@media (max-width: 320px) {
  .logo {
    display: none;
  }

  .dropdown-notification {
    width: 320px;
    margin-right: 30px !important;
  }

  .dropdown-configComCora {
    left: -100px !important;
    border-radius: $border-radius;
    width: 200px;
  }

  .dropdown-config {
    left: -65px !important;
    border-radius: $border-radius;
    width: 200px;
  }
}

@media (max-width: 300px) {
  .nome1 {
    display: none;
  }

  .consulta1 {
    display: none;
  }

  .dropdown-config {
    left: -65px !important;
    border-radius: $border-radius;
    width: 150px;
  }

  .dropdown-configComCora {
    left: -110px !important;
    border-radius: $border-radius;
    width: 150px;
  }
}

@media (max-width: 720px) {
  .logo {
    display: none;
  }
}

@media (max-width: 991px) {
  .lateral-aparece {
    display: none;
  }
  
  .CardsDireita {
    display: none !important;
  }
}

.mail-content {
  background: $primary-color;
  border: 30px;
  padding: 5px;
  box-shadow: $box-shadow;
}

.badge {
  font-size: 9px;
  height: 18px;
  text-align: center;
  width: 18px;
  position: fixed;
  padding-top: 4px;
  padding-left: 0;
  padding-right: 0;
  font-weight: 700;
  right: 145px;
  background: $primary-dark;
  border-radius: 10px;
  color: $secondary-light;
}

// Melhorias específicas para o menu
.menu-container {
  background-color: $bg-color !important;
  height: 100vh;
}

// Ajustes ao conteúdo principal e menu lateral
.sidenav {
  transition: 1s ;
}

.mat-drawer-content {
  transition: margin-left 0.3s ease;
}

.activeMenu{
  mat-icon{
    color:	$primary-dark;
  }
  span{
    color:	$primary-dark;
    font-weight: 700;
  }
  &:hover{
    background-color: #fff;
    mat-icon{
    color:	$primary-dark !important;  
    }
    span{
    color:	$primary-dark;

    }
  }
  cursor: default !important;
  background-color: #e4fff3;
}
.menu-sair{
  display: none;
  border: none;
  .sair{
    border: none;
  }
  @media (max-width: 1000px) {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}