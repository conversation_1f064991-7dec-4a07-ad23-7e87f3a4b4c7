import { Component, OnInit, ViewChild } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { PacienteService } from '../service/pacientes.service';
import { CidadeService } from '../service/cidade.service';
import { C<PERSON>e, DadosMedicosUsuario } from '../model/cliente';
import { Endereco } from '../model/endereco';
import { Contato } from '../model/contato';
import {
  FormControl, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { ClinicaService } from '../service/clinica.service';
import { ImageCropperComponent, ImageCroppedEvent, ImageTransform, ImageCropperModule } from 'ngx-image-cropper';
import { UsuarioService } from '../service/usuario.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { Pessoa } from '../model/pessoa';
import { ValidadoreseMascaras } from '../Util/validadores';
import { ConvenioService } from '../service/convenio.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { UfClass } from '../Util/UFClass';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatDividerModule } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatTooltipModule} from '@angular/material/tooltip';


@Component({
    selector: 'app-pacientes',
    templateUrl: './pacientes.component.html',
    styleUrls: ['./pacientes.component.scss'],
    providers: [
        // The locale would typically be provided on the root module of your application. We do it at
        // the component level here, due to limitations of our example generation script.
        { provide: MAT_DATE_LOCALE, useValue: 'pt-BR' },
    ],
      standalone: true,
    imports: [
      MatInputModule,
      ImageCropperModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      TranslateModule,
      MatIcon,
      MatFormFieldModule,
      MatSelectModule,
      NgSelectModule,
      NgxSmartModalModule,
      MatDividerModule,
      TruncatePipe,
      MatSlideToggleModule,
      MatTooltipModule
    ]
})
export class PacientesComponent implements OnInit {



  constructor(
    private spinner: SpinnerService,
    private usuarioService: UsuarioService,
    private cidadeService: CidadeService,
    private pacienteService: PacienteService,
    // public snackBar: MatSnackBar,
    public ngxSmartModalService: NgxSmartModalService,
    // private spinner: NgxSpinnerService, 

    private clinicaService: ClinicaService,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private convenioService: ConvenioService,
    private localStorageService: LocalStorageService,
    private snackbarAlert: AlertComponent,

  ) { }


  @ViewChild(ImageCropperComponent) imageCropper!: ImageCropperComponent;
  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  clinicaCadastro: any;
  Dtanasc?: boolean;
  TelVal?: boolean;
  TelMovVal?: boolean;
  TelMovValVasil = false;
  TelMovLimpa = true;
  TelComVal?: boolean;
  clinicaVasil = true;
  clinicaVal?: boolean;
  ImagemPessoa: any = "assets/build/img/userdefault.png";
  fileToUpload!: File;
  showMessageError = false;
  showMessageSuccess = false;
  Dados: any;
  dadosUF: any;
  dadosUFCarregaBanco: any;
  dadosCidade: any;
  dadosCidadeUf: any;
  errors = [];
  DadosUsuario: any;
  // usuario: Usuario;
  tipoUsuario: any;
  clinicas: any = new FormControl([]);
  DadosClinicas: any = [];
  retornoPessoa: any;
  retornoPaciente: any;
  retornoEndereco: any;
  retornoContato: any;
  retornoUsuario: any;
  retornoDadosMedeicos: any;
  retornoClinicas: any;
  imageChangedEvent: any = '';
  croppedImage: any = '';

  showCropper = false;
  usuarioInvalidoEmail: boolean = false;
  usuarioInvalidoTelefone: boolean = false;
  imagemCorte: any;
  dadosConvenio = [];
  DadosSexo: any = [];
  campoSexo: any;
  fem: any;
  masc: any;
  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;

  ngOnInit() {
    this.Dados = [];

    this.dadosUF = UfClass;
    this.CarregaConvenio();
    var idCliente = this.localStorageService.idCliente;
    this.localStorageService.clearByName("idCliente");
    if (idCliente)
      this.CarregaPaciente(idCliente);
    else
      this.CarregaClinicas();

  }

  carregaDadosSexo() {
    if (this.DadosSexo.length == 0) {
      this.tradutor.get('TELACADASTROPACIENTE.MASCULINO').subscribe((res: string) => {
        this.masc = res;
      });
      this.tradutor.get('TELACADASTROPACIENTE.FEMININO').subscribe((res: string) => {
        this.fem = res
      });
      this.DadosSexo = [this.masc, this.fem]
    }
  }
  readThis(inputValue: any): void {
    var file: File = inputValue.files[0];
    var myReader: FileReader = new FileReader();

    myReader.onloadend = () => {
      this.ImagemPessoa = myReader.result;
    }
    myReader.readAsDataURL(file);
    // ;
  }

  CarregaClinicas() {
    try {
      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {

        this.DadosClinicas = retornaClinicas


        this.spinner.hide();
      }, () => {
        this.snackbarAlert.falhaSnackbar("erro no retorno especialidade")

        this.spinner.hide();
      })
    } catch (error) {

      this.snackbarAlert.falhaSnackbar("falha na conexão!")
      this.spinner.hide();
    }
  }


  public CidadePorUF() {
    try {
      this.cidadeService.getCidades().then((retornaCidade) => {
        this.dadosCidade = retornaCidade;
        this.dadosCidadeUf = this.dadosCidade.filter((c: any) => c.siglasUf == this.Dados.uf);
        this.spinner.hide();

      }, () => {

        this.tradutor.get('TELACADASTROPACIENTE.ERROAOCARREGARCIDADE').subscribe((res: string) => {
          ;
          this.snackbarAlert.falhaSnackbar(res);
        });
        this.spinner.hide();
      })
    } catch (error) {

      this.tradutor.get('TELACADASTROPACIENTE.ERROAOCARREGARCIDADE').subscribe((res: string) => {
        ;
        this.snackbarAlert.falhaSnackbar(res);
      });

    }
  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    if (v.match(/^\d{2}$/) !== null) {
      return (<HTMLInputElement>evento.target).value = v + '/'
    } else if (v.match(/^\d{2}\/\d{2}$/) !== null) {

      return (<HTMLInputElement>evento.target).value = v + '/';
    }
  }

  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraCpf(mascara: any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 14) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 11) {
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }

  idUsuarioExistente: any;
  mensagemPaciente?: string;
  campoExitente?: string;
  public AceitarUsuarioExistente() {
    if (this.idUsuarioExistente != null) {
      if (this.idUsuarioExistente.paciente != null)
        this.CarregaPaciente(this.idUsuarioExistente.paciente.idCliente)
      else {
        this.retornoPessoa = this.idUsuarioExistente.pessoa;
        this.retornoEndereco = this.idUsuarioExistente.endereco;
        this.retornoContato = this.idUsuarioExistente.contato;
        this.Dados.nome = this.retornoPessoa.nomePessoa;
        this.Dados.dtaNascimento = this.retornoPessoa.dtaNascimento ? new Date(this.retornoPessoa.dtaNascimento).toLocaleDateString() : "";
        this.Dados.cpf = this.retornoPessoa.cpf;
        this.Dados.profissao = this.retornoPessoa.profissao;

        this.carregaDadosSexo()
        if (this.retornoPessoa.sexo == "Masculino") {
          this.campoSexo = this.masc
        } else if (this.retornoPessoa.sexo == "Feminino") {
          this.campoSexo = this.fem
        } else
          this.campoSexo = null
        if (this.retornoContato != null) {
          if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
            this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
            this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
            this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
          }
          this.Dados.telefone = this.retornoContato.telefone;

          if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
            this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
            this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
            this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
          }
          this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

          if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
            this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
            this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
            this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
          }
          this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;
        }

        this.Dados.nascionalidade = this.retornoPessoa.nascionalidade;
        this.Dados.naturalidade = this.retornoPessoa.naturalidade;
        this.Dados.email = this.retornoPessoa.email;
        if (this.retornoEndereco) {
          this.Dados.rua = this.retornoEndereco.rua
          this.Dados.numero = this.retornoEndereco.numero
          this.Dados.complemento = this.retornoEndereco.complemento
          this.Dados.cep = this.retornoEndereco.cep
          this.Dados.bairro = this.retornoEndereco.bairro
          if (this.retornoEndereco.idCidade != null) {
            this.cidadeService.getCidades().then((retornaCidade) => {
              this.dadosCidade = retornaCidade
              var sigle = this.dadosCidade.filter((c: any) => c.idCidade == this.retornoEndereco.idCidade)
              this.Dados.uf = sigle[0].siglasUf

              this.dadosCidadeUf = this.dadosCidade.filter((c: any) => c.siglasUf == sigle[0].siglasUf);
              this.Dados.idCidade = this.retornoEndereco.idCidade
              this.spinner.hide();
            }, () => {
              this.spinner.hide();
            })
          }
        }
        this.Dados.foto = this.retornoPessoa.foto;
        if (this.idUsuarioExistente.imagem64 != null && this.idUsuarioExistente.imagem64 != "")
          this.ImagemPessoa = this.idUsuarioExistente.imagem64;
        if (this.retornoDadosMedeicos) {
          this.Dados.peso = !this.retornoDadosMedeicos.peso ? "" : this.verificaCasaDecimal(this.retornoDadosMedeicos.peso);
          this.Dados.altura = !this.retornoDadosMedeicos.altura ? "" : this.verificaCasaDecimal(this.retornoDadosMedeicos.altura)
          this.Dados.iMC = this.retornoDadosMedeicos.imc
          this.Dados.pressao = this.retornoDadosMedeicos.pressao
          this.Dados.batimento = this.retornoDadosMedeicos.batimento
          this.Dados.temperatura = this.retornoDadosMedeicos.temperatura
        }

        if (this.idUsuarioExistente.clinicas && this.idUsuarioExistente.clinicas.length > 0) {
          this.retornoClinicas = this.idUsuarioExistente.clinicas
          this.clinicaCadastro = [];
          this.clinicas = [];
          this.idUsuarioExistente.clinicas.forEach((element: any) => {
            this.DadosClinicas.forEach((elemento: any) => {
              if (elemento.idClinica == element.idClinica) {
                this.clinicas.push(elemento)

              }
            })
          })
          this.clinicas = new FormControl(this.clinicas);
        }
      }


      this.ngxSmartModalService.getModal('UsuarioExistente').close();

    }
  }
  public NaoAceitarUsuarioExistente() {
    if (this.campoExitente == 'CPF') {
      this.Dados.cpf = ""
      this.campoExitente = ""
    }
    else if (this.campoExitente == 'EMAIL') {
      this.Dados.email = ""
      this.campoExitente = ""
    }
    else {
      this.Dados.telefoneMovel = ''
      this.campoExitente = ""

    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();

  }


  public validarCpf(value: any) {
    var valuecpf = value.replace(/[\.-]/g, "");
    this.campoCPFInvalido = false;
    var idUsuario = null
    if (this.retornoPessoa != undefined)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {

      this.campoCPFVazil = false;

      if (!this.validacao.cpf(value)) {
        this.campoCPFInvalido = true;
        return;
      }
      this.campoCPFInvalido = false;

      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(valuecpf, idUsuario, 'CPF').subscribe((retorno) => {

        if (retorno != null) {
          this.campoExitente = "CPF"
          this.mensagemPaciente = 'CPF já registrado no sistema Deseja utilizar o atendente desse cadastro?';
          this.idUsuarioExistente = retorno;
          this.ngxSmartModalService.getModal('UsuarioExistente').open();

          // this.CarregaAtendente(retorno.iduser)

          // this.tradutor.get('TELACADASTROUSUARIO.USUARIOJAREGISTRADOVERIFIQUEACLINICA').subscribe((res: string) => {
          //   ;
          //   this.AlgumErro(true, res);
          // });
        }
      })
    }
    else
      this.campoCPFVazil = true;
  }


  campoEmailInvalido: boolean = false;
  campoEmailVazil = false;

  public validarEmailPaciente(value: any) {

    this.usuarioInvalidoEmail = false;
    var idUsuario
    if (this.retornoContato != undefined)
      var idUsuario = this.retornoUsuario.idUsuario
    if (value != "") {
      this.campoEmailVazil = false;
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        return;
      }
      this.campoEmailInvalido = false
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'EMAIL').subscribe((retorno) => {
        ;

        if (retorno != null) {
          this.campoExitente = "EMAIL"
          this.mensagemPaciente = 'Email já registrado no sistema Deseja utilizar o paciente desse cadastro?';
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();

          // this.usuarioInvalidoEmail = true;

          // this.tradutor.get('TELACADASTROPACIENTE.EMAILJAREGISTRADO').subscribe((res: string) => {
          //   ;
          //   this.AlgumErro(true, res);
          // });
          // document.getElementById('email').focus();
        }
      }, () => {
        this.spinner.hide();
      })
    }
    else
      this.campoEmailVazil = true;
  }



  public validarTelMovel(value: any) {
    this.usuarioInvalidoTelefone = true;
    var idUsuario
    if (this.retornoContato != undefined)
      var idUsuario = this.retornoUsuario.idUsuario
    if (value != "") {
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'TelMovel').subscribe((retorno) => {
        ;

        if (retorno != null) {
          this.campoExitente = "Tel"
          this.mensagemPaciente = 'Telefone já registrado no sistema Deseja utilizar o paciente desse cadastro?';
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();

          // this.usuarioInvalidoTelefone = true;

          // this.tradutor.get('TELACADASTROPACIENTE.TELEFONEJAREGISTRADO').subscribe((res: string) => {
          //   ;
          //   this.AlgumErro(true, res);
          // });
          // document.getElementById('telcelular').focus();
        }
      }, () => {
        this.spinner.hide();
      })
    }
  }




  public CarregaPaciente(id: any) {

    try {
      this.pacienteService.getPacienteEdit(id).subscribe((retorno) => {



        this.retornoPessoa = retorno.paciente.pessoa;
        this.retornoPaciente = retorno.paciente.cliente;
        this.retornoEndereco = retorno.paciente.endereco;
        this.retornoContato = retorno.paciente.contato;
        this.retornoUsuario = retorno.paciente.usuario;
        this.retornoDadosMedeicos = retorno.paciente.dadosMedicos;
        this.Dados.nome = this.retornoPessoa.nomePessoa;
        this.Dados.dtaNascimento = this.retornoPessoa.dtaNascimento ? new Date(this.retornoPessoa.dtaNascimento).toLocaleDateString() : "";
        this.Dados.cpf = this.retornoPessoa.cpf;
        this.Dados.profissao = this.retornoPessoa.profissao;

        this.carregaDadosSexo()
        if (this.retornoPessoa.sexo == "Masculino") {
          this.campoSexo = this.masc
        } else if (this.retornoPessoa.sexo == "Feminino") {
          this.campoSexo = this.fem
        } else
          this.campoSexo = null

        this.Dados.planosaude = this.retornoPaciente.planoSaude;
        this.Dados.convenio = this.retornoPaciente.idConvenio;
        this.Dados.matricula = this.retornoPaciente.matricula;
        this.Dados.procedencia = this.retornoPaciente.procedencia;
        this.Dados.nascionalidade = this.retornoPessoa.nascionalidade;
        this.Dados.naturalidade = this.retornoPessoa.naturalidade;
        if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefone = this.retornoContato.telefone;

        if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

        if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;
        this.Dados.email = this.retornoPessoa.email;
        if (this.retornoEndereco) {
          this.Dados.rua = this.retornoEndereco.rua
          this.Dados.numero = this.retornoEndereco.numero
          this.Dados.complemento = this.retornoEndereco.complemento
          this.Dados.cep = this.retornoEndereco.cep
          this.Dados.bairro = this.retornoEndereco.bairro
          if (this.retornoEndereco.idCidade != null) {
            this.cidadeService.getCidades().then((retornaCidade) => {
              this.dadosCidade = retornaCidade
              var sigle = this.dadosCidade.filter((c: any) => c.idCidade == this.retornoEndereco.idCidade)
              this.Dados.uf = sigle[0].siglasUf

              this.dadosCidadeUf = this.dadosCidade.filter((c: any) => c.siglasUf == sigle[0].siglasUf);
              this.Dados.idCidade = this.retornoEndereco.idCidade
              this.spinner.hide();
            }, () => {
              this.spinner.hide();
            })
          }


        }

        this.Dados.foto = this.retornoPessoa.foto;
        if (retorno.paciente.imagem64 != null && retorno.paciente.imagem64 != "")
          this.ImagemPessoa = retorno.paciente.imagem64;




        if (this.retornoDadosMedeicos) {
          this.Dados.peso = !this.retornoDadosMedeicos.peso ? "" : this.verificaCasaDecimal(this.retornoDadosMedeicos.peso);
          this.Dados.altura = !this.retornoDadosMedeicos.altura ? "" : this.verificaCasaDecimal(this.retornoDadosMedeicos.altura)
          this.Dados.iMC = this.retornoDadosMedeicos.imc
          this.Dados.pressao = this.retornoDadosMedeicos.pressao
          this.Dados.batimento = this.retornoDadosMedeicos.batimento
          this.Dados.temperatura = this.retornoDadosMedeicos.temperatura
        }

        this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {

          this.DadosClinicas = retornaClinicas
          if (retorno.paciente.clinicas && retorno.paciente.clinicas.length > 0) {
            this.retornoClinicas = retorno.paciente.clinicas
            this.clinicaCadastro = [];
            this.clinicas = [];
            // var clinicaCadastro: any = [];
            retorno.paciente.clinicas.forEach((element: any) => {
              this.DadosClinicas.forEach((elemento: any) => {
                if (elemento.idClinica == element.idClinica) {
                  this.clinicas.push(elemento)

                }
              })
            })
            this.clinicas = new FormControl(this.clinicas);
          }
          this.spinner.hide();
        }, () => {
          this.snackbarAlert.falhaSnackbar("erro no retorno especialidade")
          this.spinner.hide();
        })

        this.spinner.hide();
      }, () => {
        this.tradutor.get('TELACADASTROPACIENTE.ERROAOCARREGARPACIENTE').subscribe((res: string) => {
          this.snackbarAlert.falhaSnackbar(res);
        });
        this.spinner.hide();
      })
    } catch (error) {

      this.tradutor.get('TELACADASTROPACIENTE.ERROAOCARREGARPACIENTE').subscribe((res: string) => {
        ;
        this.snackbarAlert.falhaSnackbar(res);
      });
    }
  }

  mascaraPressao(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    if (v.length < 3) {
      v = v.replace(/(\d{1})(\d{1})$/, "$1/$2");
    } else if (v.length < 4) {
      v = v.replace(/(\d{2})(\d{1})$/, "$1/$2");
    }
    else {
      v = v.replace(/(\d{1})(\d{2})$/, "$1/$2");
    }
    (<HTMLInputElement>evento.target).value = v
  }




  public mascaraText(evento: KeyboardEvent, campo: string) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    if (campo == 'Nascionalidade')
      this.Dados.nascionalidade = v;
    if (campo == 'Naturalidade')
      this.Dados.naturalidade = v;
    if (campo == 'Nome')
      this.Dados.nome = v;
    // (<HTMLInputElement>evento.target).value = v
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v
  }

  CalculoIMC() {

    if (this.Dados.altura && this.Dados.peso) {

      var valorAltura = (document.getElementById('CampoAltura') as HTMLInputElement)['value'];
      var valorPeso = (document.getElementById('CampoPeso') as HTMLInputElement)['value'];
      var altura = Number(valorAltura) * Number(valorAltura)
      var calc = Number(valorPeso) / altura
      this.Dados.iMC = calc.toFixed(2);
    }
    else
      this.Dados.iMC = null;
  }

  public mascaraPeso(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }

    (<HTMLInputElement>evento.target).value = v
  }


  public mascaraAltura(evento: KeyboardEvent) {

    // var v = (<HTMLInputElement>evento.target).value;
    // var v = v, integer = v.split('.')[0];
    // v = v.replace(/\D/g, "");
    // v = v.replace(/^[0]+/, "");

    // if (v.length <= 2 || !integer) {
    //   if (v.length === 1) v = '0.0' + v;
    //   if (v.length === 2) v = '0.' + v;
    // } else {
    //   v = v.replace(/(\d{1})(\d{2})$/, "$1.$2");
    //   }

    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/^[0]+/, "");
    v = v.toString().replace(/\D/g, "");
    if (v.length >= 3) {
      v = v.toString().replace(/(\d{1})(\d{2})$/, "$1.$2");
    } else {
      v = v.toString().replace(/(\d{1})(\d{1})$/, "$1.$2");
    }
    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraTemperatura(evento: KeyboardEvent) {

    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/^[0]+/, "");
    v = v.toString().replace(/\D/g, "");
    if (v.length == 3) {
      v = v.toString().replace(/(\d{1})(\d{1})$/, "$1.$2");
    } else if (v.length > 3) {
      v = v.toString().replace(/(\d{1})(\d{2})$/, "$1.$2");
    }
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor: any) {
    if (valor.toString().split('.').length < 2) {

      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {

      valor = valor + "0"
    }
    return valor;
  }

  NaoEditado(numeroTelefone: any) {
    if (numeroTelefone != null && numeroTelefone != '' && numeroTelefone != undefined)
      return numeroTelefone.replace(/\D/g, "");

    else return
  }


  public Submit() {
    try {

      this.ValidaDtaChange();
      this.ValidaTelefoneMovelChange();
      this.ValidaClinicas();

      this.validarCampos();

      if (this.showMessageError == true) {
        return;
      }


      var pessoa = new Pessoa();
      var cliente = new Cliente();
      var endereco = new Endereco();
      var contato = new Contato();
      var dadosUser = new DadosMedicosUsuario();

      if (this.retornoPaciente) {
        cliente.idCliente = this.retornoPaciente.idCliente;
        cliente.idUsuarioGerador = this.retornoPaciente.idUsuarioGerador;
        cliente.dtaCadastro = this.retornoPaciente.dtaCadastro;

        // cliente.pessoa.idPessoa  = this.retornoPessoa.idPessoa;
      }
      if (this.retornoPessoa) {
        pessoa.idPessoa = this.retornoPessoa.idPessoa;


        if (this.retornoPessoa.idEndereco) {
          pessoa.idEndereco = this.retornoPessoa.idEndereco;
          endereco.idEndereco = this.retornoPessoa.idEndereco;
        }

        if (this.retornoEndereco)
          endereco.dtaCadastro = this.retornoEndereco.dtaCadastro;

        pessoa.idUsuarioAcesso = this.retornoPessoa.idUsuarioAcesso;

        pessoa.idContato = this.retornoPessoa.idContato;
        // cliente.idUsuarioGerador = this.retornoPaciente.idUsuarioGerador;
        contato.idContato = this.retornoPessoa.idContato;
        contato.idUsuarioGerador = this.retornoContato.idUsuarioGerador;
        contato.dtaCadastro = this.retornoContato.dtaCadastro;

      }
      else
        pessoa.idUsuarioGerador = contato.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

      cliente.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

      pessoa.nomePessoa = this.Dados.nome;
      pessoa.dtaNascimento = (document.getElementById('DtaNasc') as HTMLInputElement)['value'] != '' ? this.validacao.Convertdata((document.getElementById('DtaNasc') as HTMLInputElement)['value']) : null;
      pessoa.cpf = this.Dados.cpf;
      pessoa.profissao = this.Dados.profissao;


      if (this.campoSexo == this.masc) {
        this.Dados.sexo = "Masculino"
      } else if (this.campoSexo == this.fem) {
        this.Dados.sexo = "Feminino"
      } else
        this.Dados.sexo = null

      pessoa.sexo = this.Dados.sexo;

      cliente.planoSaude = this.Dados.planosaude;
      cliente.idConvenio = this.Dados.convenio;
      cliente.matricula = this.Dados.matricula;
      cliente.procedencia = this.Dados.procedencia;
      pessoa.naturalidade = this.Dados.naturalidade;
      pessoa.nascionalidade = this.Dados.nascionalidade;
      cliente.flgInativo = false;
      pessoa.foto = this.Dados.foto;
      if (this.ImagemPessoa != "assets/build/img/userdefault.png")
        pessoa.imagem64 = this.ImagemPessoa;


      contato.telefone = this.NaoEditado(this.Dados.telefone);
      contato.telefoneMovel = this.NaoEditado(this.Dados.telefoneMovel);
      contato.telefoneComercial = this.NaoEditado(this.Dados.telefoneComercial);
      // contato.email = this.Dados.email;
      pessoa.email = this.Dados.email;
      contato.flgInativo = false;



      endereco.rua = this.Dados.rua
      endereco.numero = this.Dados.numero
      endereco.complemento = this.Dados.complemento
      endereco.cep = this.Dados.cep
      endereco.bairro = this.Dados.bairro

      endereco.idCidade = this.Dados.idCidade
      endereco.flgInativo = false


      dadosUser.Altura = (document.getElementById('CampoAltura') as HTMLInputElement)['value']
      dadosUser.Peso = (document.getElementById('CampoPeso') as HTMLInputElement)['value']
      dadosUser.Batimento = this.Dados.batimento;
      dadosUser.IMC = this.Dados.iMC;
      dadosUser.Pressao = (document.getElementById('campoPressao') as HTMLInputElement)['value']
      dadosUser.Temperatura = this.Dados.temperatura;
      dadosUser.FlgInativo = false;
      dadosUser.DtaCadastro = new Date();
      dadosUser.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();


      pessoa.endereco = endereco;
      pessoa.contato = contato;
      cliente.DadosCorporal = dadosUser;
      cliente.pessoa = pessoa;
      // this.DadosClinicas;




      var Clinic: any = []
      this.clinicas.value.forEach((element: any) => {

        Clinic.push({
          idClinica: element.idClinica,
        })
      });

      if (Clinic.length > 0)
        cliente.clinicas = Clinic;

      this.pacienteService.salvarPaciente(cliente).subscribe((retorno) => {


        this.pacienteService.atualizaDadosMes$.emit(retorno)

        this.Dados = []
        this.LimparCampos()

        this.tradutor.get('TELACADASTROPACIENTE.USUARIOSALVOCOMSUCESSO').subscribe((res: string) => {
          ;
          this.snackbarAlert.sucessoSnackbar(res);
        });
        this.spinner.hide();
      }, () => {
        this.tradutor.get('TELACADASTROPACIENTE.ERROAOSALVAR').subscribe((res: string) => {
          ;
          this.snackbarAlert.falhaSnackbar(res);
        });

        this.spinner.hide();
      })
    } catch (error) {

      this.tradutor.get('TELACADASTROPACIENTE.ERROAOSALVAR').subscribe((res: string) => {
        ;
        this.snackbarAlert.falhaSnackbar(res);
      });
    }
  }



  ValidaDtaChange() {
    const dta = (document.getElementById('DtaNasc') as HTMLInputElement)['value'];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.Dtanasc = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }

  ValidaDta(dta: any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.Dtanasc = false;
    }
    else if (patternValidaData.test(dta)) {
      this.Dtanasc = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }

  ValidaTelefone(tle: any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle: any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelMovVal = false;
      this.TelMovValVasil = true;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovVal = false;
      this.TelMovValVasil = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
      this.TelMovValVasil = false;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneMovelChange() {
    const tle = (document.getElementById('telcelular') as HTMLInputElement)['value'];
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelMovVal = false;
      this.TelMovValVasil = true;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovVal = false;
      this.TelMovValVasil = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
      this.TelMovValVasil = false;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneComercial(tle: any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
    }
    else
      this.TelComVal = false
  }

  ValidaClinicas(cli: any = []) {
    this.clinicas.value.forEach((element: any) => {

      cli.push({
        idClinica: element.idClinica,
      })
    });
    if (cli.length > 0) {
      this.clinicaVasil = false;
      this.clinicaVal = false;
    }
    else {
      this.clinicaVasil = false;
      this.clinicaVal = true;
    }
  }

  LimparCampos() {
    this.Dados = [];
    this.campoSexo = null

    this.clinicas = new FormControl([]);
    this.cpf.markAsUntouched();
    this.Nome.markAsUntouched();
    this.email.markAsUntouched();
    // this.DtaNasc.markAsUntouched();
    (document.getElementById("DtaNasc") as HTMLInputElement)['value'] = ""
    this.Dtanasc = false;
    this.TelVal = false;
    this.TelMovVal = false;
    this.TelMovValVasil = false;
    this.TelMovLimpa = false;
    this.TelComVal = false;
    this.clinicaVasil = true;
    this.clinicaVal = false;

    this.campoEmailInvalido = false;


    this.planos.markAsUntouched();
    // this.cell.markAsUntouched();
    this.ImagemPessoa = "assets/build/img/userdefault.png";
  }


  public validarCampos() {
    this.showMessageError = false;
    this.cpf.markAsTouched();
    this.Nome.markAsTouched();
    this.email.markAsTouched();
    // this.DtanascVasil = false;
    this.planos.markAsTouched();
    // this.cell.markAsTouched();

    if (this.Dados.cpf && !this.validacao.cpf(this.Dados.cpf)) {
      this.campoCPFInvalido = true;
      return;
    }

    if (this.Dados.cpf == undefined || !this.Dados.cpf.trim())
      this.campoCPFVazil = true;

    if (this.Dados.email == undefined || !this.Dados.email.trim())
      this.campoEmailVazil = true;

    if (this.Dados.nome == undefined || !this.Dados.nome.trim()
      || this.Dados.email == undefined || !this.Dados.email.trim()
      || this.Dados.cpf == undefined || !this.Dados.cpf.trim()
      || this.Dados.telefoneMovel == undefined || !this.Dados.telefoneMovel.trim()
      || this.TelMovVal == true
      || this.TelVal == true || this.TelComVal == true
      || this.clinicaVal == true || this.clinicaVasil == true
      || this.campoEmailInvalido == true
      || this.campoEmailVazil == true || this.campoCPFInvalido == true || this.campoCPFVazil == true
    ) {

      this.showMessageError = true;

      if (this.TelMovVal != true && this.TelMovVal != false || this.TelMovLimpa == false) {
        this.TelMovValVasil = true;
        this.TelMovLimpa = true;
      }
      if (this.clinicaVal != true && this.clinicaVal != false) {
        this.clinicaVasil = true;
      }
      if (this.clinicaVasil == true) {
        this.clinicaVal = true;
      }
      document.documentElement.scrollTop = 0;
    }


  }
  Nome = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  cpf = new FormControl('', [Validators.required, Validators.maxLength(14)])
  planos = new FormControl('', [Validators.required, Validators.maxLength(11)])


  getErrorMessageNome() {
    return this.Nome.hasError('required') ? 'TELACADASTROPACIENTE.ERROCAMPO' :
      this.Nome.hasError('Nome') ? 'TELACADASTROPACIENTE.ERRONAOEVALIDO' :
        '';

  }


  CarregaConvenio() {

    this.convenioService.getConvenios(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      this.dadosConvenio = retorno;
      this.spinner.hide();
    })
  }



  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELACADASTROPACIENTE.ERROCAMPO' :
      this.email.hasError('email') ? 'TELACADASTROPACIENTE.ERRONAOEVALIDO' :
        '';

  }
  getErrorMessageCPF() {
    return this.cpf.hasError('required') ? 'TELACADASTROPACIENTE.ERROCAMPO' :
      this.cpf.hasError('cpf') ? 'TELACADASTROPACIENTE.ERRONAOEVALIDO' :
        '';

  }

  geterrorMessagePlanoS() {
    return this.planos.hasError('required') ? 'TELACADASTROPACIENTE.ERROCAMPO' :
      this.planos.hasError('Plano de Saúde') ? 'TELACADASTROPACIENTE.ERRONAOEVALIDO' :
        '';
  }

  // ErroCarregar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROPACIENTE.ERROAOCARREGAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }
  CortarImagem() {
    this.ngxSmartModalService.getModal('ModalFoto').close()
    this.ImagemPessoa = this.imagemCorte
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCorte = event.base64;
    ;
  }

  AlterarImagemPessoa($event: any): void {
    this.ngxSmartModalService.getModal('ModalFoto').open();
    this.imageChangedEvent = $event
    // this.readThis($event.target);
  }


  LimpaCampoFile() {
    (document.getElementById('imageperfilusuario') as HTMLInputElement)['value'] = '';
  }


  imageLoaded() {
    this.showCropper = true;

  }
  cropperReady() {

  }
  loadImageFailed() {
    ;
  }

  transform: ImageTransform = {
    rotate: 0,
    flipH: false,
    flipV: false
  };

  rotateLeft() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! - 90 };
  }

  rotateRight() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! + 90 };
  }

  flipHorizontal() {
    this.transform = { ...this.transform, flipH: !this.transform.flipH };
  }

  flipVertical() {
    this.transform = { ...this.transform, flipV: !this.transform.flipV };
  }

}
