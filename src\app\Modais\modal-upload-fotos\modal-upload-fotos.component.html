<div class="modal-container">
    <div class="modal-header">
        <h2>Carregamento de Imagem</h2>
        <button class="close-button" (click)="FecharModal()">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <div class="modal-container">
        <!-- Seção de upload -->
        <div *ngIf="!showImage" class="upload-section">
            <label for="fileInput" class="custom-file-upload">
                <mat-icon>cloud_upload</mat-icon>
                <span>Selecionar imagem</span>
            </label>
            <input type="file" id="fileInput" (change)="onFileChange($event)" accept="image/*" #fileInput>
            <p *ngIf="fileName" class="file-name">{{fileName}}</p>
        </div>

        <!-- Seção de exibição de imagem -->
        <div *ngIf="showImage" class="image-display-section">
            <div class="image-container">
                <img *ngIf="imageBase64" [src]="imageBase64" class="responsive-image" (load)="imageLoaded()"
                    (error)="loadImageFailed()">
            </div>

            <div class="image-actions">
                <button (click)="trocarImagem()" class="action-button">
                    <mat-icon>refresh</mat-icon>
                    <span>Trocar imagem</span>
                </button>
                <button (click)="confirmImage()" class="action-button confirm">
                    <mat-icon>check</mat-icon>
                    <span>Confirmar</span>
                </button>
            </div>
        </div>
    </div>
</div>