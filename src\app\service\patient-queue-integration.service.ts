import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { RelatorioExameApiService, PatientRegistrationRequest } from './relatorio-exame-api.service';

@Injectable({
  providedIn: 'root'
})
export class PatientQueueIntegrationService {

  constructor(
    private relatorioExameApiService: RelatorioExameApiService
  ) { }

  /**
   * Integrates patient data collection with queue registration
   * This method should be called when a patient joins the queue
   */
  registerPatientInQueue(patientData: PatientQueueRegistrationData): Observable<PatientQueueResult> {
    try {
      // Convert patient data to API format (all fields as strings)
      const apiPatientData: PatientRegistrationRequest = {
        token: patientData.token, // Use provided token from frontend
        nome: patientData.nome,
        cpf: patientData.cpf,
        email: patientData.email,
        telefone: patientData.telefone,
        dataNascimento: patientData.dataNascimento,
        alergias: patientData.alergias,
        doencasPrevias: patientData.doencasPrevias,
        sintomas: patientData.sintomas,
        intensidade: patientData.intensidadeDor,
        duracaoSintomas: patientData.duracaoSintomas,
        observacoesAdicionais: patientData.observacoesAdicionais,
        pressaoSistolica: patientData.pressaoSistolica,
        pressaoDiastolica: patientData.pressaoDiastolica,
        oxigenacao: patientData.saturacaoOxigenio,
        batimento: patientData.frequenciaCardiaca,
        temperatura: patientData.temperatura
      };

      return this.relatorioExameApiService.registerPatient(apiPatientData)
        .pipe(
          map(response => ({
            success: response.success,
            message: response.message,
            patientToken: response.token || null,
            patientName: response.patientData?.nome || null,
            queueType: patientData.queueType || 'consulta',
            errors: response.errors || []
          })),
          catchError(error => {
            console.error('Error registering patient in queue:', error);
            return of({
              success: false,
              message: 'Erro ao registrar paciente na fila',
              patientToken: null,
              patientName: null,
              queueType: null,
              errors: [error.message || 'Erro desconhecido']
            });
          })
        );
    } catch (error) {
      console.error('Error in registerPatientInQueue:', error);
      return throwError(() => 'Erro ao processar dados do paciente');
    }
  }

  /**
   * Registers patient with basic information (for simple queue registration)
   */
  registerBasicPatientInQueue(basicData: BasicPatientData): Observable<PatientQueueResult> {
    const patientData: PatientQueueRegistrationData = {
      token: basicData.token, // Use provided token
      nome: basicData.nome,
      cpf: basicData.cpf,
      email: basicData.email,
      telefone: basicData.telefone,
      dataNascimento: basicData.dataNascimento,
      clinicId: basicData.clinicId,
      doctorId: basicData.doctorId,
      queueType: basicData.queueType || 'consulta'
    };

    return this.registerPatientInQueue(patientData);
  }

  /**
   * Updates patient data after initial registration
   */
  updatePatientDataInQueue(token: string, additionalData: Partial<PatientQueueRegistrationData>): Observable<PatientQueueResult> {
    // Convert to API format for update - only include fields that are provided
    const updateData: any = {
      id: 0, // Will be resolved by token
      token: token
    };

    // Only add fields that are provided in additionalData
    if (additionalData.nome !== undefined) updateData.nome = additionalData.nome;
    if (additionalData.cpf !== undefined) updateData.cpf = additionalData.cpf;
    if (additionalData.email !== undefined) updateData.email = additionalData.email;
    if (additionalData.telefone !== undefined) updateData.telefone = additionalData.telefone;
    if (additionalData.dataNascimento !== undefined) updateData.dataNascimento = additionalData.dataNascimento;
    if (additionalData.alergias !== undefined) updateData.alergias = additionalData.alergias;
    if (additionalData.doencasPrevias !== undefined) updateData.doencasPrevias = additionalData.doencasPrevias;
    if (additionalData.sintomas !== undefined) updateData.sintomas = additionalData.sintomas;
    if (additionalData.intensidadeDor !== undefined) updateData.intensidade = additionalData.intensidadeDor;
    if (additionalData.duracaoSintomas !== undefined) updateData.duracaoSintomas = additionalData.duracaoSintomas;
    if (additionalData.observacoesAdicionais !== undefined) updateData.observacoesAdicionais = additionalData.observacoesAdicionais;
    if (additionalData.pressaoSistolica !== undefined) updateData.pressaoSistolica = additionalData.pressaoSistolica;
    if (additionalData.pressaoDiastolica !== undefined) updateData.pressaoDiastolica = additionalData.pressaoDiastolica;
    if (additionalData.saturacaoOxigenio !== undefined) updateData.oxigenacao = additionalData.saturacaoOxigenio;
    if (additionalData.frequenciaCardiaca !== undefined) updateData.batimento = additionalData.frequenciaCardiaca;
    if (additionalData.temperatura !== undefined) updateData.temperatura = additionalData.temperatura;

    return this.relatorioExameApiService.updatePatient(updateData)
      .pipe(
        map(response => ({
          success: response.success,
          message: response.message,
          patientToken: token,
          patientName: response.patientData?.nome || null,
          queueType: null,
          errors: response.errors
        })),
        catchError(error => {
          console.error('Error updating patient data:', error);
          return of({
            success: false,
            message: 'Erro ao atualizar dados do paciente',
            patientToken: token,
            patientName: null,
            queueType: null,
            errors: [error.message || 'Erro desconhecido']
          });
        })
      );
  }

  /**
   * Validates patient token
   */
  validatePatientToken(token: string): Observable<TokenValidationResult> {
    return this.relatorioExameApiService.getPatientDataByToken(token)
      .pipe(
        map(response => ({
          isValid: response.success,
          patientName: response.patient?.name || null,
          registrationDate: response.patient?.registrationDate || null,
          message: response.message
        })),
        catchError(error => {
          console.error('Error validating token:', error);
          return of({
            isValid: false,
            patientName: null,
            registrationDate: null,
            message: 'Erro ao validar token do paciente'
          });
        })
      );
  }

  /**
   * Gets patient data for doctor interface
   */
  getPatientDataForDoctor(token: string): Observable<DoctorPatientView> {
    return this.relatorioExameApiService.getPatientDataByToken(token)
      .pipe(
        map(response => {
          if (response.success && response.patient) {
            return {
              success: true,
              patient: {
                name: response.patient.name,
                cpf: response.patient.cpf,
                email: response.patient.email,
                phone: response.patient.phone,
                age: response.patient.age,
                birthDate: response.patient.birthDate,
                allergies: response.patient.allergies,
                previousDiseases: response.patient.previousDiseases,
                symptoms: response.patient.symptoms,
                vitalSigns: response.patient.vitalSigns,
                additionalObservations: response.patient.additionalObservations,
                registrationDate: response.patient.registrationDate,
                hasPersonRecord: response.patient.hasPersonRecord
              },
              message: response.message,
              errors: response.errors
            };
          } else {
            return {
              success: false,
              patient: null,
              message: response.message,
              errors: response.errors
            };
          }
        }),
        catchError(error => {
          console.error('Error getting patient data for doctor:', error);
          return of({
            success: false,
            patient: null,
            message: 'Erro ao carregar dados do paciente',
            errors: [error.message || 'Erro desconhecido']
          });
        })
      );
  }
}

// Interface definitions

export interface PatientQueueRegistrationData {
  // Token Information
  token: string; // Pre-generated token that matches SignalR guest token

  // Personal Information
  nome: string;
  cpf: string;
  email?: string;
  telefone?: string;
  dataNascimento?: string;

  // Medical Information
  alergias?: string;
  doencasPrevias?: string;
  sintomas?: string;
  intensidadeDor?: string; // 1-10 scale
  duracaoSintomas?: string; // in days
  observacoesAdicionais?: string;

  // Vital Signs
  pressaoSistolica?: string;
  pressaoDiastolica?: string;
  saturacaoOxigenio?: string;
  frequenciaCardiaca?: string;
  temperatura?: string; // in Celsius (will be converted to tenths)

  // Queue Information
  queueType?: string;
  clinicId?: number;
  doctorId?: number;
}

export interface BasicPatientData {
  token: string; // Pre-generated token that matches SignalR guest token
  nome: string;
  cpf: string;
  email?: string;
  telefone?: string;
  dataNascimento?: string;
  queueType?: string;
  clinicId?: number;
  doctorId?: number;
}

export interface PatientQueueResult {
  success: boolean;
  message: string;
  patientToken: string | null;
  patientName: string | null;
  queueType: string | null;
  errors: string[];
}

export interface TokenValidationResult {
  isValid: boolean;
  patientName: string | null;
  registrationDate: Date | null;
  message: string;
}

export interface DoctorPatientView {
  success: boolean;
  patient: {
    name: string;
    cpf: string;
    email: string;
    phone: string;
    age: number | null;
    birthDate: Date | null;
    allergies: string;
    previousDiseases: string;
    symptoms: any;
    vitalSigns: any;
    additionalObservations: string;
    registrationDate: Date;
    hasPersonRecord: boolean;
  } | null;
  message: string;
  errors: string[];
}
