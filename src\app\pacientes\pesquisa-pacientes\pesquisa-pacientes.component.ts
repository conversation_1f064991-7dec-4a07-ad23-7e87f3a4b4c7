import { Component, OnInit } from '@angular/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { PacienteService } from 'src/app/service/pacientes.service';
// import { NgxSpinnerService } from 'ngx-spinner';

import { MedicoService } from 'src/app/service/medico.service';
import { EnvioEmailService } from 'src/app/service/envioEmail.service';
import { ConsultaService } from 'src/app/service/consulta.service';
import {
  SwiperConfigInterface,
} from 'ngx-swiper-wrapper';
import { TranslateModule, TranslatePipe, TranslateService } from '@ngx-translate/core';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { EnumTipoUsuario } from 'src/app/Util/tipoUsuario';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { Consulta } from 'src/app/model/consulta';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { ControleModaisService } from 'src/app/service/controle-modais.service';
import { PacienteModelview } from 'src/app/model/cliente';
import { LifelineComponent } from 'src/app/lifeLine/lifeline.component';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from 'src/app/Util/pipes/truncate.pipe';
import { MatDividerModule } from '@angular/material/divider';
import { MatRadioModule } from '@angular/material/radio';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatTooltipModule} from '@angular/material/tooltip';


@Component({
    selector: 'app-pesquisa-pacientes',
    templateUrl: './pesquisa-pacientes.component.html',
    styleUrls: ['./pesquisa-pacientes.component.scss'],
    animations: [trigger('openClose', [
            state('open', style({
                opacity: '1',
                display: 'block'
            })),
            state('closed', style({
                opacity: '0',
                display: 'none'
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ]),
        ])
    ],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      RouterModule,
      NgxSmartModalModule,
      NgSelectModule,
      TruncatePipe,
      MatDividerModule,
      MatRadioModule,
      TranslatePipe,
      MatSlideToggleModule,
      MatTooltipModule
    ]
})
export class PesquisaPacientesComponent implements OnInit {

  bOcultaCarregaMais = false;
  qtdRegistros = 10;
  DadosUsuario: any;
  idUsuarioDelet: any;
  idUsuario = 0;
  Delete: any;
  // name = 'SnackBarConfirmação';

  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔'
  // actionButtonLabel: string = 'Fechar';
  // emailenviado: string = 'Email Enviado. ✔';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  // sendemail: string = 'E-mail enviado com sucesso.  ✔'
  email?: boolean;
  flgEmail: boolean = false;

  idcliente?: number;
  nomecliente?: string;
  idEspecialidade: number = 0;
  idMedico?: number | null = 0;

  motivoExclusao = ''
  campoExclusaoPreenchido: boolean = true;

  DadosTab: PacienteModelview[] = [];
  pesquisa: string = '';
  cpf?: string;
  nomePaciente?: string;
  DadosEspecialidade: any = [];
  ListaMedicos: any = [];
  dadosLifeLine: any;
  idEmail = 0;
  DadosConsultaLifeLine: any
  dados = false;
  dadosAnonimo?: string;
  MedicoAnonimo?: string;
  Dataanonimo?: string;
  Foto: boolean = false;
  inativos: boolean = false;
  legenda = false;
  // usuario: Usuario;

  permissaoBotaoPerfil: boolean = true;

  usuarioExclusao: any;
  motivoDaExclusao: any;

  constructor(
    private spinner: SpinnerService,
    private medicoService: MedicoService,
    private pacienteService: PacienteService,
    public ngxSmartModalService: NgxSmartModalService,
    private router: Router,
    private emailService: EnvioEmailService,
    private consultaService: ConsultaService,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    private snackbarAlert: AlertComponent,
    private controleModaisService: ControleModaisService
  ) { }
  // @Output() FadeIn: string;
  // isOpen = false;


  loginComMedico: boolean = false;

  ngOnInit() {
    this.GetListaPaciente()

    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico)
      this.loginComMedico = true
    else
      this.loginComMedico = false


    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente)
      this.permissaoBotaoPerfil = false
    else
      this.permissaoBotaoPerfil = true

  }

  AbrirLifeLine(idPaciente: number) {

    this.controleModaisService.AbrirModalComponente(LifelineComponent, {
      idPaciente: idPaciente
    }, 'medium');
  }

  toggle: any = {}

  DadosPaciente(id: any) {
    this.dados = false
    this.DadosConsultaLifeLine = []
    this.consultaService.GetDadosLifeLine(id).subscribe((retorno) => {
      this.DadosConsultaLifeLine = retorno
      if (this.DadosConsultaLifeLine.anonimo != null) {
        this.dadosAnonimo = this.DadosConsultaLifeLine.anonimo
        this.MedicoAnonimo = this.DadosConsultaLifeLine.medico
        this.Dataanonimo = this.DadosConsultaLifeLine.dtaConsulta

      }
      this.dados = true

    }, () => {
      this.spinner.hide();
    })
  }


  mandaEmail() {  
    if (this.idEmail != 0) {
      this.spinner.show();
      this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe((retorno) => {
        retorno;
        this.snackbarAlert.sucessoSnackbar("Email enviado com sucesso!");
        this.GetListaPaciente();
        this.ngxSmartModalService.getModal('emailUsuario').close();
        this.idEmail = 0
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }
  }


  mandaEmailAtivarUser() {
    if (this.idEmail != 0) {
      this.spinner.show();
      this.emailService.EnviarEmailBoasVindas(this.idEmail).subscribe((retorno) => {
        retorno;
        if (this.idPaciente > 0)
          this.idUsuarioDelet = this.idPaciente

        this.pacienteService.AtivarPaciente(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
          this.Delete = retorno;
          this.idPaciente = 0

          if (this.Delete == true) {
            this.snackbarAlert.sucessoSnackbar('Paciente Ativado com sucesso!');

            this.ngxSmartModalService.getModal('emailUsuario').close();
            this.idEmail = 0
            this.GetListaPaciente();
          }
        })
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }
  }


  idPaciente: number = 0
  ModalEmail(obj: any) {
    this.idEmail = obj.idUsuarioAcceso
    this.idPaciente = obj.idCliente
    this.ngxSmartModalService.getModal('emailUsuario').open();
  }

  Editado(numeroTelefone: any) {

    numeroTelefone = numeroTelefone.replace(/\D/g, "");
    numeroTelefone = numeroTelefone.replace(/^(\d{2})(\d)/g, "($1) $2");
    numeroTelefone = numeroTelefone.replace(/(\d)(\d{4})$/, "$1-$2");

    return numeroTelefone;
  }


  ModalAgendarConsulta(id: any, nome: any) {

    if (id != null && nome != null) {
      this.idcliente = id;
      this.nomecliente = nome;
    }
    if (this.ListaMedicos.length == 0) {
      this.CarregaEspecialidade();
      // this.CarregaMedicos();
      this.medicoService.getMedicos(this.idEspecialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
          this.ListaMedicos = retorno.filter((c: any) => c.idUsuarioacesso == this.usuarioLogadoService.getIdUsuarioAcesso());

          this.idMedico = this.ListaMedicos[0].idMedico

          this.AgendarConsulta();
        } else {
          this.ListaMedicos = retorno;

          this.ngxSmartModalService.getModal('ModalAgenda').open()

        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }
  }


  AgendarConsulta() {
    var agenda = new Consulta();
    agenda.IdPaciente = this.idcliente
    agenda.IdMedico = this.idMedico!

    agenda.IdEspecialidade = this.idEspecialidade
    this.localStorageService.ConsultaAgenda = agenda;
    this.router.navigate(['/calendario']);

  }

  InativarUsuario() {


    if (this.motivoExclusao == "" || !this.motivoExclusao.trim()) {
      this.campoExclusaoPreenchido = false
      this.motivoExclusao = ""
    }
    else {

      this.pacienteService.inativarPaciente(this.idUsuarioDelet, this.motivoExclusao, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        this.Delete = retorno;
        if (this.Delete == true) {
          this.ngxSmartModalService.getModal('excluirItem').close();
          this.snackbarAlert.sucessoSnackbar("Paciente Inativado com Sucesso");
          this.GetListaPaciente();
          // this.ConcordoSnack(this.Delete);
        }
        else {

        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    }
  }


  AtivarUsuario() {
    try {
      if (this.idPaciente > 0)
        this.idUsuarioDelet = this.idPaciente

      this.pacienteService.AtivarPaciente(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        this.Delete = retorno;
        if (this.Delete == true) {
          this.idPaciente = 0
          this.ngxSmartModalService.getModal('ativarItem').close();
          this.snackbarAlert.sucessoSnackbar('Usuario Ativo com Sucesso');
          this.GetListaPaciente();
        }
        else {


          this.tradutor.get('TELAPESQUISAPACIENTES.ERROAOATIVAROUSUARIO').subscribe(() => {
            this.snackbarAlert.falhaSnackbar('Erro ao Ativar o Paciente. Tente Novamente.');
          });
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
        this.tradutor.get('TELAPESQUISAPACIENTES.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
          this.snackbarAlert.falhaSnackbar(res);
        });
      })

    } catch (error) {


      this.tradutor.get('TELAPESQUISAPACIENTES.ERROAOATIVAROUSUARIO').subscribe((res: string) => {
        ;
        this.snackbarAlert.falhaSnackbar(res);
      });
    }
  }
  public ValorUsuarioAtivar(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('ativarItem').open();
  }

  public ValorUsuario(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;

    this.motivoExclusao = ""
    this.campoExclusaoPreenchido = true;

    this.ngxSmartModalService.getModal('excluirItem').open();
  }

  CarregaMedicos() {
    this.idMedico = null;
    this.medicoService.getMedicos(this.idEspecialidade, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.ListaMedicos = retorno

      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  CarregaEspecialidade() {
    this.medicoService.getEspecialidade().then((retornaEspecialidade) => {
      this.DadosEspecialidade = retornaEspecialidade


      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  // ConcordoSnack(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;

  //     this.tradutor.get('TELAPESQUISAPACIENTES.USUARIOEXCLUIDOCOMSUCESSO').subscribe((res: string) => {
  //       ;
  //       this.snackbarAlert.sucessoSnackbar(res);
  //     });
  //   }
  //   this.concordo = value;
  //   this.concordomsg = false;
  // }

  // AtivaUsuario(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     this.snackbarAlert.sucessoSnackbar(mensagem);
  //   }
  //   this.concordo = value;
  //   this.concordomsg = false;
  // }


  public editUsuario(id: any) {

    if (id != "" && id != 0) {
      this.localStorageService.idCliente = id;
      this.router.navigate(['/paciente']);
    }

  }

  // AlgumErro(value, menssage) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     this.snackBar.open(menssage, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  //   this.concordo = value;
  //   this.concordomsg = false;
  // }



  // EnviarEmailSnack(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELAPESQUISAPACIENTES.EMAILENVIADOV').subscribe((res: string) => {
  //       ;
  //       this.snackbarAlert.sucessoSnackbar(res);
  //     });
  //   }
  //   this.flgEmail = false;
  // }


  public config: SwiperConfigInterface = {
    direction: 'horizontal',
    keyboard: true,
    loop: false,
    slidesPerView: 5,
    mousewheel: true,
    scrollbar: false,
    navigation: false,
    pagination: true
  };

  // ErroCarregar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELAPESQUISAPACIENTES.ERROAOCARREGAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }


  MotivoExclusaoCampo() {

    if (this.motivoExclusao == null || !this.motivoExclusao.trim())
      this.campoExclusaoPreenchido = false
  }



  AbrirModalMotivoExclusao(id: any) {

    this.pacienteService.GetMotivoExclusao(id).subscribe((retorno) => {
      this.spinner.hide();
      this.usuarioExclusao = retorno.usuarioExcluiu
      this.motivoDaExclusao = retorno.motivoExclusao

      this.ngxSmartModalService.getModal('MotivoExclusao').open();

    }, () => {
      this.spinner.hide();
    })

  }

  PerfilPaciente(obj: any) {
    if (obj != "" && obj != 0) {
      this.localStorageService.idPerfil = obj.idUsuarioAcceso;
      this.router.navigate(['/perfil']);
    }

  }


  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      // if (this.DadosTab[this.indexGlobal]['toggle']) {
      //   this.toggle[this.indexGlobal] = false;
      //   this.DadosTab[this.indexGlobal]['toggle'] = false;
      // }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index: any) {

    // if (this.indexGlobal != index) {
    //   this.toggle[this.indexGlobal] = false;
    //   this.DadosTab[this.indexGlobal]['toggle'] = false;
    // }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    // this.DadosTab[this.indexGlobal]['toggle'] = !this.DadosTab[this.indexGlobal]['toggle'];
  }

  ModalExportarPdf(idCliente: number, nome: string) {
    this.controleModaisService.AbreModalLifeLine(idCliente, nome);
  }

  GetListaPaciente(flgCarregarMais?: boolean) {
    this.spinner.show();

    var inicio = flgCarregarMais ? this.DadosTab.length : 0;
    var filtro = this.LimparFiltro(this.pesquisa);

    this.pacienteService.GetListaPaciente(inicio, this.qtdRegistros, filtro.trim(), this.inativos).subscribe((ret) => {
      ;

      if (flgCarregarMais) {
        for (let i = 0; i < ret.length; i++) {
          this.DadosTab.push(ret[i]);
        }
      } else {
        this.DadosTab = ret;
      };

      this.DadosTab.forEach(element => {
        if (element.tel != null && element.tel != '' && element.tel != undefined)
          element.tel = this.Editado(element.tel);
      });

      this.bOcultaCarregaMais = ret.length < this.qtdRegistros;

      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  };

  LimparFiltro(filtro: string): string {
    var ret = '';

    if (filtro.includes('@')) {
      ret = filtro.replace(/[^a-zA-Z0-9@._\-\s]/g, ''); // Caso Email
    } else {
      if (/[a-zA-Z]/.test(filtro)) {
        ret = filtro.replace(/[^a-zA-Z0-9\s]/g, ''); // Caso Nome
      } else {
        ret = filtro.replace(/[^a-zA-Z0-9]/g, '');
      }
    }

    return ret;
  }

}
