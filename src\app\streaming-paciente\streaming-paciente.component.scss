.patient-consultation-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Roboto', sans-serif;
  overflow: hidden;
}

/* Header da consulta */
.consultation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  z-index: 10;

  .patient-info {
    display: flex;
    align-items: center;
    gap: 15px;

    .patient-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: linear-gradient(45deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      
      mat-icon {
        font-size: 24px;
      }
    }

    .patient-details {
      h2 {
        margin: 0;
        font-size: 1.4rem;
        color: #333;
        font-weight: 500;
      }

      .patient-type {
        font-size: 0.9rem;
        color: #666;
        font-weight: 400;
      }
    }
  }

  .consultation-status {
    display: flex;
    align-items: center;
    gap: 12px;

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      position: relative;

      .status-dot {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: inherit;
        animation: pulse 2s infinite;
      }
    }

    .status-info {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .status-text {
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
      }

      .consultation-timer {
        font-size: 1.1rem;
        font-weight: 600;
        color: #667eea;
        font-family: 'Courier New', monospace;
      }
    }
  }
}

/* Container do vídeo */
.video-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 20px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.video-frame {
  flex: 1;
  position: relative;

  .video-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 20px;
  }
}

/* Estado de loading */
.video-loading {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;

  .loading-spinner {
    margin-bottom: 20px;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: white;
    }

    .spinning {
      animation: spin 2s linear infinite;
    }
  }

  p {
    font-size: 1.2rem;
    margin: 10px 0;
    font-weight: 500;
  }

  small {
    font-size: 0.9rem;
    opacity: 0.8;
  }
}

/* Controles do paciente */
.patient-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  .media-controls {
    display: flex;
    gap: 15px;

    button {
      width: 56px;
      height: 56px;
      background: #667eea;
      color: white;
      transition: all 0.3s ease;

      &:hover {
        background: #5a67d8;
        transform: scale(1.05);
      }

      &.muted {
        background: #e53e3e;
        
        &:hover {
          background: #c53030;
        }
      }

      &.camera-off {
        background: #e53e3e;
        
        &:hover {
          background: #c53030;
        }
      }

      mat-icon {
        font-size: 24px;
      }
    }
  }

  .end-call-control {
    button {
      width: 64px;
      height: 64px;
      background: #e53e3e;
      
      &:hover {
        background: #c53030;
        transform: scale(1.05);
      }

      mat-icon {
        font-size: 28px;
      }
    }
  }
}

/* Tela de consulta finalizada */
.consultation-ended {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;

  .ended-content {
    max-width: 400px;
    padding: 40px;

    .ended-icon {
      font-size: 80px;
      width: 80px;
      height: 80px;
      color: #48bb78;
      margin-bottom: 20px;
    }

    h2 {
      font-size: 2rem;
      margin: 20px 0;
      font-weight: 500;
    }

    p {
      font-size: 1.1rem;
      margin: 15px 0;
      opacity: 0.9;
    }

    .consultation-duration {
      font-family: 'Courier New', monospace;
      font-weight: 600;
      font-size: 1.2rem;
      color: #667eea;
    }

    .ended-actions {
      margin-top: 30px;

      .primary-action {
        padding: 12px 30px;
        font-size: 1rem;
        font-weight: 500;
      }
    }
  }
}

/* Instruções para o paciente */
.patient-instructions {
  position: absolute;
  top: 100px;
  right: 30px;
  width: 300px;
  z-index: 5;

  .instructions-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;

    h3 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 15px 0;
      color: #333;
      font-size: 1.1rem;

      mat-icon {
        color: #667eea;
      }
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin: 8px 0;
        font-size: 0.9rem;
        color: #555;
        line-height: 1.4;
      }
    }
  }
}

/* Botão de emergência */
.emergency-back {
  position: absolute;
  bottom: 30px;
  left: 30px;
  z-index: 10;

  .back-button {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border: 2px solid rgba(102, 126, 234, 0.3);
    padding: 8px 16px;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: #667eea;
    }

    mat-icon {
      margin-right: 8px;
    }
  }
}

/* Animações */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsividade */
@media (max-width: 768px) {
  .consultation-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .video-container {
    margin: 10px;
  }

  .patient-controls {
    padding: 15px 20px;
    flex-direction: column;
    gap: 20px;

    .media-controls {
      justify-content: center;
    }
  }

  .patient-instructions {
    position: static;
    width: auto;
    margin: 20px;
  }

  .emergency-back {
    position: static;
    text-align: center;
    margin: 20px;
  }
}
