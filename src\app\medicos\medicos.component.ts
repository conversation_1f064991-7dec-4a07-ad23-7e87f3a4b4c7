import { AlertComponent } from './../alert/alert.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';

import { CidadeService } from '../service/cidade.service';
import { Medico, EspecialidadeMedico, DiaHora } from '../model/medico';
import { Endereco } from '../model/endereco';
import { Contato } from '../model/contato';
import { Usuario } from '../model/usuario';
import { MedicoService } from '../service/medico.service';

import { ClinicaService } from '../service/clinica.service';
import { UsuarioService } from '../service/usuario.service';
import { ImageCroppedEvent, ImageCropperComponent, ImageCropperModule, ImageTransform } from 'ngx-image-cropper';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { Pessoa } from '../model/pessoa';
import { ValidadoreseMascaras } from '../Util/validadores';
import { CertificadoDigitalService } from '../service/certificadoDigital.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { UfClass } from '../Util/UFClass';
import { MatChipInputEvent as MatChipInputEvent } from '@angular/material/chips';
import { TipoOrientacaoService } from '../service/tipoOrientacao.service';
import { tipoOrientacao } from '../model/itens';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatSelectModule } from '@angular/material/select';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';
import { GoogleChartsModule } from 'angular-google-charts';

@Component({
    selector: 'app-medicos',
    templateUrl: './medicos.component.html',
    styleUrls: ['./medicos.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      TranslateModule,
      MatFormFieldModule,
      TranslateModule,
      MatIcon,
      NgSelectModule,
      MatSelectModule,
      NgxSmartModalModule,
      MatDivider,
      ImageCropperModule,
      TruncatePipe,
      GoogleChartsModule
    ]
})
export class MedicosComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,

    private cidadeService: CidadeService,
    public ngxSmartModalService: NgxSmartModalService,
    private tipoOrientacaoService: TipoOrientacaoService,
    // private spinner: NgxSpinnerService,
    private clinicaService: ClinicaService,
    private medicoService: MedicoService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private usuarioService: UsuarioService,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private certificadoDigitalService: CertificadoDigitalService,
    private localStorageService: LocalStorageService,
  ) { }

  @ViewChild(ImageCropperComponent) imageCropper?: ImageCropperComponent;


  Segunda: boolean = false;
  Terca: boolean = false;
  Quarta: boolean = false;
  Quinta: boolean = false;
  Sexta: boolean = false;
  Sabado: boolean = false;
  Domingo: boolean = false;

  DadosHorario: any = [];
  dia?: string;

  // actionButtonLabel: string = 'Fechar';
  // salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  // ErroSalvar: string = 'Erro ao salvar!';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;


  clinicas: any = new FormControl([]);
  especialidade: any = new FormControl([]);
  DadosEspecialidade: any;
  ImagemPessoa: any = "assets/build/img/userdefault.png";
  fileToUpload?: File;
  showMessageError = false;
  showMessageSuccess = false;
  Dados: any = [];
  dadosUF = UfClass;
  dadosUFCarregaBanco: any;
  dadosCidade: any;
  dadosCidadeUf: any;
  errors = [];
  DadosUsuario: any;
  // usuario: Usuario;
  tipoUsuario: any;

  retornoPessoa: any;
  retornoMedico: any;
  retornoEndereco: any;
  retornoContato: any;
  // retornoUsuario: any;
  retornoHorarios: any;

  fruits:any = [];
  selectedUserIds?: number[];
  visible = true;
  selectable = true;
  removable = true;
  addOnBlur = true;

  usuarioDelet:any;
  segundaHorario: any = {}
  tercaHorario: any = {}
  quartaHorario: any = {}
  quintaHorario: any = {}
  sextaHorario: any = {}
  sabadoHorario: any = {}
  domingoHorario: any = {}
  Dtanasc?: boolean;
  TelVal?: boolean;
  TelMovVal?: boolean;
  TelComVal?: boolean;
  DadosClinicas: any = [];
  clinicaVasil = true;
  clinicaVal?: boolean;
  especialidadeVasil = true;
  imageChangedEvent: any = '';
  croppedImage: any = '';
  retornoClinicas: any;

  showCropper = false;
  usuarioInvalidoEmail: boolean = false;
  usuarioInvalidoTelefone: boolean = false;
  clinic = []

  imagemCorte: any;

  campoSexo: any;
  masc: string = '';
  fem: string = '';

  DadosSexo:any = [];
  tempoConsultaInvalido: boolean = false

  idMedico: any

  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;

  objTipoOrientacao: tipoOrientacao[] = [];
  idTipoOrientacao?: number;

  ngOnInit() {


    this.idMedico = this.usuarioDelet = this.localStorageService.idMedico;
    this.localStorageService.clearByName("idMedico");
    if (this.idMedico) {
      this.CarregaMedico(this.idMedico);

      // CERTIFICADOS
      // this.flgMostraCertificados = true;
      // this.GetListaCertificados()
    }
    else {
      this.CarregaEspecialidade()
      this.CarregaClinicas();
      this.CarregaTipoOrientacao();
    }

  }
  CarregaTipoOrientacao() {
    this.tipoOrientacaoService.getTipoOrientacao().subscribe((ret) => {
      ;
      this.objTipoOrientacao = ret;
      this.spinner.hide();
    })
  }

  async carregaDadosSexo() {
    if (this.DadosSexo.length == 0) {
      this.tradutor.get('TELACADASTROMEDICO.MASCULINO').subscribe((res: string) => {
        this.masc = res;
      });
      this.tradutor.get('TELACADASTROMEDICO.FEMININO').subscribe((res: string) => {
        this.fem = res
      });
      this.DadosSexo = [this.masc, this.fem]
    }
  }


  readThis(inputValue: any): void {
    var file: File = inputValue.files[0];
    var myReader: FileReader = new FileReader();

    myReader.onloadend = () => {
      this.ImagemPessoa = myReader.result;
      ;
    }
    myReader.readAsDataURL(file);
    // ;
  }

  valueMedico() {
    if (this.usuarioDelet)
      this.ngxSmartModalService.getModal('excluirItem').open();

  }


  InativarMedico() {

    try {
      this.medicoService.inativarMedico(this.usuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        var Delete = retorno;
        if (Delete == true) {
          this.Dados = []
          this.especialidade = [];
          this.ImagemPessoa = "assets/build/img/perfilI.jpg";
          this.ngxSmartModalService.getModal('excluirItem').close();
          // this.ConcordoSnack(Delete);
          this.snackBarAlert.sucessoSnackbar('Médico inativado com sucesso!')
        }
        else {
          

          this.snackBarAlert.falhaSnackbar('Falha ao inativar médico!')
        }
        this.spinner.hide();
      }, err => {
        this.snackBarAlert.falhaSnackbar('Falha ao inativar médico!', err)
        this.spinner.hide();
      })
    } catch (error) {
      this.spinner.hide();

      this.snackBarAlert.falhaSnackbar('Falha na conexão!')
    }



  }


  public CidadePorUF() {
    try {
      this.cidadeService.getCidades().then((retornaCidade) => {
        this.dadosCidade = retornaCidade;
        this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == this.Dados.uf);
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      })
    } catch (error) {
      this.snackBarAlert.falhaSnackbar('Erro ao carregar Cidade')
      this.spinner.hide();
    }

  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }
  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    if (v.length == 0)
      this.TelMovVal = false;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");


    (<HTMLInputElement>evento.target).value = v
  }


  public validarCpf(value:any) {

    var valuecpf = value.replace(/[\.-]/g, "");
    this.campoCPFInvalido = false;
    var idUsuario
    if (this.retornoMedico != undefined)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {
      this.campoCPFVazil = false;

      if (!this.validacao.cpf(value)) {
        this.campoCPFInvalido = true;
        return;
      }
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(valuecpf, idUsuario, 'CPF').subscribe((retorno) => {
        if (retorno != null) {

          if (retorno.medico != null)
            this.mensagemPaciente = 'CPF já registrado no sistema Deseja utilizar o médico desse cadastro?';
          else
            this.mensagemPaciente = 'CPF já registrado no sistema Deseja utilizar esse usuário?';

          this.campoExitente = "CPF"
          this.idUsuarioExistente = retorno;
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      })
    }
    else
      this.campoCPFVazil = true;
  }

  AlterarImagemPessoa($event:any): void {
    this.ngxSmartModalService.getModal('ModalFoto').open();
    this.imageChangedEvent = $event
  }

  CortarImagem() {
    this.ngxSmartModalService.getModal('ModalFoto').close()
    this.ImagemPessoa = this.imagemCorte
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCorte = event.base64;
    ;
  }
  LimpaCampoFile() {
    (document.getElementById('imageperfilusuario')as HTMLInputElement)["value"] = '';
  }


  imageLoaded() {
    this.showCropper = true;
    
  }
  cropperReady() {
    
  }
  loadImageFailed() {
    ;
  }
  transform: ImageTransform = {
    rotate: 0,
    flipH: false,
    flipV: false
  };

  rotateLeft() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! - 90 };
  }

  rotateRight() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! + 90 };
  }

  flipHorizontal() {
    this.transform = { ...this.transform, flipH: !this.transform.flipH };
  }

  flipVertical() {
    this.transform = { ...this.transform, flipV: !this.transform.flipV };
  }

  idUsuarioExistente: any;
  mensagemPaciente?: string;
  campoExitente?: string;
  public AceitarUsuarioExistente() {

    if (this.idUsuarioExistente.medico != null)
      this.CarregaMedico(this.idUsuarioExistente.medico.idMedico)
    else {
      this.retornoPessoa = this.idUsuarioExistente.pessoa;
      this.retornoEndereco = this.idUsuarioExistente.endereco;
      this.retornoContato = this.idUsuarioExistente.contato;
      this.retornoClinicas = this.idUsuarioExistente.clinicas;


      this.Dados.cpf = this.retornoPessoa.cpf;
      this.Dados.nome = this.retornoPessoa.nomePessoa;
      this.Dados.dtaNascimento = this.retornoPessoa.dtaNascimento ? new Date(this.retornoPessoa.dtaNascimento).toLocaleDateString() : "";
      if (this.DadosSexo.length == 0) {
        this.tradutor.get('TELACADASTROMEDICO.MASCULINO').subscribe((res: string) => {
          this.masc = res;
        });
        this.tradutor.get('TELACADASTROMEDICO.FEMININO').subscribe((res: string) => {
          this.fem = res
        });
        this.DadosSexo = [this.masc, this.fem]
      }

      if (this.retornoPessoa.sexo == "Masculino") {
        this.campoSexo = this.masc
      } else if (this.retornoPessoa.sexo == "Feminino") {
        this.campoSexo = this.fem
      } else
        this.campoSexo = null


      if (this.retornoContato != null) {
        if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefone = this.retornoContato.telefone;

        if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

        if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;

      }
      if (this.retornoClinicas != null)
        this.Dados.retidoClinica = this.retornoClinicas[0].retidoClinica;


      this.Dados.email = this.retornoPessoa.email;
      if (this.retornoEndereco != null) {
        this.Dados.rua = this.retornoEndereco.rua
        this.Dados.numero = this.retornoEndereco.numero
        this.Dados.complemento = this.retornoEndereco.complemento
        this.Dados.cep = this.retornoEndereco.cep
        this.Dados.bairro = this.retornoEndereco.bairro

      }
      this.Dados.foto = this.retornoPessoa.foto;
      if (this.idUsuarioExistente.imagem64 != null && this.idUsuarioExistente.imagem64 != "")
        this.ImagemPessoa = this.idUsuarioExistente.imagem64;

      if (this.idUsuarioExistente.clinicas && this.idUsuarioExistente.clinicas.length > 0) {

        this.clinicas = [];
        this.idUsuarioExistente.clinicas.forEach((element:any) => {
          this.DadosClinicas.forEach((elemento:any) => {
            if (elemento.idClinica == element.idClinica)
              this.clinicas.push(elemento)
          })
        })
        this.clinicas = new FormControl(this.clinicas);
      }

    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();
  }
  public NaoAceitarUsuarioExistente() {
    if (this.campoExitente == 'CPF') {
      this.Dados.cpf = ""
      this.campoExitente = ""
    }
    else if (this.campoExitente == 'EMAIL') {
      this.Dados.email = ""
      this.campoExitente = ""
    }
    else {
      this.Dados.telefoneMovel = ''
      this.campoExitente = ""

    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();

  }

  campoEmailInvalido: boolean = false;
  campoEmailVazil = false;
  public validarEmailMedico(value:any) {
    this.usuarioInvalidoEmail = false;
    var idUsuario
    if (this.retornoContato != undefined)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {
      this.campoEmailVazil = false;
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        return;
      }
      this.campoEmailInvalido = false
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'EMAIL').subscribe((retorno) => {
        ;
        if (retorno != null) {

          if (retorno.medico != null)
            this.mensagemPaciente = 'Email já registrado no sistema Deseja utilizar o médico desse cadastro?';
          else
            this.mensagemPaciente = 'Email já registrado no sistema Deseja utilizar esse usuário?';

          this.campoExitente = "EMAIL"
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      })
    }
    else
      this.campoEmailVazil = true;
  }

  public validarTelMovel(value:any) {
    this.usuarioInvalidoTelefone = true;
    var idUsuario
    if (this.retornoContato != undefined)
      var idUsuario = this.retornoPessoa.idPessoa
    if (value != "") {
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'TelMovel').subscribe((retorno) => {
        ;
        if (retorno != null) {

          if (retorno.medico != null)
            this.mensagemPaciente = 'Telefone já registrado no sistema Deseja utilizar o médico desse cadastro?';
          else
            this.mensagemPaciente = 'Telefone já registrado no sistema Deseja utilizar esse usuário?';

          this.campoExitente = "Tel"
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      })
    }
  }


  public MascaraDinheiro(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }
    this.Dados.valorConsulta = v
  }



  public mascaraText(evento: KeyboardEvent, campo: string) {

    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    if (campo == "Nome")
      this.Dados.nome = v;
    else
      (<HTMLInputElement>evento.target).value = v
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraCpf(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 14) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 11) {
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }
  CarregaEspecialidade() {
    try {
      this.medicoService.getEspecialidade().then((retornaEspecialidade) => {
        this.DadosEspecialidade = retornaEspecialidade
        
        
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Falha ao carregar especialidade!')
        this.spinner.hide();
      })
    } catch (error) {
      
      this.snackBarAlert.falhaSnackbar('Erro ao carregar especialidade!')
      this.spinner.hide();
    }
  }

  CarregaClinicas() {
    try {
      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {
        this.DadosClinicas = retornaClinicas
        
        
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Falha ao carregar Clinicas!')
        this.spinner.hide();
      })
    } catch (error) {
      
      this.spinner.hide();
      this.snackBarAlert.falhaSnackbar("Falha na conexão!")
    }
  }

  limparData() {
    this.Dados.dtaNascimento = ""
  }

  async CarregaMedico(id:any) {
    await this.CarregaTipoOrientacao();

    await this.medicoService.getMedicoEdit(id).subscribe((retorno) => {

      
      
      this.retornoHorarios = []

      this.retornoMedico = retorno.medico;
      this.retornoPessoa = retorno.pessoa;

      this.retornoEndereco = retorno.endereco;
      this.retornoContato = retorno.contato;
      // this.retornoUsuario = retorno.usuario;
      this.retornoClinicas = retorno.clinicas;
      this.retornoHorarios = retorno.horarios;

      this.idTipoOrientacao = this.retornoMedico.idTipoOrientacao;

      this.Dados.cpf = this.retornoPessoa.cpf;
      this.Dados.nome = this.retornoPessoa.nomePessoa;
      this.Dados.dtaNascimento = this.retornoPessoa.dtaNascimento ? new Date(this.retornoPessoa.dtaNascimento).toLocaleDateString() : "";
      if (this.DadosSexo.length == 0) {
        this.tradutor.get('TELACADASTROMEDICO.MASCULINO').subscribe((res: string) => {
          this.masc = res;
        });
        this.tradutor.get('TELACADASTROMEDICO.FEMININO').subscribe((res: string) => {
          this.fem = res
        });
        this.DadosSexo = [this.masc, this.fem]
      }

      if (this.retornoPessoa.sexo == "Masculino") {
        this.campoSexo = this.masc
      } else if (this.retornoPessoa.sexo == "Feminino") {
        this.campoSexo = this.fem
      } else
        this.campoSexo = null

      this.Dados.crm = this.retornoMedico.crm

      // if (retorno.valorConsulta)
      //   this.Dados.valorConsulta = retorno.valorConsulta.replace(/,/g, ".");


      if (retorno.medico.valorConsulta) {
        retorno.medico.valorConsulta = this.verificaCasaDecimal(retorno.medico.valorConsulta)
        retorno.medico.valorConsulta = this.aplicarMascaraValor(retorno.medico.valorConsulta)
        this.Dados.valorConsulta = retorno.medico.valorConsulta;
      }

      if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
        this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
        this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.Dados.telefone = this.retornoContato.telefone;

      if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
        this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
        this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

      if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
        this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
        this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;

      this.Dados.retidoClinica = this.retornoClinicas[0].retidoClinica;
      this.Dados.codigoConvenio = this.retornoMedico.codigoConvenio
      this.Dados.email = this.retornoPessoa.email;
      if (this.retornoEndereco != null) {
        this.Dados.rua = this.retornoEndereco.rua
        this.Dados.numero = this.retornoEndereco.numero
        this.Dados.complemento = this.retornoEndereco.complemento
        this.Dados.cep = this.retornoEndereco.cep
        this.Dados.bairro = this.retornoEndereco.bairro

      }
      this.Dados.foto = this.retornoPessoa.foto;
      if (retorno.imagem64 != null && retorno.imagem64 != "")
        this.ImagemPessoa = retorno.imagem64;

      this.medicoService.getEspecialidade().then((retornaEspecialidade) => {
        this.DadosEspecialidade = retornaEspecialidade

        if (retorno.especialidades && retorno.especialidades.length > 0) {

          this.especialidade = [];
          retorno.especialidades.forEach((element:any) => {
            this.DadosEspecialidade.forEach((elemento:any) => {
              if (elemento.idEspecialidade == element.idEspecialidade)
                this.especialidade.push(elemento)
            })
          })
          this.especialidade = new FormControl(this.especialidade);
        }
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar("Falha ao carregar especialidade")
        this.spinner.hide();
      })

      this.clinicaService.getClinicas(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retornaClinicas) => {
        
        this.DadosClinicas = retornaClinicas

        if (retorno.clinicas && retorno.clinicas.length > 0) {

          this.clinicas = [];
          retorno.clinicas.forEach((element:any) => {
            this.DadosClinicas.forEach((elemento:any) => {
              if (elemento.idClinica == element.idClinica)
                this.clinicas.push(elemento)
            })
          })
          this.clinicas = new FormControl(this.clinicas);
        }
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar("Falha ao carregar especialidade")
        this.spinner.hide();
      })

      if (this.retornoHorarios.length > 0) {
        this.retornoHorarios.forEach((element:any) => {
          switch (element.diaSemana) {
            case "Segunda": {
              this.Segunda = true;
              this.segundaHorario.SegundaInicio = element.horaEntrada.substring(0, 5)
              this.segundaHorario.SegundaInicioInt = element.horaInicioIntervalo.substring(0, 5)
              this.segundaHorario.SegundaFimInt = element.horaFimIntervalo.substring(0, 5)
              this.segundaHorario.SegundaFim = element.horaSaida.substring(0, 5)
              this.segundaHorario.SegundaTempo = element.tempoConsulta.substring(0, 5)
              break;
            }
            case "Terça": {
              this.Terca = true;
              this.tercaHorario.TercaInicio = element.horaEntrada.substring(0, 5)
              this.tercaHorario.TercaInicioInt = element.horaInicioIntervalo.substring(0, 5)
              this.tercaHorario.TercaFimInt = element.horaFimIntervalo.substring(0, 5)
              this.tercaHorario.TercaFim = element.horaSaida.substring(0, 5)
              this.tercaHorario.TercaTempo = element.tempoConsulta.substring(0, 5)
              break;
            }
            case "Quarta": {
              this.Quarta = true;
              this.quartaHorario.QuartaInicio = element.horaEntrada.substring(0, 5)
              this.quartaHorario.QuartaInicioInt = element.horaInicioIntervalo.substring(0, 5)
              this.quartaHorario.QuartaFimInt = element.horaFimIntervalo.substring(0, 5)
              this.quartaHorario.QuartaFim = element.horaSaida.substring(0, 5)
              this.quartaHorario.QuartaTempo = element.tempoConsulta.substring(0, 5)
              break;
            }
            case "Quinta": {
              this.Quinta = true;
              this.quintaHorario.QuintaInicio = element.horaEntrada.substring(0, 5);
              this.quintaHorario.QuintaInicioInt = element.horaInicioIntervalo.substring(0, 5);
              this.quintaHorario.QuintaFimInt = element.horaFimIntervalo.substring(0, 5);
              this.quintaHorario.QuintaFim = element.horaSaida.substring(0, 5);
              this.quintaHorario.QuintaTempo = element.tempoConsulta.substring(0, 5);
              break;
            }
            case "Sexta": {
              this.Sexta = true;
              this.sextaHorario.SextaInicio = element.horaEntrada.substring(0, 5);
              this.sextaHorario.SextaInicioInt = element.horaInicioIntervalo.substring(0, 5);
              this.sextaHorario.SextaFimInt = element.horaFimIntervalo.substring(0, 5);
              this.sextaHorario.SextaFim = element.horaSaida.substring(0, 5);
              this.sextaHorario.SextaTempo = element.tempoConsulta.substring(0, 5);
              break;
            }
            case "Sabado": {
              this.Sabado = true;
              this.sabadoHorario.SabadoInicio = element.horaEntrada.substring(0, 5);
              this.sabadoHorario.SabadoInicioInt = element.horaInicioIntervalo.substring(0, 5);
              this.sabadoHorario.SabadoFimInt = element.horaFimIntervalo.substring(0, 5);
              this.sabadoHorario.SabadoFim = element.horaSaida.substring(0, 5);
              this.sabadoHorario.SabadoTempo = element.tempoConsulta.substring(0, 5);
              break;
            }
            case "Domingo": {
              this.Domingo = true;
              this.domingoHorario.DomingoInicio = element.horaEntrada.substring(0, 5);
              this.domingoHorario.DomingoInicioInt = element.horaInicioIntervalo.substring(0, 5);
              this.domingoHorario.DomingoFimInt = element.horaFimIntervalo.substring(0, 5);
              this.domingoHorario.DomingoFim = element.horaSaida.substring(0, 5);
              this.domingoHorario.DomingoTempo = element.tempoConsulta.substring(0, 5);
              break;
            }
          }
        });
      }
      if (this.retornoEndereco != null && this.retornoEndereco.idCidade != null) {
        this.cidadeService.getCidades().then((retornaCidade) => {
          this.dadosCidade = retornaCidade
          var sigle = this.dadosCidade.filter((c:any) => c.idCidade == this.retornoEndereco.idCidade)
          this.Dados.uf = sigle[0].siglasUf

          this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == sigle[0].siglasUf);
          this.Dados.idCidade = this.retornoEndereco.idCidade
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Falha ao carregar Cidades por UF")
          this.spinner.hide();
        })
      }
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }
  users = [
    { id: 'Ortopedista', name: 'Ortopedista' },
    { id: 'Oftalmologista', name: 'Oftalmologista' }
  ];
  add(event: MatChipInputEvent): void {
    const input = event.input;
    const value = event.value;
    // Add our fruit
    if ((value || '').trim()) {
      this.fruits.push({ DesEspecialidade: value.trim() });
    }
    // Reset the input value
    if (input) {
      input.value = '';
    }
  }
  remove(fruit: EspecialidadeMedico): void {
    const index = this.fruits.indexOf(fruit);

    if (index >= 0) {
      this.fruits.splice(index, 1);
    }
  }




  public Submit() {

    try {

      this.validarCampos();
      this.validarTabelaHorario();
      if (this.showMessageError) {
        return;
      }
      
      // var teste = document.querySelector("#dtanascimento");

      var pessoa = new Pessoa();
      var medico = new Medico();
      var endereco = new Endereco();
      var contato = new Contato();
      var usuario = new Usuario();


      if (this.retornoPessoa) {
        if (this.retornoMedico != undefined)
          medico.idMedico = this.retornoMedico.idMedico;
        else
          medico.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()

        pessoa.idPessoa = this.retornoPessoa.idPessoa;
        usuario.idUsuario = this.retornoPessoa.idUsuarioAcesso;


        if (this.retornoPessoa.idEndereco) {
          endereco.idEndereco = this.retornoPessoa.idEndereco;
          pessoa.idEndereco = this.retornoPessoa.idEndereco;
        }
        pessoa.idContato = this.retornoPessoa.idContato;
        contato.idContato = this.retornoContato.idContato;
        medico.idTipoOrientacao = this.idTipoOrientacao;

      }
      else {
        pessoa.idUsuarioGerador = usuario.idUsuarioGerador = medico.idUsuarioGerador = contato.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;

        usuario.flgPrimeiroAcesso = true;

      }
      medico.retidoClinica = this.Dados.retidoClinica;
      medico.codigoConvenio = this.Dados.codigoConvenio;

      if (this.campoSexo == this.masc) {
        this.Dados.sexo = "Masculino"
      } else if (this.campoSexo == this.fem) {
        this.Dados.sexo = "Feminino"
      } else
        this.Dados.sexo = null

      pessoa.sexo = this.Dados.sexo
      pessoa.nomePessoa = this.Dados.nome;

      pessoa.dtaNascimento = (document.getElementById('DtaNasc')as HTMLInputElement)['value'] == '' ? null : 
                              this.validacao.Convertdata((document.getElementById('DtaNasc')as HTMLInputElement)['value']);
      medico.crm = this.Dados.crm

      // medico.valorConsulta = document.getElementById('valorConsulta')['value'];


      
      if (this.Dados.valorConsulta) {
        this.Dados.valorConsulta = this.validacao.removeMascara(this.Dados.valorConsulta);
        this.Dados.valorConsulta = this.Dados.valorConsulta.replace(/(\d{1})(\d{1,2})$/, "$1.$2");
        medico.valorConsulta = this.Dados.valorConsulta
      }

      pessoa.foto = this.Dados.foto
      if (this.ImagemPessoa != "assets/build/img/userdefault0.png")
        pessoa.imagem64 = this.ImagemPessoa;
      pessoa.cpf = this.Dados.cpf;
      pessoa.email = this.Dados.email;
      contato.telefone = this.Dados.telefone;
      contato.telefoneMovel = this.Dados.telefoneMovel;
      contato.telefoneComercial = this.Dados.telefoneComercial;
      contato.flgInativo = false;

      endereco.rua = this.Dados.rua
      endereco.numero = this.Dados.numero
      endereco.complemento = this.Dados.complemento
      endereco.cep = this.Dados.cep
      endereco.bairro = this.Dados.bairro
      endereco.idCidade = this.Dados.idCidade
      endereco.flgInativo = false

      medico.endereco = endereco;
      medico.contato = contato;
      medico.usuario = usuario;
      medico.pessoa = pessoa;


      var Especialidad:any = []
      this.especialidade.value.forEach((element:any) => {

        Especialidad.push({
          DesEspecialidade: element.idEspecialidade,
          FlgInativo: false,
          IdUsuarioGerador: this.usuarioLogadoService.getIdUsuarioAcesso(),
          DtaCadastro: new Date()
        })
      });

      this.clinic = [];
      var Clinic:any = [];


      // this.retornoClinicas.forEach(element => {
      //   var teste = this.DadosClinicas.forEach(c =>c.idClinica != element.idClinica)
      //   if(teste != undefined){
      //     Clinic.push({
      //       idClinica: element.idClinica,
      //     })
      //   }
      // });



      this.clinicas.value.forEach((element:any) => {

        Clinic.push({
          idClinica: element.idClinica,
        })
      });

      if (Clinic.length > 0)
        usuario.clinicas = Clinic;

      if (Especialidad.length > 0)
        medico.especialidade = Especialidad;
      else {

      }

      this.DadosHorario = [];

      if (this.Segunda == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Segunda';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.segundaHorario.SegundaInicio != undefined && this.segundaHorario.SegundaInicio != '')
          data.HoraEntrada = this.segundaHorario.SegundaInicio
        if (this.segundaHorario.SegundaInicioInt != undefined && this.segundaHorario.SegundaInicioInt != '')
          data.HoraInicioIntervalo = this.segundaHorario.SegundaInicioInt
        if (this.segundaHorario.SegundaFimInt != undefined && this.segundaHorario.SegundaFimInt != '')
          data.HoraFimIntervalo = this.segundaHorario.SegundaFimInt
        if (this.segundaHorario.SegundaFim != undefined && this.segundaHorario.SegundaFim != '')
          data.HoraSaida = this.segundaHorario.SegundaFim
        if (this.segundaHorario.SegundaTempo != undefined && this.segundaHorario.SegundaTempo != '')
          data.TempoConsulta = this.segundaHorario.SegundaTempo
        this.DadosHorario.push(data)

      }

      if (this.Terca == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Terça';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.tercaHorario.TercaInicio != undefined && this.tercaHorario.TercaInicio != '')
          data.HoraEntrada = this.tercaHorario.TercaInicio
        if (this.tercaHorario.TercaInicioInt != undefined && this.tercaHorario.TercaInicioInt != '')
          data.HoraInicioIntervalo = this.tercaHorario.TercaInicioInt
        if (this.tercaHorario.TercaFimInt != undefined && this.tercaHorario.TercaFimInt != '')
          data.HoraFimIntervalo = this.tercaHorario.TercaFimInt
        if (this.tercaHorario.TercaFim != undefined && this.tercaHorario.TercaFim != '')
          data.HoraSaida = this.tercaHorario.TercaFim
        if (this.tercaHorario.TercaTempo != undefined && this.tercaHorario.TercaTempo != '')
          data.TempoConsulta = this.tercaHorario.TercaTempo
        this.DadosHorario.push(data)
      }

      if (this.Quarta == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Quarta';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.quartaHorario.QuartaInicio != undefined && this.quartaHorario.QuartaInicio != '')
          data.HoraEntrada = this.quartaHorario.QuartaInicio
        if (this.quartaHorario.QuartaInicioInt != undefined && this.quartaHorario.QuartaInicioInt != '')
          data.HoraInicioIntervalo = this.quartaHorario.QuartaInicioInt
        if (this.quartaHorario.QuartaFimInt != undefined && this.quartaHorario.QuartaFimInt != '')
          data.HoraFimIntervalo = this.quartaHorario.QuartaFimInt
        if (this.quartaHorario.QuartaFim != undefined && this.quartaHorario.QuartaFim != '')
          data.HoraSaida = this.quartaHorario.QuartaFim
        if (this.quartaHorario.QuartaTempo != undefined && this.quartaHorario.QuartaTempo != '')
          data.TempoConsulta = this.quartaHorario.QuartaTempo
        this.DadosHorario.push(data)
      }
      if (this.Quinta == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Quinta';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.quintaHorario.QuintaInicio != undefined && this.quintaHorario.QuintaInicio != '')
          data.HoraEntrada = this.quintaHorario.QuintaInicio
        if (this.quintaHorario.QuintaInicioInt != undefined && this.quintaHorario.QuintaInicioInt != '')
          data.HoraInicioIntervalo = this.quintaHorario.QuintaInicioInt
        if (this.quintaHorario.QuintaFimInt != undefined && this.quintaHorario.QuintaFimInt != '')
          data.HoraFimIntervalo = this.quintaHorario.QuintaFimInt
        if (this.quintaHorario.QuintaFim != undefined && this.quintaHorario.QuintaFim != '')
          data.HoraSaida = this.quintaHorario.QuintaFim
        if (this.quintaHorario.QuintaTempo != undefined && this.quintaHorario.QuintaTempo != '')
          data.TempoConsulta = this.quintaHorario.QuintaTempo
        this.DadosHorario.push(data)
      }

      if (this.Sexta == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Sexta';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.sextaHorario.SextaInicio != undefined && this.sextaHorario.SextaInicio != '')
          data.HoraEntrada = this.sextaHorario.SextaInicio
        if (this.sextaHorario.SextaInicioInt != undefined && this.sextaHorario.SextaInicioInt != '')
          data.HoraInicioIntervalo = this.sextaHorario.SextaInicioInt
        if (this.sextaHorario.SextaFimInt != undefined && this.sextaHorario.SextaFimInt != '')
          data.HoraFimIntervalo = this.sextaHorario.SextaFimInt
        if (this.sextaHorario.SextaFim != undefined && this.sextaHorario.SextaFim != '')
          data.HoraSaida = this.sextaHorario.SextaFim
        if (this.sextaHorario.SextaTempo != undefined && this.sextaHorario.SextaTempo != '')
          data.TempoConsulta = this.sextaHorario.SextaTempo
        this.DadosHorario.push(data)
      }

      if (this.Sabado == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Sabado';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.sabadoHorario.SabadoInicio != undefined && this.sabadoHorario.SabadoInicio != '')
          data.HoraEntrada = this.sabadoHorario.SabadoInicio
        if (this.sabadoHorario.SabadoInicioInt != undefined && this.sabadoHorario.SabadoInicioInt != '')
          data.HoraInicioIntervalo = this.sabadoHorario.SabadoInicioInt
        if (this.sabadoHorario.SabadoFimInt != undefined && this.sabadoHorario.SabadoFimInt != '')
          data.HoraFimIntervalo = this.sabadoHorario.SabadoFimInt
        if (this.sabadoHorario.SabadoFim != undefined && this.sabadoHorario.SabadoFim != '')
          data.HoraSaida = this.sabadoHorario.SabadoFim
        if (this.sabadoHorario.SabadoTempo != undefined && this.sabadoHorario.SabadoTempo != '')
          data.TempoConsulta = this.sabadoHorario.SabadoTempo
        this.DadosHorario.push(data)
      }

      if (this.Domingo == true) {
        var data = new DiaHora()
        data.DiaSemana = 'Domingo';
        data.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        data.DtaCadastro = new Date()
        if (this.domingoHorario.DomingoInicio != undefined && this.domingoHorario.DomingoInicio != '')
          data.HoraEntrada = this.domingoHorario.DomingoInicio
        if (this.domingoHorario.DomingoInicioInt != undefined && this.domingoHorario.DomingoInicioInt != '')
          data.HoraInicioIntervalo = this.domingoHorario.DomingoInicioInt
        if (this.domingoHorario.DomingoFimInt != undefined && this.domingoHorario.DomingoFimInt != '')
          data.HoraFimIntervalo = this.domingoHorario.DomingoFimInt
        if (this.domingoHorario.DomingoFim != undefined && this.domingoHorario.DomingoFim != '')
          data.HoraSaida = this.domingoHorario.DomingoFim
        if (this.domingoHorario.DomingoTempo != undefined && this.domingoHorario.DomingoTempo != '')
          data.TempoConsulta = this.domingoHorario.DomingoTempo
        this.DadosHorario.push(data)

      }


      if (this.DadosHorario.length == 0) {
        this.snackBarAlert.alertaSnackbar("Não há programação de atendimento para o médico.")

        return;
      }
      else {

        this.tempoConsultaInvalido = false
        this.DadosHorario.forEach((element:any) => {
          if (element.TempoConsulta == "00:00") {

            
            this.tempoConsultaInvalido = true
          }
        });
        if (this.tempoConsultaInvalido) {

          this.snackBarAlert.falhaSnackbar("Tempo de consulta não pode ser 00:00")
          return;
        }


        medico.horarios = this.DadosHorario

        
        this.medicoService.salvarMedico(medico).subscribe((returno) => {
          
          if (returno > 0) {
            this.snackBarAlert.sucessoSnackbar("Salvo com sucesso!")
            // CERTIFICADOS
            // if (!this.flgMostraCertificados) {
            //
            //   this.idMedico = returno;
            //   this.ngxSmartModalService.getModal('CertificadoAposCadastro').open();
            // } else
            this.LimparCampos()
          }
          else
          this.snackBarAlert.falhaSnackbar("Falha ao salvar médico.")
          this.spinner.hide();

        }, () => {
          this.snackBarAlert.falhaSnackbar("Falha na conexão!")
          this.spinner.hide();
        })
      }
    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão!")
    }
  }

  LimparCampos() {
    this.Dados = [];
    this.campoSexo = null


    this.retornoPessoa = null;
    this.retornoMedico = null;
    this.retornoEndereco = null;
    this.retornoContato = null;
    this.retornoHorarios = null;

    this.cpf.markAsUntouched();
    this.Nome.markAsUntouched();
    this.email.markAsUntouched();
    this.TelMov.markAsUntouched();
    this.TelCom.markAsUntouched();
    this.mascaraTelefone;
    (document.getElementById("DtaNasc")as HTMLInputElement)['value'] = "";
    this.Dtanasc = false;
    this.TelVal = false;
    this.TelMovVal = false;
    this.TelComVal = false;
    this.clinicaVasil = true;
    this.clinicaVal = false;

    this.campoEmailInvalido = false;

    this.crm.markAsUntouched();
    this.tercaHorario = [];
    this.quartaHorario = [];
    this.quintaHorario = [];
    this.sextaHorario = [];
    this.segundaHorario = [];
    this.sabadoHorario = [];
    this.domingoHorario = [];
    this.Segunda = false;
    this.Terca = false;
    this.Quarta = false;
    this.Quinta = false;
    this.Sexta = false;
    this.Sabado = false;
    this.Domingo = false;
    this.DadosHorario = []
    this.ImagemPessoa = "assets/build/img/userdefault.png";

    (document.getElementsByName('Segundacheck')["0"]as HTMLInputElement)["checked"];
    (document.getElementsByName('Tercacheck')["0"]as HTMLInputElement)["checked"];
    (document.getElementsByName('Quartacheck')["0"]as HTMLInputElement)["checked"];
    (document.getElementsByName('Quintacheck')["0"]as HTMLInputElement)["checked"];
    (document.getElementsByName('Sextacheck')["0"]as HTMLInputElement)["checked"];
    (document.getElementsByName('Sabadocheck')["0"]as HTMLInputElement)["checked"];
    (document.getElementsByName('Domingocheck')["0"]as HTMLInputElement)["checked"];
    this.especialidade = new FormControl([])
    this.clinicas = new FormControl([])

    this.flgMostraCertificados = false
  }

  public validarCampos() {
    this.ValidaDtaChange();
    this.ValidaEspecialidades();
    this.ValidaClinicas();
    this.showMessageError = false;
    this.cpf.markAsTouched();
    this.Nome.markAsTouched();
    this.email.markAsTouched();
    this.TelMov.markAsTouched();
    this.crm.markAsTouched();

    if (this.Dados.cpf && !this.validacao.cpf(this.Dados.cpf)) {
      this.campoCPFInvalido = true;
      return;
    }


    if (this.Dados.cpf == undefined || !this.Dados.cpf.trim())
      this.campoCPFVazil = true;

    if (this.Dados.email == undefined || !this.Dados.email.trim())
      this.campoEmailVazil = true;

    if (this.Dados.nome == undefined || !this.Dados.nome.trim()
      || this.Dados.email == undefined || !this.Dados.email.trim()
      || this.Dados.cpf == undefined || !this.Dados.cpf.trim()
      || this.Dados.crm == undefined || !this.Dados.crm.trim()
      || this.Dados.telefoneMovel == undefined || !this.Dados.telefoneMovel.trim()
      || this.Dtanasc == true
      || this.TelMovVal == true
      || this.TelVal == true || this.TelComVal == true
      || this.clinicaVal == true || this.clinicaVasil == true
      || this.campoEmailInvalido == true || this.campoCPFInvalido == true || this.campoCPFVazil == true
      || this.campoEmailVazil == true) {

      this.showMessageError = true;

      if (this.clinicaVal != true && this.clinicaVal != false) {
        this.clinicaVasil = true;
      }
      if (this.clinicaVasil == true) {
        this.clinicaVal = true;
      }
      // if (this.usuarioInvalidoEmail) {
      //   this.showMessageError = true;

      //   this.tradutor.get('TELACADASTROMEDICO.EMAILJAREGISTRADOEMOUTROUSUARIO').subscribe((res: string) => {
      //     ;
      //     this.AlgumErro(true, res);
      //   });
      // }


      // if (this.usuarioInvalidoTelefone) {
      //   this.showMessageError = true;

      //   this.tradutor.get('TELACADASTROMEDICO.TELEFONEJAREGISTRADOEMOUTROUSUARIO').subscribe((res: string) => {
      //     ;
      //     this.AlgumErro(true, res);
      //   });
      // }

      document.documentElement.scrollTop = 0;
    }

  }

  // Método para validar a tabela de horário, porém, ainda não está funcionando. Ao preencher apenas as horas, o input chega como nulo.
  // Montei o esqueleto do método, mas não consegui fazer a verificação de quando o input está apenas com as horas.
  validarTabelaHorario() {
    if (this.Segunda == true)
      if (this.segundaHorario.SegundaInicio == undefined || this.segundaHorario.SegundaInicio == '' || this.segundaHorario.SegundaInicio == null ||
        this.segundaHorario.SegundaInicioInt == undefined || this.segundaHorario.SegundaInicioInt == '' || this.segundaHorario.SegundaInicioInt == null ||
        this.segundaHorario.SegundaFimInt == undefined || this.segundaHorario.SegundaFimInt == '' || this.segundaHorario.SegundaFimInt == null ||
        this.segundaHorario.SegundaFim == undefined || this.segundaHorario.SegundaFim == '' || this.segundaHorario.SegundaFim == null ||
        this.segundaHorario.SegundaTempo == undefined || this.segundaHorario.SegundaTempo == '' || this.segundaHorario.SegundaTempo == null)
        return true;
    // this.showMessageError = true;

    if (this.Terca == true)
      if (this.tercaHorario.TercaInicio == undefined || this.tercaHorario.TercaInicio == '' || this.tercaHorario.TercaInicio == null ||
        this.tercaHorario.TercaInicioInt == undefined || this.tercaHorario.TercaInicioInt == '' || this.tercaHorario.TercaInicioInt == null ||
        this.tercaHorario.TercaFimInt == undefined || this.tercaHorario.TercaFimInt == '' || this.tercaHorario.TercaFimInt == null ||
        this.tercaHorario.TercaFim == undefined || this.tercaHorario.TercaFim == '' || this.tercaHorario.TercaFim == null ||
        this.tercaHorario.TercaTempo == undefined || this.tercaHorario.TercaTempo == '' || this.tercaHorario.TercaTempo == null)
        return true;
    // this.showMessageError = true;

    if (this.Quarta == true) {
      if (this.quartaHorario.QuartaInicio == undefined || this.quartaHorario.QuartaInicio == '' || this.quartaHorario.QuartaInicio == null ||
        this.quartaHorario.QuartaInicioInt == undefined || this.quartaHorario.QuartaInicioInt == '' || this.quartaHorario.QuartaInicioInt == null ||
        this.quartaHorario.QuartaFimInt == undefined || this.quartaHorario.QuartaFimInt == '' || this.quartaHorario.QuartaFimInt == null ||
        this.quartaHorario.QuartaFim == undefined || this.quartaHorario.QuartaFim == '' || this.quartaHorario.QuartaFim == null ||
        this.quartaHorario.QuartaTempo == undefined || this.quartaHorario.QuartaTempo == '' || this.quartaHorario.QuartaTempo == null)
        return true;
      // this.showMessageError = true;
    }

    if (this.Quinta == true)
      if (this.quintaHorario.QuintaInicio == undefined || this.quintaHorario.QuintaInicio == '' || this.quintaHorario.QuintaInicio == null ||
        this.quintaHorario.QuintaInicioInt == undefined || this.quintaHorario.QuintaInicioInt == '' || this.quintaHorario.QuintaInicioInt == null ||
        this.quintaHorario.QuintaFimInt == undefined || this.quintaHorario.QuintaFimInt == '' || this.quintaHorario.QuintaFimInt == null ||
        this.quintaHorario.QuintaFim == undefined || this.quintaHorario.QuintaFim == '' || this.quintaHorario.QuintaFim == null ||
        this.quintaHorario.QuintaTempo == undefined || this.quintaHorario.QuintaTempo == '' || this.quintaHorario.QuintaTempo == null)
        return true;
    // this.showMessageError = true;

    if (this.Sexta == true)
      if (this.sextaHorario.SextaInicio == undefined || this.sextaHorario.SextaInicio == '' || this.sextaHorario.SextaInicio == null ||
        this.sextaHorario.SextaInicioInt == undefined || this.sextaHorario.SextaInicioInt == '' || this.sextaHorario.SextaInicioInt == null ||
        this.sextaHorario.SextaFimInt == undefined || this.sextaHorario.SextaFimInt == '' || this.sextaHorario.SextaFimInt == null ||
        this.sextaHorario.SextaFim == undefined || this.sextaHorario.SextaFim == '' || this.sextaHorario.SextaFim == null ||
        this.sextaHorario.SextaTempo == undefined || this.sextaHorario.SextaTempo == '' || this.sextaHorario.SextaTempo == null)
        return true;
    // this.showMessageError = true;

    if (this.Sabado == true)
      if (this.sabadoHorario.SabadoInicio == undefined || this.sabadoHorario.SabadoInicio == '' || this.sabadoHorario.SabadoInicio == null ||
        this.sabadoHorario.SabadoInicioInt == undefined || this.sabadoHorario.SabadoInicioInt == '' || this.sabadoHorario.SabadoInicioInt == null ||
        this.sabadoHorario.SabadoFimInt == undefined || this.sabadoHorario.SabadoFimInt == '' || this.sabadoHorario.SabadoFimInt == null ||
        this.sabadoHorario.SabadoFim == undefined || this.sabadoHorario.SabadoFim == '' || this.sabadoHorario.SabadoFim == null ||
        this.sabadoHorario.SabadoTempo == undefined || this.sabadoHorario.SabadoTempo == '' || this.sabadoHorario.SabadoTempo == null)
        return true;
    // this.showMessageError = true;

    if (this.Domingo == true)

      if (this.domingoHorario.DomingoInicio == undefined || this.domingoHorario.DomingoInicio == '' || this.domingoHorario.DomingoInicio == null ||
        this.domingoHorario.DomingoInicioInt == undefined || this.domingoHorario.DomingoInicioInt == '' || this.domingoHorario.DomingoInicioInt == null ||
        this.domingoHorario.DomingoFimInt == undefined || this.domingoHorario.DomingoFimInt == '' || this.domingoHorario.DomingoFimInt == null ||
        this.domingoHorario.DomingoFim == undefined || this.domingoHorario.DomingoFim == '' || this.domingoHorario.DomingoFim == null ||
        this.domingoHorario.DomingoTempo == undefined || this.domingoHorario.DomingoTempo == '' || this.domingoHorario.DomingoTempo == null)
        return true;
    // this.showMessageError = true;
  }

  ValidaDtaChange() {
    const dta = (document.getElementById('DtaNasc')as HTMLInputElement)["value"];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

    if (dta == '')
      this.Dtanasc = false;
    else if (patternValidaData.test(dta)) {
      this.Dtanasc = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }


  ValidaDta(dta:any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;

    if (dta == '') {
      this.Dtanasc = false
    }
    else if (patternValidaData.test(dta)) {
      this.Dtanasc = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtanasc = true;
    }
    else
      this.Dtanasc = false
  }

  ValidaTelefone(tle:any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle:any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {

      this.TelMovVal = false;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovVal = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
    }
    else
      this.TelMovVal = false


  }

  ValidaTimer(hr:any, camp:any) {

    var isValid = /^([0-1]?[0-9]|2[0-4]):([0-5][0-9])(:[0-5][0-9])?$/.test(hr);

    if (!isValid) {
      document.getElementById(camp.id)!.focus();

      this.tradutor.get('TELACADASTROMEDICO.PRECISATERUMHORARIOVALIDO').subscribe((res: string) => {
        ;
        // this.AlgumErro(true, camp.name + ' ' + res);
        this.snackBarAlert.falhaSnackbar(res);
      });
    }
    else {
      
    }
  }

  ValidaTimerTempo(hr:any, camp:any) {
    var isValid = /^([0-1]?[0-9]|2[0-4]):([0-5][0-9])(:[0-5][0-9])?$/.test(hr);

    if (!isValid || hr == "00:00") {
      document.getElementById(camp.id)!.focus();

      this.tradutor.get('TELACADASTROMEDICO.PRECISATERUMHORARIOVALIDO').subscribe((res: string) => {
        ;
        // this.AlgumErro(true, camp.name + ' ' + res);
        this.snackBarAlert.falhaSnackbar(res);

      });
    }
    else {
      

    }
  }


  ValidaTimerInter(hr:any, camp:any) {

    if (hr != '') {
      var isValid = /^([0-1]?[0-9]|2[0-4]):([0-5][0-9])(:[0-5][0-9])?$/.test(hr);
      if (!isValid) {
        document.getElementById(camp.id)!.focus();

        this.tradutor.get('TELACADASTROMEDICO.PRECISATERUMHORARIOVALIDO').subscribe((res: string) => {
          ;
          // this.AlgumErro(true, camp.name + ' ' + res);
          this.snackBarAlert.falhaSnackbar(res);
        });
      } else {
        
      }

    }
  }

  ValidaTelefoneComercial(tle:any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
    }
    else
      this.TelComVal = false
  }

  ValidaClinicas(cli:any = []) {
    this.clinicas.value.forEach((element:any) => {

      cli.push({
        idClinica: element.idClinica,
      })
    });
    if (cli.length > 0) {
      this.clinicaVasil = false;
      this.clinicaVal = false;
    }
    else {
      this.clinicaVasil = false;
      this.clinicaVal = true;
    }
  }

  ValidaEspecialidades(esp:any = []) {
    this.especialidade.value.forEach((element:any) => {

      esp.push({
        idEspecialidade: element.idEspecialidade,
      })
    });

  }



  Nome = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  // clinica = new FormControl('', [Validators.required, Validators.maxLength(3)])
  // especialidades = new FormControl('', [Validators.required, Validators.maxLength(3)])
  cpf = new FormControl('', [Validators.required, Validators.maxLength(14)])
  // DtaNasc = new FormControl('', [Validators.required,  Validators.required])
  vlr = new FormControl('', [Validators.required, Validators.maxLength(11)])
  crm = new FormControl('', [Validators.required, Validators.maxLength(11)])
  Tel = new FormControl('', [Validators.required, Validators.minLength(13)])
  TelMov = new FormControl('', [Validators.required, Validators.minLength(13)])
  TelCom = new FormControl('', [Validators.required, Validators.minLength(13)])


  getErrorMessageNome() {
    return this.Nome.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
      this.Nome.hasError('Nome') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';
  }
  geterrorMessageVALOR() {
    return this.vlr.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
      this.vlr.hasError('valor') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';
  }

  // getErrorMessageNome() {
  //
  //   return this.Nome.hasError('required') ? this.translate.use: "{{ 'TELACADASTROMEDICO.VOLTAR' | translate }}";

  // }

  // getErrorMessageEmail() {
  //   return this.email.hasError('required') ? 'Esse campo precisa ser preenchido' :
  //     this.email.hasError('email') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
  //       '';

  // }
  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
      this.email.hasError('email') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';

  }
  getErrorMessageCPF() {
    return this.cpf.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
      this.cpf.hasError('cpf') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';

  }

  // getErrorMessageClinica() {
  //   return this.clinica.hasError('required') ? 'Uma clinica precisa ser selecionada' :
  //     this.clinica.hasError('clinica') ? 'Não é valido' :
  //       '';

  // }

  // getErrorMessageEspecialidades() {
  //   return this.especialidades.hasError('required') ? 'Uma especialidade precisa ser selecionada' :
  //     this.especialidades.hasError('especialidade') ? 'Não é valido' :
  //       '';

  // }

  // getErrorMessageTel() {
  //   return this.Tel.hasError('required') ? 'Telefone Invalido' :
  //     this.Tel.hasError('Telefone Comercial') ? 'Não é valido' :
  //       '';

  // }

  getErrorMessageTelMov() {
    return this.TelMov.hasError('required') ? 'TELACADASTROMEDICO.TELEFONEINVALIDO' :
      this.TelMov.hasError('Telefone Movel') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';

  }

  getErrorMessageTelCom() {
    return this.TelCom.hasError('required') ? 'TELACADASTROMEDICO.TELEFONEINVALIDO' :
      this.TelCom.hasError('Telefone Comercial') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';

  }

  // geterrorMessageDate() {
  //   return this.DtaNasc.hasError('required') ? 'Preencha uma data valida' :
  //     this.DtaNasc.hasError('DtaNasc') ? 'Data de nascimento inválida' :
  //       '';
  // }

  geterrorMessageCRM() {
    return this.crm.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
      this.crm.hasError('CRM') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';
  }


  public BlockDiaSemana(check:any, dia:any) {
    var end = document.getElementsByName(check);
    
    if (!(end["0"]as HTMLInputElement)["checked"]) {
      if (dia == 'Segunda') {
        this.Segunda = false;
        this.segundaHorario = [];
      }
      else if (dia == 'Terça') {
        this.Terca = false;
        this.tercaHorario = [];
      }
      else if (dia == 'Quarta') {
        this.Quarta = false;
        this.quartaHorario = [];
      }
      else if (dia == 'Quinta') {
        this.Quinta = false;
        this.quintaHorario = [];

      }
      else if (dia == 'Sexta') {
        this.Sexta = false;
        this.sextaHorario = [];

      }
      else if (dia == 'Sabado') {
        this.Sabado = false;
        this.sabadoHorario = [];

      }
      else if (dia == 'Domingo') {
        this.Domingo = false;
        this.domingoHorario = [];

      }
    }
    else {
      if (dia == 'Segunda') {
        this.Segunda = true;
        this.segundaHorario.SegundaInicio = '08:00';
        this.segundaHorario.SegundaInicioInt = '12:00';
        this.segundaHorario.SegundaFimInt = '13:00';
        this.segundaHorario.SegundaFim = '20:00';
        this.segundaHorario.SegundaTempo = '00:20';

      }
      else if (dia == 'Terça') {
        this.Terca = true;
        this.tercaHorario.TercaInicio = '08:00';
        this.tercaHorario.TercaInicioInt = '12:00';
        this.tercaHorario.TercaFimInt = '13:00';
        this.tercaHorario.TercaFim = '20:00';
        this.tercaHorario.TercaTempo = '00:20';

      }
      else if (dia == 'Quarta') {
        this.Quarta = true;
        this.quartaHorario.QuartaInicio = '08:00';
        this.quartaHorario.QuartaInicioInt = '12:00';
        this.quartaHorario.QuartaFimInt = '13:00';
        this.quartaHorario.QuartaFim = '20:00';
        this.quartaHorario.QuartaTempo = '00:20';

      }
      else if (dia == 'Quinta') {
        this.Quinta = true;
        this.quintaHorario.QuintaInicio = '08:00';
        this.quintaHorario.QuintaInicioInt = '12:00';
        this.quintaHorario.QuintaFimInt = '13:00';
        this.quintaHorario.QuintaFim = '20:00';
        this.quintaHorario.QuintaTempo = '00:20';

      }
      else if (dia == 'Sexta') {
        this.Sexta = true;
        this.sextaHorario.SextaInicio = '08:00';
        this.sextaHorario.SextaInicioInt = '12:00';
        this.sextaHorario.SextaFimInt = '13:00';
        this.sextaHorario.SextaFim = '20:00';
        this.sextaHorario.SextaTempo = '00:20';

      }
      else if (dia == 'Sabado') {
        this.Sabado = true;
        this.sabadoHorario.SabadoInicio = '08:00';
        this.sabadoHorario.SabadoInicioInt = '12:00';
        this.sabadoHorario.SabadoFimInt = '13:00';
        this.sabadoHorario.SabadoFim = '20:00';
        this.sabadoHorario.SabadoTempo = '00:20';

      }
      else if (dia == 'Domingo') {
        this.Domingo = true;
        this.domingoHorario.DomingoInicio = '08:00';
        this.domingoHorario.DomingoInicioInt = '12:00';
        this.domingoHorario.DomingoFimInt = '13:00';
        this.domingoHorario.DomingoFim = '20:00';
        this.domingoHorario.DomingoTempo = '00:20';

      }
    }

  }


  // SalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELACADASTROMEDICO.CADASTROSALVOCOMSUCESSO').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }


  // ErroSalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROMEDICO.ERROAOSALVAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }


  // ErroCarregar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROMEDICO.ERROAOSALVAR').subscribe((res: string) => {
  //       ;
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }


  imagemCertificado: any = "assets/build/img/Ícone-Certificado-Digital.png";
  arquivoCerfiticado: any;
  flgMostraCertificados: boolean = false;
  retornoCertificados: any = [];

  CadastrarCertificadoMedico(event:any): void {
    this.arquivoCerfiticado = event
    var objCertificado: any;
    objCertificado.idmedico = this.retornoMedico.idmedico
    objCertificado.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()

    objCertificado.dtavalidade = "01/05/2010"
    objCertificado.identificador = "obj do documento"
    this.certificadoDigitalService.CadastrarCertificado(objCertificado).subscribe((retorno:any) => {
      retorno;
      this.spinner.hide();
      this.GetListaCertificados();
    })

  }

  GetListaCertificados() {
    this.certificadoDigitalService.getListaCertificados("idMedico").subscribe((retorno:any) => {
      retorno;
      this.retornoCertificados = [
        { identificadorCertificado: "teste", dtaCadastro: "10/04/2020", dtaValidade: "01/01/2010" },
        { identificadorCertificado: "teste2", dtaCadastro: "10/04/2020", dtaValidade: "01/01/2010" },
        { identificadorCertificado: "teste3", dtaCadastro: "10/04/2020", dtaValidade: "01/01/2010" },
        { identificadorCertificado: "teste4", dtaCadastro: "10/04/2020", dtaValidade: "01/01/2010" },
        { identificadorCertificado: "teste5", dtaCadastro: "10/04/2020", dtaValidade: "01/01/2010" },
        { identificadorCertificado: "teste6", dtaCadastro: "10/04/2020", dtaValidade: "05/05/2015" }
      ]
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })

  }
  LimpaFileCertificado() {
    (document.getElementById('certificado')as HTMLInputElement)["value"] = '';
  }

  CadastrarCertificadoNovoMedico() {
    
    this.flgMostraCertificados = true;
    this.CarregaMedico(this.idMedico);
    this.ngxSmartModalService.getModal('CertificadoAposCadastro').close();
  }

  CancelarCadastroCertificado() {
    this.ngxSmartModalService.getModal('CertificadoAposCadastro').close();
    this.LimparCampos()
  }

  aplicarMascaraValor(v:any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor:any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

  VerificaOrientacao() {
    
  }
}
