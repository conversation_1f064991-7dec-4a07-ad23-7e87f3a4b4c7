import { Injectable } from "@angular/core";
import { HttpClient, HttpParams } from "@angular/common/http";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment";
import { ExameClinica, ExamePaciente } from '../model/exames';
import { SpinnerService } from "./spinner.service";

@Injectable({
    providedIn: 'root'
})
export class ExamesService {
  constructor(private http: HttpClient , private spinner: SpinnerService) {}
  public headers = new Headers({ "Content-Type": "application/json" });

  public CarregaExamesGrid(inicio:any, fim:any, pesquisa:any, idClinica:any): Observable<any> {
    this.spinner.show();
    let params = new HttpParams();
    params = params.append("inicio", String(inicio));
    params = params.append("fim", String(fim));
    params = params.append("pesquisa", String(pesquisa));
    params = params.append("idClinica", String(idClinica));
    
    return this.http.get(
      environment.apiEndpoint + "/Exame/CarregaExamesGrid/",
      { params }
    );
  }

  public GetExamesClinica(pesquisa:any, idClinica:any): Observable<any> {
    this.spinner.show();
    let params = new HttpParams();
    params = params.append("pesquisa", String(pesquisa));
    params = params.append("idClinica", String(idClinica));
    return this.http.get(environment.apiEndpoint + "/Exame/GetExamesClinica", {
      params,
    });
  }

  
  public GetResultados(idPaciente:any,idClinica:any): Observable<any> {
    this.spinner.show();
    let params = new HttpParams();
    params = params.append("idPaciente", String(idPaciente));
    params = params.append("idClinica", String(idClinica));
    return this.http.get(environment.apiEndpoint + "/Exame/GetResultados", {
      params,
    });
  }
  
  public inativarExame(id:any): Observable<any> {
    this.spinner.show();
    return this.http.delete(environment.apiEndpoint + '/Exame/InativarExame/' + id );
  }
  
  
  public SalvarExame(Objexame: ExameClinica): Observable<any> {
  this.spinner.show();
  
  return this.http.post(environment.apiEndpoint + '/Exame/SalvaExameClinica', Objexame);
}


public SalvarResultadoExame(Objexame: ExamePaciente): Observable<any> {
  this.spinner.show();
  
  return this.http.post(environment.apiEndpoint + '/Exame/SalvarResultadoExame', Objexame);
}
}