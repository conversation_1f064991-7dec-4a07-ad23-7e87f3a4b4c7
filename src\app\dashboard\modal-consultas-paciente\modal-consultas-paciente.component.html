<div class="modal-container">
    <div mat-dialog-title class="modal-header">
        <h2 class="modal-title">
            <mat-icon class="title-icon">calendar_today</mat-icon>
            Consultas {{ data.nomePaciente ? 'de ' + data.nomePaciente : '' }}
        </h2>
        <button mat-icon-button class="close-button" (click)="onClose()">
            <mat-icon>close</mat-icon>
        </button>
    </div>

    <mat-dialog-content class="modal-content">
        <div *ngIf="data.consultas && data.consultas.length > 0; else noConsultas">
            <mat-card *ngFor="let consulta of data.consultas; let i = index" class="consulta-card">
                <mat-card-content class="consulta-content">
                    <div class="consulta-info">
                        <div class="consulta-header">
                            <h3 class="medico-nome">{{ consulta.nomeMedico }}</h3>
                            <span class="consulta-tipo" [class.presencial]="consulta.flgPresencial"
                                [class.online]="!consulta.flgPresencial">
                                <mat-icon>{{ consulta.flgPresencial ? 'person' : 'videocam' }}</mat-icon>
                                {{ getTipoConsulta(consulta.flgPresencial) }}
                            </span>
                        </div>

                        <div class="consulta-details">
                            <div class="data-consulta">
                                <mat-icon class="detail-icon">schedule</mat-icon>
                                <span>{{ formatarData(consulta.dataConsulta) }}</span>
                            </div>

                            <div class="id-consulta">
                                <mat-icon class="detail-icon">badge</mat-icon>
                                <span>ID: {{ consulta.id }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="consulta-actions" *ngIf="!consulta.flgPresencial">
                        <button mat-raised-button color="primary" class="acesso-button"
                            (click)="onAcessarConsulta(consulta)">
                            <mat-icon>video_call</mat-icon>
                            Acessar Consulta
                        </button>
                    </div>
                </mat-card-content>
            </mat-card>
        </div>

        <ng-template #noConsultas>
            <div class="no-consultas">
                <mat-icon class="no-consultas-icon">event_busy</mat-icon>
                <p>Nenhuma consulta encontrada</p>
            </div>
        </ng-template>
    </mat-dialog-content>

    <mat-dialog-actions class="modal-actions">
        <button mat-button (click)="onClose()">Fechar</button>
    </mat-dialog-actions>
</div>