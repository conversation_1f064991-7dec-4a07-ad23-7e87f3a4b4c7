// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}

.recipe-container {
  min-height: 100vh;
  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Cards
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: none;
  margin-bottom: 24px;
  overflow: hidden;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// Headers
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

// Sections
.section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
}

// Form
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-group {
  flex: 1;
  min-width: 250px;
}

.full-width {
  width: 100%;
}

// Editor
.editor-container {
  border: 1px solid $border-color;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 8px;
}

// CKEditor custom styles
:host ::ng-deep {
  .ck-editor__editable_inline {
    min-height: 30vh;
    max-height: 60vh;
    padding: 16px;
    font-family: inherit;
    color: $text-primary;
  }
  
  .ck-toolbar {
    border-color: $border-color !important;
    background-color: $secondary-color !important;
  }
  
  .ck-content {
    border-color: $border-color !important;
  }
  
  .ck-focused {
    border-color: $primary-color !important;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1) !important;
  }
}

// ng-select overrides
.modern-select {
  margin-bottom: 0;
  
  ::ng-deep {
    .ng-select-container {
      border-radius: 4px;
      border-color: $border-color;
      min-height: 52px;
      
      &:hover {
        border-color: $primary-light;
      }
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
    }
    
    .ng-option {
      padding: 10px 16px;
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary;
  
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background-color: rgba($primary-color, 0.05);
  }
}

.btn-success {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-link {
  background: none;
  color: $primary-color;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .card-footer {
    flex-wrap: wrap;
    
    .btn {
      flex: 1;
    }
  }
}