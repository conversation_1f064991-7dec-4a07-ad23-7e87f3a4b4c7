<div class="main-container">
  <div class="queue-card fade-in">
    <div class="status-indicator">
      <div class="status-dot"></div>
      <span class="status-text">Sistema Online</span>
    </div>

    <h1 class="welcome-title">Bem-vindo{{ dadosQuestionario?.nome ? ', ' + dadosQuestionario?.nome : '' }}!</h1>
    <p class="welcome-subtitle">Você será chamado em breve para o atendimento</p>

    <!-- Indicador de tipo de usuário -->
    <div class="user-type-indicator">
      <i class="fas fa-user" style="margin-right: 8px;"></i>
      <span>Acesso Rápido</span>
    </div>

    <div class="position-container">
      <div id="queuePosition" style="display: block;">
        <p class="position-label">Sua Posição</p>
        <div class="position-circle">
          <span class="position-number">{{ PesicaoFila  }}</span>
        </div>
      </div>

      <div id="enterButton" style="display: none;">
        <button class="enter-button" onclick="enterConsultation()">
          <i class="fas fa-video" style="margin-right: 8px;"></i>
          Entrar
        </button>
      </div>
    </div>

    <div id="quitSection" style="display: block;">
      <button class="quit-button" (click)="quitQueue()">
        <i class="fas fa-times" style="margin-right: 8px;"></i>
        Desistir da Consulta
      </button>
    </div>

    <div class="instructions-card">
      <div class="instructions-header">
        <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
        Instruções Importantes
      </div>
      <div class="instructions-content">
        <div class="instruction-item">
          <div class="instruction-number">1</div>
          <div class="instruction-text">
            Feche todos os aplicativos abertos, inclusive WhatsApp, mantendo apenas o navegador aberto.
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-number">2</div>
          <div class="instruction-text">
            Aumente o volume do seu dispositivo e desconecte fones bluetooth.
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-number">3</div>
          <div class="instruction-text">
            Conecte-se a uma rede WiFi estável para garantir a qualidade do atendimento.
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-number">4</div>
          <div class="instruction-text">
            Aceite o acesso à câmera e microfone quando solicitado pelo sistema.
          </div>
        </div>
        <div class="instruction-item">
          <div class="instruction-number">5</div>
          <div class="instruction-text">
            Se a conexão falhar, acesse novamente a plataforma e solicite um novo atendimento.
          </div>
        </div>
      </div>
    </div>
  </div>
</div>