<mat-card appearance="outlined" class="profile-card mother-div">
    <mat-card-content >
        <div class="profile-header">
            <div class="profile-title">
                <h2>Perfil do Usuário</h2>
            </div>

            <div class="">
                <button mat-stroked-button class="btn-return" onclick='history.go(-1)'>
                    <mat-icon style="height: unset!important;">arrow_back</mat-icon>
                </button>
            </div>
        </div>

        <div class="profile-content">
            <div class="profile-sidebar">
                <div class="profile-image-container" (click)="openModalUploadFotos()">
                    <label for="imageperfilusuario">
                        <div class="profile-image-wrapper">
                            <img [src]="ImagemPessoa" class="profile-image" alt="Foto de Perfil"
                                title="{{'TELAPERFIL.FOTODEPERFIL' | translate}}">
                            <div class="profile-image-overlay">
                                <mat-icon>photo_camera</mat-icon>
                            </div>
                        </div>
                    </label>
                </div>
                <div class="profile-info">
                    <h3 class="profile-name">{{nome}}</h3>
                    <p class="profile-email">{{email}}</p>
                </div>
            </div>

            <div class="profile-form-container">
                <h3 class="section-title">Informações Pessoais</h3>
                <form>
                    <div class="form-row">
                        <div class="form-group form-group-full">
                            <label for="nome">Nome</label>
                            <div class="input-wrapper">
                                <input type="text" class="form-input" id="nome" [(ngModel)]="nome" name="nome" placeholder="Nome">
                                <div class="input-error" *ngIf="flgNumerosOuSimbolosNome">
                                    Nome não deve ter nem números nem símbolos. Apenas letras.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group form-group-half">
                            <label for="email">E-mail</label>
                            <div class="input-wrapper">
                                <input type="email" class="form-input" id="email" [(ngModel)]="email" name="email" placeholder="E-mail">
                            </div>
                        </div>
                        <div class="form-group form-group-half">
                            <label for="telefone">Telefone</label>
                            <div class="input-wrapper">
                                <input type="text" class="form-input" id="telefone" [(ngModel)]="telefone" name="telefone" placeholder="(XX) XXXX-XXXX"placeholder="{{ 'TELAAGENDACONTATO.TELEFONE' | translate }}"
                                (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)" 
                                (blur)="ValidaTelefone($any($event.target).value)" maxlength="15" minlength="14">
                            <div class="invalid-feedback" *ngIf="TelVal">
                                {{ 'TELAAGENDACONTATO.TELEFONEINVALIDO' | translate }}
                            </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group form-group-half">
                            <label for="telefoneMovel">Telefone Móvel</label>
                            <div class="input-wrapper">
                                <input type="text" class="form-control" id="telefoneMovel" [(ngModel)]="telefoneMovel"
                                name="telefoneMovel" placeholder="{{ 'TELAAGENDACONTATO.CELULAR' | translate }}"
                                (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)" 
                                (blur)="ValidaTelefoneMovel($any($event.target).value)" maxlength="15" minlength="14"
                                required>
                            <div class="invalid-feedback" *ngIf="TelMovVal">
                                {{ 'TELAAGENDACONTATO.TELEFONEINVALIDO' | translate }}
                            </div>
                            <div class="invalid-feedback" *ngIf="TelMovValVasil && !TelMovVal">
                                {{ 'TELAAGENDACONTATO.ESSECAMPOPRECISASERPREECHIDO' | translate }}
                           </div>
                            </div>
                        </div>
                        <div class="form-group form-group-half">
                            <label for="telefoneComercial">Telefone Comercial</label>
                            <div class="input-wrapper">
                                <input type="text" class="form-control" id="telefoneComercial" [(ngModel)]="telefoneComercial"
                            name="telefoneComercial"
                            placeholder="{{ 'TELAAGENDACONTATO.TELEFONECOMERCIAL' | translate }}"
                            (keyup)="mascaraTelefone($event)" (keypress)="mascaraTelefone($event)" 
                            (blur)="ValidaTelefoneComercial($any($event.target).value)" maxlength="15" minlength="14">
                            <div class="invalid-feedback" *ngIf="TelComVal">
                            {{ 'TELAAGENDACONTATO.TELEFONEINVALIDO' | translate }}
                            </div>
                            </div>
                        </div>
                    </div>

                    <h3 class="section-title">Segurança</h3>
  
                    <div class="form-row">
                        <div class="form-group form-group-half">
                            <div class="password-wrapper">
                                <input [type]="showSenhaAtual ? 'text' : 'password'" class="form-input" id="senhaAtual"
                                    [(ngModel)]="senhaAtual" name="senhaAtual" placeholder="Senha Atual" (change)="flgSenhaAlterada = true">
                                <button type="button" class="password-toggle" (click)="showSenhaAtual = !showSenhaAtual">
                                    <mat-icon>{{ showSenhaAtual ? 'visibility_off' : 'visibility' }}</mat-icon>
                                </button>
                            </div>
                            <div class="input-error" *ngIf="flgSenhaInvalida">
                                Senha inválida.
                            </div>
                        </div>

                        <div class="form-group form-group-half">
                            <div class="password-wrapper">
                                <input [type]="showNovaSenha ? 'text' : 'password'" class="form-input" id="novaSenha"
                                    [(ngModel)]="novaSenha" name="novaSenha" placeholder="Nova Senha">
                                <button type="button" class="password-toggle" (click)="showNovaSenha = !showNovaSenha">
                                    <mat-icon>{{ showNovaSenha ? 'visibility_off' : 'visibility' }}</mat-icon>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="profile-footer">
            <button class="btn-save" (click)="Salvar()">
                <mat-icon>save</mat-icon>
                <span>{{ 'TELACADASTROUSUARIO.SALVAR' | translate }}</span>
            </button>
        </div>
    </mat-card-content>
</mat-card>