import { Contato } from './contato';
import { Endereco } from './endereco';
import { PessoaResponsavel } from './pessoaResponsavel';




export class Clinica {
    idClinica?: number;
    DesClinica?: string;
    Caracterizacao?: string;
    DtaLicenca?: Date | null;
    Cnpj?: string;
    Crm?: string;
    Cnes?: string;
    IssCcm?: string;
    Logo?: string;
    IdContato?: number;
    IdEndereco?: number;
    DtaCadastro?: Date;
    FlgInativo?: boolean;
    IdUsuarioGerador?: number;
    FlgSomenteProntuario?: boolean;
    FlgFilaEspera?: boolean;
    FlgSolicitarOrientacao?: boolean;
    FlgHabilitaChat?:boolean;
    FlgHabilitaPagamento?: boolean;
    valorConsulta?: string;

    RemoverFoto?: boolean;

    Imagem64?: string;

    DtaLicencaCustom?: string;


    endereco?: Endereco;

    contato?: Contato;

    responsaveis?:PessoaResponsavel[];
}

export class ClinicaCadastro {
    idClinica?: number;
    desClinica?: string;
    FlgInclusao?: boolean;
    FlgExclusao?: boolean;
    FlgOriginal?: boolean;
}

export class SelectClinica {
    idClinica?: number;
    nomeClinica?: string;
}
