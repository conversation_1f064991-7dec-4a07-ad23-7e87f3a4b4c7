import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { RecadoDia } from 'src/app/model/medico';
import { MedicoService } from 'src/app/service/medico.service';
import { SpinnerService } from 'src/app/service/spinner.service';

@Component({
  selector: 'app-modal-mensagem-do-dia',
  standalone: true,
  imports: [
    MatSelectModule,
    CommonModule,
    FormsModule,
    TranslateModule,
    MatButtonModule,
    MatDialogModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    NgSelectModule,
    ReactiveFormsModule,
  ],
  templateUrl: './modal-mensagem-do-dia.component.html',
  styleUrl: './modal-mensagem-do-dia.component.scss'
})
export class ModalMensagemDoDiaComponent {
  DesRecadoDia: string = '';
  campoDesRecadoVazio: boolean = false;
  DadosRecados: any = [];
  IdMedico: number | null = null;
  dataEnvio: Date = new Date();
  lsMedicos: any[] = [];
  IdTipoUsuario: number | null = null;
  constructor(
    private snackBarAlert: AlertComponent,
    private spinner: SpinnerService,
    private usuarioLogadoService: UsuarioLogadoService,
    private medicoService: MedicoService,
    private dialogRef: MatDialogRef<ModalMensagemDoDiaComponent>,
  ) {
    this.IdTipoUsuario = this.usuarioLogadoService.getIdTipoUsuario()!;
    if (this.IdTipoUsuario == 2) {
      this.IdMedico = this.usuarioLogadoService.getIdMedico()!;
      this.carregarMensagemDia();
    }

    this.carregarMedicos()
  }

  onMedicoChange() {
    this.carregarMedicos()
  }

  carregarMedicos() {
    this.spinner.show();
    this.medicoService.getMedicos(null, this.usuarioLogadoService.getIdUltimaClinica())
      .subscribe(
        retorno => {
          this.lsMedicos = retorno;
          this.spinner.hide();
        },
        erro => {
          console.error(erro);
          this.spinner.hide();
        }
      );
  }

  carregarMensagemDia() {
    if(!this.dataEnvio)
      return;
    
    this.medicoService.getRecadoDia(this.IdMedico, this.usuarioLogadoService.getIdUltimaClinica(), 'Todos', this.dataEnvio.toDateString()).subscribe((retorno) => {
      if (retorno) {
        this.DadosRecados = retorno;
      }
      this.spinner.hide();
    }, err => {

      this.snackBarAlert.falhaSnackbar('Erro ao Carregar Agenda de Espera')
      console.error(err)
      this.spinner.hide();
    })

  }
  fecharModalMensagemDoDia() {
    this.dialogRef.close();
  }

  SalvarMensagem() {
    try {
      if (this.DesRecadoDia == null || !this.DesRecadoDia.trim()) {
        this.snackBarAlert.falhaSnackbar('Preencha o campo da mensagem')
        this.campoDesRecadoVazio = true;
        this.DesRecadoDia = "";
        return
      }

      let data = new Date();
      if (this.dataEnvio < data) {
        this.snackBarAlert.falhaSnackbar('Data inválida, favor escolher uma data futura')
        return;
      }

      var mens = new RecadoDia
      mens.DesRecado = this.DesRecadoDia;
      mens.IdMedico = this.IdMedico!;
      mens.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
      mens.DtaDiaRecado = this.dataEnvio;
      mens.idClinica = this.usuarioLogadoService.getIdUltimaClinica();
      mens.FlgVisualizado = false;
      this.medicoService.salvarRecadoDia(mens).subscribe((retorno) => {

        if (retorno) {
          this.snackBarAlert.sucessoSnackbar('Recado criado com sucesso.');
          this.DesRecadoDia = "";
          this.campoDesRecadoVazio = false;
        }
        this.spinner.hide();

      }, err => {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar")
        console.error(err)
        this.spinner.hide();
      })


    } catch (error) {
      this.snackBarAlert.falhaSnackbar("Falha na conexão")
      console.error(error)
      this.spinner.hide();
    }
  }
}
