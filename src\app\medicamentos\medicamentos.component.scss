@import "../pacientes/pacientes.component.scss";
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');
.principal-container {
    margin-left: 15%;
    bottom: 150px;
}

.card-body {
    padding-left: unset;
    padding-right: unset;
}

.label_paciente {
    word-break: break-all;
}
// Variables based on your color scheme
$primary-color: #2E8B57;  
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

// Import fonts
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');

// Base styles
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', 'Cairo', sans-serif;
  background-color: $bg-color;
  color: $text-primary;
  line-height: 1.6;
}

// Card container
.medication-card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  margin: 20px;
  overflow: auto;
  border: none;
}

.container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

// Header Section
.header-section {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: $primary-light;
  color: $primary-color;
  border-radius: $border-radius;
  padding: 8px 16px;
  font-weight: 500;
  transition: $transition;
  border: none;
  
  &:hover {
    background-color: darken($primary-light, 5%);
  }
}

.page-title {
  flex: 1;
  text-align: center;
  color: $primary-color;
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  font-family: 'Cairo', sans-serif;
  
  @media (max-width: 768px) {
    text-align: left;
    font-size: 1.5rem;
  }
}

// Form & List Sections
.form-section,
.list-section {
  background-color: $card-bg;
  border-radius: $border-radius;
  margin-bottom: 30px;
  border: 1px solid $border-color;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid $border-color;
  
  @media (max-width: 600px) {
    justify-content: center;
  }
}

.section-icon {
  color: $primary-color;
  margin-right: 15px;
}

.section-header h2 {
  color: $primary-dark;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  font-family: 'Cairo', sans-serif;
}

// Form styles
.form-content {
  padding: 24px;
}

.form-field {
  width: 100%;
  margin-bottom: 20px;
  
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
  }
  
  ::ng-deep .mat-form-field-outline {
    color: $border-color;
  }
  
  ::ng-deep .mat-form-field-label {
    color: $text-secondary;
  }
  
  ::ng-deep .mat-input-element {
    color: $text-primary;
  }
  
  ::ng-deep .mat-form-field-flex {
    background-color: $card-bg;
  }
}

.form-controls {
  display: flex;
  flex-direction: row;
  gap: 24px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.toggle-container {
  display: flex;
  align-items: center;
  min-height: 56px;
  
  ::ng-deep .mat-slide-toggle-bar {
    background-color: rgba($primary-color, 0.3);
  }
  
  ::ng-deep .mat-slide-toggle-thumb {
    background-color: $primary-light;
  }
  
  ::ng-deep .mat-checked .mat-slide-toggle-thumb {
    background-color: $primary-color;
  }
}

.clinics-field {
  flex: 1;
  
  ::ng-deep .mat-select-panel {
    background: $card-bg;
  }
  
  ::ng-deep .mat-option.mat-selected:not(.mat-option-disabled) {
    color: $primary-color;
  }
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  
  @media (max-width: 600px) {
    justify-content: center;
    flex-direction: column;
    gap: 12px;
  }
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 20px;
  border-radius: $border-radius;
  font-weight: 500;
  border: none;
  transition: $transition;
  min-width: 120px;
  
  mat-icon {
    font-size: 18px;
  }
}

.cancel-button {
  background-color: $secondary-color;
  color: $text-secondary;
  
  &:hover {
    background-color: darken($secondary-color, 5%);
  }
}

.save-button {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

// Search styles
.search-container {
  padding: 20px;
  display: flex;
  gap: 20px;
  align-items: flex-start;
  border-bottom: 1px solid $border-color;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.search-field-container {
  flex: 1;
}

.search-field {
  width: 100%;
  margin-bottom: 0;
  
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 0;
  }
  
  button {
    color: $primary-color;
    background-color: transparent;
  }
}

.legend-container {
  position: relative;
  min-width: 150px;
}

.legend-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background-color: $secondary-light;
  color: $primary-dark;
  padding: 10px 16px;
  border-radius: $border-radius;
  border: 1px solid $border-color;
  font-weight: 500;
}

.legend-content {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  z-index: 10;
  padding: 10px;
  margin-top: 8px;
  border: 1px solid $border-color;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  color: $text-secondary;
  
  mat-icon {
    font-size: 1.2rem;
  }
}

// Medication List
.medication-list {
  padding: 20px;
}

.medication-table {
  width: 100%;
  border-collapse: collapse;
  
  tr {
    border-bottom: 1px solid $border-color;
    transition: $transition;
    
    &:hover {
      background-color: $primary-light;
    }
    
    &.highlighted {
      background-color: rgba($primary-light, 0.5);
      font-weight: 500;
      
      &:hover {
        background-color: rgba($primary-light, 0.7);
      }
    }
  }
  
  td {
    padding: 16px;
    vertical-align: middle;
    
    strong {
      color: $primary-color;
      margin-right: 8px;
    }
  }
  
  .medication-name {
    width: 40%;
  }
  
  .medication-posology {
    width: 45%;
  }
  
  .medication-actions {
    width: 15%;
    text-align: right;
  }
}

// Mobile card view
.medication-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.medication-card-item {
  background-color: $card-bg;
  border-radius: $border-radius;
  padding: 16px;
  box-shadow: $box-shadow;
  border: 1px solid $border-color;
  position: relative;
  transition: $transition;
  
  &.highlighted {
    border-left: 4px solid $primary-color;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.medication-info {
  padding-right: 40px;
}

.medication-detail {
  margin-bottom: 12px;
  
  h3 {
    color: $primary-color;
    font-size: 0.9rem;
    margin-bottom: 4px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    color: $text-primary;
    word-break: break-word;
  }
}

.card-actions {
  position: absolute;
  top: 12px;
  right: 12px;
}

.action-fab {
  background-color: $primary-light;
  color: $primary-color;
}

.action-menu {
  position: absolute;
  top: 40px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: $card-bg;
  padding: 8px;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  z-index: 5;
}

// Buttons
.edit-button {
  color: $primary-color;
  background-color: $primary-light;
  transition: $transition;
  
  &:hover {
    background-color: rgba($primary-color, 0.2);
  }
}

.delete-button {
  color: $error-color;
  background-color: rgba($error-color, 0.1);
  transition: $transition;
  
  &:hover {
    background-color: rgba($error-color, 0.2);
  }
}

// Load more
.load-more {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.load-more-button {
  background-color: $primary-light;
  color: $primary-color;
  padding: 8px 24px;
  border-radius: $border-radius;
  font-weight: 500;
  transition: $transition;
  
  &:hover {
    background-color: rgba($primary-color, 0.2);
  }
}

// Modal styling
::ng-deep .nsm-centered {
  border-radius: $border-radius !important;
  overflow: hidden !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
}

.modal-content {
  padding: 0;
}

.modal-header {
  background-color: $primary-light;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  
  h2 {
    margin: 0;
    color: $primary-dark;
    font-weight: 600;
  }
}

.warning-icon {
  color: $error-color;
  font-size: 24px;
}

.modal-body {
  padding: 24px;
  color: $text-secondary;
}

.modal-footer {
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid $border-color;
}

.confirm-button {
  background-color: $error-color;
  color: white;
  padding: 8px 20px;
  border-radius: $border-radius;
  transition: $transition;
  
  &:hover {
    background-color: darken($error-color, 7%);
  }
}

// Responsive utility classes
.desktop-only {
  @media (max-width: 768px) {
    display: none;
  }
}

.mobile-only {
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
}

// Animations for mobile menu
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

:host ::ng-deep {
  // Override Material styles for consistency
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid .mat-form-field-outline-thick {
    color: $error-color;
  }
  
  .mat-select-value {
    color: $text-primary;
  }
  
  .mat-hint, .mat-form-field-hint-wrapper {
    color: $text-secondary;
  }
  
  .mat-error {
    color: $error-color;
  }
  
  // Animation for mobile menu
  .action-menu.ng-trigger-openClose {
    &.ng-animating {
      animation-duration: 0.2s;
      animation-fill-mode: forwards;
    }
    
    &.open {
      animation-name: slideIn;
    }
    
    &.closed {
      animation-name: slideOut;
      display: none;
    }
  }
}
.fab button.mainb {
    position: absolute;
    width: 45px;
    height: 45px;
    border-radius: 30px;
    background-color: #1265b9;
    right: 0;
    bottom: -7px;
    z-index: 20;
}

.fab button.mainb:active+ul,
.fab button.mainb:focus+ul,
.fab button.mainb:target {
    bottom: 38px;
}

.botao-m {
    padding-left: 5px;
    padding-right: 5px;
}

.rotate-font {
    left: -61px;
    text-transform: uppercase;
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
    position: absolute;
    font-size: 13px;
    padding: 10px;
    border-top: 1px solid #ddd;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    top: 45px;
}

.rototate-font-medic {
    left: -70px;
    text-transform: uppercase;
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
    position: absolute;
    top: 50px;
    font-size: 13px;
    padding: 10px;
    border-top: 1px solid #ddd;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    background: #178aff;
    color: white;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.rotate-font-end {
    left: -50px;
    text-transform: uppercase;
    -webkit-transform: rotate(270deg);
    transform: rotate(270deg);
    position: absolute;
    top: 70px;
    font-size: 13px;
    padding: 10px;
    border-top: 1px solid #ddd;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.title-medicamentos {
    font-family: Cairo, sans-serif;
    text-align: center; 
    justify-content: center; 
    margin: 20px auto;
}
.mother-div {
    padding: 20px !important;
}
.card-medicamentos {
    margin-left: -8px;
    background: #f5f5f5;
}
.cadastrar-medicam {
    margin-left:-3px;
    margin-top: 6px
}
.novos-medicam {
    height:unset!important; 
    margin-right: 30px; 
    margin-left:15px; 
    font-size: 2.25rem;
    color: #1265b9;
}
.cadast-medc-mobile {
    display: none;
}

.buscar-icone {
    margin-left: -4%;
    margin-top: -4px;
}

@media (max-width: 2000px) and (min-width: 1700px) {
    .rotate-font {
        left: -71px;
        top: 105px;
    }
    .rototate-font-medic {
        left: -84px;
        top: 45px;
    }
    .rotate-font-end {
        left: -66px;
        top: 70px;
    }
}

@media (max-width: 1560px) {
    .buscar-icone {
        margin-left: -6%;
    }
    .tabela {
        right: -12%;
    }
}

@media (max-width: 1200px) {
    .tabela {
        right: -16%;
    }
    .buscar-icone {
        margin-left: -8%;
    }
}

@media (max-width: 1000px) {
    .buscar-icone {
        margin-left: -10%;
    }
    .tabela {
        right: -20%;
    }
}

@media (max-width: 1500px) and (min-width: 1200px) {
    .rotate-font {
        left: -71px;
        text-transform: uppercase;
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
        position: absolute;
        font-size: 13px;
        padding: 10px;
        border-top: 1px solid #ddd;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        top: 45px;
    }
    .rototate-font-medic {
        left: -83px;
        text-transform: uppercase;
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
        position: absolute;
        top: 42px;
        font-size: 13px;
        padding: 10px;
        border-top: 1px solid #ddd;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        background: #178aff;
        color: white;
    }
    .rotate-font-end {
        left: -65px;
        text-transform: uppercase;
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
        position: absolute;
        top: 70px;
        font-size: 13px;
        padding: 10px;
        border-top: 1px solid #ddd;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }
}

@media (max-width: 1054px) {
    .rotate-font {
        left: -71px;
        top: 25px;
    }
    .rotate-font-medic {
        left: -83px;
        top: 30px;
    }
    .rotate-font-end {
        left: -65px;
        top: 65px;
    }
    //.flag-left{
    //    bottom: 15px;
    //}
    //.flag-right{
    //    bottom: 15px;
    //}
}

@media (max-width: 425px) {
    .principal-container {
        margin-left: 0px;
    }
    .botao-m {
        padding-left: 7px;
        padding-right: 25px;
    }
}

.textboxinput {
    max-height: 300px !important;
}

@media (max-width: 425px) {
    .img-circle {
        margin-left: 25% !important;
    }
}

input[type="file"] {
    display: none;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.img-circle {
    margin-left: auto;
    margin-right: auto;
}

.example-chip-list {
    width: 100%;
}

@media (min-width: 1251px) {
    .header-button {
        padding: 10px;
        margin-right: 10px;
        margin-left: 35px;
    }
}

@media (max-width: 1250) {
    .header-button {
        padding: 10px;
        margin-right: 10px;
        margin-left: 20px;
    }
}

@media (max-width: 1050px) {
    .header-button {
        padding: 10px;
        margin-right: 10px;
        margin-left: -7px;
    }
}

@media (max-width: 991px) {
    .header-button {
        padding: 10px;
        margin-right: 10px;
        margin-left: 0px;
    }
    .tabela {
        margin-right: -8%;
    }
    .buscar-icone {
        margin-left: -8%;
    }
}

@media (max-width: 790px) {
    table {
        max-width: 96%;
        margin-left: 10px;
    }
}

@media (max-width: 425px) {
    #UsuarioForm {
        padding: 20px;
    }
}

.button_custom {
    margin-left: 15px;
}

@media (max-width: 575px) {
    .button_custom {
        margin-left: -1px !important;
    }
    .tabela {
        margin: 0 auto;
        justify-content: center;
        text-align: center;
        padding: 0;
    }
}

@media (max-width: 390px) {
    .legenda {
        display: none;
    }
    .button_custom {
        margin-left: 0px !important;
    }
}

$primary: #348bc1;
.tabela {
    right: -8%;
}

.coluna {
    margin-top: 5px;
}

tr:hover {
    background: unset;
    cursor: pointer;
}

table thead {
    background: unset;
    color: unset;
}

.cor {
    background: white;
    color: #1265b9;
}

small {
    color: #666;
    font-size: 13px;
}

.margem-c {
    margin-left: -20px;
}

#paciente {
    width: 20%;
    margin-right: 10px;
    border-left: 1px solid #ddd;
}

.estrelas input[type="radio"] {
    display: none;
}

.estrelas label i.fa:before {
    content: "\f005";
    color: #fc0;
}

.estrelas input[type="radio"]:checked~label i.fa:before {
    color: #ccc;
}

#cpf {
    width: 10%;
    margin-top: auto;
    margin-bottom: auto;
    border-left: 1px solid #ddd;
}

#data {
    margin-bottom: auto;
    margin-top: auto;
    width: 8%;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
}

.img-circle {
    border-radius: 50%;
    width: 80px !important;
    height: 80px !important;
}

#acoes {
    width: 21%;
}

.date {
    margin-top: 10px !important;
}

.md-chip {
    margin-left: 20px;
}

.spacer-card {
    padding: 5px;
    padding-left: 20px;
    padding-top: 30px;
}

.panel_initial {
    border-bottom-left-radius: 10px !important;
    border-top-left-radius: 10px !important;
}

.finish_panel {
    border-bottom-right-radius: 10px !important;
    border-top-right-radius: 10px !important;
}

.Title-b {
    font-weight: bolder;
    color: #0983ff;
}

.panel_button {
    border: 1px solid #dcdbdb !important;
    border-radius: 0px;
}

.input-align {
    margin-left: 10px;
}

table {
    background: #fff;
}

.card_table {
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.card_table:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    transition: 0.3s;
}

.div_img {
    margin-left: 25px;
}

.div_paciente {
    margin-top: auto;
    margin-bottom: auto;
    margin-left: 25px;
}

.label-paciente {
    margin-top: auto;
    margin-bottom: auto;
}

.star_point {
    font-size: 20px;
    color: #ffc107;
}

.mat-mdc-icon-button:hover {
    box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.2), 0 4px 20px 0 rgba(0, 0, 0, 0.19);
}

@media (max-width: 1380px) {
    .svg-icon {
        width: 20px;
    }
    // .mat-icon-button {
    //   width: 22px !important;
    //   height: 40px !important;
    // }
    // .material-icons {
    //   font-size: 18px !important;
    // }
}

.align-button {
    // position: absolute;
    top: 40px;
    margin-left: 5.5%;
}

.align-buttonII {
    margin-left: 38px;
    top: 40px;
}

.mini-mini {
    width: 10px;
    height: 10px;
    background: $primary;
}

.row-button {
    width: unset;
}

.mini-miniI {
    margin-right: 109px;
}

.mini-miniII {
    margin-right: 77px;
}

.mini-miniIII {
    margin-right: 110px;
}

.fab ul li label {
    margin-top: 5px !important;
    margin-right: -10px;
    margin-left: 20px;
    border-top-right-radius: 0px;
    width: 135px;
    font-size: 12px;
}

@media (max-width: 425px) {
    .md-chip {
        margin-left: 0;
    }
    .novos-medicam {
        margin-right: 0px;
    }
    .cadastrar-medicam {
        margin-left: 0px;
    }
    .div-cadast-medc {
        display: none;
    }
    .cadast-medc-mobile {
        display: block;
    }
}

@media (min-width: 601px) {
    .no-desktop {
        display: none;
    }
}

@media (max-width: 600px) {
    .header-card {
        margin-top: 20px;
        margin-bottom: 20px;
    }
    .cadastrar-medicam {
        font-family:  Cairo, sans-serif;
    }
    .medicamentos-div {
        font-family:  Cairo, sans-serif;
        text-align: left;
        justify-content: left;
        align-items: center;
        margin: 10px auto;
    }
    .btn-subir-medicam {
        margin-left: 40%;
        margin-bottom: -15%;
    }
    .botoes-card-medic {
        margin-left: 40%;
        margin-bottom: 5%;
    }
}

@media (max-width: 500px) {
    .cadastrar-medicam {
        margin-top: 8px;
    }
    .novos-medicam {
        margin-right: 15px; 
    }
    .buscar-icone {
        margin-left: -18%;
    }
}

@media (max-width: 437px) {
    .cadastrar-medicam {
        margin-left: -5px;
        font-size: 18px;
    }
    .div-cadast-medc {
        margin: 0 auto;
        text-align: center;
        justify-content: center;
    }    
}

@media (max-width: 400px) {
    .div-item-lista {
        margin: 0 auto;
        text-align: center;
        justify-content: center;
        margin-bottom: 10px;
    }
    .button_custom {
        margin: 0 auto;
        text-align: center;
    }
    #UsuarioForm {
        margin-left: 8px;
        padding: 0;
    }
    .card-medicamentos {
        padding: 5px;
        margin-left: 1px;
    }
    .novos-medicam {
        margin-left: -15px;
    }
}


.date_I {
    margin-top: 10px !important;
    text-align: left !important;
}

@media (max-width: 380px) {
    .novos-medicam {
        margin: 0 auto;
        justify-content: center;
        text-align: center;
        padding: 0;
        margin-left: -17px;
    }
    .button_custom {
        top: -20px;
    }
    .info-medic-card {
        top: 20px;
    }
    .buscar-icone {
        margin-left: -16%;
    }
    .div-buscar-legenda {
        padding: 0;
        margin: 0 auto;
        justify-content: center;
        text-align: center;
        align-items: center;
    }
    .linha-buscar-medc {
        max-width: 100%;
        margin: 0 auto;
        padding: 0;
        justify-content: center;
        margin-left: 0;
        margin-right: 0;
        right: 0;
        left: 0;
        padding-left: 0;
        padding-right: 0;
    }
    .tabela {
        right: -2%;
    }
}

@media (max-width: 340px) {
    .buscar-icone {
        margin-left: -20%;
    }
}

@media (max-width: 320px) {
    .margem-c {
        margin-left: -10px;
    }
    .col-table table {
        border: none !important;
    }
    tr:hover {
        background: unset;
    }
    .col-table thead {
        font-size: 11px;
        background: #fff;
        color: #666;
    }
    .col-table i {
        font-size: 18px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .col-table mat-icon {
        font-size: 20px !important;
        height: 20px;
        width: 20px;
        // color: #1265b9;
    }
    .value-color {
        // color: #1265b9 !important;
        font-size: 13px;
        padding: 0 !important;
        padding-left: 5px !important;
        padding-right: 5px !important;
        text-align: center !important;
    }
    .medicamentos-div {
        margin: 0 auto;
        text-align: center;
        justify-content: center;
    }
    .buscar-icone {
        margin-left: -22%;
    }
}

// @media (min-width: 601px) {
//     .coluna {
//         margin-left: -14px;
//     }
// }
@media (max-width: 1210px) and (min-width: 1169px) {
    .custom-font {
        font-size: 11px;
    }
}

@media (max-width: 1090px) and (min-width: 1052px) {
    .custom-font {
        font-size: 11px;
    }
}

@media (max-width: 780px) and (min-width: 731px) {
    .custom-font {
        font-size: 11px;
    }
    .custom-font2 {
        font-size: 10px;
    }
}

@media(max-width:480px) {
    .text-align {
        text-align: center;
    }
}

@media(max-width:480px) {
    .text-align {
        text-align: center;
    }
}
mat-form-field{
    height: 60px !important;
}