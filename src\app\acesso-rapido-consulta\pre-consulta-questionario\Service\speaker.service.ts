// speaker.service.ts
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class SpeakerService {
  private isCurrentlySpeaking = false;
  private currentAudio: HTMLAudioElement | null = null;
  private speechConfig = {
    rate: 1.5,
    pitch: 1.1,
    volume: 0.9
  };

  // ElevenLabs voice ID for Portuguese (Brazil) - using <PERSON> (female voice)
  private readonly voiceId = 'Xb7hH8MSUJpSbSDYk0k2'; // Alice - voz feminina natural
  private readonly apiUrl = 'https://api.elevenlabs.io/v1/text-to-speech';

  constructor() {
    console.log('SpeakerService inicializado com ElevenLabs API - Voz feminina (Alice)');
  }

  async speak(text: string): Promise<void> {
    try {
      // Cancel any current speech
      this.cancel();

      console.log('Iniciando síntese com ElevenLabs:', text);
      this.isCurrentlySpeaking = true;

      // Process text to make it more natural
      const processedText = this.humanizeText(text);

      // Generate audio using ElevenLabs REST API
      const response = await fetch(`${this.apiUrl}/${this.voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': environment.elevenlabs
        },
        body: JSON.stringify({
          text: processedText,
          model_id: 'eleven_multilingual_v2',
          voice_settings: {
            stability: 0.6,        
            similarity_boost: 0.9, 
            style: 0.3,            
            use_speaker_boost: true
          }
        })
      });

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      // Convert response to blob
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      this.currentAudio = new Audio(audioUrl);
      this.currentAudio.volume = this.speechConfig.volume;

      return new Promise<void>((resolve, reject) => {
        if (!this.currentAudio) {
          reject(new Error('Audio não foi criado'));
          return;
        }

        this.currentAudio.onended = () => {
          this.isCurrentlySpeaking = false;
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          console.log('Síntese de voz finalizada');
          resolve();
        };

        this.currentAudio.onerror = (error) => {
          this.isCurrentlySpeaking = false;
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          console.error('Erro na reprodução do áudio:', error);
          reject(error);
        };

        this.currentAudio.play().catch(error => {
          this.isCurrentlySpeaking = false;
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          console.error('Erro ao reproduzir áudio:', error);
          reject(error);
        });
      });

    } catch (error) {
      this.isCurrentlySpeaking = false;
      this.currentAudio = null;
      console.error('Erro na síntese de voz ElevenLabs:', error);
      throw error;
    }
  }

  isSpeaking(): boolean {
    return this.isCurrentlySpeaking;
  }

  waitUntilFinished(): Promise<void> {
    return new Promise(resolve => {
      if (!this.isSpeaking()) return resolve();

      const interval = setInterval(() => {
        if (!this.isSpeaking()) {
          clearInterval(interval);
          resolve();
        }
      }, 100);
    });
  }

  cancel(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }
    this.isCurrentlySpeaking = false;
    console.log('Síntese de voz cancelada');
  }

  setSpeed(level: 'slow' | 'normal' | 'fast' | 'very-fast'): void {
    const rates: Record<string, number> = {
      slow: 1.0,
      normal: 1.3,
      fast: 1.6,
      'very-fast': 2.0
    };
    this.speechConfig.rate = rates[level];
    console.log(`Velocidade definida: ${level} (Nota: ElevenLabs controla velocidade via configurações de voz)`);
  }

  setPitch(level: 'low' | 'normal' | 'high'): void {
    const pitches: Record<string, number> = {
      low: 0.8,
      normal: 1.0,
      high: 1.2
    };
    this.speechConfig.pitch = pitches[level];
    console.log(`Tom definido: ${level} (Nota: ElevenLabs controla tom via configurações de voz)`);
  }

  private humanizeText(text: string): string {
    return text
      .replace(/[.,:;!?]/g, '$& ') // espaço após pontuações
      .replace(/\n/g, '. ') // quebra de linha vira ponto
      .replace(/\s+/g, ' ') // remove espaços múltiplos
      .replace(/(\d+)/g, ' $1 ') // espaço em volta de números
      .replace(/\b(importante|atenção|cuidado|urgente)\b/gi, '$1.')
      .trim();
  }

  testSpeech(text: string = 'Olá! Sou a assistente virtual da FASO. Como posso ajudá-la hoje?'): Promise<void> {
    return this.speak(text);
  }
}
