// speaker.service.ts
import { Injectable } from '@angular/core';
import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';
import { environment } from 'src/environments/environment';

@Injectable({ providedIn: 'root' })
export class SpeakerService {
  private elevenlabs!: ElevenLabsClient;
  private isCurrentlySpeaking = false;
  private currentAudio: HTMLAudioElement | null = null;
  private speechConfig = {
    rate: 1.5,
    pitch: 1.1,
    volume: 0.9
  };

  // ElevenLabs voice ID for Portuguese (Brazil) - using a high-quality multilingual voice
  private readonly voiceId = 'JBFqnCBsd6RMkjVDRZzb'; // Default voice ID from your reference

  constructor() {
    console.log('SpeakerService inicializado com ElevenLabs');
    this.initializeElevenLabs();
  }

  private initializeElevenLabs(): void {
    try {
      this.elevenlabs = new ElevenLabsClient({
        apiKey: environment.elevenlabs
      });
      console.log('ElevenLabs client inicializado com sucesso');
    } catch (error) {
      console.error('Erro ao inicializar ElevenLabs:', error);
      throw new Error('Falha ao inicializar o serviço de síntese de voz');
    }
  }

  async speak(text: string): Promise<void> {
    try {
      // Cancel any current speech
      this.cancel();

      console.log('Iniciando síntese com ElevenLabs:', text);
      this.isCurrentlySpeaking = true;

      // Process text to make it more natural
      const processedText = this.humanizeText(text);

      // Generate audio using ElevenLabs
      const audio = await this.elevenlabs.textToSpeech.convert(this.voiceId, {
        text: processedText,
        modelId: 'eleven_multilingual_v2',
        outputFormat: 'mp3_44100_128',
        voiceSettings: {
          stability: 0.5,
          similarityBoost: 0.8,
          style: 0.2,
          useSpeakerBoost: true
        }
      });

      // Convert ReadableStream to ArrayBuffer then to Blob
      const reader = audio.getReader();
      const chunks: Uint8Array[] = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      const audioBuffer = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
      let offset = 0;
      for (const chunk of chunks) {
        audioBuffer.set(chunk, offset);
        offset += chunk.length;
      }

      const audioBlob = new Blob([audioBuffer], { type: 'audio/mpeg' });
      const audioUrl = URL.createObjectURL(audioBlob);

      this.currentAudio = new Audio(audioUrl);
      this.currentAudio.volume = this.speechConfig.volume;

      return new Promise<void>((resolve, reject) => {
        if (!this.currentAudio) {
          reject(new Error('Audio não foi criado'));
          return;
        }

        this.currentAudio.onended = () => {
          this.isCurrentlySpeaking = false;
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          console.log('Síntese de voz finalizada');
          resolve();
        };

        this.currentAudio.onerror = (error) => {
          this.isCurrentlySpeaking = false;
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          console.error('Erro na reprodução do áudio:', error);
          reject(error);
        };

        this.currentAudio.play().catch(error => {
          this.isCurrentlySpeaking = false;
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          console.error('Erro ao reproduzir áudio:', error);
          reject(error);
        });
      });

    } catch (error) {
      this.isCurrentlySpeaking = false;
      this.currentAudio = null;
      console.error('Erro na síntese de voz ElevenLabs:', error);
      throw error;
    }
  }

  isSpeaking(): boolean {
    return this.isCurrentlySpeaking;
  }

  waitUntilFinished(): Promise<void> {
    return new Promise(resolve => {
      if (!this.isSpeaking()) return resolve();

      const interval = setInterval(() => {
        if (!this.isSpeaking()) {
          clearInterval(interval);
          resolve();
        }
      }, 100);
    });
  }

  cancel(): void {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }
    this.isCurrentlySpeaking = false;
    console.log('Síntese de voz cancelada');
  }

  setSpeed(level: 'slow' | 'normal' | 'fast' | 'very-fast'): void {
    const rates: Record<string, number> = {
      slow: 1.0,
      normal: 1.3,
      fast: 1.6,
      'very-fast': 2.0
    };
    this.speechConfig.rate = rates[level];
    console.log(`Velocidade definida: ${level} (Nota: ElevenLabs controla velocidade via configurações de voz)`);
  }

  setPitch(level: 'low' | 'normal' | 'high'): void {
    const pitches: Record<string, number> = {
      low: 0.8,
      normal: 1.0,
      high: 1.2
    };
    this.speechConfig.pitch = pitches[level];
    console.log(`Tom definido: ${level} (Nota: ElevenLabs controla tom via configurações de voz)`);
  }

  private humanizeText(text: string): string {
    return text
      .replace(/[.,:;!?]/g, '$& ') // espaço após pontuações
      .replace(/\n/g, '. ') // quebra de linha vira ponto
      .replace(/\s+/g, ' ') // remove espaços múltiplos
      .replace(/(\d+)/g, ' $1 ') // espaço em volta de números
      .replace(/\b(importante|atenção|cuidado|urgente)\b/gi, '$1.')
      .trim();
  }

  testSpeech(text: string = 'Olá! Sou a assistente virtual da FASO. Como posso ajudá-lo hoje?'): Promise<void> {
    return this.speak(text);
  }
}
