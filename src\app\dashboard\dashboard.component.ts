import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AppComponent } from '../app.component';
import { ConsultaService } from '../service/consulta.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { EnumTipoUsuario } from '../Util/tipoUsuario';
import { DocumentosService } from '../service/documentos.service';
import { LifeLineService } from '../lifeLine/lifeline.service';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ChartConfiguration, ChartData, ChartEvent, ChartType, } from 'chart.js';
import { BaseChartDirective } from 'ng2-charts';
import DataLabelsPlugin from 'chartjs-plugin-datalabels';
import { AnaliseService } from '../service/analise.service';

import { AlertComponent } from '../alert/alert.component';
import { DomSanitizer } from '@angular/platform-browser';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TranslatePipe } from '@ngx-translate/core';
import { NgChartsModule } from 'ng2-charts';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { Consulta, ModalConsultasPacienteComponent, ModalData } from './modal-consultas-paciente/modal-consultas-paciente.component';



export interface PeriodicElement {
  name: string;
  position: string;
  weight: string;
  symbol: string;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    NgxSmartModalModule,
    MatIcon,
    MatProgressBarModule,
    TranslatePipe,
    NgChartsModule,
    MatDialogModule
  ]
})



export class DashboardComponent implements OnInit {

  @ViewChild(BaseChartDirective) chart: BaseChartDirective | undefined;

  public barChartOptions: ChartConfiguration['options'] = {
    responsive: true,
    // We use these empty structures as placeholders for dynamic theming.
    scales: {
      x: {},
      y: {
        min: 10
      }
    },
    plugins: {
      legend: {
        display: true,
      },
      datalabels: {
        anchor: 'end',
        align: 'end',
      }
    }
  };
  public barChartType: ChartType = 'bar';
  public barChartPlugins = [
    DataLabelsPlugin
  ];

  public barChartData: ChartData<'bar'> = {
    labels: ['2006', '2007', '2008', '2009', '2010', '2011', '2012'],
    datasets: [
      { data: [65, 59, 80, 81, 56, 55, 40], label: 'Series A', backgroundColor: ['rgb(82, 96, 255)'], },
      { data: [28, 48, 40, 19, 86, 27, 90], label: 'Series B', backgroundColor: ['rgb(76, 217, 255)'], }
    ]
  };


  public pieChartTypeteste: ChartType = 'pie';

  // Opções do gráfico
  public pieChartOptionsteste: ChartConfiguration['options'] = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'right',
        labels: {
          color: '#4a4a4a',
          font: {
            family: "'Roboto', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",
            size: 12,
            weight: 400
          },
          padding: 25,
          boxWidth: 12,
          boxHeight: 12,
          usePointStyle: true,
          pointStyle: 'rectRounded',
          textAlign: 'center'
        },
        align: 'center'
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        titleColor: '#4a4a4a',
        bodyColor: '#4a4a4a',
        bodyFont: {
          family: "'Roboto', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",
          size: 14
        },
        borderColor: '#e0e0e0',
        borderWidth: 1,
        padding: 12,
        boxPadding: 6,
        callbacks: {
          label: function (context) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.chart.data.datasets[0].data.reduce((a, b) => (a as number) + (b as number), 0) as number;
            const percentage = Math.round(value as number / total * 100);
            return `${label}: ${percentage}`;
          }
        }
      }
    },
    elements: {
      arc: {
        borderWidth: 1,
        borderColor: '#ffffff'
      }
    },
    layout: {
      padding: {
        left: 40
      }
    }
  };

  // Dados do gráfico
  public pieChartDatateste: ChartData<'pie', number[], string | string[]> = {
    labels: ['Exames realizados', 'Exames agendados', 'Exames cancelados'],
    datasets: [{
      data: [25, 30, 20,],
      backgroundColor: [
        'rgb(67, 177, 113)',
        'rgb(131, 219, 178)',
        'rgb(180, 230, 195)'
      ],
      hoverBackgroundColor: [
        'rgb(30, 135, 74)',
        'rgb(72, 167, 136)',
        'rgb(114, 173, 132)'
      ]
    }]
  };


  // events
  public chartClicked({ }: { event?: ChartEvent, active?: {}[] }): void {
    // ;
  }

  public chartHovered({ }: { event?: ChartEvent, active?: {}[] }): void {
    // ;
  }

  public randomize(): void {
    // Only Change 3 values
    this.barChartData.datasets[0].data = [
      Math.round(Math.random() * 100),
      59,
      80,
      Math.round(Math.random() * 100),
      56,
      Math.round(Math.random() * 100),
      40];

    this.chart?.update();
  }

  constructor(
    private spinner: SpinnerService,
    private appc: AppComponent,
    private router: Router,
    private consultaService: ConsultaService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private dialog: MatDialog,
    public documentosService: DocumentosService,
    private lifeLineService: LifeLineService,
    public usuarioLogadoService: UsuarioLogadoService,
    private analiseService: AnaliseService,
    private snackBarAlert: AlertComponent,
    public sanitizer: DomSanitizer,
  ) {

    this.screenWidthstream = window.innerWidth;
    if (this.screenWidthstream > 1150)
      this.view = [700, 300]
    else if (this.screenWidthstream < 1150 && this.screenWidthstream > 500)
      this.view = [500, 400]
    else
      this.view = [300, 700]
    // Object.assign(this, { this.single })
  }


  screenWidthstream: number;
  totalConsultas?: number;
  single?: any[];
  singleMeses?: any[];
  dados = [];
  consultas: number = 0;
  // usuario: Usuario;
  alerta: boolean = false;
  DadosConsultaLifeLine: any = []
  dadosLife = false;
  dadosAnonimo?: string;
  MedicoAnonimo?: string;
  Dataanonimo?: string;
  dadosLifeLine: any = []
  nomePaciente: string = "";
  tipoUsuario?: string
  AnexosQnt: number = 0;
  QntLifeLine: number = 0

  graficoValoresVazio: boolean = true;
  graficoConsultaMesesVazio: boolean = true;
  nomeMesAnterior: any;

  AdmPermissao = false;
  MedicoPermissao = false;
  AtendentePermissao = false;
  PacientePermissao = false;
  showPanel = false;
  showLife = true;
  video: boolean = false;
  dadosAvaliacao: any = [];
  dadosSilver: number = 0;
  dadosGold: number = 0;
  dadosBronze: number = 0;

  // message: string = 'Sua Life-Line esta vazia!.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔'
  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 60000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  // sendemail: string = 'Perfil salvo com Sucesso.  ✔'
  flgGraficoZero: boolean = false;
  listaAnalise: any = [];

  // dadosPagamentoGrafico = [
  //   ['Mês', 'R$'],
  //   ['1', 1250.23],
  //   ['7', 850.54],
  //   ['15', 780.76],
  //   ['22', 1435.88],
  //   ['30', 200.89],
  //   ['*2', 1220.00],
  //   ['*3', 3001.54],
  //   ['*4', 590.76],
  //   ['*5', 2320.88],
  //   ['*6', 605.89],
  // ]

  dadosPagamentoGrafico: any = [['Mês', 'R$']];

  examesRealizados: number = 0;
  examesAgendados: number = 0;
  examesCancelados: number = 0;
  examesTotais: number = 0;

  // public rawChartData: google.visualization.ChartSpecs = {
  //   chartType: 'AreaChart',
  //   dataTable: this.dadosPagamentoGrafico
  // };

  // view: any[];

  view: any[]
  // = [700, 300];
  // view: mobile[] = [400, 200];
  // options
  showXAxis = true;
  showYAxis = true;
  gradient = false;
  showLegend = true;
  showXAxisLabel = true;
  xAxisLabel = 'Country';
  showYAxisLabel = true;
  yAxisLabel = 'Population';

  colorScheme = {
    domain: ['#5AA454', '#C7B42C', '#A10A28', '#7aa3e5']
  };

  // line, area
  autoScale = true;

  public rawFormatter: any;


  dadosConsultasUltimosMeses: any = [];

  // public chart = {
  //   title: 'Consultas 2019',
  //   type: 'BarChart',
  //   data: this.dadosConsultasUltimosMeses,
  //   columnNames: ['Element', 'Consulta'],
  //   options: {
  //     animation: {
  //       duration: 250,
  //       easing: 'ease-in-out',
  //       startup: true
  //     }
  //   }
  // };

  async ngOnInit() {
    this.GetAvaliacoesGrafico();
    // this.GetValoresGrafico();
    // await this.GetConsultasUltimosMesesGrafico();
    this.CarregaConsultas();
    // this.Carregagraficos();
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM) {
      this.tipoUsuario = 'ADM Sistema';
      this.AdmPermissao = true;
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      // this.Carregagraficos();
      this.dadosGraficoPizza();
    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente) {
      this.tipoUsuario = 'Atendente';
      this.AtendentePermissao = true
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
      this.dadosGraficoPizza();
      // this.Carregagraficos();
    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico) {
      this.tipoUsuario = 'Médico';
      this.MedicoPermissao = true
      this.dadosGraficoPizza();
      // this.Carregagraficos();
    }
    else if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente) {
      this.tipoUsuario = 'Paciente';
      this.PacientePermissao = true;
    }

  }

  Agenda() {
    sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(['/calendario']);
    this.appc.ngOnInit();
  }

  Pacient() {
    this.router.navigate(['/pesquisapacientes']);
    this.appc.ngOnInit();
  }

  Perfil() {
    this.router.navigate(['/perfil']);
    this.appc.ngOnInit();
  }
  Grafic() {
    this.router.navigate(['/indicadores']);
    this.appc.ngOnInit();
  }

  consultasConcluida?: number;
  consultasAgendanda?: number;
  consultasCancelada?: number;
  dadosGraficoPizza() {
    this.consultaService.GetDadosPizza(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      if (retorno) {
        this.single = [];
        retorno.forEach((element: any) => {
          if (element.Status == 'Concluido')
            this.consultasConcluida = element.qtd
          if (element.Status == "Agendada")
            this.consultasAgendanda = element.qtd
          if (element.Status == "Cancelada")
            this.consultasCancelada = element.qtd


        });

        var dados = []
        dados = this.single.filter(c => c.value > 0)
        if (dados.length == 0)
          this.flgGraficoZero = true;

        var soma = 0;
        var teste: any = [];
        for (var i = 0; i < this.single.length; i++) {

          teste[i] = parseInt(this.single[i].value);
          soma += parseInt(teste[i]);
        }



      }


    })
  }

  lifeline() {

    var usuarioLifeline = {
      "tipoUsuario": this.usuarioLogadoService.getIdTipoUsuario(),
      "idUsuario": this.usuarioLogadoService.getIdUsuarioAcesso(),
    }
    this.lifeLineService.setModalLifeline(usuarioLifeline)

  }

  // LifeLineVazia(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];
  //     // this.snackBar.open(this.message, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  // MensagemSnack(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];
  //     // this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }

  DadosPaciente(id: any) {
    this.dadosLife = false
    this.DadosConsultaLifeLine = []
    this.consultaService.GetDadosLifeLine(id).subscribe((retorno) => {
      this.DadosConsultaLifeLine = retorno.convercas
      if (this.DadosConsultaLifeLine.anonimo != null) {
        this.dadosAnonimo = this.DadosConsultaLifeLine.anonimo
        this.MedicoAnonimo = this.DadosConsultaLifeLine.medico
        this.Dataanonimo = this.DadosConsultaLifeLine.dtaConsulta

      }
      this.dadosLife = true

    })
  }

  abreDate() {
    this.showPanel = !this.showPanel
    this.showLife = false;
  }

  abreLife() {
    this.showLife = !this.showLife
    this.showPanel = false;
  }
  Irconsulta() {
    // localStorage.setItem("dash", 'Agendada');
    this.router.navigate(['/consulta']);
  }


  CarregaConsultas() {
    try {
      this.consultaService.GetDashBoard(this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {

        if (this.tipoUsuario == 'Paciente' || this.tipoUsuario == 'Médico') {
          this.dados = retorno.consultas

          this.QntLifeLine = retorno.lifeLine
          this.AnexosQnt = retorno.anexo
          this.consultas = retorno.consultasTotal;
        }

        else
          this.consultas = retorno.totalconsultas

        var minutos = 20;

        if (this.consultas > 0) {
          this.dados.forEach((element: any) => {
            var teste = new Date(element.dtaConsulta)
            teste.setMinutes(teste.getMinutes() - minutos);
            var data = new Date()
            data.setMinutes(data.getMinutes() - minutos);
            if (teste <= new Date() && teste >= data) {
              this.alerta = true
              return;
            }
            else
              this.alerta = false

          });
        }

      })

    } catch (error) {

    }
  }

  download(arq: any, nome: any, contentType: any) {
    if (!contentType) {
      contentType = 'application/octet-stream';
    }
    var a = document.createElement('a');
    var blob = new Blob([arq], { 'type': contentType });
    a.href = window.URL.createObjectURL(blob);
    a.download = nome;
    a.click();
  }

  BaixarArquivo(chave: any, nome: any) {
    this.consultaService.CarregaCaminhoArquivo(String(chave), nome)
      .subscribe(
        (response) => {
          var application;
          this.download(response, nome, application);
          this.spinner.hide();
        }, () => {
          this.spinner.hide();
        });
  }

  GetAvaliacoesGrafico() {
    this.consultaService.GetAvaliacoesGrafico(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.dadosAvaliacao = retorno

      this.dadosSilver = this.dadosAvaliacao.filter((c: any) => c.avaliacao == 'Silver').length;
      this.dadosGold = this.dadosAvaliacao.filter((c: any) => c.avaliacao == 'Gold').length;
      this.dadosBronze = this.dadosAvaliacao.filter((c: any) => c.avaliacao == 'Bronze').length;

      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }

  // GetValoresGrafico() {
  //   this.consultaService.GetValoresGrafico(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno: any) => {
  //     

  //     if (retorno.length > 0) {
  //       this.graficoValoresVazio = false

  //       switch (retorno[0].Mês) {
  //         case EnumMeses.Janeiro:
  //           this.nomeMesAnterior = "Janeiro"
  //           break;
  //         case EnumMeses.Fevereiro:
  //           this.nomeMesAnterior = "Fevereiro"
  //           break;
  //         case EnumMeses.Março:
  //           this.nomeMesAnterior = "Março"
  //           break;
  //         case EnumMeses.Abril:
  //           this.nomeMesAnterior = "Abril"
  //           break;
  //         case EnumMeses.Maio:
  //           this.nomeMesAnterior = "Maio"
  //           break;
  //         case EnumMeses.Junho:
  //           this.nomeMesAnterior = "Junho"
  //           break;
  //         case EnumMeses.Julho:
  //           this.nomeMesAnterior = "Julho"
  //           break;
  //         case EnumMeses.Agosto:
  //           this.nomeMesAnterior = "Agosto"
  //           break;
  //         case EnumMeses.Setembro:
  //           this.nomeMesAnterior = "Setembro"
  //           break;
  //         case EnumMeses.Outubro:
  //           this.nomeMesAnterior = "Outubro"
  //           break;
  //         case EnumMeses.Novembro:
  //           this.nomeMesAnterior = "Novembro"
  //           break;
  //         case EnumMeses.Dezembro:
  //           this.nomeMesAnterior = "Dezembro"
  //           break;
  //         default:
  //           this.nomeMesAnterior = "Invalido"
  //           break;
  //       }
  //       var totalDias;

  //       if (retorno[0].Mês == 1 || retorno[0].Mês == 3 || retorno[0].Mês == 5 || retorno[0].Mês == 7 || retorno[0].Mês == 8 || retorno[0].Mês == 10 || retorno[0].Mês == 12) {
  //         totalDias = 31
  //       }
  //       else if (retorno[0].Mês == 2) {
  //         var data = new Date;
  //         if ((data.getFullYear() % 4) == 0)
  //           totalDias = 29
  //         else
  //           totalDias = 28
  //       } else if (retorno[0].Mês == 4 || retorno[0].Mês == 6 || retorno[0].Mês == 9 || retorno[0].Mês == 11) {
  //         totalDias = 30
  //       } else {
  //         
  //       }

  //       for (let index = 0; index < totalDias; index++) {
  //         this.dadosPagamentoGrafico.push([(1 + index).toString(), 0])
  //       }

  //       retorno.forEach(element => {
  //         this.dadosPagamentoGrafico.forEach(elemento => {
  //           if (element.Dia == elemento[0]) {
  //             elemento[1] = element.ValorConsulta
  //           }
  //         });
  //       });
  //     }
  //     else
  //       this.graficoValoresVazio = true

  //     

  //   });
  // }


  Relatorios() {
    this.router.navigate(['/relatorios']);
    this.appc.ngOnInit();
  }

  GetConsultasUltimosMesesGrafico() {
    this.consultaService.GetConsultasUltimosMesesGrafico(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno: any) => {


      if (retorno.length > 0) {
        this.graficoConsultaMesesVazio = false

        retorno.forEach((element: any) => {
          var nomeMes = '';
          var quant = null;
          quant = element.Qtd;
          switch (element.Mês) {
            case 1:
              nomeMes = "Janeiro"
              break;
            case 2:
              nomeMes = "Fevereiro"
              break;
            case 3:
              nomeMes = "Março"
              break;
            case 4:
              nomeMes = "Abril"
              break;
            case 5:
              nomeMes = "Maio"
              break;
            case 6:
              nomeMes = "Junho"
              break;
            case 7:
              nomeMes = "Julho"
              break;
            case 8:
              nomeMes = "Agosto"
              break;
            case 9:
              nomeMes = "Setembro"
              break;
            case 10:
              nomeMes = "Outubro"
              break;
            case 11:
              nomeMes = "Novembro"
              break;
            case 12:
              nomeMes = "Dezembro"
              break;
            default:
              nomeMes = "Invalido"
              break;
          }
          this.dadosConsultasUltimosMeses.push([nomeMes.valueOf(), quant.valueOf()])
        });
      }
      else
        this.graficoConsultaMesesVazio = true


      this.spinner.hide();


    });
  }

  Carregagraficos() {
    try {
      this.documentosService.Carregagraficos(this.usuarioLogadoService.getIdUltimaClinica()).subscribe(retorno => {


        var meses = retorno;

        this.singleMeses = []
        meses.forEach((element: any) => {
          if (element.mes == '1') {
            this.singleMeses!.push({
              "name": "Janeiro",
              "value": element.total
            })
          }
          else if (element.mes == '2') {
            this.singleMeses!.push({
              "name": "Fevereiro",
              "value": element.total
            })
          }
          else if (element.mes == '3') {
            this.singleMeses!.push({
              "name": "Março",
              "value": element.total
            })
          }
          else if (element.mes == '4') {
            this.singleMeses!.push({
              "name": "Abril",
              "value": element.total
            })
          }
          else if (element.mes == '5') {
            this.singleMeses!.push({
              "name": "Maio",
              "value": element.total
            })
          }
          else if (element.mes == '6') {
            this.singleMeses!.push({
              "name": "Junho",
              "value": element.total
            })
          }
          else if (element.mes == '7') {
            this.singleMeses!.push({
              "name": "Julho",
              "value": element.total
            })
          }
          else if (element.mes == '8') {
            this.singleMeses!.push({
              "name": "Agosto",
              "value": element.total
            })
          }
          else if (element.mes == '9') {
            this.singleMeses!.push({
              "name": "Setembro",
              "value": element.total
            })
          }
          else if (element.mes == '10') {
            this.singleMeses!.push({
              "name": "Outubro",
              "value": element.total
            })
          }
          else if (element.mes == '11') {
            this.singleMeses!.push({
              "name": "Novembro",
              "value": element.total
            })
          }
          else if (element.mes == '12') {
            this.singleMeses!.push({
              "name": "Dezembro",
              "value": element.total
            })
          }
        });



      })
    } catch (error) {

    }


  }

  acessarExames() {
    this.router.navigate(['/analisepaciente']);
  }

  carregarOrientacaoPessoa(idTipoOrientacao: any) {
    this.spinner.show();
    this.analiseService.GetOrientacaoPaciente(this.usuarioLogadoService.getIdPessoa()!, idTipoOrientacao).subscribe((retorno) => {
      this.listaAnalise = []



      this.listaAnalise = retorno;

      this.listaAnalise.forEach((element: any) => {
        element.orientacao = this.sanitizer.bypassSecurityTrustHtml(element.orientacao);
      });

      if (this.listaAnalise.length == 0)
        this.snackBarAlert.falhaSnackbar("Sem Orientações salvas.");
      else
        this.ngxSmartModalService.getModal('modalOrientacao').open();

      ;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  ConverteTextoSeguro(texto: any) {
    return this.sanitizer.bypassSecurityTrustHtml(texto);
  }

  CarregarListaConsultasAtivas() {
    const consultasExemplo: Consulta[] = [
      {
        id: 1001,
        nomeMedico: 'Dr. João Silva',
        dataConsulta: new Date('2024-06-15T14:30:00'),
        flgPresencial: true
      },
      {
        id: 1002,
        nomeMedico: 'Dra. Maria Santos',
        dataConsulta: new Date('2024-06-20T10:00:00'),
        flgPresencial: false,
        linkConsulta: 'https://meet.google.com/abc-defg-hij'
      },
      {
        id: 1003,
        nomeMedico: 'Dr. Pedro Oliveira',
        dataConsulta: new Date('2024-06-25T16:15:00'),
        flgPresencial: false,
        linkConsulta: 'https://zoom.us/j/123456789'
      }
    ];

    const modalData: ModalData = {
      consultas: consultasExemplo,
      nomePaciente: 'Ana Costa'
    };

    const dialogRef = this.dialog.open(ModalConsultasPacienteComponent, {
      data: modalData,
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log('Modal fechada');
      console.log(result);
      // Aqui você pode executar alguma ação após fechar a modal
    });
  }
}




