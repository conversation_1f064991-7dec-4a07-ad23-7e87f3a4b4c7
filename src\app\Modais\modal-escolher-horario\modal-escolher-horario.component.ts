import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-modal-escolher-horario',
    templateUrl: './modal-escolher-horario.component.html',
    styleUrls: ['./modal-escolher-horario.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon,
      MatFormFieldModule
    ]
})
export class ModalEscolherHorarioComponent implements OnInit {

  varData?: string;
  horarioValido = false;
  horarioSelecionado?: Date; // Para armazenar o horário como Date

  constructor(
    public dialogRef: MatDialogRef<ModalEscolherHorarioComponent>,
    private snackBar: AlertComponent,
    @Inject(MAT_DIALOG_DATA) public dtInicio: string
  ) { }

  ngOnInit(): void {
    ;
    this.horarioSelecionado = new Date(this.dtInicio);
    this.validarHorario();
  }

  fecharModal() {
    this.dialogRef.close();
  }

  cancelar() {
    this.fecharModal();
  }

  salvar() {
    if (this.horarioValido) {
      ;
      ;

      let dtAtual = new Date();

      if (dtAtual <= this.horarioSelecionado!)
        this.dialogRef.close(this.horarioSelecionado);

      else
        this.snackBar.falhaSnackbar("Não é possivel escolher um horário anterior ao horário atual.");
    }
  }

  validarHorario() {
    this.horarioValido = !!this.varData;
    if (this.horarioValido) {
      this.converterParaDate();
    }
  }

  converterParaDate() {
    const hoje = new Date(this.dtInicio); 
    const [horas, minutos] = this.varData!.split(':'); // Divide a string em horas e minutos
    this.horarioSelecionado = new Date(hoje.getFullYear(), hoje.getMonth(), hoje.getDate(), +horas, +minutos);

    ; // Para verificar o resultado
  }
}