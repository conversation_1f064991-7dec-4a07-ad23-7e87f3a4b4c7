@import url('https://fonts.googleapis.com/css2?family=Afacad+Flux:wght@100..1000&display=swap');

* {
    font-family: "Afacad Flux", sans-serif;
}

@media (min-width: 768px) {
    .col-md-8 {
        -ms-flex: 0 0 66.666667%;
        flex: 0 0 66.666667%;
        max-width: 62.666667%;
    }
}

::ng-deep {
    .ModalSeria {

        z-index: 9999 !important;
        margin-top: 50px;
    }
}

* {
    text-decoration: none;
    list-style: none;
    outline: none;
    border: none;
    box-shadow: none;
}
::ng-deep .ng-select {
    .ng-select-container {
      background-color: transparent;
      border: none;
      border-radius: 10px;
      padding: 6px;
      font-size: 14px;
      color: #2e7d32;
      transition: all 0.2s ease-in-out;
        box-shadow: none !important;
        height: 45px  !important;
      &:hover {
        border-color: #f0f7f4;
      }
    }
  
    .ng-arrow-wrapper {
      color: #4caf50;
    }
  
    .ng-dropdown-panel {
      border-radius: 8px;
      border: none;
      box-shadow: none;
      background-color: #ffffff;
    }
  
    .ng-option {
      padding: 10px;
  
      &.ng-option-marked {
        background-color: #e8f5e9;
      }
  
      &.ng-option-selected {
        background-color: #c8e6c9;
        color: #1b5e20;
      }
    }
    .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked{
        background: #f0f7f4 !important;
    }

  }
  ::ng-deep{
    .ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{
        top: 13px;
    }
  }
  