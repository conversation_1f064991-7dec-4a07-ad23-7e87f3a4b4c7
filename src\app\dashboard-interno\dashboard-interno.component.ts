import { UsuarioLogado } from './../auth/UsuarioLogado';
import { Component, OnInit } from '@angular/core';
import { DadosDashboardInt, DashboardInternoModelView } from '../model/dashboard-interno';
import { DashboardInternoService } from '../service/dashboard-interno.service';
import { firstValueFrom } from 'rxjs';
import { Router } from '@angular/router';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';


@Component({
    selector: 'app-dashboard-interno',
    templateUrl: './dashboard-interno.component.html',
    styleUrls: ['./dashboard-interno.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCardModule
    ]
})
export class DashboardInternoComponent implements OnInit {
  listaErros: DashboardInternoModelView[] = [];
  listaErrosFiltrada: DashboardInternoModelView[] = [];
  dadosDashboard: DadosDashboardInt = new DadosDashboardInt();

  constructor(
    private spinner: SpinnerService,
    private dashboardInternoService: DashboardInternoService,
    private usuarioLogado: UsuarioLogadoService,
    private router: Router
  ) { }

  ngOnInit() {
    this.usuarioLogado.getUsuarioLogado().subscribe((usuario: UsuarioLogado | null) => {
      ;

      if (!usuario || usuario.idPessoa !== 1)
        this.router.navigate(['']);
      this.getErrosDashboard();
      this.getDadosDashboardInterno();
    });
  }

  async getErrosDashboard() {
    const ret = await firstValueFrom(this.dashboardInternoService.BuscarErros());
    if (!ret.ok)
      
    this.listaErros = ret.erros;
    this.listaErrosFiltrada = this.listaErros.slice();
    
    this.spinner.hide();
  }

  async getDadosDashboardInterno() {
    try {
      const dados: DadosDashboardInt = await firstValueFrom(this.dashboardInternoService.GetDashboardInt());
      this.spinner.hide();
      if (dados) {
        this.dadosDashboard = dados; // Atribui os dados ao objeto dadosDashboard
        ;
      } else {
        ;
      }
    } catch (error) {
      console.error("Erro ao carregar os dados do dashboard", error);
      ;
      this.spinner.hide();
    }
  }
}
