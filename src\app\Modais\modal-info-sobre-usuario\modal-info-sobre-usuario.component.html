<div style="display: flex; justify-content: space-between;">
    <h2 mat-dialog-title style="font-weight: 700; color: #279EFF; margin-bottom: -2px;">Informações do
        {{DadosInformUsuario.tipoUsuario}}</h2>
    <button mat-icon-button (click)="dialogRef.close();"> <mat-icon>close</mat-icon> </button>
</div>

<div style="padding: 0 !important; overflow: auto;">



    <mat-form-field class="col-md-6 col-sm-12 input-spacing">
        <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled name="Nome"
            [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
    </mat-form-field>



    <mat-form-field class="col-md-6 col-sm-12 input-spacing">
        <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" disabled name="CPF" mask="000.000.000-00"
            [(ngModel)]="DadosInformUsuario.cpf" style="color: black;" maxlength="14">
    </mat-form-field>

    <mat-form-field class="col-md-6 col-sm-12 input-spacing">
        <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email" disabled name="Email"
            [(ngModel)]="DadosInformUsuario.email" style="color: black;">
    </mat-form-field>


    <mat-form-field class="col-md-6 col-sm-12 input-spacing">
        <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" disabled name="Celular"
            mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.celular" style="color: black;">
    </mat-form-field>

    <mat-form-field class="col-md-6 col-sm-12 input-spacing">
        <input matInput placeholder="{{ 'TELAAGENDA.TELEFONE' | translate }}" disabled name="Telefone"
            mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.tel" style="color: black;">
    </mat-form-field>

    <mat-form-field class="col-md-6 col-sm-12 input-spacing">
        <input matInput placeholder="{{ 'TELAAGENDA.TELEFONECOM' | translate }}" disabled name="TelComerciar"
            mask="(00) 00000-0000" style="
          color: black;" [(ngModel)]="DadosInformUsuario.telComer">
    </mat-form-field>



    <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">

        <button class="btn-primary " mat-raised-button style="color:white;" (click)="dialogRef.close()"
            style="margin-right: 2%;">
            <mat-icon>clear</mat-icon> {{ 'TELAAGENDA.SAIR' | translate }}
        </button>

    </div>
</div>