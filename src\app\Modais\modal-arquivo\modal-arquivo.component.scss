.modal-container {
    display: flex;
    flex-direction: column;
    max-height: 80vh;
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
}

.modal-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 1.25rem;

    mat-icon {
        margin-right: 10px;
        color: #3f51b5;
    }
}

.btn-close {
    background: none;
    border: none;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;

    &:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
}

.modal-body {
    padding: 24px;
    flex: 1;
    overflow-y: auto;
}

// Visualização de documento
.document-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    border-radius: 8px;
    background-color: #f8f9fa;

    mat-icon {
        font-size: 32px;
        height: 32px;
        width: 32px;
        color: #3f51b5;
        margin-right: 12px;
    }
}

.info-text {
    h3 {
        margin: 0;
        font-size: 1rem;
        word-break: break-word;
    }

    p {
        margin: 4px 0 0;
        color: #666;
        font-size: 0.875rem;
    }
}

.preview-container {
    width: 100%;
    height: 300px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background-color: #fafafa;
}

.img-preview {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.pdf-preview {
    width: 100%;
    height: 100%;
    border: none;
}

.generic-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 20px;

    .file-icon {
        font-size: 64px;
        height: 64px;
        width: 64px;
        color: #3f51b5;
        margin-bottom: 16px;
    }
}

// Área de upload
.upload-area {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: #fafafa;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &:hover {
        border-color: #3f51b5;
        background-color: #f0f2ff;
    }

    &.drag-over {
        border-color: #3f51b5;
        background-color: #eef2ff;
        transform: scale(1.01);
    }

    .upload-icon {
        font-size: 48px;
        height: 48px;
        width: 48px;
        color: #3f51b5;
        margin-bottom: 16px;
    }

    h3 {
        margin: 0 0 8px;
        color: #333;
        font-weight: 500;
        font-size: 1rem;
    }

    p {
        margin: 0;
        color: #666;
        font-size: 0.875rem;
    }
}

// Rodapé e botões
.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    border: 1px solid transparent;

    mat-icon {
        margin-right: 6px;
        font-size: 16px;
        height: 16px;
        width: 16px;
    }
}

.btn-primary {
    color: #fff;
    background-color: #3f51b5;

    &:hover {
        background-color: #364495;
    }

    &:disabled {
        opacity: 0.65;
        cursor: not-allowed;
    }
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;

    &:hover {
        background-color: #5a6268;
    }
}

.btn-outline-secondary {
    color: #6c757d;
    background-color: transparent;
    border-color: #6c757d;

    &:hover {
        color: #fff;
        background-color: #6c757d;
    }
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;

    &:hover {
        background-color: #c82333;
    }
}

// Mensagens de erro
.alert {
    padding: 0.75rem 1.25rem;
    margin-top: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;

    &.alert-danger {
        color: #721c24;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
}
