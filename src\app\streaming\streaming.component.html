<mat-sidenav-container class="Drawer-esquerdo " autosize id="Modo1" *ngIf="Modo1 ">
    <mat-sidenav #panel class="drawer-height" position="end" mode="side" style="overflow: hidden;height: 100%" opened>
        <mat-nav-list class="align-menu">
            <div class="panel_historico columnify row" *ngIf="historico" contents >
                <div class="col-md-5 col-sm-4"
                    style="border: 1px solid #ddd; height: 90vh!important; overflow-y: auto; padding:0!important">
                    <div class="text-center ">
                        <div class="text-center col-md-12">
                            <span class="text-left col-md-12" style="white-space: pre-wrap;">
                                Histórico</span>
                        </div>
                        <div tmDarkenOnHover *ngFor="let item of dadosHistorico;let i = index;" contentsTable
                            class="row text-center d-flex justify-content-center" style="cursor: pointer !important;">
                            <span tmDarkenOnHover (click)="historico = true"  contentsLink
                                position
                                style=" padding: 5px;  display: block; font-size: 12px; color: #1265b9 !important;">
                                {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                            </span>
                            <mat-icon aria-label="Anonimo"
                                style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;"
                                class="" *ngIf="item.anonimo != null">visibility_off
                            </mat-icon>
                            <mat-icon aria-label="Anexo"
                                style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;"
                                class="" *ngIf="item.anexo.length >0">archive
                            </mat-icon>
                            <mat-icon aria-label="Dados Corpo"
                                style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;"
                                class="" *ngIf="item.dadosCorpo!=null">accessibility_new
                            </mat-icon>

                            <hr class="sep-2" />
                        </div>
                    </div>
                </div>
                <div class="col-md-7 col-sm-8"
                    style="overflow-y: auto !important;height: -webkit-fill-available !important;overflow-x: hidden; height: 90vh!important;"
                    #teste>

                    <div class="col-md-12 col-sm-12 col-xs-12 "
                        *ngFor="let item of dadosHistorico;let i = index">
                        <div class=" mt-30" *ngIf="historico">

                            <div class="Historico_Colap" style="padding: 10px;" id="{{item.posicao}}" #destinationRef>
                                <div class="text-center col-md-12">
                                    <span>
                                        {{ 'TELASTREAMING.CONSULTADIA' | translate }}
                                        {{item.dtaConsulta  | date: 'dd/MM/yyyy HH:mm' }}
                                        {{ 'TELASTREAMING.MEDICO' | translate }} {{item.medico}}</span>
                                </div>

                                <div class="col-md-12" style="padding: 10px;">
                                    <span class="text-left" *ngIf="item.obsConsulta != null && item.obsConsulta != '' ">
                                        <hr class="pontilhado"
                                            *ngIf="item.obsConsulta != null && item.obsConsulta != ''">
                                        {{ 'TELASTREAMING.PRONTUARIO' | translate }} {{item.obsConsulta}}</span>


                                    <br *ngIf="item.receita != null">
                                    <!-- <br *ngIf="item.receita != null">
                                    <br *ngIf="item.receita != null"> -->
                                    <span *ngIf="item.receita != null">
                                        Receituário :<br> {{ item.receita}}</span>

                                </div>

                                <div style="background-color: white; margin-top: 10px;" *ngIf="item.dadosCorpo!=null">
                                    <mat-accordion>
                                        <mat-expansion-panel hideToggle (opened)="panelOpenState = true"
                                            (closed)="panelOpenState = false">
                                            <mat-expansion-panel-header style="height: 30px !important;">
                                                <mat-panel-title>
                                                    <mat-icon>accessibility_new
                                                    </mat-icon>
                                                    <span class="ml-2">Dados do Paciente</span>
                                                </mat-panel-title>
                                            </mat-expansion-panel-header>
                                            <div class="text-left row" style="border: solid 1px black; padding: 15px;">
                                                <span class="text-left col-md-12" style="white-space: pre-wrap;">
                                                    Peso: {{item.dadosCorpo.peso}}<br>
                                                    Altura: {{item.dadosCorpo.altura}}<br>
                                                    Imc: {{item.dadosCorpo.imc}}<br>
                                                    Pressao: {{item.dadosCorpo.pressao}}<br>
                                                    Batimento: {{item.dadosCorpo.batimento}}<br>
                                                    Temperatura: {{item.dadosCorpo.temperatura}}</span>
                                            </div>
                                        </mat-expansion-panel>


                                    </mat-accordion>
                                </div>

                                <div style="background-color: white; margin-top: 10px;"
                                    *ngIf="item.anonimo != '' && item.anonimo != null && historico">
                                    <mat-accordion>
                                        <mat-expansion-panel hideToggle (opened)="panelOpenState = true"
                                            (closed)="panelOpenState = false">
                                            <mat-expansion-panel-header style="height: 30px !important;">
                                                <mat-panel-title>
                                                    <img src="assets/build/img/incognito.svg" style="width:8%">
                                                    <span class="ml-2">{{ 'TELASTREAMING.ANONIMO' | translate }}</span>
                                                </mat-panel-title>
                                            </mat-expansion-panel-header>
                                            <div class="text-left" style="border: solid 1px black; padding: 15px;">
                                                <span class="text-left" style="white-space: pre-wrap;">
                                                    {{item.anonimo}}</span>

                                            </div>
                                        </mat-expansion-panel>

                                    </mat-accordion>
                                </div>

                                <div style="background-color: white; margin-top: 10px;"
                                    *ngIf="dadosHistorico[i].anexo.length > 0 && historico">
                                    <mat-accordion>
                                        <mat-expansion-panel hideToggle (opened)="panelOpenState = true "
                                            (closed)="panelOpenState = false">
                                            <mat-expansion-panel-header style="height: 30px !important;">
                                                <mat-panel-title>
                                                    <i class="fas fa-paperclip"></i>
                                                    <span class="ml-2">{{ 'TELASTREAMING.ANEXOS' | translate }}</span>
                                                </mat-panel-title>
                                            </mat-expansion-panel-header>
                                            <div class="manha">
                                                <div class="d-flex justify-content-start"
                                                    *ngFor="let anex of dadosHistorico[i].anexo ">
                                                    <div class="balao col-12 " id="colapsoAnexo+{{i}}"
                                                        *ngIf="!spinnerAnexoDownload && (tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& anex.flgVisualizacaoUsuario==true)">
                                                        <i (click)="BaixarArquivo(anex.chave,anex.anexo,anex.tipoArquivo)"
                                                            style="    vertical-align: -webkit-baseline-middle;cursor: pointer; font-size: 20px"
                                                            class="fa fa-download text-left icone"
                                                            title="Download arquivo"><span class="texto text-left"
                                                                title="{{anex.anexo}} "
                                                                style="font-size: 12px; vertical-align: middle">
                                                                {{anex.anexo  | truncate :15 }}</span></i>

                                                        <div class="col-12" style="padding:unset">
                                                            <small
                                                                class="msg_timeAnonimo ">{{anex.dtaCadastro |  date: 'dd/MM/yyyy HH:mm'}}</small>

                                                            <small class="msg_nome "
                                                                title="{{anex.usuario}}">{{anex.usuario }}</small>
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class=" h-auto d-flow-root text-center"
                                                    *ngIf="spinnerAnexoDownload == true">
                                                    <img src="assets/build/img/spinner.gif" style="width: 50%;">

                                                </div>
                                            </div>
                                        </mat-expansion-panel>
                                    </mat-accordion>
                                </div>
                            </div>
                            <!--
                        <span class="d-flex input-group-text Historico_Colap text-center"
                            id="{{ item.posicao }}" #destinationRef>
                            {{ 'TELASTREAMING.CONSULTADIA' | translate }}
                            {{item.dtaConsulta  | date: 'dd/MM/yyyy HH:mm' }}
                            {{ 'TELASTREAMING.MEDICO' | translate }} {{item.medico}}


                            <span class="text-left" *ngIf="item.obsConsulta != null && item.obsConsulta != '' ">
                                <hr class="pontilhado"
                                    *ngIf="item.obsConsulta != null && item.obsConsulta != '' ">
                                {{ 'TELASTREAMING.PRONTUARIO' | translate }} {{item.obsConsulta}}</span>

                        </span> -->
                        </div>
                    </div>

                </div>

            </div>

            <div class="panel_principal" *ngIf="showPanel">
                <div class="abas-chat">

                    <div class="panel-menu mt-10 p-b-5 col-md-12 col-sm-12 col-xs-12"
                        [className]="closemenu ? 'panel-menu mt-10 p-b-5 col-md-12 col-sm-12 col-xs-12': 'd-none' ">
                        <div class="row">
                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="ConsultMenu()">
                                    <mat-icon>assignment</mat-icon>
                                </button>
                                <br>
                                <small> {{ 'TELASTREAMING.CONSULTAS' | translate }}</small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="AnotMenu()">
                                    <mat-icon>visibility_off</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</small>
                            </div>

                            <div class="text-center panel-selector">
                                <button mat-mini-fab class="btn-primary" (click)="AnexoMenu();CarregarAnexos()">
                                    <mat-icon>archive</mat-icon>
                                </button>
                                <br>
                                <small> {{ 'TELASTREAMING.ANEXOS' | translate }} </small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="DadosMenu()">
                                    <mat-icon>account_circle</mat-icon>
                                </button>
                                <br>
                                <small> {{ 'TELASTREAMING.DADOSPESSOAIS' | translate }} </small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="InfoMenu()">
                                    <mat-icon>info</mat-icon>
                                </button>
                                <br>
                                <small> {{ 'TELASTREAMING.INFOPESSOAIS' | translate }} </small>
                            </div>

                            <!-- <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="LocalizacaoMenu()">
                                    <mat-icon>my_location</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.LOCALIZACAO' | translate }}</small>
                            </div> -->

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="AtestadoMenu()">
                                    <mat-icon>assignment</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.ATESTADO' | translate }}</small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="ReceituarioMenu()">
                                    <mat-icon>file_copy</mat-icon>

                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.RECEITUARIO' | translate }}</small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="DeclaracaoMenu()">
                                    <mat-icon>assignment_ind</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.DECALARACAO' | translate }}</small>
                            </div>

                        </div>

                    </div>

                    <div class="col-md-12 col-sm-12 mt-10"
                        style="margin-left: auto; margin-right: auto; text-align: center; margin-bottom: 10px;">
                        <button mat-mini-fab class="close-button" (click)="closem()"
                            [className]="closemenu ? 'close-button btn-primary mat-mini-fab' : 'close-button close-buttonrotate close-button mat-mini-fab'">
                            <mat-icon>keyboard_arrow_up</mat-icon>
                        </button>
                    </div>
                    <mat-tab-group>

                        <mat-tab label="{{ 'TELASTREAMING.LOCALIZACAO' | translate }}"
                            *ngIf="MedicoPermissao && !Consultas && !Anotacoes && !anexos && !DadosP && !InfoP && Localizacao && !Atestado && !Receituario && !Declaracao">

                            <div class="modal-h text-center">
                                <h2 style="font-size: 20px;">{{dadosConsulta.paciente}}</h2>
                                <mat-divider></mat-divider>

                            </div>

                            <div class="col-md-12 col-sm-12 col-xs-12 text-center p-b-20">

                                <label class="usuario-principal">{{ 'TELASTREAMING.PACIENTE' | translate }}</label>
                                <br>
                                <mat-icon class="contato">mail_outline</mat-icon> <small class="contato">
                                    JorgeIslintan43&#64;outlook.com</small>

                                <br>
                            </div>

                            <div class="history-loc">
                                <iframe
                                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3601.3662027702208!2d-49.22476598498456!3d-25.492830683759497!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x94dcfa9559a4d587%3A0x6e6c706e333c234c!2sRua+Rosa+Sahagoff%2C+185+-+Uberaba%2C+Curitiba+-+PR%2C+81580-140!5e0!3m2!1spt-BR!2sbr!4v1555534020282!5m2!1spt-BR!2sbr"
                                    width="400" height="300" frameborder="0" style="border:0" allowfullscreen></iframe>

                            </div>
                        </mat-tab>


                        <mat-tab label="{{ 'TELASTREAMING.CONSULTA' | translate }}"
                            *ngIf="MedicoPermissao && !Anotacoes  && Consultas && !anexos && !DadosP && !InfoP && !Localizacao && !Atestado && !Receituario && !Declaracao">
                            <mat-form-field class="col-md-12">
                                <textarea matInput class="background-consulta" name="txtMensagem" id="txtMensagem"
                                    cdkTextareaAutosize #autosize="cdkTextareaAutosize" maxlength="500"
                                    name="txtMensagem" (blur)='CampoConsulta()'
                                    [(ngModel)]="CampoConsultaPerguntas"></textarea>

                            </mat-form-field>
                            <div class="col-12 row-button text-center">
                                <button mat-flat-button class=" btn btn-primary big-btn">
                                    {{ 'TELASTREAMING.SALVAR' | translate }}
                                </button>

                            </div>
                        </mat-tab>


                        <mat-tab label=" {{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }} " class="color-anon"
                            *ngIf="MedicoPermissao && Anotacoes && !Consultas && !anexos && !DadosP && !InfoP && !Localizacao && !Atestado && !Receituario && !Declaracao">


                            <mat-form-field class="col-md-12">

                                <textarea matInput class="background-anom" placeholder="" cols="30" rows="5"
                                    cdkTextareaAutosize #autosize="cdkTextareaAutosize" name="anonimo"
                                    (blur)="CampoAnonimo()" [(ngModel)]="Anonimo" maxlength="500">
								 </textarea>

                            </mat-form-field>

                            <div class=" col-12 row-button text-center">
                                <button mat-flat-button class="btn btn-primary big-btn">
                                    {{ 'TELASTREAMING.SALVAR' | translate }}
                                </button>

                            </div>
                        </mat-tab>

                        <mat-tab label="{{ 'TELASTREAMING.ANEXOS' | translate }}" class="color-anon"
                            *ngIf="!Anotacoes && !Consultas && anexos && !DadosP && !InfoP && !Localizacao && !Atestado && !Receituario && !Declaracao">
                            <div class="carregar-arquivo h-auto d-flow-root text-center"
                                style="border: 2px dotted #999; margin: 38px;" *ngIf="spinnerAnexo == true">
                                <img src="assets/build/img/spinner.gif" style="width: 100%;">

                            </div>

                            <div class="carregar-arquivo h-auto d-flow-root text-center" *ngIf="spinnerAnexo == false"
                                (dragover)="$event.preventDefault()" (drop)="fileuploadesquerdo($event)" style="border: 2px dotted #999;
									margin: 38px;">
                                <i class="fas fa-cloud-upload-alt fa-fw fa-4x" style="color: #ccc;margin-top: 20px"></i>
                                <p style="font-size:12px; font-weight:500; line-height:14px">
                                    {{ 'TELASTREAMING.AREAPARAIMPORTARARQUIVOS' | translate }}
                                    <br>
                                    <br> {{ 'TELASTREAMING.OU' | translate }} </p>

                                <label for="file2" class="btn btn-primary"
                                    style="font-size:12px!important;margin-bottom: 20px;cursor: pointer;">
                                    {{ 'TELASTREAMING.CARREGARARQUIVO' | translate }} </label>
                                <input type="file" style="width:100%;" id="file2" #inputFile
                                    (change)="SubirArquivoConsulta(inputFile)" accept=".xls,.xlsx,.pdf" />
                            </div>

                            <div class="background-padr" style="overflow: auto;  height: 100%;">
                                <div class="d-flex justify-content-start" *ngFor="let item of DadosanexConsulta">
                                    <div class="col-md-12 col-sm-12 col-xs-12" *ngIf="item.idUsuario == usuario">
                                        <div class="balao2 col-10 mb-4">
                                            <i (click)="BaixarArquivo(item.chave,item.nomeArquivo,item.tipoArquivo)"
                                                style="cursor: pointer;font-size: 20px;
												vertical-align: bottom;" class="fa fa-download fa-fw  icone" title="Download arquivo"></i><span
                                                title="{{item.nomeArquivo }}" class="texto text-left" style="font-size: 12px;
												vertical-align: middle;">
                                                {{item.nomeArquivo | truncate : 25 : "…"}}</span>
                                            <!-- <span class="msg_time" style="
													bottom: 0px !important;
													margin-left: 5%;
                                                    color: white;">-{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}</span> -->
                                            <span class="msg_time"
                                                style=" bottom: 0px !important;  margin-left: 5%; color: white; width: 100%;">{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}
                                                <mat-icon style="vertical-align: sub; cursor: pointer;margin-left: 20%;"
                                                    (click)="DeleteAnexo(item.chave , item.idAnexo)">delete</mat-icon>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-12 col-sm-12 col-xs-12"
                                        *ngIf="item.idUsuario != usuario && (tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& item.flgVisualizacaoUsuario==true)">
                                        <div class="balao3 col-10 mb-4">
                                            <i (click)="BaixarArquivo(item.chave,item.nomeArquivo,item.tipoArquivo)"
                                                style="cursor: pointer;    font-size: 20px;
												vertical-align: bottom;" class="fa fa-download fa-fw icone" title="Download arquivo"></i><span
                                                title="{{item.nomeArquivo }}" class="texto text-left" style="font-size: 12px;
												vertical-align: middle;">
                                                {{item.nomeArquivo | truncate : 25 : "…"}}</span>
                                            <span class="msg_time" style="
												bottom: 0px !important;
												margin-left: 5%;
												color: white;">{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </mat-tab>

                        <mat-tab label="{{ 'TELASTREAMING.DADOSPACIENTE' | translate }}" class="color-anon"
                            *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && DadosP && !InfoP && !Localizacao && !Atestado && !Receituario && !Declaracao">

                            <div>
                                <div class="row text-center">
                                    <div class="col-md-12 col-sm-12 col-xs-12">
                                        <!-- <small>{{ 'TELASTREAMING.DADOSPACIENTE' | translate }}</small> -->
                                    </div>
                                </div>


                                <div style="float: left;">

                                    <div style=" margin-right: auto; margin-left: auto;">
                                        <img src="assets/build/img/corpoPaciente.png"
                                            style="width: 305px;margin-top: 3px;	margin-left: -60px;	margin-right: -97px;">
                                    </div>

                                </div>


                                <div style="overflow: auto !important;">

                                    <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">

                                        <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                            <input matInput placeholder="{{ 'TELASTREAMING.PESO' | translate }}"
                                                name="Peso" maxlength="6" id="CampoPesoStream"
                                                (keypress)="mascaraPeso($event)" (keyup)="mascaraPeso($event)"
                                                [(ngModel)]="Dados.peso"
                                                (blur)="CalculoIMC('CampoAlturaStream','CampoPesoStream')">
                                        </mat-form-field>


                                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                            <input matInput placeholder="{{ 'TELASTREAMING.ALTURA' | translate }}"
                                                id="CampoAlturaStream" name="Altura" maxlength="4"
                                                (keypress)="mascaraAltura($event)" (keyup)="mascaraAltura($event)"
                                                [(ngModel)]="Dados.altura"
                                                (blur)="CalculoIMC('CampoAlturaStream','CampoPesoStream')">
                                        </mat-form-field>

                                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                            <input matInput placeholder="{{ 'TELASTREAMING.IMC' | translate }}"
                                                name="IMC" disabled [(ngModel)]="Dados.IMC" maxlength="5">
                                        </mat-form-field>

                                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                            <input matInput placeholder="{{ 'TELASTREAMING.PRESSAO' | translate }}"
                                                id="campoPressao" name="Pressão" [(ngModel)]="Dados.pressao"
                                                (keyup)="mascaraPressao($event)" (keypress)="mascaraPressao($event)"
                                                maxlength="5">
                                        </mat-form-field>

                                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                            <input matInput placeholder="{{ 'TELASTREAMING.BATIMENTO' | translate }}"
                                                name="Batimento" (keyup)="mascaraNumeros($event)"
                                                [(ngModel)]="Dados.batimento" maxlength="3">
                                        </mat-form-field>

                                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                            <input matInput placeholder="{{ 'TELASTREAMING.TEMPERATURA' | translate }}"
                                                id="campoTemp" (keypress)="mascaraTemperatura($event)"
                                                (keyup)="mascaraTemperatura($event)" name="Temperatura" maxlength="5"
                                                [(ngModel)]="Dados.temperatura">
                                        </mat-form-field>
                                    </div>
                                </div>
                                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-2"
                                    style="text-align: center">
                                    <button class="btn-primary " mat-raised-button style="color:white;"
                                        (click)="LimparDados()" style="margin-right: 2%;">
                                        <mat-icon>clear</mat-icon> {{ 'TELASTREAMING.LIMPAR' | translate }}
                                    </button>

                                    <button class="btn-primary " mat-raised-button style="color:white;"
                                        (click)="SalvarDadosCorpo('CampoAlturaStream','CampoPesoStream')">
                                        <mat-icon>save</mat-icon> {{ 'TELASTREAMING.SALVAR' | translate }}
                                    </button>
                                </div>
                            </div>
                        </mat-tab>

                        <mat-tab label=" {{ 'TELASTREAMING.INFOPESSOAIS' | translate }} " class="color-anon"
                            *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && InfoP && !Localizacao && !Atestado && !Receituario && !Declaracao">


                            <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">
                                <div class="row text-center" style="padding: 10px;">
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled
                                            name="Nome" [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" disabled
                                            name="CPF" mask="000.000.000-00" [(ngModel)]="DadosInformUsuario.cpf"
                                            style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email"
                                            disabled name="Email" [(ngModel)]="DadosInformUsuario.email"
                                            style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" disabled
                                            name="Celular" mask="(00) 00000-0000"
                                            [(ngModel)]="DadosInformUsuario.celular" style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.TELEFONE' | translate }}" disabled
                                            name="Telefone" mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.tel"
                                            style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.TELEFONECOM' | translate }}"
                                            disabled name="TelComerciar" mask="(00) 00000-0000" style="
                                      color: black;" [(ngModel)]="DadosInformUsuario.telComer">
                                    </mat-form-field>
                                </div>
                                <div style="overflow: auto !important;">

                                    <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">


                                    </div>
                                </div>
                            </div>
                        </mat-tab>

                        <mat-tab label="{{ 'TELASTREAMING.ATESTADO' | translate }}"
                            *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !InfoP  && !Localizacao && Atestado && !Receituario && !Declaracao">
                            <div class="dashed-content">
                                <div class="col-md-12 col-sm-12 ">
                                    <span class="custom-span"
                                        style="font-size: 12px;">{{ 'TELADOCUMENTACAO.ATIVIDADESNORMAIS' | translate }}
                                    </span><br>
                                    <input matInput maxlength="3" name="dias" [(ngModel)]="DiasAtestado"
                                        (keyup)="mascaraNumeros($event)" style="width: 30px; border: 1px solid;">

                                    <span class="custom-span" style="font-size: 12px;">
                                        {{ 'TELADOCUMENTACAO.DIA' | translate }}
                                    </span><br>
                                    <br>
                                    <!-- <mat-checkbox [(ngModel)]="DesCID" checked class="custom-span"
                                        style="font-size: 12px;">
                                        {{ 'TELADOCUMENTACAO.NAOIMPRIMIRDESCRICAODOCID' | translate }}</mat-checkbox> -->
                                </div>


                                <div class="col-md-12 col-sm-12 row">
                                    <mat-form-field class="col-md-5 col-sm-6">
                                        <input matInput placeholder="{{ 'TELADOCUMENTACAO.CID' | translate }}"
                                            name="CID" (change)="CarregaCIDCampo()" maxlength="6" [(ngModel)]="desCID"
                                            style="text-transform: uppercase;">
                                        <mat-icon aria-label="Buscar" style="
                                              margin-left: -25px;" class="col-md-1 custom-search"
                                            (click)="AbrirModalPesquisaCid()">search
                                        </mat-icon>

                                    </mat-form-field>



                                    <mat-form-field class="col-md-7 col-sm-6">
                                        <input matInput
                                            placeholder="{{ 'TELADOCUMENTACAO.DESCRICAODOCID' | translate }}" name="CID"
                                            maxlength="6" [(ngModel)]="CID" style="color: black;" disabled>
                                    </mat-form-field>
                                </div>
                            </div>
                            <div class="col-12 row-button text-center mt-30">
                                <div class="col-md-12 col-sm-12">
                                    <button mat-flat-button (click)="GerarAtestado()" class=" btn-primary align-btn">
                                        {{ 'TELASTREAMING.GERAR' | translate }}
                                    </button>

                                    <button mat-flat-button (click)="LimparAtestado()" class="btn-primary align-btn">
                                        {{ 'TELASTREAMING.LIMPAR' | translate }}
                                    </button>

                                    <!-- <button mat-flat-button (click)="GerarAtestado('Enviar')"
                                        class="btn-primary align-btn">
                                        {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                    </button> -->
                                </div>
                            </div>
                        </mat-tab>

                        <mat-tab label="{{ 'TELASTREAMING.RECEITUARIO' | translate }}"
                            *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !InfoP && !Localizacao && !Atestado && Receituario && !Declaracao">

                            <mat-form-field class="col-md-12 col-sm-6" style="margin-top: 5px;">
                                <input matInput placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOMEDICAMENTO' | translate }}"
                                    name="CPf" [(ngModel)]="nomeRemedio" (change)="filtroRemedio()"
                                    (keyup.enter)="filtroRemedio()">
                            </mat-form-field>
                            <div class="col-md-12 col-sm-12" style="margin-top: 15px;">
                                <mat-slide-toggle class="botao" style="margin-top: 4px;"
                                    [(ngModel)]='checkmedicamentosPro' (change)="filtroRemedio()" aria-checked="true">
                                    {{ 'TELADOCUMENTACAO.CHECKMEDICAMENTOS' | translate }}
                                </mat-slide-toggle>
                            </div>

                            <mat-selection-list color="primary" class="Medicine-content">
                                <div class="col-md-12 col-sm-12 col-xs-12 " *ngFor="let item of DadosRaceitaRemedios">

                                    <div (click)="addMedicamentos(item.produto ,item.apresentacao)"
                                        style="cursor: pointer;">
                                        <label title="{{item.produto}}"><b>{{item.produto| truncate : 22 : "…"}}</b>
                                        </label><br>
                                        <label title="{{item.apresentacao}}">
                                            {{item.apresentacao | truncate : 22 : "…"}}</label>
                                    </div>
                                </div>
                            </mat-selection-list>

                            <mat-form-field class="col-md-12">
                                <textarea matInput class="background-consulta tex-area-archives" name="txtMensagem"
                                    id="txtMensagem" cdkTextareaAutosize #autosize="cdkTextareaAutosize"
                                    name="txtMensagem" [(ngModel)]="CampoReceita" maxlength="900"></textarea>
                                <!-- <mat-placeholder class="textarea-placeholder">
                                    {{ 'TELASTREAMING.MEDICAMENTOS' | translate }}</mat-placeholder> -->
                            </mat-form-field>
                            <div class="col-12 row-button text-center">

                                <!-- <div class="col-md-12 col-sm-12">
                                    <mat-checkbox [(ngModel)]="flgEndereco" checked style="font-size: 12px;">
                                        {{ 'TELADOCUMENTACAO.IMPRIMIRENDERECODOPACIENTE' | translate }}
                                    </mat-checkbox>
                                </div> -->

                                <div class="col-md-12 col-sm-12">
                                    <button mat-flat-button (click)="GerarReceita('')" class="align-btn btn-primary">
                                        {{ 'TELASTREAMING.GERAR' | translate }}
                                    </button>

                                    <button mat-flat-button (click)="LimparReceita()" class="align-btn btn-primary">
                                        {{ 'TELASTREAMING.LIMPAR' | translate }}
                                    </button>
                                    <!--
                                    <button mat-flat-button (click)="GerarReceita('Enviar')"
                                        class="align-btn btn-primary"> {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                    </button> -->
                                </div>
                            </div>

                        </mat-tab>


                        <mat-tab label="{{ 'TELASTREAMING.CECLARACAO' | translate }}" class="color-anon"
                            *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !InfoP && !Localizacao && !Atestado && !Receituario && Declaracao">

                            <div class="col-md-12">
                                <span class="custom-span"
                                    style="font-size: 12px;">{{ 'TELADOCUMENTACAO.COMPARECEUNOPERIUDO' | translate }}
                                </span><br>
                            </div>

                            <mat-form-field class="col-md-12 col-sm-12">
                                <input matInput maxlength="50" name="periudo"
                                    placeholder="{{ 'TELADOCUMENTACAO.PERIUDO' | translate }}" required
                                    [formControl]='periudo' [(ngModel)]="periudoDeclaracao">
                                <mat-error *ngIf="periudo.invalid">{{getErrorMessageperiudo() | translate }}
                                </mat-error>
                            </mat-form-field>

                            <div class="col-12 row-button text-center">

                                <div class="col-md-12 col-sm-12">
                                    <button mat-flat-button (click)="GerarDeclaracao()" class="align-btn btn-primary">
                                        {{ 'TELASTREAMING.GERAR' | translate }}
                                    </button>

                                    <button mat-flat-button (click)="LimparDeclaracao()" class="align-btn btn-primary">
                                        {{ 'TELASTREAMING.LIMPAR' | translate }}
                                    </button>

                                    <!-- <button mat-flat-button (click)="GerarDeclaracao('Enviar')"
                                        class="align-btn btn-primary">
                                        {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                    </button> -->
                                </div>

                            </div>

                        </mat-tab>
                    </mat-tab-group>


                </div>


            </div>

        </mat-nav-list>
    </mat-sidenav>
    <mat-sidenav-content>
        <mat-sidenav-container class="drawer-height" style=" height: 100%;">
            <mat-sidenav opened autosize mode="side" class="aba_secundaria drawer-height" *ngIf="showAba"
                position="end">
                <div class="spacer-aba">
                </div>
                <div class="text-center h-84">
                    <h4 class="h4-perg"> {{ 'TELASTREAMING.PERGUNTAS' | translate }} </h4>
                </div>
                <mat-card-content>
                    <section class="example-section">
                        <!-- <mat-progress-bar style=" height: 30px;" class="example-margin" [color]="color" [mode]="mode"
                            [value]="value" [bufferValue]="bufferValue">
                        </mat-progress-bar> -->
                    </section>
                </mat-card-content>
                <div>
                    <mat-selection-list #pergunta color="primary">
                        <mat-list-option *ngFor="let item of DadosPerguntas"
                            (click)="checkPerguntas(pergunta.selectedOptions.selected.length)">
                            {{item.desPergunta}}
                        </mat-list-option>
                    </mat-selection-list>
                </div>
            </mat-sidenav>
            <mat-sidenav-content>
                <div class="rec no-mobile" style="padding-top: 0px !important;width: 72px" *ngIf="MedicoPermissao">
                    <div class="red">
                    </div>
                    <label
                        style="padding-top: 9px; font-size: 0.7rem; margin-left: 3px;">{{hora}}:{{minutos | number:'2.0'}}:{{segundos | number:'2.0'}}</label>
                </div>
                <div class="" style="position: absolute;margin-left: 40px; padding-top: 62px;"
                    *ngIf="MedicoPermissao && primeiraConsulta">
                    <img src="assets/build/img/primeiraconsulta.png" title="Primeira Consulta" class="img-responsive"
                        style="width: 32px;">
                </div>
                <div class="Menu_esquerdo">
                    <button mat-mini-fab aria-label="Expandir" (click)="AbrePadrao()" class="btn-primary menu_panel ">
                        <mat-icon>settings</mat-icon>
                    </button>
                    <button mat-mini-fab class="btn-primary menu_panel" *ngIf=" MedicoPermissao"
                        (click)="AbreHistorico()">
                        <mat-icon>history</mat-icon>
                    </button>


                    <ul style="margin-bottom: 0;" *ngIf="MedicoPermissao">
                        <mat-nav-list style="padding-top:0px;">
                            <button mat-mini-fab matTooltip=" {{ 'TELASTREAMING.DESLIGARCONFERENCIA' | translate }} "
                                *ngIf="Sairsala != true" class="menu_panel btn-primary"
                                (click)="ngxSmartModalService.getModal('FinalizarChamada').open()">
                                <mat-icon class="menu_icon">power_settings_new</mat-icon>
                            </button>
                        </mat-nav-list>

                        <mat-nav-list style="padding-top:0px;">
                            <button mat-mini-fab matTooltip="Sair da Sala" *ngIf="Sairsala == true"
                                class="menu_panel btn-primary" (click)="BtnSair()">
                                <mat-icon class="menu_icon">exit_to_app</mat-icon>
                            </button>
                        </mat-nav-list>
                    </ul>


                    <!-- AVALIAÇÃO -->
                   <!-- <ul style="margin-bottom: 0;" *ngIf="MedicoPermissao">
                        <mat-nav-list style="padding-top:0px;">
                            <button mat-mini-fab matTooltip=" {{ 'TELASTREAMING.DESLIGARCONFERENCIA' | translate }} "
                                class="menu_panel btn-primary"
                                (click)="ngxSmartModalService.getModal('Avaliacao').open()">

                            </button>
                        </mat-nav-list>
                    </ul>-->
                </div>




                <div class="principal_content">

                    <!-- <div id="react" -->
                    <div id="react" [className]="isOpen ?' VideoMobileOpenMenu no-desktop ': 'VideoMobileCloseMenu no-desktop'"
                        *ngIf="showFillerStream">

                         <iframe *ngIf="FlgVideo" [src]="urlSafe" allow="microphone; camera"></iframe>
                    </div>

                    <!-- <div id="react"  -->
                    <div id="react" class="container-fluid camera" *ngIf="!showFillerStream">

                         <iframe *ngIf="FlgVideo" [src]="urlSafe" allow="microphone; camera"></iframe>


                    </div>

                    <div class="panel_mobile" [@openClose]="isOpen ? 'open': 'closed'" style="padding-bottom: 35px;">

                        <div (click)="toggle()" class="col-sm-12 Mobiletoggle">
                            <button mat-icon-button 
                                [className]="isOpen ? 'close-button close-iconrotate mat-icon-button' : 'close-button mat-icon-button'">
                                <mat-icon>keyboard_arrow_up</mat-icon>
                            </button>
                        </div>

                        <div class="col-sm-12 row buttons_mobile">

                            <div class=" text-center" style="padding-right: 2% !important;"
                                *ngIf="Sairsala != true && MedicoPermissao">
                                <button mat-mini-fab
                                    matTooltip=" {{ 'TELASTREAMING.DESLIGARCONFERENCIA' | translate }} "
                                    class="btn-primary"
                                    (click)="ngxSmartModalService.getModal('FinalizarChamada').open()">
                                    <mat-icon class="">power_settings_new</mat-icon>
                                </button>
                            </div>

                            <!-- <button mat-mini-fab matTooltip="Sair da Sala" *ngIf="Sairsala == true"
                                class="menu_panel btn-primary" (click)="BtnSair()">
                                <mat-icon class="menu_icon">exit_to_app</mat-icon>
                            </button> -->
                            <div class=" text-center" style="padding-right: 2% !important;"
                                *ngIf="Sairsala == true && MedicoPermissao">
                                <button mat-mini-fab
                                    matTooltip=" {{ 'TELASTREAMING.DESLIGARCONFERENCIA' | translate }} "
                                    class="btn-primary" (click)="BtnSair()">
                                    <mat-icon class="">exit_to_app</mat-icon>
                                </button>
                            </div>
<!-- AVALIAÇÃO -->
                            <!--<div class="text-center panel-selector">
                                <button mat-mini-fab
                                    matTooltip=" {{ 'TELASTREAMING.DESLIGARCONFERENCIA' | translate }} "
                                    class="menu_panel btn-primary"
                                    (click)="ngxSmartModalService.getModal('Avaliacao').open()">

                                </button>
                            </div>-->

                            <div class="text-center" style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Anotacoesanommobi()">
                                    <mat-icon>visibility_off</mat-icon>
                                </button>

                            </div>
                            <div class="text-center " style="padding-right: 2% !important;">
                                <button mat-mini-fab class="btn-primary" (click)="Anexomobi()">
                                    <mat-icon>archive</mat-icon>
                                </button>

                            </div>


                            <div class="text-center " style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Anotacoesmobi()">
                                    <mat-icon>assignment</mat-icon>
                                </button>

                            </div>

                            <div class="text-center" style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Consultasmobi()">
                                    <mat-icon>account_circle</mat-icon>
                                </button>

                            </div>

                            <div class="text-center" style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Infomobi()">
                                    <mat-icon>info</mat-icon>
                                </button>

                            </div>

                            <div class="text-center" style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Atestadomobi()">
                                    <mat-icon>assignment</mat-icon>
                                </button>

                            </div>

                            <div class="text-center" style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Receituariomobi()">
                                    <mat-icon>file_copy</mat-icon>
                                </button>

                            </div>

                            <div class="text-center" style="padding-right: 2% !important;" *ngIf='MedicoPermissao'>
                                <button mat-mini-fab class="btn-primary" (click)="Declaracaomobi()">
                                    <mat-icon>assignment_ind</mat-icon>
                                </button>

                            </div>

                            <!-- <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="AtestadoMenu()">
                                    <mat-icon>assignment</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.ATESTADO' | translate }}</small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="ReceituarioMenu()">
                                    <mat-icon>file_copy</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.RECEITUARIO' | translate }}</small>
                            </div>

                            <div class="text-center panel-selector" *ngIf="MedicoPermissao">
                                <button mat-mini-fab class="btn-primary" (click)="DeclaracaoMenu()">
                                    <mat-icon>assignment_ind</mat-icon>
                                </button>
                                <br>
                                <small>{{ 'TELASTREAMING.DECALARACAO' | translate }}</small>
                            </div>  -->


                            <div class="text-center  keyboard-mobile">
                                <button mat-mini-fab class="btn-primary toggle-mobi" (click)="toggle()">
                                    <mat-icon>keyboard_arrow_down</mat-icon>
                                </button>

                            </div>
                        </div>
                        <div class="content-mobile">
                            <div class="mt-10" *ngIf="anotacoesanommobi">
                                <mat-form-field class="col-md-12">

                                    <textarea matInput class="background-anom" placeholder="" cols="30" rows="5"
                                        cdkTextareaAutosize #autosize="cdkTextareaAutosize" name="anonimo"
                                        (blur)="CampoAnonimo()" [(ngModel)]="Anonimo"
                                        maxlength="500">
                                             </textarea>
                                </mat-form-field>
                                <div class=" col-12 row-button text-center">
                                    <button mat-flat-button class="btn btn-primary big-btn">
                                        {{ 'TELASTREAMING.SALVAR' | translate }}
                                    </button>

                                </div>
                            </div>

                            <div class="mt-10" *ngIf="anexomobi">

                                <div class="h-auto d-flow-root text-center">
                                    <label for="file1" class="btn btn-primary"
                                        style="font-size:12px!important;margin-bottom: 20px;">
                                        CARREGAR ARQUIVO </label>
                                    <input type="file" style="width:100%;" id="file1"
                                        (change)="SubirArquivoConsulta($event)" accept=".xls,.xlsx,.pdf" />

                                </div>

                                <div class="background-padr" style="overflow: auto visible; height: 20vw;
                                    border: dotted #999;">
                                    <div class="d-flex justify-content-start" *ngFor="let item of DadosanexConsulta">
                                        <div class="col-md-12 col-sm-12 col-xs-12" *ngIf="item.idUsuario == usuario">
                                            <div class="balao2 col-10 mb-4">
                                                <i (click)="BaixarArquivoMobile(item.chave,item.nomeArquivo)"
                                                    style="cursor: pointer;font-size: 20px;
                                                    vertical-align: bottom;" class="fa fa-download fa-fw  icone"
                                                    title="Download arquivo"></i><span title="{{item.nomeArquivo }}"
                                                    class="texto text-left" style="font-size: 12px;
                                                    vertical-align: middle;">
                                                    {{item.nomeArquivo}}</span>
                                                <span class="msg_time"
                                                    style="
                                                        bottom: 0px !important;
                                                        margin-left: 5%;
                                                        color: white;">{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-12 col-sm-12 col-xs-12"
                                            *ngIf="item.idUsuario != usuario && (tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& item.flgVisualizacaoUsuario==true)">

                                            <div class="balao3 col-10 mb-4">
                                                <i (click)="BaixarArquivoMobile(item.chave,item.nomeArquivo)"
                                                    style="cursor: pointer;    font-size: 20px;
                                                    vertical-align: bottom;" class="fa fa-download fa-fw icone"
                                                    title="Download arquivo"></i><span title="{{item.nomeArquivo }}"
                                                    class="texto text-left" style="font-size: 12px;
                                                    vertical-align: middle;">
                                                    {{item.nomeArquivo | truncate : 25 : "…"}}</span>
                                                <span class="msg_time"
                                                    style="
                                                    bottom: 0px !important;
                                                    margin-left: 5%;
                                                    color: white;">{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="mt-10" *ngIf="anotacoesmobi">
                                <mat-form-field class="col-md-12">
                                    <textarea matInput class="background-consulta" name="txtMensagem" id="txtMensagem"
                                        cdkTextareaAutosize #autosize="cdkTextareaAutosize" maxlength="500"
                                        name="txtMensagem" (blur)='CampoConsulta()'
                                        [(ngModel)]="CampoConsultaPerguntas"></textarea>

                                </mat-form-field>
                                <div class="col-12 row-button text-center">
                                    <button mat-flat-button class=" btn btn-primary big-btn">
                                        {{ 'TELASTREAMING.SALVAR' | translate }}
                                    </button>

                                </div>
                            </div>


                            <div class="mt-10" style="overflow: auto !important;" *ngIf="consultasmobi">

                                <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">

                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELASTREAMING.PESO' | translate }}" name="Peso"
                                            maxlength="6" id="campoPeso" (keypress)="mascaraPeso($event)"
                                            (keyup)="mascaraPeso($event)" [(ngModel)]="Dados.peso"
                                            (blur)="CalculoIMC('campoAltura','campoPeso')">
                                    </mat-form-field>


                                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELASTREAMING.ALTURA' | translate }}"
                                            id="campoAltura" name="Altura" maxlength="4"
                                            (keypress)="mascaraAltura($event)" (keyup)="mascaraAltura($event)"
                                            [(ngModel)]="Dados.altura" (blur)="CalculoIMC('campoAltura','campoPeso')">
                                    </mat-form-field>

                                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                        <input matInput disabled placeholder="{{ 'TELASTREAMING.IMC' | translate }}"
                                            name="IMC" [(ngModel)]="Dados.IMC" maxlength="5">
                                    </mat-form-field>


                                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELASTREAMING.PRESSAO' | translate }}"
                                            id="campoPressao" name="Pressão" [(ngModel)]="Dados.pressao"
                                            (keyup)="mascaraPressao($event)" (keypress)="mascaraPressao($event)"
                                            maxlength="5">
                                    </mat-form-field>

                                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELASTREAMING.BATIMENTO' | translate }}"
                                            name="Batimento" (keyup)="mascaraNumeros($event)"
                                            [(ngModel)]="Dados.batimento" maxlength="3">
                                    </mat-form-field>

                                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                                        <input matInput placeholder="TTEEMMPP" id="campoTemp"
                                            (keypress)="mascaraTemperatura($event)" (keyup)="mascaraTemperatura($event)"
                                            name="Temperatura" maxlength="5" [(ngModel)]="Dados.temperatura">
                                    </mat-form-field>

                                </div>
                                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4"
                                    style="text-align: center">
                                    <button class="btn-primary " mat-raised-button style="color:white;"
                                        (click)="LimparDados()" style="margin-right: 2%;">
                                        <mat-icon>clear</mat-icon>
                                    </button>

                                    <button class="btn-primary " mat-raised-button style="color:white;"
                                        (click)="SalvarDadosCorpo(1,1)"
                                        >
                                        <mat-icon>save</mat-icon>
                                    </button>
                                </div>
                            </div>


                            <div class="mt-10" style="overflow: auto !important;" *ngIf="infomobi">

                                <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled
                                            name="Nome" [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" disabled
                                            name="CPF" mask="000.000.000-00" [(ngModel)]="DadosInformUsuario.cpf"
                                            style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email"
                                            disabled name="Email" [(ngModel)]="DadosInformUsuario.email"
                                            style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" disabled
                                            name="Celular" mask="(00) 00000-0000"
                                            [(ngModel)]="DadosInformUsuario.celular" style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.TELEFONE' | translate }}" disabled
                                            name="Telefone" mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.tel"
                                            style="color: black;">
                                    </mat-form-field>
                                    <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                                        <input matInput placeholder="{{ 'TELAAGENDA.TELEFONECOM' | translate }}"
                                            disabled name="TelComerciar" mask="(00) 00000-0000" style="
                                      color: black;" [(ngModel)]="DadosInformUsuario.telComer">
                                    </mat-form-field>
                                </div>
                            </div>


                            <div class="mt-10" style="overflow: auto !important;" *ngIf="atestadomobi">

                                <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">
                                    <div class="dashed-content">
                                        <div class="col-md-12 col-sm-12 ">
                                            <span class="custom-span-Mobile"
                                                style="font-size: 12px;">{{ 'TELADOCUMENTACAO.ATIVIDADESNORMAIS' | translate }}
                                            </span><br>

                                            <mat-form-field class="col-md-5 col-sm-6">
                                                <input matInput placeholder="Dias" maxlength="3" name="dias"
                                                    [(ngModel)]="DiasAtestado" (keyup)="mascaraNumeros($event)">
                                            </mat-form-field>

                                            <!-- <input matInput maxlength="3" name="dias" [(ngModel)]="DiasAtestado"
                                                (keyup)="mascaraNumeros($event)"
                                                style="width: 30px; border: 1px solid;"> -->

                                            <!-- <span class="custom-span" style="font-size: 12px;">
                                                {{ 'TELADOCUMENTACAO.DIA' | translate }}
                                            </span><br>
                                            <br>
                                            <mat-checkbox [(ngModel)]="DesCID" checked class="custom-span"
                                                style="font-size: 12px;">
                                                {{ 'TELADOCUMENTACAO.NAOIMPRIMIRDESCRICAODOCID' | translate }}
                                            </mat-checkbox> -->
                                        </div>


                                        <div class="col-md-12 col-sm-12 row">
                                            <mat-form-field class="col-md-5 col-sm-6">
                                                <input matInput placeholder="{{ 'TELADOCUMENTACAO.CID' | translate }}"
                                                    name="CID" (change)="CarregaCID()" maxlength="6"
                                                    [(ngModel)]="desCID" style="text-transform: uppercase;">
                                            </mat-form-field>



                                            <mat-form-field class="col-md-7 col-sm-6">
                                                <input matInput
                                                    placeholder="{{ 'TELADOCUMENTACAO.DESCRICAODOCID' | translate }}"
                                                    name="CID" maxlength="6" [(ngModel)]="CID" style="color: black;"
                                                    disabled>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                    <div class="col-12 row-button text-center mt-30">
                                        <div class="col-md-12 col-sm-12">
                                            <button mat-flat-button (click)="GerarAtestado()"
                                                class=" btn-primary align-btn">
                                                {{ 'TELASTREAMING.GERAR' | translate }}
                                            </button>

                                            <button mat-flat-button (click)="LimparAtestado()"
                                                class="btn-primary align-btn">
                                                {{ 'TELASTREAMING.LIMPAR' | translate }}
                                            </button>

                                            <!-- <button mat-flat-button (click)="GerarAtestado('Enviar')"
                                                class="btn-primary align-btn">
                                                {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                            </button> -->
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="mt-10" style="overflow: auto !important;" *ngIf="receituariomobi">

                                <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">
                                    <mat-form-field class="col-md-12 col-sm-6" style="margin-top: 5px;">
                                        <input matInput
                                            placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOMEDICAMENTO' | translate }}"
                                            name="CPf" [(ngModel)]="nomeRemedio" (change)="filtroRemedio()"
                                            (keyup.enter)="filtroRemedio()">
                                    </mat-form-field>
                                    <div class="col-md-12 col-sm-12" style="margin-top: 15px;">
                                        <mat-slide-toggle class="botao" style="margin-top: 4px;"
                                            [(ngModel)]='checkmedicamentosPro' (change)="filtroRemedio()"
                                            aria-checked="true">
                                            {{ 'TELADOCUMENTACAO.CHECKMEDICAMENTOS' | translate }}
                                        </mat-slide-toggle>
                                    </div>

                                    <mat-selection-list color="primary" class="Medicine-content">
                                        <div class="col-md-12 col-sm-12 col-xs-12 "
                                            *ngFor="let item of DadosRaceitaRemedios">

                                            <div (click)="addMedicamentos(item.produto ,item.apresentacao)"
                                                style="cursor: pointer;">
                                                <label
                                                    title="{{item.produto}}"><b>{{item.produto| truncate : 22 : "…"}}</b>
                                                </label><br>
                                                <label title="{{item.apresentacao}}">
                                                    {{item.apresentacao | truncate : 22 : "…"}}</label>
                                            </div>
                                        </div>
                                    </mat-selection-list>

                                    <mat-form-field class="col-md-12">
                                        <textarea matInput class="background-consulta tex-area-archives"
                                            name="txtMensagem" id="txtMensagem" cdkTextareaAutosize
                                            #autosize="cdkTextareaAutosize" name="txtMensagem"
                                            [(ngModel)]="CampoReceita" maxlength="900"></textarea>
                                            <mat-label class="textarea-placeholder">Medicamentos</mat-label>
                                    </mat-form-field>
                                    <div class="col-12 row-button text-center">

                                        <!-- <div class="col-md-12 col-sm-12">
                                            <mat-checkbox [(ngModel)]="flgEndereco" checked style="font-size: 12px;">
                                                {{ 'TELADOCUMENTACAO.IMPRIMIRENDERECODOPACIENTE' | translate }}
                                            </mat-checkbox>
                                        </div> -->

                                        <div class="col-md-12 col-sm-12">
                                            <button mat-flat-button (click)="GerarReceita('')"
                                                class="align-btn btn-primary">
                                                {{ 'TELASTREAMING.GERAR' | translate }}
                                            </button>

                                            <button mat-flat-button (click)="LimparReceita()"
                                                class="align-btn btn-primary">
                                                {{ 'TELASTREAMING.LIMPAR' | translate }}
                                            </button>

                                            <!-- <button mat-flat-button (click)="GerarReceita('Enviar')"
                                                class="align-btn btn-primary">
                                                {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                            </button> -->
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="mt-10" style="overflow: auto !important;" *ngIf="declaracaomobi">

                                <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">
                                    <div class="col-md-12">
                                        <span class=""
                                            style="font-size: 12px;">{{ 'TELADOCUMENTACAO.COMPARECEUNOPERIUDO' | translate }}
                                        </span><br>
                                    </div>

                                    <mat-form-field class="col-md-12 col-sm-12">
                                        <input matInput maxlength="50" name="periudo"
                                            placeholder="{{ 'TELADOCUMENTACAO.PERIUDO' | translate }}" required
                                            [formControl]='periudo' [(ngModel)]="periudoDeclaracao">
                                        <mat-error *ngIf="periudo.invalid">{{getErrorMessageperiudo() | translate }}
                                        </mat-error>
                                    </mat-form-field>
                                    <div class="col-12 row-button text-center">
                                        <div class="col-md-12 col-sm-12">
                                            <button mat-flat-button (click)="GerarDeclaracao()"
                                                class="align-btn btn-primary">
                                                {{ 'TELASTREAMING.GERAR' | translate }}
                                            </button>

                                            <button mat-flat-button (click)="LimparDeclaracao()"
                                                class="align-btn btn-primary">
                                                {{ 'TELASTREAMING.LIMPAR' | translate }}
                                            </button>

                                            <!-- <button mat-flat-button (click)="GerarDeclaracao('Enviar')"
                                                class="align-btn btn-primary">
                                                {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                            </button> -->
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                </div>

            </mat-sidenav-content>
        </mat-sidenav-container>
    </mat-sidenav-content>
</mat-sidenav-container>



<ngx-smart-modal #Avaliacao identifier="Avaliacao" customClass="nsm-centered medium-modal form-modal modal-avaliacao"
    [dismissable]="false" [closable]="false">
    <div class="row">
        <div class="col-md-12 no-mobile div-aval-desktop">
            <div class="row form-content p-20" style="padding-bottom:0px">
                <h4 class="title-aval-modal" style="text-transform: uppercase; padding-bottom: 20px;">
                    {{ 'TELASTREAMING.FACASUAAVALIACAO' | translate }}
                </h4>

                <hr class="sep-1" />

                <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
                <div class="col-8 " style="text-align: center !important; padding: 0; justify-content: center;  margin: 0 auto;">

                    <form id="avaliacao" class="p-t-20 card-aval-desk" *ngIf="!showFillerStream">
                        <div class="estrelas" *ngFor="let item of quesitosAvaliacao; let i = index">
                            <input type="radio" id="cm_star-empty{{i}}" name="fb{{i}}" value="" checked />
                            <label for="cm_star-1{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-1{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-2{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-2{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-3{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-3{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-4{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-4{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-5{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-5{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label class="usuario-principal" id="{{item.idQuesito}}">{{item.quesito}}</label>
                        </div>
                    </form>

                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12" hintLabel="Máx. 100" appearance="outline"
                        floatLabel="always">
                        <mat-label>{{ 'TELASTREAMING.ALGUMAOBSERVACAO' | translate }}</mat-label>
                        <textarea matInput [(ngModel)]="AlgumaObservacao" name="Observações" id="obsAmbiente"
                            style="max-height: 77px;" maxlength="100"></textarea>
                        <mat-hint align="end">{{ AlgumaObservacao.length + 0}}/100</mat-hint>
                    </mat-form-field>
                </div>

            </div>
            <div class="col-12 row-button text-center p-t-20">
                <!-- <button mat-flat-button (click)="ModalAgenda.close()" class="input-align btn btn-danger"> NÃO </button> -->
                <button mat-flat-button (click)="EnviarAvaliacao('Desk')" class="input-align btn btn-primary big-btn">
                    {{ 'TELASTREAMING.ENVIAR' | translate }}
                </button>

            </div>

            <div class="logo-medicina-modal">
                  </div>
        </div>

        <div class="col-12 div-aval-mobile no-desktop">
            <div class="row form-content p-20 div-row-aval">
                <div style="width: 100%; text-align: center;">
                    <h4 class="p-b-20" style="text-transform: uppercase; padding-bottom: 20px;">
                        {{ 'TELASTREAMING.FACASUAAVALIACAO' | translate }}
                    </h4>
                </div>

                <hr class="sep-1" />

                <link rel="stylesheet" href="//maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css">
                <div class="col-12 " style="text-align: left !important; padding: 5px;">
                    <form id="avaliacaoMobile" class="p-t-20" *ngIf="showFillerStream">
                        <div class="estrelas" *ngFor="let item of quesitosAvaliacao; let i = index">
                            <input type="radio" id="cm_star-empty{{i}}" name="fb{{i}}" value="" checked />
                            <label for="cm_star-1{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-1{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-2{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-2{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-3{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-3{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-4{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-4{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label for="cm_star-5{{i}}"><i style="cursor: pointer" class="fa"></i></label>
                            <input type="radio" id="cm_star-5{{i}}" name="fb{{i}}" value="{{item.idQuesito}}" />
                            <label class="usuario-principal" id="{{item.idQuesito}}">{{item.quesito}}</label>
                        </div>
                    </form>

                    <mat-form-field class="col-md-12 col-sm-12 col-xs-12" hintLabel="Máx. 100" appearance="outline"
                        floatLabel="always">
                        <mat-label>{{ 'TELASTREAMING.ALGUMAOBSERVACAO' | translate }}</mat-label>
                        <textarea matInput [(ngModel)]="AlgumaObservacao" name="Observações" id="obsAmbiente"
                            style="max-height: 77px;" maxlength="100"></textarea>
                        <mat-hint align="end">{{ AlgumaObservacao.length + 0}}/100</mat-hint>
                    </mat-form-field>
                </div>

            </div>
            <div class="col-12 row-button text-center p-t-20">
                <!-- <button mat-flat-button (click)="ModalAgenda.close()" class="input-align btn btn-danger"> NÃO </button> -->
                <button mat-flat-button (click)="EnviarAvaliacao('Mobile')" class="input-align btn btn-primary big-btn">
                    {{ 'TELASTREAMING.ENVIAR' | translate }}
                </button>

            </div>

            <div class="logo-medicina-modal">
                  </div>
        </div>
    </div>

</ngx-smart-modal>

<ngx-smart-modal #AvaSucess identifier="AvaSucess" customClass="nsm-centered medium-modal">
    <div class="modal-header p-t-20 p-b-20">
        <h1 class="little-title fw-700">
            {{ 'TELASTREAMING.AVALIACAOREALIZADACOMSUCESSO' | translate }}
        </h1>
        <mat-divider></mat-divider>
        <div class="row-button text-right p-t-20">
            <hr>
            <button mat-flat-button (click)="AvaSucess.close()" class="input-align btn btn-success">
                {{ 'TELASTREAMING.FECHAR' | translate }} </button>
        </div>

    </div>
</ngx-smart-modal>


<!-- Modal inicializa -  -->

<ngx-smart-modal #Inicializando [autostart]="true" identifier="Inicializando" [closable]="false" [dismissable]="false"
    [escapable]="false" customClass="nsm-centered medium-modal emailmodal">

    <div class="content">
        <div class="no-mobile" style="display: flex; width: 491px !important;    background-size: 525px;
        background-position: bottom center;
        background-repeat: no-repeat;
        width: 460px;">
    
            <div style=" margin-right: auto; margin-left: auto;">
                <img src="assets/build/img/telemedicina.png" style="width: 490px;">
            </div>
        </div>
        <section class="example-section no-mobile">
            <mat-progress-bar class="example-margin" [value]="50" [bufferValue]="75">
            </mat-progress-bar>
        </section>
    
        <div class="modal-info no-mobile">
    
            <b *ngIf="!MedicoPermissao  && !covid">
              {{ 'TELASTREAMING.NAOSETRATADEUMACONSULTA' | translate }}
            </b>
            <b *ngIf="!MedicoPermissao  && covid">
                Este serviço trata-se de uma orientação relativa ao resultado de seu teste para covid-19.
              </b>
    
            <br *ngIf="!MedicoPermissao">
            <mat-divider *ngIf="!MedicoPermissao"></mat-divider>
    
    
    
            <li class="title-layout" style="color: #666;" *ngIf="MedicoPermissao">
                {{ 'TELASTREAMING.LAYOUTALTERNATIVO' | translate }}
            </li>
    
    
    
            <li *ngIf="MedicoPermissao">
                <div class="col-md-12 col-sm-12 row " style="justify-content: center;">
                    <div class="">
                        <img class="img-content layout-active non-active"
                            [className]="Modo1 == true ? 'img-content layout-active non-active' : 'img-content '"
                            src="assets/build/img/Layout-Padrao.svg" (click)="MudarModo('Modo1')">
                    </div>
    
    
                    <div class="">
                        <img class="img-content " src="assets/build/img/Layout-Extenso.svg"
                            [className]="Modo1 != true ? 'img-content layout-active non-active' : 'img-content '"
                            (click)="MudarModo('')">
                    </div>
    
                    <!-- <div class="" *ngIf="FlgSomenteProntuario">
                        <img class="img-content " src="assets/build/img/Layout-Extenso.svg" [className]="Modo1 != true ? 'img-content layout-active non-active' : 'img-content '" (click)="MudarModo('Modo3')">
                    </div> -->
    
    
            <li class="text-center mt-10" *ngIf="MedicoPermissao">
                <small class="small-droptext" style="color: #666;">
                    {{ 'TELASTREAMING.ESCOLHAUMLAYOUT' | translate }}
                </small>
    
                <!-- <mat-divider></mat-divider> -->
            </li>
        </div>
        </li>
    
        </div>
    
        <div class="no-desktop text-center" *ngIf="!covid">
            <b class="hight-content">
                {{ 'TELASTREAMING.NAOSETRATADEUMACONSULTA' | translate }}
            </b>
        </div>
    
        <div class="no-desktop text-center" *ngIf="!MedicoPermissao && covid">
          <b class="hight-content">
            Este serviço trata-se de uma orientação relativa ao resultado de seu teste para covid-19.
          </b>
      </div>
    
    
        <mat-divider *ngIf="!MedicoPermissao"></mat-divider>
        <div class="row-button text-center p-20 ">
    
            <button mat-flat-button (click)="Inicializacao()"
                class="input-align btn button-fontm btn-primary big-btn">{{ 'TELASTREAMING.ACEITAREINICIAR' | translate }}
            </button>
    
            <button mat-flat-button (click)="recusarSair()" style="    margin-top: 2%;"
                class=" button-fontm input-align btn btn-danger big-btn">{{ 'TELASTREAMING.SAIR' | translate }}
            </button>
        </div>
    </div>
</ngx-smart-modal>





<ngx-smart-modal #FinalizarChamada identifier="FinalizarChamada" customClass="nsm-centered medium-modal emailmodal">
   <div class="FinalizarContanier">
        <div class="background-off">
            <div style="justify-content: center;  display: flex;">
                <div class="modal-info" style="
    					margin-top: 31%;
    					margin-right: 25%;">
                </div>
                <!-- <mat-icon style="
    						color: #ffffff;
    						font-size: 136px;
    						margin-left: -10%;
    						margin-top: 27px;"> videocam_off</mat-icon> -->
            </div>
        </div>
        <div class="Title-modaloff">
            <b> {{ 'TELASTREAMING.FINALIZARCONSULTA' | translate }}</b>
        </div>
    
        <!-- <div class="modal-info">
    				<b> Médico:   Você Esta Pronto para Inicial? </b><br>
    			</div> -->
        <div class="modal-info col-md-12 col-sm-12 ">
            <!-- Paciente:  -->
            <b class="fonte">{{ 'TELASTREAMING.CONTINUARNASALA' | translate }}
            </b><br>
    
    
    
            <mat-divider></mat-divider>
    
            <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">
                <button class="btn-primary " mat-raised-button style="color:white;" (click)="FinalizarChamada.close()"
                    style="margin-right: 2%;">
                    <mat-icon class="ajuste-icon">clear</mat-icon> <span class="aparece">{{ 'TELASTREAMING.CANCELAR' | translate }}</span>
                </button>
    
    
                <button class="btn-primary " mat-raised-button style="color:white;" (click)="Finalizar()"
                    style="margin-right: 2%;">
                    <mat-icon class="ajuste-icon" >check</mat-icon> <span class="aparece"> {{ 'TELASTREAMING.FINALIZAR' | translate }}</span>
                </button>
    
    
            </div>
        </div>
   </div>
</ngx-smart-modal>

<!-- TELA 2  =============================================================================================\/-->


<div contents class="row " style="
   padding: 1%;" id="Modo2" *ngIf="Modo2">
    <div class="col-md-2 col-sm-4 pad-historico">
        <div class="text-center " style="padding: 3px; ">
            <h4 style="margin: 10px;">{{'TELASTREAMING.HISTORICO' | translate}}</h4>
        </div>
        <div class="col-md-12 col-sm-4" style="border: 1px solid #ddd; height: 58vh!important; overflow-y: auto; padding:0!important;
            overflow-x: hidden;">
            <div class="text-center " #scrollMe>
                <hr class="pontilhado">
                <div class="carregar-arquivo h-auto d-flow-root text-center" *ngIf="spinnerHistorico == true">
                    <img src="assets/build/img/spinner.gif" style="width: 100%;">
                </div>
                <div tmDarkenOnHover *ngFor="let item of dadosHistorico;let i = index;" contentsTable
                    class="row text-center d-flex justify-content-center" style="cursor: pointer !important;">
                    <span tmDarkenOnHover (click)="historico = true" contentsLink
                        position style=" padding: 5px;  display: block; font-size: 12px; color: #1265b9 !important;">
                        {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                    </span>
                    <mat-icon aria-label="Anonimo"
                        style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;" class=""
                        *ngIf="item.anonimo != null">visibility_off
                    </mat-icon>
                    <mat-icon aria-label="Anexo"
                        style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;" class=""
                        *ngIf="item.anexo.length >0">archive
                    </mat-icon>
                    <mat-icon aria-label="Dados Corpo"
                        style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;" class=""
                        *ngIf="item.dadosCorpo!=null">accessibility_new
                    </mat-icon>

                    <hr class="sep-2" />
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-10 col-sm-8 row pad-desk">
        <div class="col-md-8 col-sm-8 margem-form">
            <!-- <div class="col-md-1 col-sm-8">
             <img src={{ImagemPessoa}} style="border-radius: 50%; width: 140%; padding-top: 30%;">
           </div> -->
            <div class="row col-md-12 col-sm-8 margem-form2">
                <mat-form-field class="col-md-6 col-sm-12 input-spacing pad-campos-form">
                    <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled name="Nome"
                        [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
                </mat-form-field>

                <button mat-icon-button class="col-1 panel_button"
                    title="{{ 'TELAAGENDA.INFORMACOESPACIENTE' | translate }}" (click)="informacao()">
                    <mat-icon aria-label="Editar linha selecionada" style="color: #1265b9;" class="">info
                    </mat-icon>
                </button>

                <div class="col-md-5" style="padding-top: 0px !important; color: black; display: inline;">

                    <label>{{ 'TELASTREAMING.TEMPOCONSULTA' | translate }} :
                    </label> <label
                        style="padding-top: 9px; font-size: 1.1rem; margin-left: 3px;">{{hora}}:{{minutos | number:'2.0'}}:{{segundos | number:'2.0'}}</label>
                </div>
                <mat-form-field class="col-md-3 col-sm-12 input-spacing pad-campos-form">
                    <input matInput placeholder="{{ 'TELASTREAMING.DATA' | translate }}" disabled name="Nome"
                        [(ngModel)]="DadosInformUsuario.hoje" style="color: black;">
                </mat-form-field>

                <mat-form-field class="col-md-3 col-sm-12 input-spacing pad-campos-form">
                    <input matInput placeholder="{{ 'TELASTREAMING.HORA' | translate }}" disabled name="Nome"
                        [(ngModel)]="DadosInformUsuario.hora" style="color: black;">
                </mat-form-field>
                <div class="col-md-12 col-sm-8 pad-form-campo">
                    <mat-form-field class="col-md-3 col-sm-12 input-spacing pad-form">
                        <input matInput placeholder="Convênio" disabled name="Nome" [(ngModel)]="dadosConsulta.convenio"
                            style="color: black;">
                    </mat-form-field>

                    <img src="assets/build/img/R.png" style="
                width: 17px;
    margin-top: 2%;
                height: 17px;" *ngIf="dadosConsulta.flgretorno">
                </div>
            </div>
        </div>
        <div class="col-md-4 ">
            <!-- <div id="react"  -->
            <div  id="react" class="camera-ver">
                <!-- <iframe *ngIf="FlgVideo" [src]="urlSafe" allow="microphone; camera"></iframe> -->
                <iframe *ngIf="FlgVideo" [src]="urlSafe" allow="microphone; camera"></iframe>
            </div>
        </div>

        <div [className]="!corpo && !anexo ? 'col-12 coluna-consulta coluna-consulta-pad ' : ' col-md-9 coluna-consulta coluna-consulta-pad '"
            #scrollMe>

            <div class="col-md-12 col-sm-12 col-xs-12 padding-consultas"
                *ngFor="let item of dadosHistorico;let i = index">

                <div class=" mt-30" *ngIf="historico ">

                    <div class="Historico_Colap" style="padding: 10px;" id="{{ item.posicao }}" #destinationRef>
                        <div class="text-center col-md-12">
                            <span>
                                {{ 'TELASTREAMING.CONSULTADIA' | translate }}
                                {{item.dtaConsulta  | date: 'dd/MM/yyyy HH:mm' }}
                                {{ 'TELASTREAMING.MEDICO' | translate }} {{item.medico}}</span>
                        </div>

                        <div class="col-md-12" style="padding: 10px;">
                            <span class="text-left" *ngIf="item.obsConsulta != null && item.obsConsulta != '' ">
                                <hr class="pontilhado" *ngIf="item.obsConsulta != null && item.obsConsulta != ''">
                                {{ 'TELASTREAMING.PRONTUARIO' | translate }} {{item.obsConsulta}}</span>


                            <br *ngIf="item.receita != null">
                            <!-- <br *ngIf="item.receita != null">
                            <br *ngIf="item.receita != null"> -->
                            <span *ngIf="item.receita != null">
                                Receituário :<br> {{ item.receita}}</span>

                        </div>

                        <div style="background-color: white; margin-top: 10px;" *ngIf="item.dadosCorpo!=null">
                            <mat-accordion>
                                <mat-expansion-panel hideToggle (opened)="panelOpenState = true"
                                    (closed)="panelOpenState = false">
                                    <mat-expansion-panel-header style="height: 30px !important;">
                                        <mat-panel-title>
                                            <mat-icon>accessibility_new
                                            </mat-icon>
                                            <span class="ml-2">Dados do Paciente</span>
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <div class="text-left row" style="border: solid 1px black; padding: 15px;">
                                        <span class="text-left col-md-2" style="white-space: pre-wrap;">
                                            Peso: {{item.dadosCorpo.peso}}</span>
                                        <span class="text-left col-md-2" style="white-space: pre-wrap;">
                                            Altura: {{item.dadosCorpo.altura}}</span>
                                        <span class="text-left col-md-2" style="white-space: pre-wrap;">
                                            Imc: {{item.dadosCorpo.imc}}</span>
                                        <span class="text-left col-md-2" style="white-space: pre-wrap;">
                                            Pressao: {{item.dadosCorpo.pressao}}</span>
                                        <span class="text-left col-md-2" style="white-space: pre-wrap;">
                                            Batimento: {{item.dadosCorpo.batimento}}</span>
                                        <span class="text-left col-md-2" style="white-space: pre-wrap;">
                                            Temperatura: {{item.dadosCorpo.temperatura}}</span>
                                    </div>
                                </mat-expansion-panel>


                            </mat-accordion>
                        </div>

                        <div style="background-color: white; margin-top: 10px;"
                            *ngIf="item.anonimo != '' && item.anonimo != null && historico">
                            <mat-accordion>
                                <mat-expansion-panel hideToggle (opened)="panelOpenState = true"
                                    (closed)="panelOpenState = false">
                                    <mat-expansion-panel-header style="height: 30px !important;">
                                        <mat-panel-title>
                                            <img src="assets/build/img/incognito.svg" style="width:8%">
                                            <span class="ml-2">{{ 'TELASTREAMING.ANONIMO' | translate }}</span>
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <div class="text-left" style="border: solid 1px black; padding: 15px;">
                                        <span class="text-left" style="white-space: pre-wrap;">
                                            {{item.anonimo}}</span>

                                    </div>
                                </mat-expansion-panel>


                            </mat-accordion>
                        </div>

                        <div style="background-color: white; margin-top: 10px;"
                            *ngIf="dadosHistorico[i].anexo.length > 0 && historico">
                            <mat-accordion>
                                <mat-expansion-panel hideToggle (opened)="panelOpenState = true "
                                    (closed)="panelOpenState = false">
                                    <mat-expansion-panel-header style="height: 30px !important;">
                                        <mat-panel-title>
                                            <i class="fas fa-paperclip"></i>
                                            <span class="ml-2">{{ 'TELASTREAMING.ANEXOS' | translate }}</span>
                                        </mat-panel-title>
                                    </mat-expansion-panel-header>
                                    <div class="manha">
                                        <div class="d-flex justify-content-start"
                                            *ngFor="let anex of dadosHistorico[i].anexo ">
                                            <div class="balao col-12 " id="colapsoAnexo+{{i}}"
                                                *ngIf="tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& anex.flgVisualizacaoUsuario==true">
                                                <i (click)="BaixarArquivo(anex.chave,anex.anexo,anex.tipoArquivo)"
                                                    style="    vertical-align: -webkit-baseline-middle;cursor: pointer; font-size: 20px"
                                                    class="fa fa-download text-left icone"
                                                    title="Download arquivo"><span class="texto text-left"
                                                        title="{{anex.anexo}} "
                                                        style="font-size: 12px; vertical-align: middle">
                                                        {{anex.anexo }}</span></i>

                                                <div class="col-12" style="padding:unset">
                                                    <small
                                                        class="msg_timeAnonimo ">{{anex.dtaCadastro |  date: 'dd/MM/yyyy HH:mm'}}</small>

                                                    <small class="msg_nome "
                                                        title="{{anex.usuario}}">{{anex.usuario }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </mat-expansion-panel>
                            </mat-accordion>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="anonimo">
                <h3>{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</h3>
                <mat-form-field class="col-md-12 col-sm-12 col-xs-12 coluna-maior " appearance="outline"
                    floatLabel="always">
                    <!-- <mat-label>{{ 'TELAAGENDA.OBSERVACAO' | translate }}</mat-label> -->
                    <textarea matInput style=" height: 28vh;" id="txtAnonimo" name="Observação"
                        (keyup.enter)="addParagrafoAnonimo()" [(ngModel)]="DesAnonimo"></textarea>
                </mat-form-field>
            </div>
            <div *ngIf="consulta">
                <h3>{{ 'TELASTREAMING.CONSULTAS' | translate }}</h3>
                <mat-form-field class="col-md-12 col-sm-12 col-xs-12  coluna-maior" appearance="outline"
                    floatLabel="always">
                    <!-- <mat-label>{{ 'TELAAGENDA.OBSERVACAO' | translate }}</mat-label> -->
                    <textarea matInput style=" height: 28vh;" id="txtConsulta" name="Observação"
                        (keyup.enter)="addParagrafo()" [(ngModel)]="DesConsulta"></textarea>
                </mat-form-field>
            </div>

        </div>
        <!-- </div> -->
        <!-- <div class="col-md-3 col-sm-8">
         <div id="meets" class="container-fluid ">
         </div> -->

        <div *ngIf="anexo" class="col-md-3 col-sm-8" style="
         max-height: 321px;
         overflow: auto;
         border: solid 1px gray;">
            <div class="carregar-arquivo h-auto d-flow-root text-center" style="border: 2px dotted #999; margin: 38px;"
                *ngIf="spinnerAnexo == true">
                <img src="assets/build/img/spinner.gif" style="width: 100%;">
            </div>


            <!-- <div class="carregar-arquivo h-auto d-flow-root text-center" *ngIf="spinnerAnexo == false"
                (dragover)="$event.preventDefault()" (drop)="fileuploadesquerdo($event)" style="border: 2px dotted #999;
                margin: 38px;"></div> -->


            <!-- (dragleave)="tiraBorada($event)" -->
            <div class="carregar-arquivo h-auto d-flow-root text-center" *ngIf="spinnerAnexo == false"
                (dragover)="$event.preventDefault()" (drop)="fileuploadesquerdo($event)"
                style="border: 2px dotted #999; margin-bottom:10px; margin-top:10px">
                <i class="fas fa-cloud-upload-alt fa-fw fa-4x" style="color: #ccc;margin-top: 20px"></i>
                <p style="font-size:12px; font-weight:500; line-height:14px">
                    {{ 'TELASTREAMING.AREAPARAIMPORTARARQUIVOS' | translate  }}
                    <br>
                    <br> {{ 'TELASTREAMING.OU' | translate }} </p>

                <label for="file3" class="btn btn-primary"
                    style="font-size:12px!important;margin-bottom: 20px; cursor: pointer;">
                    {{ 'TELASTREAMING.CARREGARARQUIVO' | translate }} </label>
                <input type="file" style="width:100%;" id="file3" (change)="SubirArquivoConsulta($event)"
                    accept=".xls,.xlsx,.pdf" />

            </div>



            <div>
                <div class="d-flex justify-content-start" *ngFor="let item of DadosanexConsulta">
                    <div class="col-md-12 col-sm-12 col-xs-12" *ngIf="item.idUsuario == usuario">
                        <div class="balao2 col-12 mb-4">
                            <i (click)="BaixarArquivo(item.chave,item.nomeArquivo,item.tipoArquivo)" style="cursor: pointer;font-size: 20px;
                                           vertical-align: bottom;" class="fa fa-download fa-fw  icone"
                                title="Download arquivo"></i><span title="{{item.nomeArquivo }}" class="texto text-left"
                                style="font-size: 12px; vertical-align: middle;">
                                {{item.nomeArquivo | truncate : 15 : "…"}}</span>
                            <span class="msg_time"
                                style=" bottom: 0px !important;  margin-left: 5%; color: white; width: 100%;">{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}
                                <mat-icon style="vertical-align: sub; cursor: pointer;margin-left: 20%;"
                                    (click)="DeleteAnexo(item.chave , item.idAnexo)">delete</mat-icon>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-12 col-sm-12 col-xs-12"
                        *ngIf="item.idUsuario != usuario && (tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& item.flgVisualizacaoUsuario==true)">
                        <div class="balao3 col-12 mb-4">
                            <i (click)="BaixarArquivo(item.chave,item.nomeArquivo,item.tipoArquivo)" style="cursor: pointer;    font-size: 20px;
                                           vertical-align: bottom;" class="fa fa-download fa-fw icone"
                                title="Download arquivo"></i><span title="{{item.nomeArquivo }}" class="texto text-left"
                                style="font-size: 12px;
                                           vertical-align: middle;">
                                {{item.nomeArquivo | truncate : 25 : "…"}}</span>
                            <span class="msg_time" style="
                                           bottom: 0px !important;
                                           margin-left: 5%;
                                           color: white;">{{item.dtacadastro |  date: 'dd/MM/yyyy HH:mm'}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="corpo" class="col-md-3 col-sm-8">
            <div>
                <div class="row text-center" style="margin-top: 2%;">
                    <div class="col-md-12 col-sm-12 col-xs-12">
                        <small>{{ 'TELASTREAMING.DADOSPACIENTE' | translate }}</small>
                    </div>
                </div>
                <div style="float: left;">
                    <div style=" margin-right: auto; margin-left: auto;">
                        <img src="assets/build/img/corpoPaciente.png"
                            style="width: 200px;margin-top: 3px;	margin-left: -60px;	margin-right: -90px;">
                    </div>
                </div>
                <div style="overflow: auto !important;">
                    <div class="col-md-12 col-sm-12 mt-3" style=" margin-left: 0; margin-right: 0;">
                        <mat-form-field class="col-md-12 col-sm-12 input-spacing">
                            <input matInput placeholder="{{ 'TELASTREAMING.PESO' | translate }}" id="CampoPesoPront"
                                name="Peso" maxlength="6" (keypress)="mascaraPeso($event)" (keyup)="mascaraPeso($event)"
                                [(ngModel)]="Dados.peso" (blur)="CalculoIMC('CampoAlturaPront','CampoPesoPront')">
                        </mat-form-field>
                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                            <input matInput placeholder="{{ 'TELASTREAMING.ALTURA' | translate }}" id="CampoAlturaPront"
                                name="Altura" maxlength="4" (keypress)="mascaraAltura($event)"
                                (keyup)="mascaraAltura($event)" [(ngModel)]="Dados.altura"
                                (blur)="CalculoIMC('CampoAlturaPront','CampoPesoPront')">
                        </mat-form-field>
                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                            <input matInput placeholder="{{ 'TELASTREAMING.IMC' | translate }}" name="IMC"
                                [(ngModel)]="Dados.IMC" disabled maxlength="5" readonly="true">
                        </mat-form-field>
                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                            <input matInput placeholder="{{ 'TELASTREAMING.PRESSAO' | translate }}" id="campoPressao"
                                name="Pressão" [(ngModel)]="Dados.pressao" maxlength="5"
                                (keypress)="mascaraPressao($event)" (keyup)="mascaraPressao($event)">
                        </mat-form-field>

                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                            <input matInput placeholder="{{ 'TELASTREAMING.BATIMENTO' | translate }}" name="Batimento"
                                [(ngModel)]="Dados.batimento" maxlength="3" (keyup)="mascaraNumeros($event)"
                                (keypress)="mascaraNumeros($event)">
                        </mat-form-field>
                        <mat-form-field class="col-md-12 col-sm-12 col-xs-12 input-spacing">
                            <input matInput placeholder="{{ 'TELASTREAMING.TEMPERATURA' | translate }}" id="campoTemp"
                                (keypress)="mascaraTemperatura($event)" (keyup)="mascaraTemperatura($event)"
                                name="Temperatura" maxlength="5" [(ngModel)]="Dados.temperatura">
                        </mat-form-field>
                    </div>
                </div>
                <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-2" style="text-align: center">
                    <button class="btn-primary " mat-raised-button style="color:white;" (click)="LimparDados()"
                        style="margin-right: 2%;">
                        <mat-icon>clear</mat-icon>
                    </button>

                    <button class="btn-primary " mat-raised-button style="color:white;"
                        (click)="SalvarDadosCorpo('CampoAlturaPront','CampoPesoPront')">
                        <mat-icon>save</mat-icon>
                    </button>
                </div>
            </div>
        </div>
    </div>


    <div class="col-12 pt-4 pad-desk">
        <div class="row col-md-12 botoes">

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab [className]="consulta ? ' btn-primary BotaoAtivo': 'btn-primary Botaoinativo'"
                        (click)="ConsultMenu2()"  contentsLink>
                        <mat-icon>assignment</mat-icon>
                    </button>
                    <br>
                    <small> {{ 'TELASTREAMING.CONSULTAS' | translate }}</small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab [className]="anonimo ? ' btn-primary BotaoAtivo': 'btn-primary Botaoinativo'"
                        (click)="AnotMenu2()"  contentsLink>
                        <mat-icon>visibility_off</mat-icon>
                    </button>
                    <br>
                    <small>{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}</small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab [className]="anexo ? ' btn-primary BotaoAtivo': 'btn-primary Botaoinativo'"
                        (click)="AnexoMenu2();">
                        <mat-icon>archive</mat-icon>
                    </button>
                    <br>
                    <small> {{ 'TELASTREAMING.ANEXOS' | translate }} </small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab [className]="corpo ? ' btn-primary  BotaoAtivo': 'btn-primary Botaoinativo'"
                        (click)="DadosMenu2()">

                        <mat-icon>account_circle</mat-icon>
                    </button>
                    <br>
                    <small> {{ 'TELASTREAMING.DADOSPESSOAIS' | translate }} </small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab class="btn-primary" (click)="AtestadoModal()">
                        <mat-icon>assignment</mat-icon>
                    </button>
                    <br>
                    <small>{{ 'TELASTREAMING.ATESTADO' | translate }}</small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab class="btn-primary" (click)="ReceituarioModal()">
                        <mat-icon>file_copy</mat-icon>
                    </button>
                    <br>
                    <small>{{ 'TELASTREAMING.RECEITUARIO' | translate }}</small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab class="btn-primary" (click)="DeclaracaoModal()">
                        <mat-icon>assignment_ind</mat-icon>
                    </button>
                    <br>
                    <small>{{ 'TELASTREAMING.DECALARACAO' | translate }}</small>
                </div>
            </div>

            <div style="width: 10px;"></div>

            <!-- <div class="col-md-1 col-3">
                <div class="text-center panel-selector btns-prontuario">
                    <button mat-mini-fab class="btn-primary botoes-pront" (click)="ExamesPaciente()" >
                        <mat-icon>assignment</mat-icon>
                    </button>
                    <small>Exames</small>
                </div>
            </div> -->

            <div style="width: 10px;"></div>

            <div class="col-md-1 col-3 colunasem">

                <div class="finalizar text-center btns-prontuario" *ngIf="Sairsala != true">
                    <button mat-mini-fab class="btn-primary"
                        (click)="ngxSmartModalService.getModal('FinalizarChamada').open()" style="

                            background-color: darkgreen !important;">
                        <mat-icon>check</mat-icon>
                    </button>
                    <br>
                    <small style="font-size: 0.8rem;font-weight: 700;">
                        Concluir Consulta
                    </small>
                </div>

                <div class="finalizar text-center" *ngIf="Sairsala == true">
                    <button mat-mini-fab class="btn-primary" (click)="BtnSair()" style="

                            background-color: darkgreen !important;">
                        <mat-icon class="menu_icon">exit_to_app</mat-icon>
                    </button>
                    <br>
                    <small style="font-size: 0.7rem;font-weight: 700;">
                        Sair da Consulta
                    </small>
                </div>


            </div>
        </div>
    </div>
</div>


<!-- TELA 3  =============================================================================================\/-->


<div class="drawer-height" id="Modo3" *ngIf="Modo3">

    <div class="Menu_esquerdoI" *ngIf="MedicoPermissao">

        <ul style="margin-bottom: 0;">
            <mat-nav-list style="padding-top:0px;">

                <li class="dropdown no-mobile">
                    <button mat-mini-fab class="btn-primary menu_panel" data-toggle="dropdown" href="#">
                        <mat-icon class="menu_icon color-white" style="color: white!important;">layers
                        </mat-icon>
                    </button>
                    <ul class="dropdown-menu painel-config streaming-drop dropdown-config">
                        <li class="title-layout">
                            {{ 'TELASTREAMING.LAYOUTALTERNATIVO' | translate }}
                        </li>
                        <li class="text-center">
                            <small class="small-droptext">
                                {{ 'TELASTREAMING.ESCOLHAUMLAYOUT' | translate }}
                            </small>

                        </li>
                        <li>
                            <div class="col-md-12 col-sm-12 row " style="justify-content: center;">
                                <div class="" (click)="MudarModoExecucao('Modo1')">
                                    <img class="img-layout " src="assets/build/img/Layout-Padrao.svg">
                                </div>

                                <div class="" (click)="MudarModoExecucao('')">
                                    <img class="img-layout layout-active" src="assets/build/img/Layout-Extenso.svg">
                                </div>
                            </div>
                        </li>
                    </ul>
                </li>


                <button mat-mini-fab matTooltip=" {{ 'TELASTREAMING.DESLIGARCONFERENCIA' | translate }} "
                    class="menu_panel btn-primary" (click)="ngxSmartModalService.getModal('FinalizarChamada').open()">
                    <mat-icon class="menu_icon">power_settings_new</mat-icon>
                </button>

            </mat-nav-list>
        </ul>

    </div>



    <div class="row">

        <div class="col-md-4 col-sm-12">
            <mat-card appearance="outlined" class="webcam-container mt-10">

                <mat-tab-group style="padding: 0!important">
                    <mat-tab label="{{ 'TELASTREAMING.ANOTACOES' | translate }}">
                        <mat-form-field class="col-md-12 col-sm-12">
                            <textarea class="textarea-layout2"
                                placeholder="{{ 'TELASTREAMING.ANOTACOES' | translate }}"></textarea>
                        </mat-form-field>
                    </mat-tab>

                    <mat-tab label="{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}"
                        style="color: white; margin-top: 5px; margin-left: 10px; min-height: 150px; ">
                        <mat-form-field class="col-md-12 col-sm-12">
                            <textarea class="background-anom background-anom2"
                                style="max-height:150px; height: 140px; min-height:140px;"
                                placeholder="{{ 'TELASTREAMING.ANOTACOESANONIMAS' | translate }}"></textarea>
                        </mat-form-field>
                    </mat-tab>
                </mat-tab-group>

            </mat-card>

        </div>

        <div class="col-md-4 col-sm-12 webcam-container " style="margin-top: 10px">
            <mat-card appearance="outlined" class="hist-content">

                <div class="panel-menu mt-10 p-b-5 col-md-12 col-sm-12 col-xs-12"
                    [className]="closemenu ? 'panel-menu mt-10 p-b-5 col-md-12 col-sm-12 col-xs-12': 'd-none' ">
                    <div class="row">

                        <div class="text-center panel-selector">
                            <button mat-mini-fab class="btn-primary" (click)="LocalizacaoMenu()">
                                <mat-icon>my_location</mat-icon>
                            </button>
                            <br>
                            <small>{{ 'TELASTREAMING.LOCALIZACAO' | translate }}</small>
                        </div>

                        <div class="text-center panel-selector">
                            <button mat-mini-fab class="btn-primary" (click)="AtestadoMenu()">
                                <mat-icon>assignment</mat-icon>
                            </button>
                            <br>
                            <small>{{ 'TELASTREAMING.ATESTADO' | translate }}</small>
                        </div>

                        <div class="text-center panel-selector">
                            <button mat-mini-fab class="btn-primary" (click)="ReceituarioMenu()">
                                <mat-icon>file_copy</mat-icon>
                            </button>
                            <br>
                            <small>{{ 'TELASTREAMING.RECEITUARIO' | translate }}</small>
                        </div>

                        <div class="text-center panel-selector">
                            <button mat-mini-fab class="btn-primary" (click)="DeclaracaoMenu()">
                                <mat-icon>assignment_ind</mat-icon>
                            </button>
                            <br>
                            <small>{{ 'TELASTREAMING.DECALARACAO' | translate }}</small>
                        </div>

                        <div class="text-center panel-selector">
                            <button mat-mini-fab class="btn-primary">
                                <mat-icon>medical_services</mat-icon>
                            </button>
                            <br>
                            <small>Análise</small>
                        </div>

                        <div class="text-center panel-selector">
                            <button mat-mini-fab class="btn-primary menu_panel"
                                *ngIf=" MedicoPermissao && dadosHistorico.length > 0" (click)="historicoMenu()">
                                <mat-icon>history</mat-icon>
                            </button>
                            <br>
                            <small>{{ 'TELASTREAMING.HISTORICO' | translate }}</small>
                        </div>



                    </div>

                </div>
                <div class="col-md-12 col-sm-12 mt-10"
                    style="margin-left: auto; margin-right: auto; text-align: center; margin-bottom: 10px;">
                    <button mat-mini-fab class="close-button" (click)="closem()"
                        [className]="closemenu ? 'close-button btn-primary mat-mini-fab' : 'close-button close-buttonrotate close-button mat-mini-fab'">
                        <mat-icon>keyboard_arrow_up</mat-icon>
                    </button>
                </div>
                <mat-tab-group>

                    <mat-tab label="{{ 'TELASTREAMING.LOCALIZACAO' | translate }}"
                        *ngIf="MedicoPermissao && !Consultas && !Anotacoes && !anexos && !DadosP && Localizacao && !Atestado && !Receituario && !Declaracao  && !Historicomenu">

                        <div class="modal-h text-center">
                            <h2 style="font-size: 20px;">{{dadosConsulta.paciente}}</h2>
                            <mat-divider></mat-divider>

                        </div>

                        <div class="col-md-12 col-sm-12 col-xs-12 text-center p-b-20">

                            <label class="usuario-principal">{{ 'TELASTREAMING.PACIENTE' | translate }}</label>
                            <br>
                            <mat-icon class="contato">mail_outline</mat-icon> <small class="contato">
                                JorgeIslintan43&#64;outlook.com</small>

                            <br>
                        </div>

                        <div class="history-loc">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3601.3662027702208!2d-49.22476598498456!3d-25.492830683759497!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x94dcfa9559a4d587%3A0x6e6c706e333c234c!2sRua+Rosa+Sahagoff%2C+185+-+Uberaba%2C+Curitiba+-+PR%2C+81580-140!5e0!3m2!1spt-BR!2sbr!4v1555534020282!5m2!1spt-BR!2sbr"
                                width="400" height="300" frameborder="0" style="border:0" allowfullscreen></iframe>

                        </div>
                    </mat-tab>

                    <mat-tab label="{{ 'TELASTREAMING.ATESTADO' | translate }}"
                        *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !Localizacao && Atestado && !Receituario && !Declaracao  && !Historicomenu">
                        <div class="dashed-content">
                            <div class="col-md-12 col-sm-12 ">
                                <span class="custom-span"
                                    style="font-size: 12px;">{{ 'TELADOCUMENTACAO.ATIVIDADESNORMAIS' | translate }}
                                </span><br>
                                <input matInput maxlength="3" name="dias" [(ngModel)]="DiasAtestado"
                                    (keyup)="mascaraNumeros($event)" style="width: 30px; border: 1px solid;">

                                <span class="custom-span" style="font-size: 12px;">
                                    {{ 'TELADOCUMENTACAO.DIA' | translate }}
                                </span><br>
                                <br>
                                <mat-checkbox [(ngModel)]="DesCID" checked class="custom-span" style="font-size: 12px;">
                                    {{ 'TELADOCUMENTACAO.NAOIMPRIMIRDESCRICAODOCID' | translate }}</mat-checkbox>
                            </div>


                            <div class="col-md-12 col-sm-12 row">
                                <mat-form-field class="col-md-5 col-sm-6">
                                    <input matInput placeholder="{{ 'TELADOCUMENTACAO.CID' | translate }}" name="CID"
                                        (change)="CarregaCID()" maxlength="6" [(ngModel)]="desCID" required
                                        [formControl]='cid' style="text-transform: uppercase;">

                                    <mat-error style="font-size: 10px;" *ngIf="cid.invalid">
                                        {{getErrorMessagecid() | translate }}
                                    </mat-error>
                                </mat-form-field>



                                <mat-form-field class="col-md-7 col-sm-6">
                                    <input matInput placeholder="{{ 'TELADOCUMENTACAO.DESCRICAODOCID' | translate }}"
                                        name="CID" maxlength="6" [(ngModel)]="CID" style="color: black;" disabled>
                                </mat-form-field>
                            </div>
                        </div>
                        <div class="col-12 row-button text-center mt-30">
                            <div class="col-md-12 col-sm-12">
                                <button mat-flat-button (click)="GerarAtestado()" class=" btn-primary align-btn">
                                    {{ 'TELASTREAMING.GERAR' | translate }}
                                </button>

                                <button mat-flat-button (click)="LimparAtestado()" class="btn-primary align-btn">
                                    {{ 'TELASTREAMING.LIMPAR' | translate }}
                                </button>
                                <!--
                                <button mat-flat-button (click)="GerarAtestado('Enviar')" class="btn-primary align-btn">
                                    {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                </button> -->
                            </div>
                        </div>
                    </mat-tab>

                    <mat-tab label="{{ 'TELASTREAMING.RECEITUARIO' | translate }}"
                        *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !Localizacao && !Atestado && Receituario && !Declaracao  && !Historicomenu">

                        <mat-form-field class="col-md-12 col-sm-6" style="margin-top: 5px;">
                            <input matInput placeholder="{{ 'TELADOCUMENTACAO.BUSQUEOMEDICAMENTO' | translate }}"
                                name="CPf" [(ngModel)]="nomeRemedio" (change)="filtroRemedio()"
                                (keyup.enter)="filtroRemedio()">
                        </mat-form-field>
                        <div class="col-md-12 col-sm-12" style="margin-top: 15px;">
                            <mat-slide-toggle class="botao" style="margin-top: 4px;" [(ngModel)]='checkmedicamentosPro'
                                (change)="filtroRemedio()" aria-checked="true">
                                {{ 'TELADOCUMENTACAO.CHECKMEDICAMENTOS' | translate }}
                            </mat-slide-toggle>
                        </div>

                        <mat-selection-list color="primary" class="Medicine-content">
                            <div class="col-md-12 col-sm-12 col-xs-12 " *ngFor="let item of DadosRaceitaRemedios">

                                <div (click)="addMedicamentos(item.produto ,item.apresentacao)"
                                    style="cursor: pointer;">
                                    <label title="{{item.produto}}"><b>{{item.produto| truncate : 22 : "…"}}</b>
                                    </label><br>
                                    <label title="{{item.apresentacao}}">
                                        {{item.apresentacao | truncate : 22 : "…"}}</label>
                                </div>
                            </div>
                        </mat-selection-list>

                        <mat-form-field class="col-md-12">
                            <textarea matInput class="background-consulta tex-area-archives" name="txtMensagem"
                                id="txtMensagem" cdkTextareaAutosize #autosize="cdkTextareaAutosize" name="txtMensagem"
                                [(ngModel)]="CampoReceita" maxlength="900"></textarea>
                                <mat-label class="textarea-placeholder">Medicamentos</mat-label>
                        </mat-form-field>
                        <div class="col-12 row-button text-center">

                            <div class="col-md-12 col-sm-12">
                                <mat-checkbox [(ngModel)]="flgEndereco" checked style="font-size: 12px;">
                                    {{ 'TELADOCUMENTACAO.IMPRIMIRENDERECODOPACIENTE' | translate }}
                                </mat-checkbox>
                            </div>

                            <div class="col-md-12 col-sm-12">
                                <button mat-flat-button (click)="GerarReceita('')" class="align-btn btn-primary">
                                    {{ 'TELASTREAMING.GERAR' | translate }}
                                </button>

                                <button mat-flat-button (click)="LimparReceita()" class="align-btn btn-primary">
                                    {{ 'TELASTREAMING.LIMPAR' | translate }}
                                </button>
                                <!--
                                <button mat-flat-button (click)="GerarReceita('Enviar')" class="align-btn btn-primary">
                                    {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                </button> -->
                            </div>
                        </div>
                    </mat-tab>
                    <mat-tab label="{{ 'TELASTREAMING.CECLARACAO' | translate }}" class="color-anon"
                        *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !Localizacao && !Atestado && !Receituario && Declaracao && !Historicomenu">

                        <div class="col-md-12">
                            <span class="custom-span"
                                style="font-size: 12px;">{{ 'TELADOCUMENTACAO.COMPARECEUNOPERIUDO' | translate }}
                            </span><br>
                        </div>

                        <mat-form-field class="col-md-12 col-sm-12">
                            <input matInput maxlength="50" name="periudo"
                                placeholder="{{ 'TELADOCUMENTACAO.PERIUDO' | translate }}" required
                                [formControl]='periudo' [(ngModel)]="periudoDeclaracao">
                            <mat-error *ngIf="periudo.invalid">{{getErrorMessageperiudo() | translate }}
                            </mat-error>
                        </mat-form-field>
                        <div class="col-12 row-button text-center">
                            <div class="col-md-12 col-sm-12">
                                <button mat-flat-button (click)="GerarDeclaracao()" class="align-btn btn-primary">
                                    {{ 'TELASTREAMING.GERAR' | translate }}
                                </button>

                                <button mat-flat-button (click)="LimparDeclaracao()" class="align-btn btn-primary">
                                    {{ 'TELASTREAMING.LIMPAR' | translate }}
                                </button>

                                <!-- <button mat-flat-button (click)="GerarDeclaracao('Enviar')"
                                    class="align-btn btn-primary">
                                    {{ 'TELASTREAMING.GERAREENVIAR' | translate }}
                                </button> -->
                            </div>

                        </div>

                    </mat-tab>

                    <mat-tab label="{{ 'TELASTREAMING.HISTORICO' | translate }}"
                        *ngIf="MedicoPermissao && !Anotacoes && !Consultas && !anexos && !DadosP && !Localizacao && !Atestado && !Receituario && !Declaracao && Historicomenu">
                        <div class="panel_historico columnify row" contents >
                            <div class="col-md-5 col-sm-4"
                                style="border: 1px solid #ddd; height: 90vh!important; overflow-y: auto; padding:0!important">
                                <div class="text-center ">

                                    <div tmDarkenOnHover *ngFor="let item of dadosHistorico;let i = index;"
                                        contentsTable class="row text-center d-flex justify-content-center"
                                        style="cursor: pointer !important;">
                                        <span tmDarkenOnHover (click)="historico = true" contentsLink position
                                            style=" padding: 5px;  display: block; font-size: 12px; color: #1265b9 !important;">
                                            {{item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}
                                        </span>
                                        <mat-icon aria-label="Anonimo"
                                            style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;"
                                            class="" *ngIf="item.anonimo != null">visibility_off
                                        </mat-icon>
                                        <mat-icon aria-label="Anexo"
                                            style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;"
                                            class="" *ngIf="item.anexo.length >0">archive
                                        </mat-icon>
                                        <mat-icon aria-label="Dados Corpo"
                                            style=" padding: 5px;  display: block; font-size: 17px; color: #1265b9 !important;"
                                            class="" *ngIf="item.dadosCorpo!=null">accessibility_new
                                        </mat-icon>

                                        <hr class="sep-2" />
                                    </div>

                                </div>
                            </div>


                            <div class="col-md-7 col-sm-8"
                                style="overflow-y: auto !important;height: -webkit-fill-available !important;overflow-x: hidden; height: 90vh!important;"
                                #teste>

                                <div class="col-md-12 col-sm-12 col-xs-12 "
                                    *ngFor="let item of dadosHistorico;let i = index">
                                    <div class="justify-content-center  mt-30">

                                        <span class="d-flex input-group-text attach_btn text-left"
                                            id="{{ item.posicao }}" #destinationRef>
                                            {{ 'TELASTREAMING.CONSULTADIA' | translate }}
                                            {{item.dtaConsulta  | date: 'dd/MM/yyyy HH:mm' }} <br>
                                            {{ 'TELASTREAMING.MEDICO' | translate }} {{item.medico}}


                                            <span class="text-left" style="white-space: normal;"
                                                *ngIf="item.obsConsulta != null && item.obsConsulta != '' ">
                                                <hr class="pontilhado"
                                                    *ngIf="item.obsConsulta != null && item.obsConsulta != '' ">
                                                {{ 'TELASTREAMING.PRONTUARIO' | translate }}
                                                {{item.obsConsulta}}</span>


                                        </span>


                                    </div>

                                    <mat-accordion *ngIf="item.anonimo != '' && item.anonimo != null ">
                                        <mat-expansion-panel (opened)="panelOpenState = true"
                                            (closed)="panelOpenState = false">
                                            <mat-expansion-panel-header class="bodered-collapse">
                                                <mat-panel-title>
                                                    <img src="assets/build/img/incognito.svg" aria-label="Anonimo"
                                                        style="width:30%">
                                                    <span
                                                        class="ml-2 mt-10">{{ 'TELASTREAMING.ANONIMO' | translate }}</span>
                                                </mat-panel-title>
                                            </mat-expansion-panel-header>
                                            <div class="col-md-12 col-sm-12">
                                                <p class="text-justify">{{item.anonimo}}</p>
                                            </div>
                                        </mat-expansion-panel>
                                    </mat-accordion>

                                    <mat-accordion *ngIf="dadosHistorico[i].anexo.length > 0 ">
                                        <mat-expansion-panel (opened)="panelOpenState = true "
                                            (closed)="panelOpenState = false">
                                            <mat-expansion-panel-header class="bodered-collapse">
                                                <mat-panel-title>
                                                    <i class="fas fa-paperclip"></i>
                                                    <span class="ml-2">{{ 'TELASTREAMING.ANEXOS' | translate }}</span>
                                                </mat-panel-title>
                                            </mat-expansion-panel-header>
                                            <div class="manha">
                                                <div class="d-flex justify-content-start"
                                                    *ngFor="let anex of dadosHistorico[i].anexo ">
                                                    <div class="balao col-12 " id="colapsoAnexo+{{i}}"
                                                        *ngIf="tipoUsuario!='Paciente'|| tipoUsuario == 'Paciente'&& anex.flgVisualizacaoUsuario==true">

                                                        <i (click)="BaixarArquivo(anex.chave,anex.anexo,anex.tipoArquivo)"
                                                            style="vertical-align: -webkit-baseline-middle;cursor: pointer; font-size: 20px"
                                                            class="fa fa-download text-left icone"
                                                            title="Download arquivo"><span class="texto text-left"
                                                                title="{{anex.anexo}} "
                                                                style="font-size: 12px; vertical-align: middle">
                                                                {{anex.anexo}}</span></i>

                                                        <div class="col-12" style="padding:unset">
                                                            <small
                                                                class="msg_timeAnonimo ">{{anex.dtaCadastro |  date: 'dd/MM/yyyy HH:mm'}}</small>

                                                            <small class="msg_nome "
                                                                title="{{anex.usuario}}">{{anex.usuario}}</small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </mat-expansion-panel>
                                    </mat-accordion>

                                    <div>

                                        <hr style="border-top: 1px solid #DDD;">

                                    </div>
                                </div>

                            </div>

                        </div>

                    </mat-tab>
                </mat-tab-group>

            </mat-card>
        </div>

        <div class="col-md-4 col-sm-12 content-space mt-10">
            <mat-card appearance="outlined" class="row card-bottom">
                <div class="col-md-6 col-sm-12 col-xs-6" style="float:left; margin-left: -25px;">
                    <div style="margin-right: auto; margin-left:auto; ">
                        <img src="assets/build/img/corpoPaciente.png" class="corpo-paciente">
                    </div>
                </div>
                <div class="col-md-6 col-sm-6 col-xs-6 content-use" style="overflow-y: hidden;	overflow-x: hidden;">

                    <mat-form-field class="example-full-width">
                        <input matInput placeholder="{{ 'TELASTREAMING.PESO' | translate }}">
                    </mat-form-field>

                    <mat-form-field class="example-full-width">
                        <input matInput placeholder="{{ 'TELASTREAMING.ALTURA' | translate }}">
                    </mat-form-field>

                    <mat-form-field class="example-full-width">
                        <input matInput placeholder="{{ 'TELASTREAMING.IMC' | translate }}">
                    </mat-form-field>

                    <mat-form-field class="example-full-width">
                        <input matInput placeholder="{{ 'TELASTREAMING.PRESSAO' | translate }}">
                    </mat-form-field>

                    <mat-form-field class="example-full-width">
                        <input matInput placeholder="{{ 'TELASTREAMING.BATIMENTO' | translate }}">
                    </mat-form-field>
                    <mat-form-field class="example-full-width">
                        <input matInput placeholder="{{ 'TELASTREAMING.TEMPERATURAC' | translate }}">
                    </mat-form-field>
                </div>

            </mat-card>
        </div>

        <div class="col-md-4 col-sm-12 mt-10">
            <mat-card appearance="outlined" class="card-bottom">

                <div class="carregar-arquivo h-auto d-flow-root text-center" style="border: 2px dotted #999;
                            margin: 38px;">
                    <i class="fas fa-cloud-upload-alt fa-fw fa-4x" style="color: #ccc;margin-top: 20px"></i>
                    <p style="font-size:12px; font-weight:500; line-height:14px">
                        {{ 'TELASTREAMING.IMPORTARNOVOSARQUIVOS' | translate }}
                        <br>
                        <br> {{ 'TELASTREAMING.OU' | translate }} </p>

                    <label for="file" class="btn btn-primary" style="font-size:12px!important;margin-bottom: 20px;">
                        {{ 'TELASTREAMING.CARREGARARQUIVO' | translate }} </label>
                    <input type="file" style="width:100%;" id="file" accept=".xls,.xlsx,.pdf" />

                </div>

            </mat-card>
        </div>

    </div>

</div>



<ngx-smart-modal #InforUsuario identifier="InforUsuario"
    customClass="nsm-centered medium-modal modal form-modal-Cliente emailmodal" style="padding: 0 !important;">
    <!-- <div class="col-md-12 background-Iniciar" style="display: flex; height: 100px; width: 100% !important">
        <div class="modal-info" style="padding-left: 30px; align-self: center;">
            <b> {{ 'TELAAGENDA.DADOS' | translate }} {{DadosInformUsuario.tipoUsuario}} </b><br>
        </div>
        <mat-icon style="color: white;font-size: 88px;margin-left: 35%;"> perm_identity</mat-icon>
    </div> -->
    <div class="col-md-12 background-Iniciar"
        style="display: flex; height: 100px; width: 100% !important;padding-bottom: 15px; padding-top: 15px;">

        <div class="col-6" style=" align-self: flex-end;">
            <div class="" style="color: #666;">
                <b> Informações do</b><br>
                <b> {{DadosInformUsuario.tipoUsuario}}</b>
            </div>
        </div>
        <div class="col-6" style="align-self: center;">
            <mat-icon style="color: white; font-size: 88px; margin-left: 30%;">
                perm_identity</mat-icon>
        </div>
    </div>


    <div class="modal-info">
        <div class="col-md-12 col-sm-12 row mt-3" style=" margin-left: 0; margin-right: 0;">
            <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                <input matInput placeholder="{{ 'TELAAGENDA.NOME' | translate }}" disabled name="Nome"
                    [(ngModel)]="DadosInformUsuario.nome" style="color: black;">
            </mat-form-field>
            <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                <input matInput placeholder="{{ 'TELAAGENDA.CPF' | translate }}" disabled name="CPF"
                    mask="000.000.000-00" [(ngModel)]="DadosInformUsuario.cpf" style="color: black;">
            </mat-form-field>
            <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                <input matInput placeholder="{{ 'TELAAGENDA.EMAIL' | translate }}" type="email" disabled name="Email"
                    [(ngModel)]="DadosInformUsuario.email" style="color: black;">
            </mat-form-field>
            <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                <input matInput placeholder="{{ 'TELAAGENDA.CELULAR' | translate }}" disabled name="Celular"
                    mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.celular" style="color: black;">
            </mat-form-field>
            <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                <input matInput placeholder="{{ 'TELAAGENDA.TELEFONE' | translate }}" disabled name="Telefone"
                    mask="(00) 00000-0000" [(ngModel)]="DadosInformUsuario.tel" style="color: black;">
            </mat-form-field>
            <mat-form-field class="col-md-6 col-sm-12 input-spacing">
                <input matInput placeholder="{{ 'TELAAGENDA.TELEFONECOM' | translate }}" disabled name="TelComerciar"
                    mask="(00) 00000-0000" style="
              color: black;" [(ngModel)]="DadosInformUsuario.telComer">
            </mat-form-field>
            <div class=" col-md-12 col-sm-12 col-xs-12 col-lg-12 button_custom mt-4" align="right">
                <button class="btn-primary " mat-raised-button style="color:white;" (click)="InforUsuario.close()"
                    style="margin-right: 2%;">
                    <mat-icon>clear</mat-icon> {{ 'TELAAGENDA.SAIR' | translate }}
                </button>
            </div>
        </div>
    </div>

</ngx-smart-modal>

<ngx-smart-modal #PDF identifier="PDF" customClass="nsm-centered medium-modal emailmodal" style="width: 100%;
    padding: 10px !important;">

    <pdf-viewer [src]="src" [original-size]="false"></pdf-viewer>


    <mat-divider></mat-divider>

    <div class="text-center" style="
    margin-bottom: 10px;">
        <button mat-raised-button class="input-align btn-primary text-center" style="margin-top: 10px;"
            (click)="BaixarPdfModal()">
            <mat-icon style="color:white;">get_app</mat-icon> <a class="ad-pp ">Baixar Arquivo</a>
        </button>
    </div>

</ngx-smart-modal>

<ngx-smart-modal #PermissaoAnexoVisualizacao identifier="PermissaoAnexoVisualizacao" [dismissable]="false"
    customClass="nsm-centered medium-modal modal form-modal-Cliente" style="padding: 0 !important;">
    <div class="col-12 background-Iniciar no-mobile" style="display: flex; height: 100px; width: 100% !important">
        <div class="col-md-6" style="text-align: center; margin-top: 13%; margin-left: -7%;">
            <h4>Upload de Arquivo</h4>
        </div>
        <div class="modal-info col-6">
            <i class="fas fa-cloud-upload-alt fa-fw fa-3x" style="color: #fff;"></i>
        </div>
    </div>
    <div class="col-12 no-desktop" style="display: flex; width: 100% !important">
        <div class="col-md-12" style="text-align: center; margin-top: 5%;">
            <h4>Upload de Arquivo</h4>
        </div>
    </div>
    <div class="modal-info">
        <h3>Permitir que o Paciente veja esse anexo?</h3>
        <br><br>
        <div class="row">
            <div class="col-6">
                <button class="btn-primary button_custom" mat-raised-button style="color:white;float: right;"
                    (click)="PermissaoAnexoVisu(true)">
                    SIM
                </button>
            </div>
            <div class="col-6">
                <button class="btn-primary button_custom" mat-raised-button style="color:white;float: left;"
                    (click)="PermissaoAnexoVisu(false)">
                    NÃO
                </button>
            </div>
        </div>
    </div>
</ngx-smart-modal>
