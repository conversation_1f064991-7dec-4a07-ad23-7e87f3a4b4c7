import { Component, OnInit, ViewChild } from '@angular/core';
// import { NgxSpinnerService } from 'ngx-spinner';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { ConvenioService } from '../service/convenio.service';
import { CidadeService } from '../service/cidade.service';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { Convenio } from '../model/convenio';

import { Contato } from '../model/contato';
import { Endereco } from '../model/endereco';
import { TranslateModule } from '@ngx-translate/core';
import { MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition as MatSnackBarVerticalPosition } from '@angular/material/snack-bar';
type CalendarPeriod = 'day' | 'week' | 'month';
import { ImageCropperComponent, ImageCroppedEvent, ImageTransform, ImageCropperModule } from 'ngx-image-cropper';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ValidadoreseMascaras } from '../Util/validadores';
import { LocalStorageService } from '../service/LocalStorageService';
import { UfClass } from '../Util/UFClass';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from '../alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';


@Component({
    selector: 'app-convenio',
    templateUrl: './convenio.component.html',
    styleUrls: ['./convenio.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      NgSelectModule,
      NgxSmartModalModule,
      MatDivider,
      ImageCropperModule

    ]
})
export class ConvenioComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private convenioService: ConvenioService,
    private cidadeService: CidadeService,
    // public snackBar: MatSnackBar,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private localStorageService: LocalStorageService,
    private snackBarAlert: AlertComponent,
  ) { }


  @ViewChild(ImageCropperComponent) imageCropper?: ImageCropperComponent;
  imageChangedEvent: any = '';
  croppedImage: any = '';

  showCropper = false;
  imagemCorte: any;
  ImagemConvenio: any = "assets/build/img/logo-clinica.png";
  retornoConvenio: any;
  retornoEndereco: any;
  retornoContato: any;
  DadosConvenio: any;
  // usuario: Usuario;
  dadosCidade: any;
  dadosUF = UfClass;
  dadosUFCarregaBanco: any;
  dadosCidadeUf: any;
  showMessageError = false;
  showMessageSuccess = false;
  viewDate: Date = new Date();
  view: CalendarPeriod = 'month';
  TelComVal?: boolean;
  TelComValVasil = false;
  TelComLimpa?: boolean;
  Descricao = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  CNPJ = new FormControl('', [Validators.required, Validators.maxLength(18)])
  Telefone = new FormControl('', [Validators.required, Validators.maxLength(11)])
  actionButtonLabel: string = 'Fechar';
  salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  ErroSalvar: string = 'Erro ao salvar!';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  concordo?: boolean;
  concordomsg?: boolean;

  campoCNPJInvalido: boolean = false;
  campoCNPJVazil: boolean = false;

  ngOnInit() {
    this.DadosConvenio = [];
    var IdConvenio = this.localStorageService.idConvenio;
    this.localStorageService.clearByName("idConvenio");
    if (IdConvenio)
      this.CarregaConvenio(IdConvenio);
  }
  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }

  mascaraNum(evento: KeyboardEvent) {

    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/[^0-9]/g, "");

    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraCnpj(mascaraCNPJ:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;
    var saida = mascaraCNPJ.substring(0, 1);
    var texto = mascaraCNPJ.substring(i);
    if (texto.substring(0, 1) != saida) {
      return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
    }
    var ao_cnpj = valorEvento;
    ao_cnpj = ao_cnpj.replace(/\D/g, "");
    ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
    ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
    ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");
    return (<HTMLInputElement>evento.target).value = ao_cnpj;
  }



  geterrorMessageVALOR() {
    return this.vlr.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
      this.vlr.hasError('Nome') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
        '';
  }
  // geterrorMessageRETOR() {
  //   return this.retor.hasError('required') ? 'TELACADASTROMEDICO.ERROCAMPO' :
  //     this.retor.hasError('Nome') ? 'TELACADASTROMEDICO.ERRONAOEVALIDO' :
  //       '';
  // }
  public validarCampos() {
    this.showMessageError = false;
    this.CNPJ.markAsTouched();
    this.email.markAsTouched();
    this.vlr.markAsTouched();
    // this.retor.markAsTouched();
    this.Descricao.markAsTouched();
    this.ValidaTelefoneComercial((document.getElementById('TelefoneComercial')as HTMLInputElement)['value']);
    // this.ValidaTelefoneMovel(document.getElementById('TelefoneMovel')['value']);
    this.ValidaCNPJ(this.DadosConvenio.cNPJ)


    if (this.DadosConvenio.cNPJ == undefined || !this.DadosConvenio.cNPJ.trim())
      this.campoCNPJVazil = true;

    if (this.DadosConvenio.email == "" || this.DadosConvenio.email == undefined
      || this.DadosConvenio.descricao == "" || this.DadosConvenio.descricao == undefined
      || this.DadosConvenio.telefoneComercial == "" || this.DadosConvenio.telefoneComercial == undefined
      || this.campoCNPJInvalido == true || this.campoCNPJVazil == true) {

      if (this.TelComVal != true && this.TelComVal != false || this.TelComLimpa == false) {
        this.TelComValVasil = true;
        this.TelComLimpa = true;
      }

      this.showMessageError = true;

      document.documentElement.scrollTop = 0;
    }

  }

  ValidaTelefoneComercial(tle:any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComValVasil = true;
      this.TelComVal = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComValVasil = false;
      this.TelComVal = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComVal = true;
      this.TelComValVasil = false;
    }
    else
      this.TelComVal = false
  }

  public ValidaCNPJ(value:any) {
    this.campoCNPJInvalido = false;
    if (value != "") {
      this.campoCNPJVazil = false;
      if (value.length < 18) {
        this.campoCNPJInvalido = true;
        return
      }
      if (!this.validacao.cnpj(value)) {
        this.campoCNPJInvalido = true;
        return
      }
    }
    else
      this.campoCNPJVazil = true;
  }


  Submit() {

    this.validarCampos();
    if (this.showMessageError) {
      return;
    }

    var convenio = new Convenio();
    var endereco = new Endereco();
    var contato = new Contato();
    convenio.desConvenio = this.DadosConvenio.descricao;
    convenio.observacao = this.DadosConvenio.observacao;
    convenio.regClinica = this.DadosConvenio.regClinica;
    convenio.cnpj = this.DadosConvenio.cNPJ;
    convenio.periodoRetorno = this.DadosConvenio.retorno

    // convenio.valorConsulta = document.getElementById('valorConsulta')['value'];

    if (this.DadosConvenio.valorConsulta) {
      this.DadosConvenio.valorConsulta = this.validacao.removeMascara(this.DadosConvenio.valorConsulta);
      this.DadosConvenio.valorConsulta = this.DadosConvenio.valorConsulta.replace(/(\d{1})(\d{1,2})$/, "$1.$2");
      convenio.valorConsulta = this.DadosConvenio.valorConsulta;
    }


    contato.idUsuarioGerador = convenio.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;

    contato.email = this.DadosConvenio.email;
    contato.telefone = this.DadosConvenio.telefone;
    contato.telefoneMovel = this.DadosConvenio.telefoneMovel;
    contato.telefoneComercial = this.DadosConvenio.telefoneComercial;
    contato.site = this.DadosConvenio.website;

    endereco.rua = this.DadosConvenio.rua;
    endereco.numero = this.DadosConvenio.numero;
    endereco.complemento = this.DadosConvenio.complemento;
    endereco.idCidade = this.DadosConvenio.idCidade;
    endereco.bairro = this.DadosConvenio.bairro;
    endereco.cep = this.DadosConvenio.cep;

    if (this.retornoConvenio) {
      convenio.idConvenio = this.retornoConvenio.idConvenio;
      convenio.idEndereco = this.retornoConvenio.idEndereco;
      convenio.idContato = this.retornoConvenio.idContato;
      endereco.idEndereco = this.retornoConvenio.idEndereco;
      contato.idContato = this.retornoConvenio.idContato;
      convenio.dtaCadastro = this.retornoEndereco.dtaCadastro;
      endereco.dtaCadastro = this.retornoEndereco.dtaCadastro;
      contato.dtaCadastro = this.retornoEndereco.dtaCadastro;
    }
    else {
      endereco.dtaCadastro = new Date;
      contato.dtaCadastro = new Date;
      convenio.dtaCadastro = new Date;


    }
    convenio.contato = contato;
    convenio.endereco = endereco;
    convenio.idClinica = this.usuarioLogadoService.getIdUltimaClinica()!;

    if (this.ImagemConvenio != "assets/build/img/logo-clinica.png")
      convenio.imagem64 = this.ImagemConvenio;

    this.convenioService.salvarConvenio(convenio).subscribe((retorno) => {
      

      if (retorno != true) {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar")
        
      }
      else {
        this.snackBarAlert.sucessoSnackbar("Salvo com sucesso")
        this.LimparCampos()

      }
      this.spinner.hide();
    }, () => {
      
      this.snackBarAlert.falhaSnackbar("Falha na conexão")
      
      this.spinner.hide();
    })







  }
  public MascaraDinheiro(evento: KeyboardEvent) {
    // var v = (<HTMLInputElement>evento.target).value;
    // v = v.replace(/\D/g, "");
    // v = new String(Number(v));
    // var len = v.length;
    // if (1 == len)
    //     v = v.replace(/(\d)/, "0.0$1");
    // else if (2 == len)
    //     v = v.replace(/(\d)/, "0.$1");
    // else if (len > 2) {
    //     v = v.replace(/(\d{2})$/, '.$1');
    // }
    // return v;



    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }

    (<HTMLInputElement>evento.target).value = v
  }

  // SalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELACADASTROCLINICA.CADASTROSLAVOCOMSUCESSO').subscribe((res: string) => {
  //       ;
  //       this.snackBarAlert.sucessoSnackbar(res);
  //     });
  //   }
  // }


  // ErroSalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROCLINICA.ERROAOSALVAR').subscribe((res: string) => {
  //       ;
  //       this.snackBarAlert.falhaSnackbar(res);        
  //     });
  //   }
  // }


  LimparCampos() {
    this.DadosConvenio = [];
    this.ImagemConvenio = "assets/build/img/logo-clinica.png";
    this.CNPJ.markAsUntouched();
    this.email.markAsUntouched();

    this.TelComVal = false;
    this.TelComValVasil = false;
    this.TelComLimpa = false;
    this.Descricao.markAsUntouched();
    this.vlr.markAsUntouched();
    // this.retor.markAsUntouched();

  }


  CarregaConvenio(id:any) {
    this.convenioService.getConvenioEdit(id).subscribe((retorno) => {
      
      


      this.retornoEndereco = retorno.endereco;
      this.retornoContato = retorno.contato;
      this.retornoConvenio = retorno.convenio;

      if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
        this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
        this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.DadosConvenio.telefone = this.retornoContato.telefone;

      if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
        this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
        this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.DadosConvenio.telefoneMovel = this.retornoContato.telefoneMovel;

      if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
        this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
        this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
        this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      this.DadosConvenio.telefoneComercial = this.retornoContato.telefoneComercial;
      this.DadosConvenio.email = this.retornoContato.email;
      this.DadosConvenio.website = this.retornoContato.site;


      this.DadosConvenio.cNPJ = this.retornoConvenio.cnpj;
      this.DadosConvenio.regClinica = this.retornoConvenio.regClinica;
      this.DadosConvenio.descricao = this.retornoConvenio.desConvenio;
      this.DadosConvenio.observacao = this.retornoConvenio.observacao;

      // this.DadosConvenio.valorConsulta = this.verificaCasaDecimal(this.retornoConvenio.valorConsulta);

      if (this.retornoConvenio.valorConsulta) {
        this.retornoConvenio.valorConsulta = this.verificaCasaDecimal(this.retornoConvenio.valorConsulta)
        this.retornoConvenio.valorConsulta = this.aplicarMascaraValor(this.retornoConvenio.valorConsulta)
        this.DadosConvenio.valorConsulta = this.retornoConvenio.valorConsulta
      }

      this.DadosConvenio.retorno = this.retornoConvenio.periodoRetorno;

      this.DadosConvenio.logo = this.retornoConvenio.logo;

      if (retorno.imagem64 != null && retorno.imagem64 != "")
        this.ImagemConvenio = retorno.imagem64;

      this.DadosConvenio.rua = this.retornoEndereco.rua;
      this.DadosConvenio.numero = this.retornoEndereco.numero;
      this.DadosConvenio.complemento = this.retornoEndereco.complemento;
      this.DadosConvenio.cep = this.retornoEndereco.cep;
      this.DadosConvenio.bairro = this.retornoEndereco.bairro;
      if (this.retornoEndereco.idCidade != null) {
        this.cidadeService.getCidades().then((retornaCidade) => {

          this.dadosCidade = retornaCidade
          var sigle = this.dadosCidade.filter((c:any) => c.idCidade == this.retornoEndereco.idCidade)
          this.DadosConvenio.uf = sigle[0].siglasUf

          this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == sigle[0].siglasUf);
          this.DadosConvenio.idCidade = this.retornoEndereco.idCidade
          this.spinner.hide();
        }, () => {
          this.spinner.hide();
        })
      }
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  public mascaraText(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    this.DadosConvenio.website = v;
    // (<HTMLInputElement>evento.target).value = v
  }

  InativarUsuario() {
    this.convenioService.inativarConvenio(1, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      var Delete = retorno;
      if (Delete == true) {
        this.spinner.hide();
        this.ngxSmartModalService.getModal('excluirItem').close();
      }
      else {
        this.spinner.hide();
        
      }
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })

  }

  public CidadePorUF() {

    this.cidadeService.getCidades().then((retornaCidade) => {
      this.dadosCidade = retornaCidade;
      this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == this.DadosConvenio.uf);
      this.spinner.hide();
    }, () => {
      
      this.spinner.hide();
    })
  }




  // retor = new FormControl('', [Validators.required, Validators.maxLength(11)])
  vlr = new FormControl('', [Validators.required, Validators.maxLength(11)])

  getErrorMessageNome() {
    return this.Descricao.hasError('required') ? 'TELACONVENIO.ERROCAMPO' :
      this.Descricao.hasError('Descrição') ? 'TELACONVENIO.ERRONAOEVALIDO' :
        '';

  }
  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELACONVENIO.ERROCAMPO' :
      this.email.hasError('email') ? 'TELACONVENIO.ERRONAOEVALIDO' :
        '';

  }

  getErrorMessageCNPJ() {
    return this.CNPJ.hasError('required') ? 'TELACONVENIO.ERROCAMPO' :
      this.CNPJ.hasError('CNPJ') ? 'TELACONVENIO.ERRONAOEVALIDO' :
        '';

  }

  geterrorMessageDate() {
    return this.Telefone.hasError('required') ? 'TELACONVENIO.ERROCAMPO' :
      this.Telefone.hasError('Telefone') ? 'TELACONVENIO.ERRONAOEVALIDO' :
        '';
  }
  AlterarImagemClinica($event:any): void {
    this.ngxSmartModalService.getModal('ModalFoto').open();
    this.imageChangedEvent = $event
    // this.readThis($event.target);


  }
  impaCampoFile() {
    (document.getElementById('imageperfilClinica')as HTMLInputElement)['value'] = '';
  }

  CortarImagem() {
    this.ngxSmartModalService.getModal('ModalFoto').close()
    this.ImagemConvenio = this.imagemCorte
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCorte = event.base64;
    ;
  }
  imageLoaded() {
    this.showCropper = true;
    
  }
  cropperReady() {
    
  }
  loadImageFailed() {
    ;
  }
  transform: ImageTransform = {
    rotate: 0,
    flipH: false,
    flipV: false
  };

  rotateLeft() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! - 90 };
  }

  rotateRight() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! + 90 };
  }

  flipHorizontal() {
    this.transform = { ...this.transform, flipH: !this.transform.flipH };
  }

  flipVertical() {
    this.transform = { ...this.transform, flipV: !this.transform.flipV };
  }
  LimpaCampoFile() {
    (document.getElementById('imageperfilClinica')as HTMLInputElement)['value'] = '';
  }


  aplicarMascaraValor(v:any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor:any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

}