import { CommonModule } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { MatDialogRef as MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
    selector: 'app-modal-cabecalho',
    templateUrl: './modal-cabecalho.component.html',
    styleUrls: ['./modal-cabecalho.component.scss'],
      standalone: true,
    imports: [
      CommonModule
    ]
})
export class ModalCabecalhoComponent {

  constructor(
    private dialogRef: MatDialogRef<any>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  Fechar() {
    this.dialogRef.close();
  }

}
