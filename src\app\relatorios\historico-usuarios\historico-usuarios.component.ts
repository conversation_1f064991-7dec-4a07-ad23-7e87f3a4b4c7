import { Component, OnInit } from '@angular/core';
import { DocumentosService } from 'src/app/service/documentos.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import * as XLSX from 'xlsx';
import { MedicoService } from 'src/app/service/medico.service';
import { PacienteService } from 'src/app/service/pacientes.service';
import { AtendenteService } from 'src/app/service/atendente.service';
import { ParametrosAtividadesUsuario } from 'src/app/model/medico';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';

// Enum para resultados de validação de datas
enum DateValidationResult {
  OK = 0,
  DtInicio_Nao_Preenchida = 1,
  DtFim_Nao_Preenchida = 2,
  DtInicio_Maior_DtFim = 3,
  Diferenca_Entre_Datas_Supera_Limite_Fornecido = 4
}

// Função para converter string de data para objeto Date
function parseDateString(dateStr: string): Date | null {
  if (!dateStr) return null;
  return new Date(dateStr);
}

// Função para validar datas
function validateDates(dtInicio: Date, dtFim: Date | null, limiteMeses: number): DateValidationResult {
  if (!dtInicio) {
    return DateValidationResult.DtInicio_Nao_Preenchida;
  }

  if (!dtFim) {
    return DateValidationResult.DtFim_Nao_Preenchida;
  }

  if (dtInicio > dtFim) {
    return DateValidationResult.DtInicio_Maior_DtFim;
  }

  // Calcular diferença em meses
  const diffMonths = (dtFim.getFullYear() - dtInicio.getFullYear()) * 12 +
    (dtFim.getMonth() - dtInicio.getMonth());

  if (Math.abs(diffMonths) > limiteMeses) {
    return DateValidationResult.Diferenca_Entre_Datas_Supera_Limite_Fornecido;
  }

  return DateValidationResult.OK;
}

@Component({
  selector: 'app-historico-usuarios',
  templateUrl: './historico-usuarios.component.html',
  styleUrls: ['./historico-usuarios.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatCardModule,
    MatIcon,
    NgSelectModule,
    MatFormFieldModule,
    NgxSmartModalModule
  ]
})
export class HistoricoUsuariosComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private documentosService: DocumentosService,
    public snackBar: MatSnackBar,
    private tradutor: TranslateService,
    public ngxSmartModalService: NgxSmartModalService,
    private medicoService: MedicoService,
    private pacienteService: PacienteService,
    private atendenteService: AtendenteService,
    private usuarioLogadoService: UsuarioLogadoService,
    private snackBarAlert: AlertComponent,
  ) { }

  // Novos campos para o componente de datas
  strDtInicio: string = '';
  strDtFim: string = '';
  dtInicio: Date | null = null;
  dtFim: Date | null = null;
  flgDiferencaDatas: boolean = false;
  flgDtInicioNaoPreenchida: boolean = false;
  flgDtFimNaoPreenchida: boolean = false;
  flgDtFimMaiorQueInicio: boolean = false;

  // Campos originais (mantidos para compatibilidade)
  DtaFimVasil: boolean = false;
  DtaFimErrado: boolean = false;
  DtaInicioVasil: boolean = false;
  DtaInicioErrado: boolean = false;
  abas: any;
  DtaFim?: string;
  DtaInicio?: string;
  FlgErroDiferencaEntreDatas: boolean = false;
  readonly NumValorDiferencaEntreDatas = 6;
  dadosExcelFor: any;
  dadosExcel: any;

  cabecalhoAbas: any;
  nomeUsuarioExcel: any;
  clinicaExcel: any;
  nomeArquivoExcel: any;

  tipoUsuario?: string;
  Flgusuario: boolean = false;
  DadosUsuario = [{ user: 'Médico' }, { user: 'Atendente' }, { user: 'Paciente' }];
  UsuariosDados: any = [];
  User?: number | null;
  showMessageError = false;

  pesquisaAtividades: any = [];
  mostrarTabela: boolean = false;
  qtdRegistros = 10;
  bOcultaCarregaMais = false;

  ngOnInit() {
    // Configurar as datas iniciais (1 mês atrás até hoje)
    const dataI = new Date();
    const dataF = new Date();
    dataI.setMonth(dataI.getMonth() - 1);

    // Formatar datas para os inputs type="date" (formato yyyy-MM-dd)
    this.strDtInicio = this.formatDateForInput(dataI);
    this.strDtFim = this.formatDateForInput(dataF);

    // Inicializar as datas
    this.dtInicio = dataI;
    this.dtFim = dataF;

    // Manter compatibilidade com o código antigo
    this.DtaInicio = this.formatDateToLocal(dataI);
    this.DtaFim = this.formatDateToLocal(dataF);

    this.pesquisarRelatorio();
  }

  // Função para formatar datas para input type="date"
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Função para formatar data para formato local (dd/MM/yyyy)
  formatDateToLocal(date: Date): string {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Novas funções para lidar com as datas
  dtChangeInicio() {
    this.dtInicio = parseDateString(this.strDtInicio);
    this.flgDtInicioNaoPreenchida = !this.dtInicio;
    this.validateDates();

    // Manter compatibilidade com o código antigo
    if (this.dtInicio) {
      this.DtaInicio = this.formatDateToLocal(this.dtInicio);
      this.DtaInicioVasil = false;
      this.DtaInicioErrado = false;
    } else {
      this.DtaInicioVasil = true;
    }
  }

  dtChangeFim() {
    this.dtFim = parseDateString(this.strDtFim);
    this.flgDtFimNaoPreenchida = !this.dtFim;
    this.validateDates();

    // Manter compatibilidade com o código antigo
    if (this.dtFim) {
      this.DtaFim = this.formatDateToLocal(this.dtFim);
      this.DtaFimVasil = false;
      this.DtaFimErrado = false;
    } else {
      this.DtaFimVasil = true;
    }
  }

  validateDates() {
    // Limpar flags de erro
    this.flgDiferencaDatas = false;
    this.flgDtFimMaiorQueInicio = false;
    this.FlgErroDiferencaEntreDatas = false;

    const validationResult = validateDates(this.dtInicio!, this.dtFim, this.NumValorDiferencaEntreDatas);
    switch (validationResult) {
      case DateValidationResult.DtInicio_Nao_Preenchida:
        this.flgDtInicioNaoPreenchida = true;
        this.DtaInicioVasil = true;
        break;
      case DateValidationResult.DtFim_Nao_Preenchida:
        this.flgDtFimNaoPreenchida = true;
        this.DtaFimVasil = true;
        break;
      case DateValidationResult.DtInicio_Maior_DtFim:
        this.flgDtFimMaiorQueInicio = true;
        this.DtaFimErrado = true;
        this.DtaInicioErrado = true;
        break;
      case DateValidationResult.Diferenca_Entre_Datas_Supera_Limite_Fornecido:
        this.flgDiferencaDatas = true;
        this.FlgErroDiferencaEntreDatas = true;
        break;
      case DateValidationResult.OK:
        break;
    }
  }

  AlteraUser() {
    if (this.User)
      this.Flgusuario = false;
    else
      this.Flgusuario = true;
  }

  CarregaUsuario() {
    this.User = null;
    this.spinner.show();

    if (this.tipoUsuario == 'Médico') {
      this.medicoService.getMedicos(0, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        this.UsuariosDados = retorno;
        this.mostrarTabela = false;
        this.bOcultaCarregaMais = true;
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Erro ao Carregar médico');
        this.spinner.hide();
      });
    }
    else if (this.tipoUsuario == 'Atendente') {
      this.atendenteService.getGridAtendente(0, 1000, '', this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
        this.UsuariosDados = retorno;
        this.mostrarTabela = false;
        this.bOcultaCarregaMais = true;
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Erro ao Carregar atendente');
        this.spinner.hide();
      });
    }
    else if (this.tipoUsuario == 'Paciente') {
      this.pacienteService.GetListaPaciente(0, 1000, '').subscribe((retorno) => {
        this.UsuariosDados = retorno;
        this.mostrarTabela = false;
        this.bOcultaCarregaMais = true;
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar('Erro ao Carregar paciente');
        this.spinner.hide();
      });
    }
    else {
      this.UsuariosDados = [];
      this.mostrarTabela = false;
      this.bOcultaCarregaMais = true;
      this.spinner.hide();
    }
  }

  baixarExcel() {
    try {
      this.spinner.show();
      this.validateDates();

      if (this.flgDtInicioNaoPreenchida || this.flgDtFimNaoPreenchida) {
        this.tradutor.get('TELARELATORIOS.PREENCHACAMPOSDATA').subscribe(() => {
          this.snackBarAlert.falhaSnackbar("Falha ao baixar relatório");
        });
        this.spinner.hide();
        return;
      }

      if (this.flgDiferencaDatas) {
        this.spinner.hide();
        this.snackBarAlert.falhaSnackbar("A diferença entre as datas, não pode ser superior a " + this.NumValorDiferencaEntreDatas + " mêses");
        return;
      }

      if (this.flgDtFimMaiorQueInicio) {
        this.spinner.hide();
        this.snackBarAlert.falhaSnackbar("Data Inicio deve ser menor que Data Fim");
        return;
      }

      // Continuar com a geração do relatório
      if (!this.flgDtInicioNaoPreenchida && !this.flgDtFimNaoPreenchida && !this.flgDtFimMaiorQueInicio && !this.flgDiferencaDatas) {
        var parametrosRelatorio = new ParametrosAtividadesUsuario();
        parametrosRelatorio.DtaInicio = this.dtInicio!;
        parametrosRelatorio.DtaFim = this.dtFim!;
        parametrosRelatorio.idclinica = this.usuarioLogadoService.getIdUltimaClinica();
        parametrosRelatorio.idUser = this.User ? this.User : 0;
        parametrosRelatorio.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso();
        parametrosRelatorio.TipoUsuario = this.tipoUsuario;

        this.documentosService.baixarExcelRelatorioUsuario(parametrosRelatorio).subscribe(result => {
          if (result.length == 0) {
            document.body.scrollTop = document.documentElement.scrollTop = 0;
            this.tradutor.get('TELARELATORIOS.USUARIONAORALIZOUACOES').subscribe(() => {
              this.snackBarAlert.falhaSnackbar("nenhum resultado encontrado");
            });
            this.spinner.hide();
            return;
          }

          this.cabecalhoAbas = [];
          this.abas = [this.cabecalhoAbas];
          this.dadosExcelFor = [];
          this.dadosExcel = result;

          this.dadosExcel.forEach((element: any) => {
            var conteudoAbas = [];

            if (this.dadosExcel[0] == element)
              this.cabecalhoAbas.push("NOME");
            conteudoAbas.push(element.nomeUsuario);

            if (this.dadosExcel[0] == element)
              this.cabecalhoAbas.push("ATIVIDADE");
            conteudoAbas.push(element.desAtividade);

            if (this.dadosExcel[0] == element)
              this.cabecalhoAbas.push("DATA E HORA");
            conteudoAbas.push(new Date(element.dtaAtividade).toLocaleString());

            if (this.dadosExcel[0] == element)
              this.cabecalhoAbas.push("CLÍNICA");
            conteudoAbas.push(element.clinica);

            this.abas.push(conteudoAbas);
          });

          if (this.abas.length == 0) {
            this.spinner.hide();
            return;
          }

          var ws = XLSX.utils.aoa_to_sheet(this.abas);
          var wb = XLSX.utils.book_new();
          XLSX.utils.book_append_sheet(wb, ws, "RELÁTORIO");
          var wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

          function s2ab(s: any) {
            var buf = new ArrayBuffer(s.length);
            var view = new Uint8Array(buf);
            for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
            return buf;
          }

          let fileName = "Arquivo";

          if (!this.User) {
            if (this.tipoUsuario == 'Médico') {
              fileName = "Médicos";
            } else if (this.tipoUsuario == 'Paciente') {
              fileName = "Pacientes";
            } else if (this.tipoUsuario == 'Atendente') {
              fileName = "Atendentes";
            } else {
              fileName = this.abas[1][3];
            }
          } else {
            fileName = this.abas[1][0];
          }

          // Convertendo Blob para Base64 antes de chamar a função
          const reader = new FileReader();
          reader.readAsDataURL(new Blob([s2ab(wbout)], { type: "application/octet-stream" }));
          reader.onloadend = () => {
            const base64data = reader.result?.toString().split(',')[1]; // Pegando apenas o conteúdo Base64
            if (base64data) {
              this.downloadExcel(fileName, base64data);
            }
          };

          this.ngxSmartModalService.getModal('GeracaoEnviada').close();
          this.spinner.hide();
        }, () => {
          this.ngxSmartModalService.getModal('GeracaoEnviada').close();
          this.spinner.hide();
        });

        this.spinner.hide();
        this.ngxSmartModalService.getModal('GeracaoEnviada').open();
      }
    } catch (error) {
      this.ngxSmartModalService.getModal('GeracaoEnviada').close();
      this.spinner.hide();
    }
  }

  downloadExcel(fileName: string, data: any) {
    const source = `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${data}`;
    const link = document.createElement('a');
    link.href = source;
    link.download = `${fileName}.xlsx`;
    link.click();
  }

  pesquisarRelatorio() {
    this.bOcultaCarregaMais = true;
    try {
      this.validateDates();

      if (this.flgDtInicioNaoPreenchida || this.flgDtFimNaoPreenchida || this.flgDtFimMaiorQueInicio || this.flgDiferencaDatas) {
        return;
      }

      var parametrosRelatorio = new ParametrosAtividadesUsuario();
      parametrosRelatorio.DtaInicio = this.dtInicio!;
      parametrosRelatorio.DtaFim = this.dtFim!;
      parametrosRelatorio.inicio = 0;
      parametrosRelatorio.fim = this.qtdRegistros;
      parametrosRelatorio.idUser = this.User ? this.User : 0;
      parametrosRelatorio.idclinica = this.usuarioLogadoService.getIdUltimaClinica();
      parametrosRelatorio.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso();
      parametrosRelatorio.TipoUsuario = this.tipoUsuario;

      this.spinner.show();
      this.documentosService.pesquisarRelatorioUsuario(parametrosRelatorio).subscribe(result => {
        if (result.length == 0) {
          document.body.scrollTop = document.documentElement.scrollTop = 0;
          this.tradutor.get('TELARELATORIOS.USUARIONAORALIZOUACOES').subscribe(() => {
            this.mostrarTabela = false;
            this.bOcultaCarregaMais = true;
            this.snackBarAlert.falhaSnackbar("Nenhum Registro encontrado");
          });
          this.spinner.hide();
          return;
        } else {
          this.bOcultaCarregaMais = false;
        }

        this.pesquisaAtividades = result;
        this.mostrarTabela = true;

        if (result.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;

        this.spinner.hide();
      }, () => {
        this.spinner.hide();
      });
    } catch (error) {
      this.spinner.hide();
    }
  }

  CarregarMais() {
    this.bOcultaCarregaMais = false;
    try {
      this.validateDates();
      if (this.flgDtInicioNaoPreenchida || this.flgDtFimNaoPreenchida || this.flgDtFimMaiorQueInicio || this.flgDiferencaDatas) {
        this.bOcultaCarregaMais = true;
        return;
      }

      var parametrosRelatorio = new ParametrosAtividadesUsuario();
      parametrosRelatorio.DtaInicio = this.dtInicio!;
      parametrosRelatorio.DtaFim = this.dtFim!;
      parametrosRelatorio.inicio = this.pesquisaAtividades.length;
      parametrosRelatorio.fim = this.qtdRegistros;
      parametrosRelatorio.idUser = this.User ? this.User : 0;
      parametrosRelatorio.idclinica = this.usuarioLogadoService.getIdUltimaClinica();
      parametrosRelatorio.idUsuario = this.usuarioLogadoService.getIdUsuarioAcesso();
      parametrosRelatorio.TipoUsuario = this.tipoUsuario;

      this.documentosService.pesquisarRelatorioUsuario(parametrosRelatorio).subscribe((result: any) => {

        var dados: any = result;
        for (let index = 0; index < dados.length; index++) {
          this.pesquisaAtividades.push(dados[index]);
        }
        if (result.length < this.qtdRegistros)
          this.bOcultaCarregaMais = true;
        this.spinner.hide();

      }, () => {
        this.spinner.hide();
      })
    }

    catch (error) {

      this.spinner.hide();
    }
  }
}