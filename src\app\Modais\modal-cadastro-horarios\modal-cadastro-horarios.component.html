<div class="modal-container">
    <div class="modal-header">
        <h3 >
            {{ 'TELAAGENDA.MARCARHORARIO' | translate }}
        </h3>
    </div>

    <div class="modal-body">
        <div class="form-grid">
            <mat-form-field appearance="outline">
                <mat-label>Médico *</mat-label>
                <mat-select [(value)]="idMedico" required [disabled]="idTipoUsuarioLogado == 2"
                    (selectionChange)="carregarEspecialidadesMedico($event.value)">
                    <mat-option *ngFor="let medico of lsMedicos"
                        [value]="medico.idMedico">
                        {{ medico.nomeMedico }}
                    </mat-option>
                </mat-select>
            </mat-form-field>

            <mat-checkbox [(ngModel)]="flgTimeOff" color="primary">
                Periodo desligado
            </mat-checkbox>

            <mat-form-field appearance="outline">
                <mat-label>Data Início *</mat-label>
                <input matInput type="datetime-local" [(ngModel)]="dataInicio"
                    placeholder="Data Início" required>
            </mat-form-field>

            <mat-form-field appearance="outline" *ngIf="flgTimeOff">
                <mat-label>Data Fim *</mat-label>
                <input matInput type="datetime-local" [(ngModel)]="dataFim"
                    placeholder="Data Fim" required>
            </mat-form-field>

            <!-- Campos para quando NÃO estiver em período desligado -->
            <ng-container *ngIf="!flgTimeOff">
                <mat-form-field appearance="outline">
                    <mat-label>Paciente *</mat-label>
                    <mat-select [(value)]="idPaciente" required>
                        <mat-option *ngFor="let paciente of lsPacientes"
                            [value]="paciente.idCliente">
                            {{ paciente.nome }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                    <mat-label>Especialidade</mat-label>
                    <mat-select [(value)]="idEspecialidade">
                        <mat-option
                            *ngFor="let especialidade of lsEspecialidades"
                            [value]="especialidade.idEspecialidade">
                            {{ especialidade.desEspecialidade }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                    <mat-label>Tipo de Agendamento</mat-label>
                    <mat-select [(value)]="idTipoAgendamento">
                        <mat-option *ngFor="let tipo of lsTipoAgendamento"
                            [value]="tipo.idTipoAgendamento">
                            {{ tipo.desTipoAgendamento }}
                        </mat-option>
                    </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline">
                    <mat-label>Valor da Consulta</mat-label>
                    <input matInput type="number" [(ngModel)]="valorConsulta"
                        step="0.01">
                </mat-form-field>

                <mat-checkbox [(ngModel)]="flgRetorno" color="primary">
                    Retorno
                </mat-checkbox>

            </ng-container>
        </div>
        <div *ngIf="!flgTimeOff" style="display: flex; flex: 0;">
            <mat-radio-group [(ngModel)]="tipoConsulta" color="primary"
                required>
                <mat-radio-button *ngFor="let tipo of tiposConsulta"
                    [value]="tipo.valor">
                    {{ tipo.label }}
                </mat-radio-button>
            </mat-radio-group>
        </div>

        <mat-form-field appearance="outline" class="observacao-field">
            <mat-label>Observação</mat-label>
            <textarea matInput [(ngModel)]="observacao" rows="4"></textarea>
        </mat-form-field>
    </div>
    <div class="modal-footer">
        <button   (click)="excluirAgendamento()" *ngIf="flgEdicao">
            Excluir Agendamento
        </button>
        <button class="action-button cancel-button" (click)="fecharModal()">
            Cancelar
        </button>
        <button class="action-button confirm-button" (click)="salvarAgendamento()"
            [disabled]="(dataInicio && DataAtual) &&  (dataInicio! < DataAtual!)">
            Salvar Agendamento
        </button>    
    </div>
</div>

