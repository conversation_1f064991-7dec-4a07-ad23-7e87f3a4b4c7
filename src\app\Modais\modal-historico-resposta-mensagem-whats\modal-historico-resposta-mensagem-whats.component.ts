// src/app/modal-historico-resposta-mensagem-whats/modal-historico-resposta-mensagem-whats.component.ts

import { CommonModule } from "@angular/common";
import { Component, OnInit } from "@angular/core";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatDialogRef as MatDialogRef } from "@angular/material/dialog";
import { MatIcon } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { AlertComponent } from "src/app/alert/alert.component";
import { ModelConsultaWhats } from "src/app/model/whats";
import { ControleModaisService } from "src/app/service/controle-modais.service";
import { SpinnerService } from "src/app/service/spinner.service";
import { WhatsService } from "src/app/service/whats.service";

@Component({
    selector: 'app-modal-historico-resposta-mensagem-whats',
    templateUrl: './modal-historico-resposta-mensagem-whats.component.html',
    styleUrls: ['./modal-historico-resposta-mensagem-whats.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon,

    ]
})
export class ModalHistoricoRespostaMensagemWhatsComponent implements OnInit {

  constructor(
    private dialogRef: MatDialogRef<ModalHistoricoRespostaMensagemWhatsComponent>,
    private controleModaisService: ControleModaisService,
    private whatsService: WhatsService,
    private spinner: SpinnerService,
    private alert: AlertComponent,
  ) { }

  //#region Filtro Tipo Mensagem
  IdFiltroMensagem: number = 0;
  lsTipoMensagem = [
    { id: 0, desc: 'Todas' },
    { id: 1, desc: 'Texto livre' },
    { id: 2, desc: 'Mensagem: Confirmação de consulta' },
    { id: 3, desc: 'Mensagem: Aviso de atraso da consulta' },
  ];
  //#endregion

  //#region Filtro Status
  IdFiltroStatus: number = 0;
  lsFiltroStatusMensagem = [
    { id: 0, desc: "Todas" },
    { id: 1, desc: "Mensagens enviadas" },
    { id: 2, desc: "Mensagens não enviadas" },
    { id: 3, desc: "Mensagens respondidas" },
    { id: 4, desc: "Mensagens não respondidas" },
    { id: 5, desc: "Mensagens não aguardam resposta" },
  ];
  //#endregion

  lsIdsConsultas: number[] = [];
  lsIdsSelecionados: number[] = [];
  lsConsultas: ModelConsultaWhats[] = [];
  lsConsultasBKP: ModelConsultaWhats[] = [];

  ngOnInit(): void {
    this.lsIdsConsultas = this.controleModaisService.listaIdsConsulta;
    this.CarregaConsultas();
  }

  CarregaConsultas() {
    this.spinner.show();
    this.whatsService.CarregaConsultaWhats(this.lsIdsConsultas).subscribe((ret) => {
      this.lsConsultas = ret;
      this.lsConsultasBKP = ret;
      this.spinner.hide();
    }, (erro) => {
      this.alert.falhaSnackbar("Erro ao carregar a consulta");
      console.error("Erro", erro);
      this.spinner.hide();
    });
  }

  AdicionarItemLista(obj: ModelConsultaWhats) {
    obj.flgSelecionado = !obj.flgSelecionado;
  }

  filtroTipoMensagem() {

    let idFiltro = Number(this.IdFiltroMensagem);

    if (idFiltro == 0)
      this.lsConsultas = this.copiaLista(this.lsConsultasBKP);

    else {
      let consulta1: ModelConsultaWhats[] = [];

      this.lsConsultasBKP.forEach(element => {
        if (element.lsMensagens!.length > 0)
          consulta1.push(element);
      });

      let consulta = this.copiaLista(consulta1);

      consulta = consulta.filter((element:any) => {
        let newLista:any = [];
        element.lsMensagens.forEach((el:any) => {
          if (el.idTipoMensagem == idFiltro) {
            newLista.push(el);
          }
        });
        element.lsMensagens = newLista;
        return element.lsMensagens.length > 0;
      });


      this.lsConsultas = [];
      this.lsConsultas = consulta;
    }
  }

  filtroStatusMensagem() {

    let idFiltro = Number(this.IdFiltroStatus);

    if (idFiltro === 0)
      this.lsConsultas = this.copiaLista(this.lsConsultasBKP);

    else {
      let consulta = this.copiaLista(this.lsConsultasBKP);

      consulta = consulta.filter((element:any) => {

        if (idFiltro == 1 || idFiltro == 2) {
          switch (idFiltro) {
            case 1: // Mensagens enviadas
              return element.lsMensagens.length > 0;

            case 2: // Mensagens não enviadas
              return element.lsMensagens.length === 0;
          }
        }

        else {
          let newLista:any = [];

          element.lsMensagens.forEach((el:any) => {
            switch (idFiltro) {
              case 3: // Mensagens respondidas
                if (el.flgRespondido == true)
                  newLista.push(el);
                break;

              case 4: // Mensagens não respondidas
                if (el.flgRespondido != true)
                  newLista.push(el);
                break;

              case 5: // Mensagens não aguardam resposta
                if (el.flgAguardaResposta == false)
                  newLista.push(el);
                break;
            }
          });

          element.lsMensagens = newLista;
          return element.lsMensagens.length > 0;
        }
      });

      this.lsConsultas = [];
      this.lsConsultas = consulta;
    }
  }

  removerExcedente(str: string): string {
    let texto = "";
    const textoLimpo = str.replace(" ", "");
    if (textoLimpo.length > 41) {
      texto = str.slice(0, 40) + "...";
    } else {
      texto = str;
    }
    return texto;
  }

  fecharModal() {
    this.dialogRef.close();
  }



  copiaLista(lista:any) {
    let serializedData = JSON.stringify(lista);
    

    let deserializedData = JSON.parse(serializedData);
    

    return deserializedData;
  }

  formatDate(dateInput: string | null): string {
    if (!dateInput) {
      return '';
    }

    // Converte o valor para um objeto Date, tratando casos de string
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      return ''; // Retorna vazio se não for uma data válida
    }

    // Formata os valores para DD/MM/YYYY e HH:mm
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Meses começam do zero
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} - ${hours}:${minutes}`;
  }
}
