<div class="analysis-container">
    <!-- Modal para recorte de foto -->
    <ngx-smart-modal #ModalFoto identifier="ModalFoto" customClass="nsm-centered medium-modal">
        <div class="modal-header">
            <h2 class="modal-title">{{ 'TELACADASTROPACIENTE.FOTODEPERFIL' | translate }}</h2>
        </div>
        <div class="modal-body">
            <div class="image-edit-controls">
                <div class="edit-row">
                    <button class="btn-control" (click)="rotateLeft()">
                        <span class="material-icons">rotate_left</span>
                        <span class="control-text">{{ 'TELACADASTROPACIENTE.GIRARAESQUERDA' | translate }}</span>
                    </button>
                    <button class="btn-control" (click)="rotateRight()">
                        <span class="material-icons">rotate_right</span>
                        <span class="control-text">{{ 'TELACADASTROPACIENTE.GIRARADIREITA' | translate }}</span>
                    </button>
                </div>
                <div class="edit-row">
                    <button class="btn-control" (click)="flipHorizontal()">
                        <span class="material-icons">flip</span>
                        <span class="control-text">{{ 'TELACADASTROPACIENTE.VIRARHORIZONTALMENTE' | translate }}</span>
                    </button>
                    <button class="btn-control" (click)="flipVertical()">
                        <span class="material-icons">flip</span>
                        <span class="control-text">{{ 'TELACADASTROPACIENTE.VIRARVERTIVALEMENTE' | translate }}</span>
                    </button>
                </div>
            </div>

            <div class="cropper-wrapper">
                <image-cropper [imageChangedEvent]="imageChangedEvent" [maintainAspectRatio]="true"
                    [aspectRatio]="1 / 1" [onlyScaleDown]="true" [roundCropper]="true" outputType="base64"
                    (imageCropped)="imageCropped($any($event))" (imageLoaded)="imageLoaded()"
                    (cropperReady)="cropperReady()" (loadImageFailed)="loadImageFailed()"
                    [style.display]="showCropper ? null : 'none'" [alignImage]="'left'">
                </image-cropper>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" (click)="CortarImagem()">
                <span class="material-icons">content_cut</span>
                {{ 'TELACADASTROPACIENTE.CORTAR' | translate }}
            </button>
        </div>
    </ngx-smart-modal>

    <!-- Modal de termos de uso -->
    <ngx-smart-modal #Termos identifier="Termos" customClass="nsm-centered medium-modal">
        <div class="modal-header">
            <h2 class="modal-title">{{ 'TELACADASTROPACIENTE.NOSSOSTERMOSDEUSO' | translate }}</h2>
            <span class="material-icons title-icon">copyright</span>
        </div>
        <div class="modal-body">
            <div class="terms-content">
                <p><strong>1 Introdução</strong> O Serviço xxx inclui recursos sociais e interativos...</p>
                <p><strong>2 Alterações nos Acordos</strong> Ocasionalmente, poderemos, a nosso critério...</p>
                <p><strong>3 Curtindo o xxx</strong> Eis algumas informações sobre todas as formas...</p>
                <p><strong>3.1 Nossos serviços e assinaturas pagas</strong> O xxx fornece serviços de streaming...</p>
                <p><strong>3.2 Códigos e outras ofertas pré-pagas</strong> Se você tiver comprado ou recebido...</p>
                <p><strong>3.3 Testes</strong> De tempos em tempos, nós ou outros em nosso nome...</p>
                <p><strong>4 Direitos que concedemos a você</strong> O Serviço xxxx e o Conteúdo são propriedade...</p>
            </div>
        </div>
        <div class="modal-footer terms-footer">
            <p class="terms-question">{{ 'TELACADASTROPACIENTE.VOCECONCORDACOMOSNOSSOSTERMOS' | translate }}</p>
            <div class="terms-actions">
                <button class="btn btn-outline" (click)="Termos.close()">
                    {{ 'TELACADASTROPACIENTE.NAO' | translate }}
                </button>
                <button class="btn btn-primary" (click)="Termos.close()">
                    {{ 'TELACADASTROPACIENTE.SIM' | translate }}
                </button>
            </div>
        </div>
    </ngx-smart-modal>

    <!-- Modal de confirmação -->
    <ngx-smart-modal #UsuarioExistente identifier="UsuarioExistente" customClass="nsm-centered small-modal"
        [closable]="false" [dismissable]="false" [escapable]="false">
        <div class="modal-header">
            <h2 class="modal-title">Atenção</h2>
        </div>
        <div class="modal-body">
            <div class="message-container">
                <span class="material-icons icon-warning">warning</span>
                <p>{{mensagemPaciente}}</p>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" (click)="NaoAceitarUsuarioExistente()">
                Cancelar
            </button>
            <button class="btn btn-primary" (click)="AceitarUsuarioExistente()">
                Aceitar
            </button>
        </div>
    </ngx-smart-modal>

    <!-- Conteúdo principal -->
    <div class="card">
        <!-- Cabeçalho -->
        <div class="card-header">
            <div class="header-icon-title">
                <div class="icon-box">
                    <span class="material-icons">person</span>
                </div>
                <h1>{{ 'TELACADASTROPACIENTE.CADASTROPACIENTE' | translate }}</h1>
            </div>
            <button class="btn-back" onclick='history.go(-1)'>
                <span class="material-icons">arrow_back</span>
            </button>
        </div>

        <div class="card-content">
            <!-- Mensagem de sucesso -->
            <div *ngIf="showMessageSuccess" class="success-message">
                <span class="material-icons success-icon">check_circle</span>
                <p>{{ 'TELACADASTROPACIENTE.REGISTROSALVOCOMSUCESSO' | translate }}</p>
            </div>


                <!-- Dados do Paciente -->
            <div class="form-section">
                <div class="section-header">
                <span class="section-marker"></span>
                <h2>{{ 'TELACADASTROPACIENTE.DADOSPACIENTE' | translate }}</h2>
                </div>
            
                <div class="form-container">
                <div class="profile-section">
                    <label for="imageperfilusuario" class="profile-photo-label">
                    <div class="profile-photo-container">
                        <img src="{{ ImagemPessoa }}" alt="Foto de Perfil">
                        <div class="profile-photo-overlay">
                        <span class="material-icons">photo_camera</span>
                        </div>
                    </div>
                    <span class="photo-hint">{{ 'TELACADASTROPACIENTE.FOTODEPERFIL' | translate }}</span>
                    </label>
                    <input type="file" id="imageperfilusuario" (change)="AlterarImagemPessoa($event)" (click)="LimpaCampoFile()">
                    <input type="hidden" id="Foto" name="Foto" [(ngModel)]="Dados.foto">
                </div>
                
                <div class="form-grid">
                    <!-- Nome -->
                    <div class="form-field field-large">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.NOME' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.NOME' | translate }}"
                        (keyup)="mascaraText($event, 'Nome')" 
                        (change)="mascaraText($any($event), 'Nome')" 
                        name="Nome"
                        required [(ngModel)]="Dados.nome">
                        <mat-error *ngIf="Nome.invalid">{{getErrorMessageNome() | translate }}</mat-error>
                    </mat-form-field>
                    </div>
        
                    <!-- Data de Nascimento -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.DATADENASCIMENTO' | translate }}</mat-label>
                        <input matInput
                        placeholder="{{ 'TELACADASTROPACIENTE.DATADENASCIMENTO' | translate }}"
                        mask="00/00/0000" name="Data de Nascimento" id="DtaNasc" #dtaNascimentoIn
                        (keypress)="mascaraData($event)" [(ngModel)]="Dados.dtaNascimento"
                        maxlength="10" (blur)="ValidaDta(dtaNascimentoIn.value)">
                    </mat-form-field>
                    <span class="error-text" *ngIf="Dtanasc">
                        {{ 'TELACADASTROPACIENTE.DATAINVALIDA' | translate }}
                    </span>
                    </div>
        
                    <!-- Sexo -->
                    <div class="form-field">
                    <ng-select 
                        [items]="DadosSexo"
                        placeholder="{{ 'TELACADASTROPACIENTE.SEXO' | translate }}"
                        (focus)="carregaDadosSexo()"
                        name="Sexo"
                        [selectOnTab]="true"
                        [(ngModel)]="campoSexo"
                        class="custom-select">
                    </ng-select>
                    </div>
        
                    <!-- Naturalidade -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.NATURALIDADE' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.NATURALIDADE' | translate }}"
                        name="Naturalidade" [(ngModel)]="Dados.naturalidade" #NaturalidadeInput
                        (keyup)="mascaraText($any($event), 'Naturalidade')" 
                        (change)="mascaraText($any($event), 'Naturalidade')" maxlength="20">
                    </mat-form-field>
                    </div>
        
                    <!-- Nacionalidade -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.NACIONALIDADE' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.NACIONALIDADE' | translate }}"
                        name="Nascionalidade" [(ngModel)]="Dados.nascionalidade" #NACIONALIDADEInputt
                        (keyup)="mascaraText($any($event), 'Nascionalidade')" 
                        (change)="mascaraText($any($event), 'Nascionalidade')" maxlength="20">
                    </mat-form-field>
                    </div>
        
                    <!-- CPF -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.CPF' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.CPF' | translate }}" #cpfIn
                        name="CPF" (change)='validarCpf(cpfIn.value)'
                        (keypress)="mascaraCpf('###.###.###-##', $event)" mask="000.000.000-00"
                        length="11" required [(ngModel)]="Dados.cpf">
                    </mat-form-field>
                    <span class="error-text" *ngIf="campoCPFInvalido">CPF inválido</span>
                    <span class="error-text" *ngIf="campoCPFVazil && !campoCPFInvalido">
                        Esse campo precisa ser preenchido
                    </span>
                    </div>
        
                    <!-- Email -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.EMAIL' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.EMAIL' | translate }}" #emailInp2
                        type="email" (change)='validarEmailPaciente(emailInp2.value)' name="Email"
                        required [(ngModel)]="Dados.email">
                    </mat-form-field>
                    <span class="error-text" *ngIf="campoEmailInvalido">Email inválido</span>
                    <span class="error-text" *ngIf="campoEmailVazil && !campoEmailInvalido">
                        Esse campo precisa ser preenchido
                    </span>
                    </div>
        
                    <!-- Profissão -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.PROFISSAO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.PROFISSAO' | translate }}"
                        name="profissao" [(ngModel)]="Dados.profissao">
                    </mat-form-field>
                    </div>
        
                    <!-- Telefone -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.TELEFONE' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.TELEFONE' | translate }}"
                        name="Telefone" (keyup)="mascaraTelefone($event)"
                        (keypress)="mascaraTelefone($event)" #telefoneInn
                        (change)="ValidaTelefone(telefoneInn.value)" maxlength="15" minlength="14"
                        [(ngModel)]="Dados.telefone">
                    </mat-form-field>
                    <span class="error-text" *ngIf="TelVal">Telefone Inválido</span>
                    </div>
        
                    <!-- Celular -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.CELULAR' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.CELULAR' | translate }}"
                        id="telcelular" (change)='validarTelMovel(telefoneMovelInput222.value)' #telefoneMovelInput222
                        name="Telefone Movel" (keyup)="mascaraTelefone($event)"
                        (keypress)="mascaraTelefone($event)" maxlength="15" minlength="14" required
                        [(ngModel)]="Dados.telefoneMovel">
                    </mat-form-field>
                    <span class="error-text" *ngIf="TelMovVal">
                        {{ 'TELACADASTROPACIENTE.TELEFONEINVALIDO' | translate }}
                    </span>
                    <span class="error-text" *ngIf="TelMovValVasil && !TelMovVal">
                        {{ 'TELACADASTROPACIENTE.ESSECAMPOPRECISASERPREENCHIDO' | translate }}
                    </span>
                    </div>
        
                    <!-- Telefone Comercial -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.TELEFONECOMERCIAL' | translate }}</mat-label>
                        <input matInput #telefoneInput
                        placeholder="{{ 'TELACADASTROPACIENTE.TELEFONECOMERCIAL' | translate }}"
                        name="Telefone Comercial" (keyup)="mascaraTelefone($event)"
                        (keypress)="mascaraTelefone($event)"
                        (change)="ValidaTelefoneComercial(telefoneInput.value)" maxlength="15"
                        minlength="14" [(ngModel)]="Dados.telefoneComercial">
                    </mat-form-field>
                    <span class="error-text" *ngIf="TelComVal">
                        {{ 'TELACADASTROPACIENTE.TELEFONEINVALIDO' | translate }}
                    </span>
                    </div>
        
                    <!-- Clínicas Correspondentes -->
                    <div class="form-field field-large">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.CLINICASCORRESPONDENTE' | translate }}</mat-label>
                        <mat-select #CLINICASCORRESPONDENTE
                        placeholder="{{ 'TELACADASTROPACIENTE.CLINICASCORRESPONDENTE' | translate }}"
                        required multiple name="clinicas" (keyup)="mascaraText($event, 'clinicas')"
                        (blur)="ValidaClinicas($any($event.target).value)" [formControl]="clinicas">
                        <mat-option *ngFor="let item of DadosClinicas;let i = index" [value]="item">
                            {{item.desClinica | truncate : 40 : "…"}}
                        </mat-option>
                        </mat-select>
                    </mat-form-field>
                    <span class="error-text" *ngIf="clinicaVal">
                        {{ 'TELACADASTROPACIENTE.UMACLINICAPRECISASERSELECIONADA' | translate }}
                    </span>
                    </div>
                </div>
                </div>
            </div>
            <!-- Dados Pessoais (Medidas) -->
            <div class="form-section">
                <div class="section-header">
                <span class="section-marker"></span>
                <h2>{{ 'TELACADASTROPACIENTE.DADOSPESSOAIS' | translate }}</h2>
                </div>
                <div class="form-container">
                <div class="form-grid">
                    <!-- Peso -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.PESO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.PESO' | translate }}"
                        id="CampoPeso" maxlength="6" [(ngModel)]="Dados.peso" (blur)="CalculoIMC()"
                        (keypress)="mascaraPeso($event)" (keyup)="mascaraPeso($event)">
                    </mat-form-field>
                    </div>
        
                    <!-- Altura -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.ALTURA' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.ALTURA' | translate }}"
                        id="CampoAltura" maxlength="4" [(ngModel)]="Dados.altura" (blur)="CalculoIMC()"
                        (keypress)="mascaraAltura($event)" (keyup)="mascaraAltura($event)">
                    </mat-form-field>
                    </div>
        
                    <!-- IMC -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.IMC' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.IMC' | translate }}"
                        name="IMC" [(ngModel)]="Dados.iMC" maxlength="5" readonly="true">
                    </mat-form-field>
                    </div>
        
                    <!-- Pressão -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.PRESSAO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.PRESSAO' | translate }}"
                        id="campoPressao" name="Pressão" [(ngModel)]="Dados.pressao"
                        (keypress)="mascaraPressao($event)" (keyup)="mascaraPressao($event)"
                        maxlength="5">
                    </mat-form-field>
                    </div>
        
                    <!-- Batimento -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.BATIMENTO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.BATIMENTO' | translate }}"
                        name="Batimento" [(ngModel)]="Dados.batimento" (keyup)="mascaraNumeros($event)"
                        maxlength="3">
                    </mat-form-field>
                    </div>
        
                    <!-- Temperatura -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.TEMPERATURA' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.TEMPERATURA' | translate }}"
                        name="Temperatura" [(ngModel)]="Dados.temperatura" maxlength="5"
                        (keypress)="mascaraTemperatura($event)" (keyup)="mascaraTemperatura($event)">
                    </mat-form-field>
                    </div>
                </div>
            </div>
            </div>
            <!-- Convênio -->
            <div class="form-section">
                <div class="section-header">
                <span class="section-marker"></span>
                <h2>{{ 'TELACADASTROPACIENTE.CONVENIO' | translate }}</h2>
                </div>
                
                <div class="form-container">
                <div class="form-grid">
                    <!-- Plano de Saúde -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.PLANODESAUDE' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.PLANODESAUDE' | translate }}"
                        name="Plano de Saúde" [(ngModel)]="Dados.planosaude">
                    </mat-form-field>
                    </div>

                    <!-- Convênio -->
                    <div class="form-field">
                    <ng-select 
                        [items]="dadosConvenio"
                        placeholder="{{ 'TELAAGENDA.CONVENIO' | translate }}" 
                        bindLabel="desConvenio"
                        bindValue="idConvenio" 
                        name="especialidade" 
                        [selectOnTab]="true"
                        [(ngModel)]="Dados.convenio" 
                        notFoundText="Cadastre convenios."
                        class="custom-select">
                    </ng-select>
                    </div>

                    <!-- Matrícula -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.MATRICULA' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.MATRICULA' | translate }}"
                        name="Matrícula" [(ngModel)]="Dados.matricula">
                    </mat-form-field>
                    </div>

                    <!-- Procedência -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.PROCEDENCIA' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.PROCEDENCIA' | translate }}"
                        name="Procedência" [(ngModel)]="Dados.procedencia">
                    </mat-form-field>
                    </div>
                </div>
                </div>
            </div>
            <!-- Endereço -->
            <div class="form-section">
                <div class="section-header">
                <span class="section-marker"></span>
                <h2>{{ 'TELACADASTROPACIENTE.ENDEREÇO' | translate }}</h2>
                </div>
                
                <div class="form-container">
                <div class="form-grid">
                    <!-- Rua -->
                    <div class="form-field field-large">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.RUA' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.RUA' | translate }}"
                        name="Rua" [(ngModel)]="Dados.rua">
                    </mat-form-field>
                    </div>
        
                    <!-- Número -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.NUMERO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.NUMERO' | translate }}"
                        name="n°" type="number" maxlength="10" [(ngModel)]="Dados.numero">
                    </mat-form-field>
                    </div>
        
                    <!-- Complemento -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.COMPLEMENTO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.COMPLEMENTO' | translate }}"
                        name="Complemento" [(ngModel)]="Dados.complemento">
                    </mat-form-field>
                    </div>
        
                    <!-- UF -->
                    <div class="form-field field-small">
                    <ng-select 
                        [items]="dadosUF"
                        placeholder="{{ 'TELACADASTROPACIENTE.UF' | translate }}" 
                        bindLabel="siglasUf"
                        bindValue="siglasUf" 
                        name="UF" 
                        (change)="CidadePorUF()" 
                        [selectOnTab]="true"
                        notFoundText="{{ 'TELACADASTROPACIENTE.UFNAOENCONTRADA' | translate }}"
                        [(ngModel)]="Dados.uf"
                        class="custom-select">
                    </ng-select>
                    </div>
        
                    <!-- Município -->
                    <div class="form-field">
                    <ng-select 
                        [items]="dadosCidadeUf"
                        placeholder="{{ 'TELACADASTROPACIENTE.MUNICIPIO' | translate }}" 
                        bindLabel="nmeCidade"
                        bindValue="idCidade" 
                        name="Municipio" 
                        [selectOnTab]="true"
                        notFoundText="{{ 'TELACADASTROPACIENTE.UFNAOENCONTRADA' | translate }}"
                        [(ngModel)]="Dados.idCidade"
                        class="custom-select">
                    </ng-select>
                    </div>
        
                    <!-- Bairro -->
                    <div class="form-field">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.BAIRRO' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.BAIRRO' | translate }}"
                        name="Bairro" [(ngModel)]="Dados.bairro">
                    </mat-form-field>
                    </div>
        
                    <!-- CEP -->
                    <div class="form-field field-small">
                    <mat-form-field appearance="outline">
                        <mat-label>{{ 'TELACADASTROPACIENTE.CEP' | translate }}</mat-label>
                        <input matInput placeholder="{{ 'TELACADASTROPACIENTE.CEP' | translate }}"
                        name="CEP" mask="00000-000" maxlength="9" [(ngModel)]="Dados.cep">
                    </mat-form-field>
                    </div>
                </div>
                </div>
            </div>
        </div>
        <div class="card-actions">
                <button class="btn btn-outline" (click)="LimparCampos()">
                  <span class="material-icons">refresh</span>
                  <span class="btn-text">{{ 'TELACADASTROPACIENTE.LIMPAR' | translate }}</span>
                </button>
                <button class="btn btn-primary" (click)="Submit()">
                  <span class="material-icons">save</span>
                  <span class="btn-text">{{ 'TELACADASTROPACIENTE.SALVAR' | translate }}</span>
                </button>
        </div>
    </div>
</div>
<!-- </form> -->



<ngx-smart-modal #Termos identifier="Termos" customClass="nsm-centered medium-modal">
    <div class="modal-header p-t-20 p-b-20">
        <div class="row">
            <div class=" ">
                <h1 class="little-title fw-700" style=" padding-left: 3vh;">
                    {{ 'TELACADASTROPACIENTE.NOSSOSTERMOSDEUSO' | translate }}</h1>
            </div>
            <div class="">
                <mat-icon style="color: #4c599e; padding-left: 10px;">copyright</mat-icon>
            </div>
        </div>
    </div>

    <div class="card-actions">
        <button class="btn btn-outline" (click)="LimparCampos()">
            <span class="material-icons">refresh</span>
            <span class="btn-text">{{ 'TELACADASTROPACIENTE.LIMPAR' | translate }}</span>
        </button>
        <button class="btn btn-primary" (click)="Submit()">
            <span class="material-icons">save</span>
            <span class="btn-text">{{ 'TELACADASTROPACIENTE.SALVAR' | translate }}</span>
        </button>
    </div>
</ngx-smart-modal>