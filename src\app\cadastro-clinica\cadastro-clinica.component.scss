// Variáveis - Paleta Moderna com Verde Saúde
$primary-color: #2E8B57;        // Verde Saúde
$primary-light: #A3D9B1;        // Verde claro suavizado
$primary-dark: #1F5F3D;         // Verde escuro
$secondary-color: #F4F4F9;      // Cinza Claro / Off-White
$secondary-light: #FFFFFF;      // Branco puro para elementos contrastantes
$secondary-dark: #DADDE5;       // Cinza médio para hover/active
$accent-color: #4ECDC4;         // Turquesa Claro (toque moderno)
$error-color: #FF6B6B;          // Vermelho Pastel
$warning-color: #FFC107;        // Amarelo para alertas
$success-color: #4CAF50;        // Verde para sucesso
$text-primary: #333333;         // Cinza escuro para boa legibilidade
$text-secondary: #6B7280;       // Cinza médio
$border-color: #E5E7EB;         // Bordas suaves
$bg-color: #F9FAFB;             // Fundo geral suave
$card-bg: #FFFFFF;              // Fundo dos cards
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

* {
  box-sizing: border-box;
}

.clinica-container {
  height: 87vh;

  @media (max-width: 768px) {
    padding: 16px;
  }
}

// Modal styles
.modal-content {
  background-color: white;
  border-radius: $border-radius;
  overflow: hidden;
}

.modal-header {
  padding: 16px 24px;
  background-color: $primary-color;
  
  .modal-title {
    margin: 0;
    color: white;
    font-size: 1.25rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .warning-title {
    color: white;
    
    .warning-icon {
      color: $warning-color;
    }
  }
}

.modal-body {
  padding: 24px;
  
  .modal-message {
    margin: 0;
    font-size: 1rem;
    color: $text-primary;
  }
  
  .image-editor-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    justify-content: center;
  }
  
  .image-cropper-container {
    max-width: 300px;
    margin: 0 auto;
    
    ::ng-deep image-cropper {
      display: block;
      max-width: 100%;
    }
  }
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// Material overrides
:host ::ng-deep {
  // Form fields
  .mat-form-field-appearance-outline .mat-form-field-outline {
    color: $border-color;
  }
  
  .mat-form-field-label {
    color: $text-secondary;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: $primary-color;
  }
  
  .mat-form-field-appearance-outline.mat-focused .mat-form-field-label {
    color: $primary-color;
  }
  
  .mat-input-element {
    color: $text-primary;
  }
  
  .mat-form-field-subscript-wrapper {
    overflow: visible;
  }
  
  .mat-select-value {
    color: $text-primary;
  }
  
  .mat-select-arrow {
    color: $primary-color;
  }
  
  // Checkbox and toggle styling
  .mat-checkbox-checked.mat-accent .mat-checkbox-background,
  .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
    background-color: $primary-color;
  }
  
  .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
    background-color: $primary-color;
  }
  
  .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: rgba($primary-color, 0.5);
  }
  
  // Smart modal
  .nsm-dialog {
    padding: 0 !important;
  }
  
  .nsm-content {
    padding: 0 !important;
    border-radius: $border-radius !important;
  }
  
  .medium-modal .nsm-content {
    max-width: 600px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .card-body {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .form-row {
    margin: 0 -4px 12px;
  }
  
  .form-group {
    padding: 0 4px;
    margin-bottom: 12px;
  }
  
  .card-footer {
    flex-wrap: wrap;
    
    .btn {
      flex: 1;
    }
  }
  
  .image-editor-controls {
    flex-direction: column;
    
    .btn-control {
      width: 100%;
    }
  }
  
  .modal-footer {
    flex-direction: column;
    
    .btn {
      width: 100%;
    }
  }
}

// Buttons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  transition: all $transition ease;
  cursor: pointer;
  font-size: 14px;
  
  .material-icons {
    font-size: 18px;
    margin-right: 8px;
  }
  
  &:focus {
    outline: none;
  }
}

.btn-primary {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-outline {
  background-color: transparent;
  border: 1px solid $border-color;
  color: $text-secondary;
  padding: 8px;
  border-radius: 5px;
  gap: 6px;
  &:hover {
    border-color: $primary-color;
    color: $primary-color;
    background-color: rgba($primary-color, 0.05);
  }
}
.btn-add{
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  color: white;
  background-color: $primary-color;
  border-radius: 5px;
  gap: 6px;
  &:hover {
    background-color: $primary-dark ;
  }
}

.btn-success {
  background-color: $primary-color;
  color: white;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
}

.btn-danger {
  background-color: $error-color;
  color: white;
  
  &:hover {
    background-color: darken($error-color, 5%);
  }
}

.btn-link {
  background: none;
  color: $primary-color;
  padding: 6px 12px;
  font-weight: 500;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
}

.btn-control {
  background-color: $secondary-color;
  color: $text-primary;
  border: 1px solid $border-color;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  transition: all $transition ease;
  
  .material-icons {
    font-size: 16px;
    margin-right: 4px;
  }
  
  &:hover {
    background-color: $secondary-dark;
  }
}

// Responsibles table
.responsibles-table {
  margin-top: 16px;
  overflow: hidden;
  border: 1px solid $border-color;
  border-radius: $border-radius;
  
  .data-table {
    width: 100%;
    border-collapse: collapse;
    
    tr {
      border-bottom: 1px solid $border-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:hover {
        background-color: rgba($primary-color, 0.05);
      }
    }
    
    td {
      padding: 12px 16px;
      font-size: 0.875rem;
      
      &.name-col {
        min-width: 120px;
      }
      
      &.cpf-col {
        min-width: 130px;
        color: $text-secondary;
      }
      
      &.email-col {
        min-width: 200px;
        color: $text-secondary;
      }
      
      &.phone-col {
        min-width: 130px;
        color: $text-secondary;
      }
      
      &.actions-col {
        text-align: right;
        white-space: nowrap;
      }
    }
  }
  
  .btn-action {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background-color: transparent;
    color: $primary-color;
    cursor: pointer;
    transition: $transition;
    
    &:hover {
      background-color: rgba($primary-color, 0.1);
    }
    
    &.btn-delete {
      color: $error-color;
      
      &:hover {
        background-color: rgba($error-color, 0.1);
      }
    }
    
    mat-icon {
      font-size: 18px;
    }
  }
}

// Cards
.card {
  background-color: $card-bg;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  border: none;
  margin-bottom: 24px;
  overflow: hidden;
}

.main-card {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  background-color: $secondary-light;
}

.card-body {
  padding: 24px;
}

.card-footer {
  padding: 16px 24px;
  border-top: 1px solid $border-color;
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

// Headers
.header-left {
  display: flex;
  align-items: center;
}

.icon-container {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba($primary-color, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  
  .material-icons {
    color: $primary-color;
    font-size: 20px;
  }
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  color: $primary-dark;
  font-weight: 600;
}

// Sections
.section {
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 16px;
}
.header-actions{
  display: flex;
  gap: 12px;
}
.section-header-with-actions{
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

.section-title {
  font-size: 1.25rem;
  color: $primary-color;
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: $primary-color;
    margin-right: 8px;
    border-radius: 2px;
  }
}

.section-content {
  background-color: $secondary-light;
  border-radius: $border-radius;
  padding: 20px;
}

// Toggles
.toggles-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  
  .toggle-option {
    flex: 1;
    min-width: 200px;
  }
}

// Profile upload
.profile-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.photo-container {
  width: 120px;
  height: 120px;
  position: relative;
  overflow: hidden;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: $box-shadow;
  margin-bottom: 8px;
  
  .profile-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($primary-dark, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity $transition ease;
    
    .material-icons {
      color: white;
      font-size: 24px;
    }
  }
  
  &:hover .photo-overlay {
    opacity: 1;
  }
}

h5 {
  margin: 8px 0;
  color: $text-secondary;
  font-weight: 500;
  font-size: 1rem;
}

input[type="file"] {
  display: none;
}

// Form
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-group {
  padding: 0 8px;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.col-md-2 {
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-3 {
  flex: 0 0 25%;
  max-width: 25%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
  
  @media (max-width: 768px) {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.full-width {
  width: 100%;
}

.field-error {
  color: $error-color;
  font-size: 0.75rem;
  margin-top: -0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
}

// Select container
.select-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  
  .select-label {
    font-size: 0.75rem;
    color: $text-secondary;
    margin-bottom: 0.5rem;
    padding-left: 1rem;
  }
}

// Modern Select (ng-select)
.modern-select {
  ::ng-deep {
    .ng-select-container {
      border-color: $border-color;
      border-radius: 4px;
      height: 54px !important;
      min-height: 54px !important;
      align-items: center;
      
      &:hover {
        border-color: $primary-color;
      }
    }
    
    .ng-value-container {
      padding-top: 0 !important;
    }
    
    .ng-placeholder {
      color: $text-secondary;
    }
    
    .ng-value {
      color: $text-primary;
    }
    
    .ng-dropdown-panel {
      border-radius: 8px;
      border-color: $border-color;
      box-shadow: $box-shadow;
      
      .ng-dropdown-panel-items {
        border-radius: 8px;
      }
    }
    
    .ng-option {
      padding: 10px 16px;
      
      &.ng-option-selected, &.ng-option-marked {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
    }
  }
}