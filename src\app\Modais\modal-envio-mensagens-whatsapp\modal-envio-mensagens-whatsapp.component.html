<div class="modal-container">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">Enviar mensagem paciente</h3>
        </div>
        <button class="close-button" (click)="fecharModal()">
            <mat-icon>close</mat-icon>
        </button>
    </header>

    <main class="modal-container">
        <div class="content-columns">
            <!-- Coluna das mensagens padrão -->
            <div class="messages-column">
                <div class="section-title">Mensagens padrões</div>
                <div class="messages-list">
                    <div *ngFor="let item of lsMensagenSalvas" class="message-card" (click)="CarregaMsg(item)">
                        <span>{{item.tituloMensagem}}</span>
                    </div>
                    <div *ngIf="lsMensagenSalvas?.length === 0" class="empty-state">
                        Nenhuma mensagem padrão disponível
                    </div>
                </div>
            </div>

        <div class="divMensagens" style="width: 100%;">


            <div class="contentModal">
                <p style="text-align: center;"> Conteudo mensagem </p>

                <mat-form-field appearance="outline">
                    <mat-label>Mensagem</mat-label> 
                    <textarea matInput name="Mensagem" id="Mensagem" class="txtAreaInput" [(ngModel)]="msg"
                        rows="18"></textarea>
                </mat-form-field>


                <div style="width: 100%; display: flex; justify-content: space-between; flex-direction: row;">
                    <span style="display: flex;">
                        <!-- <app-toggle [(flg)]="flgMsgTemResposta">
                        </app-toggle> -->

                        {{flgMsgTemResposta ? 'A mensagem tem resposta':'A mensagem não tem resposta'}}
                    </span>

                    <button mat-icon-button class="IconeBotaoInfo" (click)="abrirModalInformacao()"><mat-icon>info</mat-icon></button> 
                </div>
            </div>
        </div>

            <!-- Coluna das consultas -->
            <div class="appointments-column">
                <div class="section-title">Consultas:</div>
                <div class="appointments-list">
                    <div *ngIf="lsConsultas.length < 1" class="empty-state">Não há consultas</div>

                    <div *ngFor="let item of lsConsultas" class="appointment-item" 
                         (click)="AlteraStatusConsultaLista(item.idConsulta!)">
                        <div class="checkbox-container">
                            <input type="checkbox" [checked]="ValidaUsuarioSelecionado(item.idConsulta!)">
                        </div>
                        <div class="appointment-details">
                            <span class="appointment-message">{{item.mensagem}}</span>
                            <span class="appointment-date">{{formatDate(item.dtConsulta!)}}</span>
                        </div>
                    </div>
                </div>
                <div class="selection-counter">{{listaIdsConsultas.length + " / " + lsConsultas.length}}</div>
            </div>
        </div>
    </main>

    <footer class="modal-footer">
        <button class="action-button cancel-button" (click)="fecharModal()">
            <mat-icon>cancel</mat-icon>
            <span>Cancelar</span>
        </button>
        
        <button class="action-button confirm-button" (click)="enviaMensagens()">
            <mat-icon>send</mat-icon>
            <span>Enviar mensagens</span>
        </button>
    </footer>
</div>