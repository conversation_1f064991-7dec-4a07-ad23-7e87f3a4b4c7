import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { ContasPagar } from '../model/contasPagar';
import { ContasReceber } from '../model/contasReceber';
import { Observable } from 'rxjs';
import { ValidadoreseMascaras } from '../Util/validadores';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class ContasPagarReceberService {

    constructor(private http: HttpClient, private validacao: ValidadoreseMascaras, private spinner: SpinnerService) { }

    salvarContaPagar(objContaPagar: ContasPagar) {

        objContaPagar.DtaVencimento = objContaPagar.DtaVencimento ? this.validacao.Convertdata(objContaPagar.DtaVencimento) : null;
        objContaPagar.DtaBaixa = objContaPagar.DtaBaixa ? this.validacao.Convertdata(objContaPagar.DtaBaixa) : null;
        return this.http.post(environment.apiEndpoint + '/ContaPagar/SalvarContaPagar', objContaPagar);
    }
    GetTipoPagamento() {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaPagar/GetTipoPagamento');
    }
    DeletarContaPagar(IdContasPagar:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaPagar/DeletarContaPagar/' + IdContasPagar);
    }
    EditarContaPagar(paulo:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaPagar/EditarContaPagar/' + paulo);
    }
    CarregarEdicaoContaPagar(idcontasPagar:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaPagar/CarregarEdicaoContaPagar/' + idcontasPagar);
    }
    ListarContaPagar(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaPagar/ListarContaPagar');
    }
    salvarContaReceber(objContaReceber: ContasReceber) {
        this.spinner.show();

        objContaReceber.DtaRecebimento = objContaReceber.DtaRecebimento ? this.validacao.Convertdata(objContaReceber.DtaRecebimento) : null;
        objContaReceber.DtaBaixa = objContaReceber.DtaBaixa ? this.validacao.Convertdata(objContaReceber.DtaBaixa) : null;
        return this.http.post(environment.apiEndpoint + '/ContaReceber/SalvarContaReceber', objContaReceber);
    }
    DeletarContaReceber(idcontasPagar:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaReceber/DeletarContaReceber/' + idcontasPagar);
    }
    EditarContaReceber(idContaReceber:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaReceber/EditarContaReceber/' + idContaReceber);
    }
    CarregarEdicaoContaReceber(idcontasReceber:any) {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaReceber/CarregarEdicaoContaReceber/' + idcontasReceber);
    }
    ListarContaReceber(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/ContaReceber/ListarContaReceber');
    }
}