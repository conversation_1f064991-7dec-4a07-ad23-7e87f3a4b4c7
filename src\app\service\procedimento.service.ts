import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ProcedimentoModelview } from '../model/procedimento';
import { environment } from 'src/environments/environment';
import { catchError, throwError } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class ProcedimentoService {

    constructor(
        private http: HttpClient,
    ) { }

    public headers = new Headers({ 'Content-Type': 'application/json' });
    
  public SalvarProcedimento(procedimento: ProcedimentoModelview){
    return this.http.post(environment.apiEndpoint + '/Procedimento/SalvarProcedimento', procedimento)
    .pipe(
      catchError((error) => {
          ;
          return throwError(() => error);
      })
    );
  }

  public GetDadosProcedimento(idProcedimento: number){
      let params = new HttpParams();
      params = params.append('idProcedimento', idProcedimento);
      return this.http.get<ProcedimentoModelview>(environment.apiEndpoint + '/Procedimento/GetDadosProcedimento', {params})
      .pipe(
        catchError((error) => {
          ;
          return throwError(() => error);
      })
      );
  }

  public GetListaProcedimentoConsulta(idConsulta: number){
      let params = new HttpParams();
      params = params.append('idConsulta', idConsulta);
      return this.http.get<ProcedimentoModelview[]>(environment.apiEndpoint + '/Procedimento/GetListaProcedimentoConsulta', {params})
      .pipe(
        catchError((error) => {
          ;
          return throwError(() => error);
      })
      );
  }
  
}