.streaming-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header-consulta {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .info-consulta {
    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 24px;
    }

    .participantes {
      display: flex;
      gap: 24px;

      .medico-info, .paciente-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #666;

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }

      .medico-info {
        color: #1976d2;
      }

      .paciente-info {
        color: #388e3c;
      }
    }
  }

  .status-consulta {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;

    .status-indicator {
      padding: 6px 12px;
      border-radius: 16px;
      color: white;
      font-weight: 500;
      font-size: 14px;
    }

    .tempo-consulta {
      display: flex;
      align-items: center;
      gap: 4px;
      color: #666;
      font-size: 16px;
      font-weight: 500;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: hidden;

  ::ng-deep .mat-mdc-tab-group {
    height: 100%;

    .mat-mdc-tab-body-wrapper {
      height: calc(100% - 48px);

      .mat-mdc-tab-body {
        height: 100%;

        .mat-mdc-tab-body-content {
          height: 100%;
          overflow: auto;
        }
      }
    }
  }
}

.video-container {
  height: 100%;
  position: relative;
  background: #000;

  .video-area {
    height: 100%;
    width: 100%;

    .video-iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }

  .loading-video {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;

    mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.7;
    }

    p {
      font-size: 18px;
      margin: 0;
    }
  }

  .video-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 16px;

    button {
      width: 56px;
      height: 56px;

      &.muted {
        background-color: #f44336 !important;
      }

      &.camera-off {
        background-color: #f44336 !important;
      }

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
    }
  }
}

.dados-paciente {
  padding: 24px;
  height: 100%;
  overflow-y: auto;

  .dados-card {
    margin-bottom: 24px;

    mat-card-header {
      margin-bottom: 16px;

      mat-card-title {
        color: #333;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .dados-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 16px;

      .campo {
        display: flex;
        flex-direction: column;
        gap: 4px;

        &.campo-full {
          grid-column: 1 / -1;
        }

        label {
          font-weight: 500;
          color: #555;
          font-size: 14px;
        }

        span {
          padding: 8px 12px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          color: #333;
          min-height: 20px;
        }
      }
    }
  }

  .sem-dados {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #666;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    p {
      margin: 8px 0;
      font-size: 16px;

      &:last-child {
        font-family: monospace;
        background: #f8f9fa;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
      }
    }
  }
}

.actions-bar {
  background: white;
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  button {
    display: flex;
    align-items: center;
    gap: 8px;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .header-consulta {
    flex-direction: column;
    gap: 16px;
    text-align: center;

    .participantes {
      justify-content: center;
    }
  }

  .dados-paciente {
    padding: 16px;

    .dados-card .dados-grid {
      grid-template-columns: 1fr;
    }
  }

  .actions-bar {
    flex-direction: column;
    gap: 12px;

    button {
      width: 100%;
      justify-content: center;
    }
  }

  .video-controls {
    bottom: 10px;
    gap: 12px;

    button {
      width: 48px;
      height: 48px;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }
    }
  }
}
