import { Component, OnInit } from '@angular/core';
import { guiaModalService } from './guia-exame.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { GuiaExames } from 'src/app/model/guiaExames';
import { DocumentosService } from 'src/app/service/documentos.service';
import { saveAs } from 'file-saver';
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from '@angular/forms';
import { UfClass } from 'src/app/Util/UFClass';
import { MedicoService } from 'src/app/service/medico.service';
import { PacienteService } from 'src/app/service/pacientes.service';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { CommonModule } from '@angular/common';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-guia-exame',
    templateUrl: './guia-exame.component.html',
    styleUrls: ['./guia-exame.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      FormsModule,
ReactiveFormsModule,
      CommonModule,
      NgxSmartModalModule,
      NgSelectModule,
      MatFormFieldModule,
      MatIcon,
      TranslateModule
    ]
})
export class GuiaExameComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    public ngxSmartModalService: NgxSmartModalService,
    private GuiaModalService: guiaModalService,
    private documentosService: DocumentosService,
    public medicoService: MedicoService,
    public pacientesService: PacienteService,
    public usuarioLogadoService: UsuarioLogadoService
  ) { 
    this.GuiaModalService
    .getAbrirModal()
    .subscribe(ret => {
      if (!ret) return;
      
      // this.objGuiaExames.DataAssinProcSerie86 = new Date().toLocaleDateString();

      this.ngxSmartModalService.getModal('GuiaExame').open();
    });
  }

  objGuiaExames = new GuiaExames;

  showMessageError = false;
  dadosUF = UfClass;
  dtaInvalida: boolean = false;
  ufInvalido: boolean = false;


  ngOnInit() {

    this.CarregaMedicos();
    this.CarregaPacientes();
  }

  limparCampoGuiaExame() {
    this.objGuiaExames = new GuiaExames;
    this.Nome.markAsUntouched();
    this.CodCNES.markAsUntouched();
    this.NomeProSolicitante.markAsUntouched();
    this.ConsProfi.markAsUntouched();
    this.NumConse.markAsUntouched();
    this.CodCBOS.markAsUntouched();
    this.IndicacaoClin.markAsUntouched();
    this.CodProced.markAsUntouched();
    this.DtaAssinatura.markAsUntouched();

    this.dtaInvalida = false;
    this.ufInvalido = false;
  }

  GerarGuiaExame() {
    this.validarCampos();
    if (this.showMessageError) {
      this.spinner.hide();
      return;
    }
    this.documentosService.GerarGuia(this.objGuiaExames).subscribe((retorno) => {
      var mediaType = 'application/pdf';
      var blob = new Blob([retorno], { type: mediaType });
      var filename = 'GuiaExame.pdf';
      saveAs(blob, filename);
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }

  public validarCampos() {
    this.showMessageError = false;

    this.ValidaUf();
    this.ValidaDta(this.objGuiaExames.DataAssinProcSerie86);

    this.Nome.markAsTouched();
    this.CodCNES.markAsTouched();
    this.NomeProSolicitante.markAsTouched();
    this.ConsProfi.markAsTouched();
    this.NumConse.markAsTouched();
    this.CodCBOS.markAsTouched();
    this.IndicacaoClin.markAsTouched();
    this.CodProced.markAsTouched();
    this.DtaAssinatura.markAsTouched();

    if (this.objGuiaExames.Nome11 == undefined || !this.objGuiaExames.Nome11.trim()
    ) {
      this.NomePaciente = true;
    }
    if (this.objGuiaExames.NomeProfissionalSolicitante16 == undefined || !this.objGuiaExames.NomeProfissionalSolicitante16.trim()) {
      this.Nomemedico = true;
    }

    if (this.objGuiaExames.Nome11 == undefined || !this.objGuiaExames.Nome11.trim()
      || this.objGuiaExames.CodigoCnes15 == undefined || !this.objGuiaExames.CodigoCnes15.trim()
      || this.objGuiaExames.NomeProfissionalSolicitante16 == undefined || !this.objGuiaExames.NomeProfissionalSolicitante16.trim()
      || this.objGuiaExames.ConselhoProfissional17 == undefined || !this.objGuiaExames.ConselhoProfissional17.trim()
      || this.objGuiaExames.NumeroConselho18 == undefined || !this.objGuiaExames.NumeroConselho18.trim()
      || this.objGuiaExames.CodigoCboS20 == undefined || !this.objGuiaExames.CodigoCboS20.trim()
      || this.objGuiaExames.IndicacaoClinica24 == undefined || !this.objGuiaExames.IndicacaoClinica24.trim()
      || this.objGuiaExames.CodigoProcedimento26_1 == undefined || !this.objGuiaExames.CodigoProcedimento26_1.trim()
      || this.objGuiaExames.DataAssinProcSerie86 == undefined || !this.objGuiaExames.DataAssinProcSerie86.trim()
      || this.objGuiaExames.UF19 == undefined || this.objGuiaExames.UF19 == null
      || this.dtaInvalida == true || this.ufInvalido == true
    ) {
      this.showMessageError = true;
    }

  }
  ValidaDta(dta:any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.dtaInvalida = false;
    }
    else if (patternValidaData.test(dta)) {
      this.dtaInvalida = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.dtaInvalida = true;
    }
    else
      this.dtaInvalida = false
  }
  ValidaUf() {
    this.ufInvalido = false;
    if (this.objGuiaExames.UF19 == undefined || this.objGuiaExames.UF19 == null) {
      this.ufInvalido = true;
    }
  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");
    v = v.replace(/(\d{2})(\d)/, "$1/$2");
    v = v.replace(/(\d{2})(\d)/, "$1/$2");
    (<HTMLInputElement>evento.target).value = v;
  }
  public mascaraText(evento: KeyboardEvent, campo: string) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\d/g, "");

    if (campo == "ConselhoProfissional17")
      this.objGuiaExames.ConselhoProfissional17 = v;
    if (campo == "IndicacaoClinica24")
      this.objGuiaExames.IndicacaoClinica24 = v;
    // (<HTMLInputElement>evento.target).value = v
  }
  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    (<HTMLInputElement>evento.target).value = v
  }


  Nome = new FormControl('', [Validators.required, Validators.required]);
  CodCNES = new FormControl('', [Validators.required, Validators.required]);
  NomeProSolicitante = new FormControl('', [Validators.required, Validators.required]);
  ConsProfi = new FormControl('', [Validators.required, Validators.required]);
  NumConse = new FormControl('', [Validators.required, Validators.required]);
  CodCBOS = new FormControl('', [Validators.required, Validators.required]);
  IndicacaoClin = new FormControl('', [Validators.required, Validators.required]);
  CodProced = new FormControl('', [Validators.required, Validators.required]);
  DtaAssinatura = new FormControl('', [Validators.required, Validators.required]);

  getErrorMessageNome() {
    return this.Nome.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.Nome.hasError('Nome') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageNomeProSolicitante() {
    return this.NomeProSolicitante.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.NomeProSolicitante.hasError('NomeProSolicitante') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageNumConse() {
    return this.NumConse.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.NumConse.hasError('NumConse') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageCodCBOS() {
    return this.CodCBOS.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.CodCBOS.hasError('CodCBOS') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageCodProced() {
    return this.CodProced.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.CodProced.hasError('CodProced') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageCodCNES() {
    return this.CodCNES.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.CodCNES.hasError('CodCNES') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageConsProfi() {
    return this.ConsProfi.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.ConsProfi.hasError('ConsProfi') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageIndicacaoClin() {
    return this.IndicacaoClin.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.IndicacaoClin.hasError('IndicacaoClin') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  getErrorMessageDtaAssinatura() {
    return this.DtaAssinatura.hasError('required') ? 'TELAAGENDA.ERROCAMPO' :
      this.DtaAssinatura.hasError('DtaAssinatura') ? 'TELAAGENDA.ERRONAOEVALIDO' :
        '';
  }
  pacienteValido = false
  ListaMedicos:any = [];
  DadosPacientes:any = [];
  Paciente:any = [];
  Medico:any = []
  NomePaciente: boolean = false;
  Nomemedico: boolean = false;
  CarregaMedicos() {
    this.medicoService.getMedicos(null, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {

      
      
      this.ListaMedicos = retorno

      if (this.ListaMedicos.length == 1) {
        this.Medico = this.ListaMedicos[0].idMedico;

      }
      
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }


  CarregaPacientes() {
    this.pacientesService.GetPacienteAgenda(null, this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      this.DadosPacientes = []
      

      this.DadosPacientes = retorno.filter((c:any) => c.flgInativo != true);

      if (this.DadosPacientes.length == 1)
        this.Paciente = this.DadosPacientes[0].idCliente
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }
}
