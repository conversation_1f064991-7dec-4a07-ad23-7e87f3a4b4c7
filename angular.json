{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"TeleMedicina": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "allowedCommonJsDependencies": ["core-js/modules/es.array.index-of.js", "core-js/modules/es.array.iterator.js", "core-js/modules/es.array.reduce.js", "core-js/modules/es.array.reverse.js", "core-js/modules/es.promise.js", "core-js/modules/es.regexp.to-string.js", "core-js/modules/es.string.ends-with.js", "core-js/modules/es.string.includes.js", "core-js/modules/es.string.match.js", "core-js/modules/es.string.replace.js", "core-js/modules/es.string.split.js", "core-js/modules/es.string.starts-with.js", "core-js/modules/es.string.trim.js", "core-js/modules/web.dom-collections.iterator.js", "raf", "rgbcolor", "html2canvas", "date-fns", "xlsx", "crypto-js", "file-saver"], "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "15mb"}], "serviceWorker": false}, "teste": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.teste.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "15mb"}], "serviceWorker": false}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "100mb"}], "serviceWorker": false}, "dev3": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev3.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "10mb"}], "serviceWorker": false}, "dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "TeleMedicina:build"}, "configurations": {"production": {"buildTarget": "TeleMedicina:build:production"}, "dev": {"buildTarget": "TeleMedicina:build:dev"}}, "defaultConfiguration": "dev"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "TeleMedicina:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "TeleMedicina-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "TeleMedicina:serve"}, "configurations": {"production": {"devServerTarget": "TeleMedicina:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "cli": {"analytics": "dc93708c-e60e-4634-ae7b-373995b5a018"}}