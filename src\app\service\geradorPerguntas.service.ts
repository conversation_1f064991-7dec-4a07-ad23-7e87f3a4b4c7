import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { Observable } from 'rxjs';
import { Perguntas } from '../model/perguntas';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class GeradorPerguntasService {
    constructor(
        private http: HttpClient,
        private spinner : SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });


    public GetPerguntas(inicio:any, fim:any, pesquisa:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('pesquisa', String(pesquisa));
        return this.http.get(environment.apiEndpoint + '/Perguntas/GetPerguntas', { params });
    }
    public getPergunta(): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Perguntas');
    }
    
    public getPerguntaEdit(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Perguntas/' + id);
    }
    public inativarPergunta(id:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Perguntas/' + id + '/' + idUsuario);
    }
    
    public salvarPergunta(perguntas: Perguntas): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Perguntas/SalvarPerguntas/', perguntas);
    }
}
