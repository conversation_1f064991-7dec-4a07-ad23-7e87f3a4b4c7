import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { IntegrationApiExameModelView, LogRequisicaoExameModelView } from '../../../model/IntegrationApiExame';

@Injectable({
  providedIn: 'root',
})
export class IntegrationService {
  // private readonly baseUrl = 'http://localhost:5117/Integration';
  private readonly baseUrl = environment.apiEndpoint + '/Integration';
  private readonly endpointApi = environment.apiEndpoint + '/Integration';

  constructor(
    private readonly http: HttpClient
  ) { }

  //#region Metodos aparelho vittal-tec
  healthCheck(): Observable<any> {
    const url = `${this.baseUrl}/healthcheck`;
    return this.http.get(url, { headers: new HttpHeaders({ accept: '*/*' }) });
  }

  capture(): Observable<any> {
    const url = `${this.baseUrl}/capture`;
    return this.http.post(url, '', { headers: new HttpHeaders({ accept: '*/*' }) });
  }

  read(): Observable<any> {
    const url = `${this.baseUrl}/read`;
    const headers = new HttpHeaders({ accept: 'text/plain' });
    return this.http.post(url, '', { headers });
  }

  switchWindowBrowser(): Observable<any> {
    const url = `${this.baseUrl}/switchWindowBrowser`;
    return this.http.get(url, { headers: new HttpHeaders({ accept: '*/*' }) });
  }
  //#endregion

  //#region Metodos api 
  RegistraLogRequisicao(obj: LogRequisicaoExameModelView): Observable<any> {
    const url = `${this.endpointApi}/RegistraLogRequisicao`;
    return this.http.post(url, obj);
  }

  RegistrarExame(obj: IntegrationApiExameModelView): Observable<any> {
    const url = `${this.endpointApi}/RegistrarExame`;
    return this.http.post(url, obj);
  }
  //#endregion
} 