/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo principal */
.modal-content {
    flex: 1;
    padding: 45px;
    background-color: $secondary-light;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Formulário */
.form-container {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.form-field {
    width: 100%;
    margin-bottom: 16px;
}

/* Input de observação */
.observation-input {
    width: 100%;
}

::ng-deep .observation-input .mat-mdc-form-field-flex {
    background-color: $secondary-light;
}

::ng-deep .observation-input .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
::ng-deep .observation-input .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
::ng-deep .observation-input .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: $border-color;
}

::ng-deep .observation-input .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,
::ng-deep .observation-input .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,
::ng-deep .observation-input .mdc-text-field--focused:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
    border-color: $primary-color;
}

::ng-deep .observation-input textarea {
    resize: none;
}

/* Rodapé */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    background-color: $secondary-color;
    border-top: 1px solid $border-color;
}

/* Botões de ação */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 110px;
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: darken($secondary-dark, 5%);
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

.save-button {
    background-color: $primary-color;
    color: white;
}

.save-button:hover {
    background-color: $primary-dark;
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

/* Responsividade */
@media (max-width: 768px) {
    .modal-content {
        padding: 16px;
    }
    
    .action-button {
        min-width: 0;
        padding: 8px;
    }
    
    .action-button span {
        display: none;
    }
}