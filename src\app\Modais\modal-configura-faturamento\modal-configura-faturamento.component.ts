import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  FormBuilder, FormGroup, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { MatDialog as MatDialog, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { AlertComponent } from 'src/app/alert/alert.component';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { ListaProcedimentoComponent } from 'src/app/lista-procedimento/lista-procedimento.component';
import { ObjFaturamento, ObjFaturamentoLote } from 'src/app/model/fatura';
import { ConsultaService } from 'src/app/service/consulta.service';
import { ControleModaisService } from 'src/app/service/controle-modais.service';
import { ConvenioService } from 'src/app/service/convenio.service';
import { FaturaService } from 'src/app/service/fatura.service';
import { SpinnerService } from 'src/app/service/spinner.service';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';

@Component({
  selector: 'app-modal-configura-faturamento',
  templateUrl: './modal-configura-faturamento.component.html',
  styleUrls: ['./modal-configura-faturamento.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatIcon,
    TranslateModule
  ]
})
export class ModalConfiguraFaturamentoComponent implements OnInit {

  constructor(
    private dialogRef: MatDialogRef<ModalConfiguraFaturamentoComponent>,
    private controleModaisService: ControleModaisService,
    private convenioService: ConvenioService,
    private usuarioLogadoService: UsuarioLogadoService,
    private spinner: SpinnerService,
    private faturaService: FaturaService,
    private consultaService: ConsultaService,
    private matDialog: MatDialog,
    private snackbar: AlertComponent,
    private fb: FormBuilder
  ) { }

  //#region  Variaveis
  formFaturamento!: FormGroup;
  lsConsultas: any[] = [];
  lsFaturas: any[] = [];
  lsConvenios: any[] = [];
  listaIdsConsultas: number[] = [];
  flgSalvo: boolean = true;

  flgCarregamentoGeral: boolean = false;
  idConsulta: number = 0;
  //#endregion


  ngOnInit(): void {
    this.inicializarFormulario();
    this.ListaConvenios();
    this.ListaFaturas();

    this.flgCarregamentoGeral = this.controleModaisService.flgCarreagemntoGeral;
    if (this.flgCarregamentoGeral)
      this.lsConsultas = this.controleModaisService.lsConsultas;
    else {
      this.idConsulta = this.controleModaisService.idConsulta;
      this.carregaConsulta();
    }

    ;
    ;
    ;


  }

  //#region  Forms
  //#region  InicializarFormulario
  inicializarFormulario() {
    this.formFaturamento = this.fb.group({
      idConvenio: ['', Validators.required],
      codigoConvenio: ['', Validators.required],
      idFatura: ['', Validators.required],
      valorConsulta: ['', [Validators.required]]
    });
  }
  //#endregion

  //#region ValidaFormulario
  validarFormulario() {
    if (this.formFaturamento.invalid) {
      this.formFaturamento.markAllAsTouched();
      return false;
    }
    else if (this.listaIdsConsultas.length == 0 && this.flgCarregamentoGeral) {
      this.snackbar.falhaSnackbar("Selecione ao menos uma consulta para faturar!");
      return false;
    }
    else if (!this.flgCarregamentoGeral && this.idConsulta == 0) {
      this.snackbar.falhaSnackbar("Falha ao salvar consulta, tente novamente mais tarde.!");
      return false;
    }
    else
      return true;
  }
  //#endregion
  //#endregion

  //#region  Salvar
  SalvaConsulta() {
    if (!this.validarFormulario()) {
      ;
      return;
    }

    this.spinner.show();



    if (!this.flgCarregamentoGeral) {

      let objFaturamento = new ObjFaturamento();

      objFaturamento.idConsulta = this.idConsulta;
      objFaturamento.idConvenio = this.formFaturamento.value.idConvenio;
      objFaturamento.idFatura = this.formFaturamento.value.idFatura;
      objFaturamento.valorConsulta = this.formFaturamento.value.valorConsulta;
      objFaturamento.codigoConvenio = this.formFaturamento.value.codigoConvenio;

      ;

      // return;

      this.consultaService.SalvarFaturamento(objFaturamento).subscribe(() => {
        this.snackbar.sucessoSnackbar("Faturamento salvo com sucesso!");
        this.spinner.hide();
      }, () => {
        this.snackbar.falhaSnackbar("Erro ao salvar o faturamento!");
        this.spinner.hide();
      });
    }

    else {

      let objFaturamento = new ObjFaturamentoLote();

      objFaturamento.idConvenio = this.formFaturamento.value.idConvenio;
      objFaturamento.idFatura = this.formFaturamento.value.idFatura;
      objFaturamento.valorConsulta = this.formFaturamento.value.valorConsulta;
      objFaturamento.codigoConvenio = this.formFaturamento.value.codigoConvenio;

      objFaturamento.idsConsulta = this.listaIdsConsultas;


      ;

      // return

      this.consultaService.SalvarFaturamentoLote(objFaturamento).subscribe(() => {
        this.snackbar.sucessoSnackbar("Faturamento salvo com sucesso!");
        this.spinner.hide();
      }, () => {
        this.snackbar.falhaSnackbar("Erro ao salvar o faturamento!");
        this.spinner.hide();
      });
    }

  }
  //#endregion



  //#region  Consultas
  //#region  Carrega Consulta
  carregaConsulta() {
    this.spinner.show();
    this.consultaService.CarregaFaturamento(this.idConsulta).subscribe((retorno) => {
      // alert("carregaConsulta() | ok");
      this.formFaturamento.patchValue({
        idConvenio: retorno.idConvenio,
        codigoConvenio: retorno.codigoConvenio,
        idFatura: retorno.idFatura,
        valorConsulta: retorno.valorConsulta
      });


      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }
  //#endregion

  //#region  Carrega Listas
  //#region  ListaConvenios
  ListaConvenios() {
    this.spinner.show();
    this.convenioService.getConvenios(this.usuarioLogadoService.getIdUltimaClinica()).subscribe((retorno) => {
      // alert("ListaConvenios() | ok");
      this.lsConvenios = retorno;
      this.spinner.hide();
    },
      () => {
        this.spinner.hide();
      })
  }
  //#endregion
  //#region ListaFaturas
  ListaFaturas() {
    this.spinner.show();
    this.faturaService.GetListaFatura(false).subscribe((retorno) => {
      this.lsFaturas = retorno;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }
  //#endregion
  //#endregion
  //#endregion



  AbrirModalListaProcedimento() {
    if (!this.validarFormulario())
      return;

    this.matDialog.open(ListaProcedimentoComponent, {
      width: '710px',
      height: '90vh',
      data: {
        idConsulta: this.idConsulta,
        idConvenio: this.formFaturamento.value.idConvenio,
        idFatura: this.formFaturamento.value.idFatura,
      }
    });
  }
  AlteraStatusConsultaLista(idConsulta: number) {
    const index = this.listaIdsConsultas.indexOf(idConsulta);
    if (index > -1)
      this.listaIdsConsultas.splice(index, 1);
    else
      this.listaIdsConsultas.push(idConsulta);


    ;
    ;
  }

  fecharModal() {
    this.dialogRef.close();
  }

  PreencheValorPagamento() {
    this.formFaturamento.patchValue({
      valorConsulta: this.lsConvenios.filter(x => x.id == this.formFaturamento.value.idConvenio)[0].valor
    });
  }

  ValidaUsuarioSelecionado(idConsulta: number): boolean {
    return this.listaIdsConsultas.includes(idConsulta);
  }
}


