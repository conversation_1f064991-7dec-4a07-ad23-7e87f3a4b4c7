<div class="calendar-container">
    <div class="calendar-controls">
        <button *ngIf="!flgExibirCalendario" class="btn btn-switch" 
            (click)="flgExibirCalendario = !flgExibirCalendario; this.gerarCalendario();">
            <i class="icon-calendar"></i>
            <span>Visualização mensal</span>
        </button>
        <button *ngIf="flgExibirCalendario" class="btn btn-switch"
            (click)="flgExibirCalendario = !flgExibirCalendario;">
            <i class="icon-agenda"></i>
            <span>Visualização diária</span>
        </button>
        <button class="btn btn-today" (click)="bt_DataAtual()">Hoje</button>
    </div>
    
    <!-- Visualização do calendário mensal -->
    <div class="calendar" *ngIf="flgExibirCalendario">
        <div class="calendar-header">
            <button class="nav-btn" (click)="bt_AlterarMes(false)">
                <i class="icon-chevron-left"></i>
            </button>
            <h2 class="calendar-title">{{TituloCalendario}}</h2>
            <button class="nav-btn" (click)="bt_AlterarMes(true)">
                <i class="icon-chevron-right"></i>
            </button>
        </div>
        
        <div class="calendar-weekdays">
            <div class="weekday" *ngFor="let dia of diasSemana">{{ dia }}</div>
        </div>
        
        <div class="calendar-days">
            <div class="day" 
                 *ngFor="let dia of diasDoMes" 
                 (click)="selecionarDia(dia)"
                 >
                <span class="day-number">{{ dia.data.getDate() }}</span>
                
                <div class="events-indicator">
                    <span class="event-dot"
                          *ngFor="let tipo of getTiposAgendamentoLimitado(dia.agendamentos)" 
                          [ngStyle]="{ 'background-color': getCorAgendamento(tipo) }" 
                          data-bs-toggle="tooltip"
                          data-placement="top"
                          [attr.title]="dia.agendamentos[tipo].descricao">
                    </span>
                    
                    <span *ngIf="getAgendamentosExtras(dia.agendamentos) > 0" 
                          class="events-more"
                          data-bs-toggle="tooltip" 
                          data-placement="top" 
                          [attr.title]="getTooltipExtras(dia.agendamentos)">
                        +{{ getAgendamentosExtras(dia.agendamentos) }}
                    </span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Visualização da agenda diária -->
    <div class="schedule" *ngIf="!flgExibirCalendario">
        <div class="calendar-header">
            <button class="nav-btn" (click)="bt_AlterarDia(false)">
                <i class="icon-chevron-left"></i>
            </button>
            <h2 class="calendar-title">{{TituloCalendario}}</h2>
            <button class="nav-btn" (click)="bt_AlterarDia(true)">
                <i class="icon-chevron-right"></i>
            </button>
        </div>
        
        <div class="schedule-content">
            <div class="time-column">
                <div class="time-slot" *ngFor="let hora of horasDoDia">
                    <span class="time-label">{{hora}}</span>
                </div>
            </div>
            
            <div class="events-column">
                <div class="time-slot" *ngFor="let hora of horasDoDia" (click)="onHorarioClicado(hora)">
                    <div class="time-grid">
                        <div class="hour-line"></div>
                        <div class="half-hour-line"></div>
                    </div>
                    
                    <div class="events-wrapper">
                        <div class="event" 
                             *ngFor="let evento of getEventosProcessadosPorHora(hora)"
                             [ngStyle]="getEstiloEvento(evento)" 
                             [attr.data-id]="evento.IdAgendamento"
                             data-bs-toggle="tooltip" 
                             data-placement="top" 
                             [attr.title]="evento.DescAgendamento"
                             (click)="onEventoClicado(evento, $event)">
                            
                            <ng-container *ngIf="evento.isPrimary">
                                <div class="event-time">
                                    {{formatarHora(evento.horaInicioAgendamento)}} - 
                                    {{formatarHora(evento.horaFinalAgendamento)}}
                                </div>
                                <div class="event-title">{{ evento.DescAgendamento }}</div>
                            </ng-container>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>