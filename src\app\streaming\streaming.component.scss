// Variáveis de cores padrão
$primary-color: #2E8B57; // Verde mar
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9; 
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834; // Tom mais escuro de verde
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;
.btn-primary{
  background-color: #175834 !important;
}
// Outras variáveis do sistema original adaptadas
$md-chip-height: 40px;
$md-chip-color: #e0e0e0;

@mixin justify-center {
  justify-content: center;
  display: flex;
}

html {
  scroll-behavior: smooth;
}

// Classes de estilo para avaliação
.custom-span-Mobile {
  font-size: 12px;
  line-height: 30px;
  font-weight: 600;
  color: $text-secondary;
}

.logo-medicina-modal {
  max-width: 100%;
  margin: 0 auto;
  text-align: center;
  justify-content: center;
}

.logo-modal-footer {
  max-width: 300px; 
  height: 35px;  
  margin: 0 auto;  
  margin-top: 10px;
  margin-bottom: 20px;
}

#avaliacaoMobile {
  margin: 0 auto; 
  justify-content: center; 
  text-align: left; 
  margin-left: 15px;
}

.div-aval-mobile {
  max-width: 320px; 
  padding: 10px; 
  margin: 0 auto;
  justify-content: center;
}

.div-row-aval {
  padding-bottom: 30px; 
  text-align: center; 
  justify-content: center;
}

.modal-avaliacao {
  justify-content: center;
}

.title-aval-modal {
  text-align: center;
  justify-content: center;
  margin: 0 auto;
}

.card-aval-desk {
  padding-left: 15px;
  text-align: left !important;
}

// Linhas separadoras
hr.sep-1 {
  border: 0; 
  margin-bottom: 0 !important;
  height: 4px; 
  width: 100%;
  background-image: linear-gradient(to right, #fff, $primary-color, $primary-color, #fff);
  border-radius: 10px;
  margin-top: 0;
}

hr.sep-2 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  border: 0;
  height: 2px;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  background-image: linear-gradient(to right, #ffffff, $primary-color, $primary-color, #ffffff);
}

hr.sep-3 {
  border: 0;
  margin-bottom: 0 !important;
  margin-top: 0 !important;
  height: 3px;
  width: 100%;
  background-image: linear-gradient(to right, #ffffff, $primary-color, $primary-color, #ffffff);
}

hr.sep-4 {
  border: 0;
  margin-bottom: 2px !important;
  margin-top: 0 !important;
  height: 1px;
  width: 100%;
  background-image: linear-gradient(to right, #ffffff, $primary-dark, $primary-dark, #ffffff);
}

hr.sep-5 {
  border: 0;
  height: 2px;
  width: 100%;
  background-image: linear-gradient(to right, #ffffff, $primary-dark, $primary-dark, #ffffff);
}

.btns-prontuario small {
  font-weight: 500;
  font-family: Cairo, sans-serif;
  font-size: 0.8rem;
  color: $text-primary;
}

// Classes de layout e containers
.camera-ver {
  width: 106% !important;
  margin-left: -7px;
}

.padding-consultas {
  padding-right: unset;
}

.pad-form-campo {
  padding-left: unset;
  padding-right: 20px;
}

.pad-form {
  margin-left: unset;
  padding-right: 10px;
}

.botoes {
  padding: unset;
  display: flex;
}

.colunasem {
  padding-right: unset;
}

.coluna-consulta {
  overflow-y: auto !important;
  height: -webkit-fill-available !important;
  overflow-x: hidden;
  height: 43vh !important;
}

.finalizar {
  float: right;
}

.coluna-maior {
  background-color: $card-bg;
  right: -15px;
  height: 34vh;
  padding: unset;
  left: -1px;
}

.textarea-layout2 {
  min-height: 28vh;
  max-height: 30vh;
}

.carregar-arquivo {
  margin-top: 7vh;
}

.Title-modaloff {
  position: absolute;
  margin-top: -140px;
  left: 30px;
  font-weight: bold;
  color: white;
}

.watermark {
  display: none;
}

.lay_cellphone {
  font-size: 5rem;
  width: auto;
  height: auto;
  margin-top: 10px;
  margin-bottom: 10px;
  animation: Rotate 3s infinite;
  font-size: 6rem;
}

@keyframes Rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(135deg);
  }
}

@keyframes slidein {
  0% {
    margin-top: 1500px;
  }
  100% {
    margin-top: 0px;
  }
}

// Botões e controles
.close-button {
  margin-left: auto;
  margin-right: auto;
  transition: all 0.5s;
}

.close-buttonrotate {
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  background-color: white;
  color: $primary-color;
  border-radius: 5px;
}

.close-iconrotate {
  -ms-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
  color: $primary-color;
}

// Painéis e menus
.panel_principal {
  transition: all 0.5s;
}

.panel-menu {
  transition: all 0.5s;
  animation-name: slidein;
}

.d-none {
  display: none;
  transition: all 0.5s;
  animation-name: slidein;
}

.manha {
  width: 95%;
  background: white;
  margin-left: 10px;
}

.align-btn {
  font-size: 12px;
}

.color-white {
  color: white;
}

.mt-10 {
  margin-top: 10px;
}

.drawer-height {
  height: 42.35vw;
}

// Áreas de texto e inputs
.background-anom {
  width: 100%;
  height: 270px;
  background-color: rgba(23, 88, 52, 0.6);
  background-image: url(/assets/build/img/incognito.svg);
  background-repeat: no-repeat;
  color: white;
  background-position: center;
  background-size: 80px;
  text-align: left;
  padding: 10px;
  max-height: 400px;
}

/* Material Design adaptado */
.mat-form-field-appearance-legacy .mat-form-field-label {
  color: white;
  margin-top: 5px;
  margin-left: 10px;
}

.background-hist {
  background: $primary-color;
  border-radius: 15px;
  border: 0px;
  color: white;
  cursor: pointer;
  font-size: 11.2px;
  padding: 7px;
  display: block;
  word-break: break-word;
  margin-top: 30px;
}

.date-archive {
  font-size: 12px;
  background: $primary-color;
  box-shadow: $box-shadow;
  color: white !important;
  border-radius: 15px;
  text-align: center;
  padding: 7px;
  transition: all 0.5s;
  margin-top: 35px;
  color: white;
  cursor: pointer;
  margin-left: auto;
  margin-right: auto;
  width: 120px;
}

.bodered-collapse {
  border: 1px solid #c3c1c1;
  margin-top: 10px;
}

.date-archive:hover {
  box-shadow: unset;
  transition: all 0.5s;
}

.date-archive::after {
  content: "";
  border: 1px solid $primary-dark;
  height: 42px;
  background: $primary-dark;
  position: absolute;
  color: #fff;
  z-index: 0;
  margin-left: -50px;
  margin-top: 20px;
  text-align: center;
}

// Containers para webcam e conteúdo
.content-webcam {
  background: rgba(0, 0, 0, 0.62);
  height: 180px;
  padding-bottom: 35px;
}

.content-webcamI {
  background: rgba(0, 0, 0, 0.62);
  height: 192px;
}

.content-space {
  padding-left: 40px;
  padding-right: 40px;
}

.webcam-container {
  height: 40vh;
}

.hist-content {
  height: 87vh;
  overflow: auto;
}

.card-bottom {
  height: 54.5vh;
}

.camera {
  padding: 0;
  height: 100%;
  width: 85% !important;
  padding-left: 10px;
  margin-top: unset;
}

.align-button {
  margin-left: 21px;
  margin-right: 76px;
}

.custom-modal {
  margin-top: 67px !important;
  height: 550px !important;
}

.Drawer-esquerdo {
  width: 100%;
  height: 60vw;
  @media (max-width: 1000px){
    height: 91vh !important;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
  }
}

.align-buttonII {
  margin-left: -10px;
  margin-right: 74px;
}

.drawer-height {
  height: 90vh;
}

.hist_chat {
  overflow: auto;
  height: 22vw;
}

.anon_anot {
  overflow: auto;
  height: 22vw;
}

.limitador_height {
  height: 75vh;
  overflow: auto;
}

.Selo-align {
  position: absolute;
  right: 289px;
  margin-top: -127px;
  color: white;
  font-size: 20px;
}

// Itens e elementos interativos
.hovered-item {
  padding: 10px;
}

.subject-text {
  vertical-align: middle;
  display: none !important;
}

.participants-count {
  background: #fff;
  border-radius: 4px;
  color: #5e6d7a;
  cursor: pointer;
  display: inline-block;
  font-size: 13px;
  line-height: 20px;
  margin-left: 16px;
  padding: 4px 8px;
  pointer-events: auto;
  display: none !important;
}

.hovered-item:hover {
  background: rgba($primary-color, 0.15);
}

.Drawer-esquerdo {
  width: 100%;
  height: 90.8vh;
}

.font-anom {
  color: $text-primary;
}

.card-anot {
  box-shadow: $box-shadow;
  border-radius: $border-radius;
  padding: 10px;
  border: 1px solid $border-color;
  margin-top: 10px;
  margin-bottom: 10px;
}

.mini-mini {
  width: 10px;
  height: 10px;
  background: $primary-color;
}

.open_aba {
  position: absolute;
  z-index: 2000;
  padding: 10px;
  line-height: 9px;
  color: white;
  right: 0px;
  top: 0;
  cursor: pointer;
  background: $primary-color;
  margin-top: 15px;
  border-bottom-left-radius: 20px;
  border-top-left-radius: 20px;
  font-weight: 700;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
  padding-left: 15px;
  transition: all 1s;
}

.open_aba_Geral {
  position: absolute;
  z-index: 2000;
  line-height: 9px;
  color: white;
  right: 0px;
  top: 0;
  cursor: pointer;
  background: $primary-color;
  margin-top: 15px;
  border-bottom-left-radius: 20px;
  border-top-left-radius: 20px;
  font-weight: 700;
  transition: all 1s;
}

input[type="file"] {
  display: none;
}

.aberto2 {
  text-align: center;
  margin-top: 25%;
}

.aberto {
  text-align: center;
  margin-top: 8%;
}

.fechado {
  text-align: center;
  margin-top: 5%;
}

.open_aba:hover {
  transition: all 1s;
  padding: 20px;
}

.mini-miniI {
  margin-right: 109px;
}

.mini-miniII {
  margin-right: 77px;
}

.mini-miniIII {
  margin-right: 110px;
}

.bordered-content {
  height: 40vh;
  border: 3px solid rgba(0, 0, 0, 0.54);
}

.img-webcam {
  width: 35%;
  margin-top: auto;
  margin-bottom: auto;
  margin-left: auto;
  margin-right: auto;
  padding-top: 40px;
  opacity: 0.5;
}

.Menu_video {
  background: #292d38;
}

.menu_panel {
  color: white;
  margin-top: 10px;
  margin-bottom: 10px;
}

.panel_footer {
  bottom: 25px;
  position: absolute;
}

.anexo-rotate {
  transform: rotate(240deg);
}

pdf-viewer {
  display: block;
  height: 100vh;
  max-height: 70vh;
  overflow: auto;
}

.red {
  width: 10px;
  height: 10px;
  background: #dc0606;
  margin-top: 10px;
  margin-bottom: auto !important;
  margin-right: 5px;
  margin-left: 8px;
  border-radius: 50%;
}

.rec {
  position: absolute;
  color: white;
  z-index: 100;
  display: flex;
  top: 20px;
  left: 0px;
  border: 1px solid #ffffff;
  border-radius: 10px;
  padding-top: 9px;
  width: 100px;
  text-align: center;
  background: $primary-dark;
}

.usuario-principal {
  color: $text-secondary;
  padding-bottom: 10px;
  margin-left: 10px;
  font-weight: 700;
  padding-top: 15px;
}

.paciente {
  background: $primary-color;
  border-radius: 50%;
  width: 45px;
  height: 40px;
  font-size: 20px !important;
  padding-left: 13px;
  padding-right: 15px;
  padding-bottom: 15px;
  padding-top: 9px;
  margin-top: 10px;
  color: WHITE;
  cursor: pointer;
}

mat-drawer-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.h-32 {
  height: 32px;
}

.div-usuarios-ativos {
  height: 170px;
}

.mat-drawer-inner-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-image: none !important;
  background-color: $primary-color;
  -webkit-overflow-scrolling: touch;
}

.non-active {
  animation: shadow-pulse 1s infinite;
}

.img-layout-inicial:hover {
  border: 3px solid $primary-color;
  transform: scale(1);
}

.img-content:hover {
  border: 5px solid $primary-color;
  transition: all 0.2s;
}

.img-content {
  margin-left: 20px;
  margin-top: 25px;
  width: 100px;
  transition: all 0.2s;
}

@keyframes shadow-pulse {
  0% {
    box-shadow: 0 0 0 0px rgba(0, 0, 0, 0.2);
  }
  100% {
    box-shadow: 0 0 0 35px rgba(0, 0, 0, 0);
  }
}

.align-chip {
  margin-top: 10px;
  margin-bottom: 10px;
}

.users-on {
  border: 1px solid $border-color;
  background: #fff;
  width: 100%;
  height: 140px;
  box-sizing: content-box;
  display: flex;
  margin-right: auto;
  margin-left: auto;
}

.user-ativo {
  height: 32px;
  width: 100px !important;
}

.webcam-little {
  background: #000;
  height: 180px;
}

.background-anom {
  width: 100%;
  min-height: 180px;
  height: 270px;
  background-color: rgba(23, 88, 52, 0.6);
  background-image: url(/assets/build/img/incognito.svg);
  background-repeat: no-repeat;
  color: white;
  background-position: center;
  background-size: 160px;
  text-align: left;
  padding: 10px;
}

.pill-content {
  padding-bottom: 14px;
  z-index: 200;
  margin-right: 10px;
  margin-top: 13vh;
}

.principal_content {
  width: 100%;
  height: 100%;
  background-color: rgba(41, 45, 56, 0.75);
  color: white;
}

.color-anon {
  background: #87898b;
}

.panel_principal {
  width: 375px;
  border: 1px solid #eee;
}

.panel_historico {
  width: 460px;
  border: 1px solid #eee;
}

// Chips e elementos de interface
.md-chip {
  display: inline-block;
  background: $md-chip-color;
  padding: 0 12px;
  border-radius: 32px;
  font-size: 13px;
  
  &.md-chip-hover:hover {
    background: #ccc;
  }
}

.background-anom2 {
  background-size: 75px;
}

.background-consulta {
  min-height: 180px;
  max-height: 350px;
}

.md-chip-clickable {
  cursor: pointer;
}

.md-chip,
.md-chip-icon {
  height: $md-chip-height;
  line-height: 17px;
  padding-top: 4px;
}

.md-chip-icon {
  display: block;
  float: left;
  background: $primary-dark;
  width: $md-chip-height;
  border-radius: 50%;
  text-align: center;
  color: white;
  margin: 0 8px 0 -12px;
}

.md-chip-remove {
  display: inline-block;
  background: #aaa;
  border: 0;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  padding: 0;
  margin: 0 -4px 0 4px;
  cursor: pointer;
  font: inherit;
  line-height: 20px;
  
  &:after {
    color: $md-chip-color;
    content: "x";
  }
  
  &:hover {
    background: #999;
  }
  
  &:active {
    background: #777;
  }
}

.md-chips {
  padding: 12px 0;
  
  .md-chip {
    margin: 0 5px 3px 0;
  }
}

.md-chip-raised {
  box-shadow: $box-shadow;
}

.panel-button {
  margin-right: 10px;
  margin-left: 10px;
  border: 1px solid $border-color;
}

.Menu_esquerdo {
  position: absolute;
  top: 20%;
  display: grid;
  border-radius: 10px;
  padding: 10px;
  right: 0;
}

.dashed-content {
  border: 2px dashed #8e8e8e;
  padding-bottom: 40px;
  margin-top: 15px;
  margin-bottom: 10px;
  margin-right: 5px;
}

.Medicine-content {
  padding-top: 10px;
  height: 125px;
  overflow: auto;
  margin-top: 25px;
  padding-bottom: 10px;
  border: 1px solid $border-color;
  margin-bottom: 10px;
}

.tex-area-archives {
  border: 1px solid $border-color;
  height: 180px;
  padding: 10px;
  box-shadow: $box-shadow;
}

.textarea-placeholder {
  padding: 10px !important;
}

.Menu_esquerdoI {
  position: absolute;
  top: 39%;
  display: grid;
  border-radius: 10px;
  padding: 10px;
  z-index: 10;
}

.align-video {
  top: 20%;
  right: 20%;
}

.bordered-bottom {
  border-bottom: 1px solid #fff;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.menu-item:hover {
  background: rgba(30, 46, 88, 0.72);
}

.custom-span {
  font-size: 12px;
  line-height: 30px;
  font-weight: 600;
  color: $text-secondary;
}

// Componentes de mensagens e chat
.balao {
  background: $primary-color;
  border-radius: 15px;
  border-top-left-radius: 12px;
  position: relative;
  min-height: 44px;
  margin-top: 10px;
  margin-bottom: 5px;
  
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    border-left: 1px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid $primary-color;
    bottom: 22.5px;
    left: -6%;
    transform: rotate(90deg);
  }
}

.balao2 {
  background: $primary-color;
  border-radius: 15px;
  border-top-left-radius: 12px;
  position: relative;
  min-height: 54px;
  float: right;
  
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    border-left: 1px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid $primary-color;
    bottom: 24px;
    left: 96%;
    -webkit-transform: rotate(90deg);
    transform: rotate(360deg);
  }
}

.balao3 {
  background: $primary-color;
  border-radius: 15px;
  border-top-left-radius: 12px;
  position: relative;
  min-height: 54px;
  
  &:after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    border-left: 1px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid $primary-color;
    bottom: 23.5px;
    left: -4%;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}

.card-anonimo {
  height: unset !important;
  margin-bottom: 15px;
}

.chat {
  margin-top: auto;
  margin-bottom: auto;
}

.card {
  height: 500px;
  border-radius: $border-radius !important;
  background-color: white !important;
}

.contacts_body {
  padding: 0.75rem 0 !important;
  overflow-y: auto;
  white-space: nowrap;
}

.msg_card_body {
  overflow-y: auto;
}

.card-header {
  border-radius: 15px 15px 0 0 !important;
  border-bottom: 0 !important;
}

.card-footer {
  border-radius: 0 0 15px 15px !important;
  border-top: 0 !important;
}

.container {
  align-content: center;
}

.search {
  border-radius: 15px 0 0 15px !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 0 !important;
  color: white !important;
  
  &:focus {
    box-shadow: none !important;
    outline: 0px !important;
  }
}

.texto {
  color: white;
}

.icone {
  color: white;
}

.type_msg {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 0 !important;
  color: white !important;
  height: 60px !important;
  overflow-y: auto;
  
  &:focus {
    box-shadow: none !important;
    outline: 0px !important;
  }
}

.Historico_Colap {
  border-radius: 15px !important;
  background-color: $primary-color;
  border: 0 !important;
  color: white !important;
  white-space: pre-wrap;
  cursor: pointer;
  font-size: 14px;
  display: block !important;
}

.attach_btn {
  border-radius: 15px !important;
  border: 0 !important;
  color: white !important;
  white-space: pre-wrap;
  cursor: pointer;
  font-size: 14px;
  display: block !important;
}

.send_btn {
  border-radius: 0 15px 15px 0 !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 0 !important;
  color: white !important;
  cursor: pointer;
}

.search_btn {
  border-radius: 0 15px 15px 0 !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 0 !important;
  color: white !important;
  cursor: pointer;
}

.contacts {
  list-style: none;
  padding: 0;
  
  li {
    width: 100% !important;
    padding: 5px 10px;
    margin-bottom: 15px !important;
  }
}

.active {
  background-color: rgba(0, 0, 0, 0.3);
}

.user_img {
  height: 70px;
  width: 70px;
  border: 1.5px solid #f5f6fa;
}

.user_img_msg {
  height: 40px;
  width: 40px;
  border: 1.5px solid #f5f6fa;
}

.img_cont {
  position: relative;
  height: 70px;
  width: 70px;
}

.img_cont_msg {
  height: 40px;
  width: 40px;
}

.online_icon {
  position: absolute;
  height: 15px;
  width: 15px;
  background-color: #4cd137;
  border-radius: 50%;
  bottom: 0.2em;
  right: 0.4em;
  border: 1.5px solid white;
}

.offline {
  background-color: $error-color !important;
}

.user_info {
  margin-top: auto;
  margin-bottom: auto;
  margin-left: 15px;
  
  span {
    font-size: 20px;
    color: white;
  }
  
  p {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
  }
}

.video_cam {
  margin-left: 50px;
  margin-top: 5px;
  
  span {
    color: white;
    font-size: 20px;
    cursor: pointer;
    margin-right: 20px;
  }
}

.msg_cotainer {
  margin-top: auto;
  margin-bottom: auto;
  margin-left: 10px;
  border-radius: 25px;
  background-color: $primary-light;
  padding: 10px;
  position: relative;
}

.msg_cotainer_send {
  margin-top: auto;
  margin-bottom: auto;
  margin-right: 10px;
  border-radius: 25px;
  background-color: $primary-color;
  padding: 10px;
  position: relative;
  color: white;
}

.msg_time {
  position: absolute;
  left: 0;
  bottom: -15px;
  color: $text-primary;
  font-size: 10px;
}

.msg_timeAnonimo {
  position: absolute;
  left: 0;
  margin-top: 5px;
  color: white;
  font-size: 9px;
  padding-right: unset;
}

.msg_nome {
  color: white;
  font-size: 9px;
  padding: unset;
  margin-top: 5px;
  float: right;
}

.msg_time_send {
  position: absolute;
  right: 0;
  bottom: -15px;
  color: $text-primary;
  font-size: 10px;
}

.msg_head {
  position: relative;
}

#action_menu_btn {
  position: absolute;
  right: 10px;
  top: 10px;
  color: white;
  cursor: pointer;
  font-size: 20px;
}

.action_menu {
  z-index: 1;
  position: absolute;
  padding: 15px 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 15px;
  top: 30px;
  right: 15px;
  display: none;
  
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    
    li {
      width: 100%;
      padding: 10px 15px;
      margin-bottom: 5px;
      
      i {
        padding-right: 10px;
      }
      
      &:hover {
        cursor: pointer;
        background-color: rgba(0, 0, 0, 0.2);
      }
    }
  }
}

.title-p {
  margin: unset;
  color: white;
  text-align: center;
  font-size: 14px;
}

.pontilhado {
  margin-top: 5px;
  margin-bottom: 5px;
  border: 0;
  border-top: 2px dotted white;
}

// Botões e elementos de interface para avaliação
.estrelas input[type="radio"] {
  display: none;
}

.estrelas label i.fa:before {
  content: "\f005";
  color: #fc0;
}

.estrelas input[type="radio"]:checked ~ label i.fa:before {
  color: #ccc;
}

// Botões alternância ativo/inativo
.BotaoAtivo {
  background-color: $primary-color !important;
  box-sizing: border-box;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  outline: 0;
  border: none;
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  white-space: nowrap;
  text-decoration: none;
  vertical-align: baseline;
  text-align: center;
  margin: 0;
  min-width: 64px;
  line-height: 36px;
  padding: 0 16px;
  border-radius: 4px;
  overflow: visible;
  transform: translate3d(0, 0, 0);
  transition: background 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  flex-shrink: 0;
  padding-top: 13%;
}

.Botaoinativo {
  box-sizing: border-box;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
  outline: 0;
  border: none;
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  white-space: nowrap;
  text-decoration: none;
  vertical-align: baseline;
  text-align: center;
  margin: 0;
  min-width: 64px;
  line-height: 36px;
  padding: 0 16px;
  border-radius: 4px;
  overflow: visible;
  transform: translate3d(0, 0, 0);
  transition: background 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
    box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  padding: 0;
  flex-shrink: 0;
  padding-top: 13%;
}

// Layout móvel
.VideoMobileOpenMenu {
  height: 54vh !important;
}

.VideoMobileCloseMenu {
  height: 85vh !important;
}

// Containers específicos
.content {
  z-index: 1060;
  width: 530px;
  background-color: $card-bg;
  padding: 25px;
  border-radius: $border-radius;
}

mat-form-field {
  height: auto !important;
}

.FinalizarContanier {
  width: 519px;
  background-color: $card-bg;
  border-radius: 0 0 15px 15px;
  @media (max-width: 1000px) {
    width: 300px;
  }
}

.ajuste-icon {
  vertical-align: middle;
}

.aparece {
  @media (max-width: 425px) {
    display: none;
  }
}

.fonte {
  font-size: 13px;
  
  @media (max-width: 425px) {
    font-size: 12px;
  }
}

.background-off {
  background-image: url(/assets/build/img/off-camera.jpg);
  background-size: cover;
  background-position: center;
  height: 200px;
  border-radius: 10px 10px 0 0;
  
  @media (max-width: 600px) {
    display: none;
  }
}

// Media queries responsivas
@media (min-width: 1680px) {
  .Drawer-esquerdo {
    height: 45.6vw;
  }
}

@media (min-width: 1480px) {
  .panel_menu {
    height: 5vw;
  }

  .background-padr {
    height: 555px;
  }

  .background-anom {
    height: 610px;
    max-height: 610px !important;
  }

  .background-consulta {
    height: 610px;
    max-height: 610px !important;
  }
}

@media (max-width: 1370px) {
  .background-padr {
    height: 290px;
  }
  
  .background-anom {
    height: 250px;
  }
  
  .background-consulta {
    height: 256px;
  }
}

@media (max-width: 1400px) and (min-width: 1373px) {
  .align-chip {
    margin-top: 8vh;
  }

  .background-padr {
    height: 210px;
  }

  .panel_menu {
    height: 6vw;
  }

  .div-usuarios-ativos {
    height: 12vw;
  }

  .background-anom {
    height: 305px;
    padding: 15px;
  }

  .background-consulta {
    height: 300px;
    max-height: 330px;
    padding: 15px;
  }

  .drawer-height {
    height: 103%;
  }
}

@media (min-width: 1500px) {
  .align-button {
    padding-right: 0px;
    margin-left: 60px;
    margin-right: 37px;
  }

  .align-buttonII {
    padding-right: 0px;
    margin-left: 27px;
    margin-right: 25px;
  }
}

@media (max-width: 1024px) {
  .Drawer-esquerdo {
    width: 100%;
    height: 54vw;
  }

  .div-usuarios-ativos {
    height: 17vw;
  }

  .background-consulta {
    max-height: 242px;
  }

  .background-padr {
    height: 188px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .background-anom {
    height: 367px;
    max-height: 240px !important;
    padding: 15px;
  }
}

@media (min-width: 768px) {
  .pad-desk {
    padding-right: unset;
  }
  
  .no-desktop {
    display: none;
  }
}

@media (max-width: 768px) {
  .Drawer-esquerdo {
    width: 100%;
    height: 52rem;
  }

  .background-login {
    height: 70vw;
  }
  
  .no-mobile {
    display: none;
  }
}

@media (max-width: 710px) {
  .Drawer-esquerdo {
    height: 38rem;
  }
}

@media (max-width: 700px) {
  .contacts_card {
    margin-bottom: 15px !important;
  }

  .keyboard-mobile {
    position: absolute;
    right: 0px;
  }

  .keyboard-mobile button {
    background-color: white !important;
    color: $primary-color !important;
  }
  
  .button-fontm {
    font-size: 0.7rem;
  }

  .camera {
    max-height: 100%;
    height: unset !important;
    min-height: 20vh;
    width: 100vw !important;
    padding-left: 0px;
  }

  .principal_content {
    height: 100%;
  }

  .content-mobile {
    overflow: auto;
    height: 15vh;
  }

  .Menu_esquerdo {
    display: none;
  }

  .panel_mobile {
    height: 45px;
    background: #fff;
    color: #000;
    text-align: center;
    padding: 5px;
    border: 1px solid #e2e8f0;
    position: absolute;
    width: 86vw;
    margin-left: 14vw;
    bottom: 0;
  }

  .Mobiletoggle {
    width: 100%;
    height: 30px;
    border-bottom: 1px solid $border-color;
  }
  
  .buttons_mobile {
    @include justify-center();
    margin: 0;
    margin-top: 10px;
    gap: 8px;
  }

  .background-anom {
    min-height: 115px;
    height: 95px;
    background-size: 70px;
  }

  .background-consulta {
    min-height: 105px;
    height: 105px;
  }
}

@media (max-width: 749px) {
  .manha {
    width: 95%;
    background: white;
    margin-left: 10px;
  }
}

@media (min-height: 450px) {
  .toggle-mobi {
    display: none;
  }
}

@media (max-height: 450px) {
  .background-anom {
    min-height: 140px;
  }

  .Drawer-esquerdo {
    height: 82vh;
  }

  .hight-content {
    font-size: 0.8rem !important;
  }

  .lay_cellphone {
    font-size: 5rem;
  }

  .big-btn {
    width: 35%;
  }

  .Menu_esquerdo {
    display: none;
  }

  .content-mobile {
    height: 66vh;
  }

  .camera {
    max-height: 80vh;
    height: unset !important;
    min-height: 70vh;
    width: 100vw !important;
    padding-left: 0px;
  }

  .panel_mobile {
    height: 45px;
    background: #fff;
    color: #000;
    text-align: center;
    padding: 5px;
    border: 1px solid $border-color;
    position: absolute;
    width: 100%;
    bottom: 0;
  }

  .buttons_mobile {
    justify-content: center;
    display: flex;
  }
}

@media (max-width: 425px) {
  .Title-modaloff {
    position: unset;
    margin-top: unset;
    left: unset;
    font-weight: 700;
    color: $text-secondary;
    text-align: center;
    margin-top: 20px;
  }
  
  .pad-historico {
    padding-right: 15px;
  }
  
  .Drawer-esquerdo {
    height: 90vh;
  }

  .bordered-content {
    height: 24vh;
    margin-bottom: 10px;
  }

  .img-webcam {
    width: 30%;
  }
  
  .botoes {
    display: flex;
  }
  
  .coluna-maior {
    left: 0px;
    right: 0px;
    padding-right: unset;
  }
  
  .colunasem {
    padding: unset;
  }
  
  .finalizar {
    padding-left: 20px;
    float: unset;
  }
  
  .margem-form {
    padding-right: 5px;
    padding-left: 4px;
  }
  
  .margem-form2 {
    margin: unset;
    padding: unset;
  }
  
  .pad-form {
    padding-right: unset;
    padding-left: 15px;
  }
  
  .panel_button {
    margin-left: 10px;
  }
  
  .camera {
    width: 110% !important;
    margin-left: 0px;
  }
  
  .pad-desk {
    padding-right: 0px;
  }
  
  .pad-form-campo {
    padding-left: unset;
    padding-right: 0px;
  }
  
  .pad-campos-form {
    padding-right: 0px;
  }
  
  .camera-ver {
    width: 105% !important;
    margin-left: 1px;
    padding-right: 3px;
  }
  
  .coluna-consulta-pad {
    padding-right: 0px !important;
  }
}

// Estilos específicos para resoluções diferentes de dispositivos
// Galaxy S3
@media (width: 360px) and (height: 640px) {
  .Drawer-esquerdo {
    height: 162vw;
  }
}

// LG Optimus L70
@media (width: 384px) and (height: 640px) {
  .Drawer-esquerdo {
    height: 152vw;
  }
}

// Laptop with HIDPI Screen
@media (width: 1440px) and (height: 900px) {
  .Drawer-esquerdo {
    height: 58vw;
  }
}

// Laptop with MDPI Screen
@media (width: 1280px) and (height: 800px) {
  .Drawer-esquerdo {
    height: 57.5vw;
  }
}

// Laptop with touch
@media (width: 1280px) and (height: 950px) {
  .Drawer-esquerdo {
    height: 69.2vw;
  }
}

// Nexus 10
@media (width: 800px) and (height: 1280px) {
  .Drawer-esquerdo {
    height: 152vw;
  }
}

// Nexus 6P
@media (width: 412px) and (height: 732px) {
  .Drawer-esquerdo {
    height: 164vw;
  }
}

// Nexus 7
@media (width: 600px) and (height: 960px) {
  .Drawer-esquerdo {
    height: 149.1vw;
  }
}

// Iphone 4
@media (width: 320px) and (height: 480px) {
  .Drawer-esquerdo {
    height: 132.5vw;
  }
}

// Pixel 2
@media (width: 411px) and (height: 731px) {
  .Drawer-esquerdo {
    height: 164vw;
  }
}

// Pixel 2 XL
@media (width: 411px) and (height: 823px) {
  .Drawer-esquerdo {
    height: 186.5vw;
  }
}

// Iphone 5
@media (width: 320px) and (height: 568px) {
  .Drawer-esquerdo {
    height: 160vw;
  }
}

// Iphone 6/7/8
@media (width: 375px) and (height: 667px) {
  .Drawer-esquerdo {
    height: 163vw;
  }
}

// Iphone 6/7/8 PLUS
@media (width: 414px) and (height: 736px) {
  .Drawer-esquerdo {
    height: 164.2vw;
  }
}

// Iphone X
@media (width: 375px) and (height: 812px) {
  .Drawer-esquerdo {
    height: 201.5vw;
  }
}

// Ipad
@media (width: 768px) and (height: 1024px) {
  .Drawer-esquerdo {
    height: 125vw;
  }
}

// Ipad Pro
@media (width: 1024px) and (height: 1366px) {
  .Drawer-esquerdo {
    height: 127.2vw;
  }
}

//XGA
@media (width: 1024px) and (height: 768px) {
  .Drawer-esquerdo {
    height: 68.7vw;
  }
}

//HD
@media (width: 1280px) and (height: 720px) {
  .Drawer-esquerdo {
    height: 51.2vw;
  }
}

//HD VARIANTE
@media (width: 1366px) and (height: 635px) {
  .Drawer-esquerdo {
    height: 41.8vw;
  }
}

//WXGA
@media (width: 1366px) and (height: 768px) {
  .Drawer-esquerdo {
    height: 51.5vw;
  }
}

//FHD
@media (width: 1920px) and (height: 1080px) {
  .Drawer-esquerdo {
    height: 52.9vw;
  }
  
  .coluna-maior {
    background-color: white;
    right: -15px;
    height: 56.5vh;
    padding: unset;
  }
  
  .coluna-consulta {
    height: 61vh !important;
  }
}

//FHD VARIANTE
@media (width: 1920px) and (height: 947px) {
  .Drawer-esquerdo {
    height: 46vw;
  }
}

// Outras alturas específicas
@media (height: 644px) {
  .Drawer-esquerdo {
    height: 38.7vw;
  }
}

@media (height: 682px) {
  .Drawer-esquerdo {
    height: 45vw;
  }
}

@media (height: 667px) {
  .Drawer-esquerdo {
    height: 44vw;
  }
}

@media (height: 548px) {
  .Drawer-esquerdo {
    height: 36.5vw;
  }
}

@media (height: 860px) {
  .Drawer-esquerdo {
    height: 42.3vw;
  }
}

@media (height: 892px) {
  .Drawer-esquerdo {
    height: 44vw;
  }
}

@media (height: 993px) {
  .Drawer-esquerdo {
    height: 49.6vw;
  }
}

@media (height: 979px) {
  .Drawer-esquerdo {
    height: 47.6vw;
  }
}

@media (height: 900px) {
  .Drawer-esquerdo {
    height: 44.4vw;
  }
}

@media (height: 767px) {
  .Drawer-esquerdo {
    height: 37.4vw;
  }
}

@media (height: 799px) {
  .Drawer-esquerdo {
    height: 51.1vw;
  }
}

@media (height: 1440px) {
  .Drawer-esquerdo {
    height: 73.1vw;
  }
}

@media (height: 1307px) {
  .Drawer-esquerdo {
    height: 66.1vw;
  }
}

@media (height: 1339px) {
  .Drawer-esquerdo {
    height: 67.8vw;
  }
}
  @media (max-width: 1000px) {
    .nsm-content{
        z-index: 1048;
        position: relative;
        margin: 0 auto !important; 
        margin-top: 50px; 
        justify-content: center; 
        display: flex;
        align-items: center; 
        left: 19%;
    }
    .content{
      width: 340px;
    }
  }