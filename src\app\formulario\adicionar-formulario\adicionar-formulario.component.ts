import { MatIconModule } from '@angular/material/icon';
import { Router } from '@angular/router';
import { CommonModule, DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { formulariosModel, perguntas } from 'src/app/model/formularios';
import { tabTipoOrientacao } from 'src/app/model/tipoOrientacao';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { FormulariosService } from 'src/app/service/formulario.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { TipoOrientacaoService } from 'src/app/service/tipoOrientacao.service';
import { TipoRespostaService } from 'src/app/service/tipoResposta.service';
import { TabTipoResposta } from 'src/app/model/tipoResposta';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule, MatLabel } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { CdkDragHandle, DragDropModule } from '@angular/cdk/drag-drop';

@Component({
    selector: 'app-adicionar-formulario',
    templateUrl: './adicionar-formulario.component.html',
    styleUrls: ['./adicionar-formulario.component.scss'],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      MatCardModule,
      MatIconModule,
      TranslateModule,
      MatFormFieldModule,
      MatLabel,
      NgSelectModule,
      MatSelectModule,
      DragDropModule,
      CdkDragHandle
    ],
    providers: [DatePipe]
})
export class AdicionarFormularioComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private datePipe: DatePipe,
    private usuarioLogadoService: UsuarioLogadoService,
    private tipoOrientacaoService: TipoOrientacaoService,
    private tipoRespostaService: TipoRespostaService,
    private formulariosService: FormulariosService,
    private router: Router,
    // public snackBar: MatSnackBar,
    private snackbarAlert: AlertComponent,
    private localStorageService: LocalStorageService
  ) { }

  id_formulario?: number;
  id_tipoOrientacao?: number;
  flgmodal_pergunta?: boolean;
  flgmodal_salvar?: boolean;


  objFormulario = new formulariosModel;
  listaPergunta: perguntas[] = [];
  perguntasExcluidas: perguntas[] = [];
  objTipoOrientacao: tabTipoOrientacao[] = [];
  listTipoReposta: TabTipoResposta[] = [];
  dtCreate?: string;

  objPergunta = new perguntas();

  ngOnInit() {

    this.id_formulario = this.localStorageService.idFormularios;

    this.getTipoResposta();
    this.getTipoOrientacao();

    if (this.id_formulario > 0) {
      this.getFormulario();
    }
    else {
      this.preparaCamposTela();
    }
    this.localStorageService.clearByName('idFormularios');
  }

  preparaCamposTela() {
    if (this.id_formulario! > 0) {
      let date = this.objFormulario.dtaCadastro;
      this.dtCreate = this.datePipe.transform(date, 'dd/MM/yyyy')!;
    }
    else {
      let date = Date.now();
      this.dtCreate = this.datePipe.transform(date, 'dd/MM/yyyy')!;
    }
  }

  getTipoResposta() {
    this.tipoRespostaService.getTipoResposta().subscribe((ret) => {
      this.listTipoReposta = ret;
      this.spinner.hide();
    })
  }

  getTipoOrientacao() {
    this.tipoOrientacaoService.getTipoOrientacao().subscribe((ret) => {
      ;
      this.objTipoOrientacao = ret;
      this.spinner.hide();
    })
  }

  getFormulario() {
    this.formulariosService.getFormularioById(this.id_formulario!).subscribe((ret) => {
      ;

      this.objFormulario = ret;

      this.objFormulario.perguntasFormulario!.forEach(element => {
        let pergunta = new perguntas();
        pergunta.idPergunta = element.idPergunta;
        pergunta.idTipoResposta = element.idTipoResposta;
        pergunta.pergunta = element.pergunta;
        pergunta.posicaoPergunta = element.posicaoPergunta;
        this.listaPergunta.push(pergunta);
      });

      this.listaPergunta.sort((a, b) => a.posicaoPergunta! - b.posicaoPergunta!);

      this.id_tipoOrientacao = ret.idTipoOrientacao;
      this.preparaCamposTela();
      this.spinner.hide();

    })
  }

  corrigePosicaoLista() {
    for (let i = 0; i < this.listaPergunta.length; i++) {
      this.listaPergunta[i].posicaoPergunta = i + 1;
    }
  }

  drop(event: CdkDragDrop<perguntas[]>) {
    moveItemInArray(this.listaPergunta, event.previousIndex, event.currentIndex);
    this.corrigePosicaoLista();
  }

  remove(pergunta: perguntas) {
    if (pergunta.idPergunta != null || pergunta.idPergunta! > 1) {
      this.perguntasExcluidas.push(pergunta);
    }

    const index = this.listaPergunta.findIndex(p => p.posicaoPergunta === pergunta.posicaoPergunta);
    if (index !== -1) {
      this.listaPergunta.splice(index, 1);
      this.corrigePosicaoLista();
    }
  }

  modalNovaPergunta() {
    this.flgmodal_pergunta = true;
  }

  adicionaPergunta(op: boolean) {
    if (op) {
      if (this.objPergunta.pergunta == '' || this.objPergunta.idTipoResposta == null) {
        this.snackbarAlert.falhaSnackbar("Preencha todos os campos antes de salvar")
      }
      else {
        this.objPergunta.posicaoPergunta = (this.listaPergunta.length + 1);
        this.listaPergunta.push(this.objPergunta);

        this.objPergunta = new perguntas();
        this.flgmodal_pergunta = false;
        this.corrigePosicaoLista();

      }

    }
    else {
      this.objPergunta = new perguntas();
      this.flgmodal_pergunta = false;
    }
  }

  preparaSalvar() {
    this.flgmodal_salvar = true;
  }

  salvar(op: boolean) {

    if (op) {
      this.objFormulario.perguntasFormulario = this.listaPergunta;
      this.objFormulario.idTipoOrientacao = this.id_tipoOrientacao;

      if (this.objFormulario.idFormulario == null) {
        this.objFormulario.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
        this.objFormulario.dtaCadastro = new Date();
      }

      if (this.objFormulario.dtaCadastro == null ||
        this.objFormulario.idTipoOrientacao == null ||
        this.objFormulario.idUsuarioGerador == null ||
        this.objFormulario.nomeFormulario == null ||
        this.objFormulario.perguntasFormulario == null
      ) {
        this.snackbarAlert.falhaSnackbar("Complete todos os campos para salvar")
      }
      else {
        this.objFormulario.perguntasExcluidas = this.perguntasExcluidas;
        this.formulariosService.saveFormulario(this.objFormulario).subscribe((ret) => {
          this.snackbarAlert.sucessoSnackbar(ret.msgResposta);
          this.flgmodal_salvar = false;
          if (ret.ok) {
            this.router.navigate(['/formularios']);
          }
          this.spinner.hide();
        }, (err) => {
          this.snackbarAlert.falhaSnackbar(err);
          this.spinner.hide();
        })
      }
    }
    else {
      this.flgmodal_salvar = false;
    }
  }
}
