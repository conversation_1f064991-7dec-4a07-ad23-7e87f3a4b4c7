import { Clinica } from './clinica';
import { <PERSON><PERSON><PERSON> } from './pessoa';


export class Usuario {
    idUsuario?: number;
    cpf?:string;
    email?: string;
    senha?: string;
    flgInativo?: boolean;
    dtaCadastro?: Date;
    idUsuarioGerador?: number;
    idTipoUsuario?: number;
    desTipoUsuario?: string;
    flgPrimeiroAcesso?: boolean;
    flgEmail?: boolean
    flgPrivacidade?: boolean
    idUltimaClinica?: number
    dtaUltimoLogin?: Date
    dtaLogin?: Date
    tokenLogado?: string;
    flgLogado?: boolean;

    pessoa?:Pessoa;


    imagem64?:string;
    removerFoto?:boolean
    

    clinicas?: Clinica[];

    usuarioclinicas?: UsuarioClinica[];
}


export class UsuarioClinica {
    IdUsuarioClinica?: number;
    IdClinica?: number;
    IdUsuario?: number;
    retidoClinica?: string;
}