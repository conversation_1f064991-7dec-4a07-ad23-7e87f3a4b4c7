/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

:host {
    height: auto;
    display: flex;
    padding: 24px;
}

main {
    display: flex;
    overflow-y: auto; 
    width: 100%;
    height: 60vh;
    gap: 20px;
    position: relative;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
}

section {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: $border-radius;
    background-color: $secondary-light;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid $border-color;
}

h3 {
    font-weight: 500;
    font-size: 16px;
    color: $text-primary;
    margin: 0;
}

/* Messages Section */
#ListaMensagens {
    width: 30%;
    background-color: $secondary-light;
}

.message-card {
    background-color: $card-bg;
    padding: 12px;
    border-radius: $border-radius;
    margin-bottom: 8px;
    border: 1px solid $border-color;
    transition: all $transition ease;
    cursor: pointer;
    color: $text-primary;
}

.message-card:hover {
    background-color: $secondary-color;
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

/* Message Content Section */
#ConteudoMensagem {
    width: 40%;
}

textarea {
    width: 100%;
    height: 100%;
    border-radius: $border-radius;
    padding: 12px;
    border: 1px solid $border-color;
    background-color: $secondary-light;
    color: $text-primary;
    font-family: inherit;
    resize: none;
    transition: border-color $transition ease;
}

textarea:focus {
    outline: none;
    border-color: $primary-color;
}

/* Patients Section */
#ListaPacientes {
    width: 30%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

#ListaPacientes header {
    justify-content: space-between;
    flex-wrap: wrap;
}

.search-container {
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 8px;
    border: 1px solid $border-color;
    border-radius: $border-radius;
    overflow: hidden;
    background-color: $secondary-light;
}

.search-container input {
    flex: 1;
    border: none;
    padding: 8px 12px;
    color: $text-primary;
    background: transparent;
}

.search-container input:focus {
    outline: none;
}

.search-button {
    background-color: $primary-color;
    border: none;
    color: white;
    padding: 8px;
    cursor: pointer;
    transition: background-color $transition ease;
}

.search-button:hover {
    background-color: $primary-dark;
}

.patients-container {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px;
}

.patients-container ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.empty-state {
    color: $text-secondary;
    text-align: center;
    padding: 16px;
}

.patient-card {
    display: flex;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background-color: $card-bg;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    transition: all $transition ease;
    cursor: pointer;
}

.patient-card:hover {
    background-color: $secondary-color;
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

.checkbox-container {
    margin-right: 12px;
}

.checkbox-container input[type="checkbox"] {
    accent-color: $primary-color;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.patient-data {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.patient-name {
    font-weight: 500;
    color: $text-primary;
    margin-bottom: 4px;
}

.patient-phone {
    color: $text-secondary;
    font-size: 14px;
}

.load-more-button {
    width: 100%;
    padding: 10px;
    background-color: transparent;
    color: $primary-color;
    border: 1px solid $primary-color;
    border-radius: $border-radius;
    cursor: pointer;
    transition: all $transition ease;
    margin-top: 8px;
}

.load-more-button:hover {
    background-color: $primary-light;
    color: $primary-dark;
}

/* Action Panel */
#PainelAcoes {
    position: absolute; 
    bottom: 16px; 
    right: 16px; 
    display: flex;
    gap: 12px; 
}

.action-button {
    border: none;
    padding: 10px 16px;
    border-radius: $border-radius;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 90px;
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: $secondary-color;
}

.send-button {
    background-color: $primary-color;
    color: white;
}

.send-button:hover {
    background-color: $primary-dark;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: $secondary-color;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: $primary-light;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: $primary-color;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    main {
        flex-direction: column;
        height: auto;
        min-height: 60vh;
    }
    
    section {
        width: 100% !important;
    }
    
    #PainelAcoes {
        position: static;
        margin-top: 16px;
        justify-content: flex-end;
    }
}