import { Injectable, EventEmitter } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { Cid } from '../pesquisa-cid/cid';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class CidService {
    
  public changeCID$: EventEmitter<any>;
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
        this.changeCID$ = new EventEmitter();
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });

    public GetListaCid(inicio:any, fim:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Cid/GetListaCid/' + inicio + '/' + fim);
    }

    public GetListaFiltaPorCod(inicio:any, fim:any, pesquisaCod:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Cid/GetListaFiltaPorCod/' + inicio + '/' + fim + '/' + pesquisaCod)
    }

    public GetListaFiltaPorDes(inicio:any, fim:any, pesquisaDes:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Cid/GetListaFiltaPorDes/' + inicio + '/' + fim + '/' + pesquisaDes)
    }

    public EnviarCid(codCid:any){
        this.spinner.show();
        this.changeCID$.emit(codCid);
    }

    public GetCid(idCid: number): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Cid/GetCid/' + idCid);
    }

    public SalvarCid(obj: Cid): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Cid/SalvarCid', obj);
    }

    public ExcluirCid(idCid: number): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Cid/ExcluirCid/' + idCid);
    }
}