import { AlertComponent } from './../../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { LoginService } from 'src/app/service/login.service';
import { AuthService } from '../auth.service';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';


@Component({
    selector: 'app-recuperar-senha',
    templateUrl: './recuperar-senha.component.html',
    styleUrls: ['./recuperar-senha.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCardModule,
      MatFormFieldModule,
      MatDividerModule,
      TranslateModule,
      MatIconModule
    ]
})
export class RecuperarSenhaComponent implements OnInit {

  // private spinner: SpinnerService,
  constructor(    
    private router: Router,
    private authService: AuthService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private loginservice: LoginService,
    public translate: TranslateService,
    private localStorageService: LocalStorageService,
    ) { }

  dados: any = [];
  dadosInvalidos?: boolean;
  // name = 'SnackBarConfirmação';

  // messageExclusao: string = 'Email enviado com uma nova senha.  ✔ ';
  // messageADD: string = 'Email enviado com uma nova senha.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔'
  // nconcordomessageADD: string = 'Adicionado com Sucesso. ✔'
  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  // concordo: boolean;
  // concordomsg: boolean;
  Linguagem = 'pt'
  ngOnInit() {
   
    this.Linguagem = this.localStorageService.Linguagem;
    this.translate.use(this.Linguagem)
  }
  esqueciSenha() {

    if (this.dados.email == null || this.dados.cpf == null) {
      this.dadosInvalidos = true;
    }
    else {
      ;
      this.authService.EsqueciSenha(this.dados.cpf, this.dados.email).subscribe((data: any) => {
        if (data != true) {
          this.dadosInvalidos = true;
        }
        else {
          this.snackBarAlert.sucessoSnackbar(data)
          this.dados = [];
          this.router.navigate(['/Login']);
        }
      })
    }
  }
  // ADDItem(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;

  //     this.tradutor.get('TELALOGINRECUPERARSENHA.EMAILINVALIDOCOMUMANOVASENHA').subscribe((res: string) => {
  //       ;
  //     this.snackBarAlert.sucessoSnackbar(res);
  //   });
  //   }
  //   this.concordo = value;
  //   this.concordomsg = false;
  // }

  voltarLogin() {

    localStorage.clear();
    sessionStorage.clear();
    this.loginservice.recuperarSenha$.emit(false);
    this.router.navigate(['login']);
  }


  public mascaraCpf(mascara:any, evento: KeyboardEvent) {
      mascara;
      var valorEvento = (<HTMLInputElement>evento.target).value;
      // var i = valorEvento.length;
      var ao_cpf = valorEvento;
      ao_cpf = ao_cpf.replace(/\D/g, "");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
      return (<HTMLInputElement>evento.target).value = ao_cpf;
    
    // var valorEvento = (<HTMLInputElement>evento.target).value;
    // var i = valorEvento.length;

    // if (i < 14) {
    //   var saida = mascara.substring(0, 1);
    //   var texto = mascara.substring(i);

    //   if (texto.substring(0, 1) != saida) {
    //     return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
    //   }
    //   if (i >= 11) {
    //     var ao_cpf = valorEvento;

    //     ao_cpf = ao_cpf.replace(/\D/g, "");
    //     ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
    //     ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
    //     ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

    //     return (<HTMLInputElement>evento.target).value = ao_cpf;
    //   }
    // }
  }
}
