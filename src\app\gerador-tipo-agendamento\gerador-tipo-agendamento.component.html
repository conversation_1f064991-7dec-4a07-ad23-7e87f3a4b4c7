<mat-card appearance="outlined" class="mother-div">
    <mat-card-title class="spacer-card" style="padding: unset">
        <div class="col-md-12" style="padding: unset">
            <mat-icon class="icon-title">group</mat-icon><a
                class="title-content fonte-tamanho">{{ 'TELAGERADORTIPOAGENDAMENTO.TITULO' | translate }}</a>
        </div>
    </mat-card-title>


    <mat-divider class="p-t-20"></mat-divider>

    <br>
    <div class="input-group col-md-12  mt-1 div-desktop">
        <mat-form-field class="input-spacing col-md-5 col-sm-6 col-10 adc-agend">
            <input matInput placeholder="{{ 'TELAGERADORTIPOAGENDAMENTO.TIPODEAGENDAMENTO' | translate }}" maxlength="50" name="tipoAgendamento" id="tipoAgendamento" required [(ngModel)]="tipoAgendamento" (keyup.enter)="salvarTipoAgendamento()">
            <mat-error *ngIf="campoTipoAgendamento.invalid">{{getErrorMessagecampoTipoAgendamento() | translate }}</mat-error>

        </mat-form-field>
        <span class="input-group-btn input-spacing col-md-1 col-sm-1 text-center">
            <button mat-mini-fab class=" btn-primary " (click)="salvarTipoAgendamento()">
                <mat-icon class="btn-adc" style="color:white;">add</mat-icon>
            </button>
        </span>
        <!-- <mat-form-field class="col-md-5 col-sm-5  input-spacing mt-1">
            <input matInput placeholder="{{ 'TELAGERADORTIPOAGENDAMENTO.BUSCAR' | translate }}" id="inputBusca" value="" name="pesquisa" style="cursor:pointer;" [(ngModel)]="pesquisa" (keyup)=CarregaTipoAgendamento()>
        </mat-form-field> -->


        <div class="col-md-5">
            <mat-form-field class="col-md-10 col-sm-12 col-xs-12  pesquisar-agend" style=" font-size: 17px;">
                <input matInput placeholder="{{ 'TELAGERADORTIPOAGENDAMENTO.BUSCAR' | translate }}" id="inputBusca"
                    value="" name="pesquisa" style="cursor:pointer;" [(ngModel)]="pesquisa"   (keyup.enter)="CarregaTipoAgendamento()">

                <mat-icon aria-label="Buscar" class="col-md-1 custom-search" (click)="CarregaTipoAgendamento()">search
                </mat-icon>
            </mat-form-field>
        </div>
    </div>


    <div class="input-group col-md-12  mt-1 div-mobile">
        
        <!-- <mat-form-field class="col-md-5 col-sm-5  input-spacing mt-1">
            <input matInput placeholder="{{ 'TELAGERADORTIPOAGENDAMENTO.BUSCAR' | translate }}" id="inputBusca" value="" name="pesquisa" style="cursor:pointer;" [(ngModel)]="pesquisa" (keyup)=CarregaTipoAgendamento()>
        </mat-form-field> -->

        <div class="col-md-5 col-md-12">
            <mat-form-field class="col-md-10 col-sm-12 col-xs-12 pesquisar-agend" style=" font-size: 17px;">
                <input matInput placeholder="{{ 'TELAGERADORTIPOAGENDAMENTO.BUSCAR' | translate }}" id="inputBusca" 
                value="" name="pesquisa" style="cursor:pointer;" [(ngModel)]="pesquisa" (blur)=CarregaTipoAgendamento()  (keyup.enter)="CarregaTipoAgendamento()">

                <mat-icon aria-label="Buscar" class="col-md-1 custom-search" (click)="CarregaTipoAgendamento()">search
                </mat-icon>
            </mat-form-field>
        </div>

        <div class="col-md-5 col-md-12">
            <mat-form-field class="input-spacing col-md-5 col-sm-6 col-md-12 adc-agend">
                <input matInput placeholder="{{ 'TELAGERADORTIPOAGENDAMENTO.TIPODEAGENDAMENTO' | translate }}" maxlength="50" name="tipoAgendamento"  id="tipoAgendamento" required [(ngModel)]="tipoAgendamento" (keyup.enter)="salvarTipoAgendamento()">
                <mat-error *ngIf="campoTipoAgendamento.invalid">{{getErrorMessagecampoTipoAgendamento() | translate }}</mat-error>
            </mat-form-field>

            <span class="input-group-btn input-spacing col-md-1 col-sm-1 text-center">
                <button mat-mini-fab class=" btn-primary btn-add-tipoagend" (click)="salvarTipoAgendamento()">
                    <mat-icon class="btn-adc" style="color:white;">add</mat-icon>
                </button>
            </span>
        </div>


    </div>


    <mat-card-content>
        <div class="row">
            <div class="col-md-9 col-sm-8"></div>
            <div class="col-md-3 col-sm-4  coluna">
                <table class="tabela legenda-btn">
                    <thead class="col-md-3 col-md-12">
                        <tr class="cor" style="border-bottom: 1px solid #ddd;">
                            <td class="text-center bold value-color btn-legenda" style="padding: 8px!important; cursor: pointer;" (click)="legenda = !legenda">
                                {{ 'TELAGERADORTIPOAGENDAMENTO.LEGENDA' | translate }}
                                <mat-icon style="float: left;" *ngIf="legenda">expand_less</mat-icon>
                                <mat-icon style="float: left;" *ngIf="!legenda">expand_more</mat-icon>
                            </td>
                        </tr>
                    </thead>
                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class=""
                                style=" font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                <mat-icon class="button-interative" style="vertical-align: sub; ">edit</mat-icon><span
                                    style="vertical-align: super;margin-left: 5px;">
                                    {{ 'TELAGERADORTIPOAGENDAMENTO.EDITAR' | translate }}</span>
                            </td>
                        </tr>
                    </tbody>

                    <tbody *ngIf="legenda" style="  pointer-events: none;">
                        <tr>
                            <td class=""
                                style="font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;font-family: 'Roboto', sans-serif; font-size: 12px; padding: 8px!important;">
                                <mat-icon class="button-interative" style="vertical-align: sub;">delete</mat-icon><span
                                    style="vertical-align: super;margin-left: 8px;">{{ 'TELAGERADORTIPOAGENDAMENTO.EXCLUIR' | translate }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-12 col-sm-12 col-xs-12 p-t-20" style="padding: unset; margin-top: 30px;">
            <div class="col-md-12 col-sm-12 col-xs-12 card-info-agend no-mobile-card">
                <table *ngFor="let item of DadosTipoAgendamento" class="table" id="DatatableCliente"
                    style="margin-top: 10px; margin-bottom: 10px;">
                    <thead style="display: none;">

                        <tr>
                            <th>{{ 'TELAGERADORTIPOAGENDAMENTO.PERGUNTAS' | translate }}</th>
                            <th>{{ 'TELAGERADORTIPOAGENDAMENTO.ACOES' | translate }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="card_table">
                            <td class="" id="nome" style="width:100%">
                                <div class="row">
                                    <div class="div_paciente quebra">
                                        <label class="hover-underline-animation"
                                            style="margin-top: 10px;padding-right: 10px; ">{{item.desTipoAgendamento}}</label>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center" id="acoes" style="min-width: 100px;">
                                <button mat-icon-button (click)="editTipoAgendamento(item.idTipoAgendamento)"
                                    class="panel_button" title="{{'TELAGERADORTIPOAGENDAMENTO.EDITAR' | translate}}">
                                    <mat-icon aria-label="Editar linha selecionada" class="button-interative">edit
                                    </mat-icon>
                                </button>
                                <button mat-icon-button (click)="ValorTipoAgendamento(item.idTipoAgendamento)"
                                    class="panel_button" title="{{'TELAGERADORTIPOAGENDAMENTO.EXCLUIR' | translate}}">
                                    <mat-icon aria-label="Deletar Linha selecionada" class="button-interative">delete
                                    </mat-icon>
                                </button>

                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <mat-card appearance="outlined" *ngFor="let item of DadosTipoAgendamento; let i = index" class="header-card card-info-agend no-desktop"
                id="DatatableCliente">



                <div class="text-left quebra text-tipo-agend">
                    <p>{{item.desTipoAgendamento}}</p>
                </div>
              
                <div class="lateral">
                    <div [@openClose]="toggle[i] ? 'open': 'closed'">
                        <button class="btn-primary buttons-lateral" id="opcao2" mat-mini-fab
                            (click)="editTipoAgendamento(item.idTipoAgendamento)"
                            title="{{'TELAGERADORTIPOAGENDAMENTO.EDITAR' | translate}}">
                            <mat-icon aria-label="Editar linha selecionada">edit</mat-icon>
                        </button>
                        <button class="btn-primary buttons-lateral" id="opcao2" mat-mini-fab
                            (click)="ValorTipoAgendamento(item.idTipoAgendamento)"
                            title="{{'TELAGERADORTIPOAGENDAMENTO.EXCLUIR' | translate}}">
                            <mat-icon aria-label="Deletar Linha selecionada">delete
                            </mat-icon>
                        </button>
                    </div>
                    <button class="btn-primary" mat-mini-fab (click)="toggle[i] = !toggle[i]">
                        <mat-icon>keyboard_arrow_up</mat-icon>
                    </button>
                </div>
                <!-- <div class="text-center fab" ontouchstart="">

                    <button class="mainb material-icons">


                    </button>
                    <ul>
                        <li (click)="editTipoAgendamento(item.idTipoAgendamento)">
                            <label for="opcao1">
                                <p style="margin-top: -9px;">
                                    Editar</p>
                            </label>
                            <button id="opcao2">
                                <mat-icon aria-label="Editar linha selecionada" class="">edit</mat-icon>
                            </button>
                        </li>

                        <li (click)="ValorTipoAgendamento(item.idTipoAgendamento)">
                            <label for="opcao2">
                                <p style="margin-top: -9px;">
                                    Excluir</p>
                            </label>
                            <button id="opcao2">
                                <mat-icon aria-label="Deletar Linha selecionada" class="">delete
                                </mat-icon>
                            </button>
                        </li>

                    </ul>
                </div> -->
            </mat-card>
        </div>

        <div class="col-sm-12 text-center">
            <button mat-flat-button class="btn-primary"
                *ngIf="(DadosTipoAgendamento != undefined &&  DadosTipoAgendamento.length > 0) && bOcultaCarregaMais == false"
                (click)="CarregarMais()">{{ 'TELAPESQUISAMEDICO.CARREGARMAIS' | translate }}</button>
        </div>


        <div class="p-t-20"></div>
    </mat-card-content>
</mat-card>




<!-- Modal excluir -  -->

<ngx-smart-modal #excluirItem identifier="excluirItem" customClass="nsm-centered medium-modal emailmodal">
    <div class="modal-header p-t-20 p-b-20">
        <h1 class="little-title fw-700">
            <mat-icon style="color:red">warning</mat-icon>
            {{ 'TELAGERADORTIPOAGENDAMENTO.EXCLUIRAGENDAMENTO' | translate }}
        </h1>
    </div>



    <mat-divider></mat-divider>
    <div class="row-button text-right " style="padding: 10px 0px;">
        <button mat-flat-button class="input-align btn btn-danger"
            (click)="ngxSmartModalService.getModal('excluirItem').close()">
            {{ 'TELAGERADORTIPOAGENDAMENTO.NAO' | translate }} </button>
        <button mat-flat-button class="input-align btn btn-success" (click)="inativarTipoAgendamento()">
            {{ 'TELAGERADORTIPOAGENDAMENTO.SIM' | translate }} </button>
    </div>
</ngx-smart-modal>