import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { AcessoRapidoConsulta } from '../model/acesso-rapido-consulta';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class AcessoRapidoConsultaService {

    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) { }

    public getCodigoAcesso(idGuid: string) {
        this.spinner.show();

        let params = new HttpParams();
        params = params.append('idGuid', String(idGuid));

        var reqHeader = new HttpHeaders({ 'No-Auth': 'True' });
        return this.http.get<AcessoRapidoConsulta>(environment.apiEndpoint + '/Agenda/GetCodigoAcesso', { headers: reqHeader, params });

        // return this.http.get<AcessoRapidoConsulta>(environment.apiEndpoint + '/Agenda/GetCodigoAcesso', { params });
    }

    public getAcessoReuniao(idGuid: string) {
        let params = new HttpParams();
        params = params.append('idGuid', String(idGuid));

        var reqHeader = new HttpHeaders({ 'No-Auth': 'True' });
        return this.http.get<AcessoRapidoConsulta>(environment.apiEndpoint + '/Agenda/GetCodigoAcesso', { headers: reqHeader, params });

        // return this.http.get<AcessoRapidoConsulta>(environment.apiEndpoint + '/Agenda/GetCodigoAcesso', { params });
    }

}