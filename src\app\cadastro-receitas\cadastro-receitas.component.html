<div class="container  mother-div">
  <!-- MODAL DE CONFIRMAÇÃO -->
  <div *ngIf="flgmodal" class="modal-overlay">
    <div class="modal-container">
      <div class="modal-header">
        <h3>Confirmação</h3>
      </div>
      <div class="modal-body">
        <p class="modal-text">Deseja realmente excluir essa receita?</p>
      </div>
      <div class="modal-footer">
        <button class="btn-cancelar" (click)="Excluir(false)">Cancelar</button>
        <button class="btn-confirmar btn-excluir" (click)="Excluir(true)">Excluir</button>
      </div>
    </div>
  </div>

  <mat-card class="card-principal">
    <!-- CABEÇALHO -->
    <div class="header">
      <div class="header-icon">
        <span class="material-icons">description</span>
      </div>
      <h2 class="header-title">Cadastro de Receitas</h2>
    </div>

    <!-- FILTROS -->
    <div class="filtros">
      <div class="busca-container">
        <mat-form-field appearance="outline" class="busca-field">
          <mat-label>Buscar</mat-label>
          <input matInput type="search" [(ngModel)]="filtroBusca" (keyup)="filtrarReceitas()">
          <button mat-icon-button matSuffix class="btn-busca">
            <mat-icon>search</mat-icon>
          </button>
        </mat-form-field>
      </div>
      
      <div class="adicionar-container">
        <button class="btn-adicionar" (click)="adicionarreceitas()">
          <mat-icon>add</mat-icon>
          <span>Adicionar Receita</span>
        </button>
      </div>
    </div>

    <!-- LISTA DE RECEITAS -->
    <div class="lista-container">
      <div class="lista-scroll">
        <div class="receita-card" *ngFor="let receitas of listaReceitas">
          <!-- INFO DA RECEITA -->
          <div class="receita-info">
            <div class="receita-avatar">
              <img src="{{ ImagemPessoa }}" class="img-circle" alt="Foto do paciente">
            </div>
            <div class="receita-detalhes">
              <div class="info-item">
                <mat-icon>person</mat-icon>
                <span class="nome">Paciente: {{receitas.nomPaciente}}</span>
              </div>
              <div class="info-item">
                <mat-icon>description</mat-icon>
                <span class="tipo">Tipo: {{receitas.tipoOrientacao}}</span>
              </div>
            </div>
          </div>

          <!-- DATA DE CADASTRO -->
          <div class="receita-data">
            <div class="data-item">
              <label class="data-label">Data de Cadastro</label>
              <span class="data-valor">{{receitas.dtaCadastro | date: 'dd/MM/yyyy'}}</span>
            </div>
          </div>

          <!-- AÇÕES -->
          <div class="receita-acoes">
            <button mat-icon-button matTooltip="Editar" (click)="Editar(receitas!.idReceita!)">
              <mat-icon>edit</mat-icon>
            </button>
            <button mat-icon-button matTooltip="Excluir" (click)="excluirReceita(receitas.idReceita!)">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
        
        <!-- MENSAGEM DE LISTA VAZIA -->
        <div class="lista-vazia" *ngIf="listaReceitas?.length === 0">
          <mat-icon>sentiment_very_dissatisfied</mat-icon>
          <p>Nenhuma receita encontrada</p>
        </div>
      </div>
    </div>
  </mat-card>
</div>