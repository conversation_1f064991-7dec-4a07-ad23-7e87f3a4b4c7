<mat-card appearance="outlined" class="mother-div mother-permissoes" >

    <mat-card-title class="spacer-card" style="padding: unset">
        <div class="row col-md-12" style="margin: 0 auto; padding: 0;">

            <div class="col-md-3" style="padding: unset">
                <mat-icon class="icon-title">list</mat-icon> <a class="title-content title-permissoes">Permissões</a>
            </div>

            <!-- <mat-form-field class="col-md-6 col-sm-12 col-lg-6" style=" font-size: 17px;">
                <input matInput placeholder="{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}" id="inputBusca" name="pesquisa" value="" style="cursor:pointer;" [(ngModel)]="pesquisa">
            </mat-form-field> -->

                
            <div class="col-md-6 div-buscar">
                <mat-form-field class="col-md-10 col-sm-12 col-xs-12 linha-buscar" style=" font-size: 17px;">
                    <input matInput placeholder="{{ 'TELAPESQUISAMEDICO.BUSCAR' | translate }}"
                     id="inputBusca" name="pesquisa" value="" style="cursor:pointer;" (blur)="GetListaPerfil()" [(ngModel)]="pesquisa">

                    <mat-icon aria-label="Buscar" class="col-md-1 custom-search icone-lupa"
                        (click)="GetListaPerfil()">search
                    </mat-icon>
                </mat-form-field>
            </div>

            <div class="col-md-3 col-sm-3 col-sm-12 div-btn-add">
                <button mat-raised-button class="input-align btn-primary f-r btn-add-perm" [routerLink]="['/permissoes']">
                        <mat-icon style="color:white;">add</mat-icon> <a class="ad-pp">Adicionar Permissões</a>
                    </button>
            </div>
        </div>
    </mat-card-title>

    <mat-divider class="p-t-20"></mat-divider>

    <mat-card-content style="padding-top: 15px;">

        <table *ngFor="let item of listaPerfil" class="table table-infos-desktop" style="margin-top: 4px; margin-bottom: 10px;">
            <thead style="display: none;">

            </thead>

            <tbody class="card_table">

                <tr class="card_table">

                    <td class="" id="nome" style="width:100%">
                        <div class="div_paciente quebra">
                            <label class=" quebra" style="margin-top: 10px;padding-right: 10px;"><b>Nome do Perfil:
                                        {{item.nomePerfil}}</b></label>
                        </div>
                    </td>
                    <td class="table-data">
                        Data de Cadastro: {{item.dtaCadastro | date: 'dd/MM/yyyy'}}
                    </td>
                    <td class="text-center" id="acoes" style="min-width:100px;">
                        <button mat-icon-button class="panel_button" (click)="EditarPerfil(item.idPerfil)" title="{{'TELAGERADORDEPERGUNTAS.EDITAR' | translate}}">
                                <mat-icon aria-label="Editar linha selecionada">edit
                                </mat-icon>
                            </button>
                        <button mat-icon-button class="panel_button" (click)="ExcluirPerfil(item.idPerfil)" title="{{'TELAGERADORDEPERGUNTAS.EXCLUIR' | translate}}">
                                <mat-icon aria-label="Deletar Linha selecionada">
                                    delete
                                </mat-icon>
                            </button>
                    </td>

                </tr>
            </tbody>

        </table>

        <mat-card appearance="outlined" *ngFor="let item of listaPerfil; let i = index" class="card-infos-mobile">
            <mat-card-header style="margin: 0 auto; text-align: center; justify-content: center;">
                <mat-card-title>
                    <div style="height: 5px;"></div>
                    <div class="info-header">
                        <b style="font-weight: 700; font-size: 18x; color: #1265b9;">Nome do Perfil</b><br>
                        <b>{{item.nomePerfil}}</b><br>                        
                    </div>
                </mat-card-title>   
            </mat-card-header>
            
            <mat-divider></mat-divider>
            <div style="height: 20px;"></div>

            <div class="info-card-perm" style="margin: 0 auto; text-align: center; justify-content: center;">
                <b style="color: #1265b9; font-weight: 700 ">Data de Cadastros</b><br>
                <b>{{item.dtaCadastro | date: 'dd/MM/yyyy'}}</b><br>                        
            </div>

            <div style="height: 10px;"></div>
    

            <div class="grid-buttons">

                <div class="botoes-de-acao" (clickOutside)="toggle[i] ? fecharBotoesMobile() : null" [@openClose]="toggle[i] ? 'open': 'closed'">

                    <button class="btn-primary buttons-mobilet" id="opcao2" mat-mini-fab
                        (click)="EditarPerfil(item.idPerfil)" title="{{'TELAGERADORDEPERGUNTAS.EDITAR' | translate}}">
                        <mat-icon aria-label="Editar linha selecionada">edit</mat-icon>
                    </button>

                    <button class="btn-primary buttons-mobilet" id="opcao3" mat-mini-fab
                    (click)="ExcluirPerfil(item.idPerfil)" title="{{'TELAGERADORDEPERGUNTAS.EXCLUIR' | translate}}">
                    <mat-icon aria-label="Deletar Linha selecionada">delete</mat-icon>
                    </button>



                </div>
                <button class="btn-primary btn-subir" mat-mini-fab (click)="openToggle(i)">
                    <mat-icon class="flecha-subir">keyboard_arrow_up</mat-icon>
                </button>
            </div>
                

            
        </mat-card>



        <div class="col-sm-12 text-center">
            <button mat-flat-button class="btn-primary" *ngIf="listaPerfil.length > 0 && bOcultaCarregaMais == false" (click)="CarregarMais()">{{ 'TELAPESQUISAUSUARIO.CARREGARMAIS' | translate }}</button>
        </div>


    </mat-card-content>


</mat-card>



<!-- modal -->

<ngx-smart-modal #excluirPerfil identifier="excluirPerfil" customClass="nsm-centered medium-modal emailmodal modal-excluir">
    <div class="modal-info-excluir">
       
        <div class="modal-info">
           <b class="info-excluir-title">Deseja excluir esse Perfil?</b><br>
           <small class="info-excluir-title">não será possivel reativa-lo</small>
        </div>


        <mat-divider></mat-divider>
        <div class="row-button text-center p-20">
           <button mat-flat-button (click)="excluirPerfil.close()" class="input-align btn btn-danger">{{ 'TELAPESQUISAMEDICO.NAO' | translate }}</button>
           <button mat-flat-button (click)="InativarPerfil()" class="input-align btn btn-success">{{ 'TELAPESQUISAMEDICO.SIM' | translate }}</button>
        </div>

        <div class="modal-excluir-logo">
         </div>

    </div>
</ngx-smart-modal>