<!-- Modern History Modal Dialog -->
<div class="modern-modal-overlay" (click)="onClose()" [@fadeInOut] role="dialog" aria-modal="true"
    aria-labelledby="modal-title" aria-describedby="modal-description">
    <div class="modern-modal-container" (click)="$event.stopPropagation()" [@slideInOut]>
        <!-- Modal Header -->
        <header class="modal-header-modern">
            <div class="header-content">
                <div class="title-section">
                    <div class="icon-wrapper">
                        <mat-icon class="header-icon">history</mat-icon>
                    </div>
                    <div class="title-text">
                        <h2 id="modal-title" class="modal-title">Histórico de Informações</h2>
                        <p id="modal-description" class="modal-subtitle">
                            Dados coletados durante a conversa
                        </p>
                    </div>
                </div>
                <div class="header-actions">
                    <button mat-icon-button class="action-btn secondary" matTooltip="Exportar dados"
                        aria-label="Exportar dados coletados" (click)="exportData($event)">
                        <mat-icon>download</mat-icon>
                    </button>
                    <button mat-icon-button class="action-btn close-btn" (click)="onClose()" matTooltip="Fechar modal"
                        aria-label="Fechar modal">
                        <mat-icon>close</mat-icon>
                    </button>
                </div>
            </div>
            <!-- Progress Indicator -->
            <div class="progress-section">
                <div class="progress-info">
                    <span class="progress-text">
                        {{ getDadosPreenchidos().length }} de {{ getTotalCampos() }} campos preenchidos
                    </span>
                    <span class="progress-percentage">
                        {{ getProgressPercentage() }}%
                    </span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" [style.width.%]="getProgressPercentage()">
                    </div>
                </div>
            </div>
        </header>
        <!-- Search and Filter Section -->
        <div class="search-filter-section">
            <div class="search-container">
                <mat-form-field appearance="outline" class="search-field">
                    <mat-label>Buscar informação</mat-label>
                    <input matInput [(ngModel)]="searchTerm" placeholder="Digite para buscar..."
                        (input)="onSearchChange($event)">
                    <mat-icon matPrefix>search</mat-icon>
                    <button mat-icon-button matSuffix *ngIf="searchTerm" (click)="clearSearch()"
                        aria-label="Limpar busca">
                        <mat-icon>clear</mat-icon>
                    </button>
                </mat-form-field>
            </div>
            <div class="filter-chips">
                <mat-chip-listbox class="filter-chip-list">
                    <mat-chip-option *ngFor="let filter of availableFilters" [selected]="filter.active"
                        (click)="toggleFilter(filter)" [class]="'filter-chip-' + filter.type">
                        <mat-icon>{{ filter.icon }}</mat-icon>
                        {{ filter.label }}
                    </mat-chip-option>
                </mat-chip-listbox>
            </div>
        </div>
        <!-- Modal Body -->
        <main class="modal-body-modern">
            <div class="content-wrapper">
                <!-- Empty State -->
                <div class="empty-state" *ngIf="getFilteredData().length === 0">
                    <div class="empty-icon">
                        <mat-icon>search_off</mat-icon>
                    </div>
                    <h3 class="empty-title">Nenhuma informação encontrada</h3>
                    <p class="empty-description">
                        Tente ajustar os filtros ou termo de busca
                    </p>
                </div>
                <!-- Data Grid -->
                <div class="data-grid" *ngIf="getFilteredData().length > 0">
                    <div class="data-card" *ngFor="let item of getFilteredData(); trackBy: trackByFn" [@cardAnimation]
                        [class]="'card-' + item.category">
                        <div class="card-header">
                            <div class="card-icon">
                                <mat-icon>{{ item.icon }}</mat-icon>
                            </div>
                            <div class="card-title-section">
                                <h4 class="card-title">{{ item.label }}</h4>
                                <span class="card-category">{{ item.categoryLabel }}</span>
                            </div>
                            <div class="card-actions">
                                <button mat-icon-button class="card-action-btn" matTooltip="Copiar valor"
                                    (click)="copyToClipboard(item.value)" aria-label="Copiar valor">
                                    <mat-icon>content_copy</mat-icon>
                                </button>
                                <button mat-icon-button class="card-action-btn" matTooltip="Editar valor"
                                    (click)="editValue(item)" aria-label="Editar valor">
                                    <mat-icon>edit</mat-icon>
                                </button>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="value-container">
                                <span class="card-value" [innerHTML]="highlightSearchTerm(item.value)">
                                </span>
                            </div>
                            <div class="card-metadata">
                                <span class="timestamp" *ngIf="item.timestamp">
                                    <mat-icon>schedule</mat-icon>
                                    {{ formatTimestamp(item.timestamp) }}
                                </span>
                                <span class="validation-status" [class]="'status-' + item.validationStatus">
                                    <mat-icon>{{ getValidationIcon(item.validationStatus) }}</mat-icon>
                                    {{ getValidationLabel(item.validationStatus) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        <!-- Modal Footer -->
        <footer class="modal-footer-modern">
            <div class="footer-info">
                <span class="info-text">
                    <mat-icon>info</mat-icon>
                    Dados salvos automaticamente
                </span>
            </div>
            <div class="footer-actions">
                <button mat-stroked-button class="secondary-btn" (click)="clearAllData()"
                    [disabled]="getDadosPreenchidos().length === 0">
                    <mat-icon>delete_sweep</mat-icon>
                    Limpar Tudo
                </button>
                <button mat-raised-button color="primary" class="primary-btn" (click)="onClose()">
                    <mat-icon>check</mat-icon>
                    Concluído
                </button>
            </div>
        </footer>
    </div>
</div>