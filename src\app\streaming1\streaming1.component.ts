import { Component, ElementRef, ViewChild } from "@angular/core";
import { PainelAtendimentoService } from './../service/painel-atendimento.service';
import { NgxSmartModalModule, NgxSmartModalService } from "ngx-smart-modal";
import { ConsultaService } from "../service/consulta.service";
import "../../vendor/jitsi/external_api.js";
import { PacienteService } from "../service/pacientes.service";
import { TelemedicinaComponentBase } from "../Util/component.base";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { AnexosArquivos } from "../model/anexosConsulta";
import { DadosMedicosUsuario } from "../model/cliente";
import { CommonModule, Time } from "@angular/common";
import { documentosModal } from "../model/DocumentosModal";
import { documentosModalService } from "../documentacao/documentocao.service";
import { EnumTipoDocumentos } from "../Util/tipoDocumentos";
import { EnumTipoUsuario } from "../Util/tipoUsuario";
import { HubConnectionBuilder } from "@microsoft/signalr";
import { LogLevel, HubConnection } from "@microsoft/signalr";
import { environment } from "src/environments/environment";
import { AppComponent } from "../app.component";
import { UsuarioLogadoService } from "../auth/usuarioLogado.service";
import { CidService } from "../service/cid.service";
import { PesquisaCidService } from "../pesquisa-cid/pesquisa-cid.service";
import { LocalStorageService } from "../service/LocalStorageService";
import { ExamesService } from "../service/exameService.service";
import { SpinnerService } from "../service/spinner.service";
import { AlertComponent } from "../alert/alert.component";
import { FiltraExamesFormularios, analiseFormularioModel } from "../model/formularios";
import { AnaliseService } from "../service/analise.service";
import { FormulariosService } from "../service/formulario.service";
import { Analise } from "../model/analise";
import { ExamesModelView } from "../model/exames";
import { Router } from "@angular/router";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { MatIcon } from "@angular/material/icon";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatSelectModule } from "@angular/material/select";
import { MatExpansionModule } from '@angular/material/expansion';
import { MatCardModule } from "@angular/material/card";
import { MatTabsModule } from "@angular/material/tabs";
import { TruncatePipe } from "../Util/pipes/truncate.pipe";


// declare var JitsiMeetExternalAPI: any;
@Component({
    selector: "app-streaming1",
    templateUrl: "./streaming1.component.html",
    styleUrls: ["./streaming1.component.scss"],
    standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      NgxSmartModalModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      MatSelectModule,
      MatExpansionModule,
      MatCardModule,
      MatTabsModule,
      TruncatePipe    
    ]
})
export class Streaming1Component extends TelemedicinaComponentBase {
  constructor(
    private spinner: SpinnerService,
    private consultaService: ConsultaService,
    private tradutor: TranslateService,
    // private snackBar: MatSnackBar,
    private analiseService: AnaliseService,
    private snackBarAlert: AlertComponent,
    private pacientesService: PacienteService,
    public ngxSmartModalService: NgxSmartModalService,
    private router: Router,
    private pacienteService: PacienteService,
    private documentosServiceModal: documentosModalService,
    private formulariosService: FormulariosService,
    private appc: AppComponent,
    private usuarioLogadoService: UsuarioLogadoService,
    private cisService: CidService,
    private pesquisaCidService: PesquisaCidService,
    private localStorageService: LocalStorageService,
    private examesService: ExamesService,
    private painelAtendimentoService: PainelAtendimentoService
  ) {
    super();
    // router.events.takeUntil(this.onDestroy$).subscribe((event: Event) => {
    //   if (event instanceof ChildActivationStart) {
    //     const router = event.snapshot;
    //     ;
    //     this.streamService.telastream1$.emit(false);
    //   }
    // });
    this.cisService.changeCID$.subscribe((perf: any) => {
      if (perf != null) {
        this.desCID = perf;
        this.CarregaCIDProntuario();
      }
    });

    ;
    // this.streamService.telastream1$.emit(true);
  }

  dadosexame:any = [];
  spinnerHistorico: boolean = false;
  historico: boolean = false;
  seg?: number;
  min?: number;
  hr?: number;
  segundos: any = "00";
  minutos: any = "00";
  hora: any = "0";
  tempo?: Time;

  ListaCid: any = [];
  filtraCID: any = [];
  IdCID?: number;
  desCID?: string;
  CID?: string;

  Cronometro = false;
  IdConsulta?: number;
  Dados: any = [];
  spinnerAnexo: boolean = false;
  DadosanexConsulta: any = [];
  anonimo: boolean = false;
  corpo: boolean = false;
  consulta: boolean = true;
  anexo: boolean = false;
  validation = true;
  scrollDown: boolean = false;
  @ViewChild("scrollMe") private myScrollContainer?: ElementRef;
  ImagemPessoa: any = "assets/build/img/userdefault.png";
  IdPaciente?: number;
  DadosPacientes: any;
  // usuario: Usuario;
  DadosInformUsuario: any = [];
  DesConsulta: string = "";
  DesAnonimo: string = "";
  dadosHistorico: any = [];
  // name = "SnackbarContent";
  // AnexoSucesso: string = "Avaliação feita com Sucesso!  ✔ ";
  // CorpoSalvo: string = "Salvo com Sucesso!  ✔ ";
  // AnexoErro: string = "O arquivo precisa ter no maximo 30mbs. ";
  // actionButtonLabel: string = "Fechar";
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = "right";
  // verticalPosition: MatSnackBarVerticalPosition = "bottom";
  dadosConsulta: any = [];
  IdMedico?: number;
  primeiraConsulta: boolean = false;

  AdmPermissao = false;
  MedicoPermissao = false;
  AtendentePermissao = false;
  PacientePermissao = false;

  tipoUsuario: string = "";
  anexosArquivos = new AnexosArquivos();
  private hubConnection?: HubConnection;
  isHubConnected: boolean = false;

  // domain: string = "meet.jit.si";
  domain: string = "jitsi.medicinaparavoce.com.br";
  options: any;
  api: any;
  usuario?: number;
  toggle = {};
  Sairsala = false;
  panelOpenState = false;

  toogleBotoesMobile: boolean = false;
  src = "";
  // AbrirwebCan(): void {
  //   try {

  //     this.options = {
  //       roomName: 'medicinaparavoce' + this.IdConsulta,
  //       interfaceConfigOverwrite: {
  //         SHOW_JITSI_WATERMARK: false, SHOW_WATERMARK_FOR_GUESTS: false, INVITATION_POWERED_BY: false,
  //         AUTHENTICATION_ENABLE: false, DISPLAY_WELCOME_PAGE_CONTENT: false, GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false, VIDEO_QUALITY_LABEL_DISABLED: false,
  //         DEFAULT_LOCAL_DISPLAY_NAME: this.usuario.Nome, MOBILE_APP_PROMO: false, TOOLBAR_ALWAYS_VISIBLE: false,
  //         filmStripOnly: false,
  //         DEFAULT_REMOTE_DISPLAY_NAME: this.dadosConsulta.paciente, settings: false, videoquality: false, fullscreen: false, info: false,
  //         TOOLBAR_BUTTONS: ['fullscreen', 'microphone', 'camera'],
  //       },
  //       parentNode: document.querySelector('#meets')
  //     }

  //     this.api = new JitsiMeetExternalAPI(this.domain, this.options);
  //   } catch (error) {
  //     
  //     ;
  //     this.AlgumErro(true, error)

  //   }
  // }

  ngOnInit() {
    this.usuario = this.usuarioLogadoService.getIdUsuarioAcesso();
    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM) {
      this.tipoUsuario = "ADM Sistema";
      this.AdmPermissao = true;
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente
    ) {
      this.tipoUsuario = "Atendente";
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico
    ) {
      this.tipoUsuario = "Médico";
      this.MedicoPermissao = true;
      this.CarregaCIDProntuario();
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente
    ) {
      this.tipoUsuario = "Paciente";
      this.PacientePermissao = true;
    }
    var consulta = this.localStorageService.Consulta;

    if (!consulta) this.IdConsulta = 2875;
    else this.IdConsulta = consulta.idConsulta;

    if (this.IdConsulta != undefined) {
      this.Consulta();
      this.CarregarAnexos();
      this.initializeSignalIR();
    }
  }

  Consulta() {
    this.CarregaConsulta(this.IdConsulta);
    this.CarregarAnexos();

    this.Cronometro = true;
    this.TempoCons();
    setInterval(() => {
      if (this.Cronometro == true) {
        this.TempoCons();
      }
    }, 1000);

    // this.AbrirwebCan();
    // document.getElementById("btnGerarTarefa").click();
  }

  CarregaConsulta(id:any) {
    try {
      this.consultaService.GetConsulta(id).subscribe(
        (retorno) => {
          this.minutos = retorno[0].consulta.tempoConsulta.substring(3, 5);
          this.segundos = "00";
          this.IdMedico = retorno[0].idMedico;
          this.IdPaciente = retorno[0].idPaciente;
          ;
          ;

          this.dadosConsulta = retorno[0];
          if (this.dadosConsulta.consulta.flgPrimeiraConsulta == true)
            this.primeiraConsulta = true;

          ;

          this.historicoPaciente(this.dadosConsulta.idPaciente);
          this.CarregaPaciente(this.dadosConsulta.idPaciente);
        },
        (err) => {
          ;
          ;
          this.snackBarAlert.falhaSnackbar(err);
        }
      );
    } catch (error) {
      ;
      this.snackBarAlert.falhaSnackbar(error);
      ;
    }
  }

  TempoCons() {
    var zero = 0;
    this.seg;
    this.min;
    this.hr;

    if (parseInt(this.segundos) > 0) {
      this.segundos = parseInt(this.segundos) - 1;
      this.segundos = zero + this.segundos;
    } else if (this.segundos == 0) {
      if (this.minutos > 0) {
        this.segundos = 59;
        this.minutos = this.minutos - 1;
        if (this.minutos < 10) this.minutos = parseInt("0") + this.minutos;
      } else if (this.minutos == 0) {
        if (this.hora > 0) {
          this.segundos = 59;
          this.minutos = 59;
          this.hora = this.hora - 1;
          if (this.hora < 10) this.hora = parseInt("0") + this.minutos;
        }
      }
    }
  }

  BaixarPdfModal() {
    this.arquivoPdfDownload.click();
    this.ngxSmartModalService.getModal("PDF").close();
    this.arquivoPdfDownload = "";
  }
  spinnerAnexoDownload: boolean = false;
  arquivoPdfDownload: any;

  download(arq:any, nome:any, contentType:any) {
    if (!contentType) {
      contentType = "application/octet-stream";
    }
    this.arquivoPdfDownload = "";
    var a = document.createElement("a");
    var blob = new Blob([arq], { type: contentType });
    a.href = window.URL.createObjectURL(blob);
    this.src = a.href;
    a.download = nome;

    // if (tipoArquivo == "pdf" || nome.includes('.PDF')|| nome.includes('.pdf')) {
    //   this.arquivoPdfDownload = a
    //   this.ngxSmartModalService.getModal('PDF').open() ;
    //   this.spinnerAnexoDownload = false;
    // }
    // else {
    a.click();
    this.spinnerAnexoDownload = false;
    // }
  }

  BaixarArquivo(chave:any, nome:any) {
    this.consultaService.CarregaCaminhoArquivo(String(chave), nome).subscribe(
      (response) => {
        var application;
        this.download(response, nome, application);
        this.spinner.hide();
      },
      () => {
        this.spinnerAnexoDownload = false;
        this.spinner.hide();
      }
    );
  }
  // download(arq, nome, contentType) {
  //   if (!contentType) {
  //     contentType = 'application/octet-stream';
  //   }
  //   var a = document.createElement('a');
  //   var blob = new Blob([arq], { 'type': contentType });
  //   a.href = window.URL.createObjectURL(blob);
  //   a.download = nome;
  //   a.click();
  // }

  historicoPaciente(id:any) {
    try {
      this.spinnerHistorico = true;
      this.consultaService.GetDadoshistoricoPaciente(id).subscribe(
        (retorno) => {
          this.spinnerHistorico = false;
          ;

          //foreach item.receita <br> por \n
          retorno.forEach((element:any) => {
            if (element.receita)
              // element.receita = element.receita.replace("<br>", "/n");
              element.receita = element.receita.split("<br>").join("\n");
          });
          var retur = ([] = retorno);

          for (var i = 0, len = retur.length; i < len; ++i) {
            retur[i].posicaoRef = "#" + 1000 + i;
            retur[i].posicao = 1000 + i;
            // retur[i].
          }

          //
          //this.dadosHistorico = retorno;
          this.dadosHistorico = retur;

          this.validation = false;
          this.scrollToBottom();
        },
        (err) => {
          ;
          this.snackBarAlert.falhaSnackbar(err);
        }
      );
    } catch (error) {
      ;
      this.snackBarAlert.falhaSnackbar( error);
    }
  }

  ngAfterViewChecked() {
    if (this.scrollDown) {
      this.scrollToBottom();
      this.scrollDown = false;
    }
  }

  scrollToBottom(): void {
    try {
      if (this.validation == true) {
        if(this.myScrollContainer)
          this.myScrollContainer.nativeElement.scrollTop = this.myScrollContainer.nativeElement.scrollHeight;

        this.validation = false;
      } else {
        if(this.myScrollContainer)
          this.myScrollContainer.nativeElement.scrollToBottom = this.myScrollContainer.nativeElement.scrollHeight;

        this.validation = true;
      }
    } catch (err) { }
  }

  ExamesPaciente() {
    this.ExamesResult();

    this.examesService.GetResultados(this.IdPaciente, this.usuarioLogadoService.getIdUltimaClinica()
    )
      .subscribe((retorno) => {
        this.dadosexame = retorno;
        
        
        this.spinner.hide();
      });
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];
  //     this.tradutor.get("TELASTREAMING.FECHAR").subscribe((res: string) => {
  //       ;
  //       this.actionButtonLabel = res;
  //     });
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }

  informacao() {
    this.ngxSmartModalService.getModal("InforUsuario").open();
  }
  CampoConsulta() {
    if (this.DesConsulta != "" && this.DesConsulta != undefined) {
      this.consultaService
        .SalvarObservacaoConsulta(
          this.DesConsulta,
          this.IdConsulta,
          this.usuarioLogadoService.getIdUsuarioAcesso()
        )
        .subscribe(() => {
          this.snackBarAlert.sucessoSnackbar("Anotação salva com sucesso!");
        });
    }
  }
  CampoAnonimo() {
    if (this.DesAnonimo != "" && this.DesAnonimo != undefined) {
      this.consultaService
        .SalvarAnotacaoAnonima(
          this.DesAnonimo,
          this.IdConsulta,
          this.usuarioLogadoService.getIdUsuarioAcesso()
        )
        .subscribe(() => {
          this.snackBarAlert.sucessoSnackbar("Anotação anônima salva com sucesso!");
        });
    }
  }

  public CarregaPaciente(id:any) {
    try {
      this.pacientesService.getPacienteEdit(id).subscribe(
        (retorno) => {
          ;
          ;
          if (retorno) {
            this.DadosInformUsuario.nome = retorno.paciente.pessoa.nomePessoa;
            this.DadosInformUsuario.cpf = retorno.paciente.pessoa.cpf;
            this.DadosInformUsuario.email = retorno.paciente.pessoa.email;
            this.DadosInformUsuario.celular =
              retorno.paciente.contato.telefoneMovel;
            this.DadosInformUsuario.tel = retorno.paciente.contato.telefone;
            this.DadosInformUsuario.telComer =
              retorno.paciente.contato.telefoneComercial;
            this.DadosInformUsuario.IdUsuario =
              retorno.paciente.usuario.idUsuario;
            this.DadosInformUsuario.hoje = new Date().toLocaleDateString();
            this.DadosInformUsuario.hora = new Date().toLocaleTimeString();
            if (retorno.paciente.imagem64)
              this.ImagemPessoa = retorno.paciente.imagem64;

            var tipoUsu = retorno.paciente.usuario.idTipoUsuario;

            if (tipoUsu == EnumTipoUsuario.Atendente) {
              this.DadosInformUsuario.tipoUsuario = "Atendente";
            } else if (tipoUsu == EnumTipoUsuario.Médico) {
              this.DadosInformUsuario.tipoUsuario = "Médico";
            } else if (tipoUsu == EnumTipoUsuario.Paciente) {
              this.DadosInformUsuario.tipoUsuario = "Paciente";
            }
          }
          this.spinner.hide();
        },
        () => {
          this.tradutor
            .get("TELACADASTROPACIENTE.ERROAOCARREGARPACIENTE")
            .subscribe((res: string) => {
              ;
              this.snackBarAlert.falhaSnackbar(res);
            });
          this.spinner.hide();
        }
      );
    } catch (error) {
      this.tradutor
        .get("TELACADASTROPACIENTE.ERROAOCARREGARPACIENTE")
        .subscribe((res: string) => {
          ;
          this.snackBarAlert.falhaSnackbar( res);
        });
    }
  }

  DeleteAnexo(ChaveArquivo:any, idDocumento:any) {
    if (ChaveArquivo != "") {
      this.consultaService
        .RemoveArquivo(ChaveArquivo, idDocumento)
        .subscribe(() => {
          this.CarregarAnexos();
        });
    }
  }
  resultadosExame = false;
  ExamesResult() {
    this.resultadosExame = true;
    this.consulta = false;
    this.anonimo = false;
    this.anexo = false;
    this.corpo = false;
    this.historico = false;
  }
  ConsultMenu() {
    this.consulta = true;
    this.anonimo = false;
    this.anexo = false;
    this.corpo = false;
    this.historico = false;
    this.resultadosExame = false;
  }

  AnotMenu() {
    this.consulta = false;
    this.anonimo = true;
    this.anexo = false;
    this.corpo = false;
    this.historico = false;
    this.resultadosExame = false;
  }

  AnexoMenu() {
    if (this.anexo) this.anexo = false;
    else {
      this.anexo = true;
      this.corpo = false;
    }
  }

  DadosMenu() {
    if (this.corpo) this.corpo = false;
    else {
      this.DadosUsuario();
      this.corpo = true;
      this.anexo = false;
    }
  }

  abrirModalBoasVindas(teste: documentosModal) {
    this.documentosServiceModal.setAbrirModal(teste);
  }

  Atestado() {
    var documentos = new documentosModal();
    documentos.idConsulta = this.IdConsulta;
    documentos.TipoModal = EnumTipoDocumentos.Atestado;
    documentos.Caminho = "prontuario";
    documentos.idPaciente = this.IdPaciente;
    documentos.idMedico = this.IdMedico;
    // documentos.idMedico = this.
    this.abrirModalBoasVindas(documentos);
  }
  Declaracao() {
    var documentos = new documentosModal();
    documentos.TipoModal = EnumTipoDocumentos.Declaracao;
    documentos.idConsulta = this.IdConsulta;
    documentos.Caminho = "prontuario";
    documentos.idPaciente = this.IdPaciente;
    documentos.idMedico = this.IdMedico;
    this.abrirModalBoasVindas(documentos);
  }
  Receituario() {
    var documentos = new documentosModal();
    documentos.TipoModal = EnumTipoDocumentos.Receituario;
    documentos.idConsulta = this.IdConsulta!;
    documentos.Caminho = "prontuario";
    documentos.idPaciente = this.IdPaciente;
    documentos.idMedico = this.IdMedico;
    this.abrirModalBoasVindas(documentos);
  }

  PermissaoAnexoVisu(value:any) {
    this.spinnerAnexo = true;
    this.anexosArquivos.FlgVisualizacaoUsuario = value;
    this.consultaService
      .UploadArquivo(this.anexosArquivos, this.chaveArquivo)
      .subscribe(
        () => {this.CarregarAnexos();},
        () => {this.spinnerAnexo = false;}
      );
    this.ngxSmartModalService.getModal("PermissaoAnexoVisualizacao").close();
  }

  chaveArquivo:any;
  SubirArquivoConsulta(chave?:any) {
    this.chaveArquivo = null;
    this.anexosArquivos = new AnexosArquivos();

    this.spinnerAnexo = true;
    this.anexosArquivos.IdConsulta = this.IdConsulta;
    this.anexosArquivos.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    this.chaveArquivo = chave;

    if (this.tipoUsuario == "Paciente") {
      this.anexosArquivos.FlgVisualizacaoUsuario = true;
      this.consultaService.UploadArquivo(this.anexosArquivos, chave).subscribe(
        () => {
          this.CarregarAnexos();
        },
        () => {
          this.spinnerAnexo = false;
        }
      );
    } else {
      this.spinnerAnexo = false;
      this.ngxSmartModalService.getModal("PermissaoAnexoVisualizacao").open();
    }
  }

  CarregarAnexos() {
    try {
      this.spinnerAnexo = true;
      this.consultaService.AnexosConsulta(this.IdConsulta).subscribe(
        (retorno) => {
          this.DadosanexConsulta = retorno;
          this.spinnerAnexo = false;
          ;
          ;
        },
        (err) => {
          this.snackBarAlert.falhaSnackbar(err);
          console.error(err);
          this.spinnerAnexo = false;
        }
      );
    } catch (error) {
      this.snackBarAlert.falhaSnackbar(error);
      console.error(error);
    }
  }

  DadosUsuario() {


    if (this.Dados.length == 0) {
      this.pacienteService
        .getDadosPaciente(
          this.dadosConsulta.idPaciente,
          this.dadosConsulta.consulta.idConsulta
        )
        .subscribe(
          (retorno) => {
            ;
            ;
            ;
            if (retorno != null) {
              this.Dados.altura = this.verificaCasaDecimal(retorno.altura);
              this.Dados.peso = this.verificaCasaDecimal(retorno.peso);

              this.Dados.iMC = retorno.imc;
              this.Dados.pressao = retorno.pressao;
              this.Dados.batimento = retorno.batimento;
              this.Dados.temperatura = retorno.temperatura;
              this.Dados.idDadosMedicosUsuario = retorno.idDadosMedicosUsuario;
            }
            this.spinner.hide();
          },
          (err) => {
            console.error(err);
            this.spinner.hide();
          }
        );
    }
  }

  LimparDados() {
    this.Dados = [];
  }

  SalvarDadosCorpo() {


    var dados = new DadosMedicosUsuario();
    dados.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    dados.IdConsulta = this.IdConsulta;
    dados.DtaCadastro = new Date();
    dados.FlgInativo = false;

    dados.Peso = (document.getElementById("CampoPeso") as HTMLInputElement)["value"] ;
    dados.Altura = (document.getElementById("CampoAltura")as HTMLInputElement)["value"] ;
    dados.Batimento = this.Dados.batimento;
    dados.IMC = this.Dados.iMC;
    dados.Pressao = (document.getElementById("campoPressao")as HTMLInputElement)["value"] ;

    dados.Temperatura = (document.getElementById("campoTemp")as HTMLInputElement)["value"] ;
    dados.IdUsuario = this.DadosInformUsuario.IdUsuario;

    this.pacienteService.salvarDadosCorpo(dados).subscribe(
      () => {
        // let config = new MatSnackBarConfig();
        // config.verticalPosition = this.verticalPosition;
        // config.horizontalPosition = this.horizontalPosition;
        // config.duration = this.setAutoHide ? this.autoHide : 0;
        // config.panelClass = ["sucessoSnackbar"];

        var mensagem = "";
        this.tradutor
          .get("TELASTREAMING.SALVOCOMSUCESSO")
          .subscribe((res: string) => {
            ;
            mensagem = res;
          });

        this.snackBarAlert.sucessoSnackbar(mensagem);

        this.DadosUsuario();
        this.spinner.hide();

      },
      (err) => {
        console.error(err);
        this.spinner.hide();
      }
    );
  }

  CalculoIMC() {
    if (this.Dados.altura && this.Dados.peso) {
      var valorAltura = (document.getElementById("CampoAltura")as HTMLInputElement)["value"] ;
      var valorPeso = (document.getElementById("CampoPeso")as HTMLInputElement)["value"] ;
      var altura = Number(valorAltura) * Number(valorAltura);
      var calc = Number(valorPeso) / altura;
      this.Dados.iMC = calc.toFixed(2);
    } else this.Dados.iMC = null;
  }
  public mascaraPeso(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    var v = v,
      integer = v.split(".")[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = "0.0" + v;
      if (v.length === 2) v = "0." + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }

    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraAltura(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/^[0]+/, "");
    v = v.toString().replace(/\D/g, "");
    if (v.length >= 3) {
      v = v.toString().replace(/(\d{1})(\d{2})$/, "$1.$2");
    } else {
      v = v.toString().replace(/(\d{1})(\d{1})$/, "$1.$2");
    }
    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraTemperatura(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/^[0]+/, "");
    v = v.toString().replace(/\D/g, "");
    if (v.length == 3) {
      v = v.toString().replace(/(\d{1})(\d{1})$/, "$1.$2");
    } else if (v.length > 3) {
      v = v.toString().replace(/(\d{1})(\d{2})$/, "$1.$2");
    }
    (<HTMLInputElement>evento.target).value = v;
  }

  mascaraPressao(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    if (v.length < 3) {
      v = v.replace(/(\d{1})(\d{1})$/, "$1/$2");
    } else if (v.length < 4) {
      v = v.replace(/(\d{2})(\d{1})$/, "$1/$2");
    } else {
      v = v.replace(/(\d{1})(\d{2})$/, "$1/$2");
    }
    (<HTMLInputElement>evento.target).value = v;
  }

  mascaraNum(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/[^0-9]/g, "");
    // v = v.replace(/(\^|\'|\^|\[|\]|\_|\\|\`)/g,"");

    (<HTMLInputElement>evento.target).value = v;
  }

  verificaCasaDecimal(valor:any) {
    if (valor.toString().split(".").length < 2) {
      valor = valor + ".00";
    }
    if (valor.toString().split(".")[1].length == 1) {
      valor = valor + "0";
    }
    return valor;
  }

  CarregaCIDProntuario() {


    this.cisService.GetListaFiltaPorCod(0, 100, this.desCID).subscribe(
      (retorno) => {
        this.ListaCid = retorno;
        this.filtraCID = this.ListaCid.filter(
          (c:any) => c.codCid == this.desCID?.toUpperCase()
        );
        if (this.filtraCID.length > 0) {
          this.CID = this.filtraCID[0].desCid;
        } else {
          this.tradutor
            .get("TELADOCUMENTACAO.CODNAOCORRESPONDEADOENCA")
            .subscribe((res: string) => {
              ;
              this.CID = res;
            });
        }
        ;
        this.spinner.hide();

      },
      (err) => {
        console.error(err);
        this.spinner.hide();

      }
    );
  }

  Finalizar() {


    this.IdCID = undefined;
    if (this.desCID) {
      this.IdCID = this.filtraCID[0].idCid;
      ;
    }

    this.CampoConsulta();
    this.CampoAnonimo();
    this.consultaService
      .FinalizarConsultaProntuario(
        this.IdConsulta,
        this.IdCID,
        this.usuarioLogadoService.getIdUsuarioAcesso()
      )
      .subscribe((retorno) => {

        this.spinner.hide();

        if (retorno != null) {
          this.ngxSmartModalService.getModal("FinalizarChamada").close();
          // this.BtnSair();[
          this.router.navigate(["/consulta"]);
          this.Sairsala = true;
        }
      });
  }
  AbrirModalPesquisaCid() {
    this.pesquisaCidService.setModalPesquisaCid();
  }

  BtnSair() {
    ;
    // sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(["/"]);
    // this.streamService.telastream$.emit(false);
    window.location.href = "/";

    // this.appc.ngOnInit();
  }

  addParagrafo() {
    (document.getElementById("txtConsulta")as HTMLInputElement)["value"]  =
      (document.getElementById("txtConsulta")as HTMLInputElement)["value"]  + "" + "\n";
    (document.getElementById("txtConsulta")as HTMLInputElement)["value"]  = 
          (document.getElementById("txtConsulta") as HTMLInputElement)["value"].replace(/\\n/g, "<br>");
  }

  addParagrafoAnonimo() {
    (document.getElementById("txtAnonimo")as HTMLInputElement)["value"]  =
      (document.getElementById("txtAnonimo")as HTMLInputElement)["value"]  + "" + "\n";
    (document.getElementById("txtAnonimo")as HTMLInputElement)["value"]  = 
        (document.getElementById("txtAnonimo")as HTMLInputElement)["value"].replace(/\\n/g, "<br>");
  }

  fileToUpload?: FileList;

  fileuploadesquerdo(event: DragEvent) {
    event.preventDefault();
    this.fileToUpload = event.dataTransfer?.files;
    this.uploadFileToActivity();
  }

  uploadFileToActivity() {
    this.SubirArquivoConsulta(this.fileToUpload);
    this.fileToUpload = undefined;
  }

  destacaBorda(event:any) {
    event.preventDefault();

    const divArquivo = document.getElementById("divArquivo");
    if(divArquivo)
       divArquivo.style.border = "dotted 2px cornflowerblue";
  }

  tiraBorada(event:any) {
    event.preventDefault();
    
    const divArquivo = document.getElementById("divArquivo");
    if(divArquivo)
       divArquivo.style.border = "";
  }

  Modo1: boolean = true;
  Modo2: boolean = false;

  async MudarModo(obj:any) {
    if (obj == "Modo1") {
      this.Modo1 = true;
      this.Modo2 = false;
    } else {
      this.Modo1 = false;
      this.Modo2 = true;
    }
  }

  startSignalR() {
    if(this.hubConnection)
      this.hubConnection
        .start()
        .then(() => {
          this.isHubConnected = true;
        })
        .catch((err) => {
          console.error("ERROR CONNECT", err);
          this.isHubConnected = false;
        });
  }

  initializeSignalIR() {
    const buildHubConn = new HubConnectionBuilder();
    buildHubConn.configureLogging(LogLevel.Information);
    buildHubConn.withUrl(environment.apiEndpoint + "/notify");
    this.hubConnection = buildHubConn.build();

    this.startSignalR();
    this.hubConnection.onclose(() => {
      //console.error(err);
      setTimeout(() => this.startSignalR(), 1000);
    });
    this.hubConnection.on("AlertAnexo", (res: number) => {
      if (res == this.IdConsulta) this.CarregarAnexos();
    });
    if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente
    ) {
      this.hubConnection.on("FinalizaConsulta", (res) => {
        if (res == this.IdConsulta) {
          this.ngxSmartModalService.getModal("Avaliacao").open();
          this.api.dispose();
        }
      });
    }
  }
  recusarSair() {
    this.ngxSmartModalService.getModal("Inicializando").close();
    sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(["/"]);
    this.appc.ngOnInit();
  }

  // Inicializacao() {
  //   this.Cronometro = true;
  //   this.AbrirwebCan()

  //   this.TempoCons()
  //   setInterval(() => {
  //     if (this.Cronometro == true) {
  //       this.TempoCons()
  //     }
  //   }, 1000);

  //   this.ngxSmartModalService.getModal('Inicializando').close();
  // }

  isOpen = false;

  fecharBotoesMobile() {
    if (!this.isOpen)
      if (this.toogleBotoesMobile) this.toogleBotoesMobile = false;

    this.isOpen = false;
  }

  openToggle() {
    this.isOpen = true;
    this.toogleBotoesMobile = !this.toogleBotoesMobile;
  }

  flgConsultaAnonimo() {
    this.consulta = false;
    this.anonimo = false;
  }

  RechamarPaciente(){
    this.painelAtendimentoService.rechamarPacientePainel(this.IdConsulta).subscribe()
  }

  //--------------------------------------------------------------------------- MODAL ------------------------------------------------------------------------
  idAnalise?: number;
  objAnalise= new Analise();


  listaExames: any = [];

  listaAllFormularios: analiseFormularioModel[] = [];
  listaExibeFormularios: analiseFormularioModel[] = [];
  listaSelecionaFormularios: analiseFormularioModel[] = [];


  pesquisa = '';

  listaAllExames      : ExamesModelView[] = [];
  listaExibeExames    : ExamesModelView[] = [];
  listaSelecionaExames: ExamesModelView[] = [];

  FiltroExamesSelecionados?: FiltraExamesFormularios;

  listaAnalise: any = [];
  listaAnaliseTodos: any = [];

  examesSelecionados: number[] = [];

  listaNovosFormularios: number[] = [];


  async AbreModalAnalise() {
    

    this.analiseService.GetAnalises().subscribe((retorno) => {
      this.listaAnalise = []
      

      this.listaAnaliseTodos = this.listaAnalise = retorno;
    });

    await this.FecharModal('AnaliseModal');
    await this.AbrirModal('AnaliseModal')
    this.CarregaAllFormularios()
    this.CarregaAllExames()
  }




  editar(idAnalise: number) {
    
    this.idAnalise = idAnalise

    this.FiltraFormularioExames();

    this.AbrirModal('NovaAnaliseModal');
  }

  adicionaranalise() {
    
    

    this.objAnalise.idPaciente = this.IdPaciente;

    this.FecharModal('AnaliseModal');
    this.AbrirModal('NovaAnaliseModal');
  }

  //Quando o formulário for carregado com uma análise já existente esse metodo limpa so select os valores já escolhidos
  FiltraFormularioExames() {
    this.formulariosService.getIdsFormsExames(this.idAnalise!).subscribe((ret) => {
      
      this.ResetaListas_Filtro()

      //carrega listas exames
      ret.idsExames?.forEach(id => {
        id;
        this.listaExibeExames = this.listaAllExames.filter(exame => ret.idsExames?.includes(exame.idExameClinica!));
        this.listaSelecionaExames = this.listaAllExames.filter(exame => !ret.idsExames?.includes(exame.idExameClinica!));
      });
      
      //carrega lista formulario
      ret.idsForms?.forEach(id => {
        id;
        this.listaExibeFormularios = this.listaAllFormularios.filter(form => ret.idsForms?.includes(form.idFormulario!));
        this.listaSelecionaFormularios = this.listaAllFormularios.filter(form => !ret.idsForms?.includes(form.idFormulario!));
      });
    })
  }

  ResetaListas_Filtro() {
    //reseta listas exames
    this.listaExibeExames = [];
    this.listaSelecionaExames = [];
    
    // this.listaAllExames.forEach(element => {
    //   this.listaSelecionaExames.push(element)
    // });

    // this.listaSelecionaExames = [...this.listaAllExames] ;

    //reseta lista formulario
    this.listaExibeFormularios = []
    this.listaSelecionaFormularios = []

    // this.listaAllFormularios.forEach(element => {
    //   this.listaSelecionaFormularios.push(element)
    // });

    // this.listaSelecionaFormularios = [...this.listaAllFormularios]
  }

  AnaliseModal() { 
    

  }

  SalvarAnalise() {

  }

  AbrirModal(nomeModal: string) {
    this.ngxSmartModalService.getModal(nomeModal).open();
  }

  FecharModal(nomeModal: string) {
    this.ngxSmartModalService.getModal(nomeModal).close();
  }

  CarregaAllFormularios() {
    this.formulariosService.getAllFormularios().subscribe((ret) => {
      
      ret.forEach(element => {
        let objFormulario = new analiseFormularioModel();
        objFormulario.idFormulario = element.idFormulario;
        objFormulario.nomeFormulario = element.nomeFormulario;
        objFormulario.dtaCadastro = element.dtaCadastro;
        this.listaAllFormularios.push(objFormulario);
      });

    })
  }

  CarregaAllExames() {
    this.analiseService.ListaExamesParaAnalise().subscribe((retorno) => {
      this.listaAllExames = retorno;
    })
  }

  filtrarAnalise() {
    this.listaAnalise = this.listaAnaliseTodos.filter((x: any) => x.nomePaciente.toUpperCase().includes(this.pesquisa.toUpperCase()))
  }
}