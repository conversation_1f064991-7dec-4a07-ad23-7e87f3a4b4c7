export class TipoAgendamento {
    IdTipoAgendamento?: number;
    DesTipoAgendamento?: string;
    DtaCadastro?: Date;
    idClinica?: number;
    IdUsuarioGerador?: number;
    FlgInativo?: boolean;
}

export class AgendaEspera {

    IdAgendaEspera?: number;
    DesAnotacao?: string;
    TelContato?: string;
    IdPaciente?: number;
    IdMedico?: number;
    DtaConsulta?: Date;
    DtaCadastro?: Date;
    IdUsuarioGerador?: number;
    FlgInativo?: Boolean;
    idClinica?: number;
    FlgProntuario: boolean = false;

}
