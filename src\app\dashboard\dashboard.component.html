<mat-card appearance="outlined" class="dashboard-card ">
    <!-- DASHBOARD PRINCIPAL -->
    <div class="dashboard-container">
      <!-- CABEÇALHO DE BOAS-VINDAS -->
      <div class="welcome-header" *ngIf="MedicoPermissao || !MedicoPermissao && !AtendentePermissao">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>Olá, {{ this.usuarioLogadoService.getNomeUsuario()}}</h2>
            <p>Seja bem-vindo(a) à sua dashboard teste</p>
          </div>
          
        </div>
      </div>
      
      <!-- DASHBOARD DO MÉDICO -->
      <div class="dashboard-section" *ngIf="MedicoPermissao">
        <!-- RESUMO DE CONSULTAS -->
        <div class="consultations-summary">
          <div class="summary-card">
            <div class="div-matIcon">
              <mat-icon>
                event_available
              </mat-icon>
            </div>
           <div class="summary-content">
              <p class="summary-label">Consultas concluídas</p>
              <span class="summary-number ">{{consultasConcluida}} <span >Consultas</span> </span>
           </div>
          </div>
          
          <div class="summary-card">
            <div class="div-matIcon">
              <mat-icon>
                today
              </mat-icon>
            </div>
            <div class="summary-content">
              <p class="summary-label">Consultas agendadas</p>
              <span class="summary-number ">{{consultasAgendanda}} <span>Consultas</span></span>
            </div>
          </div>
          
          <div class="summary-card">
            <div class="div-matIcon">
              <mat-icon>
                event_busy
              </mat-icon>
            </div>
           <div class="summary-content">
              <p class="summary-label">Consultas canceladas</p>
              <span class="summary-number ">{{consultasCancelada}} <span >Consultas</span></span>
           </div>
          </div>
        </div>
        <div class="exames-dados">
          <div class="health-dashboard-container">
            <div class="health-chart-card">
              <div class="chart-header">
                <div>
                  <h2>Distribuição de Fatores de Saúde</h2>
                  <p class="chart-description">Análise percentual dos componentes para uma vida saudável</p>
                </div>
                <div>
                  <select name="" id="">
                    <option value="">Mês</option>
                    <option value="">Semana</option>
                    <option value="">Dia</option>
                  </select>
                </div>
              </div>
              
              <div class="chart-content">
                <div class="chart-wrapper">
                  <canvas baseChart
                    [data]="pieChartDatateste"
                    [type]="pieChartTypeteste"
                    [options]="pieChartOptionsteste">
                  </canvas>
                </div>
              </div>
            </div>
          </div>
          <div class="proximas-consultas">
            <div class="upcoming-card">
                <div class="upcoming-header">
                  <h3>Próximas Consultas</h3>
                  <button mat-icon-button class="view-all-button">
                    <mat-icon>calendar_month</mat-icon>
                  </button>
                </div>
              
                <div class="upcoming-list">
                  <div class="upcoming-day">
                    <div class="day-label">
                      <span class="day-name">Hoje</span>
                      <span class="day-date">29 Abril</span>
                    </div>
              
                    <div class="upcoming-item">
                      <div class="time-indicator">
                        <div class="time-dot active"></div>
                        <div class="time-line"></div>
                      </div>
                      <div class="appointment-time">14:30</div>
                      <div class="appointment-details">
                        <h4>Dr. Carlos Mendes</h4>
                        <p>Consulta de Rotina</p>
                      </div>
                      <div class="appointment-status in-person">
                        <mat-icon>person</mat-icon>
                      </div>
                    </div>
              
                    <div class="upcoming-item">
                      <div class="time-indicator">
                        <div class="time-dot"></div>
                        <div class="time-line"></div>
                      </div>
                      <div class="appointment-time">16:45</div>
                      <div class="appointment-details">
                        <h4>Dra. Ana Silva</h4>
                        <p>Exame de Sangue</p>
                      </div>
                      <div class="appointment-status virtual">
                        <mat-icon>videocam</mat-icon>
                      </div>
                    </div>
                  </div>
              
                  <div class="upcoming-day">
                    <div class="day-label">
                      <span class="day-name">Amanhã</span>
                      <span class="day-date">30 Abril</span>
                    </div>
              
                    <div class="upcoming-item">
                      <div class="time-indicator">
                        <div class="time-dot"></div>
                        <div class="time-line"></div>
                      </div>
                      <div class="appointment-time">09:15</div>
                      <div class="appointment-details">
                        <h4>Dr. Ricardo Alves</h4>
                        <p>Avaliação Cardíaca</p>
                      </div>
                      <div class="appointment-status in-person">
                        <mat-icon>person</mat-icon>
                      </div>
                    </div>
                  </div>
              
                  <div class="upcoming-day">
                    <div class="day-label">
                      <span class="day-name">Segunda</span>
                      <span class="day-date">05 Maio</span>
                    </div>
              
                    <div class="upcoming-item">
                      <div class="time-indicator">
                        <div class="time-dot"></div>
                      </div>
                      <div class="appointment-time">11:00</div>
                      <div class="appointment-details">
                        <h4>Dra. Camila Rocha</h4>
                        <p>Retorno Ortopédico</p>
                      </div>
                      <div class="appointment-status virtual">
                        <mat-icon>videocam</mat-icon>
                      </div>
                    </div>
                  </div>
                </div>
              
                <div class="upcoming-footer">
                  <button mat-button class="schedule-button" (click)="Agenda()">
                    <span>Agendar Nova Consulta</span>
                  </button>
                </div>
            </div>
          </div>
        </div>
      </div>
      
      
      <!-- DASHBOARD DO PACIENTE -->
      <div class="patient-dashboard" *ngIf="!MedicoPermissao && !AtendentePermissao">
        <div class="dashboard-layout-paciente">
          <!-- ILUSTRAÇÃO DO CORPO -->
          <div class="body-illustration">
            <img src="/assets/build/img/corpohumanoossos.png" alt="Ilustração anatômica">
          </div>
          
          <!-- SEÇÃO PRINCIPAL -->
          <div class="dashboard-main">
            <!-- LINKS RÁPIDOS -->
            <div class="quick-links">
              <!-- EXAMES -->
              <div class="quick-link-card" (click)="acessarExames()">
                <div class="link-content">
                  <div class="link-icon">
                    <img src="/assets/build/img/ossos.png" alt="Exames">
                  </div>
                  <div class="link-text">
                    <h3>Exames</h3>
                    <p>Acesse seus exames</p>
                  </div>
                </div>
                <div class="link-action">
                  <span class="material-icons">navigate_next</span>
                </div>
              </div>
              
              <!-- NUTRIÇÃO -->
              <div class="quick-link-card" (click)="carregarOrientacaoPessoa(2)">
                <div class="link-content">
                  <div class="link-icon">
                    <img src="/assets/build/img/maca.png" alt="Nutrição">
                  </div>
                  <div class="link-text">
                    <h3>Nutrição</h3>
                    <p>Acesse sua dieta</p>
                  </div>
                </div>
                <div class="link-action">
                  <span class="material-icons">navigate_next</span>
                </div>
              </div>
              
              <!-- FISIOTERAPIA -->
              <div class="quick-link-card" (click)="carregarOrientacaoPessoa(3)">
                <div class="link-content">
                  <div class="link-icon">
                    <img src="/assets/build/img/fisio.png" alt="Fisioterapia">
                  </div>
                  <div class="link-text">
                    <h3>Fisioterapia</h3>
                    <p>Acesse suas recomendações</p>
                  </div>
                </div>
                <div class="link-action">
                  <span class="material-icons">navigate_next</span>
                </div>
              </div>
              
              <!-- MÉDICO -->
              <div class="quick-link-card" (click)="carregarOrientacaoPessoa(1)">
                <div class="link-content">
                  <div class="link-icon">
                    <img src="/assets/build/img/medical.png" alt="Médico">
                  </div>
                  <div class="link-text">
                    <h3>Médico</h3>
                    <p>Histórico de consultas</p>
                  </div>
                </div>
                <div class="link-action">
                  <span class="material-icons">navigate_next</span>
                </div>
              </div>
              
              <!-- Consulta -->
              <div class="quick-link-card" (click)="CarregarListaConsultasAtivas()">
                <div class="link-content">
                  <div class="link-icon">
                    <img src="/assets/build/img/support.png" alt="Consulta">
                  </div>
                  <div class="link-text">
                    <h3>Consulta</h3>
                    <p>Acessar consulta</p>
                  </div>
                </div>
                <div class="link-action">
                  <span class="material-icons">navigate_next</span>
                </div>
              </div>
            </div>
            
            <!-- PENDÊNCIAS -->
            <div class="pending-tasks">
              <div class="pending-card">
                <div class="pending-info">
                  <span class="pending-count">02</span>
                  <p>Pendências de Exames</p>
                </div>
              </div>
              
              <div class="pending-card">
                <div class="pending-info">
                  <span class="pending-count">04</span>
                  <p>Pendências de Nutrição</p>
                </div>
              </div>
              
              <div class="pending-card">
                <div class="pending-info">
                  <span class="pending-count">02</span>
                  <p>Pendências de Fisioterapia</p>
                </div>
              </div>
              
              <div class="pending-card">
                <div class="pending-info">
                  <span class="pending-count">01</span>
                  <p>Pendências Médicas</p>
                </div>
              </div>
            </div>
            
            <!-- PROGRESSO DE SAÚDE -->
            <div class="health-progress">
              <h3>Veja como anda sua saúde</h3>
              <mat-progress-bar mode="determinate" value="40"></mat-progress-bar>
            </div>
          </div>
        </div>
      </div>
      
      
     
    </div>
  </mat-card>
  
  <!-- MODAL DE ORIENTAÇÃO -->
  <ngx-smart-modal #modalOrientacao identifier="modalOrientacao" class="orientation-modal" customClass="medium-modal">
    <div class="modal-container">
      <div class="modal-header">
        <div class="header-icon">
          <mat-icon>group</mat-icon>
        </div>
        <h2 class="header-title">Orientação</h2>
      </div>
      
      <div class="modal-body">
        <div class="orientation-list">
          <div class="orientation-item" *ngFor="let item of listaAnalise">
            <div class="item-header">
              <div class="professional-info">
                <h4>Profissional</h4>
                <p>{{item.nomeMedico}}</p>
                <p>{{item.tipoOrientacao}}</p>
              </div>
              
              <div class="date-info">
                <h4>{{ 'TELACOLUNADIREITA.DATADECADASTRO' | translate }}</h4>
                <p>{{item.dtaCadastro | date:'dd/MM/yyyy HH:mm'}}</p>
              </div>
            </div>
            
            <div class="orientation-content">
              <h4>Observação:</h4>
              <div class="content-text" [innerHTML]="item.orientacao"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ngx-smart-modal>