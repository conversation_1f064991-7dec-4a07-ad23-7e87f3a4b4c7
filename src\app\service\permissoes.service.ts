import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { Perfil } from '../model/perfil';
import { SpinnerService } from './spinner.service';

@Injectable({
    providedIn: 'root'
})
export class PermissoesService {
    constructor(
        private http: HttpClient,
        private spinner: SpinnerService
    ) {
    }
    public headers = new Headers({ 'Content-Type': 'application/json' });



    public GetPermissoes(inicio:any, fim:any, idClinica:any, idUsuario:any, pesquisa:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('idClinica', String(idClinica));
        params = params.append('idUsuario', String(idUsuario));
        params = params.append('pesquisa', String(pesquisa));
        return this.http.get(environment.apiEndpoint + '/Permissao/GetPermissoes', { params });
    }

    public SalvarPerfil(perfil: Perfil): Observable<any> {
        this.spinner.show();
        return this.http.post(environment.apiEndpoint + '/Permissao/SalvarPerfil', perfil);

    }

    public GetListaPerfil(inicio:any, fim:any, idClinica:any, idUsuario:any, pesquisa:any): Observable<any> {
        this.spinner.show();
        let params = new HttpParams();
        params = params.append('inicio', String(inicio));
        params = params.append('fim', String(fim));
        params = params.append('idClinica', String(idClinica));
        params = params.append('idUsuario', String(idUsuario));
        params = params.append('pesquisa', String(pesquisa));

        return this.http.get(environment.apiEndpoint + '/Permissao/GetListaPerfil', { params });

    }

    public InativarPerfil(idPerfil:any, idUsuario:any): Observable<any> {
        this.spinner.show();
        return this.http.delete(environment.apiEndpoint + '/Permissao/InativarPerfil/' + idPerfil + '/' + idUsuario);
    }

    public GetPerfilEdicao(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Permissao/GetPerfilEdicao/' + id);
    }
    
    public GetPerfilPermissaoPorId(id:any): Observable<any> {
        this.spinner.show();
        return this.http.get(environment.apiEndpoint + '/Permissao/GetPerfilPermissaoPorId/' + id);
    }


}