// Variáveis de Design
$primary-color: #2E8B57;
$primary-light: #f3fff3;
$primary-dark: #175834;
$secondary-color: #f4f4f9;
$secondary-light: #f4f2fc;
$secondary-dark: #175834;
$accent-color: #175834;
$error-color: #dc2626;
$text-primary: #1e293b;
$text-secondary: #64748b;
$border-color: #e2e8f0;
$bg-color: #f8fafc;
$card-bg: #ffffff;
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

// Resetando alguns estilos
* {
  box-sizing: border-box;
}

body {
  background-color: $bg-color;
  color: $text-primary;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

// Cartão do Perfil
.profile-card {
  max-width: 1000px; // Aumentado para maior largura
  margin: 1rem auto;
  border-radius: $border-radius !important;
  border: none !important;
  background-color: $card-bg !important;
  box-shadow: $box-shadow !important;
  overflow: hidden;
  height: 66vh; 
  border-top:4px solid #2E8B57 !important ;

}

.profile-card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

// Cabeçalho do Perfil
.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid $border-color;
  flex: 0 0 auto; // Não cresce nem encolhe
}

.profile-title h2 {
  color: $primary-color;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-transform: none;
}

.btn-return {
  display: flex;
  align-items: center;
  color: $primary-color;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color $transition;
  background: none;
  padding: 6px 12px;
  text-decoration: none;
  
  &:hover {
    background-color: rgba($primary-color, 0.05);
  }
  mat-icon {
    margin-right: 0.25rem;
    font-size: 1.25rem;
    height: 1.25rem;
    width: 1.25rem;
  }
}

// Conteúdo do Perfil - Layout com sidebar
.profile-content {
  display: flex;
  padding: 1rem;
  gap: 2rem;
  flex: 1 1 auto; // Cresce para preencher o espaço disponível
  overflow-y: auto; // Adiciona scroll se o conteúdo for maior que o espaço
}

// Sidebar com foto e informações básicas
.profile-sidebar {
  flex: 0 0 250px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-right: 1px solid $border-color;
  padding-right: 2rem;
}

.profile-info {
  width: 100%;
  text-align: center;
  margin-top: 1rem;
}

.profile-name {
  color: $text-primary;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0.5rem 0;
}

.profile-email {
  color: $text-secondary;
  font-size: 0.875rem;
  margin: 0;
}

// Seção da Imagem de Perfil
.profile-image-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.profile-image-wrapper {
  position: relative;
  width: 150px; // Aumentado para destacar mais
  height: 150px; // Aumentado para destacar mais
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform $transition;
  
  &:hover {
    transform: scale(1.05);
    
    .profile-image-overlay {
      opacity: 1;
    }
  }
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.profile-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba($primary-dark, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity $transition;
  
  mat-icon {
    color: white;
    font-size: 2rem;
  }
}

// Seção do Formulário
.profile-form-container {
  flex: 1;
  max-width: 100%;
}

.section-title {
  color: $primary-color;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid $primary-light;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.75rem;
}

.form-group {
  padding: 0 0.75rem;
  margin-bottom: 1.5rem;
}

.form-group-full {
  width: 100%;
}

.form-group-half {
  width: 100%;
  
  @media (min-width: 576px) {
    width: 50%;
  }
}

.form-separator {
  width: 100%;
  margin: 1.5rem 0;
  position: relative;
  text-align: center;
  
  &:before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: $border-color;
  }
  
  span {
    position: relative;
    display: inline-block;
    background-color: $card-bg;
    padding: 0 1rem;
    color: $primary-color;
    font-weight: 500;
    font-size: 0.875rem;
  }
}

.form-group label {
  display: block;
  margin-bottom: 0.1rem;
  color: $text-primary;
  font-weight: 500;
  font-size: 0.875rem;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 5px 1rem;
  border: 1px solid $border-color;
  border-radius: 8px;
  font-size: 1rem;
  color: $text-primary;
  background-color: white;
  transition: border-color $transition, box-shadow $transition;
  
  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }
  
  &::placeholder {
    color: lighten($text-secondary, 15%);
  }
}

.input-error {
  color: $error-color;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

// Campos de Senha
.password-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: $text-secondary;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color $transition;
  
  &:hover {
    color: $primary-color;
  }
  
  mat-icon {
    font-size: 1.25rem;
  }
}

// Rodapé do Perfil
.profile-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 1.5rem;
  border-top: 1px solid $border-color;
  flex: 0 0 auto; // Não cresce nem encolhe
}

.btn-save {
  display: flex;
  align-items: center;
  background-color: $primary-color;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color $transition;
  
  &:hover {
    background-color: darken($primary-color, 5%);
  }
  
  mat-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
}

// Responsividade
@media (min-width: 768px) {
  .profile-content {
    flex-direction: row;
  }
}

@media (max-width: 767px) {
  .profile-card {
    max-width: 100%;
    margin: 0.5rem;
    height: 85vh;
  }
  
  .profile-content {
    flex-direction: column;
    padding: 1rem;
  }
  
  .profile-sidebar {
    flex: 0 0 auto;
    width: 100%;
    border-right: none;
    border-bottom: 1px solid $border-color;
    padding-right: 0;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
  }
  
  .profile-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
  }
  
  .profile-title {
    margin-bottom: 0.5rem;
  }
  
  .profile-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }
  
  .form-group-half {
    width: 100%;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .profile-image-wrapper {
    width: 120px;
    height: 120px;
  }
  
  .profile-footer {
    padding: 0.75rem 1rem;
  }
}