<!-- Modal Faturamento -->
<div class="modal-container modalemail">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">Modal Faturamento</h3>
        </div>
    </header>
    
    <main class="modal-container">
        <div class="content-columns">
            <!-- Coluna do formulário -->
            <div class="form-column">
                <form (ngSubmit)="validarFormulario()" class="form-container">
                    <div class="form-field">
                        <label for="selConvenio">Seleção de convênio</label>
                        <select class="form-control custom-select" formControlName="idConvenio" id="selConvenio" required>
                            <option *ngFor="let consulta of lsConvenios" [value]="consulta.idConvenio">
                                {{ consulta.desConvenio }}
                            </option>
                        </select>
                        <div *ngIf="formFaturamento.get('idConvenio')?.invalid && formFaturamento.get('idConvenio')?.touched"
                            class="error-message">
                            Selecione um convênio válido.
                        </div>
                    </div>
                    
                    <div class="form-field">
                        <label for="codConvenio">{{ 'TELAAGENDA.CODIGOCONVENIO' | translate }}</label>
                        <input type="text" class="form-control custom-input" id="codConvenio" formControlName="codigoConvenio" required>
                        <div *ngIf="formFaturamento.get('codigoConvenio')?.invalid && formFaturamento.get('codigoConvenio')?.touched"
                            class="error-message">
                            O código do convênio é obrigatório.
                        </div>
                    </div>
                    
                    <div class="form-field">
                        <label for="selFatura">Seleção de fatura:</label>
                        <select class="form-control custom-select" id="selFatura" formControlName="idFatura" required>
                            <option *ngFor="let consulta of lsFaturas" [value]="consulta.idFatura">
                                {{ consulta.descricao }}
                            </option>
                        </select>
                        <div *ngIf="formFaturamento.get('idFatura')?.invalid && formFaturamento.get('idFatura')?.touched"
                            class="error-message">
                            Selecione uma fatura válida.
                        </div>
                    </div>
                    
                    <div class="form-field">
                        <label for="VlrConsulta">Valor:</label>
                        <input type="number" class="form-control custom-input" id="VlrConsulta" formControlName="valorConsulta" required>
                        <div *ngIf="formFaturamento.get('valorConsulta')?.invalid && formFaturamento.get('valorConsulta')?.touched"
                            class="error-message">
                            O valor da consulta é obrigatório.
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Coluna da lista de consultas -->
            <div class="list-column" *ngIf="this.flgCarregamentoGeral">
                <div class="list-container">
                    <div class="empty-state" *ngIf="lsConsultas.length < 1">Não há consultas</div>
                    <div *ngFor="let item of lsConsultas" class="list-item" (click)="AlteraStatusConsultaLista(item.idConsulta);">
                        <div class="checkbox-container">
                            <input type="checkbox" [checked]="ValidaUsuarioSelecionado(item.idConsulta)">
                        </div>
                        <div class="item-details">
                            <div class="detail-row">
                                <span class="detail-label">Doutor:</span>
                                <span class="detail-value">{{item.medico.pessoa.nomePessoa}}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Paciente:</span>
                                <span class="detail-value">{{item.paciente.pessoa.nomePessoa}}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">Data:</span>
                                <span class="detail-value">{{ item.dtaConsulta | date: 'dd/MM/yyyy HH:mm' }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <footer class="modal-footer">
        <button class="action-button cancel-button" (click)="fecharModal()">
            <mat-icon>cancel</mat-icon>
            <span>Cancelar</span>
        </button>
        
        <button class="action-button confirm-button" (click)="SalvaConsulta()">
            <mat-icon>save</mat-icon>
            <span>Salvar</span>
        </button>
        
        <button class="action-button accent-button" *ngIf="!flgCarregamentoGeral" (click)="AbrirModalListaProcedimento()">
            <mat-icon>list</mat-icon>
            <span>Procedimentos</span>
        </button>
    </footer>
</div>