.modal-container {
    background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    border-radius: 12px;
    overflow: hidden;
    min-width: 500px;
    max-width: 700px;
    max-height: 80vh;
}

.modal-header {
    background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #b2dfb2;

    .modal-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        color: #2e7d32;
        font-size: 1.4rem;
        font-weight: 500;

        .title-icon {
            color: #4caf50;
        }
    }

    .close-button {
        color: #2e7d32;

        &:hover {
            background-color: rgba(46, 125, 50, 0.1);
        }
    }
}

.modal-content {
    padding: 24px;
    background: #f9fcf9;
    max-height: 400px;
    overflow-y: auto;

    // Scrollbar personalizada
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #e8f5e8;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #a5d6a7;
        border-radius: 3px;

        &:hover {
            background: #81c784;
        }
    }
}

.consulta-card {
    margin-bottom: 16px;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f8f1 100%);
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 8px rgba(76, 175, 80, 0.15);
        transform: translateY(-1px);
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.consulta-content {
    padding: 16px !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.consulta-info {
    flex: 1;
}

.consulta-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .medico-nome {
        margin: 0;
        color: #2e7d32;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .consulta-tipo {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.85rem;
        font-weight: 500;

        &.presencial {
            background-color: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #a5d6a7;
        }

        &.online {
            background-color: #e3f2fd;
            color: #1565c0;
            border: 1px solid #90caf9;
        }

        mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
        }
    }
}

.consulta-details {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .data-consulta,
    .id-consulta {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #4a5a4a;
        font-size: 0.9rem;

        .detail-icon {
            font-size: 18px;
            width: 18px;
            height: 18px;
            color: #66bb6a;
        }
    }
}

.consulta-actions {
    display: flex;
    flex-direction: column;
    align-items: center;

    .acesso-button {
        background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            background: linear-gradient(135deg, #43a047 0%, #5cb85c 100%);
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }

        mat-icon {
            margin-right: 4px;
        }
    }
}

.no-consultas {
    text-align: center;
    padding: 40px 20px;
    color: #6a8a6a;

    .no-consultas-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #a5d6a7;
        margin-bottom: 16px;
    }

    p {
        margin: 0;
        font-size: 1.1rem;
    }
}

.modal-actions {
    background: #f1f8f1;
    padding: 16px 24px;
    border-top: 1px solid #c8e6c9;
    justify-content: flex-end;

    button {
        color: #2e7d32;
        font-weight: 500;

        &:hover {
            background-color: rgba(46, 125, 50, 0.1);
        }
    }
}

// Responsividade
@media (max-width: 600px) {
    .modal-container {
        min-width: 90%;
    }

    .consulta-content {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .consulta-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .consulta-actions {
        align-items: stretch;

        .acesso-button {
            width: 100%;
        }
    }
}
