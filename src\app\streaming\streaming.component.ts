import { AlertComponent } from './../alert/alert.component';
import {
  Component,
  ViewChild,
} from "@angular/core";
import { NgxSmartModalService,  NgxSmartModalModule } from "ngx-smart-modal";
import {
  MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition,
  MatSnackBarVerticalPosition as MatSnackBarVerticalPosition,
} from "@angular/material/snack-bar";
import { ConsultaService } from "../service/consulta.service";
import { Avaliacao } from "../model/avaliacao";
import {
  ChatConsulta,
  QuesitoAvaliacao,
} from "../model/consulta";
import { CommonModule, Time } from "@angular/common";
import { GeradorPerguntasService } from "../service/geradorPerguntas.service";
import { AnexosArquivos } from "../model/anexosConsulta";
import { environment } from "src/environments/environment";
import "../../vendor/jitsi/external_api.js";
import { AppComponent } from "../app.component";
import { EnumTipoUsuario } from "../Util/tipoUsuario";
import { PacienteService } from "../service/pacientes.service";
import { DadosMedicosUsuario } from "../model/cliente";
import { TelemedicinaComponentBase } from "../Util/component.base";
import { TranslateModule, TranslateService } from "@ngx-translate/core";
import { saveAs } from "file-saver";
import { DocumentosService } from "../service/documentos.service";
import { Receita, Declaracao, Atestado } from "../model/documentos";
import { FormControl, FormsModule,
ReactiveFormsModule, Validators } from "@angular/forms";
import { CdkTextareaAutosize } from "@angular/cdk/text-field";
import { NgZone } from "@angular/core";
import { take } from "rxjs/operators";
import {
  state,
  style,
  transition,
  animate,
  trigger,
} from "@angular/animations";
import { UsuarioLogadoService } from "../auth/usuarioLogado.service";
import { SafeResourceUrl, DomSanitizer } from "@angular/platform-browser";
import { documentosModal } from "../model/DocumentosModal";
import { EnumTipoDocumentos } from "../Util/tipoDocumentos";
import { documentosModalService } from "../documentacao/documentocao.service";
import { PesquisaCidService } from "../pesquisa-cid/pesquisa-cid.service";
import { CidService } from "../service/cid.service";
import { LocalStorageService } from "../service/LocalStorageService";
import { SignalHubService } from "../service/signalHub.service";
import { SpinnerService } from "../service/spinner.service";
import { Router } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatListModule } from '@angular/material/list';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatTooltipModule } from '@angular/material/tooltip';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { TruncatePipe } from '../Util/pipes/truncate.pipe';

// declare var JitsiMeetExternalAPI: any;

@Component({
    selector: "app-streaming",
    templateUrl: "./streaming.component.html",
    styleUrls: ["./streaming.component.scss"],
    animations: [
        trigger("openClose", [
            state("open", style({
                height: "300px",
            })),
            state("closed", style({
                height: "36px",
            })),
            transition("open => closed", [animate("0.2s")]),
            transition("closed => open", [animate("0.2s")]),
        ]),
        // Fim transition
    ],
      standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      NgxSmartModalModule,
      MatIcon,
      TranslateModule,
      MatFormFieldModule,
      MatDivider,
      MatSelectModule,
      MatExpansionModule,
      MatProgressBarModule,
      MatListModule,
      MatSidenavModule,
      MatTooltipModule,
      PdfViewerModule,
      MatCardModule,
      MatTabsModule,
      TruncatePipe
    ]
})
export class StreamingComponent extends TelemedicinaComponentBase {
  constructor(
    private spinner: SpinnerService,
    private _ngZone: NgZone,
    private consultaService: ConsultaService,
    // private snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private perguntasService: GeradorPerguntasService,
    private appc: AppComponent,
    private router: Router,
    public ngxSmartModalService: NgxSmartModalService,
    private pacienteService: PacienteService,
    private tradutor: TranslateService,
    private documentosService: DocumentosService,
    private usuarioLogadoService: UsuarioLogadoService,
    public sanitizer: DomSanitizer,
    private documentosServiceModal: documentosModalService,
    private pesquisaCidService: PesquisaCidService,
    private cisService: CidService,
    private localStorageService: LocalStorageService,
    private signalHubService: SignalHubService
  ) {
    super();
    this.signalHubService.OnAlertAnexo.subscribe((res) => {
      if (res == this.IdConsulta) this.CarregarAnexos();
    });

    this.signalHubService.OnFinalizaConsulta.subscribe(() => {
      this.ngxSmartModalService.getModal("Avaliacao").open();
    });
    this.signalHubService.OnFinalizaOrientacao.subscribe(() => {
      this.FlgVideo = false;
      this.BtnSair();
    });

    // router.events.takeUntil(this.onDestroy$).subscribe((event: Event) => {
    //   if (event instanceof ChildActivationStart) {
    //     const router = event.snapshot;
    //     ;
    //     this.streamService.telastream$.emit(false);
    //   }
    // });

    this.cisService.changeCID$.subscribe((perf: any) => {
      if (perf != null) {
        this.desCID = perf;
        this.CarregaCIDCampo();
      }
    });
    this.screenWidthstream = window.innerWidth;
    if (this.screenWidthstream < 600) this.showFillerStream = true;
    else this.showFillerStream = false;
    // this.streamService.telastream$.emit(true);
  }

  urlSafe?: SafeResourceUrl;
  src = "";
  @ViewChild("autosize") autosize?: CdkTextareaAutosize;
  isOpen = false;
  consultasmobi: boolean = false;
  infomobi: boolean = false;
  atestadomobi: boolean = false;
  receituariomobi: boolean = false;
  declaracaomobi: boolean = false;
  anotacoesanommobi: boolean = false;
  anotacoesmobi: boolean = false;
  anexomobi: boolean = false;
  Modo1: boolean = true;
  Modo2: boolean = false;
  Modo3: boolean = false;
  AlgumaObservacao = "";
  Sairsala = false;

  CorpoCliente = false;
  Cronometro = false;
  // public connection : Hub
  success: boolean = false;
  FormularioVotacao:any;
  quesitosAvaliacao:any;
  DadosAnot: any;
  DadosPerguntas: any;
  DadosAnexo: any;
  width?: number;
  Chat?: string;
  // usuario: Usuario;
  seg?: number;
  min?: number;
  hr?: number;
  segundos: any = "00";
  minutos: any = "00";
  hora: any = "0";
  tempo?: Time;
  dadosChat: any;
  CampoConsultaPerguntas?: string;
  IdConsulta: any;
  Anonimo?: string;
  dadosConsulta: any = [];
  Arquivo: any;
  DadosanexConsulta: any = [];
  Arquivo64?: string;
  dadosHistorico: any = [];
  documentacao = false;
  Remedios = false;
  isHubConnected: boolean = false;
  primeiraConsulta = false;

  AdmPermissao = false;
  MedicoPermissao = false;
  AtendentePermissao = false;
  PacientePermissao = false;
  tipoUsuario: string = "";
  Dados: any = [];

  // domain: string = "jitsi.medicinaparavoce.com.br";
  domain: string = "meet.jit.si";
  options: any;
  api: any;
  legenda = false;
  spinnerAnexo = false;

  flgEndereco: boolean = false;
  showMessageError: boolean = false;
  CampoReceita: string = "";
  DadosRaceitaRemedios: any;
  checkmedicamentosPro: boolean = true;
  nomeRemedio?: string;

  CampoDeclaracao?: string;
  CampoReceituario?: string;
  CampoAtestado?: string;
  periudoDeclaracao?: string;

  filtraCID: any = [];
  ListaCid: any = [];
  IdCID?: number;
  desCID?: string;
  CID?: string;
  DesCID?: boolean;
  DiasAtestado?: number;
  spinnerHistorico: boolean = false;

  // Boolean dudes
  Consultas: boolean = false;
  Anotacoes: boolean = false;
  anexos: boolean = false;
  DadosP: boolean = false;
  Localizacao: boolean = false;
  Atestado: boolean = false;
  Receituario: boolean = false;
  Declaracao: boolean = false;
  closemenu: boolean = true;
  Historicomenu: boolean = false;
  InfoP: boolean = false;
  covid: boolean = false;

  DadosInformUsuario: any = [];

  anonimo: boolean = false;
  corpo: boolean = false;
  consulta: boolean = true;
  anexo: boolean = false;
  validation = true;
  idPagamento?: number;
  arquivoPdfDownload: any;
  usuario?: number;

  DesConsulta: string = "";
  DesAnonimo: string = "";
  anexosArquivos = new AnexosArquivos();
  panelOpenState = false;

  ngOnInit() {
    this.usuario = this.usuarioLogadoService.getIdUsuarioAcesso();
    let consulta = this.localStorageService.Consulta;
    if (consulta) {
      // this.FlgSomenteProntuario = consulta.flgSomenteProntuario;
      this.IdConsulta = consulta.idConsulta;
      this.idPagamento = consulta.idPagamento;
    }
    if (this.IdConsulta == undefined) {
      sessionStorage.setItem("TelaStream", "false");
      this.router.navigate(["/consulta"]);
      this.appc.ngOnInit();
    }
    // this.IdConsulta = 2985;

    if (this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.ADM) {
      this.tipoUsuario = "ADM Sistema";
      this.AdmPermissao = true;
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Atendente
    ) {
      this.tipoUsuario = "Atendente";
      this.AtendentePermissao = true;
      this.MedicoPermissao = true;
      this.PacientePermissao = true;
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Médico
    ) {
      this.tipoUsuario = "Médico";
      this.MedicoPermissao = true;
      this.CarregaCID();
    } else if (
      this.usuarioLogadoService.getIdTipoUsuario() == EnumTipoUsuario.Paciente
    ) {
      this.tipoUsuario = "Paciente";
      this.PacientePermissao = true;
    }
    if (this.IdConsulta != undefined && this.IdConsulta != "") {
      this.Consulta();
      this.CarregarAnexos();
    }
  }

  toggle() {
    this.isOpen = !this.isOpen;
  }
  entrouconsulta() {
    this.consultaService
      .PutOnlinenaConsulta(this.IdConsulta)
      .subscribe();
  }

  Infomobi() {
    if ((this.infomobi = true)) {
      this.anotacoesanommobi = false;
      this.anotacoesmobi = false;
      this.consultasmobi = false;
      this.infomobi = true;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    } else if ((this.infomobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    }
  }

  Atestadomobi() {
    if ((this.atestadomobi = true)) {
      this.anotacoesanommobi = false;
      this.anotacoesmobi = false;
      this.consultasmobi = false;
      this.infomobi = false;
      this.atestadomobi = true;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    } else if ((this.atestadomobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    }
  }

  Receituariomobi() {
    if ((this.receituariomobi = true)) {
      this.anotacoesanommobi = false;
      this.anotacoesmobi = false;
      this.consultasmobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = true;
      this.declaracaomobi = false;
    } else if ((this.receituariomobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    }
  }

  Declaracaomobi() {
    if ((this.declaracaomobi = true)) {
      this.anotacoesanommobi = false;
      this.anotacoesmobi = false;
      this.consultasmobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = true;
    } else if ((this.declaracaomobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    }
  }

  Consultasmobi() {
    if ((this.consultasmobi = true)) {
      this.anotacoesanommobi = false;
      this.anotacoesmobi = false;
      this.infomobi = false;
      this.consultasmobi = true;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    } else if ((this.consultasmobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
      this.anotacoesanommobi = false;
    }
  }

  Anotacoesanommobi() {
    if ((this.anotacoesanommobi = true)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anexomobi = false;
      this.infomobi = false;
      this.anotacoesanommobi = true;
    } else if ((this.anotacoesanommobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anexomobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
      this.anotacoesanommobi = false;
    }
  }
  Anexomobi() {
    if ((this.anexomobi = true)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anexomobi = true;
      this.anotacoesanommobi = false;
      this.infomobi = false;
    } else if ((this.anexomobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anexomobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    }
  }
  Anotacoesmobi() {
    if ((this.anotacoesmobi = true)) {
      this.consultasmobi = false;
      this.anotacoesmobi = true;
      this.anexomobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
    } else if ((this.anotacoesmobi = false)) {
      this.consultasmobi = false;
      this.anotacoesmobi = false;
      this.anexomobi = false;
      this.anotacoesanommobi = false;
      this.infomobi = false;
      this.atestadomobi = false;
      this.receituariomobi = false;
      this.declaracaomobi = false;
    }
  }

  recusarSair() {
    this.ngxSmartModalService.getModal("Inicializando").close();
    sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(["/"]);
    this.appc.ngOnInit();
  }
  Finalizar() {
    this.consultaService
      .FinalizarConsulta(
        this.IdConsulta,
        this.usuarioLogadoService.getIdUsuarioAcesso()
      )
      .subscribe((retorno) => {
        if (retorno != null) {
          // this.api.dispose();
          this.FlgVideo = false;
          this.Cronometro = false;
          this.ngxSmartModalService.getModal("FinalizarChamada").close();
          this.Sairsala = true;
        }
      });
  }

  BtnSair() {
    // sessionStorage.setItem("TelaStream", "false");
    this.router.navigate(["/"]);
    // this.streamService.telastream$.emit(false);
    // this.appc.ngOnInit();
  }
  FlgVideo: boolean = false;
  LinkVideo?: string;
  sala?: string;
  AbrirwebCan(): void {
    try {
      this.sala = "medicinaparavoce" + this.IdConsulta;
      this.FlgVideo = true;

      if (window.location.hostname == "camposverdes.medicinaparavoce.com.br")
        this.LinkVideo =
          `${environment.endPointTokboxCamposVerdes}&room=` +
          this.sala +
          "&iframe=true";
      else if (window.location.hostname == "atendimento.medicinaparavoce.com.br")
        this.LinkVideo =
          `${environment.endPointTokboxCovid}&room=` +
          this.sala +
          "&iframe=true";
      else
        this.LinkVideo =
          `${environment.endPointTokbox}&room=` + this.sala + "&iframe=true";
      this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(
        this.LinkVideo
      );

      // || this.usuarioLogadoService.getIdPessoa == this.dadosConsulta.
      if (
        this.usuarioLogadoService.getIdUsuarioAcesso() ==
        this.dadosConsulta.userPaciente
      ) {
        this.entrouconsulta();
      }
    } catch (error) {
      this.snackBarAlert.falhaSnackbar(error);
    }
  }

  // AbrirwebCan(): void {
  //   try {
  //     if (this.usuarioLogadoService.getIdUsuarioAcesso() != this.dadosConsulta.userPaciente) {
  //       this.options = {
  //         roomName: 'medicinaparavoce' + this.IdConsulta,
  //         interfaceConfigOverwrite: {
  //           SHOW_JITSI_WATERMARK: false, SHOW_WATERMARK_FOR_GUESTS: false, INVITATION_POWERED_BY: false,
  //           AUTHENTICATION_ENABLE: false, DISPLAY_WELCOME_PAGE_CONTENT: false, GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false, VIDEO_QUALITY_LABEL_DISABLED: true,
  //           MOBILE_APP_PROMO: false, TOOLBAR_ALWAYS_VISIBLE: false,
  //           settings: false, videoquality: false, fullscreen: false, info: false,
  //           TOOLBAR_BUTTONS: ['microphone', 'camera', 'fullscreen',
  //             'tileview', 'hangup', 'profile', 'chat',
  //             'recording', 'livestreaming',
  //             'videoquality', 'settings',
  //             'shortcuts',
  //             'help'],
  //         },
  //         parentNode: document.querySelector('#react')
  //       }
  //     }
  //     else if (this.usuarioLogadoService.getIdUsuarioAcesso() == this.dadosConsulta.userPaciente) {
  //       this.options = {
  //         roomName: 'medicinaparavoce' + this.IdConsulta,
  //         interfaceConfigOverwrite: {
  //           SHOW_JITSI_WATERMARK: false, SHOW_WATERMARK_FOR_GUESTS: false, INVITATION_POWERED_BY: false,
  //           AUTHENTICATION_ENABLE: false, DISPLAY_WELCOME_PAGE_CONTENT: false, GENERATE_ROOMNAMES_ON_WELCOME_PAGE: false, VIDEO_QUALITY_LABEL_DISABLED: false,
  //           MOBILE_APP_PROMO: false,
  //           TOOLBAR_BUTTONS: ['fullscreen', 'tileview','microphone', 'camera',]

  //         },
  //         parentNode: document.querySelector('#react')

  //       }
  //     }

  //     this.api = new JitsiMeetExternalAPI(this.domain, this.options);

  //     
  //     

  //     
  //     ;
  //     ;
  //   } catch (error) {
  //     
  //     ;
  //     this.AlgumErro(true, error)

  //   }
  // }

  AbreMedicamentos(teste:any) {
    if (teste.index == 1) this.Remedios = !this.Remedios;
    else this.Remedios = false;
  }

  CriarDocumentacao() {
    this.documentacao = !this.documentacao;
    this.historico = false;
    this.showPanel = false;
    this.Remedios = false;
  }

  AbreHistorico() {
    this.historico = !this.historico;
    this.showPanel = false;
    this.documentacao = false;
    this.Remedios = false;
  }

  AbrePadrao() {
    this.showPanel = !this.showPanel;
    this.historico = false;
    this.documentacao = false;
    this.Remedios = false;

    this.Atestado = false;
    this.Receituario = false;
    this.Declaracao = false;
    this.Anotacoes = false;
    this.Consultas = true;
    this.anexos = false;
    this.DadosP = false;
    this.Localizacao = false;
  }

  abrirModalBoasVindas(teste: documentosModal) {
    this.documentosServiceModal.setAbrirModal(teste);
  }

  AtestadoModal() {
    var documentos = new documentosModal();
    documentos.idConsulta = this.IdConsulta;
    documentos.TipoModal = EnumTipoDocumentos.Atestado;
    documentos.Caminho = "prontuario";
    documentos.idPaciente = this.dadosConsulta.idPaciente;
    documentos.idMedico = this.dadosConsulta.idMedico;
    // documentos.idMedico = this.
    this.abrirModalBoasVindas(documentos);
  }
  DeclaracaoModal() {
    var documentos = new documentosModal();
    documentos.TipoModal = EnumTipoDocumentos.Declaracao;
    documentos.idConsulta = this.IdConsulta;
    documentos.Caminho = "prontuario";
    documentos.idPaciente = this.dadosConsulta.idPaciente;
    documentos.idMedico = this.dadosConsulta.idMedico;
    this.abrirModalBoasVindas(documentos);
  }
  ReceituarioModal() {
    var documentos = new documentosModal();
    documentos.TipoModal = EnumTipoDocumentos.Receituario;
    documentos.idConsulta = this.IdConsulta;
    documentos.Caminho = "prontuario";
    documentos.idPaciente = this.dadosConsulta.idPaciente;
    documentos.idMedico = this.dadosConsulta.idMedico;
    this.abrirModalBoasVindas(documentos);
  }
  CarregaConsulta(id:any) {
    try {
      this.consultaService.GetConsulta(id).subscribe(
        (retorno) => {
          this.minutos = retorno[0].consulta.tempoConsulta.substring(3, 5);
          this.segundos = "00";


          if (retorno[0].tipoAgendamento == 'COVID') {
            this.covid = true;
          }





          this.dadosConsulta = retorno[0];
          if (this.dadosConsulta.consulta.flgPrimeiraConsulta == true)
            this.primeiraConsulta = true;

          if (
            this.dadosConsulta.idPessoaPaciente ==
            this.usuarioLogadoService.getIdPessoa()
          ) {
            this.tipoUsuario = "Paciente";
            this.PacientePermissao = true;
            this.AdmPermissao = false;
            this.AtendentePermissao = false;
            this.MedicoPermissao = false;
          }

          if (this.dadosConsulta.idPaciente) {
            this.CarregaPaciente(this.dadosConsulta.idPaciente);
            this.historicoPaciente(this.dadosConsulta.idPaciente);
          }
        },
        (err) => {
          ;
          this.snackBarAlert.falhaSnackbar(err);
        }
      );
    } catch (error) {
      ;
      this.snackBarAlert.falhaSnackbar(error);
      ;
    }
  }

  Consulta() {
    this.CarregaConsulta(this.IdConsulta);
    this.CarregarAnotacoes();
    this.CarregarAnexos();
    this.CarregarPerguntas();
    this.CarregaQuisitosAvaliacao();
    // document.getElementById("btnGerarTarefa").click();
  }

  ConsultaAndamento() {
    this.consultaService
      .ConsultaAndamento(this.IdConsulta)
      .subscribe(() => { });
  }

  Inicializacao() {
    this.ngxSmartModalService.getModal("Inicializando").close();

    this.Cronometro = true;
    this.CarregaChatConsulta(this.IdConsulta);
    this.AbrirwebCan();
    if (this.dadosConsulta.consulta.observacaoPaciente) {
      this.CampoConsultaPerguntas = this.dadosConsulta.consulta.observacaoPaciente;
    }
    if (this.dadosConsulta.consulta.anotacaoAnonima) {
      this.Anonimo = this.dadosConsulta.consulta.anotacaoAnonima;
    }

    this.TempoCons();
    this.DadosUsuario();
    this.ConsultaAndamento();
    setInterval(() => {
      if (this.Cronometro == true) {
        this.TempoCons();
      }
    }, 1000);

  }

  TempoCons() {
    var zero = 0;
    this.seg;
    this.min;
    this.hr;

    if (parseInt(this.segundos) > 0) {
      this.segundos = parseInt(this.segundos) - 1;
      this.segundos = zero + this.segundos;
    } else if (this.segundos == 0) {
      if (this.minutos > 0) {
        this.segundos = 59;
        this.minutos = this.minutos - 1;
        if (this.minutos < 10) this.minutos = parseInt("0") + this.minutos;
      } else if (this.minutos == 0) {
        if (this.hora > 0) {
          this.segundos = 59;
          this.minutos = 59;
          this.hora = this.hora - 1;
          if (this.hora < 10) this.hora = parseInt("0") + this.minutos;
        }
      }
    }
  }
  color = "primary";
  mode = "indeterminate";
  value = 0;
  bufferValue = 0;
  showAba: boolean = false;
  historico: boolean = false;
  showFillerStream: boolean = false;
  showPanel: boolean = false;
  screenWidthstream: number;

  CarregarPerguntas() {
    try {
      this.perguntasService.getPergunta().subscribe(
        (retorno) => {
          ;
          this.DadosPerguntas = retorno;
          this.spinner.hide();
        },
        () => {
          this.snackBarAlert.falhaSnackbar("Erro ao carregar as perguntas");
          this.spinner.hide();
        }
      );
    } catch (error) {
      ;
      this.snackBarAlert.falhaSnackbar(error);
      this.spinner.hide();
    }
  }

  lifeline(id:any) {
    this.consultaService.GetLifeLine("Paciente", id).subscribe(
      () => {
        ;
        this.ngxSmartModalService.getModal("History").open();
      }
    );
  }

  CarregarAnotacoes() {
    (this.DadosAnot = []),
      this.DadosAnot.push({ Title: "Anotação-21Dez", Data: "21/12/2019" }),
      this.DadosAnot.push({ Title: "Anotação-25Dez", Data: "25/12/2019" }),
      this.DadosAnot.push({ Title: "Anotação-20Jan", Data: "20/01/2019" }),
      this.DadosAnot.push({ Title: "Anotação-29Jan", Data: "29/01/2019" });
  }

  checkPerguntas(i:any) {
    this.bufferValue = (100 / this.DadosPerguntas.length) * i;
  }
  CampoConsulta() {
    if (
      this.CampoConsultaPerguntas != "" &&
      this.CampoConsultaPerguntas != undefined
    ) {
      this.consultaService
        .SalvarObservacaoConsulta(
          this.CampoConsultaPerguntas,
          this.IdConsulta,
          this.usuarioLogadoService.getIdUsuarioAcesso()
        )
        .subscribe(() => {
          this.snackBarAlert.sucessoSnackbar("Anotação salva com sucesso!");
        });
    }
  }
  CampoAnonimo() {
    if (this.Anonimo != "" && this.Anonimo != undefined) {
      this.consultaService
        .SalvarAnotacaoAnonima(
          this.Anonimo,
          this.IdConsulta,
          this.usuarioLogadoService.getIdUsuarioAcesso()
        )
        .subscribe(() => {
          this.snackBarAlert.sucessoSnackbar("Anotação anônima salva com sucesso!");
        });
    }
  }

  PermissaoAnexoVisu(value:any) {
    this.spinnerAnexo = true;
    this.anexosArquivos.FlgVisualizacaoUsuario = value;
    this.consultaService
      .UploadArquivo(this.anexosArquivos, this.chaveArquivo)
      .subscribe(
        () => {
          this.CarregarAnexos();
        },
        () => {
          this.spinnerAnexo = false;
        }
      );
    this.ngxSmartModalService.getModal("PermissaoAnexoVisualizacao").close();
  }

  chaveArquivo:any;
  SubirArquivoConsulta(chave:any) {
    this.chaveArquivo = null;
    this.anexosArquivos = new AnexosArquivos();
    this.spinnerAnexo = true;
    this.anexosArquivos.IdConsulta = this.IdConsulta;
    this.anexosArquivos.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    this.chaveArquivo = chave;

    if (this.tipoUsuario == "Paciente") {
      this.anexosArquivos.FlgVisualizacaoUsuario = true;
      this.consultaService.UploadArquivo(this.anexosArquivos, chave).subscribe(
        () => {
          this.CarregarAnexos();
        },
        () => {
          this.spinnerAnexo = false;
        }
      );
    } else {
      this.spinnerAnexo = false;
      this.ngxSmartModalService.getModal("PermissaoAnexoVisualizacao").open();
    }
  }

  CarregarAnexos() {
    try {
      this.spinnerAnexo = true;

      this.consultaService.AnexosConsulta(this.IdConsulta).subscribe(
        (retorno) => {
          this.DadosanexConsulta = retorno;
          this.spinnerAnexo = false;
          ;
          ;
        },
        (err) => {
          this.snackBarAlert.falhaSnackbar(err);
          ;
          this.spinnerAnexo = false;
        }
      );
    } catch (error) {
      this.snackBarAlert.falhaSnackbar(error);
      ;
    }
  }

  ChatConversa() {
    if (this.Chat != "") {
      var chat = new ChatConsulta();
      chat.idConsulta = this.IdConsulta;
      chat.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
      chat.FlgInativo = false;
      chat.DtaCadastro = new Date();
      chat.DesChat = this.Chat;
      this.consultaService.SalvaChat(chat).subscribe(
        (retorno) => {
          if (retorno == null) {
            this.Chat = "";
            this.CarregaChatConsulta(this.IdConsulta);
          }
        }
      );
    }
  }
  CarregaChatConsulta(id:any) {
    this.consultaService.GetchatConsulta(id).subscribe(
      (retorno) => {
        ;
        this.spinner.hide();
        this.dadosChat = retorno;
      }, () => {this.spinner.hide()}
    );
  }

  CarregaQuisitosAvaliacao() {
    this.consultaService.getQuesitosAvaliacao().then(
      (retorno) => {
        ;

        this.quesitosAvaliacao = retorno;
      }
    );
  }
  EnviarAvaliacao(obj:any) {
    var form =
      obj == "Mobile"
        ? document.getElementById("avaliacaoMobile")
        : document.getElementById("avaliacao");
    var qtdAtendimento = 0;
    var qtdTempoConsulta = 0;
    var qtdValorConsulta = 0;
    var qtdUsabiliddeSistema = 0;

    this.FormularioVotacao = [];

    const formElement = form as HTMLFormElement;
    for (let i = 0; i < formElement.elements.length; i++) {
      const inputElement = formElement.elements[i] as HTMLInputElement;
      // const Campo = inputElement.name;
      const ValorCampo = inputElement.checked;
      const IdCampo = inputElement.value;

      if (Number(IdCampo) == 1) {
        if (ValorCampo == true) {
          qtdAtendimento = i;
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdAtendimento,
            nomeCampo: "Atendimento",
          });
        } else if (
          i == 5 &&
          qtdAtendimento == 0 &&
          ValorCampo == false &&
          Number(IdCampo) == 1
        ) {
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdAtendimento,
            nomeCampo: "Atendimento",
          });
        }
      }
      if (Number(IdCampo) == 2) {
        if (ValorCampo == true) {
          qtdTempoConsulta = i - 6;
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdTempoConsulta,
            nomeCampo: "TempoConsulta",
          });
        } else if (
          i == 11 &&
          qtdTempoConsulta == 0 &&
          ValorCampo == false &&
          Number(IdCampo) == 2
        ) {
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdTempoConsulta,
            nomeCampo: "TempoConsulta",
          });
        }
      }
      if (Number(IdCampo) == 3) {
        if (ValorCampo == true) {
          qtdValorConsulta = i - 12;
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdValorConsulta,
            nomeCampo: "ValorConsulta",
          });
        } else if (
          i == 17 &&
          qtdValorConsulta == 0 &&
          ValorCampo == false &&
          Number(IdCampo) == 3
        ) {
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdValorConsulta,
            nomeCampo: "ValorConsulta",
          });
        }
      }
      if (Number(IdCampo) == 4) {
        if (ValorCampo == true) {
          qtdUsabiliddeSistema = i - 18;
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdUsabiliddeSistema,
            nomeCampo: "UsabiliddeSistema",
          });
        } else if (
          i == 23 &&
          qtdUsabiliddeSistema == 0 &&
          ValorCampo == false &&
          Number(IdCampo) == 4
        ) {
          this.FormularioVotacao.push({
            Campo: IdCampo,
            valor: qtdUsabiliddeSistema,
            nomeCampo: "UsabiliddeSistema",
          });
        }
      }
    }

    var teste : any = [];

    if (this.FormularioVotacao.length == 0) {
    } else {
      this.FormularioVotacao.forEach((element:any) => {
        var quesito = new QuesitoAvaliacao();
        if (element.nomeCampo == "Atendimento") {
          quesito.IdQuesito = 1;
          quesito.valorAvaliacao = element.valor;
          quesito.FlgInativo = false;
          quesito.DtaCadastro = new Date();
        } else if (element.nomeCampo == "TempoConsulta") {
          quesito.IdQuesito = 2;
          quesito.valorAvaliacao = element.valor;
          quesito.FlgInativo = false;
          quesito.DtaCadastro = new Date();
        } else if (element.nomeCampo == "ValorConsulta") {
          quesito.IdQuesito = 3;
          quesito.valorAvaliacao = element.valor;
          quesito.FlgInativo = false;
          quesito.DtaCadastro = new Date();
        } else if (element.nomeCampo == "UsabiliddeSistema") {
          quesito.IdQuesito = 4;
          quesito.valorAvaliacao = element.valor;
          quesito.FlgInativo = false;
          quesito.DtaCadastro = new Date();
        }
        teste.push(quesito);
      });
    }

    var avaliacao = new Avaliacao();
    avaliacao.quesitosAvaliacao = teste;
    avaliacao.FlgInativo = false;
    avaliacao.IdConsulta = this.IdConsulta;
    avaliacao.IdCliente = this.dadosConsulta.idPaciente;
    avaliacao.Observacao = (document.getElementById("obsAmbiente") as HTMLInputElement)["value"];

    this.consultaService.SalvaAvaliacao(avaliacao).subscribe(
      () => {
        this.ngxSmartModalService.getModal("Avaliacao").close();
        this.snackBarAlert.sucessoSnackbar("avaliacao salva com sucesso!");
        sessionStorage.setItem("TelaStream", "false");
        this.router.navigate(["/"]);
        this.appc.ngOnInit();
      }
    );
  }
  public CarregaPaciente(id:any) {
    try {
      this.pacienteService.getPacienteEdit(id).subscribe(
        (retorno) => {
          ;
          ;
          if (retorno) {
            this.DadosInformUsuario.nome = retorno.paciente.pessoa.nomePessoa;
            this.DadosInformUsuario.cpf = retorno.paciente.pessoa.cpf;
            this.DadosInformUsuario.email = retorno.paciente.pessoa.email;
            this.DadosInformUsuario.celular =
              retorno.paciente.contato.telefoneMovel;
            this.DadosInformUsuario.tel = retorno.paciente.contato.telefone;
            this.DadosInformUsuario.telComer =
              retorno.paciente.contato.telefoneComercial;
            this.DadosInformUsuario.IdUsuario =
              retorno.paciente.usuario.idUsuario;
            this.DadosInformUsuario.hoje = new Date().toLocaleDateString();
            this.DadosInformUsuario.hora = new Date().toLocaleTimeString();

            var tipoUsu = retorno.paciente.usuario.idTipoUsuario;

            if (tipoUsu == EnumTipoUsuario.Atendente) {
              this.DadosInformUsuario.tipoUsuario = "Atendente";
            } else if (tipoUsu == EnumTipoUsuario.Médico) {
              this.DadosInformUsuario.tipoUsuario = "Médico";
            } else if (tipoUsu == EnumTipoUsuario.Paciente) {
              this.DadosInformUsuario.tipoUsuario = "Paciente";
            }
          }
          this.spinner.hide();
        },
        () => {
          this.tradutor
            .get("TELACADASTROPACIENTE.ERROAOCARREGARPACIENTE")
            .subscribe((res: string) => {
              ;
              this.snackBarAlert.falhaSnackbar(res);
            });
          this.spinner.hide();
        }
      );
    } catch (error) {
      this.tradutor
        .get("TELACADASTROPACIENTE.ERROAOCARREGARPACIENTE")
        .subscribe((res: string) => {
          ;
          this.snackBarAlert.falhaSnackbar(res);
        });
    }
  }
  name = "SnackbarContent";
  AnexoSucesso: string = "Avaliação feita com Sucesso!  ✔ ";
  CorpoSalvo: string = "Salvo com Sucesso!  ✔ ";
  AnexoErro: string = "O arquivo precisa ter no maximo 30mbs. ";
  actionButtonLabel: string = "Fechar";
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = "right";
  verticalPosition: MatSnackBarVerticalPosition = "bottom";

  // SucessAvaliacao(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["sucessoSnackbar"];
  //     var mensagem = "";
  //     this.tradutor
  //       .get("TELASTREAMING.SALVOCOMSUCESSO")
  //       .subscribe((res: string) => {
  //         ;
  //         mensagem = res;
  //       });
  //     this.tradutor.get("TELASTREAMING.FECHAR").subscribe((res: string) => {
  //       ;
  //       this.actionButtonLabel = res;
  //     });
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  //   this.success = value;
  // }

  fileToUpload?: FileList;
  arrayBuffer: any;

  DeleteAnexo(ChaveArquivo: any, idDocumento:any) {
    if (ChaveArquivo != "") {
      this.consultaService
        .RemoveArquivo(ChaveArquivo, idDocumento)
        .subscribe(() => {
          this.CarregarAnexos();
        });
    }
  }

  ConsultMenu2() {
    this.consulta = true;
    this.anonimo = false;
    this.anexo = false;
    this.corpo = false;
    this.historico = false;
  }

  AnotMenu2() {
    this.consulta = false;
    this.anonimo = true;
    this.anexo = false;
    this.corpo = false;
    this.historico = false;
  }

  AnexoMenu2() {
    if (this.anexo) this.anexo = false;
    else {
      this.anexo = true;
      this.corpo = false;
    }
  }

  DadosMenu2() {
    if (this.corpo) this.corpo = false;
    else {
      this.DadosUsuario();
      this.corpo = true;
      this.anexo = false;
    }
  }

  uploadFileToActivity() {
    this.SubirArquivoConsulta(this.fileToUpload);
    // this.fileUploadService.postFile(this.fileToUpload, this.IdConsulta);
    // this.fileToUpload = null;
    // swal('Enviado!', 'O arquivo foi enviado com sucesso. Aguarde a conclusão. Quando estiver pronto você poderá consultar em histórico de registro.', 'success');

    // this.fileUploadService.postFile(this.fileToUpload).then(data => {
    //   swal('Enviado!', 'O arquivo foi enviado com sucesso.', 'success');
    // }, error => {
    //   swal('Erro ao enviar!', 'Houve um problema ao enviar o arquivo.', 'error');
    //   ;
    // });
  }

  fileuploadesquerdo(event: DragEvent) {
    event.preventDefault();
    this.fileToUpload = event.dataTransfer?.files;
    this.uploadFileToActivity();
  }

  BaixarPdfModal() {
    this.arquivoPdfDownload.click();
    this.ngxSmartModalService.getModal("PDF").close();
    this.arquivoPdfDownload = "";
  }
  spinnerAnexoDownload: boolean = false;
  download(arq:any, nome:any, contentType:any, tipoArquivo:any) {
    if (!contentType) {
      contentType = "application/octet-stream";
    }
    this.arquivoPdfDownload = "";
    var a = document.createElement("a");
    var blob = new Blob([arq], { type: contentType });
    a.href = window.URL.createObjectURL(blob);

    this.src = a.href;
    a.download = nome;

    if (
      tipoArquivo == "pdf" ||
      nome.includes(".PDF") ||
      nome.includes(".pdf")
    ) {
      this.arquivoPdfDownload = a;
      this.ngxSmartModalService.getModal("PDF").open();
      this.spinnerAnexoDownload = false;
    } else {
      a.click();
      this.spinnerAnexoDownload = false;
    }
  }
  BaixarArquivo(chave:any, nome:any, tipoArquivo:any) {
    this.spinnerAnexoDownload = true;
    this.consultaService.CarregaCaminhoArquivo(String(chave), nome).subscribe(
      (response) => {
        var application;

        this.download(response, nome, application, tipoArquivo);
        this.spinner.hide();
      },
      () => {
        this.spinnerAnexoDownload = false;
        this.spinner.hide();
      }
    );
  }

  addParagrafo() {
    (document.getElementById("txtConsulta")as HTMLInputElement)["value"]  =
      (document.getElementById("txtConsulta")as HTMLInputElement)["value"]  + "" + "\n";
    (document.getElementById("txtConsulta")as HTMLInputElement)["value"]  = 
          (document.getElementById("txtConsulta") as HTMLInputElement)["value"].replace(/\\n/g, "<br>");
  }

  addParagrafoAnonimo() {
    (document.getElementById("txtAnonimo")as HTMLInputElement)["value"]  =
      (document.getElementById("txtAnonimo")as HTMLInputElement)["value"]  + "" + "\n";
    (document.getElementById("txtAnonimo")as HTMLInputElement)["value"]  = 
        (document.getElementById("txtAnonimo")as HTMLInputElement)["value"].replace(/\\n/g, "<br>");
  }

  BaixarArquivoMobile(chave:any, nome:any) {
    this.consultaService.CarregaCaminhoArquivo(String(chave), nome).subscribe(
      (response) => {
        var application;
        if (!application) {
          application = "application/octet-stream";
        }
        this.arquivoPdfDownload = "";
        var a = document.createElement("a");
        var blob = new Blob([response], { type: application });
        a.href = window.URL.createObjectURL(blob);
        this.src = a.href;
        a.download = nome;

        a.click();
        this.spinner.hide();
      },
      () => {
        this.spinnerAnexoDownload = false;
        this.spinner.hide();
      }
    );
  }

  historicoPaciente(id:any) {
    try {
      this.spinnerHistorico = true;
      this.consultaService.GetDadoshistoricoPaciente(id).subscribe(
        (retorno) => {
          ;


          retorno.forEach((element:any) => {
            if (element.receita)
              // element.receita = element.receita.replace("<br>", "/n");
              element.receita = element.receita.split("<br>").join("\n");
          });

          var retur = ([] = retorno);
          // var inicio = 1;

          for (var i = 0, len = retur.length; i < len; ++i) {
            retur[i].posicaoRef = "#" + 1000 + i;
            retur[i].posicao = 1000 + i;
          }

          //
          //this.dadosHistorico = retorno;
          this.dadosHistorico = retur;
          this.spinnerHistorico = false;
        },
        (err) => {
          ;
          this.snackBarAlert.falhaSnackbar(err);
        }
      );
    } catch (error) {
      ;
      this.snackBarAlert.falhaSnackbar(error);
    }
  }

  DadosUsuario() {
    if (this.CorpoCliente == false) {
      if (this.dadosConsulta.idPaciente) {
        this.pacienteService
          .getDadosPaciente(
            this.dadosConsulta.idPaciente,
            this.dadosConsulta.consulta.idConsulta
          )
          .subscribe(
            (retorno) => {
              ;
              ;
              if (retorno != null) {
                this.Dados.altura = this.verificaCasaDecimal(retorno.altura);
                this.Dados.peso = this.verificaCasaDecimal(retorno.peso);

                this.Dados.batimento = retorno.batimento;
                this.Dados.IMC = retorno.imc;
                this.Dados.pressao = retorno.pressao;
                this.Dados.temperatura = retorno.temperatura;
                this.Dados.idDadosMedicosUsuario =
                  retorno.idDadosMedicosUsuario;
              }
              this.CorpoCliente = true;
              this.spinner.hide();
            },
            () => {
              this.spinner.hide();
            }
          );
      }
    } else this.CorpoCliente = false;
  }

  LimparDados() {
    this.Dados = [];
  }

  SalvarDadosCorpo(campoAltura:any, campoPeso:any) {
    ;
    var dados = new DadosMedicosUsuario();
    dados.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    dados.IdConsulta = this.IdConsulta;
    dados.DtaCadastro = new Date();
    dados.FlgInativo = false;

    dados.Altura = (document.getElementById(campoAltura)as HTMLInputElement)["value"];
    dados.Peso = (document.getElementById(campoPeso)as HTMLInputElement)["value"];

    dados.Batimento = this.Dados.batimento;
    dados.IMC = this.Dados.IMC;
    dados.Pressao = (document.getElementById("campoPressao")as HTMLInputElement)["value"];
    dados.Temperatura = (document.getElementById("campoTemp")as HTMLInputElement)["value"];

    dados.IdUsuario = this.dadosConsulta.userPaciente;
    // dados.IdUsuario = this.DadosInformUsuario.IdUsuario;

    ;
    this.pacienteService.salvarDadosCorpo(dados).subscribe(
      () => {
        // let config = new MatSnackBarConfig();
        // config.verticalPosition = this.verticalPosition;
        // config.horizontalPosition = this.horizontalPosition;
        // config.duration = this.setAutoHide ? this.autoHide : 0;
        // config.panelClass = ["sucessoSnackbar"];

        var mensagem = "";
        this.tradutor
          .get("TELASTREAMING.SALVOCOMSUCESSO")
          .subscribe((res: string) => {
            ;
            mensagem = res;
          });
        this.tradutor.get("TELASTREAMING.FECHAR").subscribe((res: string) => {
          ;
          this.actionButtonLabel = res;
        });
        this.snackBarAlert.sucessoSnackbar(mensagem);

        this.spinner.hide();
      },
      () => {
        this.spinner.hide();
      }

    );
    this.spinner.hide();
  }

  verificaCasaDecimal(valor:any) {
    if (valor.toString().split(".").length < 2) {
      valor = valor + ".00";
    }
    if (valor.toString().split(".")[1].length == 1) {
      valor = valor + "0";
    }
    return valor;
  }

  CalculoIMC(idAltura:any, idPeso:any) {
    if (this.Dados.altura && this.Dados.peso) {
      var valorAltura = (document.getElementById(idAltura)as HTMLInputElement)["value"];
      var valorPeso = (document.getElementById(idPeso)as HTMLInputElement)["value"];
      var altura = Number(valorAltura) * Number(valorAltura);
      var calc = Number(valorPeso) / altura;
      this.Dados.IMC = calc.toFixed(2);
    } else this.Dados.IMC = null;
  }

  public mascaraPeso(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    var v = v,
      integer = v.split(".")[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = "0.0" + v;
      if (v.length === 2) v = "0." + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }

    (<HTMLInputElement>evento.target).value = v;
  }
  public mascaraAltura(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/^[0]+/, "");
    v = v.toString().replace(/\D/g, "");
    if (v.length >= 3) {
      v = v.toString().replace(/(\d{1})(\d{2})$/, "$1.$2");
    } else {
      v = v.toString().replace(/(\d{1})(\d{1})$/, "$1.$2");
    }
    (<HTMLInputElement>evento.target).value = v;
  }
  public mascaraTemperatura(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/^[0]+/, "");
    v = v.toString().replace(/\D/g, "");
    if (v.length == 3) {
      v = v.toString().replace(/(\d{1})(\d{1})$/, "$1.$2");
    } else if (v.length > 3) {
      v = v.toString().replace(/(\d{1})(\d{2})$/, "$1.$2");
    }
    (<HTMLInputElement>evento.target).value = v;
  }

  GerarReceita(obj:any) {
    if (this.CampoReceita == null || this.CampoReceita == undefined) {
      this.tradutor
        .get("TELADOCUMENTACAO.NAOHAMEDICAMENTOSNARECEITA")
        .subscribe((res: string) => {
          ;
          this.snackBarAlert.sucessoSnackbar(res);
        });
      this.showMessageError = true;
    }

    if (this.showMessageError == true) return;
    var receita = new Receita();
    receita.flgEndereco = this.flgEndereco;
    receita.flgInativo = false;
    receita.IdMedico = this.dadosConsulta.idMedico;
    receita.IdPaciente = this.dadosConsulta.idPaciente;
    receita.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    receita.DesReceita = this.CampoReceituario;
    receita.IdConsulta = this.IdConsulta;
    this.documentosService.GerarReceita(receita).subscribe(
      (retorno) => {
        if (retorno != null) {
          var mediaType = "application/pdf";
          var blob = new Blob([retorno], { type: mediaType });

          var receita = "";
          this.tradutor
            .get("TELASTREAMING.RECEITA")
            .subscribe((res: string) => {
              ;
              receita = res;
            });

          var filename = receita + this.dadosConsulta.paciente + ".pdf";
          saveAs(blob, filename);

          if (obj == "Enviar") {
          }
        }
      }
    );
  }

  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];
  //     this.tradutor.get("TELASTREAMING.FECHAR").subscribe((res: string) => {
  //       ;
  //       this.actionButtonLabel = res;
  //     });
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }

  // AlgumAcerto(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ["error-snack"];
  //     this.tradutor.get("TELASTREAMING.FECHAR").subscribe((res: string) => {
  //       ;
  //       this.actionButtonLabel = res;
  //     });
  //     this.snackBar.open(
  //       mensagem,
  //       this.action ? this.actionButtonLabel : undefined,
  //       config
  //     );
  //   }
  // }
  filtroRemedio() {
    try {
      this.DadosRaceitaRemedios = [];
      if (this.checkmedicamentosPro) {
        this.documentosService
          .GetMedicamentosClinica(
            this.nomeRemedio,
            this.usuarioLogadoService.getIdUltimaClinica()
          )
          .subscribe(
            (retornaClinicas: any = []) => {
              if (retornaClinicas.length > 0) {
                retornaClinicas.forEach((element:any) => {
                  this.DadosRaceitaRemedios.push({
                    produto: element.medicamento.medicamentosProgramados,
                    apresentacao: element.medicamento.programacao,
                  });
                });
              }
              ;
            }
          );
      } else {
        this.documentosService.GetListaMedicamentos(this.nomeRemedio).subscribe(
          (retorno) => {
            this.DadosRaceitaRemedios = retorno;
            ;
            this.spinner.hide();
          },
          () => {
            this.spinner.hide();
          }
        );
      }
    } catch (error) {
      ;
      this.spinner.hide();
    }
  }
  LimparDeclaracao() {
    this.periudoDeclaracao = "";
  }
  LimparReceita() {
    this.CampoReceituario = "";
    this.CampoReceita = "";
    this.nomeRemedio = "";
    this.DadosRaceitaRemedios = [];
  }

  addMedicamentos(item:any, item2:any) {
    if (this.checkmedicamentosPro) {
      this.CampoReceita = this.CampoReceita + "" + "\n" + item;
      this.CampoReceita = this.CampoReceita + "" + "\n" + item2 + "\n";
      this.CampoReceita = this.CampoReceita.replace(/\\n/g, "<br>");
      this.CampoReceituario = this.CampoReceituario + "" + "<br>" + item;
      this.CampoReceituario =
        this.CampoReceituario + "" + "<br>" + item2 + "<br>";
    } else {
      this.CampoReceita = this.CampoReceita + "" + "\n" + item;
      this.CampoReceita = this.CampoReceita + "" + "\n" + item2 + "\n";
      this.CampoReceita = this.CampoReceita.replace(/\\n/g, "<br>");
      this.CampoReceituario = this.CampoReceituario + "" + "<br>" + item;
      this.CampoReceituario =
        this.CampoReceituario + "" + "<br>" + item2 + "<br>";
    }
  }

  cid = new FormControl("", [Validators.required, Validators.maxLength(11)]);
  periudo = new FormControl("", [
    Validators.required,
    Validators.maxLength(11),
  ]);
  getErrorMessageperiudo() {
    return this.periudo.hasError("required")
      ? "TELADOCUMENTACAO.TELACAMPO"
      : this.periudo.hasError("Períudo")
        ? "TELADOCUMENTACAO.TELANAOEVALIDA"
        : "";
  }
  getErrorMessagecid() {
    return this.cid.hasError("required")
      ? "TELADOCUMENTACAO.TELACAMPO"
      : this.cid.hasError("Procedência")
        ? "TELADOCUMENTACAO.TELANAOEVALIDA"
        : "";
  }

  GerarDeclaracao() {
    if (this.periudoDeclaracao == "" || this.periudoDeclaracao == undefined)
      return;

    var declaracao = new Declaracao();
    declaracao.flgInativo = false;
    declaracao.IdMedico = this.dadosConsulta.idMedico;
    declaracao.IdPaciente = this.dadosConsulta.idPaciente;
    declaracao.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    declaracao.Periodo = this.periudoDeclaracao;
    declaracao.IdConsulta = this.IdConsulta;
    this.documentosService.GerarDeclaracao(declaracao).subscribe(
      (retorno) => {
        if (retorno != null) {
          var mediaType = "application/pdf";
          var blob = new Blob([retorno], { type: mediaType });
          var filename = "Declaracao" + this.dadosConsulta.paciente + ".pdf";
          saveAs(blob, filename);
        }
        this.spinner.hide();
        // this.showMessageSuccess = true;
      },() => {
        this.spinner.hide();
      }
    );
  }
  async CarregaCID() {
    this.cisService.GetListaFiltaPorCod(0, 100, this.desCID).subscribe(
      (retorno) => {
        this.ListaCid = retorno;
        this.filtraCID = this.ListaCid.filter(
          (c:any) => c.codCid == this.desCID?.toUpperCase()
        );
        if (this.filtraCID.length > 0) {
          this.CID = this.filtraCID[0].desCid;
        } else {
          this.tradutor
            .get("TELADOCUMENTACAO.CODNAOCORRESPONDEADOENCA")
            .subscribe((res: string) => {
              ;
              this.CID = res;
            });
        }
        ;
        this.spinner.hide();
      },() => {
        this.spinner.hide();
      }
    );
  }

  CarregaCIDCampo() {
    if (this.desCID != "" && this.desCID != null) {
      this.filtraCID = [];
      this.CarregaCID();
    }
  }
  LimparAtestado() {
    this.DiasAtestado = undefined;
    this.desCID = undefined;
    // this.cid = null;
    this.CID = undefined;
  }

  GerarAtestado() {
    // this.cid.markAsTouched();
    if (this.DiasAtestado == null || this.DiasAtestado == undefined) {
      this.tradutor.get("TELADOCUMENTACAO.PREENCHERDIAS").subscribe(
        (res: string) => {
          ;
          this.snackBarAlert.sucessoSnackbar(res);
        }
      );
      this.showMessageError = true;
    }
    // if (this.desCID != null && this.desCID != '' && this.filtraCID.length == 0) {
    //   this.tradutor.get('TELADOCUMENTACAO.CIDINVALIDO').subscribe((res: string) => {
    //     ;
    //     this.AlgumErro(true, res);
    //   });
    //   this.showMessageError = true
    // }
    if (this.showMessageError == true) return;
    var atestado = new Atestado();
    atestado.IdCid = this.desCID ? this.filtraCID[0].idCid : null;
    atestado.FlgDesCid = this.DesCID;
    atestado.flgInativo = false;
    atestado.IdMedico = this.dadosConsulta.idMedico;
    atestado.IdPaciente = this.dadosConsulta.idPaciente;
    atestado.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();
    atestado.QuantDias = this.DiasAtestado;
    atestado.IdConsulta = this.IdConsulta;
    this.documentosService.GerarAtestado(atestado).subscribe(
      (retorno) => {
        if (retorno != null) {
          var mediaType = "application/pdf";
          var blob = new Blob([retorno], { type: mediaType });
          var filename = "Atestado.pdf";
          saveAs(blob, filename);
        }
        // this.showMessageSuccess = true;
      }
    );
  }

  AbrirModalPesquisaCid() {
    this.pesquisaCidService.setModalPesquisaCid();
  }

  mascaraPressao(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "");
    if (v.length < 3) {
      v = v.replace(/(\d{1})(\d{1})$/, "$1/$2");
    } else if (v.length < 4) {
      v = v.replace(/(\d{2})(\d{1})$/, "$1/$2");
    } else {
      v = v.replace(/(\d{1})(\d{2})$/, "$1/$2");
    }
    (<HTMLInputElement>evento.target).value = v;
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v;
  }

  async MudarModo(obj:any) {
    if (obj == "Modo1") {
      this.Modo1 = true;
      this.Modo2 = false;
    } else {
      this.Modo1 = false;
      this.Modo2 = true;
    }
  }

  async MudarModoExecucao(obj:any) {
    if (obj == "Modo1") {
      if (!this.Modo1) {
        await this.MudarModo(obj);

        // this.api = new JitsiMeetExternalAPI(this.domain, this.options);
      }
    } else {
      if (!this.Modo2) {
        await this.MudarModo(obj);

        // this.api = new JitsiMeetExternalAPI(this.domain, this.options);
      }
    }
  }

  triggerResize() {
    // Wait for changes to be applied, then trigger textarea resize.
    this._ngZone.onStable
      .pipe(take(1))
      .subscribe(() => this.autosize?.resizeToFitContent(true));
  }

  LocalizacaoMenu() {
    if (this.Localizacao == false) {
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Localizacao = true;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.DadosP == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  DadosMenu() {
    if (this.DadosP == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = true;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.DadosP == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  InfoMenu() {
    if (this.InfoP == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = true;
    } else if (this.InfoP == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.InfoP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
    }
  }

  AnexoMenu() {
    if (this.anexos == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = true;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.anexos == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  ConsultMenu() {
    if (this.Consultas == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = true;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.Consultas == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  AnotMenu() {
    if (this.Anotacoes == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = true;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Atestado = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.Anotacoes == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  AtestadoMenu() {
    if (this.Atestado == false) {
      this.Atestado = true;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.Atestado == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  ReceituarioMenu() {
    if (this.Receituario == false) {
      this.Atestado = false;
      this.Receituario = true;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.Receituario == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  DeclaracaoMenu() {
    if (this.Declaracao == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = true;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    } else if (this.Declaracao == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  closem() {
    if (this.closemenu == true) {
      this.closemenu = false;
    } else if (this.closemenu == false) {
      this.closemenu = true;
    }
  }

  historicoMenu() {
    if (this.Historicomenu == false) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = true;
      this.InfoP = false;
    } else if (this.Historicomenu == true) {
      this.Atestado = false;
      this.Receituario = false;
      this.Declaracao = false;
      this.Anotacoes = false;
      this.Consultas = false;
      this.anexos = false;
      this.DadosP = false;
      this.Localizacao = false;
      this.Historicomenu = false;
      this.InfoP = false;
    }
  }

  informacao() {
    this.ngxSmartModalService.getModal("InforUsuario").open();
  }

}
