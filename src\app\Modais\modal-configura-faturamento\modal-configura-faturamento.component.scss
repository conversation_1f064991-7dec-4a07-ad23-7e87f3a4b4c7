/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Contêiner do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    margin: 0 auto;
    overflow: hidden;
    .modal-container{
        padding: 20px;
    }
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo do modal */


.content-columns {
    display: flex;
    gap: 20px;
}

.form-column {
    flex: 1;
    padding-right: 20px;
}

.list-column {
    flex: 1;
    border-left: 1px solid $border-color;
    padding-left: 20px;
}

/* Formulário */
.form-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-field {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
}

label {
    font-size: 14px;
    color: $text-secondary;
    margin-bottom: 6px;
}

.custom-select,
.custom-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid $border-color;
    border-radius: $border-radius;
    background-color: $secondary-light;
    color: $text-primary;
    font-size: 14px;
    transition: border-color $transition ease;
}

.custom-select:focus,
.custom-input:focus {
    outline: none;
    border-color: $primary-color;
}

.error-message {
    color: $error-color;
    font-size: 12px;
    margin-top: 4px;
}

/* Lista de consultas */
.list-container {
    max-height: 60vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.empty-state {
    text-align: center;
    color: $text-secondary;
    padding: 20px;
}

.list-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    background-color: $secondary-light;
    cursor: pointer;
    transition: all $transition ease;
}

.list-item:hover {
    background-color: $secondary-color;
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

.checkbox-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 8px;
}

.checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: $primary-color;
}

.item-details {
    flex: 1;
}

.detail-row {
    display: flex;
    margin-bottom: 4px;
}

.detail-label {
    color: $text-secondary;
    margin-right: 6px;
}

.detail-value {
    color: $text-primary;
    font-weight: 500;
}

/* Rodapé do modal */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px;
    background-color: $secondary-color;
    gap: 12px;
}

/* Botões */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 110px;
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: darken($secondary-dark, 5%);
}

.confirm-button {
    background-color: $primary-color;
    color: white;
}

.confirm-button:hover {
    background-color: $primary-dark;
}

.accent-button {
    background-color: $accent-color;
    color: white;
}

.accent-button:hover {
    background-color: darken($accent-color, 10%);
}

/* Responsividade */
@media (max-width: 768px) {
    .content-columns {
        flex-direction: column;
    }
    
    .form-column {
        padding-right: 0;
    }
    
    .list-column {
        border-left: none;
        border-top: 1px solid $border-color;
        padding-left: 0;
        padding-top: 20px;
        margin-top: 20px;
    }
    
    .action-button {
        min-width: 0;
        padding: 8px;
    }
    
    .action-button span {
        display: none;
    }
    
    .modal-footer {
        justify-content: center;
    }
}