/* Old css  */
.full-height{
    height: 150vh;
    width: 100vw;
  }
  
  /* ul li:before {
      left: 0;
      content: '\2022';
      color: #000;
      position: absolute;
    font-size: 20px;
  } */


/* width */
::-webkit-scrollbar {
  width: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

ul{
    text-align: left;
}
  .bar {
    width: 8px;
    height: 275px;
    border: 1px solid #AFA5A5;
    position: relative;
    margin: auto;
  }
  .bar::before {
    content: '';
    position: absolute;
    top: 283px;
    background-image: url(https://nsassets4.s3.amazonaws.com/landingpages/sound.png);
    background-repeat: no-repeat;
    left: -2px;
    width: 16px;
    height: 16px;
  }

.grid_pid{
  display: grid;
}
  .colun{
    height:20px;
    display: grid;
    margin-left: -30px;
    margin-top: 80px;
    width: 200px;
    position: absolute;

  }

  .colun-0{
    height: 20px;
    display: grid;
    margin-top: 61px;
    width: 200px;
    position: absolute;
    margin-left:-12px;
  }

  .colun-1{
    height: 20px;
    display: grid;
    margin-top: 40px;
    margin-left: 5px;
    width:200px;
    position: absolute;
  }  

  .colun-2{
    height: 20px;
    width: 200px;
    margin-left: 24px;
    margin-top: 20px;
    position: absolute;
}
 

  .colun-3{
    height: 20px;
    width: 200px;
    margin-left: 45px;
    margin-top: -0px;  
    position: absolute;
  }

  .colun-4{
    height:  20px;
    width: 200px;
    margin-left: 65px;
    margin-top: -20px;
    position: absolute;
 
  }
    
    @media (height: 644px) {
      .Drawer-esquerdo {
        height: 38.7vw;
      }
    }
    @media (height: 682px) {
      .Drawer-esquerdo {
        height: 45vw;
      }
    }
    @media (height: 667px) {
      .Drawer-esquerdo {
        height: 44vw;
      }
    }
    @media (height: 548px) {
      .Drawer-esquerdo {
        height: 36.5vw;
      }
    }
    @media (height: 860px) {
      .Drawer-esquerdo {
        height: 42.3vw;
      }
    }
    @media (height: 892px) {
      .Drawer-esquerdo {
        height: 44vw;
      }
    }
    @media (height: 993px) {
      .Drawer-esquerdo {
        height: 49.6vw;
      }
    }
    @media (height: 979px) {
      .Drawer-esquerdo {
        height: 47.6vw;
      }
    }
    @media (height: 900px) {
      .Drawer-esquerdo {
        height: 44.4vw;
      }
    }
    @media (height: 767px) {
      .Drawer-esquerdo {
        height: 37.4vw;
      }
    }
    @media (height: 799px) {
      .Drawer-esquerdo {
        height: 39.1vw;
      }
    }
    @media (height: 1440px) {
      .Drawer-esquerdo {
        height: 73.1vw;
      }
    }
    @media (height: 1307px) {
      .Drawer-esquerdo {
        height: 66.1vw;
      }
    }
    @media (height: 1339px) {
      .Drawer-esquerdo {
        height: 67.8vw;
      }
    }
    @media (max-width: 425px) {
      .Drawer-esquerdo {
        height: 36rem;
      }
  }
  @media (max-width: 710px) {
      .Drawer-esquerdo {
        height: 38rem;
      }
    }
  
  @media (max-width: 1024px) {
      .Drawer-esquerdo {
        width: 100%;
        height: 54vw;
      }
  }  
  
  @media (min-width: 1680px) {
      .Drawer-esquerdo {
        height: 45.4vw!important;
      }
    }
  
 
    
    .Drawer-esquerdo {
      width: 100%;
      height: 46.4vw;
    }
  
  
    @media (max-width: 768px) {
      .Drawer-esquerdo {
        width: 100%;
        height: 52rem;
      }
    
      .background-login {
        height: 70vw;
      }
    }
  
    .camera {
      padding: 0;
      height: 100%;
      width: 85%!important;
      padding-left: 10px;
      margin-top: unset;
    }
  
    video {
      width: 46% !important;
      height: 10% !important;
      /* height: 300px; */
    }


/* Novo css  */
html,body{
    border:none; padding:0; margin:0;
    background:#FFFFFF;
    color:#202020;
}
body{
    text-align:center;
    font-family:"Roboto",sans-serif;
}
h1{
    color:#404040;
}
#startStopBtn{
    display: inline-block;
    margin: 0 auto;
    color: #6060AA;
    background-color: rgba(0,0,0,0);
    border: 0.15em solid #6060FF;
    border-radius: 0.3em;
    transition: all 0.3s;
    box-sizing: border-box;
    width: 85px;
    /* height: 3em; */
    line-height: 2.7em;
    cursor: pointer;
    box-shadow: 0 0 0 rgba(0,0,0,0.1), inset 0 0 0 rgba(0,0,0,0.1);
    padding: 0px;
    text-align: center;
}
#startStopBtn:hover{
    box-shadow: 0 0 2em rgba(0,0,0,0.1), inset 0 0 1em rgba(0,0,0,0.1);
}
#startStopBtn.running{
    background-color:#FF3030;
    border-color:#FF6060;
    color:#FFFFFF;
}
#startStopBtn:before{
    content:"Start";
}
#startStopBtn.running:before{
    content:"Abort";
}
 
div.testArea{
    display: inline-block;
    width: 7em;
    height: 8.2em;
    position: relative;
    box-sizing: border-box;
}
div.testName{
    position: absolute;
    top: 0.1em;
    left: 0;
    width: 50%;
    font-size: 1.1em;
    z-index: 9;

}
div.meterText{
    position: absolute;
    bottom: 3em;
    left: 0;
    width: 50%;
    font-size: 1.5em;
    z-index: 9;
 
}
#dlText{
    color:#6060AA;
}
#ulText{
    color:#309030;
}
#pingText,#jitText{
    color:#AA6060;
}
div.meterText:empty:before{
    color:#4d7dff !important;
    content:"0.00";
}
div.unit{
    position: absolute;
    bottom: 3em;
    left: 0;
    width: 100%;
    z-index: 9;
}
div.testGroup{
    display:inline-block;
}
@media all and (max-width:65em){
    body{
        font-size:1.5vw;
    }
}
@media all and (max-width:40em){
    body{
        font-size:0.8em;
    }
    div.testGroup{
        display:block;
        margin: 0 auto;
    }
}
