<mat-card appearance="outlined" class=" mother-div">
    <mat-card-content class="" id="">
        <div class="col-md-12 col-sm-12 col-xs-3 row " style="margin-left: 0px;">

            <mat-icon class="icon-title"> account_box</mat-icon>
            <h4 class="title-content" style="margin-bottom: auto !important;
            margin-top: auto !important;">Carteira</h4>

        </div>



        <div class="shadow p-3 mt-5" *ngIf="graficoValoresVazio == false">
            <div class="p-3">
                <h4 class="text-center">{{ nomeMesAnterior }}</h4>
            </div>
            <!-- <raw-chart style="width: 100%" [chartData]="rawChartData" [dynamicResize]="true" [formatter]="rawFormatter">
            </raw-chart> -->
        </div>


        <div class="shadow p-3 mt-5">
            <div class="p-3">
                <h4 class="text-center">Últimos pagamentos recebidos</h4>
            </div>
            <hr class="sep-1" />

            <div *ngIf="listaPagamentos?.length; else naoHaDados">
                <div class="row ajuste-row" *ngFor="let item of listaPagamentos">

                    <div class="col-md-6 row ajuste-row d-flex align-items-center justify-content-center">
                        <div>
                            <span class="material-icons">
                                description
                            </span>
                        </div>

                        <div>
                            <span>{{ item.desPagamento }}</span>
                        </div>
                    </div>
                    <div class="col row ajuste-row d-flex align-items-center justify-content-center">
                        <div>
                            <span class="material-icons">
                                calendar_today
                            </span>
                        </div>

                        <div>
                            <span>{{ item.dtaPagamento | date: 'dd/MM/yyyy'}}</span>
                        </div>
                    </div>
                    <div class="col row ajuste-row d-flex align-items-center justify-content-center">
                        <div class="">
                            <span class="material-icons">
                                monetization_on
                            </span>
                        </div>

                        <div class="">
                            <span>{{ item.vlrPagamento | currency:'BRL':true }}</span>
                        </div>
                    </div>

                    <hr class="sep-2" />
                </div>
            </div>

            <ng-template #naoHaDados>
                <span><b>Não há recebimento...</b></span>
            </ng-template>


        </div>
    </mat-card-content>
</mat-card>