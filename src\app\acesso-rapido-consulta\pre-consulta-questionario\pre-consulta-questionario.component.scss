// Variáveis de cores
$primary-color: #667eea;
$secondary-color: #764ba2;
$accent-color: #f093fb;
$success-color: #4facfe;
$background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$ai-color: #667eea;
$text-dark: #2d3748;
$text-light: #718096;
$white: #ffffff;
$shadow: 0 10px 25px rgba(0, 0, 0, 0.1);

.ai-questionnaire-container {
  min-height: 100vh;
  background: $background;
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: 1.3em;
  position: relative;
  overflow-x: hidden;
}

/* #region Tela Ociosa */
.idle-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.idle-content {
  text-align: center;
  background: rgba($white, 0.95);
  padding: 60px 40px;
  border-radius: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 550px;
  width: 100%;
  backdrop-filter: blur(10px);
  border: 1px solid rgba($white, 0.2);

  h1 {
    color: $text-dark;
    font-size: 2.8em;
    font-weight: 700;
    margin: 30px 0 15px 0;
  }

  p {
    color: $text-light;
    font-size: 1.3em;
    margin-bottom: 40px;
    line-height: 1.6;
  }
}

/* IA Robot CSS */
.ai-robot {
  display: inline-block;
  animation: float 3s ease-in-out infinite;
  transform: scale(1.1);
}

.robot-head {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  border-radius: 20px;
  position: relative;
  margin: 0 auto 15px;
  box-shadow: 0 8px 20px rgba($primary-color, 0.3);
}

.robot-eyes {
  display: flex;
  justify-content: space-between;
  padding: 20px 15px 0;
}

.eye {
  width: 12px;
  height: 12px;
  background: $white;
  border-radius: 50%;
  animation: blink 3s infinite;

  &.left-eye {
    animation-delay: 0.1s;
  }

  &.right-eye {
    animation-delay: 0.2s;
  }
}

.robot-mouth {
  width: 20px;
  height: 8px;
  background: $white;
  border-radius: 0 0 10px 10px;
  margin: 8px auto 0;
}

.robot-body {
  width: 60px;
  height: 40px;
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  border-radius: 15px;
  margin: 0 auto;
  position: relative;
}

.robot-chest {
  width: 8px;
  height: 8px;
  background: $white;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

/* Start Button */
.start-btn {
  background: linear-gradient(135deg, $success-color, $accent-color);
  border: none;
  padding: 20px 45px;
  border-radius: 50px;
  color: $white;
  font-size: 1.6em;
  font-weight: 700;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba($success-color, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba($success-color, 0.4);
  }

  span {
    position: relative;
    z-index: 2;
  }
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba($white, 0.3), transparent);
  transition: left 0.5s;
}

.start-btn:hover .btn-glow {
  left: 100%;
}
/* #endregion */

/* #region Chat Interface */
.chat-interface {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Controls Header */
.controls-header {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba($white, 0.95);
  padding: 12px 20px;
  border-radius: 25px;
  box-shadow: $shadow;
  backdrop-filter: blur(10px);
}

.mode-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  border-radius: 50%;
  color: $white;

  .mode-icon {
    font-size: 20px;
  }
}

.mode-toggle {
  font-weight: 500;
  color: $text-dark;
}

/* Main Chat Area */
.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  padding: 120px 30px 30px;
  max-width: 85vmax;
  margin: 0 auto;
  width: 100%;
}

/* AI Section */
.ai-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.ai-avatar {
  width: 132px;
  height: 132px;
  background: $white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: $shadow;
  transition: all 0.3s ease;

  &.processing {
    animation: processing-pulse 2s infinite;
    box-shadow: 0 0 30px rgba($primary-color, 0.5);
  }

  &.listening {
    animation: listening-pulse 1s infinite;
    box-shadow: 0 0 30px rgba($accent-color, 0.5);
  }

  &.waiting {
    animation: waiting-pulse 2s infinite;
    box-shadow: 0 0 30px rgba($success-color, 0.5);
  }
}

.ai-face {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.ai-eyes {
  display: flex;
  gap: 12px;

  .eye {
    width: 8px;
    height: 8px;
    background: $primary-color;
    border-radius: 50%;
    animation: ai-blink 4s infinite;
  }
}

.ai-mouth {
  width: 16px;
  height: 6px;
  background: $primary-color;
  border-radius: 0 0 8px 8px;
  transition: all 0.3s ease;

  &.talking {
    animation: mouth-talk 0.5s infinite alternate;
  }
}

.ai-pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 3px solid $primary-color;
  border-radius: 50%;
  animation: pulse-ring 2s infinite;
}

/* Response Section */
.response-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 700px;
}

.ai-message {
  max-width: 100%;
  margin-bottom: 20px;
  animation: fadeIn 0.5s ease-in-out;
}

.message-bubble {
  background: $white;
  padding: 25px 30px;
  border-radius: 25px;
  box-shadow: $shadow;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 20px;
    bottom: -10px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid $white;
  }

  p {
    margin: 0;
    color: $text-dark;
    font-size: 1.4em;
    line-height: 1.6;
    font-weight: 500;
  }
}

.processing-indicator {
  display: flex;
  align-items: center;
  gap: 15px;
  color: $text-light;
  font-style: italic;
}

.typing-dots {
  display: flex;
  gap: 4px;

  span {
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}
/* #endregion */

/* #region Data Section */
.data-section {
  width: 100%;
  max-width: 700px;
  margin-top: 20px;
}

.data-panel {
  background: rgba($white, 0.9);
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  width: 100%;
  animation: fadeIn 0.5s ease-in-out;
  border: 1px solid rgba($primary-color, 0.1);

  h3 {
    margin: 0 0 20px 0;
    color: $text-dark;
    font-size: 1.5em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid rgba($primary-color, 0.1);
    padding-bottom: 15px;

    &::before {
      content: "📋";
      font-size: 1.2em;
    }
  }
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 40vh !important;
  overflow-y: auto !important;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.data-item {
  padding: 12px 15px;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid $success-color;

  .descInfoCategoria {
    display: block;
    font-size: 1.15em;
    color: $text-light;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .descInfovalue {
    display: block;
    color: $text-dark;
    font-weight: 600;
    font-size: 1.25em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Input Section */
.input-section {
  padding: 20px 30px 30px;
  background: rgba($white, 0.95);
  backdrop-filter: blur(10px);
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

.user-input {
  width: 100%;

  .mat-mdc-form-field-wrapper {
    background: $white;
    border-radius: 25px;
    box-shadow: $shadow;
  }

  .mat-mdc-text-field-wrapper {
    border-radius: 25px;
  }

  input {
    font-size: 1.6em;
    padding: 15px 20px;
  }
}

.voice-display {
  background: $white;
  border-radius: 25px;
  box-shadow: $shadow;
  padding: 20px 30px;
  text-align: center;
}

.voice-input-field {
  .voice-placeholder {
    color: $text-light;
    font-size: 1.2em;
    font-style: italic;
  }
}

/* Audio Visualization */
.audio-visualization {
  position: fixed;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba($white, 0.95);
  padding: 20px 30px;
  border-radius: 25px;
  box-shadow: $shadow;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 1000;
}

.sound-wave {
  display: flex;
  align-items: center;
  gap: 3px;
  height: 40px;
}

.wave-bar {
  width: 4px;
  background: linear-gradient(to top, $primary-color, $accent-color);
  border-radius: 2px;
  animation: wave-animation 1.5s infinite ease-in-out;

  &:nth-child(1) {
    animation-delay: 0s;
    height: 20px;
  }
  &:nth-child(2) {
    animation-delay: 0.1s;
    height: 30px;
  }
  &:nth-child(3) {
    animation-delay: 0.2s;
    height: 40px;
  }
  &:nth-child(4) {
    animation-delay: 0.3s;
    height: 35px;
  }
  &:nth-child(5) {
    animation-delay: 0.4s;
    height: 25px;
  }
  &:nth-child(6) {
    animation-delay: 0.5s;
    height: 40px;
  }
  &:nth-child(7) {
    animation-delay: 0.6s;
    height: 30px;
  }
  &:nth-child(8) {
    animation-delay: 0.7s;
    height: 20px;
  }
}

.recording-text {
  color: $text-dark;
  font-weight: 600;
  font-size: 1.1em;
}

/* Voice Status Indicator */
.voice-status-indicator {
  position: fixed;
  bottom: 30px;
  right: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  z-index: 1000;
}

.status-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, $text-light, #a0aec0);
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 25px rgba($text-light, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &.recording {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    animation: recording-pulse 1s infinite;
  }

  &.processing {
    background: linear-gradient(135deg, $primary-color, $secondary-color);
    animation: processing-pulse 2s infinite;
  }

  mat-icon {
    font-size: 28px;
    z-index: 2;
    position: relative;
  }
}

.status-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba($white, 0.3);
  animation: ripple 1.5s infinite;
}

.status-text {
  background: rgba($white, 0.95);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: 600;
  color: $text-dark;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  white-space: nowrap;
}
/* #endregion */

/* #region Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes blink {
  0%,
  90%,
  100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ai-blink {
  0%,
  90%,
  100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

@keyframes mouth-talk {
  0% {
    transform: scaleY(1);
  }
  100% {
    transform: scaleY(1.5);
  }
}

@keyframes processing-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes listening-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes waiting-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes wave-animation {
  0%,
  100% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1);
  }
}

@keyframes recording-pulse {
  0%,
  100% {
    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(255, 71, 87, 0.8);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
/* #endregion */

/* #region Responsividade */
@media (max-width: 1200px) {
  .main-chat-area {
    padding: 100px 20px 20px;
  }

  .ai-avatar {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 768px) {
  .main-chat-area {
    flex-direction: column;
    gap: 30px;
    padding: 120px 20px 20px;
  }

  .ai-section {
    order: 1;
    padding-top: 0;
  }

  .response-section {
    order: 2;
    min-height: 200px;
  }

  .data-section {
    order: 3;
    padding-top: 0;
  }

  .controls-header {
    position: relative;
    top: auto;
    left: auto;
    margin-bottom: 20px;
    justify-content: center;
  }

  .idle-content {
    padding: 40px 30px;

    h1 {
      font-size: 1.8em;
    }

    p {
      font-size: 1.1em;
    }
  }

  .robot-head {
    width: 60px;
    height: 60px;
  }

  .robot-body {
    width: 45px;
    height: 30px;
  }

  .ai-avatar {
    width: 80px;
    height: 80px;
  }

  .message-bubble {
    padding: 20px 25px;

    p {
      font-size: 1.1em;
    }
  }

  .voice-btn {
    width: 60px;
    height: 60px;
    bottom: 20px;
    right: 20px;

    mat-icon {
      font-size: 24px;
    }
  }

  .audio-visualization {
    bottom: 100px;
    padding: 15px 25px;
  }
}

@media (max-width: 480px) {
  .idle-content {
    padding: 30px 20px;

    h1 {
      font-size: 1.6em;
    }
  }

  .controls-header {
    padding: 10px 15px;
    gap: 10px;
  }

  .mode-indicator {
    width: 35px;
    height: 35px;

    .mode-icon {
      font-size: 18px;
    }
  }

  .main-chat-area {
    padding: 100px 15px 15px;
  }

  .message-bubble {
    padding: 15px 20px;

    p {
      font-size: 1em;
    }
  }

  .data-panel {
    padding: 20px;
  }

  .input-container {
    padding: 0 10px;
  }

  .voice-btn {
    width: 55px;
    height: 55px;

    mat-icon {
      font-size: 22px;
    }
  }
}
/* #endregion */

/* #region Modern History Modal */
// Enhanced Design System Variables
$modal-primary: #667eea;
$modal-secondary: #764ba2;
$modal-accent: #4facfe;
$modal-success: #10b981;
$modal-warning: #f59e0b;
$modal-error: #ef4444;
$modal-info: #3b82f6;

// Semantic color tokens
$surface-primary: #ffffff;
$surface-secondary: #f8fafc;
$surface-tertiary: #f1f5f9;
$surface-elevated: #ffffff;
$surface-overlay: rgba(15, 23, 42, 0.8);

// Text hierarchy
$text-primary: #0f172a;
$text-secondary: #475569;
$text-tertiary: #64748b;
$text-inverse: #ffffff;
$text-accent: $modal-primary;

// Spacing system (8px base)
$space-xs: 0.25rem; // 4px
$space-sm: 0.5rem; // 8px
$space-md: 1rem; // 16px
$space-lg: 1.5rem; // 24px
$space-xl: 2rem; // 32px
$space-2xl: 3rem; // 48px
$space-3xl: 4rem; // 64px

// Border radius scale
$radius-sm: 0.375rem; // 6px
$radius-md: 0.5rem; // 8px
$radius-lg: 0.75rem; // 12px
$radius-xl: 1rem; // 16px
$radius-2xl: 1.5rem; // 24px
$radius-full: 9999px;

// Shadow system
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// Typography scale
$text-xs: 0.75rem; // 12px
$text-sm: 0.875rem; // 14px
$text-base: 1rem; // 16px
$text-lg: 1.125rem; // 18px
$text-xl: 1.25rem; // 20px
$text-2xl: 1.5rem; // 24px
$text-3xl: 1.875rem; // 30px

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// Z-index scale
$z-modal: 1000;
$z-overlay: 999;
$z-dropdown: 50;

.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $surface-overlay;
  backdrop-filter: blur(8px) saturate(180%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: $z-modal;
  padding: $space-md;

  // Enhanced accessibility
  &:focus-within {
    outline: 2px solid $modal-accent;
    outline-offset: -2px;
  }
}

.modern-modal-container {
  background: $surface-elevated;
  border-radius: $radius-2xl;
  box-shadow: $shadow-2xl;
  width: 100%;
  max-width: 56rem; // 896px
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);

  // Glass morphism effect
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);

  @media (max-width: $breakpoint-md) {
    max-width: 95vw;
    max-height: 95vh;
    margin: $space-sm;
  }
}

// Modal Header
.modal-header-modern {
  padding: $space-xl $space-xl $space-lg;
  border-bottom: 1px solid $surface-tertiary;
  background: linear-gradient(135deg, rgba($modal-primary, 0.05) 0%, rgba($modal-secondary, 0.05) 100%);

  .header-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: $space-lg;
    margin-bottom: $space-lg;
  }

  .title-section {
    display: flex;
    align-items: flex-start;
    gap: $space-md;
    flex: 1;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, $modal-primary, $modal-secondary);
    border-radius: $radius-xl;
    box-shadow: $shadow-md;

    .header-icon {
      color: $text-inverse;
      font-size: 1.5rem;
    }
  }

  .title-text {
    flex: 1;

    .modal-title {
      margin: 0 0 $space-xs 0;
      font-size: $text-2xl;
      font-weight: 700;
      color: $text-primary;
      line-height: 1.2;
    }

    .modal-subtitle {
      margin: 0;
      font-size: $text-sm;
      color: $text-secondary;
      font-weight: 500;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: $space-sm;

    .action-btn {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: $radius-lg;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &.secondary {
        background: $surface-secondary;
        color: $text-secondary;

        &:hover {
          background: $surface-tertiary;
          color: $text-primary;
          transform: translateY(-1px);
          box-shadow: $shadow-md;
        }
      }

      &.close-btn {
        background: rgba($modal-error, 0.1);
        color: $modal-error;

        &:hover {
          background: rgba($modal-error, 0.15);
          transform: translateY(-1px);
          box-shadow: $shadow-md;
        }
      }
    }
  }

  // Progress Section
  .progress-section {
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $space-sm;

      .progress-text {
        font-size: $text-sm;
        color: $text-secondary;
        font-weight: 500;
      }

      .progress-percentage {
        font-size: $text-sm;
        color: $modal-accent;
        font-weight: 700;
      }
    }

    .progress-bar {
      height: 0.5rem;
      background: $surface-tertiary;
      border-radius: $radius-full;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, $modal-accent, $modal-primary);
        border-radius: $radius-full;
        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          animation: shimmer 2s infinite;
        }
      }
    }
  }
}

// Search and Filter Section
.search-filter-section {
  padding: $space-lg $space-xl;
  border-bottom: 1px solid $surface-tertiary;
  background: $surface-secondary;

  .search-container {
    margin-bottom: $space-md;

    .search-field {
      width: 100%;

      .mat-mdc-form-field-wrapper {
        background: $surface-primary;
        border-radius: $radius-xl;
        box-shadow: $shadow-sm;
        border: 1px solid $surface-tertiary;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: rgba($modal-primary, 0.3);
          box-shadow: $shadow-md;
        }

        &:focus-within {
          border-color: $modal-primary;
          box-shadow: 0 0 0 3px rgba($modal-primary, 0.1);
        }
      }
    }
  }

  .filter-chips {
    .filter-chip-list {
      display: flex;
      flex-wrap: wrap;
      gap: $space-sm;

      .mat-mdc-chip-option {
        border-radius: $radius-full;
        font-weight: 500;
        font-size: $text-sm;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        &.filter-chip-personal {
          --mdc-chip-selected-container-color: #{rgba($modal-primary, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-primary};
        }

        &.filter-chip-medical {
          --mdc-chip-selected-container-color: #{rgba($modal-success, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-success};
        }

        &.filter-chip-contact {
          --mdc-chip-selected-container-color: #{rgba($modal-info, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-info};
        }

        &.filter-chip-optional {
          --mdc-chip-selected-container-color: #{rgba($modal-warning, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-warning};
        }
      }
    }
  }
}

// Modal Body
.modal-body-modern {
  flex: 1;
  overflow-y: auto;
  padding: $space-lg $space-xl;

  .content-wrapper {
    max-height: 100%;
  }

  // Empty State
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $space-3xl $space-lg;
    text-align: center;

    .empty-icon {
      width: 4rem;
      height: 4rem;
      background: $surface-tertiary;
      border-radius: $radius-full;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: $space-lg;

      mat-icon {
        font-size: 2rem;
        color: $text-tertiary;
      }
    }

    .empty-title {
      margin: 0 0 $space-sm 0;
      font-size: $text-xl;
      font-weight: 600;
      color: $text-primary;
    }

    .empty-description {
      margin: 0;
      font-size: $text-sm;
      color: $text-secondary;
      max-width: 24rem;
    }
  }

  // Data Grid
  .data-grid {
    display: grid;
    gap: $space-md;
    grid-template-columns: 1fr;

    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Data Cards
  .data-card {
    background: $surface-primary;
    border: 1px solid $surface-tertiary;
    border-radius: $radius-xl;
    padding: $space-lg;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, $modal-primary, $modal-secondary);
      opacity: 0;
      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover {
      border-color: rgba($modal-primary, 0.2);
      box-shadow: $shadow-lg;
      transform: translateY(-2px);

      &::before {
        opacity: 1;
      }
    }

    // Category-specific styling
    &.card-personal {
      border-left: 4px solid $modal-primary;
    }

    &.card-medical {
      border-left: 4px solid $modal-success;
    }

    &.card-contact {
      border-left: 4px solid $modal-info;
    }

    &.card-optional {
      border-left: 4px solid $modal-warning;
    }

    .card-header {
      display: flex;
      align-items: flex-start;
      gap: $space-md;
      margin-bottom: $space-md;

      .card-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: $surface-secondary;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        mat-icon {
          font-size: 1.25rem;
          color: $text-secondary;
        }
      }

      .card-title-section {
        flex: 1;
        min-width: 0;

        .card-title {
          margin: 0 0 $space-xs 0;
          font-size: $text-base;
          font-weight: 600;
          color: $text-primary;
          line-height: 1.3;
        }

        .card-category {
          font-size: $text-xs;
          color: $text-tertiary;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }

      .card-actions {
        display: flex;
        gap: $space-xs;
        opacity: 0;
        transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        .card-action-btn {
          width: 2rem;
          height: 2rem;
          border-radius: $radius-md;
          background: $surface-secondary;
          color: $text-secondary;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

          mat-icon {
            font-size: 1rem;
          }

          &:hover {
            background: $modal-primary;
            color: $text-inverse;
            transform: scale(1.1);
          }
        }
      }
    }

    &:hover .card-actions {
      opacity: 1;
    }

    .card-content {
      .value-container {
        margin-bottom: $space-md;

        .card-value {
          font-size: $text-base;
          color: $text-primary;
          font-weight: 500;
          line-height: 1.5;
          word-break: break-word;

          ::ng-deep mark {
            background: rgba($modal-accent, 0.2);
            color: $modal-accent;
            padding: 0.125rem 0.25rem;
            border-radius: $radius-sm;
            font-weight: 600;
          }
        }
      }

      .card-metadata {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: $space-sm;
        padding-top: $space-sm;
        border-top: 1px solid $surface-tertiary;

        .timestamp {
          display: flex;
          align-items: center;
          gap: $space-xs;
          font-size: $text-xs;
          color: $text-tertiary;

          mat-icon {
            font-size: 0.875rem;
          }
        }

        .validation-status {
          display: flex;
          align-items: center;
          gap: $space-xs;
          font-size: $text-xs;
          font-weight: 500;
          padding: $space-xs $space-sm;
          border-radius: $radius-full;

          mat-icon {
            font-size: 0.875rem;
          }

          &.status-valid {
            background: rgba($modal-success, 0.1);
            color: $modal-success;
          }

          &.status-warning {
            background: rgba($modal-warning, 0.1);
            color: $modal-warning;
          }

          &.status-error {
            background: rgba($modal-error, 0.1);
            color: $modal-error;
          }
        }
      }
    }
  }
}

// Modal Footer
.modal-footer-modern {
  padding: $space-lg $space-xl;
  border-top: 1px solid $surface-tertiary;
  background: $surface-secondary;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $space-lg;

  .footer-info {
    .info-text {
      display: flex;
      align-items: center;
      gap: $space-sm;
      font-size: $text-sm;
      color: $text-secondary;

      mat-icon {
        font-size: 1rem;
        color: $modal-info;
      }
    }
  }

  .footer-actions {
    display: flex;
    gap: $space-md;

    .secondary-btn {
      border-radius: $radius-lg;
      font-weight: 500;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: $shadow-md;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .primary-btn {
      border-radius: $radius-lg;
      font-weight: 600;
      background: linear-gradient(135deg, $modal-primary, $modal-secondary);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: $shadow-lg;
        background: linear-gradient(135deg, darken($modal-primary, 5%), darken($modal-secondary, 5%));
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    align-items: stretch;
    gap: $space-md;

    .footer-actions {
      justify-content: stretch;

      .secondary-btn,
      .primary-btn {
        flex: 1;
      }
    }
  }
}

// Animations
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Accessibility enhancements
@media (prefers-reduced-motion: reduce) {
  .modern-modal-container,
  .data-card,
  .action-btn,
  .card-action-btn,
  .secondary-btn,
  .primary-btn {
    transition: none;
  }

  .progress-fill::after {
    animation: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .modern-modal-container {
    border: 2px solid $text-primary;
  }

  .data-card {
    border: 2px solid $text-secondary;
  }

  .search-field .mat-mdc-form-field-wrapper {
    border: 2px solid $text-secondary;
  }
}

// Dark mode support (if needed in the future)
@media (prefers-color-scheme: dark) {
  // Dark mode variables would go here
  // This is prepared for future dark mode implementation
}
/* #endregion */

/* #region Botão de Teste */
.test-button {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  font-size: 0.9em !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
    background: linear-gradient(135deg, #f57c00 0%, #e65100 100%) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }

  mat-icon {
    margin-right: 8px !important;
    font-size: 1.1em !important;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

// Estilo específico para o botão na tela inicial
.idle-content .test-button {
  margin-top: 10px;
  font-size: 0.85em !important;
  padding: 10px 20px !important;
}

// Estilo específico para o botão no header de controles
.controls-header .test-button {
  margin-left: 15px;
  font-size: 0.8em !important;
  padding: 8px 16px !important;
}
/* #endregion */
