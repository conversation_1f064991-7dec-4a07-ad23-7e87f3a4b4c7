import { SpinnerService } from './../../service/spinner.service';
import { Component, OnInit } from '@angular/core';
import { MatDialogRef as MatDialogRef, MatDialog as MatDialog } from '@angular/material/dialog';
import { ModalConfirmacaoComponent } from '../modal-confirmacao/modal-confirmacao.component';
import { ObjConsultaChat, ObjMensagemEnvio, ObjMensagemWhatsappPadrao } from 'src/app/model/whats';
import { WhatsService } from 'src/app/service/whats.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { ModalInformacaoComponent, objModalInformacaoContent } from '../modal-informacao/modal-informacao.component';
import { ToggleComponent } from 'src/app/Util/toggle/toggle.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCardModule } from '@angular/material/card';

@Component({
    selector: 'app-modal-envio-mensagens-whatsapp',
    templateUrl: './modal-envio-mensagens-whatsapp.component.html',
    styleUrls: ['./modal-envio-mensagens-whatsapp.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      ToggleComponent,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatIcon,
      MatFormFieldModule,
      MatCardModule
    ]
})
export class ModalEnvioMensagensWhatsappComponent implements OnInit {

  lsMensagenSalvas: ObjMensagemWhatsappPadrao[] = [];
  lsConsultas: ObjConsultaChat[] = [];
  flgMsgTemResposta: boolean = false;
  listaIdsConsultas: number[] = [];
  msg?: string;
  idTipoMensagem: number = 1;
  constructor(
    public dialogRef: MatDialogRef<ModalConfirmacaoComponent>,
    private whatsService: WhatsService,
    private spinner: SpinnerService,
    private snackbar: AlertComponent,
    private matDialog: MatDialog
  ) { }

  ngOnInit(): void {
    this.CarregarMensagens();
    this.CarregaConsultas();
  }

  CarregarMensagens() {
    this.spinner.show();
    this.whatsService.GetListaMensagemPadrao(1).subscribe((ret) => {
      this.lsMensagenSalvas = ret;
      ;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
      this.snackbar.falhaSnackbar("Erro ao carregar dados");
    })
  }

  CarregaConsultas() {
    this.spinner.show();
    this.whatsService.CarregaConsultaChat().subscribe((ret) => {
      ;
      this.lsConsultas = ret;
      this.spinner.hide();
    },
      () => {
        this.spinner.hide();
        this.snackbar.falhaSnackbar("Erro ao carregar dados");
      }
    )
  }

  CarregaMsg(item: ObjMensagemWhatsappPadrao) {
    ;
    this.msg = item.mensagem;
    this.idTipoMensagem = item.idMensagemWhatsappPadrao!;
    this.flgMsgTemResposta = item.flgAguardaResposta!;
  }

  enviaMensagens() {

    if (!this.validaEnviarMsg())
      return;

    else {
      var objMsg = new ObjMensagemEnvio();
      objMsg.flgAguardaResposta = this.flgMsgTemResposta;
      objMsg.idsConsulta = this.listaIdsConsultas;
      objMsg.mensagem = this.msg!;
      objMsg.idTipoMensagem = this.idTipoMensagem

      this.spinner.show();
      this.whatsService.EnviarMensagensConsultas(objMsg).subscribe(() => {
        this.snackbar.sucessoSnackbar("Envio concluido");
        this.spinner.hide();
      },() => {
          ;
          this.snackbar.falhaSnackbar("Falha ao enviar as mensagens");
          this.spinner.hide();
        }
      )

    }


  }

  validaEnviarMsg(): boolean {
    if (this.msg === null || typeof this.msg !== 'string') {
      this.snackbar.falhaSnackbar("Preencha a mensagem para poder enviá-la.");
      return false;
    }

    const limpaStr = this.msg.replace(/\s+/g, '');
    const numeroCaracteres = limpaStr.length;

    if (numeroCaracteres < 1) {
      this.snackbar.falhaSnackbar("Preencha a mensagem para poder enviá-la.");
      return false;
    }

    if (this.listaIdsConsultas.length < 1) {
      this.snackbar.falhaSnackbar("Selecione no mínimo 1 consulta para poder enviar a mensagem.");
      return false;
    }

    return true;
  }

  ValidaUsuarioSelecionado(idConsulta: number): boolean {
    return this.listaIdsConsultas.includes(idConsulta);
  }

  AlteraStatusConsultaLista(idConsulta: number) {
    const index = this.listaIdsConsultas.indexOf(idConsulta);
    if (index > -1)
      this.listaIdsConsultas.splice(index, 1);
    else
      this.listaIdsConsultas.push(idConsulta);

  }

  abrirModalInformacao() {

    let dt = new objModalInformacaoContent();
    dt.strTitulo = "Variaveis para as mensagens"
    dt.strCorpo = '<div style="text-align:center;">Para utilizar informações como o nome do médico ou o horario do agendamento utilize as seguintes variaveis:<br><ol><li><b>Nome do médico</b> - "@NOMEMEDICO"</li><li><b>Nome do paciente</b> - "@NOMEPACIENTE"</li><li><b>Data do agendamento</b> - "@DTAGENDAMENTO"</li></ol></div>'
    this.matDialog.open(ModalInformacaoComponent, {
      data: dt
    })
  }

  fecharModal() {
    this.dialogRef.close();
  }

  formatDate(dateInput: string | Date | null): string {
    if (!dateInput) {
      return '';
    }

    // Converte o valor para um objeto Date, tratando casos de string
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      return ''; // Retorna vazio se não for uma data válida
    }

    // Formata os valores para DD/MM/YYYY e HH:mm
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Meses começam do zero
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} - ${hours}:${minutes}`;
  }
}
