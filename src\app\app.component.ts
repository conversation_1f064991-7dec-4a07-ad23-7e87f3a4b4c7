import { ChangeDetectorRef, Component } from '@angular/core';
import { LoginService } from './service/login.service';
import { CalendarEvent, CalendarView, DAYS_OF_WEEK } from 'angular-calendar';
import { TranslateService } from '@ngx-translate/core';
import { SpinnerService } from './service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, RouterOutlet } from '@angular/router';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { SpinnerComponent } from './spinner/spinner.component';
@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    standalone: true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      RouterModule,
      RouterOutlet,
      MatMenuModule,
      SpinnerComponent
    ],
    
})



export class AppComponent {
  flgExibeSpinner: boolean = false;
  screenWidth: number;
  showFiller?: boolean;
  FillerOpen = false;

  readonly VAPID_PUBLIC_KEY = "BAZhaS0vNZSmy4afUHkNIF2TlUfCcaQCcMOexTa9x1VZoi_AZZpAEZNzVt-xoQ8tMEvGmr3kGP8ClDbUurtyM74";

  view: CalendarView = CalendarView.Month;

  viewDate = new Date();

  events: CalendarEvent[] = [];


  locale: string = 'ptBr';

  weekStartsOn: number = DAYS_OF_WEEK.MONDAY;

  weekendDays: number[] = [DAYS_OF_WEEK.FRIDAY, DAYS_OF_WEEK.SATURDAY];

  CalendarView = CalendarView;

  setView(view: CalendarView) {
    this.view = view;
  }

  constructor(
    // private swPush: SwPush,
    private LoginService: LoginService,
    translate: TranslateService,
    private spinner: SpinnerService,
    private cdr: ChangeDetectorRef

  ) {
    this.LoginService.recuperarSenha$
      .subscribe((res: any) => {
        this.RecuperarSenha(res);
      });

    // this language will be used as a fallback when a translation isn't found in the current language
    translate.setDefaultLang('pt');

    // the lang to use, if the lang isn't available, it will use the current loader to get them
    translate.use(sessionStorage.getItem('Linguagem') || 'pt');


    // set screenWidth on page load
    this.screenWidth = window.innerWidth;
    window.onresize = () => {
      this.screenWidth = window.innerWidth;
      if (this.screenWidth < 991)
        this.showFiller = true;
      else
        this.showFiller = false;
    };
  }

  privaci = false;
  title = 'TeleMedicina';
  mostrarMenu = false;
  recuperarSenha = false;
  stream = false;

  stream1 = false;
  conect = false;
  public static isMobile: boolean = false;


  ngAfterViewInit(): void {

    this.spinner.flgExibeSpinner$
    .subscribe((res: boolean) => {
      this.flgExibeSpinner = res;
    })

    this.cdr.detectChanges();
  } 

  ngOnInit() { 

    if (JSON.parse(sessionStorage.getItem('Logado')!)) {
      this.mostrarMenu = true;
      // if (sessionStorage.getItem('TelaStream') == 'true')
      //   this.Telastream(JSON.parse(sessionStorage.getItem('TelaStream')));
      // else
      if (sessionStorage.getItem('Conect') == 'true')
        this.TesteConecao(JSON.parse(sessionStorage.getItem('Conect')!));

      else if (sessionStorage.getItem('TelaStream1') == 'true')
        this.Telastream1(JSON.parse(sessionStorage.getItem('TelaStream1')!));
      else if (sessionStorage.getItem('Priva') == 'true') {
        this.privaci = true;
        this.mostrarMenu = false;
        this.conect = false;
        this.stream1 = false;
        this.stream = false;

      } else {
        this.mostrarMenu = true;
        this.stream = false;
        this.conect = false;
        this.privaci = false;
        this.stream1 = false;
      }
    }
    else if (JSON.parse(sessionStorage.getItem('Priva')!)) {
      this.privaci = true;
      this.mostrarMenu = false;
      this.conect = false;
      this.stream = false;

    }
    else {
      this.mostrarMenu = false;
      this.stream = false;
      this.stream1 = false;
      this.conect = false;
    }
  }

  Privacidade(value: boolean) {
    if (value) {
      this.privaci = true;
      this.mostrarMenu = false;
      this.conect = false;
      this.stream1 = false;
      this.stream = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      this.stream = false;
      this.conect = false;
      this.stream1 = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }
  Telastream1(value: boolean) {
    if (value) {
      this.mostrarMenu = false;
      this.conect = false;
      this.stream = false;
      this.stream1 = true;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      this.stream = false;
      this.conect = false;
      this.stream1 = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }
  Telastream(value: boolean) {
    if (value) {
      // this.mostrarMenu = false;
      this.conect = false;
      this.stream = true;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      this.stream = false;
      this.conect = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }
  TesteConecao(value: boolean) {
    if (value) {
      this.mostrarMenu = false;
      this.stream = false;
      this.conect = true;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "true");
    }
    else {
      this.mostrarMenu = true;
      this.stream = false;
      this.conect = false;
      this.privaci = false;
      // sessionStorage.setItem("TelaStream", "false");
    }
  }

  RecuperarSenha(value: boolean) {
    if (value) {
      this.mostrarMenu = false;
      this.recuperarSenha = true;
    }
    else {
      this.mostrarMenu = false;
      this.recuperarSenha = false;
    }

  }

  subscribeToNotifications() {

    // this.swPush.requestSubscription(
    //   {
    //     serverPublicKey: this.VAPID_PUBLIC_KEY
    //   })
    //   .then(sub => this.newsletterService.addPushSubscriber(sub).subscribe())
    //   .catch(err => console.error("Could not subscribe to notifications", err));
  }



}
