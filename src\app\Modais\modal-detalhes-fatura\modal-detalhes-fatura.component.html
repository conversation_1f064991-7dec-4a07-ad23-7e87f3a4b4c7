<div class="modal-container">
    <header class="modal-header">
        <div class="modal-title-container">
            <h3 class="modal-title">{{ flgFaturaProcedimento ? 'Lista Procedimentos' : 'Lista Consultas' }}</h3>
        </div>
        <button class="close-button" (click)="FecharModal()">
            <mat-icon>close</mat-icon>
        </button>
    </header>

    <main class="modal-container">
        <!-- Seção de Procedimentos -->
        <section *ngIf="flgFaturaProcedimento" class="content-section">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="column-procedure">Procedimento</th>
                            <th class="column-invoice">Fatura</th>
                            <th class="column-total">Total</th>
                            <th class="column-action">Selecionar</th>
                        </tr>
                    </thead>
                    
                    <tbody *ngIf="listaProcedimento && listaProcedimento.length > 0">
                        <tr *ngFor="let item of listaProcedimento">
                            <td class="text-left">{{item.desProcedimento}}</td>
                            <td class="text-center">{{item.desFatura}}</td>
                            <td class="text-center">{{item.vlrM2filme! + item.vlrCustoOp! + item.vlrHonorarios! + item.vlrParticular! + item.vlrConvenio!}}</td>
                            <td class="text-center">
                                <mat-checkbox (change)="AdicionarProcedimento($event, item.idGuiaTiss!)" color="primary">
                                </mat-checkbox>
                            </td>
                        </tr>
                    </tbody>
                    
                    <tbody *ngIf="!listaProcedimento || listaProcedimento.length === 0">
                        <tr>
                            <td colspan="4" class="empty-message">Nenhum procedimento encontrado.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
        
        <!-- Seção de Consultas -->
        <section *ngIf="!flgFaturaProcedimento" class="content-section">
            <div class="actions-bar">
                <button class="action-button report-button" (click)="BaixarRelatorioListaConsulta()">
                    <mat-icon>description</mat-icon>
                    <span>Baixar Relatório</span>
                </button>
            </div>
            
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="column-patient">Paciente</th>
                            <th class="column-date">Data Consulta</th>
                            <th class="column-total">Total</th>
                            <th class="column-action">Selecionar</th>
                        </tr>
                    </thead>
                    
                    <tbody *ngIf="listaConsulta && listaConsulta.length > 0">
                        <tr *ngFor="let item of listaConsulta">
                            <td class="text-left">{{item.paciente!.pessoa!.nomePessoa!}}</td>
                            <td class="text-center">{{item.dtaConsulta}}</td>
                            <td class="text-center">{{item.valorConsulta}}</td>
                            <td class="text-center">
                                <mat-checkbox (change)="AdicionarConsulta($event, item.idConsulta!)" color="primary">
                                </mat-checkbox>
                            </td>
                        </tr>
                    </tbody>
                    
                    <tbody *ngIf="!listaConsulta || listaConsulta.length === 0">
                        <tr>
                            <td colspan="4" class="empty-message">Nenhuma consulta encontrada.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </main>
    
    <footer class="modal-footer">
        <button class="action-button xml-button" (click)="BaixarZipXml()">
            <mat-icon>file_download</mat-icon>
            <span>Converter XML</span>
        </button>
    </footer>
</div>