/* Variáveis - Paleta Moderna com Verde Saúde */
$primary-color: #2E8B57; /* Verde Saúde */
$primary-light: #A3D9B1; /* Verde claro suavizado */
$primary-dark: #1F5F3D; /* Verde escuro */
$secondary-color: #F4F4F9; /* Cinza Claro / Off-White */
$secondary-light: #FFFFFF; /* Branco puro para elementos contrastantes */
$secondary-dark: #DADDE5; /* Cinza médio para hover/active */
$accent-color: #4ECDC4; /* Turquesa Claro (toque moderno) */
$error-color: #FF6B6B; /* Vermelho Pastel */
$text-primary: #333333; /* Cinza escuro para boa legibilidade */
$text-secondary: #6B7280; /* Cinza médio */
$border-color: #E5E7EB; /* Bordas suaves */
$bg-color: #F9FAFB; /* Fundo geral suave */
$card-bg: #FFFFFF; /* Fundo dos cards */
$border-radius: 12px;
$box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$transition: 0.2s;

/* Importação da fonte */
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

/* Container principal do modal */
.modal-container {
    display: flex;
    flex-direction: column;
    background-color: $bg-color;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Cabeçalho do modal */
.modal-header {
    background-color: $primary-color;
    padding: 16px 20px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.modal-title-container {
    flex: 1;
    text-align: center;
    max-width: 80%;
}

.modal-title {
    color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 20px;
    z-index: 10;
    pointer-events: none;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.close-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color $transition ease;
    z-index: 11;
}

.close-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Conteúdo principal */
.modal-content {
    flex: 1;
    padding: 20px;
    background-color: $secondary-light;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
}

/* Barra de ações */
.actions-bar {
    display: flex;
    justify-content: flex-start;
    padding: 0 0 16px 0;
}

/* Botões de ação */
.action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: $border-radius;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all $transition ease;
    min-width: 120px;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow;
}

.xml-button, 
.report-button {
    background-color: $accent-color;
    color: white;
}

.xml-button:hover,
.report-button:hover {
    background-color: darken($accent-color, 8%);
}

.cancel-button {
    background-color: $secondary-dark;
    color: $text-primary;
}

.cancel-button:hover {
    background-color: darken($secondary-dark, 5%);
}

.confirm-button {
    background-color: $primary-color;
    color: white;
}

.confirm-button:hover {
    background-color: $primary-dark;
}

/* Container da tabela */
.table-container {
    flex: 1;
    overflow: auto;
    border-radius: $border-radius;
    border: 1px solid $border-color;
    background-color: $card-bg;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.05);
}

/* Tabela de dados */
.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

/* Cabeçalho da tabela */
.data-table thead tr {
    background-color: $primary-color;
    color: white;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
}

.data-table th:first-child {
    border-top-left-radius: $border-radius;
}

.data-table th:last-child {
    border-top-right-radius: $border-radius;
}

/* Corpo da tabela */
.data-table tbody tr {
    transition: background-color $transition ease;
    border-bottom: 1px solid $border-color;
}

.data-table tbody tr:last-child {
    border-bottom: none;
}

.data-table tbody tr:hover {
    background-color: $secondary-color;
}

.data-table td {
    padding: 12px 16px;
    font-size: 14px;
    color: $text-primary;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Alinhamento de texto */
.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

/* Mensagem quando a lista está vazia */
.empty-message {
    text-align: center;
    padding: 24px;
    color: $text-secondary;
    font-style: italic;
}

/* Layout de colunas */
.column-procedure,
.column-patient {
    width: 40%;
}

.column-invoice,
.column-date {
    width: 30%;
}

.column-total {
    width: 15%;
}

.column-action {
    width: 15%;
    text-align: center;
}

/* Estilo do checkbox */
::ng-deep .mat-checkbox-checked.mat-primary .mat-checkbox-background {
    background-color: $primary-color !important;
}

/* Estilo da barra de rolagem */
.table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: $secondary-color;
    border-radius: $border-radius;
}

.table-container::-webkit-scrollbar-thumb {
    background: $primary-light;
    border-radius: $border-radius;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: $primary-color;
}

/* Rodapé do modal */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    background-color: $secondary-color;
    border-top: 1px solid $border-color;
}

/* Responsividade */
@media (max-width: 768px) {
    .modal-content {
        padding: 16px;
    }
    
    .action-button {
        min-width: 0;
        padding: 8px;
    }
    
    .action-button span {
        display: none;
    }
    
    .data-table th,
    .data-table td {
        padding: 10px 8px;
        font-size: 12px;
    }
}