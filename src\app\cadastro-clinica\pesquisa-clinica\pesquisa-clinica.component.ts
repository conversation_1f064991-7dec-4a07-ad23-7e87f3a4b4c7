import { Component, OnInit } from '@angular/core';
import { ClinicaService } from 'src/app/service/clinica.service';
import { Router, RouterModule } from '@angular/router';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
// import { FadeIn } from 'src/app/Util/Fadein.animation';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { SpinnerService } from 'src/app/service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';

@Component({
  selector: 'app-pesquisa-clinica',
  templateUrl: './pesquisa-clinica.component.html',
  styleUrls: ['./pesquisa-clinica.component.scss'],
  animations: [trigger('openClose', [
    state('open', style({
      opacity: '1',
      display: 'block'
    })),
    state('closed', style({
      opacity: '0',
      display: 'none'
    })),
    transition('open => closed', [
      animate('0.2s')
    ]),
    transition('closed => open', [
      animate('0.2s')
    ]),
  ])
  ],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatIcon,
    TranslateModule,
    RouterModule,
    NgxSmartModalModule,
    MatDividerModule,
    MatTooltipModule,
    MatSlideToggleModule
  ]
})
export class PesquisaClinicaComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private clinicaService: ClinicaService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private router: Router,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
    private snackBarAlert: AlertComponent,

  ) { }

  // @Output() FadeIn: string;
  isOpen = false;
  horaData = new Date().toLocaleString();
  imagemclinica: any = "assets/build/img/logo-clinica.png";
  Logo = false;

  inativos = false;
  DadosTab: any = [];
  bOcultaCarregaMais = false;
  pesquisa = ""
  qtdRegistros = 10;
  idUsuarioDelet: number = 0;
  // message: string = 'Usuario excluido com Sucesso.  ✔ ';
  // nconcordomessage: string = 'Excluido com Sucesso. ✔';
  // emailenviado: string = 'Email Enviado. ✔';
  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  concordo?: boolean;
  concordomsg?: boolean;
  legenda: boolean = false;

  ngOnInit() {
    this.CarregaTable();
  }
  toggle: any = {}

  CarregaTable() {
    try {
      this.bOcultaCarregaMais = false

      if (this.inativos == false) {
        this.clinicaService.getGridClinica(0, this.qtdRegistros, this.pesquisa).subscribe((retorno) => {

          this.DadosTab = retorno
          this.DadosTab.forEach((element: any) => {
            if (element.cnpj != null && element.cnpj != '' && element.cnpj != undefined)
              element.cnpj = this.Editado(element.cnpj);
          });
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.spinner.hide();
          this.snackBarAlert.falhaSnackbar("Falha ao carregar tabela!")
        })
      }
      else {
        this.clinicaService.getGridClinicaInativos(0, this.qtdRegistros, this.pesquisa).subscribe((retorno) => {

          this.DadosTab = retorno
          this.DadosTab.forEach((element: any) => {
            if (element.cnpj != null && element.cnpj != '' && element.cnpj != undefined)
              element.cnpj = this.Editado(element.cnpj);
          });
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, () => {
          this.snackBarAlert.falhaSnackbar("Falha ao carregar tabela!")
          this.spinner.hide();
        })
      }
    } catch (error) {

      this.snackBarAlert.falhaSnackbar("Falha de conexão!")

    }
  }

  CarregarMais() {
    try {
      this.bOcultaCarregaMais = false

      if (this.inativos == false) {
        this.clinicaService.getGridClinica(this.DadosTab.length, this.qtdRegistros, this.pesquisa).subscribe((retorno) => {
          var dados = retorno;

          for (let index = 0; index < dados.length; index++) {
            this.DadosTab.push(dados[index]);
          }
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, err => {
          console.error(err)
          this.spinner.hide();
        })
      }
      else {
        this.clinicaService.getGridClinicaInativos(this.DadosTab.length, this.qtdRegistros, this.pesquisa).subscribe((retorno) => {
          var dados = retorno;

          for (let index = 0; index < dados.length; index++) {
            this.DadosTab.push(dados[index]);
          }
          if (retorno.length < this.qtdRegistros)
            this.bOcultaCarregaMais = true;
          this.spinner.hide();
        }, err => {
          console.error(err)
          this.spinner.hide();
        })
      }
    } catch (error) {

      this.snackBarAlert.falhaSnackbar("Falha de conexão!")
      this.spinner.hide();

    }

  }



  Editado(ao_cnpj: any) {

    ao_cnpj = ao_cnpj.replace(/\D/g, "");
    ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
    ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
    ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
    ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");

    return ao_cnpj;
  }


  public editClinica(id: any) {
    if (id != "" && id != 0) {
      this.localStorageService.idClinica = id;
      this.router.navigate(['/cadastroclinica']);
    }
  }

  // ErroCarregar(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELAPESQUISACLINICA.ERROAOCARREGAR').subscribe((res: string) => {

  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }



  AtivarUsuario() {
    try {
      //remover id
      this.clinicaService.AtivarClinica(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {

        if (retorno == true) {

          this.tradutor.get('TELAPESQUISACLINICA.SUCESSOAOATIVAR').subscribe(() => {

            this.ngxSmartModalService.getModal('ativarItem').close();
            this.snackBarAlert.sucessoSnackbar("Usuário ativado!")
            this.CarregaTable();
          });
          this.spinner.hide();
        }
        else {

          this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe(() => {

            ;
            this.snackBarAlert.falhaSnackbar("Erro ao ativar usuário!")
          });
        }
        this.spinner.hide();
      }, () => {

        this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe(() => {
          this.snackBarAlert.falhaSnackbar("Falha na conexão!")
        });
        this.spinner.hide();
      })

    } catch (error) {

      this.tradutor.get('TELAPESQUISACLINICA.ERROAOATIVAR').subscribe(() => {

        ;
        this.snackBarAlert.falhaSnackbar("Falha na conexão!")
      });
    }
  }

  InativarUsuario() {
    try {

      //remover id
      this.clinicaService.inativarClinica(this.idUsuarioDelet, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
        if (retorno == true) {
          this.ngxSmartModalService.getModal('excluirItem').close();

          this.tradutor.get('TELAPESQUISACLINICA.CLINICACOMSUCESSO').subscribe(() => {

            this.snackBarAlert.sucessoSnackbar("Usuário inativado!")
            this.CarregaTable();
            this.spinner.hide();
          });
        }
        else {

          this.tradutor.get('TELAPESQUISACLINICA.ERROAOINATIVAR').subscribe(() => {

            ;
            this.snackBarAlert.falhaSnackbar("Falha ao inativar usuário!")
            this.spinner.hide();
          });
        }
        this.spinner.hide();
      }, err => {
        console.error(err)
        this.spinner.hide();
      })
    } catch (error) {

      this.tradutor.get('TELAPESQUISACLINICA.ERROAOINATIVAR').subscribe(() => {

        this.snackBarAlert.falhaSnackbar("Falha na conexão!")
        this.spinner.hide();
      });
    }

  }
  public ValorUsuarioAtivar(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('ativarItem').open();
  }

  public ValorUsuario(value: any) {
    this.idUsuarioDelet = 0
    this.idUsuarioDelet = value;
    this.ngxSmartModalService.getModal('excluirItem').open();
  }


  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }


  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.DadosTab[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.DadosTab[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index: any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.DadosTab[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.DadosTab[this.indexGlobal]['toggle'] = !this.DadosTab[this.indexGlobal]['toggle'];
  }

}
