import { Component, Inject, OnInit, ViewChild, ElementRef } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

export interface FileModalData {
  arquivo?: string;
  nomeArquivo?: string;
  tipoArquivo?: string;
  flgEditaArquivo?: boolean;  // Permite edição do arquivo
  flgRemocaoArquivo?: boolean; // Permite remoção do arquivo
}

export interface FileModalResult {
  acao: 'salvar' | 'excluir' | 'cancelar';
  arquivo?: string;
  nomeArquivo?: string;
  tipoArquivo?: string;
  tamanhoArquivo?: number;
}

@Component({
  selector: 'app-modal-arquivo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule
  ],
  templateUrl: './modal-arquivo.component.html',
  styleUrl: './modal-arquivo.component.scss'
})
export class ModalArquivoComponent implements OnInit {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  
  temArquivo: boolean = false;
  modoUpload: boolean = false;
  dragOverActive: boolean = false;
  uploading: boolean = false;
  erro: boolean = false;
  mensagemErro: string = '';
  arquivoSelecionado: File | null = null;
  
  constructor(
    public dialogRef: MatDialogRef<ModalArquivoComponent>,
    @Inject(MAT_DIALOG_DATA) public data: FileModalData,
    private sanitizer: DomSanitizer
  ) { }

  ngOnInit(): void {
    this.temArquivo = !!this.data.arquivo;
    
    // Inicia em modo upload se não tiver arquivo
    if (!this.temArquivo) {
      this.modoUpload = true;
    }
  }

  // Eventos de arrastar e soltar
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    this.dragOverActive = true;
  }

  onDragLeave(): void {
    this.dragOverActive = false;
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    this.dragOverActive = false;

    if (event.dataTransfer?.files.length) {
      this.processarArquivo(event.dataTransfer.files[0]);
    }
  }

  // Seleção de arquivo
  selecionarArquivo(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files?.length) {
      this.processarArquivo(input.files[0]);
    }
  }

  // Processamento do arquivo
  processarArquivo(arquivo: File): void {
    const tiposPermitidos = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'image/jpeg',
      'image/png'
    ];
    
    if (!tiposPermitidos.includes(arquivo.type)) {
      this.erro = true;
      this.mensagemErro = 'Formato não suportado. Use PDF, DOC, DOCX, XLS, XLSX, JPG ou PNG.';
      return;
    }
    
    if (arquivo.size > 5 * 1024 * 1024) {
      this.erro = true;
      this.mensagemErro = 'Arquivo muito grande. Tamanho máximo: 5MB.';
      return;
    }

    this.arquivoSelecionado = arquivo;
    this.erro = false;
    this.converterParaBase64(arquivo);
  }

  converterParaBase64(arquivo: File): void {
    this.uploading = true;
    const reader = new FileReader();

    reader.onload = (e: ProgressEvent<FileReader>) => {
      if (e.target?.result) {
        const base64String = (e.target.result as string).split(',')[1];
        this.data.arquivo = base64String;
        this.data.nomeArquivo = arquivo.name;
        this.data.tipoArquivo = arquivo.type;
        this.temArquivo = true;
        this.modoUpload = false;
        this.uploading = false;
      }
    };

    reader.onerror = () => {
      this.erro = true;
      this.mensagemErro = 'Erro ao processar o arquivo. Tente novamente.';
      this.uploading = false;
    };

    reader.readAsDataURL(arquivo);
  }

  // Ações de botões
  iniciarEdicao(): void {
    this.modoUpload = true;
  }

  cancelar(): void {
    if (this.modoUpload && this.temArquivo) {
      // Volta para visualização se já tinha arquivo
      this.modoUpload = false;
    } else {
      // Fecha a modal
      this.dialogRef.close({ acao: 'cancelar' });
    }
  }

  salvar(): void {
    if (!this.temArquivo) {
      return;
    }

    const resultado: FileModalResult = {
      acao: 'salvar',
      arquivo: this.data.arquivo,
      nomeArquivo: this.data.nomeArquivo,
      tipoArquivo: this.data.tipoArquivo,
      tamanhoArquivo: this.arquivoSelecionado?.size
    };

    this.dialogRef.close(resultado);
  }

  excluir(): void {
    if (confirm('Tem certeza que deseja excluir este arquivo?')) {
      this.dialogRef.close({ acao: 'excluir' });
    }
  }

  // Auxiliares para visualização
  ehImagem(): boolean {
    return this.data.tipoArquivo?.startsWith('image/') || false;
  }

  obterIconeArquivo(): string {
    if (!this.data.tipoArquivo) return 'insert_drive_file';

    if (this.data.tipoArquivo.startsWith('image/')) {
      return 'image';
    } else if (this.data.tipoArquivo === 'application/pdf') {
      return 'picture_as_pdf';
    } else if (
      this.data.tipoArquivo === 'application/msword' ||
      this.data.tipoArquivo === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ) {
      return 'description';
    } else if (
      this.data.tipoArquivo === 'application/vnd.ms-excel' ||
      this.data.tipoArquivo === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      return 'table_chart';
    }

    return 'insert_drive_file';
  }

  formatarTipoArquivo(tipo?: string): string {
    if (!tipo) return 'Arquivo desconhecido';

    const tipos: { [key: string]: string } = {
      'application/pdf': 'Documento PDF',
      'application/msword': 'Documento Word',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Documento Word',
      'application/vnd.ms-excel': 'Planilha Excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Planilha Excel',
      'image/jpeg': 'Imagem JPEG',
      'image/png': 'Imagem PNG'
    };

    return tipos[tipo] || 'Arquivo';
  }

  obterUrlSegura(): SafeResourceUrl {
    if (!this.data.arquivo || !this.data.tipoArquivo) {
      return this.sanitizer.bypassSecurityTrustResourceUrl('about:blank');
    }

    return this.sanitizer.bypassSecurityTrustResourceUrl(
      'data:' + this.data.tipoArquivo + ';base64,' + this.data.arquivo
    );
  }
}