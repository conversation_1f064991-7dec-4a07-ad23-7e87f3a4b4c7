import { formatDate } from '@angular/common';
import { saveAs } from 'file-saver';

export class Uteis {

  static BaixarBase64EmPDF(arquivoBase64: string, tituloArquivo?: string) {

    const source = `data:application/pdf;base64,${arquivoBase64}`;
    const link = document.createElement('a');

    link.href = source;
    link.download = `${tituloArquivo || 'Arquivo'}.pdf`;
    link.click();
  }

  static BaixarFileEmPDF(file: any, nome: string = "Arquivo") {
    var mediaType = 'application/pdf';
    var blob = new Blob([file], { type: mediaType });
    saveAs(blob, `${nome}.pdf`);
  }
  
}

export function aplicarMascaraTelefone(telefone: string): string {
  if (!telefone) return '';

  // Remove todos os caracteres não numéricos
  let v = telefone.replace(/\D/g, "");

  // Aplica a máscara
  v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
  v = v.replace(/(\d)(\d{4})$/, "$1-$2");

  return v;
}

export function apenasNumeros(valor: string): string {
  
  if (!valor) return '';
  
  return valor.replace(/\D/g, '');
}

export function utilFormataData(data: Date) {
  return formatDate(data, 'dd/MM/yyyy - HH:mm', 'pt');
}

// Calcula idade a partir da data de nascimento
export function calcularIdade(dataNascimento: string | Date): number {
  if (!dataNascimento) return 0;
  
  let nascimento: Date;
  
  if (typeof dataNascimento === 'string') {
    if (dataNascimento.includes('/')) {
      const [dia, mes, ano] = dataNascimento.split('/');
      nascimento = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
    } else {
      nascimento = new Date(dataNascimento);
    }
  } else {
    nascimento = dataNascimento;
  }
  
  // Validação da data
  if (isNaN(nascimento.getTime())) {
    console.error('Data de nascimento inválida:', dataNascimento);
    return 0;
  }
  
  const hoje = new Date();
  let idade = hoje.getFullYear() - nascimento.getFullYear();
  const mesAtual = hoje.getMonth();
  const mesNascimento = nascimento.getMonth();
  
  if (mesAtual < mesNascimento || (mesAtual === mesNascimento && hoje.getDate() < nascimento.getDate())) {
    idade--;
  }
  
  return idade;
}
