export class objChamadoAtendimento {
    idChamadoAtendimento?: number | null;

    idMedico?: number | null;
    nomeMedico: string = "";

    idPaciente?: number | null;
    nomePaciente: string = "";

    idSala?: number | null;
    descSala: string = "";
    numeroSala?: number | null;

    idClinica?: number | null;
    idTipoAtendimento?: number | null;

    dtChamado?: Date | null;

    flgInativo?: boolean | null;
}

export class objNovoAtendimento {
    idConsulta?           : number | null;
    idSala?              : number | null;
    idTipoAtendimento?   : number | null;
}
export const ListaConsultasDeTESTE: objChamadoAtendimento[] = [
    { idChamadoAtendimento: 1, idMedico: 1, nomeMedico: 'Doutor Teste', idPaciente: 1, nomePaciente: "Paciente Teste", idSala: 1, numeroSala: 1, descSala: "Sala Teste", idClinica: 42, idTipoAtendimento: 1, dtChamado: new Date(), flgInativo: false },
    { idChamadoAtendimento: 2, idMedico: 2, nomeMedico: 'Doutor Teste', idPaciente: 2, nomePaciente: "Paciente Teste", idSala: 2, numeroSala: 2, descSala: "Sala Teste", idClinica: 42, idTipoAtendimento: 2, dtChamado: new Date(), flgInativo: false },
    { idChamadoAtendimento: 3, idMedico: 3, nomeMedico: 'Doutor Teste', idPaciente: 3, nomePaciente: "Paciente Teste", idSala: 3, numeroSala: 3, descSala: "Sala Teste", idClinica: 42, idTipoAtendimento: 3, dtChamado: new Date(), flgInativo: false },
    { idChamadoAtendimento: 4, idMedico: 4, nomeMedico: 'Doutor Teste', idPaciente: 4, nomePaciente: "Paciente Teste", idSala: 4, numeroSala: 4, descSala: "Sala Teste", idClinica: 42, idTipoAtendimento: 4, dtChamado: new Date(), flgInativo: false },
    { idChamadoAtendimento: 5, idMedico: 5, nomeMedico: 'Doutor Teste', idPaciente: 5, nomePaciente: "Paciente Teste", idSala: 5, numeroSala: 5, descSala: "Sala Teste", idClinica: 42, idTipoAtendimento: 5, dtChamado: new Date(), flgInativo: false }
]

export class objTipoAtendimento
{
    idTipoAtendimento?: number;
    descAtendimento?:string;
    idClinica?: number;
}