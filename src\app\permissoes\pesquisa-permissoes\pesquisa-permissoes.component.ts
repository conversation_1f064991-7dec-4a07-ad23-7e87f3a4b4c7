import { AlertComponent } from './../../alert/alert.component';
import { Component, OnInit } from '@angular/core';
import { PermissoesService } from 'src/app/service/permissoes.service';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { UsuarioLogadoService } from 'src/app/auth/usuarioLogado.service';
import { LocalStorageService } from 'src/app/service/LocalStorageService';
import { trigger, state, transition, animate, style } from '@angular/animations';
import { SpinnerService } from 'src/app/service/spinner.service';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { MatDivider } from '@angular/material/divider';
import { MatInputModule } from '@angular/material/input';


@Component({
    selector: 'app-pesquisa-permissoes',
    templateUrl: './pesquisa-permissoes.component.html',
    styleUrls: ['./pesquisa-permissoes.component.scss'],
    animations: [trigger('openClose', [
            state('open', style({
                opacity: '1',
                display: 'block'
            })),
            state('closed', style({
                opacity: '0',
                display: 'none'
            })),
            transition('open => closed', [
                animate('0.2s')
            ]),
            transition('closed => open', [
                animate('0.2s')
            ]),
        ])
    ],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatCardModule,
      MatIcon,
      MatFormFieldModule,
      TranslateModule,
      MatDivider,
      RouterModule,
      NgxSmartModalModule
    ]
})
export class PesquisaPermissoesComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private permissoesService: PermissoesService,
    public ngxSmartModalService: NgxSmartModalService,
    // public snackBar: MatSnackBar,
    private snackBarAlert: AlertComponent,
    private router: Router,
    private usuarioLogadoService: UsuarioLogadoService,
    private localStorageService: LocalStorageService,
  ) { }

  // actionButtonLabel: string = 'Fechar';
  // action: boolean = true;
  // setAutoHide: boolean = true;
  // autoHide: number = 6000;
  // horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  // verticalPosition: MatSnackBarVerticalPosition = 'bottom';

  // usuario: Usuario;

  bOcultaCarregaMais = false;

  inicio: number = 0;
  fim: number = 10;
  pesquisa: string = ''
  listaPerfil:any = [];

  idExclusao?: number | null;
  legenda = false;


  ngOnInit() {
    this.GetListaPerfil()
  }




  // SnackMensagem(value, message) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     this.snackBar.open(message, this.action ? this.actionButtonLabel : undefined, config);
  //   }

  // }

  GetListaPerfil() {

    this.permissoesService.GetListaPerfil(this.inicio, this.fim, this.usuarioLogadoService.getIdUltimaClinica(), this.usuarioLogadoService.getIdUsuarioAcesso(), this.pesquisa).subscribe((retorno) => {

      this.listaPerfil = retorno;

      if (retorno.length < this.fim)
        this.bOcultaCarregaMais = true;


      
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })

  }

  CarregarMais() {
    this.bOcultaCarregaMais = false;
    this.permissoesService.GetListaPerfil(this.listaPerfil.length, this.fim, this.usuarioLogadoService.getIdUltimaClinica(), this.usuarioLogadoService.getIdUsuarioAcesso(), this.pesquisa).subscribe((retorno) => {

      var dados = retorno;
      for (let index = 0; index < dados.length; index++) {
        this.listaPerfil.push(dados[index]);
      }
      if (retorno.length < this.fim)
        this.bOcultaCarregaMais = true;
      this.spinner.hide();
    }, () => {
      this.spinner.hide();
    })
  }

  EditarPerfil(idPerfil:any) {

    
    if (idPerfil != "" && idPerfil != 0) {
      this.localStorageService.idPerfil = idPerfil;
      this.router.navigate(['/permissoes']);
    }
  }

  ExcluirPerfil(idPerfil:any) {
    this.idExclusao = null;
    this.idExclusao = idPerfil;
    this.ngxSmartModalService.getModal('excluirPerfil').open();

  }

  InativarPerfil() {

    this.permissoesService.InativarPerfil(this.idExclusao, this.usuarioLogadoService.getIdUsuarioAcesso()).subscribe((retorno) => {
      
      if (retorno) {
        this.ngxSmartModalService.getModal('excluirPerfil').close();
        this.GetListaPerfil()
        this.snackBarAlert.sucessoSnackbar("Perfil Excluido!")

      } else {
        this.snackBarAlert.falhaSnackbar("Erro ao Excluir")
      }
      this.spinner.hide();

    }, () => {
      this.spinner.hide();
    })
  }
  toggle:any = {}
  CarregaTable() { }

  isOpen = false;
  toogleBotoesMobile = false;

  fecharBotoesMobile() {
    if (!this.isOpen) {
      if (this.listaPerfil[this.indexGlobal]['toggle']) {
        this.toggle[this.indexGlobal] = false;
        this.listaPerfil[this.indexGlobal]['toggle'] = false;
      }
    }


    this.isOpen = false;
  }

  indexGlobal: number = 0;
  openToggle(index:any) {

    if (this.indexGlobal != index) {
      this.toggle[this.indexGlobal] = false;
      this.listaPerfil[this.indexGlobal]['toggle'] = false;
    }


    this.indexGlobal = parseInt(index);
    this.isOpen = true;
    this.toggle[this.indexGlobal] = !this.toggle[this.indexGlobal];
    this.listaPerfil[this.indexGlobal]['toggle'] = !this.listaPerfil[this.indexGlobal]['toggle'];
  }

}
