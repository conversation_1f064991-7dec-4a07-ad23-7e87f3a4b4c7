import { Component, OnInit, ViewChild } from '@angular/core';

import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { CidadeService } from '../service/cidade.service';
import { FormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Clinica } from '../model/clinica';
import { Endereco } from '../model/endereco';
import { Contato } from '../model/contato';
import { ClinicaService } from '../service/clinica.service';
import { MatSnackBarVerticalPosition as MatSnackBarVerticalPosition, MatSnackBarHorizontalPosition as MatSnackBarHorizontalPosition} from '@angular/material/snack-bar';
import { ImageCropperComponent, ImageCroppedEvent, ImageTransform, ImageCropperModule } from 'ngx-image-cropper';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { ValidadoreseMascaras } from '../Util/validadores';
import { UsuarioService } from '../service/usuario.service';
import { ResponsavelClinicaService } from '../service/ResponsavelClinica.service';
import { PessoaResponsavel } from '../model/pessoaResponsavel';
import { LocalStorageService } from '../service/LocalStorageService';
import { UfClass } from '../Util/UFClass';
import { SpinnerService } from '../service/spinner.service';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { NgSelectModule } from '@ng-select/ng-select';
import { MatInputModule } from '@angular/material/input';
import { MatDividerModule } from '@angular/material/divider';
import { CalendarDateFormatter } from 'angular-calendar';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';


type CalendarPeriod = 'da../Util/UFClass' | 'month';

// function subPeriod(period: CalendarPeriod, date: Date, amount: number): Date {
//   return {
//     day: subDays,
//     week: subWeeks,
//     month: subMonths
//   }[period](date, amount);
// }

@Component({
    selector: 'app-cadastro-clinica',
    templateUrl: './cadastro-clinica.component.html',
    styleUrls: ['./cadastro-clinica.component.scss'],
    providers: [CalendarDateFormatter],
    standalone:true,
    imports: [
      MatInputModule,
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      MatCardModule,
      MatIcon,
      MatCardModule,
      MatFormFieldModule,
      NgSelectModule,
      NgxSmartModalModule,
      ImageCropperModule,
      MatDividerModule,
      MatDividerModule,
      MatSlideToggleModule
    ]
})
export class CadastroClinicaComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private cidadeService: CidadeService,
    private clinicaService: ClinicaService,
    // public snackBar: MatSnackBar,
    public ngxSmartModalService: NgxSmartModalService,
    private tradutor: TranslateService,
    private usuarioLogadoService: UsuarioLogadoService,
    private validacao: ValidadoreseMascaras,
    private usuarioService: UsuarioService,
    private responsavelClinicaService: ResponsavelClinicaService,
    private localStorageService: LocalStorageService,
    private snackBarAlert: AlertComponent,
  ) { }


  @ViewChild(ImageCropperComponent) imageCropper!: ImageCropperComponent;
  actionButtonLabel: string = 'Fechar';
  salvoSucess: string = 'Cadastro salvo com Sucesso. ✔';
  ErroSalvar: string = 'Erro ao salvar!';
  action: boolean = true;
  setAutoHide: boolean = true;
  autoHide: number = 6000;
  horizontalPosition: MatSnackBarHorizontalPosition = 'right';
  verticalPosition: MatSnackBarVerticalPosition = 'bottom';
  concordo?: boolean;
  concordomsg?: boolean;

  imageChangedEvent: any = '';
  croppedImage: any = '';

  showCropper = false;
  imagemCorte: any;
  FlgProntuario: boolean = false;
  FlgFilaEspera: boolean = false;
  FlgSolicitarOrientacao: boolean = false;
  FlgHabilitaChat: boolean = false;
  FlgHabilitaPagamento: boolean = false;
  campoEmailInvalido = false;
  mensagemErroEmail = ""

  view: CalendarPeriod = 'month';
  viewDate: Date = new Date();
  ImagemClinica: any = "assets/build/img/logo-clinica.png";
  fileToUpload?: File;
  showMessageError = false;
  showMessageSuccess = false;
  Dados: any;
  dadosUF = UfClass;
  dadosUFCarregaBanco: any;
  dadosCidade: any;
  dadosCidadeUf: any;
  errors = [];
  DadosClinica: any;
  // usuario: Usuario;
  tipoClinica: any;
  retornoEndereco: any = [];
  retornoContato: any = [];
  retornoClinica: any = [];
  Dtalice?: boolean;
  TelVal?: boolean;
  TelMovVal?: boolean;
  TelComInvalido?: boolean;
  TelComVasil = false;
  TelComLimpa?: boolean;

  campoCNPLInvalido: boolean = false;
  campoCPFVazil: boolean = false;
  campoCPFInvalido: boolean = false;


  gridResponsavelClinica: PessoaResponsavel[] = [];
  idUsuarioExistente: any;
  mensagemPessoa?: string;
  campoExitente?: string;
  TelMovValResponsavelVasil:any
  TelMovValResponsavel:any
  TelMovRespLimpa:any
  campoCpfResponsavelInvalido: boolean = false;
  campoCpfResponsavelVazil: boolean = false;
  campoEmailResponsavelInvalido = false;
  campoEmailResponsavelVazil = false;
  retornoResponsavel:any;
  showMessageErrorResp: boolean = false;

  flgEdicaoResponsavelGrid: boolean = false
  indexResponsavelGrid:any;
  pessoaResponsavel?: PessoaResponsavel;
  camposResponsaveisAtivos: boolean = false;
  
  ngOnInit() {
    this.pessoaResponsavel = new PessoaResponsavel();
    this.Dados = [];
    let idClinica = this.localStorageService.idClinica;
    this.localStorageService.clearByName("idClinica")

    if (idClinica) {
      this.CarregaAtendente(idClinica);
      this.CarregarGridResponsaveis(idClinica)
    }
  }



  public mascaraText(evento: KeyboardEvent, campo: string) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\d/g, "");
    if (campo == 'nome')
      this.Dados.nome = v;
    if (campo == 'NomeResponsavel')
      this.pessoaResponsavel!.nome = v;
    if (campo == 'caracterizacao')
      this.Dados.caracterizacao = v;

    (<HTMLInputElement>evento.target).value = v
  }

  public mascaraNumeros(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");

    (<HTMLInputElement>evento.target).value = v
  }

  public CidadePorUF() {
    try {

      this.cidadeService.getCidades().then((retornaCidade) => {
        this.dadosCidade = retornaCidade;
        this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == this.Dados.uf);
        this.spinner.hide();
      }, () => {
        this.snackBarAlert.infoSnackbar('Erro ao carregar Cidade')
      })
      this.spinner.hide();

    } catch (error) {
      this.snackBarAlert.falhaSnackbar("falha na conexão!")
      this.spinner.hide();

    }
  }

  public mascaraData(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;;
    v = v.replace(/\D/g, "");                    //Remove tudo o que não é dígito
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    v = v.replace(/(\d{2})(\d)/, "$1/$2");       //Coloca um ponto entre o terceiro e o quarto dígitos
    //de novo (para o segundo bloco de números)

    (<HTMLInputElement>evento.target).value = v;
  }


  ValidaDtaChange() {
    const dta = (document.getElementById('dataLicenca') as HTMLInputElement)['value'];
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '') {
      this.Dtalice = false;
    }
    else if (patternValidaData.test(dta)) {
      this.Dtalice = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtalice = true;
    }
    else
      this.Dtalice = false
  }

  ValidaDta(dta:any) {
    var patternValidaData = /^(((0[1-9]|[12][0-9]|3[01])([-.\/])(0[13578]|10|12)([-.\/])(\d{4}))|(([0][1-9]|[12][0-9]|30)([-.\/])(0[469]|11)([-.\/])(\d{4}))|((0[1-9]|1[0-9]|2[0-8])([-.\/])(02)([-.\/])(\d{4}))|((29)(\.|-|\/)(02)([-.\/])([02468][048]00))|((29)([-.\/])(02)([-.\/])([13579][26]00))|((29)([-.\/])(02)([-.\/])([0-9][0-9][0][48]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][2468][048]))|((29)([-.\/])(02)([-.\/])([0-9][0-9][13579][26])))$/;
    if (dta == '')
      this.Dtalice = false;


    else if (patternValidaData.test(dta)) {
      this.Dtalice = false;
    }
    else if (!patternValidaData.test(dta)) {
      this.Dtalice = true;
    }
    else
      this.Dtalice = false
  }

  ValidaTelefone(tle:any) {
    var patternValidaTel = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelVal = false;
    }
    else if (patternValidaTel.test(tle)) {
      this.TelVal = false;
    }
    else if (!patternValidaTel.test(tle)) {
      this.TelVal = true;
    }
    else
      this.TelVal = false
  }

  ValidaTelefoneMovel(tle:any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelMovVal = false;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovVal = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovVal = true;
    }
    else
      this.TelMovVal = false
  }

  ValidaTelefoneComercial(tle:any) {
    var patternValidaTelCom = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelComVasil = true;
      this.TelComInvalido = false;
    }
    else if (patternValidaTelCom.test(tle)) {
      this.TelComVasil = false;
      this.TelComInvalido = false;
    }
    else if (!patternValidaTelCom.test(tle)) {
      this.TelComInvalido = true;
      this.TelComVasil = false;
    }
    else
      this.TelComInvalido = false
  }


  public mascaraTelefone(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;

    v = v.replace(/\D/g, "");
    v = v.replace(/^(\d{2})(\d)/g, "($1) $2");
    v = v.replace(/(\d)(\d{4})$/, "$1-$2");

    (<HTMLInputElement>evento.target).value = v
  }
  mascaravindoBanco(item:any) {


    if (item.length > 14) {


      var ao_cnpj = item;

      ao_cnpj = ao_cnpj.replace(/\D/g, "");
      ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
      ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
      ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");

      return ao_cnpj;
    }
    else {

      var ao_cpf = item;

      ao_cpf = ao_cpf.replace(/\D/g, "");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
      ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

      return ao_cpf;
    }
  }

  public mascaraCnpj(mascaraCNPJ:any, mascaraCPF:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 18) {

      if (i > 14) {
        var saida = mascaraCNPJ.substring(0, 1);
        var texto = mascaraCNPJ.substring(i);

        if (texto.substring(0, 1) != saida) {
          return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
        }

        var ao_cnpj = valorEvento;

        ao_cnpj = ao_cnpj.replace(/\D/g, "");
        ao_cnpj = ao_cnpj.replace(/(\d{2})(\d)/, "$1.$2");
        ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cnpj = ao_cnpj.replace(/(\d{3})(\d)/, "$1/$2");
        ao_cnpj = ao_cnpj.replace(/(\d{4})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cnpj;
      }
      else {
        var saida = mascaraCPF.substring(0, 1);
        var texto = mascaraCPF.substring(i);

        if (texto.substring(0, 1) != saida) {
          return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
        }
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }

  public mascaraIss(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 11) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 8) {
        var ao_iss = valorEvento;

        ao_iss = ao_iss.replace(/\D/g, "");
        ao_iss = ao_iss.replace(/(\d{1})(\d)/, "$1.$2");
        ao_iss = ao_iss.replace(/(\d{3})(\d)/, "$1.$2");
        ao_iss = ao_iss.replace(/(\d{3})(\d{1})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_iss;
      }
    }
  }

  public mascaraCrm(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 7) {
      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 7) {
        var ao_crm = valorEvento;

        ao_crm = ao_crm.replace(/\D/g, "");

        return (<HTMLInputElement>evento.target).value = ao_crm;
      }
    }
  }

  LimparCampos() {
    this.Dados = [];
    this.ImagemClinica = "assets/build/img/logo-clinica.png";
    (document.getElementById("dataLicenca") as HTMLInputElement)['value'] = ""
    this.Nome.markAsUntouched();
    this.cnpj.markAsUntouched();
    this.email.markAsUntouched();
    this.Dtalice = false;
    this.TelVal = false;
    this.TelMovVal = false;
    this.TelComInvalido = false;
    this.TelComVasil = false;
    this.TelComLimpa = false;
    this.FlgProntuario = false;
    this.FlgFilaEspera = false;
    this.FlgSolicitarOrientacao = false;
    this.FlgHabilitaChat = false;
    this.FlgHabilitaPagamento = false;
    this.retornoClinica = null;
    this.campoEmailInvalido = false;
    this.campoCPFInvalido = false;
    this.campoCNPLInvalido = false;
    this.campoCPFVazil = false;

    this.LimparCamposResponsavel()
    this.gridResponsavelClinica = [];
    this.camposResponsaveisAtivos = false;
  }
  public MascaraDinheiro(evento: KeyboardEvent) {

    var v = (<HTMLInputElement>evento.target).value;
    var v = v, integer = v.split('.')[0];
    v = v.replace(/\D/g, "");
    v = v.replace(/^[0]+/, "");

    if (v.length <= 2 || !integer) {
      if (v.length === 1) v = '0.0' + v;
      if (v.length === 2) v = '0.' + v;
    } else {
      v = v.replace(/^(\d{1,})(\d{2})$/, "$1.$2");
    }
    this.Dados.valorConsulta = v
  }

  public validarCampos() {
    this.showMessageError = false;
    this.Nome.markAsTouched();
    this.cnpj.markAsTouched();
    this.email.markAsTouched();
    this.ValidaDtaChange();
    this.ValidarEmail(this.Dados.email)
    this.ValidaCPFCNPJ(this.Dados.cnpj)

    this.ValidaTelefoneComercial((document.getElementById('TelefoneComercial') as HTMLInputElement)['value']);

    if (this.Dados.cnpj == undefined || !this.Dados.cnpj.trim())
      this.campoCPFVazil = true;

    if (this.Dados.nome == undefined || !this.Dados.nome.trim()
      || this.Dados.cnpj == undefined || !this.Dados.cnpj.trim()
      || this.Dtalice == true || this.TelMovVal == true
      || this.TelVal == true || this.TelComInvalido == true || this.TelComVasil == true
      || this.campoCNPLInvalido == true || this.campoCPFInvalido == true
      || this.campoEmailInvalido == true) {

      this.showMessageError = true;

      if (this.TelComInvalido != true && this.TelComInvalido != false || this.TelComLimpa == false) {
        this.TelComVasil = true;
        this.TelComLimpa = true;
      }
      document.documentElement.scrollTop = 0;
    }

  }
  Nome = new FormControl('', [Validators.required, Validators.required]);
  email = new FormControl('', [Validators.required, Validators.email]);
  cnpj = new FormControl('', [Validators.required, Validators.maxLength(18)])
  nomeResponsavel = new FormControl('', [Validators.required, Validators.required]);
  emailResponsavel = new FormControl('', [Validators.required, Validators.email]);

  getErrorMessageNome() {
    this.showMessageSuccess = true;
    return this.Nome.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.Nome.hasError('Nome') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
        '';

  }

  getErrorMessageNomeResponsavel() {
    this.showMessageSuccess = true;
    return this.nomeResponsavel.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.nomeResponsavel.hasError('nomeResponsavel') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
        '';

  }

  // getErrorMessageMat() {
  //   return this.mat.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
  //     this.mat.hasError('Matrícula') ? 'TELACADASTROCLINICA.ERRONAOEVALIDA' :
  //       '';

  // }


  getErrorMessageEmail() {
    return this.email.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.email.hasError('Email') ? 'TELACADASTROCLINICA.ERROEMAILNAOEVALIDO' :
        '';

  }
  getErrorMessageCNPJ() {
    return this.cnpj.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
      this.cnpj.hasError('CNPJ') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
        '';

  }
  // getErrorMessageISS() {
  //   return this.iss.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
  //     this.iss.hasError('ISS') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
  //       '';

  // }
  // getErrorMessageCRM() {
  //   return this.crm.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
  //     this.crm.hasError('CRM') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
  //       '';

  // }
  // getErrorMessageCNES() {
  //   return this.cnes.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
  //     this.cnes.hasError('CNES') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
  //       '';

  // }

  // getErrorMessageWebsite() {
  //   return this.website.hasError('required') ? 'TELACADASTROCLINICA.ERROCAMPO' :
  //     this.website.hasError('Website') ? 'TELACADASTROCLINICA.ERRONAOEVALIDO' :
  //       '';
  // }




  public CarregaAtendente(id:any) {
    try {
      this.clinicaService.getClinicaEdit(id).subscribe((retorno) => {
        
        

        this.retornoEndereco = retorno.endereco;
        this.retornoContato = retorno.contato;
        this.retornoClinica = retorno.clinica;

        this.Dados.nome = this.retornoClinica.desClinica;
        this.Dados.dataLicenca = this.retornoClinica.dtaLicenca ? new Date(this.retornoClinica.dtaLicenca).toLocaleDateString() : '';
        this.Dados.caracterizacao = this.retornoClinica.caracterizacao;
        this.Dados.cnpj = this.mascaravindoBanco(this.retornoClinica.cnpj);
        this.Dados.iss = this.retornoClinica.issCcm;
        this.Dados.crm = this.retornoClinica.crm;
        this.Dados.cnes = this.retornoClinica.cnes;


        if (retorno.clinica.valorConsulta) {
          // this.Dados.valorConsulta = retorno.valorConsulta.replace(/,/g, ".");
          retorno.clinica.valorConsulta = this.verificaCasaDecimal(retorno.clinica.valorConsulta)
          retorno.clinica.valorConsulta = this.aplicarMascaraValor(retorno.clinica.valorConsulta)
          this.Dados.valorConsulta = retorno.clinica.valorConsulta;
        }


        // this.Dados.valorConsulta  = this.retornoClinica.valorConsulta;
        this.Dados.website = this.retornoContato.site;
        this.FlgProntuario = this.retornoClinica.flgSomenteProntuario;
        this.FlgFilaEspera = this.retornoClinica.flgFilaEspera;
        this.FlgSolicitarOrientacao = this.retornoClinica.flgSolicitarOrientacao;
        this.FlgHabilitaChat = this.retornoClinica.flgHabilitaChat;
        this.FlgHabilitaPagamento = this.retornoClinica.flgHabilitaPagamento;

        // this.Dados.sexo = this.retornoPaciente.sexo;

        if (this.retornoContato.telefone != null && this.retornoContato.telefone != '' && this.retornoContato.telefone != undefined) {
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/\D/g, "");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefone = this.retornoContato.telefone.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefone = this.retornoContato.telefone;


        if (this.retornoContato.telefoneMovel != null && this.retornoContato.telefoneMovel != '' && this.retornoContato.telefoneMovel != undefined) {
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/\D/g, "");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneMovel = this.retornoContato.telefoneMovel.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneMovel = this.retornoContato.telefoneMovel;

        if (this.retornoContato.telefoneComercial != null && this.retornoContato.telefoneComercial != '' && this.retornoContato.telefoneComercial != undefined) {
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/\D/g, "");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/^(\d{2})(\d)/g, "($1) $2");
          this.retornoContato.telefoneComercial = this.retornoContato.telefoneComercial.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.Dados.telefoneComercial = this.retornoContato.telefoneComercial;
        // this.ValidaTelefoneComercial(document.getElementById('TelefoneComercial')['value']);
        this.Dados.email = this.retornoContato.email;
        this.Dados.site = this.retornoContato.site;

        this.Dados.rua = this.retornoEndereco.rua
        this.Dados.numero = this.retornoEndereco.numero
        this.Dados.complemento = this.retornoEndereco.complemento
        this.Dados.cep = this.retornoEndereco.cep
        this.Dados.bairro = this.retornoEndereco.bairro
        this.Dados.logo = this.retornoClinica.logo;

        if (retorno.imagem64 != null && retorno.imagem64 != "")
          this.ImagemClinica = retorno.imagem64;


        if (this.retornoEndereco.idCidade != null) {
          this.cidadeService.getCidades().then((retornaCidade) => {

            this.dadosCidade = retornaCidade
            var sigle = this.dadosCidade.filter((c:any) => c.idCidade == this.retornoEndereco.idCidade)
            this.Dados.uf = sigle[0].siglasUf

            this.dadosCidadeUf = this.dadosCidade.filter((c:any) => c.siglasUf == sigle[0].siglasUf);
            this.Dados.idCidade = this.retornoEndereco.idCidade
            this.spinner.hide();
          }, () => {

            this.tradutor.get('TELACADASTROCLINICA.ERRONORETORNOPORUF').subscribe(() => {
              
              console.error("erro no retorno Cidade por uf ");
            });
            this.spinner.hide();
          })
        }


      }, () => {

        this.tradutor.get('TELACADASTROCLINICA.ERROAOCARREGARCLINICA').subscribe(() => {
          

          this.snackBarAlert.infoSnackbar('Erro ao Carregar Clinica!');
        });
      })
    } catch (error) {

      this.tradutor.get('TELACADASTROCLINICA.ERROAOCARREGARCLINICA').subscribe(() => {
        
        this.snackBarAlert.falhaSnackbar("falha na conexão!")
      });
    }
  }


  public ValidaCPFCNPJ(value:any) {
    this.campoCPFInvalido = false;
    this.campoCNPLInvalido = false;
    if (value != "" && value != undefined) {
      this.campoCPFVazil = false;
      // if (value) {
      if (value.length < 14) {
        this.campoCPFInvalido = true;
        return
      }
      if (value.length > 14 && value.length < 18) {
        this.campoCNPLInvalido = true;
        return
      }

      if (value.length < 18) {
        if (!this.validacao.cpf(value)) {
          this.campoCPFInvalido = true;
          return;
        }
      }
      else {
        if (!this.validacao.cnpj(value)) {
          this.campoCNPLInvalido = true;
          return
        }
      }
      // }
    }
    else
      this.campoCPFVazil = true;
  }


  // AlgumErro(value, mensagem) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];
  //     this.snackBar.open(mensagem, this.action ? this.actionButtonLabel : undefined, config);
  //   }
  // }
  ValidarEmail(value:any) {
    if (value == undefined || !value.trim()) {
      this.mensagemErroEmail = "Esse campo precisa ser preenchido"
      this.campoEmailInvalido = true
      return
    } else {
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailInvalido = true;
        this.mensagemErroEmail = "Email inválido"
      }
      else
        this.campoEmailInvalido = false;

      return

    }
  }




  public OnSubmit() {
    this.validarCampos();
    if (this.showMessageError) {
      return;
    }
    var clinica = new Clinica();
    var endereco = new Endereco();
    var contato = new Contato();

    clinica.Logo = this.Dados.logo;
    clinica.DesClinica = this.Dados.nome;
    clinica.DtaLicenca = (document.getElementById('dataLicenca') as HTMLInputElement)['value'] != '' ? 
                          this.validacao.Convertdata((document.getElementById('dataLicenca') as HTMLInputElement)['value']) : null;
    clinica.Caracterizacao = this.Dados.caracterizacao;
    clinica.Cnpj = this.Dados.cnpj;
    clinica.Cnes = this.Dados.cnes;
    clinica.IssCcm = this.Dados.iss;
    clinica.Crm = this.Dados.crm;
    clinica.FlgSomenteProntuario = this.FlgProntuario;
    clinica.FlgFilaEspera = this.FlgFilaEspera;
    clinica.FlgSolicitarOrientacao = this.FlgSolicitarOrientacao;
    clinica.FlgHabilitaChat = this.FlgHabilitaChat;
    clinica.FlgHabilitaPagamento = this.FlgHabilitaPagamento;
    // clinica.valorConsulta = document.getElementById('valorConsulta')['value'];


    if (this.Dados.valorConsulta) {
      this.Dados.valorConsulta = this.validacao.removeMascara(this.Dados.valorConsulta);
      this.Dados.valorConsulta = this.Dados.valorConsulta.replace(/(\d{1})(\d{1,2})$/, "$1.$2");
      clinica.valorConsulta = this.Dados.valorConsulta;
    }

    contato.site = this.Dados.website;
    contato.email = this.Dados.email;
    contato.telefone = this.Dados.telefone;
    contato.telefoneMovel = this.Dados.telefoneMovel;
    contato.telefoneComercial = this.Dados.telefoneComercial;

    endereco.rua = this.Dados.rua;
    endereco.numero = this.Dados.numero;
    endereco.complemento = this.Dados.complemento;
    endereco.idCidade = this.Dados.idCidade;
    endereco.bairro = this.Dados.bairro;
    endereco.cep = this.Dados.cep;

    contato.idUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso()!;
    clinica.IdUsuarioGerador = this.usuarioLogadoService.getIdUsuarioAcesso();

    if (this.ImagemClinica != "assets/build/img/logo-clinica.png")
      clinica.Imagem64 = this.ImagemClinica;

    

    if (this.retornoClinica && this.retornoClinica.idClinica > 0) {
      clinica.idClinica = this.retornoClinica.idClinica;
      clinica.IdEndereco = this.retornoClinica.idEndereco;
      clinica.IdContato = this.retornoClinica.idContato;
      contato.idContato = this.retornoClinica.idContato;
      endereco.idEndereco = this.retornoClinica.idEndereco;
      endereco.dtaCadastro = this.retornoEndereco.dtaCadastro;
      clinica.DtaCadastro = this.retornoEndereco.dtaCadastro;
      contato.dtaCadastro = this.retornoEndereco.dtaCadastro;
    }
    else {
      endereco.dtaCadastro = new Date;
      contato.dtaCadastro = new Date;
    }


    if (this.gridResponsavelClinica.length > 0) {
      if (this.retornoClinica.length == 0) {
        clinica.responsaveis = this.gridResponsavelClinica;
      }
    }
    clinica.endereco = endereco;
    clinica.contato = contato;


    this.clinicaService.salvarClinica(clinica).subscribe((retorno) => {
      

      if (retorno != true) {
        this.snackBarAlert.falhaSnackbar("Falha ao salvar clínica")        
      }
      else {
        this.snackBarAlert.sucessoSnackbar("Clínica salva com sucesso")
        this.DadosClinica = []
        this.LimparCampos()

      }
      this.spinner.hide();

    }, () => {

      this.snackBarAlert.falhaSnackbar("Falha ao salvar clínica")        
      this.spinner.hide();
    })
  }

  // SalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['success-snack'];

  //     this.tradutor.get('TELACADASTROCLINICA.CADASTROSLAVOCOMSUCESSO').subscribe((res: string) => {
        
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }


  // ErroSalvarCadastro(value) {
  //   if (value == true) {
  //     let config = new MatSnackBarConfig();
  //     config.verticalPosition = this.verticalPosition;
  //     config.horizontalPosition = this.horizontalPosition;
  //     config.duration = this.setAutoHide ? this.autoHide : 0;
  //     config.panelClass = ['error-snack'];

  //     this.tradutor.get('TELACADASTROCLINICA.ERROAOSALVAR').subscribe((res: string) => {
        
  //       this.snackBar.open(res, this.action ? this.actionButtonLabel : undefined, config);
  //     });
  //   }
  // }


  AlterarImagemClinica($event:any): void {
    this.ngxSmartModalService.getModal('ModalFoto').open();
    this.imageChangedEvent = $event
    // this.readThis($event.target);
  }

  LimpaCampoFile() {
    (document.getElementById('imageperfilClinica') as HTMLInputElement)['value'] = '';
  }

  CortarImagem() {
    this.ngxSmartModalService.getModal('ModalFoto').close()
    this.ImagemClinica = this.imagemCorte
  }

  imageCropped(event: ImageCroppedEvent) {
    this.imagemCorte = event.base64;
    
  }
  imageLoaded() {
    this.showCropper = true;
    
  }
  cropperReady() {
    
  }
  loadImageFailed() {
    
  }
  transform: ImageTransform = {
    rotate: 0,
    flipH: false,
    flipV: false
  };

  rotateLeft() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! - 90 };
  }

  rotateRight() {
    this.transform = { ...this.transform, rotate: this.transform.rotate! + 90 };
  }

  flipHorizontal() {
    this.transform = { ...this.transform, flipH: !this.transform.flipH };
  }

  flipVertical() {
    this.transform = { ...this.transform, flipV: !this.transform.flipV };
  }

  aplicarMascaraValor(v:any) {
    v = v.toString().replace(/\D/g, "");
    v = v.toString().replace(/(\d{1})(\d{14})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{11})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{8})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{5})$/, "$1.$2");
    v = v.toString().replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    return v;
  }

  public mascaraValor(evento: KeyboardEvent) {
    var v = (<HTMLInputElement>evento.target).value;
    v = v.replace(/\D/g, "")
    v = v.replace(/(\d{1})(\d{14})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{11})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{8})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{5})$/, "$1.$2")
    v = v.replace(/(\d{1})(\d{1,2})$/, "$1,$2");
    (<HTMLInputElement>evento.target).value = v
  }

  verificaCasaDecimal(valor:any) {
    if (valor.toString().split('.').length < 2) {
      valor = valor + ".00"
    }
    if ((valor.toString().split(".")[1]).length == 1) {
      valor = valor + "0"
    }
    if ((valor.toString().split(".")[1]).length > 2) {
      var a = (valor.toString().split(".")[1]).replace(/(\d{2})$/, ".$1")
      valor = valor.toString().split(".")[0] + "." + a.toString().split(".")[1]
    }
    return valor;
  }

  public mascaraCpf(mascara:any, evento: KeyboardEvent) {
    var valorEvento = (<HTMLInputElement>evento.target).value;
    var i = valorEvento.length;

    if (i < 14) {

      var saida = mascara.substring(0, 1);
      var texto = mascara.substring(i);

      if (texto.substring(0, 1) != saida) {
        return (<HTMLInputElement>evento.target).value += texto.substring(0, 1);
      }
      if (i >= 11) {
        var ao_cpf = valorEvento;

        ao_cpf = ao_cpf.replace(/\D/g, "");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d)/, "$1.$2");
        ao_cpf = ao_cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");

        return (<HTMLInputElement>evento.target).value = ao_cpf;
      }
    }
  }


  ValidaTelefoneMovelResponsavel(tle:any) {
    var patternValidaTelMov = /^(?:(?:\+|00)?(55)\s?)?(?:\(?([1-9][0-9])\)?\s?)?(?:((?:[1-9]\d|[2-9])\d{3})\-?(\d{4}))$/;
    if (tle == '') {
      this.TelMovValResponsavel = false;
      this.TelMovValResponsavelVasil = true;
    }
    else if (patternValidaTelMov.test(tle)) {
      this.TelMovValResponsavel = false;
      this.TelMovValResponsavelVasil = false;
    }
    else if (!patternValidaTelMov.test(tle)) {
      this.TelMovValResponsavel = true;
      this.TelMovValResponsavelVasil = false;
    }
    else
      this.TelMovValResponsavel = false
  }
  public validarTelMovel(value:any) {
    var idUsuario:any;
    // this.usuarioInvalidoTelefone = false;

    if (this.pessoaResponsavel != undefined && this.pessoaResponsavel != null)
      idUsuario = this.pessoaResponsavel.idPessoa
    if (value != "") {
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'TelMovel').subscribe((retorno) => {
        
        if (retorno != null) {
          this.campoExitente = "Tel"
          this.mensagemPessoa = 'Telefone já registrado no sistema Deseja utilizar a pessoa desse cadastro?';
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      })
    }
  }
  public validarCpfResponsavel(value:any) {

    var valuecpf = value.replace(/[\.-]/g, "");
    this.campoCpfResponsavelInvalido = false;
    var idUsuario = null
    if (this.pessoaResponsavel != undefined)
      idUsuario = this.pessoaResponsavel.idPessoa
    if (value != "") {
      this.campoCpfResponsavelVazil = false;

      if (!this.validacao.cpf(value)) {
        this.campoCpfResponsavelInvalido = true;
        return;
      }

      this.campoCpfResponsavelInvalido = false;
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(valuecpf, idUsuario!, 'CPF').subscribe((retorno) => {
        if (retorno != null) {
          this.campoExitente = "CPF"
          this.mensagemPessoa = 'CPF já registrado no sistema Deseja utilizar a pessoa desse cadastro?';
          this.idUsuarioExistente = retorno;
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      })
    }
    else
      this.campoCpfResponsavelVazil = true;
  }
  public validarEmailResponsavel(value:any) {
    this.emailResponsavel.markAsTouched();
    var idUsuario:any;
    this.campoEmailResponsavelInvalido = false;
    // this.usuarioInvalidoEmail = false;
    if (this.pessoaResponsavel != undefined)
      idUsuario = this.pessoaResponsavel.idPessoa
    if (value != "") {
      this.campoEmailResponsavelVazil = false;
      if (!this.validacao.validarEmail(value)) {
        this.campoEmailResponsavelInvalido = true;
        return
      }
      this.campoEmailResponsavelInvalido = false
      this.usuarioService.validar_TELMOVEL_CPF_EMAIL(value, idUsuario, 'EMAIL').subscribe((retorno) => {
        
        if (retorno != null) {
          this.campoExitente = "EMAIL"
          this.mensagemPessoa = 'Email já registrado no sistema Deseja utilizar a pessoa desse cadastro?';
          this.idUsuarioExistente = retorno
          this.ngxSmartModalService.getModal('UsuarioExistente').open();
        }
      })
    }
    else
      this.campoEmailResponsavelVazil = true;
  }
  public AceitarUsuarioExistente() {

    if (this.idUsuarioExistente.pessoa) {
      this.retornoResponsavel = this.idUsuarioExistente.pessoa;
      this.pessoaResponsavel!.nome = this.retornoResponsavel.nomePessoa;
      this.pessoaResponsavel!.cpf = this.retornoResponsavel.cpf;
      if (this.idUsuarioExistente.contato) {
        var contatoResp = this.idUsuarioExistente.contato.telefoneMovel;
        if (contatoResp != null && contatoResp != '' && contatoResp != undefined) {
          contatoResp = contatoResp.replace(/\D/g, "");
          contatoResp = contatoResp.replace(/^(\d{2})(\d)/g, "($1) $2");
          contatoResp = contatoResp.replace(/(\d)(\d{4})$/, "$1-$2");
        }
        this.pessoaResponsavel!.celular = contatoResp;
      }
      this.pessoaResponsavel!.email = this.retornoResponsavel.email;
    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();
  }
  public NaoAceitarUsuarioExistente() {
    if (this.campoExitente == 'CPF') {
      this.pessoaResponsavel!.cpf = ""
      this.campoExitente = ""
    }
    else if (this.campoExitente == 'EMAIL') {
      this.pessoaResponsavel!.email = ""
      this.campoExitente = ""
    }
    else {
      this.pessoaResponsavel!.celular = ''
      this.campoExitente = ""
    }
    this.ngxSmartModalService.getModal('UsuarioExistente').close();
  }



  AdicionarResponsavelClinica() {

    if (!this.camposResponsaveisAtivos) {
      this.camposResponsaveisAtivos = true;
      
      return;
    }
    this.ValidaCamposResponsavel()
    if (this.showMessageErrorResp) {
      this.snackBarAlert.falhaSnackbar("Preencha os campos nescessarios para cadastrar a pessoa")
      return;
    }

    var resp: PessoaResponsavel = new PessoaResponsavel();
    resp = this.pessoaResponsavel!;
    if (this.retornoResponsavel) {

      resp.idPessoa = this.retornoResponsavel.idPessoa;
    }
    if (this.retornoClinica)
      resp.idClinicaRespornsavel = this.retornoClinica.idClinica

    if (this.retornoClinica && this.retornoClinica.idClinica > 0) {
      this.responsavelClinicaService.SalvarResponsavel(resp).subscribe((retorno) => {

        
        if (retorno == true) {
          // this.gridResponsavelClinica.push(pes);
          if (this.flgEdicaoResponsavelGrid)
            this.snackBarAlert.sucessoSnackbar("Pessoa editada")
          else
            this.snackBarAlert.sucessoSnackbar("Pessoa incluida aos responsaveis dessa clinica")

          this.flgEdicaoResponsavelGrid = false
          this.CarregarGridResponsaveis(this.retornoClinica.idClinica)
          this.LimparCamposResponsavel()
        }
        else {
          this.snackBarAlert.falhaSnackbar("Erro ao salvar pessoa")
        }

        this.spinner.hide();
      }, () => {
        this.snackBarAlert.falhaSnackbar("Erro ao salvar pessoa")
        this.spinner.hide();
      })
    }
    else {


      if (resp.celular != null && resp.celular != '' && resp.celular != undefined) {
        resp.celular = resp.celular.replace(/\D/g, "");
        resp.celular = resp.celular.replace(/^(\d{2})(\d)/g, "($1) $2");
        resp.celular = resp.celular.replace(/(\d)(\d{4})$/, "$1-$2");
      }
      if (resp.cpf != null && resp.cpf != '' && resp.cpf != undefined) {
        resp.cpf = resp.cpf.replace(/\D/g, "");
        resp.cpf = resp.cpf.replace(/(\d{3})(\d)/, "$1.$2");
        resp.cpf = resp.cpf.replace(/(\d{3})(\d)/, "$1.$2");
        resp.cpf = resp.cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
      }


      if (this.flgEdicaoResponsavelGrid) {
        this.gridResponsavelClinica.splice(this.indexResponsavelGrid, 1, resp)
        this.snackBarAlert.sucessoSnackbar("Pessoa editada")
      }
      else {
        this.gridResponsavelClinica.push(resp);
        this.snackBarAlert.sucessoSnackbar("Pessoa incluida aos responsaveis dessa clinica")
      }

      this.LimparCamposResponsavel()

    }
  }

  CarregarGridResponsaveis(idClinica:any) {
    this.gridResponsavelClinica = [];
    this.responsavelClinicaService.GetGridResponsaveis(idClinica).subscribe((retorno) => {
      
      if (Array.isArray(retorno)) {
        retorno.forEach((elemento: any) => {

          if (elemento != elemento.celular != null && elemento.celular != '' && elemento.celular != undefined) {
            elemento.celular = elemento.celular.replace(/\D/g, "");
            elemento.celular = elemento.celular.replace(/^(\d{2})(\d)/g, "($1) $2");
            elemento.celular = elemento.celular.replace(/(\d)(\d{4})$/, "$1-$2");
          }

          if (elemento != elemento.cpf != null && elemento.cpf != '' && elemento.cpf != undefined) {
            elemento.cpf = elemento.cpf.replace(/\D/g, "");
            elemento.cpf = elemento.cpf.replace(/(\d{3})(\d)/, "$1.$2");
            elemento.cpf = elemento.cpf.replace(/(\d{3})(\d)/, "$1.$2");
            elemento.cpf = elemento.cpf.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
          }

          this.gridResponsavelClinica.push(elemento);
        })
      }

      this.spinner.hide();
    })
  }

  ValidaCamposResponsavel() {
    this.showMessageErrorResp = false;
    this.nomeResponsavel.markAsTouched();
    this.emailResponsavel.markAsTouched();
    this.ValidaTelefoneMovelResponsavel((document.getElementById("celularResp") as HTMLInputElement)['value']);

    if (this.pessoaResponsavel!.email == undefined || !this.pessoaResponsavel!.email.trim())
      this.campoEmailResponsavelVazil = true;

    if (this.pessoaResponsavel!.cpf == undefined || !this.pessoaResponsavel!.cpf.trim())
      this.campoCpfResponsavelVazil = true;

    if (this.pessoaResponsavel!.nome == undefined || !this.pessoaResponsavel!.nome.trim()
      || this.pessoaResponsavel!.email == undefined || !this.pessoaResponsavel!.email.trim()
      || this.pessoaResponsavel!.cpf == undefined || !this.pessoaResponsavel!.cpf.trim()
      || this.pessoaResponsavel!.celular == undefined || !this.pessoaResponsavel!.celular.trim()
      || this.campoEmailResponsavelVazil == true || this.campoCpfResponsavelInvalido == true || this.campoCpfResponsavelVazil == true
      || this.TelMovValResponsavel == true
    ) {
      this.showMessageErrorResp = true;

      if (this.TelMovValResponsavel != true && this.TelMovValResponsavel != false || this.TelMovRespLimpa == false) {
        this.TelMovValResponsavelVasil = true;
        this.TelMovRespLimpa = true;
      }
    }

  }

  LimparCamposResponsavel() {

    this.pessoaResponsavel = new PessoaResponsavel();
    this.retornoResponsavel = null;
    this.nomeResponsavel.markAsUntouched();
    this.emailResponsavel.markAsUntouched();
    this.TelMovValResponsavel = false;
    this.TelMovValResponsavelVasil = false;
    this.TelMovRespLimpa = false;
    this.flgEdicaoResponsavelGrid = false;
    this.campoEmailResponsavelInvalido = false;
    this.campoEmailResponsavelVazil = false;
    this.campoCpfResponsavelInvalido = false;
    this.campoCpfResponsavelVazil = false;


  }

  ApagarResponsavel(index:any, idPessoa:any) {
    

    if (this.retornoClinica && this.retornoClinica.idClinica > 0) {
      //exclui direto
      this.responsavelClinicaService.InativarResponsavel(idPessoa).subscribe((retorno) => {

        if (retorno == true) {
          
          this.CarregarGridResponsaveis(this.retornoClinica.idClinica)
          this.snackBarAlert.sucessoSnackbar("Pessoa excluida dos responsáveis dessa clinica")
        } else {
          this.snackBarAlert.falhaSnackbar("Erro ao excluir pessoa dos responsáveis!")
        }
        this.spinner.hide();
      }, () => {
        this.spinner.hide();
        this.snackBarAlert.falhaSnackbar("Erro ao excluir pessoa")
      })

    }
    else {
      //remove do array
      
      this.gridResponsavelClinica.splice(index, 1)
      
    }

  }

  EditarResponsavel(index:any, item:any) {
    

    var obj = this.gridResponsavelClinica.filter(c => c.cpf == item)

    this.pessoaResponsavel = obj[0];
    this.flgEdicaoResponsavelGrid = true
    this.indexResponsavelGrid = index;
  }


}
