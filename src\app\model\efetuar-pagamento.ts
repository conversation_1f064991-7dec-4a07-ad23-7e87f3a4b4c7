
export class EfetuarPagamento {
    idConsulta?:number | null;
    IdPessoaRecebedora?: number | null;
    IdPagador?:number | null;
    CartaoNumero?: string;
    CartaoMesAno?: string;
    CartaoSeg?: string;
    CartaoNome?: string;
    FraseFatura?: string;
    Valor?: string | null;
    ValorConsulta?:number | null;
    flgSalvaCartao?: boolean;

}

export class RetornoPagamento {
    ok?: boolean;
    mensagemErro?: string;
    idPagamento? : number;
    idConsulta?:number;
}

export class Pagamento {
        idPagamento?: number;
        idContasPagar?: any;
        idContasReceber?: any;
        flgEstorno?: boolean;
        flgInativo?: boolean;
        idUsuarioGerador?: number;
        dtaPagamento?: Date;
        vlrPagamento?: number;
        desPagamento?: string;
}