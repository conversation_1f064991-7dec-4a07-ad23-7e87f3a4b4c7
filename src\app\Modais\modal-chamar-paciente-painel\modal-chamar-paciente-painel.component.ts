import { Component, Inject, OnInit } from '@angular/core';
import {
  Form<PERSON>uilder, FormGroup, FormsModule,
  ReactiveFormsModule, Validators
} from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef as MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { SpinnerService } from 'src/app/service/spinner.service';
import { PainelAtendimentoService } from 'src/app/service/painel-atendimento.service';
import { SalaService } from 'src/app/service/sala.service';
import { objNovoAtendimento, objTipoAtendimento } from 'src/app/model/painel-atendimento';
import { SalaModelview } from 'src/app/model/salas';
import { AlertComponent } from 'src/app/alert/alert.component';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';

@Component({
  selector: 'app-modal-chamar-paciente-painel',
  templateUrl: './modal-chamar-paciente-painel.component.html',
  styleUrls: ['./modal-chamar-paciente-painel.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    MatSelectModule,
    MatButtonModule,
    MatDialogModule,
    MatRadioModule
  ]
})
export class ModalChamarPacientePainelComponent implements OnInit {
  form: FormGroup;
  listaTipoAtendimento: objTipoAtendimento[] = [];

  constructor(
    private fb: FormBuilder,
    private salaService: SalaService,
    private painelAtendimentoService: PainelAtendimentoService,
    private spinner: SpinnerService,
    private router: Router,
    private snackbar: AlertComponent,
    public dialogRef: MatDialogRef<ModalChamarPacientePainelComponent>,
    @Inject(MAT_DIALOG_DATA) public id: number
  ) {
    this.form = this.fb.group({
      idSala: [null, Validators.required],
      idTipoAtendimento: [null, Validators.required]
    });
  }

  listaSala: SalaModelview[] = [];

  ngOnInit(): void {
    this.carregarListas();
  }
  async carregarListas() {
    await this.GetListaSala();
    await this.carregarTipoAtendimento();
  }

  // carregarSalasMedico() {
  //   this.spinner.show();
  //   this.salaService.ListarSalas().subscribe(
  //     (ret) => {
  //       this.listaSalas = ret;
  //       this.spinner.hide();
  //     },
  //     (erro) => {
  //       ;
  //       this.spinner.hide();
  //     }
  //   );
  // }

  carregarTipoAtendimento() {
    this.spinner.show();
    this.painelAtendimentoService.listarTiposAtendimentos().subscribe(
      (ret) => {
        ret;
        this.listaTipoAtendimento = ret;
        this.spinner.hide();
      },
      () => {
        this.spinner.hide();
      }
    );
  }

  fecharModal() {
    this.dialogRef.close();
  }

  SalvarChamado() {


    if (this.form.valid) {
      var obj = new objNovoAtendimento();
      obj.idConsulta = this.id;
      obj.idSala = this.form.value.idSala;
      obj.idTipoAtendimento = this.form.value.idTipoAtendimento;

      this.spinner.show();
      this.painelAtendimentoService.ChamarPacientePainel(obj).subscribe((ret) => {
        ret;
        this.spinner.hide();
        // Sucesso
        this.dialogRef.close();
        this.router.navigate(['/prontuario']);
      }, (erro) => {
        erro;
        this.spinner.hide();
        // Falha
      });
    }
  }

  async GetListaSala() {
    this.spinner.show();
    await this.salaService.GetListaSala("").subscribe((ret) => {
      // this.snackbar.sucessoSnackbar('Sala salva com sucesso.')
      this.listaSala = ret;

    }, () => {
      this.snackbar.sucessoSnackbar('Ocurreu um erro tentando listar as salas.')
    })
    this.spinner.hide();
  }

}