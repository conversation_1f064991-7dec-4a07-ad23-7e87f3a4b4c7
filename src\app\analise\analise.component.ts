import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AnaliseService } from '../service/analise.service';
import { LocalStorageService } from '../service/LocalStorageService';
import { UsuarioLogadoService } from '../auth/usuarioLogado.service';
import { AnaliseMensagemModelView } from '../model/analise';
import { AlertComponent } from 'src/app/alert/alert.component';
import { NgxSmartModalModule, NgxSmartModalService } from 'ngx-smart-modal';
import { SpinnerService } from '../service/spinner.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCard } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatDialog } from '@angular/material/dialog';
import { ChatInternoModalComponent } from './adicionar-analise/chat-interno-modal/chat-interno-modal.component';

@Component({
  selector: 'app-analise',
  templateUrl: './analise.component.html',
  styleUrls: ['./analise.component.scss'],
  standalone: true,
  imports: [
    MatInputModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCard,
    MatFormFieldModule,
    MatIcon,
    NgxSmartModalModule,
    TranslateModule,
    MatSlideToggleModule
  ]
})
export class AnaliseComponent implements OnInit {

  constructor(
    private spinner: SpinnerService,
    private router: Router,
    private usuarioLogadoService: UsuarioLogadoService,
    private analiseService: AnaliseService,
    private snackBarAlert: AlertComponent,
    public ngxSmartModalService: NgxSmartModalService,
    private localStorageService: LocalStorageService,
    private dialog: MatDialog,
  ) { }

  ImagemPessoa: any = "assets/build/img/userdefault.png";
  listaAnalise: any = [];
  listaAnaliseTodos: any = [];
  FlgOrientacaoMedicaPendente = false;
  flgOrientacaoNutricionalPendente = false;
  flgOrientacaoFisicaPendente = false;
  pesquisa = '';
  listaMensagens: AnaliseMensagemModelView[] = [];
  objetoMensagem = new AnaliseMensagemModelView();

  idAnalise: number = 0;

  ngOnInit() {
    this.carregarAnalises();

    var idTipoOrientacao = this.usuarioLogadoService.getIdTipoOrientacao();

    if (idTipoOrientacao == 1)
      this.FlgOrientacaoMedicaPendente = true;
    else if (idTipoOrientacao == 2)
      this.flgOrientacaoNutricionalPendente = true;
    else if (idTipoOrientacao == 3)
      this.flgOrientacaoFisicaPendente = true;


    // this.carregaOritentacao();
  }
  // carregaOritentacao() {
  //   this.medicoService.getOrientacaoDoMedico(this.usuarioLogadoService.getIdPessoa()).subscribe((ret) => {
  //     if(ret== 1){
  //       this.FlgOrientacaoMedicaPendente = true;
  //       this.flgOrientacaoNutricionalPendente = false;
  //       this.flgOrientacaoFisicaPendente = false;
  //     }
  //     else if(ret== 2){
  //       this.FlgOrientacaoMedicaPendente = false;
  //       this.flgOrientacaoNutricionalPendente = true;
  //       this.flgOrientacaoFisicaPendente = false;
  //     }
  //     else if(ret== 3){
  //       this.FlgOrientacaoMedicaPendente = false;
  //       this.flgOrientacaoNutricionalPendente = false;
  //       this.flgOrientacaoFisicaPendente = true;
  //     }

  //     this.CarregaListaAnalise();
  //   })
  // }


  carregarAnalises() {
    this.spinner.show();
    this.analiseService.GetAnalises().subscribe((retorno) => {
      this.listaAnalise = []


      this.listaAnaliseTodos = this.listaAnalise = retorno;
      this.CarregaListaAnalise();


      this.spinner.hide();
    }, err => {
      console.error(err)
      this.spinner.hide();
    })
  }


  adicionaranalise() {
    this.router.navigate(['/adicionaranalise']);
  }

  editar(idAnalise: any) {
    this.localStorageService.idAnalise = idAnalise;
    this.adicionaranalise();
  }

  public CarregaListaAnalise() {
    this.listaAnalise = [];
    if (!this.FlgOrientacaoMedicaPendente && !this.flgOrientacaoNutricionalPendente && !this.flgOrientacaoFisicaPendente) {
      this.listaAnalise = this.listaAnaliseTodos;
      return;
    }
    this.listaAnaliseTodos.forEach((element: any) => {
      var adiciona = false;
      if (this.FlgOrientacaoMedicaPendente && element.flgOrientacaoMedica != 'Sim')
        adiciona = true;
      if (this.flgOrientacaoNutricionalPendente && element.flgOrientacaoNutricional != 'Sim')
        adiciona = true;
      if (this.flgOrientacaoFisicaPendente && element.flgOrientacaoFisica != 'Sim')
        adiciona = true;
      if (adiciona)
        this.listaAnalise.push(element);
    });
  }

  filtrarAnalise() {
    this.FlgOrientacaoMedicaPendente = this.flgOrientacaoNutricionalPendente = this.flgOrientacaoFisicaPendente = false;
    this.listaAnalise = this.listaAnaliseTodos.filter((x: any) => x.nomePaciente.toUpperCase().includes(this.pesquisa.toUpperCase()))
  }


  abreModalChat(idAnalise: number) {
    this.idAnalise = idAnalise
    this.dialog.open(ChatInternoModalComponent, {
      width: '30vmax',
      height: '70vh',
      maxWidth: '100vw',
      maxHeight: '90vh',
      panelClass: 'chat-dialog-container',
      data: { idAnalise: this.idAnalise!, flgApenasVisualizacao: true },
    });
  }

  recuperaMensagens(idAnalise: number) {

    this.analiseService.getMentsagensImportantes(idAnalise).subscribe((ret) => {
      this.listaMensagens = ret;

      this.spinner.hide();
    })
  }

  EnviarMensagem() {

    this.objetoMensagem.idAnalise = this.idAnalise;
    this.objetoMensagem.idPessoa = this.usuarioLogadoService.getIdPessoa();
    this.objetoMensagem.flgMensagemImportante = true;
    if (this.objetoMensagem.idAnalise == null || this.objetoMensagem.idPessoa == null || this.objetoMensagem.mensagem == "") {
      if (this.objetoMensagem.mensagem == "") {
        this.snackBarAlert.falhaSnackbar("Erro ao enviar mensagem, escreva primeiramente o conteudo da mensagem")
      }
      else {
        this.snackBarAlert.falhaSnackbar("Erro ao enviar mensagem, não foi possivel completar os dados")
      }

    }
    else {
      this.analiseService.salvaMensagemInterna(this.objetoMensagem).subscribe((ret) => {
        if (ret) {
          this.objetoMensagem = new AnaliseMensagemModelView();
          this.snackBarAlert.sucessoSnackbar("mensagem enviada");
          this.recuperaMensagens(this.idAnalise);
          this.spinner.hide();
        }
        else {
          this.snackBarAlert.falhaSnackbar("Erro ao salvar");
          this.spinner.hide();
        }
      })
    }
  }

  abreModal(modalNome: string) {
    this.ngxSmartModalService.getModal(modalNome).open();
  }
  fechaModal(modalNome: string) {
    this.listaMensagens = [];
    this.objetoMensagem = new AnaliseMensagemModelView();
    this.idAnalise = 0;
    this.ngxSmartModalService.getModal(modalNome).close();
  }

  formatarData(data: string | Date): string {
    const dataEnvio = new Date(data);
    const agora = new Date();

    const diffEmMilissegundos = agora.getTime() - dataEnvio.getTime();
    const diffEmMinutos = Math.floor(diffEmMilissegundos / 60000);

    if (diffEmMinutos < 3) {
      return 'Agora';
    } else if (diffEmMinutos < 420) { // 7 horas = 7 * 60 minutos = 420 minutos
      const hora = String(dataEnvio.getHours()).padStart(2, '0');
      const minutos = String(dataEnvio.getMinutes()).padStart(2, '0');
      return `${hora}:${minutos}`;
    } else {
      const dia = String(dataEnvio.getDate()).padStart(2, '0');
      const mes = String(dataEnvio.getMonth() + 1).padStart(2, '0');
      const ano = dataEnvio.getFullYear();
      const hora = String(dataEnvio.getHours()).padStart(2, '0');
      const minutos = String(dataEnvio.getMinutes()).padStart(2, '0');

      return `${dia}/${mes}/${ano} - ${hora}:${minutos}`;
    }
  }
}
