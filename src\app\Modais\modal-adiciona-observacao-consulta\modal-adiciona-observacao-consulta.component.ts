import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef as MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { AlertComponent } from 'src/app/alert/alert.component';
import { ObjetoPadrao } from 'src/app/model/RetornoPadraoApi';
import { SpinnerService } from 'src/app/service/spinner.service';
import { WhatsService } from 'src/app/service/whats.service';

@Component({
    selector: 'app-modal-adiciona-observacao-consulta',
    templateUrl: './modal-adiciona-observacao-consulta.component.html',
    styleUrls: ['./modal-adiciona-observacao-consulta.component.scss'],
      standalone: true,
    imports: [
MatInputModule,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
      MatFormFieldModule,
      MatIcon
    ]
})
export class ModalAdicionaObservacaoConsultaComponent implements OnInit {

  constructor(

    private dialogRef: MatDialogRef<ModalAdicionaObservacaoConsultaComponent>,
    private whatsService: WhatsService,
    @Inject(MAT_DIALOG_DATA) public obj: ObjetoPadrao,
    private spinner: SpinnerService,
    private snackBar: AlertComponent
  ) { }

  ngOnInit(): void {
    this.idConsulta = this.obj.num;
    this.strObservacaoChegada = this.obj.str!;
    
    
    
  }

  idConsulta:any;
  strObservacaoChegada: string = "";
  validaSalvar(){
    if(!this.idConsulta){
      this.snackBar.falhaSnackbar("Falha ao salvar, consulta não selecionada corretamente");
      return false;
    }
    
    if(!this.strObservacaoChegada){
      this.snackBar.falhaSnackbar("Observação não preenchida.");
      return false;
    }

    if(this.strObservacaoChegada.length < 1){
      this.snackBar.falhaSnackbar("Observação não preenchida");
      return false;
    }

    
    return true;
  }

  SalvarObservacaoChegadaPaciente() {
    if(!this.validaSalvar())
      return;

    this.spinner.show();
    let obj: ObjetoPadrao = new ObjetoPadrao();

    obj.num = this.idConsulta;
    obj.str = this.strObservacaoChegada;
    this.whatsService.SalvarObservacaoChegadaPaciente(obj).subscribe(() => {
      this.snackBar.sucessoSnackbar("Observação salva com sucesso");
      this.spinner.hide();
      this.fecharModal(true);

    }, () => {
      this.snackBar.sucessoSnackbar("Observação salva com sucesso");
      this.spinner.hide();
    })
  }

  fecharModal(flgEditado: boolean) {
    this.dialogRef.close(flgEditado);
  }
}
