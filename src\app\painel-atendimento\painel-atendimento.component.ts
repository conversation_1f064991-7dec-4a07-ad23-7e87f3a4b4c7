import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { objChamadoAtendimento } from '../model/painel-atendimento';
import { PainelAtendimentoService } from '../service/painel-atendimento.service';
import { AlertComponent } from '../alert/alert.component';
import { SpinnerService } from '../service/spinner.service';
import { SignalHubService } from '../service/signalHub.service';
import { RelogioComponent } from './relogio/relogio.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';

@Component({
    selector: 'app-painel-atendimento',
    templateUrl: './painel-atendimento.component.html',
    styleUrls: ['./painel-atendimento.component.scss'],
    encapsulation: ViewEncapsulation.ShadowDom,
      standalone: true,
    imports: [
MatInputModule,
      RelogioComponent,
      CommonModule,
      FormsModule,
ReactiveFormsModule,
    ]
})
export class PainelAtendimentoComponent implements OnInit {

  constructor(
    private painelService: PainelAtendimentoService,
    private snackbar: AlertComponent,
    private spinner: SpinnerService,
    private signalHubService: SignalHubService
  ) {
    this.signalHubService.AtualizaPainelAtendimento
      .subscribe(() => {
        this.recarregarTabela();
      });
  }



  listaAtendimentos: objChamadoAtendimento[] = [];

  ngOnInit(): void {
    this.spinner.show();
    this.carregaUltimosAtendimentos();
  }

  carregaUltimosAtendimentos() {
    this.painelService.GetUltimasConsultas().subscribe(
      (ret) => {
        ;
        this.listaAtendimentos = ret;
        this.spinner.hide();
      },() => {
        this.snackbar.falhaSnackbar("Falha ao carrgar os chamados.")
        this.spinner.hide();
      }
    )
  }

  recarregarTabela() {
    this.carregaUltimosAtendimentos();
  }

  playAudio() {
    let audio = new Audio();
    audio.src = "../../../assets/build/som/alert.mp3";
    audio.load();
    audio.play();
  }
}
